# Event Registration Email Template with Questions and Answers

## Overview

This solution adds attendee questions and answers to the event registration confirmation email template in Odoo. The enhanced template displays all the questions that attendees answered during registration along with their responses.

## Key Features

1. **Attendee Questions Display**: Shows all questions and answers from the registration form
2. **Multiple Question Types Support**: Handles different question types (text_box, simple_choice, etc.)
3. **Clean Formatting**: Questions and answers are displayed in a well-formatted table
4. **Conditional Display**: Only shows the questions section if there are answers to display
5. **Maintains Original Design**: Preserves the original email template styling and layout

## Files Created

- `event_registration_questions_template.xml` - The enhanced email template with questions and answers section

## How It Works

### Questions and Answers Section

The template includes a new section that:

1. **Checks for Answers**: Uses `t-if="object.registration_answer_ids"` to only display if there are answers
2. **Iterates Through Answers**: Uses `t-foreach="object.registration_answer_ids" t-as="answer"` to loop through all answers
3. **Displays Question Title**: Shows `answer.question_id.title` for each question
4. **Shows Appropriate Answer**: 
   - For `simple_choice` questions: displays `answer.value_answer_id.name`
   - For `text_box` questions: displays `answer.value_text_box`
   - Shows "No answer provided" for empty answers

### Template Structure

```xml
<!-- ATTENDEE QUESTIONS AND ANSWERS SECTION -->
<t t-if="object.registration_answer_ids">
<div style="margin-top: 20px; margin-bottom: 20px;">
<strong>Your Registration Details</strong>
<table style="...">
<t t-foreach="object.registration_answer_ids" t-as="answer">
<tr style="border-bottom: 1px solid #e0e0e0;">
<td style="...">
<t t-out="answer.question_id.title"></t>:
</td>
<td style="...">
<t t-if="answer.question_type == 'simple_choice' and answer.value_answer_id">
<t t-out="answer.value_answer_id.name"></t>
</t>
<t t-elif="answer.question_type == 'text_box' and answer.value_text_box">
<t t-out="answer.value_text_box"></t>
</t>
<t t-else="">
<span style="color: #999; font-style: italic;">No answer provided</span>
</t>
</td>
</tr>
</t>
</table>
</div>
</t>
```

## Installation Instructions

1. **Copy the Template File**: Place `event_registration_questions_template.xml` in your custom module's `data/` directory

2. **Update Module Manifest**: Add the template file to your module's `__manifest__.py`:
   ```python
   'data': [
       'data/event_registration_questions_template.xml',
       # ... other data files
   ],
   ```

3. **Install/Update Module**: Install or update your custom module

4. **Configure Email Template**: 
   - Go to Settings > Technical > Email Templates
   - Find "Event: Registration Confirmation with Questions"
   - Set it as the default template for event registrations

## Customization Options

### Styling
You can customize the appearance by modifying the CSS styles in the template:
- Table borders and spacing
- Font sizes and colors
- Background colors
- Padding and margins

### Question Types
The template currently handles:
- `simple_choice`: Multiple choice questions
- `text_box`: Text input questions

To add support for other question types, extend the conditional logic:
```xml
<t t-elif="answer.question_type == 'your_new_type'">
<!-- Handle your new question type -->
</t>
```

### Section Title
Change "Your Registration Details" to any title you prefer by modifying:
```xml
<strong>Your Custom Title Here</strong>
```

## Data Model Reference

The template uses these Odoo models:
- `event.registration`: The main registration record
- `event.registration.answer`: Individual answers to questions
- `event.question`: The questions themselves
- `event.question.answer`: Predefined answer choices

### Key Fields Used
- `object.registration_answer_ids`: List of all answers for this registration
- `answer.question_id.title`: The question text
- `answer.question_type`: Type of question (simple_choice, text_box, etc.)
- `answer.value_answer_id.name`: Selected answer for multiple choice
- `answer.value_text_box`: Text answer for text input questions

## Testing

To test the template:
1. Create an event with registration questions
2. Register for the event and answer the questions
3. Check the confirmation email to see the questions and answers displayed

## Troubleshooting

### Questions Not Showing
- Verify that `registration_answer_ids` contains data
- Check that questions were actually answered during registration
- Ensure the template is properly installed and activated

### Formatting Issues
- Check CSS styles in the template
- Verify HTML structure is valid
- Test in different email clients

## Support for Different Question Types

The template is designed to be extensible. Currently supported:
- **Text Box**: Free text input
- **Simple Choice**: Single selection from predefined options

Future enhancements could include:
- Multiple choice questions
- Date/time questions
- File upload questions
- Rating scales
