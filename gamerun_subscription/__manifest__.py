# noinspection PyStatementEffect
{
    'name': "Gamerun Subscription & Payments",
    'author': "Gamerun",
    'category': 'Website',
    'summary': "Subscription & Payments",
    'description': """
    """,
    'website': 'https://gamerun.ai',
    'depends': [
        'website_sale_subscription'
    ],
    'data': [
        'views/product_view.xml',
        'views/sale_subscription_plan.xml',
        'views/templates.xml'
    ],
    'assets': {
        'web.assets_frontend': [
            'gamerun_subscription/static/src/js/deposit.js',
            'gamerun_subscription/static/src/js/subscription.js',
            'gamerun_subscription/static/src/xml/pricing_view.xml',
        ]
    },
    'post_load': 'patch_sale_order',
    'auto_install': False,
    'installable': True,
    'license': 'LGPL-3',
}
