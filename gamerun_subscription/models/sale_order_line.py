from odoo import fields, models, api, _, Command


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    subscription_deposit_type = fields.Selection([
        ('usd', 'USD'),
        ('percentage', 'Percentage(%)')
    ])
    subscription_deposit_value = fields.Float(string="Deposit Value")

    # Field to store the selected subscription plan for this line
    selected_plan_id = fields.Many2one(
        'sale.subscription.plan',
        string="Selected Subscription Plan",
        help="The subscription plan selected by the user for this product"
    )

    # Field to mark lines that should keep custom pricing
    custom_subscription_price = fields.Boolean(
        string="Custom Subscription Price",
        default=False,
        help="If True, this line will keep its custom price and not be recomputed by subscription pricing"
    )

    @api.depends('product_id', 'product_uom', 'product_uom_qty', 'custom_subscription_price')
    def _compute_price_unit(self):
        """Override to prevent price recomputation for custom subscription prices"""
        lines_to_recompute = self.filtered(lambda l: not l.custom_subscription_price)
        lines_with_custom_price = self - lines_to_recompute

        # Store custom prices before calling super
        custom_prices = {}
        for line in lines_with_custom_price:
            custom_prices[line.id] = line.price_unit

        # Let super handle normal lines
        super(SaleOrderLine, lines_to_recompute)._compute_price_unit()

        # Restore custom prices
        for line in lines_with_custom_price:
            if line.id in custom_prices:
                line.price_unit = custom_prices[line.id]

