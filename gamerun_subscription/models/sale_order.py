# -*- coding: utf-8 -*-

import logging

from dateutil.relativedelta import relativedelta

from odoo import models, api, Command, _
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    @api.depends('order_line', 'order_line.recurring_invoice')
    def _compute_has_recurring_line(self):
        recurring_product_orders = self.order_line.filtered(lambda l: l.product_id.recurring_invoice and not l.subscription_deposit_value).order_id
        recurring_product_orders.has_recurring_line = True
        (self - recurring_product_orders).has_recurring_line = False

    def _cart_update_order_line(self, product_id, quantity, order_line, **kwargs):
        order_line_exist = bool(order_line)
        order_line = super()._cart_update_order_line(product_id, quantity, order_line, **kwargs)
        product_id = order_line.product_id.id or product_id
        product = self.env['product.product'].browse(product_id)
        if product.recurring_invoice and kwargs.get('deposit_value', 0) > 0:
            # Store the selected plan before clearing it from the order
            selected_plan_id = kwargs.get('plan_id') or self.plan_id.id
            if selected_plan_id:
                order_line.selected_plan_id = int(selected_plan_id)

            self.plan_id = False  # Clear plan from order since this will be a deposit order
            if order_line_exist:
                order_line_exist.subscription_deposit_type = kwargs.get('deposit_type', False)
                order_line_exist.subscription_deposit_value = kwargs.get('deposit_value')
                if selected_plan_id:
                    order_line_exist.selected_plan_id = int(selected_plan_id)
        return order_line

    def _prepare_order_line_values(self, product_id, quantity, deposit_type=False, deposit_value=False, **kwargs):
        values = super()._prepare_order_line_values(product_id, quantity, **kwargs)
        if not deposit_type:
            return values
        values['subscription_deposit_type'] = deposit_type
        values['subscription_deposit_value'] = deposit_value

        # Store the selected plan_id if provided
        if kwargs.get('plan_id'):
            values['selected_plan_id'] = int(kwargs.get('plan_id'))

        return values

    def action_confirm(self):
        # implement installment for remaining
        res = super(SaleOrder, self).action_confirm()
        for so in self:
            so._process_subscription_deposits()
        return res

    def _process_subscription_deposits(self):
        """Process subscription products with initial deposits"""
        # Find order lines with subscription products that have deposits
        deposit_lines = self.order_line.filtered(
            lambda l: l.product_id.recurring_invoice and
                     l.product_id.allow_initial_deposit and
                     l.subscription_deposit_value > 0 and
                     l.subscription_deposit_type
        )

        if not deposit_lines:
            return

        for line in deposit_lines:
            self._create_deposit_and_subscription_orders(line)

    def _create_deposit_and_subscription_orders(self, line):
        """Create separate orders for deposit and subscription"""
        # Calculate deposit amount based on the original product price (not line.price_subtotal)
        original_unit_price = line.product_id.list_price
        original_total_price = original_unit_price * line.product_uom_qty

        deposit_amount = self._calculate_deposit_amount_from_original_price(line, original_total_price)
        remaining_amount = original_total_price - deposit_amount

        _logger.info(
            "Processing subscription deposit for line %s: "
            "Original price: %s, Deposit: %s (%s), Remaining: %s",
            line.product_id.name, original_total_price, deposit_amount,
            line.subscription_deposit_type, remaining_amount
        )

        if remaining_amount <= 0:
            # If deposit covers full amount, no subscription needed
            _logger.info("Deposit covers full amount, no subscription order needed")
            return

        # Create subscription order for remaining amount
        subscription_order = self._create_subscription_order(line, remaining_amount)

        # Update current line to be deposit only
        self._update_line_for_deposit(line, deposit_amount)

        _logger.info("Created subscription order %s for remaining amount %s",
                    subscription_order.name, remaining_amount)

        return subscription_order

    def _calculate_deposit_amount(self, line):
        """Calculate the deposit amount based on type (legacy method)"""
        if line.subscription_deposit_type == 'usd':
            return min(line.subscription_deposit_value, line.price_subtotal)
        elif line.subscription_deposit_type == 'percentage':
            percentage = min(line.subscription_deposit_value, 100.0)
            return line.price_subtotal * (percentage / 100.0)
        return 0.0

    def _calculate_deposit_amount_from_original_price(self, line, original_total_price):
        """Calculate the deposit amount based on original product price"""
        if line.subscription_deposit_type == 'usd':
            return min(line.subscription_deposit_value, original_total_price)
        elif line.subscription_deposit_type == 'percentage':
            percentage = min(line.subscription_deposit_value, 100.0)
            return original_total_price * (percentage / 100.0)
        return 0.0

    def _create_subscription_order(self, line, remaining_amount):
        """Create a subscription order for the remaining amount"""
        # Get the recurring plan from the line (user's selection) or use a default
        plan_id = self._get_subscription_plan(line)

        if not plan_id:
            raise UserError(_('No subscription plan found for product %s. Please configure a subscription plan.') % line.product_id.name)

        # Calculate the new unit price for the remaining amount
        new_unit_price = remaining_amount / line.product_uom_qty

        # Create subscription order
        subscription_vals = {
            'partner_id': self.partner_id.id,
            'partner_invoice_id': self.partner_invoice_id.id,
            'partner_shipping_id': self.partner_shipping_id.id,
            'pricelist_id': self.pricelist_id.id,
            'currency_id': self.currency_id.id,
            'company_id': self.company_id.id,
            'plan_id': plan_id.id,
            'subscription_state': '1_draft',
            'is_subscription': True,
            'origin': self.name,
            'next_invoice_date': self.date_order.date() + relativedelta(months=1),
        }

        subscription_order = self.env['sale.order'].create(subscription_vals)

        # Create order line with custom price (bypassing subscription pricing)
        line_vals = {
            'order_id': subscription_order.id,
            'product_id': line.product_id.id,
            'product_uom_qty': line.product_uom_qty,
            'product_uom': line.product_uom.id,
            'price_unit': new_unit_price,
            'name': line.name + _(' (Subscription)'),
            'tax_id': [Command.set(line.tax_id.ids)],
            'custom_subscription_price': True,  # Mark as custom price to prevent recomputation
        }

        # Create the line with custom pricing flag
        subscription_line = self.env['sale.order.line'].create(line_vals)

        # Auto-confirm the subscription if needed
        if self.state == 'sale':
            subscription_order.action_confirm()

        return subscription_order

    def _get_subscription_plan(self, line):
        """Get subscription plan for the product line"""
        # First check if there's a plan selected on the order line (user's choice)
        if line.selected_plan_id:
            return line.selected_plan_id

        # Then check if there's a plan already selected on the current order
        if self.plan_id:
            return self.plan_id

        # Then check if there's a plan configured on the product
        if hasattr(line.product_id, 'subscription_plan_id') and line.product_id.subscription_plan_id:
            return line.product_id.subscription_plan_id

        # Look for a default monthly plan
        plan = self.env['sale.subscription.plan'].search([
            ('billing_period_value', '=', 1),
            ('billing_period_unit', '=', 'month')
        ], limit=1)

        if not plan:
            # Create a default monthly plan if none exists
            plan = self.env['sale.subscription.plan'].create({
                'name': 'Monthly Plan',
                'billing_period_value': 1,
                'billing_period_unit': 'month',
            })

        return plan

    def _update_line_for_deposit(self, line, deposit_amount):
        """Update the order line to reflect only the deposit amount"""
        # Calculate new unit price for deposit
        new_unit_price = deposit_amount / line.product_uom_qty

        # Update line with deposit information
        line.write({
            'price_unit': new_unit_price,
            'name': line.name + _(' (Initial Deposit)'),
        })

        # Clear subscription-related fields since this is now just a deposit
        line.write({
            'subscription_deposit_type': False,
            'subscription_deposit_value': 0.0,
        })
