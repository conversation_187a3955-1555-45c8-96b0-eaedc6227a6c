<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Enhanced Event Registration Email Template with Questions and Answers -->
        <record id="event_subscription_with_questions" model="mail.template">
            <field name="name">Event: Registration Confirmation with Questions</field>
            <field name="model_id" ref="event.model_event_registration"/>
            <field name="subject">Your registration at {{ object.event_id.name }}</field>
            <field name="email_from">{{ (object.event_id.organizer_id.email_formatted or object.event_id.user_id.email_formatted or user.email_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="description">Event registration confirmation email with attendee questions and answers</field>
            <field name="body_html" type="html">
<div data-oe-version="1.0"><br/></div>
<table border="0" cellpadding="0" cellspacing="0" style="border-radius:0px;border-style:none;padding:16px 0 0 0;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;caption-side:bottom;padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;" width="100%">
<tbody style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td align="center" style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">

<t t-set="date_begin" t-value="format_datetime(object.event_id.date_begin, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)"></t>
<t t-set="date_end" t-value="format_datetime(object.event_id.date_end, tz='UTC', dt_format=&quot;yyyyMMdd'T'HHmmss'Z'&quot;)"></t>
<t t-set="is_online" t-value="'is_published' in object.event_id and object.event_id.is_published"></t>
<t t-set="is_sale" t-value="'sale_order_id' in object and object.sale_order_id"></t>
<t t-set="event_organizer" t-value="object.event_id.organizer_id"></t>
<t t-set="event_address" t-value="object.event_id.address_id"></t>
<t t-set="registration_ids" t-value="object.ids if not is_sale else object._get_event_registration_ids_from_order()"></t>

<table border="0" cellpadding="0" cellspacing="0" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; margin: 0px; box-sizing: border-box; border-width: 0px; caption-side: bottom; padding: 16px; background-color: white; color: #454748; border-collapse: separate; font-family: Verdana, Arial, sans-serif;" width="590">
<tbody style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td align="center" style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;min-width: 590px;">

<table border="0" cellpadding="0" cellspacing="0" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; margin: 0px; box-sizing: border-box; border-width: 0px; caption-side: bottom; min-width: 590px; background-color: white; padding: 0px 8px; border-collapse: separate; color: #454748; font-family: Verdana, Arial, sans-serif;" width="590">
<tbody style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;" valign="middle">

<span style="font-size: 10px;">Your registration</span><br/>
<span style="font-size: 20px; font-weight: bold;">
<t t-out="object.name or 'Guest'"></t>
</span>
<div style="margin-bottom: 5px;margin-top: 18px;">
<a style="color: #454748; margin: 0px; box-sizing: border-box; padding: 8px 12px; font-size: 12px; text-decoration: none; font-weight: 400; background-color: #875a7b; border: 0px solid #875a7b; border-radius: 3px;" t-attf-href="/event/{{ object.event_id.id }}/my_tickets?registration_ids={{ registration_ids }}&amp;tickets_hash={{ object.event_id._get_tickets_access_hash(registration_ids) }}&amp;responsive_html=1" target="_blank"><font style="color: #ffffff;">
                            View Tickets&nbsp;</font></a>
</div>

</td>
<td align="right" style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;" valign="middle">

<t t-if="object.barcode">
<div style="margin-bottom: 5px;">
<img alt="QR Code" height="100" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px; vertical-align: middle; width: 100px; height: 100px;" t-attf-src="/report/barcode/QR/{{object.barcode}}?&amp;width=100&amp;height=100&amp;quiet=0" width="100"/>
</div>
</t>
<t t-if="not object.company_id.uses_default_logo">
<img style="border-radius:0px;border-style:none;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;vertical-align:middle;padding: 0px; margin: 0px; margin-right: 10px; height: auto; width: 80px;" t-att-alt="'%s' % object.company_id.name" t-att-src="'/logo.png?company=%s' % object.company_id.id" width="80"/>
</t>

</td>
</tr>
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td colspan="2" style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;text-align:center;">
<hr style="border-style:none;border-top-color:currentcolor;border-top-width:0px;border-image-width:1;border-image-source:none;border-image-slice:100%;border-image-repeat:stretch;border-image-outset:0;border-left-color:currentcolor;border-left-width:0px;border-bottom-color:currentcolor;border-bottom-width:0px;border-right-color:currentcolor;border-right-width:0px;border-radius: 0px; padding: 0px; box-sizing: border-box; opacity: 0.25; color: #454748; background-color: #cccccc; border: medium; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px;" width="100%"/>
</td>
</tr>
</tbody>
</table>

</td>
</tr>
</tbody>
</table>
<!-- MAIN CONTENT -->
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td align="center" style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;min-width: 590px;">

<table border="0" cellpadding="0" cellspacing="0" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; margin: 0px; box-sizing: border-box; border-width: 0px; caption-side: bottom; min-width: 590px; background-color: white; padding: 0px 8px; border-collapse: separate; color: #454748; font-family: Verdana, Arial, sans-serif;" width="590">
<tbody style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;font-size: 14px;" valign="top">

<div>
                        Hello <t t-out="object.name or 'Guest'"></t>,<br/><br/>
                        We are happy to confirm your registration to the event
                        <t t-if="is_online">
<a style="color: #454748; border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px; text-decoration: none; font-weight: bold;" t-att-href="object.event_id.website_url" t-out="object.event_id.name or ''"><font style="color: #875a7b;">OpenWood Collection Online Reveal</font></a>
</t>
<t t-else="">
<strong style="border-radius:0px;border-style:none;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;font-weight:bolder;" t-out="object.event_id.name or ''">OpenWood Collection Online Reveal</strong>
</t>.
                        <t t-if="object.partner_id and object.partner_id.name and object.partner_id.name != object.name">
                            This ticket was registered by <t t-out="object.partner_id.name"></t>.
                        </t>
</div>

<div t-if="is_sale">
<br/>
                        The order for this ticket has reference <t t-out="object.sale_order_id.name"></t>
                        and was placed on <t t-out="object.sale_order_id.date_order.date()"></t>
<t t-if="object.sale_order_line_id.price_unit"> for an amount of
                            <t t-options="{'widget': 'monetary', 'display_currency': object.sale_order_line_id.currency_id}" t-out="object.sale_order_line_id.price_unit"></t>
</t>.
</div>

<div><br/></div>

<!-- ATTENDEE QUESTIONS AND ANSWERS SECTION -->
<t t-if="object.registration_answer_ids">
<div style="margin-top: 20px; margin-bottom: 20px;">
<strong style="border-radius:0px;border-style:none;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;font-weight:bolder;">Your Registration Details</strong>
<table style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; padding: 0px; margin: 10px 0px; box-sizing: border-box; border-width: 0px; border-collapse: collapse; caption-side: bottom; width: 100%; color: #454748; font-size: 14px; font-family: Verdana, Arial, sans-serif;" width="100%">
<t t-foreach="object.registration_answer_ids" t-as="answer">
<tr style="border-bottom: 1px solid #e0e0e0;">
<td style="padding: 8px 0px; vertical-align: top; width: 40%; font-weight: bold;">
<t t-out="answer.question_id.title"></t>:
</td>
<td style="padding: 8px 0px; vertical-align: top; width: 60%;">
<t t-if="answer.question_type == 'simple_choice' and answer.value_answer_id">
<t t-out="answer.value_answer_id.name"></t>
</t>
<t t-elif="answer.question_type == 'text_box' and answer.value_text_box">
<t t-out="answer.value_text_box"></t>
</t>
<t t-else="">
<span style="color: #999; font-style: italic;">No answer provided</span>
</t>
</td>
</tr>
</t>
</table>
</div>
</t>

<div>/</div>
<div><br/>
<strong style="border-radius:0px;border-style:none;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;font-weight:bolder;">Add this event to your calendar</strong>
<a style="color: #454748; margin: 0px; box-sizing: border-box; padding: 3px 5px; border: 1px solid #875a7b; text-decoration: none; border-radius: 3px;" t-attf-href="https://www.google.com/calendar/render?action=TEMPLATE&amp;text={{ object.event_id.name }}&amp;dates={{ date_begin }}/{{ date_end }}&amp;location={{ location }}&amp;details={{ object.event_id._get_external_description() }}" target="new"><font style="color: #875a7b;"><img alt="" height="16" src="/web_editor/font_to_img/61525/rgb(135,90,123)/16" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px; vertical-align: middle; width: 14px; height: 16px;" width="14"/> Google</font></a>
<a style="color: #454748; margin: 0px; box-sizing: border-box; padding: 3px 5px; border: 1px solid #875a7b; text-decoration: none; border-radius: 3px;" t-attf-href="/event/{{ slug(object.event_id) }}/ics"><font style="color: #875a7b;"><img alt="" height="16" src="/web_editor/font_to_img/61525/rgb(135,90,123)/16" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px; vertical-align: middle; width: 14px; height: 16px;" width="14"/> iCal/Outlook</font></a>
<a style="color: #454748; margin: 0px; box-sizing: border-box; padding: 3px 5px; border: 1px solid #875a7b; text-decoration: none; border-radius: 3px;" t-attf-href="https://calendar.yahoo.com/?v=60&amp;view=d&amp;type=20&amp;title={{ object.event_id.name }}&amp;in_loc={{ location }}&amp;st={{ format_datetime(object.event_id.date_begin, tz='UTC', dt_format='yyyyMMdd\'T\'HHmmss') }}&amp;et={{ format_datetime(object.event_id.date_end, tz='UTC', dt_format='yyyyMMdd\'T\'HHmmss') }}" target="new"><font style="color: #875a7b;">
<img alt="" height="16" src="/web_editor/font_to_img/61525/rgb(135,90,123)/16" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px; vertical-align: middle; width: 14px; height: 16px;" width="14"/> Yahoo
                        </font></a>
<br/><br/>
</div>

<div>
                         See you soon,<br/>
<span style="color: #454748;">
                        -- <br/>
<t t-if="event_organizer">
<t t-out="event_organizer.name">YourCompany</t>
</t>
<t t-else="">
                            The <t t-out="object.event_id.name or ''">OpenWood Collection Online Reveal</t> Team
                        </t>
</span>
</div>

</td>
</tr>
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;text-align:center;">
<hr style="border-style:none;border-top-color:currentcolor;border-top-width:0px;border-image-width:1;border-image-source:none;border-image-slice:100%;border-image-repeat:stretch;border-image-outset:0;border-left-color:currentcolor;border-left-width:0px;border-bottom-color:currentcolor;border-bottom-width:0px;border-right-color:currentcolor;border-right-width:0px;border-radius: 0px; padding: 0px; box-sizing: border-box; opacity: 0.25; color: #454748; background-color: #cccccc; border: medium; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px;" width="100%"/>
</td>
</tr>
</tbody>
</table>

<!-- DETAILS SECTION -->
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td align="center" style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;min-width: 590px;">

<table border="0" cellpadding="0" cellspacing="0" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; margin: 0px; box-sizing: border-box; border-width: 0px; caption-side: bottom; min-width: 590px; background-color: white; padding: 0px 8px; border-collapse: separate; color: #454748; font-family: Verdana, Arial, sans-serif;" width="590">
<tbody style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;font-size: 14px;" valign="top">

<table style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px; border-collapse: collapse; caption-side: bottom; width: 100%; color: #454748; font-size: 14px; font-family: Verdana, Arial, sans-serif;" width="100%">
<tbody style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">

<td style="border-radius:0px;border-style:solid;padding:1px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;vertical-align:top;">
<img alt="" height="34" src="/web_editor/font_to_img/61555/rgb(81,81,102)/34" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; margin: 0px; box-sizing: border-box; border-width: 0px; vertical-align: middle; padding: 4px; max-width: inherit; width: 32.4667px; height: 34px;" width="32.4667"/>
</td>

<td style="border-radius:0px;border-style:solid;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;padding: 0px 10px 0px 10px;width:50%;line-height:20px;vertical-align:top;" width="50%">
<div>
<strong style="border-radius:0px;border-style:none;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;font-weight:bolder;">From</strong>
<t t-options="{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}" t-out="object.event_id.date_begin">May 4, 2021</t>
                                     - <t t-options="{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}" t-out="object.event_id.date_begin">7:00 AM</t>
</div>
<div>
<strong style="border-radius:0px;border-style:none;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;font-weight:bolder;">To</strong>
<t t-options="{&quot;widget&quot;: &quot;datetime&quot;, &quot;date_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;format&quot;: &quot;long&quot;}" t-out="object.event_id.date_end">May 6, 2021</t>
                                     - <t t-options="{&quot;widget&quot;: &quot;datetime&quot;, &quot;time_only&quot;: True, &quot;tz_name&quot;: object.event_id.date_tz, &quot;hide_seconds&quot;: True, &quot;format&quot;: &quot;short&quot;}" t-out="object.event_id.date_end">5:00 PM</t>
</div>
<div style="font-size:12px;color:#9e9e9e"><i>(<t t-out="object.event_id.date_tz or ''">Europe/Brussels</t>)</i></div>
</td>

<td style="border-radius:0px;border-style:solid;padding:1px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;vertical-align:top;">
<t t-if="event_address">
<img alt="" height="34" src="/web_editor/font_to_img/61505/rgb(81,81,102)/34" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; margin: 0px; box-sizing: border-box; border-width: 0px; vertical-align: middle; padding: 4px; max-width: inherit; width: 23.3px; height: 34px;" width="23.3"/>
</t>
</td>

<td style="border-radius:0px;border-style:solid;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;padding: 0px 10px 0px 10px;width:50%;vertical-align:top;" width="50%">
<t t-if="event_address">
<t t-set="location" t-value="''"></t>
<t t-if="object.event_id.address_id.name">
<div t-out="object.event_id.address_id.name">Teksa SpA</div>
</t>
<t t-if="object.event_id.address_id.street">
<div t-out="object.event_id.address_id.street">Puerto Madero 9710</div>
<t t-set="location" t-value="object.event_id.address_id.street"></t>
</t>
<t t-if="object.event_id.address_id.street2">
<div t-out="object.event_id.address_id.street2">Of A15, Santiago (RM)</div>
<t t-set="location" t-valuef="{{location}}, {{object.event_id.address_id.street2}}"></t>
</t>
<div>
<t t-if="object.event_id.address_id.city">
<t t-out="object.event_id.address_id.city">Pudahuel</t>,
                                        <t t-set="location" t-valuef="{{location}}, {{object.event_id.address_id.city}}"></t>
</t>
<t t-if="object.event_id.address_id.state_id.name">
<t t-out="object.event_id.address_id.state_id.name">C1</t>,
                                        <t t-set="location" t-valuef="{{location}}, {{object.event_id.address_id.state_id.name}}"></t>
</t>
<t t-if="object.event_id.address_id.zip">
<t t-out="object.event_id.address_id.zip">98450</t>
<t t-set="location" t-valuef="{{location}}, {{object.event_id.address_id.zip}}"></t>
</t>
</div>
<t t-if="object.event_id.address_id.country_id.name">
<div t-out="object.event_id.address_id.country_id.name">Argentina</div>
<t t-set="location" t-valuef="{{location}}, {{object.event_id.address_id.country_id.name}}"></t>
</t>
</t>
</td>

</tr>
</tbody>
</table>

</td>
</tr>
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;text-align:center;">
<t t-if="event_organizer">
<hr style="border-style:none;border-top-color:currentcolor;border-top-width:0px;border-image-width:1;border-image-source:none;border-image-slice:100%;border-image-repeat:stretch;border-image-outset:0;border-left-color:currentcolor;border-left-width:0px;border-bottom-color:currentcolor;border-bottom-width:0px;border-right-color:currentcolor;border-right-width:0px;border-radius: 0px; padding: 0px; box-sizing: border-box; opacity: 0.25; color: #454748; background-color: #cccccc; border: medium; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px;" width="100%"/>
</t>
</td>
</tr>
</tbody>
</table>

<!-- ORGANIZER CONTACT SECTION -->
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;font-size: 14px;" valign="top">

<t t-if="event_organizer">
<div>
<span style="font-weight:300;margin:10px 0px">Questions about this event?</span>
<div>Please contact the organizer:</div>
<ul style="border-radius:0px;border-style:none;padding:0 0 0 32px;margin:0px 0 16px 0;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;list-style-type:disc;">
<li><t t-out="event_organizer.name or ''">YourCompany</t></li>
<t t-if="event_organizer.email">
<li>Mail: <a style="color: #454748; border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px; text-decoration: none;" t-attf-href="mailto:{{ event_organizer.email }}" t-out="event_organizer.email or ''"><font style="color: #875a7b;"><EMAIL></font></a></li>
</t>
<t t-if="event_organizer.phone">
<li>Phone: <t t-out="event_organizer.phone">******-123-4567</t></li>
</t>
</ul>
</div>
</t>

</td>
</tr>
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;text-align:center;">
<t t-if="is_online or event_address">
<hr style="border-style:none;border-top-color:currentcolor;border-top-width:0px;border-image-width:1;border-image-source:none;border-image-slice:100%;border-image-repeat:stretch;border-image-outset:0;border-left-color:currentcolor;border-left-width:0px;border-bottom-color:currentcolor;border-bottom-width:0px;border-right-color:currentcolor;border-right-width:0px;border-radius: 0px; padding: 0px; box-sizing: border-box; opacity: 0.25; color: #454748; background-color: #cccccc; border: medium; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px;" width="100%"/>
</t>
</td>
</tr>

<!-- MOBILE APP PROMOTION -->
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;font-size: 14px;" valign="top">

<t t-if="is_online">
<div>
<strong style="border-radius:0px;border-style:none;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;font-weight:bolder;">Get the best mobile experience.</strong>
<a href="/event" style="border-top-width:0px;border-right-width:0px;border-bottom-width:0px;border-left-width:0px;color:#008f8c;text-decoration: none; border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px;"><font style="color: #008f8c;">Install our mobile app</font></a>
</div>
</t>

</td>
</tr>
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;text-align:center;">
<t t-if="is_online and event_address">
<hr style="border-style:none;border-top-color:currentcolor;border-top-width:0px;border-image-width:1;border-image-source:none;border-image-slice:100%;border-image-repeat:stretch;border-image-outset:0;border-left-color:currentcolor;border-left-width:0px;border-bottom-color:currentcolor;border-bottom-width:0px;border-right-color:currentcolor;border-right-width:0px;border-radius: 0px; padding: 0px; box-sizing: border-box; opacity: 0.25; color: #454748; background-color: #cccccc; border: medium; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px;" width="100%"/>
</t>
</td>
</tr>

<!-- GOOGLE MAPS LINK -->
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;font-size: 14px;" valign="top">

<t t-if="event_address and location">
<table style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px; border-collapse: collapse; caption-side: bottom; width: 100%; color: #454748; font-size: 14px; font-family: Verdana, Arial, sans-serif;" width="100%">
<tbody style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:1px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<div>
<span class="oe_unbreakable" style="display: inline-block; width: 8px; height: 14px; vertical-align: text-bottom;"><img data-class="fa fa-map-marker" data-style="null" height="16.8" src="/web_editor/font_to_img/61505/rgb(69%2C71%2C72)/rgb(255%2C255%2C255)/8x17" style="border-radius:0px;border-style:none;padding:0px;margin:0px;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;box-sizing: border-box; line-height: 14px; width: 8px; height: 16.8px; vertical-align: unset;" width="8"/></span>
</div>
<span class="oe_unbreakable" style="display: inline-block; width: 8px; height: 14px; vertical-align: text-bottom;"><img data-class="fa fa-map-marker" data-style="null" height="16.8" src="/web_editor/font_to_img/61505/rgb(69%2C71%2C72)/rgb(255%2C255%2C255)/8x17" style="border-radius:0px;border-style:none;padding:0px;margin:0px;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;box-sizing: border-box; line-height: 14px; width: 8px; height: 16.8px; vertical-align: unset;" width="8"/></span>
</td>
</tr>
</tbody>
</table>
</t>

</td>
</tr>
</tbody>
</table>

</td>
</tr>
</tbody>
</table>

</td>
</tr>

<!-- FOOTER -->
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td align="center" style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;min-width: 590px;">

<t t-if="object.company_id">
<table border="0" cellpadding="0" cellspacing="0" style="border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-radius: 0px; border-style: none; margin: 0px; box-sizing: border-box; border-width: 0px; caption-side: bottom; min-width: 590px; background-color: #f1f1f1; color: #454748; padding: 8px; border-collapse: separate; font-family: Verdana, Arial, sans-serif;" width="590">
<tbody style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<tr style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;">
<td style="border-radius:0px;border-style:solid;padding:0px;margin:0px;box-sizing:border-box;border-left-width:0px;border-bottom-width:0px;border-right-width:0px;border-top-width:0px;border-left-color:inherit;border-bottom-color:inherit;border-right-color:inherit;border-top-color:inherit;text-align: center; font-size: 14px;">
            Sent by <a style="color: #454748; text-decoration: none; border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px;" t-attf-href="{{ object.company_id.website }}" t-out="object.company_id.name or ''" target="_blank"><font style="color: #875a7b;">YourCompany</font></a>
<t t-if="is_online">
<br/>
                Discover <a href="/event" style="border-top-width:0px;border-right-width:0px;border-bottom-width:0px;border-left-width:0px;color:#008f8c;text-decoration: none; border-radius: 0px; border-style: none; padding: 0px; margin: 0px; box-sizing: border-box; border-width: 0px;"><font style="color: #875a7b;">all our events</font></a>.
            </t>
</td>
</tr>
</tbody>
</table>
</t>

</td>
</tr>
</tbody>
</table>
            </field>
        </record>
    </data>
</odoo>
