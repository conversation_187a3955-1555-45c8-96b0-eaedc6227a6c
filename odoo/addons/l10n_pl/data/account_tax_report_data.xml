<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report" model="account.report">
        <field name="name">Tax Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.pl"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_tax_report_line_razem_c" model="account.report.line">
                <field name="name">Base - Total C</field>
                <field name="aggregation_formula">PLTAXC_01_10.balance + PLTAXC_02_11.balance + PLTAXC_03_13.balance + PLTAXC_04_15.balance + PLTAXC_05_17.balance + PLTAXC_06_19.balance + PLTAXC_07_21.balance + PLTAXC_08_22.balance + PLTAXC_09_23.balance + PLTAXC_10_25.balance + PLTAXC_11_27.balance + PLTAXC_12_31.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_line_kraj_zwolnione" model="account.report.line">
                        <field name="name">Base - Supply of goods/services, domestic, exempt</field>
                        <field name="code">PLTAXC_01_10</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_kraj_zwolnione_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_10</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_poza_kraj" model="account.report.line">
                        <field name="name">Base - Supply of goods/services, out of the country</field>
                        <field name="code">PLTAXC_02_11</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_poza_kraj_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_11</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_uslugi_art_100_1_4" model="account.report.line">
                                <field name="name">Base - Services included in art. 100.1.4</field>
                                <field name="code">PLTAXC_02a_12</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_uslugi_art_100_1_4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_12</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_triangular_buyer_2nd_payer" model="account.report.line">
                                <field name="name">Triangular transaction 2. VAT payer</field>
                                <field name="code">PLTAXC_02a_12_Triangular</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_triangular_buyer_2nd_payer_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">Triangular Purchase</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_uslugi_kraj_0" model="account.report.line">
                        <field name="name">Base - Supply of goods/services, domestic, 0%</field>
                        <field name="code">PLTAXC_03_13</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_uslugi_kraj_0_tag" model="account.report.expression">
                                <field name="label">tag</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_13</field>
                            </record>
                            <record id="account_tax_report_line_uslugi_kraj_0_formula" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">PLTAXC_03_13.tag + PLTAXC_03_14.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_towary_art_129" model="account.report.line">
                                <field name="name">Base - Goods under art. 129</field>
                                <field name="code">PLTAXC_03_14</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_towary_art_129_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_14</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_kraj_5" model="account.report.line">
                        <field name="name">Base - Supply of goods/services, domestic, 5%</field>
                        <field name="code">PLTAXC_04_15</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_kraj_5_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_15</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_kraj_8" model="account.report.line">
                        <field name="name">Base - Supply of goods/services, domestic, 8%</field>
                        <field name="code">PLTAXC_05_17</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_kraj_8_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_17</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_kraj_23" model="account.report.line">
                        <field name="name">Base - Supply of goods/services, domestic, 23%</field>
                        <field name="code">PLTAXC_06_19</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_kraj_23_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_19</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_dostawa_towarow" model="account.report.line">
                        <field name="name">Base - Intra-Community supply of goods</field>
                        <field name="code">PLTAXC_07_21</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_dostawa_towarow_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_21</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_intracom_procedure_i_42" model="account.report.line">
                                <field name="name">- under customs procedure 42</field>
                                <field name="code">PLTAXC_07_21_I42</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_intracom_procedure_i_42_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I_42</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_intracom_procedure_i_63" model="account.report.line">
                                <field name="name">- under customs procedure 63</field>
                                <field name="code">PLTAXC_07_21_I63</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_intracom_procedure_i_63_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">I_63</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_eksport_towarow" model="account.report.line">
                        <field name="name">Base - Export of goods</field>
                        <field name="code">PLTAXC_08_22</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_eksport_towarow_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_22</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_nabycie_towarow" model="account.report.line">
                        <field name="name">Base - Intra-Community acquisition of goods</field>
                        <field name="code">PLTAXC_09_23</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_nabycie_towarow_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_23</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_triangular_2nd_payer" model="account.report.line">
                                <field name="name">Triangular transaction 2. VAT payer</field>
                                <field name="code">PLTAXC_09_23_Triangular</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_triangular_2nd_payer_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">Triangular Sale</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_art_33a" model="account.report.line">
                        <field name="name">Base - Import of goods under art. 33a</field>
                        <field name="code">PLTAXC_10_25</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_art_33a_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_25</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_import_uslug" model="account.report.line">
                        <field name="name">Base - Importation of services</field>
                        <field name="code">PLTAXC_11_27</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_import_uslug_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_27</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_art_28b" model="account.report.line">
                                <field name="name">Base - Acquisition under art. 28b</field>
                                <field name="code">PLTAXC_11a_29</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_art_28b_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_29</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_podatnik_nabywca" model="account.report.line">
                        <field name="name">Base - Supply of goods, taxable person acquiring</field>
                        <field name="code">PLTAXC_12_31</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_podatnik_nabywca_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_31</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_razem_d" model="account.report.line">
                <field name="name">Base - Total D</field>
                <field name="aggregation_formula">PLTAXD_02_40.balance + PLTAXD_02_42.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_line_uslug_s_trwale" model="account.report.line">
                        <field name="name">Basis - Acquisition of goods and services, fixed assets</field>
                        <field name="code">PLTAXD_02_40</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_uslug_s_trwale_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_40</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_uslug_pozostalych" model="account.report.line">
                        <field name="name">Base - Purchase of other goods and services</field>
                        <field name="code">PLTAXD_02_42</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_uslug_pozostalych_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">K_42</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_do_przeniesienia" model="account.report.line">
                <field name="name">Tax - To be carried over</field>
                <field name="code">PLTAX</field>
                <field name="aggregation_formula" eval="False"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_tax_report_line_do_przeniesienia_formula" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">PLTAXD.balance + PLTAX_49.balance + PLTAX_50.balance - PLTAXC.balance</field>
                    </record>
                    <record id="tax_report_line_do_przeniesienia_carryover" model="account.report.expression">
                        <field name="label">_carryover_balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">PLTAX.balance</field>
                        <field name="subformula">if_above(EUR(0))</field>
                        <field name="carryover_target">PLTAXC_39._applied_carryover_balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_tax_report_line_podatek_razem_c" model="account.report.line">
                        <field name="name">Tax - Total C</field>
                        <field name="code">PLTAXC</field>
                        <field name="aggregation_formula">PLTAXC_04_16.balance + PLTAXC_05_18.balance + PLTAXC_06_20.balance + PLTAXC_09_24.formula + PLTAXC_10_26.balance + PLTAXC_11_28.balance + PLTAXC_12_32.balance + PLTAXC_12_33.balance + PLTAXC_01_34.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_podatek_kraj_5" model="account.report.line">
                                <field name="name">Tax - Supply of goods/services, domestic, 5%</field>
                                <field name="code">PLTAXC_04_16</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_kraj_5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_16</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_kraj_8" model="account.report.line">
                                <field name="name">Tax - Supply of goods/services, domestic, 8%</field>
                                <field name="code">PLTAXC_05_18</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_kraj_8_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_18</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_kraj_23" model="account.report.line">
                                <field name="name">Tax - Supply of goods/services, domestic, 23%</field>
                                <field name="code">PLTAXC_06_20</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_kraj_23_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_20</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_nabycie_towarow" model="account.report.line">
                                <field name="name">Tax - Intra-Community acquisition of goods</field>
                                <field name="code">PLTAXC_09_24</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_nabycie_towarow_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_24</field>
                                    </record>
                                    <record id="account_tax_report_line_podatek_nabycie_towarow_formula" model="account.report.expression">
                                        <field name="label">formula</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">PLTAXC_09_24.balance + PLTAXC_10_35.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_tax_report_line_podatek_transp_termin" model="account.report.line">
                                        <field name="name">Tax - Inter-Community acquisition of means of transport</field>
                                        <field name="code">PLTAXC_10_35</field>
                                        <field name="expression_ids">
                                            <record id="account_tax_report_line_podatek_transp_termin_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">K_35</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_art_33a" model="account.report.line">
                                <field name="name">Tax - Importation of goods under art. 33a</field>
                                <field name="code">PLTAXC_10_26</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_art_33a_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_26</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_import_uslug" model="account.report.line">
                                <field name="name">Tax - Importation of services</field>
                                <field name="code">PLTAXC_11_28</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_import_uslug_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_28</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_tax_report_line_podatek_art_28b" model="account.report.line">
                                        <field name="name">Tax - Acquisition under art. 28b</field>
                                        <field name="code">PLTAXC_11a_30</field>
                                        <field name="expression_ids">
                                            <record id="account_tax_report_line_podatek_art_28b_tag" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">K_30</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_podatnik_nabywca" model="account.report.line">
                                <field name="name">Tax - Supply of goods, taxable person acquiring</field>
                                <field name="code">PLTAXC_12_32</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_podatnik_nabywca_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_32</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_art_14_5" model="account.report.line">
                                <field name="name">Tax - From physical inventory under art. 14.5</field>
                                <field name="code">PLTAXC_12_33</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_art_14_5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_33</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_kasy_rejestrujace" model="account.report.line">
                                <field name="name">Tax - Expenditure on cash registers</field>
                                <field name="code">PLTAXC_01_34</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_kasy_rejestrujace_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_34</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_wewnątrzwspólnotowe_103_5a" model="account.report.line">
                                <field name="name">Tax - Intra-Community acquisition of goods under art. 103 sec. 5a</field>
                                <field name="code">PLTAXC_01_36</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_wewnątrzwspólnotowe_103_5a_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_36</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_podatek_razem_d" model="account.report.line">
                        <field name="name">Tax - Total D</field>
                        <field name="code">PLTAXD</field>
                        <field name="aggregation_formula">PLTAXC_39.balance + PLTAXD_02_41.balance + PLTAXD_02_43.balance + PLTAXD_02_44.balance + PLTAXD_02_45.balance + PLTAXD_02_46.balance + PLTAXD_02_47.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_podatek_deklaracji" model="account.report.line">
                                <field name="name">Tax - Surplus from previous declaration</field>
                                <field name="code">PLTAXC_39</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_deklaracji_applied_carryover" model="account.report.expression">
                                        <field name="label">_applied_carryover_balance</field>
                                        <field name="engine">external</field>
                                        <field name="formula">most_recent</field>
                                        <field name="date_scope">previous_tax_period</field>
                                    </record>
                                    <record id="account_tax_report_line_podatek_deklaracji_tag" model="account.report.expression">
                                        <field name="label">tag</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_39</field>
                                    </record>
                                    <record id="account_tax_report_line_podatek_deklaracji_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">PLTAXC_39.tag + PLTAXC_39._applied_carryover_balance</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_s_trwale" model="account.report.line">
                                <field name="name">Tax - Acquisition of goods and services, fixed assets</field>
                                <field name="code">PLTAXD_02_41</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_s_trwale_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_41</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_uslug_pozostalych" model="account.report.line">
                                <field name="name">Tax - Purchase of other goods and services</field>
                                <field name="code">PLTAXD_02_43</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_uslug_pozostalych_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_43</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_s_trwalych" model="account.report.line">
                                <field name="name">Tax - Adjustment of input tax on acquisition of fixed assets</field>
                                <field name="code">PLTAXD_02_44</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_s_trwalych_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_44</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_podatek_pozostalych_nabyc" model="account.report.line">
                                <field name="name">Tax - Adjustment of input tax on other acquisitions</field>
                                <field name="code">PLTAXD_02_45</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_podatek_pozostalych_nabyc_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_45</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_korekta_art89b1" model="account.report.line">
                                <field name="name">Tax - Input tax adjustments under art. 89b sec 1</field>
                                <field name="code">PLTAXD_02_46</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_korekta_art89b1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_46</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_korekta_art89b4" model="account.report.line">
                                <field name="name">Tax - Input tax adjustments under art. 89b sec 4</field>
                                <field name="code">PLTAXD_02_47</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_korekta_art89b4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">K_47</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_podatek_okresie" model="account.report.line">
                        <field name="name">Tax - Expenditure on cash registers to be reimbursed in the period</field>
                        <field name="code">PLTAX_49</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_podatek_okresie_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_zaniechaniem_poboru" model="account.report.line">
                        <field name="name">Tax - Subject to non-collection</field>
                        <field name="code">PLTAX_50</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_zaniechaniem_poboru_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
