# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_pl
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-27 12:37+0000\n"
"PO-Revision-Date: 2023-09-27 12:37+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_intracom_procedure_i_42
msgid "- under customs procedure 42"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_intracom_procedure_i_63
msgid "- under customs procedure 63"
msgstr ""

#. module: l10n_pl
#: model_terms:ir.ui.view,arch_db:l10n_pl.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\" aria-label=\"Values set here are company-specific.\" "
"groups=\"base.group_multi_company\" role=\"img\"/>"
msgstr ""

#. module: l10n_pl
#: model:ir.model,name:l10n_pl.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_account_bank_statement_line__l10n_pl_vat_b_mpv_prowizja
#: model:ir.model.fields,field_description:l10n_pl.field_account_move__l10n_pl_vat_b_mpv_prowizja
#: model:ir.model.fields,field_description:l10n_pl.field_account_payment__l10n_pl_vat_b_mpv_prowizja
msgid "B_MPV_Prowizja"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_account_bank_statement_line__l10n_pl_vat_b_spv
#: model:ir.model.fields,field_description:l10n_pl.field_account_move__l10n_pl_vat_b_spv
#: model:ir.model.fields,field_description:l10n_pl.field_account_payment__l10n_pl_vat_b_spv
msgid "B_SPV"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_account_bank_statement_line__l10n_pl_vat_b_spv_dostawa
#: model:ir.model.fields,field_description:l10n_pl.field_account_move__l10n_pl_vat_b_spv_dostawa
#: model:ir.model.fields,field_description:l10n_pl.field_account_payment__l10n_pl_vat_b_spv_dostawa
msgid "B_SPV_Dostawa"
msgstr ""

#. module: l10n_pl
#: model:account.report.column,name:l10n_pl.tax_report_balance
msgid "Balance"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_art_28b
msgid "Base - Acquisition under art. 28b"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_eksport_towarow
msgid "Base - Export of goods"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_towary_art_129
msgid "Base - Goods under art. 129"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_art_33a
msgid "Base - Import of goods under art. 33a"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_import_uslug
msgid "Base - Importation of services"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_nabycie_towarow
msgid "Base - Intra-Community acquisition of goods"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_dostawa_towarow
msgid "Base - Intra-Community supply of goods"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_uslug_pozostalych
msgid "Base - Purchase of other goods and services"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_uslugi_art_100_1_4
msgid "Base - Services included in art. 100.1.4"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatnik_nabywca
msgid "Base - Supply of goods, taxable person acquiring"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_uslugi_kraj_0
msgid "Base - Supply of goods/services, domestic, 0%"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_kraj_23
msgid "Base - Supply of goods/services, domestic, 23%"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_kraj_5
msgid "Base - Supply of goods/services, domestic, 5%"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_kraj_8
msgid "Base - Supply of goods/services, domestic, 8%"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_kraj_zwolnione
msgid "Base - Supply of goods/services, domestic, exempt"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_poza_kraj
msgid "Base - Supply of goods/services, out of the country"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_razem_c
msgid "Base - Total C"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_razem_d
msgid "Base - Total D"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_uslug_s_trwale
msgid "Basis - Acquisition of goods and services, fixed assets"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_l10n_pl_l10n_pl_tax_office__code
msgid "Code"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,help:l10n_pl.field_product_product__l10n_pl_vat_gtu
#: model:ir.model.fields,help:l10n_pl.field_product_template__l10n_pl_vat_gtu
msgid "Codes for specific types of products, needed for VAT declaration"
msgstr ""

#. module: l10n_pl
#: model:ir.model,name:l10n_pl.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_pl
#: model:ir.model,name:l10n_pl.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_pl
#: model:ir.model,name:l10n_pl.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_l10n_pl_l10n_pl_tax_office__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_l10n_pl_l10n_pl_tax_office__create_date
msgid "Created on"
msgstr ""

#. module: l10n_pl
#. odoo-python
#: code:addons/l10n_pl/models/account_move.py:0
#, python-format
msgid "Credit notes can't have a total amount greater than the invoice's"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_l10n_pl_l10n_pl_tax_office__name
msgid "Description"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_l10n_pl_l10n_pl_tax_office__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_pl
#: model_terms:ir.ui.view,arch_db:l10n_pl.product_template_form
msgid "GTU"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_product_product__l10n_pl_vat_gtu
#: model:ir.model.fields,field_description:l10n_pl.field_product_template__l10n_pl_vat_gtu
msgid "GTU Codes"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_01
msgid "GTU_01 - Alcoholic beverages"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_02
msgid "GTU_02 - Goods referred to under Art. 103 sec 5aa"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_03
msgid "GTU_03 - Fuel oil for excise duty, lubricating oils and other oils"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_04
msgid "GTU_04 - Tobacco products, tobacco, e-liquid"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_05
msgid "GTU_05 - Wastes"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_06
msgid "GTU_06 - Electronic devices, their parts and materials"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_07
msgid "GTU_07 - Vehicles and vehicle parts"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_08
msgid "GTU_08 - Precious metals and base metals"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_09
msgid "GTU_09 - Medicament and medical devices, medicinal products"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_10
msgid "GTU_10 - Buildings, structures and land"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_11
msgid ""
"GTU_11 - Services related to the greenhouse gas emission allowance trading"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_12
msgid "GTU_12 - Intangible services"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields.selection,name:l10n_pl.selection__product_template__l10n_pl_vat_gtu__gtu_13
msgid "GTU_13 - Transport services and warehouse management services"
msgstr ""

#. module: l10n_pl
#: model:account.account.tag,name:l10n_pl.gold_tag
msgid "Gold"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_l10n_pl_l10n_pl_tax_office__id
msgid "ID"
msgstr ""

#. module: l10n_pl
#: model:ir.model,name:l10n_pl.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_l10n_pl_l10n_pl_tax_office__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_l10n_pl_l10n_pl_tax_office__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_res_partner__l10n_pl_links_with_customer
#: model:ir.model.fields,field_description:l10n_pl.field_res_users__l10n_pl_links_with_customer
msgid "Links With Company"
msgstr ""

#. module: l10n_pl
#: model_terms:ir.ui.view,arch_db:l10n_pl.view_move_form_l10n_pl
msgid "PL Extra"
msgstr ""

#. module: l10n_pl
#: model_terms:ir.ui.view,arch_db:l10n_pl.res_partner_account_pl_form
msgid "PL Information"
msgstr ""

#. module: l10n_pl
#: model_terms:ir.ui.view,arch_db:l10n_pl.res_config_settings_view_form
msgid "Polish Localization"
msgstr ""

#. module: l10n_pl
#: model:ir.model,name:l10n_pl.model_product_template
msgid "Product"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,help:l10n_pl.field_account_bank_statement_line__l10n_pl_vat_b_mpv_prowizja
#: model:ir.model.fields,help:l10n_pl.field_account_move__l10n_pl_vat_b_mpv_prowizja
#: model:ir.model.fields,help:l10n_pl.field_account_payment__l10n_pl_vat_b_mpv_prowizja
msgid ""
"Supply of agency and other services pertaining to the transfer of a single-"
"purpose voucher"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,help:l10n_pl.field_account_bank_statement_line__l10n_pl_vat_b_spv_dostawa
#: model:ir.model.fields,help:l10n_pl.field_account_move__l10n_pl_vat_b_spv_dostawa
#: model:ir.model.fields,help:l10n_pl.field_account_payment__l10n_pl_vat_b_spv_dostawa
msgid ""
"Supply of goods and/or services covered by a single-purpose voucher to a "
"taxpayer"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,help:l10n_pl.field_res_partner__l10n_pl_links_with_customer
#: model:ir.model.fields,help:l10n_pl.field_res_users__l10n_pl_links_with_customer
msgid ""
"TP: Existing connection or influence between the customer and the supplier"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_s_trwale
msgid "Tax - Acquisition of goods and services, fixed assets"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_art_28b
msgid "Tax - Acquisition under art. 28b"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_s_trwalych
msgid "Tax - Adjustment of input tax on acquisition of fixed assets"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_pozostalych_nabyc
msgid "Tax - Adjustment of input tax on other acquisitions"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_kasy_rejestrujace
msgid "Tax - Expenditure on cash registers"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_okresie
msgid "Tax - Expenditure on cash registers to be reimbursed in the period"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_art_14_5
msgid "Tax - From physical inventory under art. 14.5"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_art_33a
msgid "Tax - Importation of goods under art. 33a"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_import_uslug
msgid "Tax - Importation of services"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_korekta_art89b1
msgid "Tax - Input tax adjustments under art. 89b sec 1"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_korekta_art89b4
msgid "Tax - Input tax adjustments under art. 89b sec 4"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_transp_termin
msgid "Tax - Inter-Community acquisition of means of transport"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_nabycie_towarow
msgid "Tax - Intra-Community acquisition of goods"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_wewnątrzwspólnotowe_103_5a
msgid "Tax - Intra-Community acquisition of goods under art. 103 sec. 5a"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_uslug_pozostalych
msgid "Tax - Purchase of other goods and services"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_zaniechaniem_poboru
msgid "Tax - Subject to non-collection"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_podatnik_nabywca
msgid "Tax - Supply of goods, taxable person acquiring"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_kraj_23
msgid "Tax - Supply of goods/services, domestic, 23%"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_kraj_5
msgid "Tax - Supply of goods/services, domestic, 5%"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_kraj_8
msgid "Tax - Supply of goods/services, domestic, 8%"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_deklaracji
msgid "Tax - Surplus from previous declaration"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_do_przeniesienia
msgid "Tax - To be carried over"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_razem_c
msgid "Tax - Total C"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_podatek_razem_d
msgid "Tax - Total D"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,field_description:l10n_pl.field_res_company__l10n_pl_reports_tax_office_id
#: model:ir.model.fields,field_description:l10n_pl.field_res_config_settings__l10n_pl_reports_tax_office_id
msgid "Tax Office"
msgstr ""

#. module: l10n_pl
#: model:ir.model,name:l10n_pl.model_l10n_pl_l10n_pl_tax_office
msgid "Tax Office in Poland"
msgstr ""

#. module: l10n_pl
#: model:account.report,name:l10n_pl.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_pl
#: model:ir.model.constraint,message:l10n_pl.constraint_l10n_pl_l10n_pl_tax_office_code_company_uniq
msgid "The code of the tax office must be unique !"
msgstr ""

#. module: l10n_pl
#: model:ir.model.fields,help:l10n_pl.field_account_bank_statement_line__l10n_pl_vat_b_spv
#: model:ir.model.fields,help:l10n_pl.field_account_move__l10n_pl_vat_b_spv
#: model:ir.model.fields,help:l10n_pl.field_account_payment__l10n_pl_vat_b_spv
msgid ""
"Transfer of a single-purpose voucher effected by a taxable person acting on "
"his/its own behalf"
msgstr ""

#. module: l10n_pl
#: model:account.report.line,name:l10n_pl.account_tax_report_line_triangular_2nd_payer
#: model:account.report.line,name:l10n_pl.account_tax_report_line_triangular_buyer_2nd_payer
msgid "Triangular transaction 2. VAT payer"
msgstr ""
