<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_pl" model="res.partner" forcecreate="1">
        <field name="name">PL Company</field>
        <field name="vat">PL5795955811</field>
        <field name="street"/>
        <field name="city">Racibórz</field>
        <field name="country_id" ref="base.pl"/>

        <field name="zip">47-400</field>
        <field name="phone">+48 ***********</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.plexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_pl" model="res.company" forcecreate="1">
        <field name="name">PL Company</field>
        <field name="partner_id" ref="base.partner_demo_company_pl"/>
    </record>

    <record id="base.demo_bank_pl" model="res.partner.bank" forcecreate="1">
        <field name="acc_number">PL47109024022846836723239796</field>
        <field name="partner_id" ref="base.partner_demo_company_pl"/>
        <field name="company_id" ref="base.demo_company_pl"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_pl')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_pl'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>pl</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_pl')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
