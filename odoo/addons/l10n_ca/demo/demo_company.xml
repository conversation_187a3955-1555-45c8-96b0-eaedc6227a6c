<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_ca" model="res.partner" forcecreate="1">
        <field name="name">CA Company</field>
        <field name="vat">*********</field>
        <field name="street">112 Place Charles-Le Moyne</field>
        <field name="city">Longueuil</field>
        <field name="country_id" ref="base.ca"/>
        <field name="state_id" ref="base.state_ca_yt"/>
        <field name="zip">J4K4Y9</field>
        <field name="phone">******-234-5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.caexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_ca" model="res.company" forcecreate="1">
        <field name="name">CA Company</field>
        <field name="paperformat_id" ref="base.paperformat_us"/>
        <field name="partner_id" ref="base.partner_demo_company_ca"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_ca')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [Command.link(ref('base.demo_company_ca'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>ca_2023</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_ca')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
