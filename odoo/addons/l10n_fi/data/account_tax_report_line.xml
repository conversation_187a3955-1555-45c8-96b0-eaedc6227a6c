<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="vat_report" model="account.report">
        <field name="name">VAT Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.fi"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="vat_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_sales_title" model="account.report.line">
                <field name="name">Tax on domestic sales by tax rates</field>
                <!-- "sale_25_5_and_24" refers to both 24% and 25.5% taxes. Will only be 25.5 on 01/01/2025.-->
                <field name="aggregation_formula">sale_25_5_and_24.balance + sale_14.balance + sale_10.balance</field>
                <field name="children_ids">
                    <record id="tax_report_sales_25_5_and_24" model="account.report.line">
                        <field name="name">25.5% tax + 24% tax</field>
                        <field name="code">sale_25_5_and_24</field>
                        <field name="foldable">True</field>
                        <field name="aggregation_formula">sale_25_5.balance + sale_24.balance</field>
                        <field name="children_ids">
                            <record id="tax_report_sales_25_5" model="account.report.line">
                                <field name="name">25.5% tax</field>
                                <field name="code">sale_25_5</field>
                                <field name="expression_ids">
                                    <record id="tax_report_sales_25_5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">fi_320</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_report_sales_24" model="account.report.line">
                                <field name="name">24% tax</field>
                                <field name="code">sale_24</field>
                                <field name="expression_ids">
                                    <record id="tax_report_sales_24_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">fi_301</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_sales_14" model="account.report.line">
                        <field name="name">14% tax</field>
                        <field name="code">sale_14</field>
                        <field name="expression_ids">
                            <record id="tax_report_sales_14_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">fi_302</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_sales_10" model="account.report.line">
                        <field name="name">10% tax</field>
                        <field name="code">sale_10</field>
                        <field name="expression_ids">
                            <record id="tax_report_sales_10_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">fi_303</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_tax_purchase_goods_eu" model="account.report.line">
                <field name="name">Tax on purchases of goods from other EU countries</field>
                <field name="code">goods_eu</field>
                <field name="expression_ids">
                    <record id="tax_report_tax_purchase_goods_eu_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_305</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_tax_purchase_service_eu" model="account.report.line">
                <field name="name">Tax on service purchases from other EU countries</field>
                <field name="code">service_eu</field>
                <field name="expression_ids">
                    <record id="tax_report_tax_purchase_service_eu_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_tax_306</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_tax_import_goods_no_eu" model="account.report.line">
                <field name="name">Tax on goods imported from outside the EU</field>
                <field name="code">goods_no_eu</field>
                <field name="expression_ids">
                    <record id="tax_report_tax_import_goods_no_eu_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_340</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_tax_purchase_construct_service" model="account.report.line">
                <field name="name">Tax on purchases of construction services and scrap metal (inverted tax liability)</field>
                <field name="code">construct</field>
                <field name="expression_ids">
                    <record id="tax_report_tax_purchase_construct_service_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_318</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_deductible" model="account.report.line">
                <field name="name">Deductible tax for the tax season</field>
                <field name="code">deductible</field>
                <field name="expression_ids">
                    <record id="tax_report_deductible_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_307</field>
                    </record>
                </field>
            </record>
            <record id="vat_report_relief" model="account.report.line">
                <field name="name">The amount of lower limit relief</field>
                <field name="expression_ids">
                    <record id="vat_report_relief_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_vat_relief</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_tax_payable" model="account.report.line">
                <field name="name">Payable tax / Refundable tax (-)</field>
                <!-- "sale_25_5_and_24" refers to both 24% and 25.5% taxes. Will only be 25.5 on 01/01/2025.-->
                <field name="aggregation_formula">sale_25_5_and_24.balance + sale_14.balance + sale_10.balance + goods_eu.balance + service_eu.balance + goods_no_eu.balance + construct.balance - deductible.balance</field>
            </record>
            <record id="tax_report_base_turnover_0_vat" model="account.report.line">
                <field name="name">0-Turnover subject to zero tax rate</field>
                <field name="expression_ids">
                    <record id="tax_report_base_turnover_0_vat_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_304</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_base_sales_goods_eu" model="account.report.line">
                <field name="name">Sales of goods to other EU countries</field>
                <field name="expression_ids">
                    <record id="tax_report_base_sales_goods_eu_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_311</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_base_sales_service_eu" model="account.report.line">
                <field name="name">Sales of services to other EU countries</field>
                <field name="expression_ids">
                    <record id="tax_report_base_sales_service_eu_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_312</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_base_purchase_goods_eu" model="account.report.line">
                <field name="name">Purchases of goods from other EU countries</field>
                <field name="expression_ids">
                    <record id="tax_report_base_purchase_goods_eu_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_base_purchase_goods_eu</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_base_purchase_service_eu" model="account.report.line">
                <field name="name">Service purchases from other EU countries</field>
                <field name="expression_ids">
                    <record id="tax_report_base_purchase_service_eu_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_base_306</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_base_import_goods_no_eu" model="account.report.line">
                <field name="name">Imports of goods from outside the EU</field>
                <field name="expression_ids">
                    <record id="tax_report_base_import_goods_no_eu_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_base_340</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_base_sales_construct_service" model="account.report.line">
                <field name="name">Sales of construction services and scrap metal (inverted tax liability)</field>
                <field name="expression_ids">
                    <record id="tax_report_base_sales_construct_service_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_319</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_base_purchase_construct_service" model="account.report.line">
                <field name="name">Purchases of construction services and scrap metal (inverted tax liability)</field>
                <field name="expression_ids">
                    <record id="tax_report_base_purchase_construct_service_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">fi_base_318</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
