# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll_holidays
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid ""
"A great way to keep track on employee’s PTOs, sick days, and approval "
"status."
msgstr ""
"Una excelente manera de realizar un seguimiento de las ausencias pagadas de "
"los empleados, los permisos por enfermedad y su estado de aprobación."

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_mail_activity
msgid "Activity"
msgstr "Actividad"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__done
msgid "Computed in current payslip"
msgstr "Calculado en el recibo de nómina actual"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: hr_payroll_holidays
#: model:ir.actions.server,name:hr_payroll_holidays.ir_actions_server_report_to_next_month
msgid "Defer to Next Month"
msgstr "Aplazar al próximo mes"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Deferred Time Off"
msgstr "Ausencias aplazadas"

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_company__deferred_time_off_manager
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_res_config_settings__deferred_time_off_manager
msgid "Deferred Time Off Manager"
msgstr "Gerente de ausencias aplazadas"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.view_hr_leave_allocation_inherit_filter
msgid "Employee Code"
msgstr "Código del empleado"

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_hr_contract
msgid "Employee Contract"
msgstr "Contrato del empleado"

#. module: hr_payroll_holidays
#: model:mail.activity.type,name:hr_payroll_holidays.mail_activity_data_hr_leave_to_defer
msgid "Leave to Defer"
msgstr "Permiso a aplazar"

#. module: hr_payroll_holidays
#: model:ir.actions.server,name:hr_payroll_holidays.ir_actions_server_mark_as_reported
msgid "Mark as deferred"
msgstr "Marcar como aplazado"

#. module: hr_payroll_holidays
#: model_terms:ir.actions.act_window,help:hr_payroll_holidays.hr_leave_work_entry_action
msgid "Meet the time off dashboard."
msgstr "Conozca el tablero de ausencias."

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid ""
"Not enough attendance work entries to report the time off %s. Please make "
"the operation manually"
msgstr ""
"No hay suficientes entradas de trabajo de asistencia para trasladar la "
"ausencia %s. Por favor, realice la operación manualmente"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid "Only an employee time off to defer can be reported to next month"
msgstr ""
"Sólo la ausencia de un empleado para aplazar puede ser traslada al próximo "
"mes."

#. module: hr_payroll_holidays
#: model:ir.model,name:hr_payroll_holidays.model_hr_payslip
msgid "Pay Slip"
msgstr "Recibo de nómina"

#. module: hr_payroll_holidays
#: model:ir.model.fields,field_description:hr_payroll_holidays.field_hr_leave__payslip_state
msgid "Payslip State"
msgstr "Estado del recibo de nómina"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid "Please create manually the work entry for %s"
msgstr "Cree la entrada de trabajo para %s manualmente."

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Postpone time off after payslip validation"
msgstr ""
"Posponer las ausencias a después de la validación del recibo de nómina"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_form_inherit
msgid "Report to Next Month"
msgstr "Trasladar al próximo mes"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Responsible"
msgstr "Responsable"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid ""
"The next month work entries are not generated yet or are validated already "
"for time off %s"
msgstr ""
"Aún no se generan las entradas de trabajo del próximo mes o ya se validaron "
"para la ausencia %s"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid ""
"The pay of the month is already validated with this day included. If you "
"need to adapt, please refer to HR."
msgstr ""
"El pago del mes ya está validado e incluye este día. Si necesita "
"modificarlo, consulte a RR. HH."

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid ""
"The time off %s can not be reported because it is defined over more than 2 "
"months"
msgstr ""
"La ausencia %s no se puede trasladar porque se define en más de 2 meses "

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid "There is no work entries linked to this time off to report"
msgstr "No hay entradas de trabajo vinculadas a esta ausencia para trasladar"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_payslip.py:0
msgid ""
"There is some remaining time off to defer for these employees: \n"
"\n"
" %s"
msgstr ""
"Hay ausencias restantes por aplazar para estos empleados: \n"
"\n"
" %s"

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_work_entry_action
#: model:ir.model,name:hr_payroll_holidays.model_hr_leave
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.res_config_settings_view_form
msgid "Time Off"
msgstr "Ausencias"

#. module: hr_payroll_holidays
#: model:hr.payroll.dashboard.warning,name:hr_payroll_holidays.hr_payroll_dashboard_warning_leaves_no_allocation
msgid "Time Off Not Related To An Allocation"
msgstr "Ausencia no relacionada con una asignación"

#. module: hr_payroll_holidays
#: model:hr.payroll.dashboard.warning,name:hr_payroll_holidays.hr_payroll_dashboard_warning_leaves_to_defer
msgid "Time Off To Defer"
msgstr "Ausencia a aplazar"

#. module: hr_payroll_holidays
#: model:hr.payroll.dashboard.warning,name:hr_payroll_holidays.hr_payroll_dashboard_warning_leaves_no_document
msgid "Time Off Without Joined Document"
msgstr "Ausencia sin documento unido"

#. module: hr_payroll_holidays
#: model:ir.actions.act_window,name:hr_payroll_holidays.hr_leave_action_open_to_defer
msgid "Time Off to Defer"
msgstr "Ausencia a aplazar"

#. module: hr_payroll_holidays
#: model:ir.ui.menu,name:hr_payroll_holidays.menu_work_entry_leave_to_approve
msgid "Time Off to Report"
msgstr "Ausencia a trasladar"

#. module: hr_payroll_holidays
#: model_terms:ir.ui.view,arch_db:hr_payroll_holidays.hr_leave_view_search
msgid "To Defer"
msgstr "A aplazar"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__normal
msgid "To compute in next payslip"
msgstr "Para calcular en el siguiente recibo de nómina"

#. module: hr_payroll_holidays
#: model:ir.model.fields.selection,name:hr_payroll_holidays.selection__hr_leave__payslip_state__blocked
msgid "To defer to next payslip"
msgstr "A aplazar en el siguiente recibo de nómina"

#. module: hr_payroll_holidays
#. odoo-python
#: code:addons/hr_payroll_holidays/models/hr_leave.py:0
msgid "Validated Time Off to Defer"
msgstr "Ausencia validada a aplazar"

#. module: hr_payroll_holidays
#. odoo-javascript
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
msgid "You have some"
msgstr "Tiene"

#. module: hr_payroll_holidays
#. odoo-javascript
#: code:addons/hr_payroll_holidays/static/src/views/hooks.js:0
msgid "You have some <button>time off</button> to defer to the next month."
msgstr "Tiene algunas <button>ausencias</button> para aplazar al próximo mes."

#. module: hr_payroll_holidays
#. odoo-javascript
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
msgid "time off"
msgstr "ausencias"

#. module: hr_payroll_holidays
#. odoo-javascript
#: code:addons/hr_payroll_holidays/static/src/xml/templates.xml:0
msgid "to defer to the next month."
msgstr "para aplazar al próximo mes."
