# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr "#{year}"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr "\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr "\". Mostrando resultados para \""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr "- Todas las fechas"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""
"<b>Los prismáticos son ligeros y portátiles.</b> A menos que tenga el lujo "
"de instalar y dirigir un observatorio desde el patio de su casa, lo más "
"probable es que viaje para realizar sus observaciones. Los prismáticos son "
"mucho más fáciles de llevar y de transportar que un voluminoso telescopio."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""
"<b>Elija los cerebros de los expertos</b>. Si aún no está activo en una "
"sociedad o club de astronomía, los vendedores de la tienda de telescopios "
"podrán guiarlo a las sociedades activas en su área. Una vez que tenga "
"conexiones con personas que han comprado telescopios, puede obtener consejos"
" sobre qué funciona y qué evitar que sea más válido que cualquier cosa que "
"obtenga de un artículo web o de un vendedor de Wal-Mart."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr ""
"<b>Comparta la publicación en su blog</b> para que sus visitantes puedan "
"verla."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>Iniciar sesión</b>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""
"<b>Pruebe antes de comprar.</b> Esta es otra ventaja de realizar algunas "
"excursiones con el club de astronomía. Puede reservar algunas horas de "
"calidad con personas que conocen los telescopios y tienen sus equipos "
"configurados para examinar su equipo, aprender los aspectos técnicos clave y"
" probarlos antes de invertir dinero en su propia configuración."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>Escriba su historia aquí.</b> Use la barra de herramientas para diseñar "
"su texto (añadir una imagen o tabla, utilizar negrita o cursiva, etc.). "
"Arrastre y suelte componentes para hacer más vistosos y gráficas los blogs."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">Además de la población nativa, la vida silvestre local"
" también atrae a las multitudes.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""
"<em class=\"h4 my-0\">Es de vital importancia que obtenga el telescopio "
"adecuado.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr ""
"<em class=\"h4 my-0\">Ese momento 'Wow' es de lo que se trata la "
"astrología.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">Cuantas más reseñas lea, más notará cómo tienden a "
"agruparse en los extremos de las opiniones.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">Hay algo atemporal en el cosmos.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">Su estudio de la luna, como cualquier otra cosa, puede"
" ir de lo simple a lo muy complejo.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>No hay etiquetas definidas</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"
msgstr "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Leer "
"siguiente</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"me-1\">Show:</span>"
msgstr "<span class=\"me-1\">Mostrar:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled ps-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled ps-0\">Blogs:</span>"

#. module: website_blog
#: model_terms:web_tour.tour,rainbow_man_message:website_blog.blog
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>¡Buen trabajo!</b> Pasó por todos los pasos de este "
"recorrido.</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "Una excelente manera de descubrir lugares escondidos"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"Unas vacaciones en las Barrancas del Cobre prometen ser una emocionante "
"combinación de relajación, cultura, historia, vida salvaje y senderismo."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "Una nueva publicación"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"Un viajero puede optar por explorar el área caminando alrededor del cañón o "
"aventurándose en él. Se requiere una planificación detallada para aquellos "
"que deseen aventurarse en las profundidades del cañón. Hay varias compañías "
"de viajes que se especializan en organizar viajes a la región. Los "
"visitantes pueden volar a Barrancas del Cobre con una visa de turista, que "
"es válida por 180 días. Los viajeros también pueden conducir desde cualquier"
" lugar de los Estados Unidos y adquirir una visa en la estación de aduanas "
"mexicana en la frontera."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "Sobre nosotros"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"Sobre todo, <b>establezca una relación con una tienda de telescopios de "
"buena reputación</b> que emplee a personas que sepan lo que hacen. Si compra"
" su telescopio en un Wal-Mart o en una tienda departamental, las "
"probabilidades de que obtenga lo correcto son remotas."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "Acceder a la publicación"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Activo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "Añadir algunas"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "Todos"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "Todos los blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "Todos los blogs"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Alone in the ocean"
msgstr "Solo en el océano"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "En ese sentido, ¿qué tan difícil es la configuración y el desglose?"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/website_blog.js:0
msgid "Amazing blog article: %(title)s! Check it live: %(url)s"
msgstr "Increíble artículo de blog: %(title)s Véalo publicado: %(url)s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"Una emocionante mezcla de relajación, cultura, historia, vida salvaje y "
"senderismo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"Y cuando todo esté dicho y hecho, <b>prepárese</b>. Su búsqueda de "
"telescopios más nuevos y mejores será para toda la vida. Déjese volver "
"adicto a la astronomía y la experiencia enriquecerá todos los aspectos de la"
" vida. Será una adicción que nunca querrá romper."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"Otra característica única de las Barrancas del Cobre es la presencia de la "
"cultura indígena Tarahumara. Estas personas semi-nómadas viven en viviendas "
"cueva. Su sustento depende principalmente de la agricultura y la ganadería."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archive"
msgstr "Archivar"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "Archivado"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archives"
msgstr "Archivos"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "Artículo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "Artículos"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "Astronomía"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"Los clubes de astronomía son lugares animados llenos de aficionados "
"conocedores a los que les encanta compartir sus conocimientos contigo. Por "
"el precio de una coca cola y bocadillos, irán a mirar las estrellas contigo "
"y te abrumarán con trivialidades y gran conocimiento."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "La astronomía es 'mirar las estrellas'"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom Feed"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "Nombre del autor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "Avatar"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Awesome hotel rooms"
msgstr "Geniales habitaciones de hotel"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "Tenga en cuenta lo que se llama 'astronomía'"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"Formar parte de la sociedad de astrónomos aficionados devotos le dará acceso"
" a estos esfuerzos organizados para alcanzar nuevos niveles en nuestra "
"capacidad para estudiar la Luna de la Tierra. Y le proporcionará compañeros "
"y amigos que comparten su pasión por la astronomía y que pueden compartir su"
" experiencia y áreas de especialización mientras busca dónde podría mirar a "
"continuación en el inmenso cielo nocturno, en la Luna y más allá de ella en "
"su búsqueda de conocimientos sobre el universo aparentemente infinito que "
"hay sobre nosotros."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "Formar parte de una sociedad de astrónomos"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "Instalaciones del dormitorio"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"Antes de hacer ese gran gasto, podría ser un mejor paso a simple vista "
"invertir en un buen par de prismáticos. Incluso hay prismáticos que son "
"adecuados para observar las estrellas que harán un trabajo igual de bien al "
"proporcionarle esa visión adicional que desea para ver un poco mejor las "
"maravillas del universo. Un conjunto de prismáticos bien diseñado también le"
" proporciona mucha más movilidad y capacidad para mantener su \"visión "
"mejorada\" al alcance de la mano cuando esa vista increíble se le presenta."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "Antes de realizar su primera compra..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "Más allá del ojo"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog"
msgstr "Blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "Nombre del blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blog Page"
msgstr "Página del blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog Post"
msgstr "Publicación de blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "Portada de publicación de blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_post
msgid "Blog Post Pages"
msgstr "Páginas de publicaciones de blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Título de la publicación del blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_post_pages
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blog Posts"
msgstr "Publicaciones del blog"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Subtítulo del blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Etiqueta del blog"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "Categoría de etiqueta de blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "Etiquetas del blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "Título del blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "Título del blog"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blogs"
msgstr "Blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs List"
msgstr "Lista de blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs Page"
msgstr "Página de blogs"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Bottom"
msgstr "Abajo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Breadcrumb"
msgstr "Migas de pan"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"Pero, ¿cómo examina las increíbles opciones que se ofrecen? Y lo que es más "
"importante, ¿realmente confía en las fotografías y descripciones de los "
"hoteles que se han premiado con la motivación de conseguir reservas? Las "
"reseñas de viajeros pueden ser útiles, pero debe tener cuidado. A menudo son"
" parciales, a veces desactualizados y es posible que no sirvan para nada a "
"sus intereses. ¿Cómo sabe que las características que son importantes para "
"el revisor lo son para usted?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "Comprar un telescopio"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"Comprar el telescopio adecuado para llevar su amor por la astronomía al "
"siguiente nivel es un gran paso en el desarrollo de su pasión por las "
"estrellas."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cards"
msgstr "Tarjetas"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Categoría"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "Instalaciones para niños"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Choose an image from the library."
msgstr "Elija una imagen de la biblioteca."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Click here to add new content to your website."
msgstr "Haga clic aquí para añadir contenido nuevo a su sitio web."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""
"Haga clic en \"<b>Nuevo</b>\" en la esquina superior derecha para escribir "
"su primera publicación de blog."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "Cerrar"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__color
msgid "Color"
msgstr "Color"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "Comentario"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments"
msgstr "Comentarios"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments/Views Stats"
msgstr "Estadísticas de comentarios/vistas"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Contenido"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"La Barranca del Cobre es una de las seis barrancas de la zona. Aunque el "
"nombre sugiere que barranca podría tener alguna relevancia para la minería "
"del cobre, este no es el caso. El nombre se deriva del liquen de cobre y "
"verde que cubre el cañón. Las Barrancas del Cobre tienen dos zonas "
"climáticas. La región presenta un clima alpino en la parte superior y un "
"clima subtropical en los niveles inferiores. Los inviernos son fríos con "
"frecuentes tormentas de nieve en las altitudes más altas. Los veranos son "
"secos y calurosos. La ciudad capital, Chihuahua, es un desierto de gran "
"altitud donde el clima varía desde inviernos fríos hasta veranos calurosos. "
"La región es única por los diversos ecosistemas que existen en ella."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cover"
msgstr "Portada"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "Propiedades de la portada"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Date"
msgstr "Fecha"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Fecha (de más recientes a más antiguos)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Fecha (de más antiguos a más recientes)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "Descripción"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "Dexter"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "Este de Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"Los recorridos en helicóptero por el este de Maui le darán una vista del "
"volcán de diez mil pies, Haleakala o la Casa del sol. Este volcán está "
"inactivo y entró en erupción por última vez en 1790. Podrá ver el cráter del"
" volcán y la tierra seca y árida que rodea el lado sur de la pendiente del "
"volcán con recorridos en helicóptero por Maui."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "Editar en el backend"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "Edite el encabezado de la página \""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "Edite el encabezado de la página \"Todos los blogs\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "Edite el encabezado de la página 'Filtrar resultados'."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Edit your title, the subtitle is optional."
msgstr "Edite su título, el subtítulo es opcional."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Enter your post's title"
msgstr "Escriba el título de su publicación"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "Hechos que debe tener en cuenta."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"Por último, y lo más importante, el equipo de inspección del directorio de "
"hoteles de calidad debería haber visitado el hotel en cuestión con "
"regularidad, reunirse con el personal, dormir en un dormitorio y probar la "
"comida. Deben experimentar el hotel como solo un huésped del hotel puede "
"hacerlo y solo entonces están realmente en una posición sólida para escribir"
" sobre el hotel."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Follow Us"
msgstr "Síganos"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"Para muchos de nosotros que somos habitantes de la ciudad, realmente no "
"notamos ese cielo allá arriba de forma rutinaria. Las luces de la ciudad "
"hacen un buen trabajo al disfrazar la asombrosa exhibición que está sobre "
"nuestras cabezas todo el tiempo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"Para muchos de nosotros, nuestra primera experiencia de aprendizaje sobre "
"los cuerpos celestes comienza cuando vimos nuestra primera luna llena en el "
"cielo. Es realmente una vista magnífica incluso a simple vista."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"Desde el bebé más pequeño hasta el astrofísico más avanzado, hay algo para "
"cualquiera que quiera disfrutar de la astronomía. De hecho, es una ciencia "
"tan accesible que prácticamente cualquiera puede hacerlo prácticamente en "
"cualquier lugar. Todo lo que tienen que saber hacer es mirar hacia arriba."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Full-Width"
msgstr "Ancho completo"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "Hablar con un experto"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "Conseguir un telescopio"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "Obtener algo de historia"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "Empezar"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Grid"
msgstr "Cuadrícula"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "Estos son algunos de los datos clave que debe tener en cuenta:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "Consejos de vacaciones"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Hover Effect"
msgstr "Efecto al pasar el ratón"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "Cómo mirar hacia arriba"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"¿Qué tan complejo es el telescopio y tendrá problemas con el mantenimiento? "
"Conéctese para obtener respuestas a estas y otras preguntas. Si hace su "
"tarea de esta manera, encontrará el telescopio adecuado para este próximo "
"gran paso en la evolución de su pasión por la astronomía."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "¿Qué tan móvil debe ser su telescopio?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "How to choose the right hotel"
msgstr "Cómo elegir el hotel adecuado"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"Si importa que su hotel esté, por ejemplo, en la playa, cerca del parque "
"temático o conveniente para el aeropuerto, entonces la ubicación es "
"primordial. Cualquier directorio decente debería ofrecer un mapa de "
"ubicación del hotel y sus alrededores. Debería haber cartas de distancia al "
"aeropuerto ofrecidas, así como algún tipo de mapa interactivo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"Si la noche está despejada, puede ver detalles asombrosos de la superficie lunar simplemente mirando las estrellas en su patio trasero.\n"
"Naturalmente, a medida que crece en su amor por la astronomía, encontrará fascinantes muchos cuerpos celestes. Pero la luna puede ser siempre nuestro primer amor porque es el único objeto espacial lejano que tiene la distinción única de volar cerca de la tierra y sobre el que ha caminado el hombre."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
msgid "In"
msgstr "Dentro"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"En muchos sentidos, es un gran paso de alguien que está jugando con la "
"astronomía a un estudiante serio de la ciencia. Pero usted y yo sabemos que "
"hay otro gran paso después de comprar un telescopio antes de que realmente "
"sepa cómo usarlo."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Increase Readability"
msgstr "Aumentar la legibilidad"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Islands"
msgstr "Islas"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"Es muy divertido comenzar a aprender las constelaciones, cómo navegar por el"
" cielo nocturno y encontrar los planetas y las estrellas famosas. Hay sitios"
" web y libros en abundancia para guiarlo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"Es importante elegir un hotel que lo haga sentir cómodo: muebles "
"contemporáneos o tradicionales, decoración local o internacional, formal o "
"relajada. El directorio de hoteles ideal debería informarle de las opciones "
"disponibles."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"Es seguro decir que en algún momento de nuestras vidas, todos y cada uno de "
"nosotros tenemos ese momento en el que de repente nos quedamos atónitos "
"cuando nos encontramos cara a cara con la enormidad del universo que vemos "
"en el cielo nocturno."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"Realmente es asombroso cuando lo piensa que con solo mirar hacia arriba en "
"una noche determinada, podría ver virtualmente cientos de miles de "
"estrellas, sistemas estelares, planetas, lunas, asteroides, cometas y tal "
"vez incluso un transbordador espacial ocasional podría pasar por ahí. Es aún"
" más impresionante cuando le da cuenta de que el cielo que está mirando "
"hacia arriba es, para todos los efectos, exactamente el mismo cielo que "
"nuestros antepasados hace cientos y miles de años disfrutaron cuando solo "
"miraron hacia arriba."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Jungle"
msgstr "Jungla"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "Sepa lo que está mirando"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "Saber cuando mirar"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Large"
msgstr "Grande"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Último colaborador"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "Últimas publicaciones en el blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Layout"
msgstr "Diseño"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"Aprender los antecedentes de los grandes descubrimientos en astronomía hará "
"que sus momentos de observación de estrellas sean más significativos. Es una"
" de las ciencias más antiguas de la tierra, así que descubra a los grandes "
"de la historia que han mirado estas estrellas antes que usted."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "Instalaciones de ocio"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "List"
msgstr "Lista"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"El color local es fantástico, pero los restaurantes y bares del hotel pueden"
" desempeñar un papel importante en su estancia. Debe tener en cuenta la "
"elección, el estilo y si son inteligentes o informales o no. Un buen informe"
" del hotel debería informarle esto, y en particular sobre las instalaciones "
"para el desayuno."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "Ubicación"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Marley"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "Maui helicopter tours"
msgstr "Recorridos en helicóptero por Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"Los recorridos en helicóptero por Maui son una excelente manera de ver la "
"isla desde una perspectiva diferente y tener una aventura divertida. Si "
"nunca antes ha estado en un helicóptero, este es un gran lugar para hacerlo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"Los recorridos en helicóptero por Maui son una excelente manera de recorrer "
"aquellos lugares a los que no se puede llegar a pie o en coche. Los "
"recorridos duran aproximadamente una hora y van desde aproximadamente ciento"
" ocho cinco dólares hasta doscientos cuarenta dólares por persona. Para "
"muchos, esta es una oportunidad única en la vida de ver paisajes naturales "
"que no estarán disponibles nuevamente. Tomar cámaras y vídeos para capturar "
"los momentos también le permitirá revivir el recorrido una y otra vez "
"mientras recuerda a lo largo de los años."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"Los recorridos en helicóptero por Maui le permitirán ver todos estos "
"lugares. Asegúrese de llevar una cámara o un vídeo cuando vaya de excursión "
"en helicóptero a Maui para capturar la belleza del paisaje y mostrarle a sus"
" amigos y familiares en casa todas las cosas maravillosas que vio durante "
"sus vacaciones."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Medium"
msgstr "Medio"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "Metadescripción"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "Palabras clave meta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "Título meta"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "Molokai Maui"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"Los recorridos en helicóptero de Molokai Maui lo llevarán a una isla diferente, pero que está a solo nueve millas de distancia y es de fácil acceso por aire. Esta isla tiene una población muy pequeña con una cultura y un paisaje diferentes. Toda la costa del noreste está bordeada de acantilados y playas remotas. Son completamente inaccesibles por cualquier otro medio de transporte que no sea el aire.\n"
"Las personas que viven en la isla nunca han visto este paisaje extraordinario a menos que hayan realizado recorridos en helicóptero por Maui para verlo. Cuando el clima ha sido lluvioso y hay mucha lluvia durante la temporada, verá muchas cascadas asombrosas."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"Más importante para el viajero familiar que para el viajero de negocios, "
"debe averiguar qué tan amigable para los niños es el hotel en el directorio "
"y tomar una decisión a partir de ahí. Una cosa que vale la pena buscar es si"
" el hotel ofrece un servicio de niñera. Para el viajero de negocios que "
"desee escapar de los niños, esto también es muy relevante, ¡quizás un hotel "
"que no sea apto para niños sería algo más apropiado!"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "Publicaciones del blog más vistas"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Nombre"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.blog_post_action_add
msgid "New Blog Post"
msgstr "Nueva publicación en el blog"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Next Article"
msgstr "Siguiente artículo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "No Cover"
msgstr "Sin portada"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "Todavía no hay publicaciones en el blog."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "Nº de visitas"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "Sin resultados para \"%s\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "No se han encontrado resultados para \""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "Aún no se han definido etiquetas."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "Ninguno"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"Nada de esto le impide seguir adelante con sus planes para armar un "
"impresionante sistema de telescopio. Solo asegúrese de recibir asesoramiento"
" y capacitación de calidad sobre cómo configurar su telescopio para "
"satisfacer sus necesidades. Con estas pautas, disfrutará de horas de "
"disfrute contemplando las estrellas en las vistas fenomenales del cielo "
"nocturno que están más allá del ojo humano."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Normal"
msgstr "Normal"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Normal picture"
msgstr "Imagen normal"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Not Published"
msgstr "No publicado"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"No solo conocer el clima asegurará que su observación de las estrellas sea "
"gratificante, sino que si se entera de cuándo ocurrirán las grandes lluvias "
"de meteoritos y otros grandes eventos astronómicos, la emoción de la "
"astronomía cobrará vida para usted."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"Por supuesto, para llevar su adoración a la luna al máximo, aumentar su "
"equipo a un buen telescopio de inicio le dará el detalle más impresionante "
"de la superficie lunar. Con cada una de estas actualizaciones, su "
"conocimiento y la profundidad y el alcance de lo que podrá ver mejorarán "
"geométricamente. Para muchos astrónomos aficionados, a veces no podemos "
"obtener suficiente de lo que podemos ver en este nuestro objeto espacial más"
" cercano."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid ""
"Once you have reviewed the content on mobile, you can switch back to the "
"normal view by clicking here again"
msgstr ""
"Una vez que haya revisado el contenido en el móvil, puede volver a la vista "
"normal pulsando aquí de nuevo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "Otros"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Our Latest Posts"
msgstr "Nuestras publicaciones más recientes"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "Nuestros blogs"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "Foto de Anton Repponen, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "Foto de Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "Foto de Boris Smokrovic, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "Foto de Denys Nevozhai, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "Foto de Greg Rakozy, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "Foto de Jason Briscoe, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "Foto de Jon Ly, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "Foto de Patrick Brinksma, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "Foto de PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "Foto de SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "Foto de Teddy Kelley, @teddykelley"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Picture size"
msgstr "Tamaño de la imagen"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "Publicaciones"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Posts List"
msgstr "Lista de publicaciones"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Fecha de publicación"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Published"
msgstr "Publicado"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "Publicado ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "Fecha publicada"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Publicación publicada"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "Opciones de publicación"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "Fecha de publicación"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__rating_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"oi oi-chevron-right ms-2\"/>"
msgstr "Leer más <i class=\"oi oi-chevron-right ms-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "Restaurantes, cafés y bares"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict to a specific website."
msgstr "Restringir a un sitio web específico."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "Optimizado para SEO"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "Muestra"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Satellites"
msgstr "Satélites"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Search for an image. (eg: type \"business\")"
msgstr "Busque una imagen. (p. ej. escriba \"empresa\")"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Seaside vs mountain side"
msgstr "Mar contra montaña"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Seeing the world from above"
msgstr "Ver el mundo desde arriba"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
msgid "Select Blog"
msgstr "Seleccionar blog"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Select the blog you want to add the post to."
msgstr "Seleccione el blog al que desea añadir la publicación."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Select this menu item to create a new blog post."
msgstr ""
"Seleccione este elemento de menú para crear una nueva publicación en el "
"blog."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Comment"
msgstr "Seleccionar para comentar"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Tweet"
msgstr "Seleccionar para tuit"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "Nombre SEO"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "Separe cada palabra clave con una coma."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Set a blog post <b>cover</b>."
msgstr "Establezca la <b>portada</b> de una publicación del blog."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"Varios pájaros migratorios y nativos, mamíferos y reptiles llaman a las "
"Barrancas del Cobre su hogar. También vale la pena echarle un vistazo a la "
"exquisita fauna de esta tierra casi virgen."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Share Links"
msgstr "Compartir enlaces"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Compartir en Facebook"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "Compartir en Linkedin"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on X"
msgstr "Compartir en X"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "Compartir esta publicación"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Sidebar"
msgstr "Barra lateral"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "Sierra Tarahumara"
msgstr "Sierra Tarahumara"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"La Sierra Tarahumara, conocida popularmente como Barrancas del Cobre, se "
"encuentra en México. El área es un destino favorito entre aquellos que "
"buscan unas vacaciones aventureras."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Chico tonto"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Skies"
msgstr "Cielos"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Smaller"
msgstr "Más pequeño"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Smaller picture"
msgstr "Imagen más pequeña"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"Por lo tanto, es de vital importancia que obtenga el telescopio adecuado "
"para el lugar donde se encuentra y cuáles son sus preferencias de "
"observación de estrellas. Para empezar, analicemos los tres tipos "
"principales de telescopios y luego establezcamos algunos conceptos de "
"\"Telescopio 101\" para aumentar sus posibilidades de comprar lo correcto."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"Puede ser que durante unas vacaciones en un campamento o en la casa de un "
"pariente en el campo, nos encontremos en el exterior y el cielo nocturno "
"decida ofrecer un espectáculo. Si ha tenido ese tipo de momento en el que se"
" ha quedado literalmente sin aliento por el espectáculo que el cielo "
"nocturno puede mostrarnos, quizá pueda recordar ese momento exacto en el que"
" no pudo decir nada más que \"wow\" ante lo que vio."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"Entonces, para seleccionar el tipo correcto de telescopio, sus objetivos al "
"usar el telescopio son importantes. Para comprender realmente las fortalezas"
" y debilidades no solo de las lentes y el diseño del telescopio, sino "
"también de cómo funciona el telescopio en diversas situaciones de "
"observación de estrellas, es mejor hacer algunos deberes por adelantado y "
"exponerse a los diferentes tipos. Entonces, antes de realizar su primera "
"compra..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr ""
"Así que se va al extranjero, ha elegido su destino y ahora tiene que elegir "
"un hotel."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr "Alguien famoso en el <cite title=\"título fuente\">título fuente</cite>"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Spotting the fauna"
msgstr "Observar la fauna"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
msgid "Start writing here..."
msgstr "Empiece a escribir aquí..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "Estilo"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "Subtítulo"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "Subtítulo"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "Categorías de etiquetas"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "Categoría de etiqueta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "Formulario de categoría de etiqueta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Formulario de etiqueta"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Lista de etiquetas"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists!"
msgstr "¡La categoría de etiquetas ya existe!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists!"
msgstr "¡El nombre de la etiqueta ya existe!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "Etiquetas"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags List"
msgstr "Lista de etiquetas"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Taking pictures in the dark"
msgstr "Tomar fotografías en la oscuridad"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Teaser"
msgstr "Avance"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Teaser & Tags"
msgstr "Avance y etiquetas"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "Contenido del avance"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"Hace diez años, probablemente habría visitado a su agente de viajes local y "
"habría confiado en los consejos en persona que le dieron los llamados "
"'expertos'. La forma del siglo XXI de seleccionar y reservar su hotel es, "
"por supuesto, a través de Internet, mediante el uso de sitios web de viajes."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"Ese momento 'Wow' es de lo que se trata la astrología. Para algunos, ese "
"momento asombroso se convierte en una pasión que los lleva a una carrera "
"estudiando las estrellas. Para unos pocos afortunados, ese momento asombroso"
" se debe a una obsesión que los lleva a viajar a las estrellas en el "
"transbordador espacial o en una de nuestras primeras misiones espaciales. "
"Pero para la mayoría de nosotros, la astrología puede convertirse en un "
"pasatiempo o un pasatiempo habitual. Pero llevamos ese momento asombroso con"
" nosotros por el resto de nuestras vidas y comenzamos a buscar formas de "
"mirar más profundamente y aprender más sobre el universo espectacular que "
"vemos en los millones de estrellas sobre nosotros cada noche."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr "La belleza de la astronomía es que cualquiera puede hacerlo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"El mejor momento para ver la luna, obviamente, es por la noche cuando hay "
"pocas nubes y el clima es propicio para un estudio largo y duradero. El "
"primer trimestre arroja el mayor detalle de estudio. Y no te dejes engañar "
"por el borrado de parte de la luna cuando no está en la etapa de luna llena."
" El fenómeno conocido como 'luz de la tierra' le da la capacidad de ver la "
"parte oscurecida de la luna con algunos detalles también, incluso si la luna"
" está solo en un cuarto o medio punto."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "El mejor momento para ver la luna."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"La entrada del blog será visible para sus visitantes en la fecha indicada en"
" el sitio web si se fija como publicada."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"Los acantilados de esta región son algunos de los más altos del mundo y "
"contemplar el agua caer en cascada desde los altos picos es sencillamente "
"impresionante. El corto viaje desde Maui con las excursiones en helicóptero "
"de Maui merece la pena para contemplar la belleza de este entorno natural."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"Lo siguiente que queremos conseguir, por supuesto, es un buen telescopio. Es"
" posible que haya visto a algún aficionado muy avanzado en sus estudios "
"instalando estos telescopios de aspecto realmente genial en alguna colina. "
"Eso excita al astrónomo aficionado que hay en usted, porque ese debe ser el "
"siguiente paso lógico en el crecimiento de su afición. Pero comprar un buen "
"telescopio puede resultar muy confuso e intimidante."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"El sitio debe ofrecer un análisis detallado de los servicios de ocio dentro "
"del hotel (spa, piscina, gimnasio, sauna), así como detalles de cualquier "
"otra instalación cercana, como campos de golf. 7. Necesidades especiales: el"
" sitio del directorio de hoteles debe informar al visitante sobre los "
"servicios de necesidades especiales y la política de accesibilidad de cada "
"hotel. Si bien, nuevamente, esto no se aplica a todos los visitantes, es "
"absolutamente vital para algunos."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"Las decisiones sobre el trípode u otros accesorios cambiarán "
"significativamente con un telescopio que vivirá en su plataforma en "
"comparación con uno que planifica llevar a muchas ubicaciones remotas."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"La vista de esto es realmente impresionante y es un espectáculo que no debe "
"perderse. También es muy educativo con la oportunidad de ver de cerca un "
"volcán inactivo, algo que no se puede ver todos los días. Sin embargo, en "
"los lados norte y sur del volcán, verá una vista diferente increíble. Estos "
"lados son exuberantes y verdes y podrás ver hermosas cascadas y hermosos "
"arbustos. Las selvas tropicales abundan en este lado de la isla y es algo a "
"lo que no se puede acceder fácilmente por ningún otro medio que no sea por "
"aire."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"Luego está el problema de la motivación del autor. Cuantas más críticas se "
"leen, más se nota que tienden a ser extremistas. Por un lado, están los "
"críticos enfadados con intereses personales y, por otro, los huéspedes "
"encantados que ofrecen elogios inimaginables. No le sorprenderá saber que "
"los hoteles a veces publican sus propias reseñas positivas, o que los "
"competidores se preparan para atacar a la competencia con malas críticas. "
"Por ello, conviene tener en cuenta lo que es realmente importante para usted"
" a la hora de elegir un hotel. Entonces, debería escoger un directorio en "
"línea de hoteles que ofrezca información actualizada, independiente e "
"imparcial que realmente tenga importancia."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr ""
"Hay otras consideraciones a tener en cuenta en su decisión de compra final."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""
"Hay algo atemporal en el cosmos. El hecho de que los planetas, la luna y las"
" estrellas más allá de ellos hayan estado allí durante siglos influye en "
"nuestro sentido de nuestro lugar en el universo. De hecho, muchas de las "
"estrellas que “vemos” a simple vista son en realidad luz que provino de esa "
"estrella hace cientos de miles de años. Esa luz acaba de llegar a la tierra."
" Entonces, de una manera muy real, mirar hacia arriba es como viajar en el "
"tiempo."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""
"Estas cosas realmente importan y cualquier directorio de hoteles decente "
"debería darle este tipo de consejos sobre las habitaciones, ¡no solo sobre "
"el número de habitaciones, que es la opción habitual!"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr "Este cuadro no será visible para sus visitantes."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "This tag already exists"
msgstr "¡Esta etiqueta ya existe!"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Tiny"
msgstr "Pequeño"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "Título"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Above Cover"
msgstr "Título arriba de la portada"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Inside Cover"
msgstr "Título dentro de la portada"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""
"Para contemplar la luna a simple vista, familiarizarse con el mapa lunar le "
"ayudará a identificar los mares, cráteres y otros fenómenos geográficos que "
"otros ya han cartografiado para que su estudio sea más agradable. Los mapas "
"de la luna se pueden obtener en cualquier tienda de astronomía o en línea y "
"bien vale la pena la inversión."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""
"Para comenzar a aprender a observar las estrellas mucho mejor, hay algunas "
"cosas básicas que podríamos necesitar para mirar más profundamente, más allá"
" de lo que podemos ver a simple vista y comenzar a estudiar las estrellas y "
"disfrutarlas. Lo primero que necesita no es equipo, sino literatura. Un buen"
" mapa de estrellas le mostrará las constelaciones principales, la ubicación "
"de las estrellas clave que usamos para navegar por el cielo y los planetas "
"que parecerán más grandes que las estrellas. Y si añade a ese mapa algunos "
"materiales introductorios bien hechos al pasatiempo de la astronomía, está "
"bien encaminado."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""
"Para mejorarlo, un buen par de binoculares puede hacer maravillas con los "
"detalles que verá en la superficie lunar. Para obtener los mejores "
"resultados, obtenga un buen campo amplio en la configuración de los "
"binoculares para que pueda disfrutar del paisaje lunar en toda su belleza. Y"
" debido a que es casi imposible mantener los binoculares quietos durante el "
"tiempo que desee contemplar este magnífico cuerpo en el espacio, es posible "
"que desee añadir a su arsenal de equipos un buen trípode al que pueda "
"colocar los binoculares para poder Estudie la luna con comodidad y con una "
"plataforma de observación estable."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""
"Para llevarlo al siguiente nivel natural, es posible que desee aprovechar "
"las asociaciones con otros astrónomos o visitando uno de los telescopios "
"verdaderamente geniales que han sido establecidos por profesionales que han "
"invertido en mejores técnicas para eliminar la interferencia atmosférica "
"para ver la luna. aun mejor. Internet puede darle acceso al Hubble y a "
"muchos de los enormes telescopios que apuntan a la luna todo el tiempo. "
"Además, muchos clubes de astronomía están trabajando en formas de combinar "
"múltiples telescopios, cuidadosamente sincronizados con ordenadores para "
"obtener la mejor vista del paisaje lunar."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Top Banner"
msgstr "Banner superior"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr "Viajes"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "No publicado ("

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "Publicación sin título"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"Use este icono para previsualizar su publicación en el blog desde "
"<b>dispositivos móviles</b>."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Usada en:"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Viewpoints"
msgstr "Miradores"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "Vistas"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr "Visible en todas las páginas de los blogs"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr "ESCRIBA AQUÍ O ARRASTRE BLOQUES DE CREACIÓN"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Blogs del sitio web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr "Filtro de snippets del sitio web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Descripción meta del sitio web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Palabras clave meta del sitio web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Título meta del sitio web"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Imagen Open Graph del sitio web"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr "¿Y si le dejan dirigir el Hubble?"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""
"Si bien cualquier persona puede mirar hacia arriba y enamorarse de las "
"estrellas en cualquier momento, la diversión de la astronomía es aprender a "
"ser cada vez más hábil y equipado para observar las estrellas que ve y "
"comprende cada vez más cada vez que mira hacia arriba. A continuación se "
"indican algunos pasos que puede seguir para que los momentos que pueda "
"dedicar a su afición por la astronomía sean mucho más agradables."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "With a View"
msgstr "Con una vista"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr "Escriba aquí un pequeño texto para describir su blog o empresa."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""
"Siempre hay que prever el tipo de instalaciones que necesita en su "
"habitación y encontrar el hotel que tenga las que considere más importantes."
" El sitio web del directorio de hoteles debería detallar cuestiones como: el"
" tamaño de la cama, el acceso a Internet (su precio, si hay conexión Wi-Fi o"
" de banda ancha por cable), los servicios de cortesía, las vistas desde la "
"habitación y las ofertas de lujo, el menú de almohadas o el menú de baño, la"
" elección de habitaciones para fumadores o no fumadores, etc."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""
"Verá toda la belleza que Maui tiene para ofrecer y podrá pasar un buen rato "
"para toda la familia. Los recorridos no son demasiado caros y duran desde "
"cuarenta y cinco minutos hasta más de una hora. Puede ver lugares que "
"normalmente son inaccesibles con los recorridos en helicóptero por Maui. Los"
" lugares que no están disponibles a pie o en coche pueden verse por aire. "
"Impresionantes vistas aguardan a aquellos que estén dispuestos a disfrutar "
"de divertidos recorridos en helicóptero por Maui. Si permanecerá en la isla "
"durante un periodo de tiempo considerable, es posible que desee pensar en "
"hacer varios recorridos en helicóptero por Maui."

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr "aventura"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "blog. Haga clic aquí para acceder al blog:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr "barra de migas"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "por"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr "descubrimiento"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr "guías"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "ha sido publicada en el"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr "hoteles"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "en"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr "telescopios"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr "para dejar un comentario"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "no publicado"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"
msgstr ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr "| Sin comentarios aún"
