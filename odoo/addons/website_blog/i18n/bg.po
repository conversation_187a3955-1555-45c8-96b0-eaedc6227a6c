# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_blog
# 
# Translators:
# Ивайло <PERSON>линов <<EMAIL>>, 2024
# <PERSON> <igor.shelud<PERSON>@gmail.com>, 2024
# KeyVillage, 2024
# <PERSON><PERSON><PERSON> <alben<PERSON>_v<PERSON><PERSON>@abv.bg>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "#{year}"
msgstr ""

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
msgid "%s (copy)"
msgstr "%s (копие)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_feed
msgid "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"
msgstr "&lt;?xml version=\"1.0\" encoding=\"utf-8\"?&gt;"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "' page header."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "'. Showing results for '"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.date_selector
msgid "-- All dates"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Binoculars are lightweight and portable.</b> Unless you have the luxury "
"to set up and operate an observatory from your deck, you are probably going "
"to travel to perform your viewings. Binoculars go with you much easier and "
"they are more lightweight to carry to the country and use while you are "
"there than a cumbersome telescope set up kit."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Pick the brains of the experts</b>. If you are not already active in an "
"astronomy society or club, the sales people at the telescope store will be "
"able to guide you to the active societies in your area. Once you have "
"connections with people who have bought telescopes, you can get advice about"
" what works and what to avoid that is more valid than anything you will get "
"from a web article or a salesperson at Wal-Mart."
msgstr ""

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "<b>Publish your blog post</b> to make it visible to your visitors."
msgstr ""
"<b>Публикувайте публикацията си от блога,</b> за да я направите видима за "
"посетителите си."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "<b>Sign in</b>"
msgstr "<b>Регистрирайте се</b>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<b>Try before you buy.</b> This is another advantage of going on some field "
"trips with the astronomy club. You can set aside some quality hours with "
"people who know telescopes and have their rigs set up to examine their "
"equipment, learn the key technical aspects, and try them out before you sink"
" money in your own set up."
msgstr ""

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid ""
"<b>Write your story here.</b> Use the top toolbar to style your text: add an"
" image or table, set bold or italic, etc. Drag and drop building blocks for "
"more graphical blogs."
msgstr ""
"<b>Напишете историята си тук.</b> За да оформите текста си, използвайте "
"горната лента с инструменти: добавете изображение или таблица, настройте "
"болд или курсив и т.н. Влачете и пускайте изграждащите блокове за повече "
"графични блогове."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"<em class=\"h4 my-0\">Apart from the native population, the local wildlife "
"is also a major crowd puller.</em>"
msgstr ""
"<em class=\"h4 my-0\">Освен местното население, дивата природа в района също"
" е голямо привличане за посетителите.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"<em class=\"h4 my-0\">It is critically important that you get just the right"
" telescope.</em>"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"<em class=\"h4 my-0\">That “Wow” moment is what astrology is all about.</em>"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"<em class=\"h4 my-0\">The more reviews you read, the more you notice how "
"they tend to cluster at the extremes of opinion.</em>"
msgstr ""
"<em class=\"h4 my-0\">Колкото повече ревюта четете, толкова по-ясно "
"забелязвате, че те обикновено се групират в двата крайни полюса на "
"мненията.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "<em class=\"h4 my-0\">There is something timeless about the cosmos.</em>"
msgstr "<em class=\"h4 my-0\">Има нещо вечно в космоса.</em>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"<em class=\"h4 my-0\">Your study of the moon, like anything else, can go "
"from the simple to the very complex.</em>"
msgstr ""
"<em class=\"h4 my-0\">Вашето изследване на луната, както всяко друго нещо, "
"може да премине от простото към много сложното.</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "<em>No tags defined</em>"
msgstr "<em>Не са дефинирани тагове</em>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"
msgstr ""
"<i class=\"fa fa-angle-down fa-3x text-white\" aria-label=\"To blog "
"content\" title=\"To blog content\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"
msgstr ""
"<i class=\"fa fa-clock-o me-1\" role=\"img\" aria-label=\"Post date\" "
"title=\"Post date\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"
msgstr "<i class=\"fa fa-github text-github\" aria-label=\"Github\" title=\"Github\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"
msgstr ""
"<i class=\"fa fa-instagram text-instagram\" aria-label=\"Instagram\" "
"title=\"Instagram\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"
msgstr ""
"<i class=\"fa fa-linkedin text-linkedin\" aria-label=\"LinkedIn\" "
"title=\"LinkedIn\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"
msgstr "<i class=\"fa fa-rss-square\" aria-label=\"RSS\" title=\"RSS\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"
msgstr "<i class=\"fa fa-tiktok text-tiktok\" aria-label=\"TikTok\" title=\"TikTok\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"
msgstr "<i class=\"fa fa-twitter text-twitter\" aria-label=\"X\" title=\"X\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"
msgstr ""
"<i class=\"fa fa-youtube-play text-youtube\" aria-label=\"Youtube\" "
"title=\"Youtube\"/>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Read "
"Next</span>"
msgstr ""
"<span class=\"bg-o-color-3 h6 d-inline-block py-1 px-2 rounded-1\">Прочетете"
" напред</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
msgid ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"
msgstr ""
"<span class=\"h4 d-inline-block py-1 px-2 rounded-1 text-white\">\n"
"                                    <i class=\"fa fa-angle-right fa-3x text-white\" aria-label=\"Read next\" title=\"Read Next\"/>\n"
"                                </span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "<span class=\"me-1\">Show:</span>"
msgstr "<span class=\"me-1\">Показване:</span>"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "<span class=\"nav-link disabled ps-0\">Blogs:</span>"
msgstr "<span class=\"nav-link disabled ps-0\">Блогове:</span>"

#. module: website_blog
#: model_terms:web_tour.tour,rainbow_man_message:website_blog.blog
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Добра работа!</b> Вие преминахте през всички стъпки от тази "
"обиколка.</span>"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_2
msgid "A great way to discover hidden places"
msgstr "Страхотен начин да откриете скрити места"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A holiday to the Copper Canyon promises to be an exciting mix of relaxation,"
" culture, history, wildlife and hiking."
msgstr ""
"Ваканцията в Медния каньон обещава вълнуваща комбинация от релаксация, "
"култура, история, дива природа и пешеходен туризъм."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "A new post"
msgstr "Нова публикация"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"A traveler may choose to explore the area by hiking around the canyon or "
"venturing into it. Detailed planning is required for those who wish to "
"venture into the depths of the canyon. There are a number of travel "
"companies that specialize in organizing tours to the region. Visitors can "
"fly to Copper Canyon using a tourist visa, which is valid for 180 days. "
"Travelers can also drive from anywhere in the United States and acquire a "
"visa at the Mexican customs station at the border."
msgstr ""
"Пътешествениците могат да изберат да изследват района, като се разходят "
"около каньона или се впуснат в приключение в неговите дълбини. Подробното "
"планиране е необходимо за тези, които искат да навлязат в недрата на "
"каньона. Има множество туристически компании, които се специализират в "
"организирането на обиколки до региона. Посетителите могат да летят до Медния"
" каньон с туристическа виза, която е валидна за 180 дни. Пътуващите също "
"могат да шофират от която и да е точка в Съединените щати и да получат виза "
"на мексиканския митнически пункт на границата."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "About us"
msgstr "За нас"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Above all, <b>establish a relationship with a reputable telescope shop</b> "
"that employs people who know their stuff. If you buy your telescope at a "
"Wal-Mart or department store, the odds you will get the right thing are "
"remote."
msgstr ""
"Най-важното е, <b>да установите връзка с реномиран магазин за телескопи</b>,"
" който разполага с персонал, запознат с тази техника. Ако купите телескоп от"
" Wal-Mart или универсален магазин, вероятността да получите подходящото "
"устройство е много малка."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "Access post"
msgstr "Публикация за достъп"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction
msgid "Action Needed"
msgstr "Необходимо действие"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__active
#: model:ir.model.fields,field_description:website_blog.field_blog_post__active
msgid "Active"
msgstr "Активно"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Add some"
msgstr "Добавете малко"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
msgid "All"
msgstr "Всичко"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "All Blogs"
msgstr "Всички блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "All blogs"
msgstr "Всички блогове"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Alone in the ocean"
msgstr "Сам в океана"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Along those lines, how difficult is the set up and break down?"
msgstr "По тези линии, колко трудно е настройката и повредата?"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/website_blog.js:0
msgid "Amazing blog article: %(title)s! Check it live: %(url)s"
msgstr "Невероятна блог статия: %(title)s! Проверете на живо: %(url)s"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_1
msgid "An exciting mix of relaxation, culture, history, wildlife and hiking."
msgstr ""
"Вълнуваща комбинация от релакс, култура, история, дива природа и туризъм."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"And when all is said and done,<b> get equipped</b>. Your quest for newer and"
" better telescopes will be a lifelong one. Let yourself get addicted to "
"astronomy and the experience will enrich every aspect of life. It will be an"
" addiction you never want to break."
msgstr ""
"И когато всичко е казано и направено, <b> екипирайте се</b>. Вашето търсене "
"на все по-нови и по-добри телескопи ще бъде стремеж за цял живот. Позволете "
"си да се пристрастите към астрономията, защото това преживяване ще обогати "
"всеки аспект от живота ви. Това ще бъде пристрастяване, от което никога няма"
" да искате да се откажете."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Another unique feature of Copper Canyon is the presence of the Tarahumara "
"Indian culture. These semi-nomadic people live in cave dwellings. Their "
"livelihood chiefly depends on farming and cattle ranching."
msgstr ""
"Друга уникална черта на Медния каньон е присъствието на културата на "
"индианците Тараумара. Тези полу-номадски хора живеят в пещерни жилища. "
"Основният им поминък е свързан със земеделие и животновъдство."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_archive_display
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archive"
msgstr "Архив"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Archived"
msgstr "Архивиран"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_archives
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Archives"
msgstr "Архиви"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Article"
msgstr "Статия"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Articles"
msgstr "Статии"

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_2
msgid "Astronomy"
msgstr "Астрономия"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Astronomy clubs are lively places full of knowledgeable amateurs who love to"
" share their knowledge with you. For the price of a coke and snacks, they "
"will go star gazing with you and overwhelm you with trivia and great "
"knowledge."
msgstr ""
"Астрономическите клубове са оживени места, пълни с опитни любители, които с "
"удоволствие ще споделят знанията си с вас. Срещу цената на една кола и малко"
" закуски те ще излязат с вас да наблюдават звездите и ще ви залеят с "
"любопитни факти и ценна информация."

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_2
msgid "Astronomy is “stargazing\""
msgstr "Астрономията е “наблюдаване на звездите“"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Atom Feed"
msgstr "Atom Feed"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_attachment_count
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_attachment_count
msgid "Attachment Count"
msgstr "Брой прикачени файлове"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_id
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Author"
msgstr "Автор"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_name
msgid "Author Name"
msgstr "Име на автора"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__author_avatar
msgid "Avatar"
msgstr "Аватар"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Awesome hotel rooms"
msgstr "Страхотни хотелски стаи"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_4
msgid "Be aware of this thing called “astronomy”"
msgstr "Бъдете наясно с това, което наричаме “астрономия”"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Becoming part of the society of devoted amateur astronomers will give you "
"access to these organized efforts to reach new levels in our ability to "
"study the Earth’s moon. And it will give you peers and friends who share "
"your passion for astronomy and who can share their experience and areas of "
"expertise as you seek to find where you might look next in the huge night "
"sky, at the moon and beyond it in your quest for knowledge about the "
"seemingly endless universe above us."
msgstr ""
"Присъединяването към обществото на отдадени любители астрономи ще ви даде "
"достъп до организирани усилия за постигане на нови нива в изучаването на "
"Луната. Освен това ще намерите съмишленици и приятели, които споделят вашата"
" страст към астрономията и могат да споделят своя опит и експертиза, докато "
"се стремите да откриете следващите области за наблюдение в огромното нощно "
"небе – към Луната и отвъд нея – в търсенето на знания за привидно "
"безкрайната вселена над нас."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_7
msgid "Becoming part of the society of devoted amateur astronomers."
msgstr "Ставане част от обществото на отдадените любители астрономи."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Bedroom Facilities"
msgstr "Удобства в спалнята"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"Before you go to that big expense, it might be a better next step from the "
"naked eye to invest in a good set of binoculars. There are even binoculars "
"that are suited for star gazing that will do just as good a job at giving "
"you that extra vision you want to see just a little better the wonders of "
"the universe. A well designed set of binoculars also gives you much more "
"mobility and ability to keep your “enhanced vision” at your fingertips when "
"that amazing view just presents itself to you."
msgstr ""
"Преди да направите голяма инвестиция, може би е по-добре следващата стъпка "
"след наблюдението с просто око да бъде закупуването на добър чифт бинокли. "
"Има дори бинокли, специално създадени за наблюдение на звездите, които ще ви"
" осигурят същото допълнително зрение, което искате, за да видите чудесата на"
" вселената малко по-ясно. Добре проектираният чифт бинокли ви предоставя и "
"много повече мобилност, като ви позволява да държите “подобреното зрение“ "
"винаги под ръка, когато някоя невероятна гледка внезапно се разкрие пред "
"вас."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_6
msgid "Before you make your first purchase…"
msgstr "Преди да направите първата си покупка..."

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_7
msgid "Beyond The Eye"
msgstr "Отвъд окото"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website.py:0
#: model:ir.model,name:website_blog.model_blog_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__blog_id
#: model:ir.ui.menu,name:website_blog.menu_website_blog_root_global
#: model:website.menu,name:website_blog.menu_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_blog_view_search
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog"
msgstr "Блог"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__name
msgid "Blog Name"
msgstr "Име на блог"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blog Page"
msgstr "Страница в блога"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_post
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Blog Post"
msgstr "Блогова публикация"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
msgid "Blog Post Cover"
msgstr "Корица на публикация в блог"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_post
msgid "Blog Post Pages"
msgstr "Страници с публикации в блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Post Title"
msgstr "Заглавие на блогова публикация"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_post_pages
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blog Posts"
msgstr "Блогови публикации"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__subtitle
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Blog Subtitle"
msgstr "Подзаглавие на блог"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag
msgid "Blog Tag"
msgstr "Маркер на блог"

#. module: website_blog
#: model:ir.model,name:website_blog.model_blog_tag_category
msgid "Blog Tag Category"
msgstr "Категория на етикета на блога"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tags
msgid "Blog Tags"
msgstr "Маркери на блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Blog Title"
msgstr "Заглавие на блог"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
msgid "Blog's Title"
msgstr "Заглавие на блог"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_blog_blog
#: model:ir.ui.menu,name:website_blog.menu_blog_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_blog_list
#: model_terms:ir.ui.view,arch_db:website_blog.website_blog
msgid "Blogs"
msgstr "Блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs List"
msgstr "Списък блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Blogs Page"
msgstr "Страница блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Bottom"
msgstr "Дъно"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Breadcrumb"
msgstr "Навигационна следа"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"But how do you sift through the amazing choices on offer? And more "
"importantly, do you really trust the photographs and descriptions of the "
"hotels that they have awarded themselves with the motivation of getting "
"bookings? Traveler reviews can be helpful, but you need to exercise caution."
" They are often biased, sometimes out of date, and may not serve your "
"interests at all. How do you know that the features that are important to "
"the reviewer are important to you?"
msgstr ""
"Но как да се ориентирате сред изумителния избор от оферти? И по-важното – "
"наистина ли можете да се доверите на снимките и описанията на хотелите, "
"които те сами са си направили с цел да привлекат повече резервации? Ревютата"
" на пътешественици могат да бъдат полезни, но е необходимо да подхождате с "
"внимание. Те често са пристрастни, понякога остарели и може изобщо да не "
"отразяват вашите нужди. Как можете да сте сигурни, че характеристиките, "
"които са важни за рецензента, са също толкова важни за вас?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_6
msgid "Buying A Telescope"
msgstr "Закупуване на телескоп"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"Buying the right telescope to take your love of astronomy to the next level "
"is a big next step in the development of your passion for the stars."
msgstr ""
"Закупуването на правилния телескоп, за да издигнете любовта си към "
"астрономията на следващото ниво, е важна стъпка в развитието на вашата "
"страст към звездите."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__can_publish
msgid "Can Publish"
msgstr "Може да публикува"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cards"
msgstr "Карти"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__category_id
msgid "Category"
msgstr "Категория"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Children’s’ Facilities"
msgstr "Детски съоръжения"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Choose an image from the library."
msgstr "Изберете изображение от библиотеката."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Click here to add new content to your website."
msgstr "Щракнете тук, за да добавите ново съдържание към уебсайта си."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid ""
"Click on \"<b>New</b>\" in the top-right corner to write your first blog "
"post."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blogs_nav
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Close"
msgstr "Затвори"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__color
msgid "Color"
msgstr "Цвят"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Comment"
msgstr "Коментар"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments"
msgstr "Коментари"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Comments/Views Stats"
msgstr "Статистика за коментари/гледания"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__content
#: model:ir.model.fields,field_description:website_blog.field_blog_post__content
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Content"
msgstr "Съдържание"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Copper Canyon is one of the six gorges in the area. Although the name "
"suggests that the gorge might have some relevance to copper mining, this is "
"not the case. The name is derived from the copper and green lichen covering "
"the canyon. Copper Canyon has two climatic zones. The region features an "
"alpine climate at the top and a subtropical climate at the lower levels. "
"Winters are cold with frequent snowstorms at the higher altitudes. Summers "
"are dry and hot. The capital city, Chihuahua, is a high altitude desert "
"where weather ranges from cold winters to hot summers. The region is unique "
"because of the various ecosystems that exist within it."
msgstr ""
"Медният каньон е един от шестте каньона в региона. Въпреки че името "
"предполага връзка с добива на мед, това не е така. Името произлиза от медно-"
"зелените лишеи, които покриват каньона. Медният каньон има две климатични "
"зони. Районът се характеризира с алпийски климат на върха и субтропичен "
"климат в по-ниските части. Зимите са студени с чести снежни бури по високите"
" надморски височини. Летата са сухи и горещи. Столицата на щата, Чиуауа, се "
"намира в пустинен район с висока надморска височина, където времето варира "
"от студени зими до горещи лета. Регионът е уникален заради различните "
"екосистеми, които съществуват в него."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Cover"
msgstr "Корица"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__cover_properties
#: model:ir.model.fields,field_description:website_blog.field_blog_post__cover_properties
msgid "Cover Properties"
msgstr "Свойства на корицата"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_uid
msgid "Created by"
msgstr "Създаден от"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__create_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Date"
msgstr "Дата"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "Дата (нова към стара)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "Дата (стара към нова)"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Description"
msgstr "Описание"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Dexter"
msgstr "Декстър"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__display_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "East Maui"
msgstr "Източен Мауи"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"East Maui helicopter tours will give you a view of the ten thousand foot "
"volcano, Haleakala or House of the sun. This volcano is dormant and last "
"erupted in 1790. You will be able to see the crater of the volcano and the "
"dry, arid earth surrounding the south side of the volcano’s slop with Maui "
"helicopter tours."
msgstr ""
"Хеликоптерните обиколки на Източен Мауи ще ви предоставят гледка към "
"десетхилядния вулкан Халеакала, известен още като „Домът на слънцето“. Този "
"вулкан е спящ и последното му изригване е било през 1790 година. С "
"хеликоптерните обиколки на Мауи ще имате възможност да видите кратера на "
"вулкана и сухата, пустинна земя, която заобикаля южния склон на вулкана."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
msgid "Edit in backend"
msgstr "Редактирайте в бекенда"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the '"
msgstr "Редактирайте '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'All Blogs' page header."
msgstr "Редактирайте заглавката на страницата 'Всички блогове'."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "Edit the 'Filter Results' page header."
msgstr "Редактирайте заглавката на страницата 'Филтриране на резултати'."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Edit your title, the subtitle is optional."
msgstr "Редактирайте заглавието си, подзаглавието не е задължително."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Enter your post's title"
msgstr "Въведете заглавието на вашата публикация"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
msgid "Facebook"
msgstr "Facebook"

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_3
msgid "Facts you should bear in mind."
msgstr "Факти, които трябва да имате предвид."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Finally and most importantly, the quality hotel directory inspection team "
"should have visited the hotel in question on a regular basis, met the staff,"
" slept in a bedroom and tried the food. They should experience the hotel as "
"only a hotel guest can and it is only then that they are really in a strong "
"position to write about the hotel."
msgstr ""
"И накрая, най-важното е, че екипът за инспекция на качествената хотелска "
"директория трябва редовно да посещава съответния хотел, да се среща с "
"персонала, да нощува в стая и да опитва храната. Те трябва да преживеят "
"хотела така, както би го направил обикновен гост, и едва тогава ще бъдат в "
"състояние да напишат обективен и информиран доклад за хотела."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_follow_us
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Follow Us"
msgstr "Последвайте ни"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_follower_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_follower_ids
msgid "Followers"
msgstr "Последователи"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_partner_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Последователи (партньори)"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"For many of us who are city dwellers, we don’t really notice that sky up "
"there on a routine basis. The lights of the city do a good job of disguising"
" the amazing display that is above all of our heads all of the time."
msgstr ""
"За мнозина от нас, които живеят в градовете, не обръщаме особено внимание на"
" небето над нас в ежедневието си. Градските светлини успешно прикриват "
"удивителния спектакъл, който винаги се намира над главите ни."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"For many of us, our very first experience of learning about the celestial "
"bodies begins when we saw our first full moon in the sky. It is truly a "
"magnificent view even to the naked eye."
msgstr ""
"За мнозина от нас първият опит с изучаването на небесните тела започва, "
"когато за пръв път сме видели пълната Луна в нощното небе. Това е наистина "
"величествена гледка, дори с просто око."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"From the tiniest baby to the most advanced astrophysicist, there is "
"something for anyone who wants to enjoy astronomy. In fact, it is a science "
"that is so accessible that virtually anybody can do it virtually anywhere "
"they are. All they have to know how to do is to look up."
msgstr ""
"От най-малкото бебе до най-напредналия астрофизик, астрономията предлага "
"нещо за всеки, който иска да й се наслади. Всъщност, това е наука, която е "
"толкова достъпна, че практически всеки може да се занимава с нея, независимо"
" къде се намира. Единственото, което трябва да знае, е как да погледне "
"нагоре."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Full-Width"
msgstr "Пълна височина"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get a geek"
msgstr "Вземете маниак"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get a telescope"
msgstr "Вземете телескоп"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Get some history"
msgstr "Вземете малко история"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Get started"
msgstr "Първи стъпки"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Grid"
msgstr "Матрица"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Group By"
msgstr "Групиране по"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__has_message
#: model:ir.model.fields,field_description:website_blog.field_blog_post__has_message
msgid "Has Message"
msgstr "има съобщение"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Here are some of the key facts you should bear in mind:"
msgstr "Ето някои от основните факти, които трябва да имате предвид:"

#. module: website_blog
#: model:blog.blog,subtitle:website_blog.blog_blog_1
msgid "Holiday tips"
msgstr "Ваканционни съвети"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Hover Effect"
msgstr "Ефект на задържане"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_4
msgid "How To Look Up"
msgstr "Как да се гледа нагоре"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"How complex is the telescope and will you have trouble with maintenance? "
"Network to get the answers to these and other questions. If you do your "
"homework like this, you will find just the right telescope for this next big"
" step in the evolution of your passion for astronomy."
msgstr ""
"Колко сложен е телескопът и ще имате ли трудности с поддръжката му? "
"Потърсете мрежи за споделяне на опит, за да получите отговори на тези и "
"други въпроси. Ако направите своето проучване по този начин, ще намерите "
"точния телескоп за тази следваща голяма стъпка в развитието на вашата страст"
" към астрономията."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "How mobile must your telescope be?"
msgstr "Колко мобилен трябва да бъде вашият телескоп?"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_3
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "How to choose the right hotel"
msgstr "Как да изберем правилния хотел"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__id
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__id
msgid "ID"
msgstr "ID"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ако е отметнато, новите съобщения ще изискват внимание."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ако е отметнато, някои съобщения имат грешка при доставката."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"If it matters that your hotel is, for example, on the beach, close to the "
"theme park, or convenient for the airport, then location is paramount. Any "
"decent directory should offer a location map of the hotel and its "
"surroundings. There should be distance charts to the airport offered as well"
" as some form of interactive map."
msgstr ""
"Ако е важно хотелът ви да бъде, например, на плажа, близо до увеселителния "
"парк или удобен за летището, тогава местоположението е от първостепенно "
"значение. Всяка добра директория трябва да предоставя карта с "
"местоположението на хотела и околностите му. Трябва също да има таблица с "
"разстояния до летището, както и някаква форма на интерактивна карта."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"If the night is clear, you can see amazing detail of the lunar surface just star gazing on in your back yard.\n"
"Naturally, as you grow in your love of astronomy, you will find many celestial bodies fascinating. But the moon may always be our first love because is the one far away space object that has the unique distinction of flying close to the earth and upon which man has walked."
msgstr ""
"Ако нощта е ясна, можете да видите удивителни детайли от лунната повърхност, просто наблюдавайки звездите от собствения си двор. \n"
"Естествено, с развитието на любовта ви към астрономията ще откриете, че много небесни тела са пленителни. Но Луната може би винаги ще остане първата ни любов, защото е единственият обект в далечния космос, който лети толкова близо до Земята и върху който човек е стъпвал."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
msgid "In"
msgstr "В"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"In many ways, it is a big step from someone who is just fooling around with "
"astronomy to a serious student of the science. But you and I both know that "
"there is still another big step after buying a telescope before you really "
"know how to use it."
msgstr ""
"В много отношения това е голяма стъпка – от човек, който просто се забавлява"
" с астрономията, до сериозен ученик в тази наука. Но и вие, и аз знаем, че "
"има още една значима стъпка след закупуването на телескоп, преди наистина да"
" разберете как да го използвате."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Increase Readability"
msgstr "Увеличете четливостта"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_is_follower
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_is_follower
msgid "Is Follower"
msgstr "е последовател"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_published
msgid "Is Published"
msgstr "Е публикувано"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Islands"
msgstr "Острови"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It is great fun to start learning the constellations, how to navigate the "
"night sky and find the planets and the famous stars. There are web sites and"
" books galore to guide you."
msgstr ""
"Изключително забавно е да започнете да изучавате съзвездията, да се "
"ориентирате в нощното небе и да откривате планетите и известните звезди. Има"
" изобилие от уебсайтове и книги, които могат да ви помогнат в това "
"начинание."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"It is important to choose a hotel that makes you feel comfortable – "
"contemporary or traditional furnishings, local decor or international, "
"formal or relaxed. The ideal hotel directory should let you know of the "
"options available."
msgstr ""
"Важно е да изберете хотел, който ви кара да се чувствате комфортно – със "
"съвременно или традиционно обзавеждане, с местен декор или международен "
"стил, с официална или по-непринудена обстановка. Идеалната хотелска "
"директория трябва да ви информира за наличните опции."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"It is safe to say that at some point on our lives, each and every one of us "
"has that moment when we are suddenly stunned when we come face to face with "
"the enormity of the universe that we see in the night sky."
msgstr ""
"Със сигурност можем да кажем, че в даден момент от живота си всеки от нас "
"преживява онзи миг, когато внезапно е зашеметен, изправен лице в лице с "
"необятността на вселената, която се разкрива в нощното небе."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"It really is amazing when you think about it that just by looking up on any "
"given night, you could see virtually hundreds of thousands of stars, star "
"systems, planets, moons, asteroids, comets and maybe a even an occasional "
"space shuttle might wander by. It is even more breathtaking when you realize"
" that the sky you are looking up at is for all intents and purposes the "
"exact same sky that our ancestors hundreds and thousands of years ago "
"enjoyed when they just looked up."
msgstr ""
"Наистина е удивително, когато се замислите, че само като погледнете нагоре в"
" някоя обикновена нощ, можете да видите буквално стотици хиляди звезди, "
"звездни системи, планети, луни, астероиди, комети, а понякога дори и "
"преминаваща космическа совалка. Още по-зашеметяващо е, когато осъзнаете, че "
"небето, което гледате, е на практика същото небе, което нашите предци преди "
"стотици и хиляди години са съзерцавали, когато просто са вдигали поглед "
"нагоре."

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Jungle"
msgstr "Джунгла"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know what you are looking at"
msgstr "Знайте какво гледате"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Know when to look"
msgstr "Знайте кога да търсите"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Large"
msgstr "Голям"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_uid
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_search
msgid "Last Contributor"
msgstr "Последен сътрудник"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_uid
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_post__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__write_date
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_latest_blog_posts
msgid "Latest Blog Posts"
msgstr "Последни публикации в блогове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Layout"
msgstr "Редайтирай изгледа"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Learning the background to the great discoveries in astronomy will make your"
" moments star gazing more meaningful. It is one of the oldest sciences on "
"earth so find out the greats of history who have looked at these stars "
"before you."
msgstr ""
"Да научите историята зад великите открития в астрономията ще направи "
"моментите ви на наблюдение на звездите много по-смислени. Астрономията е "
"една от най-древните науки на Земята, затова си струва да се запознаете с "
"великите умове от историята, които са гледали тези звезди преди вас."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Leisure Facilities"
msgstr "Съоръжения за отдих"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "LinkedIn"
msgstr "LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "List"
msgstr "Списък"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Local color is great but the hotel’s own restaurants and bars can play an "
"important part in your stay. You should be aware of choice, style and "
"whether or not they are smart or informal. A good hotel report should tell "
"you this, and particularly about breakfast facilities."
msgstr ""
"Местният колорит е чудесен, но ресторантите и баровете на самия хотел могат "
"да играят важна роля за вашия престой. Трябва да знаете какви са "
"възможностите, стилът и дали обстановката е елегантна или неформална. Един "
"добър хотелски доклад трябва да ви информира за това, особено за условията "
"за закуска."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Location"
msgstr "Локация"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Marley"
msgstr "Марли"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_2
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "Maui helicopter tours"
msgstr "Обиколки с хеликоптер на Мауи"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to see the island from a different "
"perspective and have a fun adventure. If you have never been on a helicopter"
" before, this is a great place to do it."
msgstr ""
"Хеликоптерните обиколки на Мауи са чудесен начин да видите острова от "
"различна перспектива и да изживеете вълнуващо приключение. Ако никога досега"
" не сте летели с хеликоптер, това е идеалното място да го направите."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours are a great way to tour those places that can not be "
"reached on foot or by car. The tours last approximately one hour and range "
"from approximately one hundred eight five dollars to two hundred forty "
"dollars person. For many, this is a once in a lifetime opportunity to see "
"natural scenery that will not be available again. Taking cameras and videos "
"to capture the moments will also allow you to relive the tour again and "
"again as you reminisce throughout the years."
msgstr ""
"Хеликоптерните обиколки на Мауи са чудесен начин да посетите места, които не"
" могат да бъдат достигнати пеша или с кола. Обиколките продължават "
"приблизително един час и струват между 185 и 240 долара на човек. За мнозина"
" това е възможност веднъж в живота да видят природни пейзажи, които няма да "
"могат да се повторят. Вземането на камера или видеокамера, за да заснемете "
"тези моменти, ще ви позволи да преживявате обиколката отново и отново, "
"връщайки се към спомените през годините."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Maui helicopter tours will allow you to see all of these sights. Make sure "
"to take a camera or video with you when going on Maui helicopter tours to "
"capture the beauty of the scenery and to show friends and family at home all"
" the wonderful things you saw while on vacation."
msgstr ""
"Хеликоптерните обиколки на Мауи ще ви позволят да видите всички тези "
"забележителности. Не забравяйте да вземете със себе си камера или "
"видеокамера, когато отивате на обиколка, за да заснемете красотата на "
"пейзажа и да покажете на приятели и семейство у дома всички прекрасни неща, "
"които сте видели по време на ваканцията си."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Medium"
msgstr "Преносител"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error
msgid "Message Delivery error"
msgstr "Грешка при доставяне на съобщението"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_ids
msgid "Messages"
msgstr "Syob]eniq"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Description"
msgstr "Мета описание"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Keywords"
msgstr "Мета ключови думи"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Meta Title"
msgstr "Мета заглавие"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Molokai Maui"
msgstr "Молокай Мауи"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"Molokai Maui helicopter tours will take you to a different island but one that is only nine miles away and easily accessible by air. This island has a very small population with a different culture and scenery. The entire coast of the northeast is lined with cliffs and remote beaches. They are completely inaccessible by any other means of transportation than air.\n"
"People who live on the island have never even seen this remarkable scenery unless they have taken Maui helicopter tours to view it. When the weather has been rainy and there is a lot of rainfall for he season you will see many astounding waterfalls."
msgstr ""
"Хеликоптерните обиколки от Мауи до Молокай ще ви отведат на друг остров, който е само на девет мили разстояние и лесно достъпен по въздух. Този остров има много малко население, различна култура и уникални пейзажи. Цялото североизточно крайбрежие е осеяно със скали и отдалечени плажове, които са напълно недостъпни с никакъв друг вид транспорт, освен по въздух. \n"
"Дори хората, които живеят на острова, често никога не са виждали тази забележителна природа, освен ако не са се качили на хеликоптерна обиколка от Мауи. Когато времето е било дъждовно и сезонът е богат на валежи, ще видите множество удивителни водопади."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"More important to the family traveler than the business traveler, you should"
" find out just how child friendly the hotel is from the directory and make "
"your decision from there. One thing worth looking for is whether the hotel "
"offers a baby sitters service. For the business traveler wishing to escape "
"children this is of course very relevant too – perhaps a hotel that is not "
"child friendly would be something more appropriate!"
msgstr ""
"По-важно за семейния пътешественик, отколкото за бизнес пътешественика, е да"
" разберете доколко хотелът е подходящ за деца, като проверите информацията в"
" директорията, и да направите избора си въз основа на това. Една от нещата, "
"които си заслужава да се проверят, е дали хотелът предлага услуги за гледане"
" на деца. За бизнес пътешествениците, които искат да избегнат присъствието "
"на деца, това също е много важно – може би хотел, който не е насочен към "
"семейства с деца, би бил по-подходящ!"

#. module: website_blog
#: model:website.snippet.filter,name:website_blog.dynamic_filter_most_viewed_blog_posts
msgid "Most Viewed Blog Posts"
msgstr "Най-гледани блог постове"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__name
msgid "Name"
msgstr "Име"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.blog_post_action_add
msgid "New Blog Post"
msgstr "Нова блогова публикация"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Next Article"
msgstr "Следваща статия"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "No Cover"
msgstr "Без покритие"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No blog post yet."
msgstr "Няма блог статии"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__visits
msgid "No of Views"
msgstr "Брой изгледи"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "No results for \"%s\"."
msgstr "Няма резултати за \"%s\"."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_short
msgid "No results found for '"
msgstr "Няма намерени резултати за '"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "No tags defined yet."
msgstr "Все още няма дефинирани етикети."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "None"
msgstr "Никакъв"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"None of this precludes you from moving forward with your plans to put "
"together an awesome telescope system. Just be sure you get quality advice "
"and training on how to configure your telescope to meet your needs. Using "
"these guidelines, you will enjoy hours of enjoyment stargazing at the "
"phenomenal sights in the night sky that are beyond the naked eye."
msgstr ""
"Нищо от това не ви пречи да продължите с плановете си да сглобите невероятна"
" система за телескоп. Просто се уверете, че получавате качествени съвети и "
"обучение как да конфигурирате телескопа си, за да отговаря на вашите нужди. "
"Следвайки тези насоки, ще се наслаждавате на часове удоволствие, "
"наблюдавайки феноменалните гледки на нощното небе, които са недостъпни за "
"невъоръженото око."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Normal"
msgstr "Нормален"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Normal picture"
msgstr "Нормално изображение"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Not Published"
msgstr "Непубликуван"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"Not only knowing the weather will make sure your star gazing is rewarding "
"but if you learn when the big meteor showers and other big astronomy events "
"will happen will make the excitement of astronomy come alive for you."
msgstr ""
"Не само че познаването на времето ще гарантира успешно наблюдение на "
"звездите, но ако научите кога ще се случат големите метеорни дъждове и други"
" важни астрономически събития, вълнението от астрономията ще оживее за вас."

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Брой действия"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_error_counter
msgid "Number of errors"
msgstr "Брой грешки"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_needaction_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Брой съобщения изискващи действие"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__message_has_error_counter
#: model:ir.model.fields,help:website_blog.field_blog_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Брой съобщения с грешка при доставка"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"Of course, to take your moon worship to the ultimate, stepping your "
"equipment up to a good starter telescope will give you the most stunning "
"detail of the lunar surface. With each of these upgrades your knowledge and "
"the depth and scope of what you will be able to see will improve "
"geometrically. For many amateur astronomers, we sometimes cannot get enough "
"of what we can see on this our closest space object."
msgstr ""
"Разбира се, ако искате да издигнете вашето „поклонение пред Луната“ на "
"следващото ниво, преминаването към добър начален телескоп ще ви разкрие "
"зашеметяващи детайли от лунната повърхност. С всяко от тези надграждания "
"вашите знания и способността да виждате по-дълбоко и по-широко ще се "
"увеличават геометрично. За много любители астрономи това усещане никога не е"
" достатъчно – не можем да се наситим на гледката на този наш най-близък "
"космически обект."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid ""
"Once you have reviewed the content on mobile, you can switch back to the "
"normal view by clicking here again"
msgstr ""
"След като прегледате съдържанието на мобилно устройство, можете да се "
"върнете към нормалния изглед, като щракнете отново тук"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
msgid "Others"
msgstr "Други"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post_fullwidth_design
msgid "Our Latest Posts"
msgstr "Наши последни постове"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_blogs_display
msgid "Our blogs"
msgstr "Наши блогове"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Anton Repponen, @repponen"
msgstr "Снимка от Антон Репонен, @repponen"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid "Photo by Arto Marttinen, @wandervisions"
msgstr "Снимка от Arto Marttinen, @wandervisions"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by Boris Smokrovic, @borisworkshop"
msgstr "Снимка от Борис Смокрович, @borisworkshop"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Denys Nevozhai, @dnevozhai"
msgstr "Снимка от Денис Невожай, @dnevozhai"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Greg Rakozy, @grakozy"
msgstr "Снимка от Грег Ракози, @grakozy"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Photo by Jason Briscoe, @jbriscoe"
msgstr "Снимка от Джейсън Бриско, @jbriscoe"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid "Photo by Jon Ly, @jonatron"
msgstr "Снимка от Джон Ли, @jonatron"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Photo by Patrick Brinksma, @patrickbrinksma"
msgstr "Снимка от Патрик Бринксма, @patrickbrinksma"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid "Photo by PoloX Hernandez, @elpolox"
msgstr "Снимка от PoloX Hernandez, @elpolox"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid "Photo by SpaceX, @spacex"
msgstr "Снимка от SpaceX, @spacex"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid "Photo by Teddy Kelley, @teddykelley"
msgstr "Снимка от Теди Кели, @teddykelley"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Picture size"
msgstr "Размер на изображение"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__blog_post_count
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__post_ids
msgid "Posts"
msgstr "Публикации"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Posts List"
msgstr "Списък с публикации"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "Дата ба публикацията"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_kanban
msgid "Published"
msgstr "Публикуван"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Published ("
msgstr "Публикуван ("

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__published_date
msgid "Published Date"
msgstr "Дата на публикуване"

#. module: website_blog
#: model:mail.message.subtype,description:website_blog.mt_blog_blog_published
#: model:mail.message.subtype,name:website_blog.mt_blog_blog_published
msgid "Published Post"
msgstr "Публикувана статия"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Publishing Options"
msgstr "Опции за публикуване"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__post_date
msgid "Publishing date"
msgstr "Дата на публикуване"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__rating_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__rating_ids
msgid "Ratings"
msgstr "Оценявания"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Read more <i class=\"oi oi-chevron-right ms-2\"/>"
msgstr "Прочитане повече <i class=\"oi oi-chevron-right ms-2\"/>"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Restaurants, Cafes and Bars"
msgstr "Ресторанти, кафенета и барове"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,help:website_blog.field_blog_post__website_id
msgid "Restrict to a specific website."
msgstr "Ограничете до конкретен уебсайт."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "SEO"
msgstr "СЕО"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO оптимизиран"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__message_has_sms_error
#: model:ir.model.fields,field_description:website_blog.field_blog_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS грешка при доставка"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_big_picture
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_card
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_horizontal
#: model_terms:ir.ui.view,arch_db:website_blog.dynamic_filter_template_blog_post_list
msgid "Sample"
msgstr "Мостра"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Satellites"
msgstr "Сателити"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Search for an image. (eg: type \"business\")"
msgstr "Търсене на изображение. (напр.: въведете \"бизнес\")"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Seaside vs mountain side"
msgstr "Морски бряг срещу планинска страна"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Seeing the world from above"
msgstr "Гледайки света отгоре"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_view_form_add
msgid "Select Blog"
msgstr "Избиране на блог"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Select the blog you want to add the post to."
msgstr "Изберете блога, към който искате да добавите публикацията."

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Select this menu item to create a new blog post."
msgstr ""
"Изберете този елемент от менюто, за да създадете нова блогова публикация."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Comment"
msgstr "Избиране за коментар"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Select to Tweet"
msgstr "Изберете за Tweet"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_post__seo_name
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__seo_name
msgid "Seo name"
msgstr "СЕО име"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.view_blog_post_form
msgid "Separate every keyword with a comma"
msgstr "Разделете всяка ключова дума със запетая"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Set a blog post <b>cover</b>."
msgstr "Настройте <b>корица</b>на блогова публикация."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Several migratory and native birds, mammals and reptiles call Copper Canyon "
"their home. The exquisite fauna in this near-pristine land is also worth "
"checking out."
msgstr ""
"Множество мигриращи и местни птици, бозайници и влечуги наричат Медния "
"каньон свой дом. Изключителната фауна на този почти девствен район също "
"заслужава внимание."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Share Links"
msgstr "Споделяне на връзки"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on Facebook"
msgstr "Споделяне във Facebook"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on LinkedIn"
msgstr "Споделяне в LinkedIn"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share on X"
msgstr "Споделяне в X"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Share this post"
msgstr "Споделяне на статус"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Sidebar"
msgstr "Странична лента"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_1
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_blog_posts_preview_data
msgid "Sierra Tarahumara"
msgstr "Сиера Тараумара"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
msgid ""
"Sierra Tarahumara, popularly known as Copper Canyon is situated in Mexico. "
"The area is a favorite destination among those seeking an adventurous "
"vacation."
msgstr ""
"Сиера Тараумара, популярно известна като Медния каньон, се намира в Мексико."
" Районът е любима дестинация за тези, които търсят приключенска ваканция."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Silly-Chico"
msgstr "Глупавият Чико "

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Skies"
msgstr "Небеса"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Smaller"
msgstr "По-малка"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Smaller picture"
msgstr "По-малка снимка"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So it is critically important that you get just the right telescope for "
"where you are and what your star gazing preferences are. To start with, "
"let’s discuss the three major kinds of telescopes and then lay down some "
"“Telescope 101″ concepts to increase your chances that you will buy the "
"right thing."
msgstr ""
"Ето защо е изключително важно да изберете правилния телескоп, който да "
"отговаря както на мястото, където се намирате, така и на вашите "
"предпочитания за наблюдение на звездите. За начало, нека разгледаме трите "
"основни типа телескопи и след това да обясним някои основни “Телескоп 101″ "
"концепции, които ще увеличат шансовете ви да направите правилния избор."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"So it might be that once a year vacation to a camping spot or a trip to a "
"relative’s house out in the country that we find ourselves outside when the "
"spender of the night sky suddenly decides to put on it’s spectacular show. "
"If you have had that kind of moment when you were literally struck "
"breathless by the spender the night sky can show to us, you can probably "
"remember that exact moment when you could say little else but “wow” at what "
"you saw."
msgstr ""
"Може би веднъж годишно, по време на ваканция на къмпинг или при посещение на"
" роднини извън града, се оказваме навън, когато величието на нощното небе "
"внезапно решава да изнесе своя грандиозен спектакъл. Ако сте имали такъв "
"момент, когато буквално ви е секнал дъхът от красотата, която нощното небе "
"може да ни разкрие, вероятно помните точния момент, в който не сте могли да "
"кажете нищо друго освен “Уау” на това, което сте видели."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"So to select just the right kind of telescope, your objectives in using the "
"telescope are important. To really understand the strengths and weaknesses "
"not only of the lenses and telescope design but also in how the telescope "
"performs in various star gazing situations, it is best to do some homework "
"up front and get exposure to the different kinds. So before you make your "
"first purchase…"
msgstr ""
"За да изберете правилния тип телескоп, е важно да определите своите цели при"
" използването му. За да разберете истински силните и слабите страни не само "
"на лещите и дизайна на телескопа, но и на това как се представя в различни "
"ситуации за наблюдение на звездите, е най-добре предварително да направите "
"проучване и да се запознаете с различните видове телескопи. Така че, преди "
"да направите първата си покупка..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"So you’re going abroad, you’ve chosen your destination and now you have to "
"choose a hotel."
msgstr ""
"И така, отивате в чужбина, избрали сте дестинацията си и сега трябва да "
"изберете хотел."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_1
#: model_terms:blog.post,content:website_blog.blog_post_3
#: model_terms:blog.post,content:website_blog.blog_post_4
#: model_terms:blog.post,content:website_blog.blog_post_5
#: model_terms:blog.post,content:website_blog.blog_post_6
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "Someone famous in <cite title=\"Source Title\">Source Title</cite>"
msgstr ""
"Някой известен в <cite title=\"Source Title\">Заглавие на източника</cite>"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Spotting the fauna"
msgstr "Наблюдение на фауната"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_blog.py:0
msgid "Start writing here..."
msgstr "Започнете да пишете тук..."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid "Style"
msgstr "Стил"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__subtitle
msgid "Sub Title"
msgstr "Подзаглавие"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_complete
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_cover_post
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_read_next
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Subtitle"
msgstr "Подзаглавие"

#. module: website_blog
#: model:ir.ui.menu,name:website_blog.menu_website_blog_tag_category_global
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_tree
msgid "Tag Categories"
msgstr "Таг категории"

#. module: website_blog
#: model:ir.actions.act_window,name:website_blog.action_tag_category
msgid "Tag Category"
msgstr "Таг категория"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_category_form
msgid "Tag Category Form"
msgstr "Форма за категория на таг"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Tag Form"
msgstr "Форма за маркери"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_tree
msgid "Tag List"
msgstr "Списък с маркери"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_category_name_uniq
msgid "Tag category already exists!"
msgstr "Таг категорията вече съществува!"

#. module: website_blog
#: model:ir.model.constraint,message:website_blog.constraint_blog_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Тагът вече съществува!"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__tag_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_tag_category__tag_ids
#: model:ir.ui.menu,name:website_blog.menu_blog_tag_global
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_tags_display
#: model_terms:ir.ui.view,arch_db:website_blog.opt_sidebar_blog_index_tags
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags"
msgstr "Маркери"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Tags List"
msgstr "Списък с тагове"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Taking pictures in the dark"
msgstr "Снимане в тъмното"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser
#: model_terms:ir.ui.view,arch_db:website_blog.s_dynamic_snippet_options_template
msgid "Teaser"
msgstr "Тийзър"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Teaser & Tags"
msgstr "Тийзър и тагове"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__teaser_manual
msgid "Teaser Content"
msgstr "Съдържание на тийзър"

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Ten years ago, you’d have probably visited your local travel agent and "
"trusted the face-to-face advice you were given by the so called ‘experts’. "
"The 21st Century way to select and book your hotel is of course on the "
"Internet, by using travel websites."
msgstr ""
"Преди десет години вероятно щяхте да посетите местния си туристически агент "
"и да се доверите на съветите, които ви дават така наречените 'експерти'. В "
"21-ви век обаче начинът за избор и резервиране на хотел, разбира се, е чрез "
"интернет, използвайки туристически уебсайтове."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"That “Wow” moment is what astrology is all about. For some, that wow moment "
"becomes a passion that leads to a career studying the stars. For a lucky "
"few, that wow moment because an all consuming obsession that leads to them "
"traveling to the stars in the space shuttle or on one of our early space "
"missions. But for most of us astrology may become a pastime or a regular "
"hobby. But we carry that wow moment with us for the rest of our lives and "
"begin looking for ways to look deeper and learn more about the spectacular "
"universe we see in the millions of stars above us each night."
msgstr ""
"Този “Уау” момент е това, което прави астрономията толкова вълнуваща. За "
"някои хора този момент на удивление се превръща в страст, която ги води към "
"кариера в изучаването на звездите. За малцина, този момент става "
"всепоглъщащо увлечение, което ги отвежда до пътувания в Космоса с "
"космическата совалка или участие в ранни космически мисии. Но за повечето от"
" нас астрономията може да се превърне в приятно занимание или редовно хоби. "
"Все пак ние запазваме този “Уау” момент за цял живот и започваме да търсим "
"начини да надникнем по-дълбоко и да научим повече за невероятната вселена, "
"която виждаме в милионите звезди над нас всяка вечер."

#. module: website_blog
#: model:blog.post,subtitle:website_blog.blog_post_5
msgid "The beauty of astronomy is that anybody can do it."
msgstr ""
"Красотата на астрономията е в това, че всеки може да се занимава с нея."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"The best time to view the moon, obviously, is at night when there are few "
"clouds and the weather is accommodating for a long and lasting study. The "
"first quarter yields the greatest detail of study. And don’t be fooled but "
"the blotting out of part of the moon when it is not in full moon stage. The "
"phenomenon known as “earthshine” gives you the ability to see the darkened "
"part of the moon with some detail as well, even if the moon is only at "
"quarter or half display."
msgstr ""
"Най-доброто време за наблюдение на Луната, очевидно, е през нощта, когато "
"има малко облаци и времето позволява дълго и детайлно изследване. Първата "
"четвърт предоставя най-големи детайли за наблюдение. Не се подвеждайте от "
"закриването на част от Луната, когато тя не е в пълнолуние. Явлението, "
"известно като “земно сияние”, ви дава възможност да видите затъмнената част "
"на Луната с известни детайли, дори когато тя е само на четвърт или половина "
"видима."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid "The best time to view the moon."
msgstr "Най-доброто време за наблюдение на Луната."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__post_date
msgid ""
"The blog post will be visible for your visitors as of this date on the "
"website if it is set as published."
msgstr ""
"Публикацията в блога ще бъде видима за Вашите посетители от тази дата на "
"уебсайта, ако е зададена като публикувана."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The cliffs in this region are among the highest in the world and to see "
"water cascading from the high peaks is simply breathtaking. The short jaunt "
"from Maui with Maui helicopter tours is well worth seeing the beauty of this"
" natural environment."
msgstr ""
"Скалите в този регион са сред най-високите в света, а да видите как водата "
"се спуска от тези високи върхове е просто спиращо дъха преживяване. Краткото"
" пътуване от Мауи с хеликоптерните обиколки на Мауи определено си заслужава,"
" за да се насладите на красотата на тази природна среда."

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_post__website_url
msgid "The full URL to access the document through the website."
msgstr "Пълен URL адрес за достъп до документа през уебсайта."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"The next thing we naturally want to get is a good telescope. You may have "
"seen a hobbyist who is well along in their study setting up those really "
"cool looking telescopes on a hill somewhere. That excites the amateur "
"astronomer in you because that must be the logical next step in the growth "
"of your hobby. But how to buy a good telescope can be downright confusing "
"and intimidating."
msgstr ""
"Следващото, което естествено бихме искали, е добър телескоп. Може би сте "
"виждали любител, напреднал в изучаването на астрономията, да настройва онези"
" наистина впечатляващи телескопи на някой хълм. Това събужда любителя "
"астроном във вас, защото изглежда като логичната следваща стъпка в "
"развитието на вашето хоби. Но изборът на добър телескоп може да бъде доста "
"объркващ и плашещ."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"The site should offer a detailed analysis of leisure services within the "
"hotel – spa, pool, gym, sauna – as well as details of any other facilities "
"nearby such as golf courses. 7. Special Needs: the hotel directory site "
"should advise the visitor of each hotel’s special needs services and "
"accessibility policy. Whilst again this does not apply to every visitor, it "
"is absolutely vital to some."
msgstr ""
"Сайтът трябва да предлага подробен анализ на услугите за отдих в хотела – "
"спа, басейн, фитнес, сауна – както и информация за други съоръжения в "
"близост, като например голф игрища. 7. Специални нужди: сайтът за хотелски "
"директории трябва да информира посетителите за услугите за хора със "
"специални нужди и политиката на достъпност на всеки хотел. Макар това да не "
"се отнася за всички посетители, то е абсолютно жизненоважно за някои."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"The tripod or other accessory decisions will change significantly with a "
"telescope that will live on your deck versus one that you plan to take to "
"many remote locations."
msgstr ""
"Изборът на статив или други аксесоари ще се промени значително в зависимост "
"от това дали телескопът ще остане постоянно на вашата тераса, или планирате "
"да го носите на различни отдалечени места."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"The view of this is truly breathtaking and is a sight not to be missed. It "
"is also highly educational with a chance to see a dormant volcano up close, "
"something that can not be seen every day. On the northern and southern sides"
" of the volcano, you will see an incredible different view however. These "
"sides are lush and green and you will be able to see some beautiful "
"waterfalls and gorgeous brush. Tropical rainforests abound on this side of "
"the island and it is something that is not easily accessible by any other "
"means than by air."
msgstr ""
"Гледката тук е наистина спираща дъха и е нещо, което не бива да се пропуска."
" Освен това е изключително образователно преживяване, тъй като имате "
"възможност да видите отблизо спящ вулкан – нещо, което не се вижда всеки "
"ден. На северната и южната страна на вулкана обаче ще видите съвсем различна"
" гледка. Тези страни са буйни и зелени, с прекрасни водопади и красива "
"растителност. От тази страна на острова преобладават тропически дъждовни "
"гори, до които не е лесно да се стигне по друг начин освен по въздух."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"Then there’s the problem of the reviewer’s motivation. The more reviews you "
"read, the more you notice how they tend to cluster at the extremes of "
"opinion. On one end, you have angry reviewers with axes to grind; at the "
"other, you have delighted guests who lavish praise beyond belief. You’ll not"
" be surprised to learn that hotels sometimes post their own glowing reviews,"
" or that competitor’s line up for the chance to lambaste the competition "
"with bad reviews. It makes sense to consider what is really important to you"
" when selecting a hotel. You should then choose an online hotel directory "
"that gives up-to-date, independent, impartial information that really "
"matters."
msgstr ""
"И тук възниква проблемът с мотивацията на рецензентите. Колкото повече "
"ревюта четете, толкова по-ясно забелязвате, че те обикновено се групират в "
"двата крайни полюса на мненията. От едната страна са ядосаните рецензенти с "
"лични сметки за уреждане, а от другата – възторжени гости, които сипят "
"хвалби отвъд всякаква реалност. Няма да се изненадате да научите, че "
"понякога хотелите сами публикуват блестящи ревюта за себе си или че "
"конкуренцията се реди на опашка, за да злепостави съперниците си с лоши "
"оценки. Затова има смисъл да се замислите кое наистина е важно за вас, "
"когато избирате хотел. След това изберете онлайн директория за хотели, която"
" предоставя актуална, независима и обективна информация, която наистина има "
"значение."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_6
msgid ""
"There are other considerations to factor into your final purchase decision."
msgstr ""
"Има и други съображения, които трябва да вземете предвид при окончателното "
"ви решение за покупка."

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"There is something timeless about the cosmos. The fact that the planets and "
"the moon and the stars beyond them have been there for ages does something "
"to our sense of our place in the universe. In fact, many of the stars we "
"“see” with our naked eye are actually light that came from that star "
"hundreds of thousands of years ago. That light is just now reaching the "
"earth. So in a very real way, looking up is like time travel."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"These things really do matter and any decent hotel directory should give you"
" this sort of advice on bedrooms – not just the number of rooms which is the"
" usual option!"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "This box will not be visible to your visitors"
msgstr ""

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "This tag already exists"
msgstr ""

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/options.js:0
msgid "Tiny"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__name
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "Title"
msgstr "Заглавие"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Above Cover"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Title Inside Cover"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To gaze at the moon with the naked eye, making yourself familiar with the "
"lunar map will help you pick out the seas, craters and other geographic "
"phenomenon that others have already mapped to make your study more "
"enjoyable. Moon maps can be had from any astronomy shop or online and they "
"are well worth the investment."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_4
msgid ""
"To get started in learning how to observe the stars much better, there are "
"some basic things we might need to look deeper, beyond just what we can see "
"with the naked eye and begin to study the stars as well as enjoy them. The "
"first thing you need isn’t equipment at all but literature. A good star map "
"will show you the major constellations, the location of the key stars we use"
" to navigate the sky and the planets that will appear larger than stars. And"
" if you add to that map some well done introductory materials into the hobby"
" of astronomy, you are well on your way."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To kick it up a notch, a good pair of binoculars can do wonders for the "
"detail you will see on the lunar surface. For best results, get a good wide "
"field in the binocular settings so you can take in the lunar landscape in "
"all its beauty. And because it is almost impossible to hold the binoculars "
"still for the length of time you will want to gaze at this magnificent body "
"in space, you may want to add to your equipment arsenal a good tripod that "
"you can affix the binoculars to so you can study the moon in comfort and "
"with a stable viewing platform."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_7
msgid ""
"To take it to a natural next level, you may want to take advantage of "
"partnerships with other astronomers or by visiting one of the truly great "
"telescopes that have been set up by professionals who have invested in "
"better techniques for eliminating atmospheric interference to see the moon "
"even better. The internet can give you access to the Hubble and many of the "
"huge telescopes that are pointed at the moon all the time. Further, many "
"astronomy clubs are working on ways to combine multiple telescopes, "
"carefully synchronized with computers for the best view of the lunar "
"landscape."
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.snippet_options
msgid "Top Banner"
msgstr ""

#. module: website_blog
#: model:blog.blog,name:website_blog.blog_blog_1
msgid "Travel"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_share_links_display
msgid "Twitter"
msgstr "Twitter"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "Unpublished ("
msgstr "Непубликуван ("

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "Untitled Post"
msgstr "Неозаглавена публикация"

#. module: website_blog
#. odoo-javascript
#: code:addons/website_blog/static/src/js/tours/website_blog.js:0
msgid "Use this icon to preview your blog post on <b>mobile devices</b>."
msgstr ""
"Използвайте тази икона, за да визуализирате блоговата си публикация в "
"<b>мобилно устройство</b>."

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_tag_form
msgid "Used in:"
msgstr "Използвано във:"

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "Viewpoints"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_info
msgid "Views"
msgstr "Гледания"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.index
msgid "Visible in all blogs' pages"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_published
msgid "Visible on current website"
msgstr "Видимо на текущия уебсайт"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "WRITE HERE OR DRAG BUILDING BLOCKS"
msgstr ""

#. module: website_blog
#: model:ir.model,name:website_blog.model_website
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_id
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_id
msgid "Website"
msgstr "Уебсайт"

#. module: website_blog
#: model:ir.actions.act_url,name:website_blog.action_open_website
msgid "Website Blogs"
msgstr "Блогове на уебсайт"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_message_ids
msgid "Website Messages"
msgstr "Съобщения в уебсайт"

#. module: website_blog
#: model:ir.model,name:website_blog.model_website_snippet_filter
msgid "Website Snippet Filter"
msgstr ""

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_url
msgid "Website URL"
msgstr "URL адрес на уебсайт"

#. module: website_blog
#: model:ir.model.fields,help:website_blog.field_blog_blog__website_message_ids
#: model:ir.model.fields,help:website_blog.field_blog_post__website_message_ids
msgid "Website communication history"
msgstr "История на комуникацията на уебсайт"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_description
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_description
msgid "Website meta description"
msgstr "Мета описание на уебсайт"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "Мета ключови думи на уебсайт"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_title
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_title
msgid "Website meta title"
msgstr "Мета заглавие на уебсайт"

#. module: website_blog
#: model:ir.model.fields,field_description:website_blog.field_blog_blog__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_blog.field_blog_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "Уебсайт opengraph изображение"

#. module: website_blog
#: model:blog.post,name:website_blog.blog_post_5
msgid "What If They Let You Run The Hubble"
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_5
msgid ""
"While anyone can look up and fall in love with the stars at any time, the "
"fun of astronomy is learning how to become more and more skilled and "
"equipped in star gazing that you see and understand more and more each time "
"you look up. Here are some steps you can take to make the moments you can "
"devote to your hobby of astronomy much more enjoyable."
msgstr ""

#. module: website_blog
#. odoo-python
#: code:addons/website_blog/models/website_snippet_filter.py:0
msgid "With a View"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.sidebar_blog_index
msgid "Write a small text here to describe your blog or company."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_3
msgid ""
"You should always carefully consider the type of facilities you need from "
"your bedroom and find the hotel that has those you consider important. The "
"hotel directory website should elaborate on matters such as: bed size, "
"Internet Access (its cost, whether there is WIFI or wired broadband "
"connection), Complimentary amenities, views from the room and luxury "
"offerings like a Pillow menu or Bath menu, choice of smoking or non smoking "
"rooms etc."
msgstr ""

#. module: website_blog
#: model_terms:blog.post,content:website_blog.blog_post_2
msgid ""
"You will see all the beauty that Maui has to offer and can have a great time"
" for the entire family. Tours are not too expensive and last from forty five"
" minutes to over an hour. You can see places that are typically inaccessible"
" with Maui helicopter tours. Places that are not available by foot or "
"vehicle can be seen by air. Breathtaking sights await those who are up for "
"some fun Maui helicopter tours. If you will be staying on the island for a "
"considerable amount of time, you may want to think about doing multiple Maui"
" helicopter tours."
msgstr ""

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_2
msgid "adventure"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "blog. Click here to access the blog :"
msgstr "блог. Натиснете тук, за да получите достъп до блога:"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.post_breadcrumbs
msgid "breadcrumb"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
#: model_terms:ir.ui.view,arch_db:website_blog.post_heading
msgid "by"
msgstr "от"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_5
msgid "discovery"
msgstr ""

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_3
msgid "guides"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_template_new_post
msgid "has been published on the"
msgstr "е публикуван в"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_1
msgid "hotels"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.blog_post_content
msgid "in"
msgstr "в"

#. module: website_blog
#: model:blog.tag,name:website_blog.blog_tag_4
msgid "telescopes"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_comment
msgid "to leave a comment"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.posts_loop
msgid "unpublished"
msgstr "непубликувано"

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid ""
"|\n"
"                            <i class=\"fa fa-comment text-muted me-1\"/>"
msgstr ""

#. module: website_blog
#: model_terms:ir.ui.view,arch_db:website_blog.opt_blog_post_regular_cover
msgid "| No comments yet"
msgstr ""
