# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_hr_account
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析資料行"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_report__unit_employee_cost
msgid "Average Employee Cost / Unit"
msgstr "每單位平均員工成本"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_account_analytic_line__employee_id
msgid ""
"Define an 'hourly cost' on the employee to track the cost of their time."
msgstr "為員工定義「每小時成本」以追蹤他們的時間成本。"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_account_analytic_line__employee_id
msgid "Employee"
msgstr "員工"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_workorder__employee_analytic_account_line_ids
msgid "Employee Analytic Account Line"
msgstr "員工分析帳戶資料行"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_mrp_report__unit_employee_cost
msgid ""
"Employee Cost per unit produced (in product UoM) of manufacturing order"
msgstr "製造訂單每生產單位（以產品計量單位計算）的員工成本"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr "MRP成本結構報告"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_report
msgid "Manufacturing Report"
msgstr "製造報告"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_report__employee_cost
msgid "Total Employee Cost"
msgstr "總員工成本"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_mrp_report__employee_cost
msgid "Total cost of employees for manufacturing order"
msgstr "製造訂單總員工成本"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "工作中心使用情況"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_workorder
msgid "Work Order"
msgstr "工作單"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "工作中心產能日誌"

#. module: mrp_workorder_hr_account
#. odoo-python
#: code:addons/mrp_workorder_hr_account/models/mrp_workorder.py:0
msgid "[EMPL] %(work_order)s - %(employee)s"
msgstr "[EMPL] %(work_order)s - %(employee)s"
