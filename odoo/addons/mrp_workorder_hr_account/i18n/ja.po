# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_hr_account
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "分析行"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_report__unit_employee_cost
msgid "Average Employee Cost / Unit"
msgstr "平均従業員原価/単位"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_account_analytic_line__employee_id
msgid ""
"Define an 'hourly cost' on the employee to track the cost of their time."
msgstr "従業員らの'時間原価'を設定し、 彼らの時間の原価を追跡します。"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_account_analytic_line__employee_id
msgid "Employee"
msgstr "従業員"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_workorder__employee_analytic_account_line_ids
msgid "Employee Analytic Account Line"
msgstr "従業員分析勘定明細"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_mrp_report__unit_employee_cost
msgid ""
"Employee Cost per unit produced (in product UoM) of manufacturing order"
msgstr "製造オーダの生産された単位 (プロダクト単位)ごとの従業員原価 "

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr "MRP費用構成レポート"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_report
msgid "Manufacturing Report"
msgstr "製造レポート"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_report__employee_cost
msgid "Total Employee Cost"
msgstr "従業員原価合計"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_mrp_report__employee_cost
msgid "Total cost of employees for manufacturing order"
msgstr "製造オーダ用従業員原価合計"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "作業区使用状況"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_workorder
msgid "Work Order"
msgstr "作業オーダ"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "作業区 生産記録"

#. module: mrp_workorder_hr_account
#. odoo-python
#: code:addons/mrp_workorder_hr_account/models/mrp_workorder.py:0
msgid "[EMPL] %(work_order)s - %(employee)s"
msgstr "[EMPL] %(work_order)s - %(employee)s"
