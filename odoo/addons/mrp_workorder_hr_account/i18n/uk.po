# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_hr_account
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Рядок аналітики"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_report__unit_employee_cost
msgid "Average Employee Cost / Unit"
msgstr "Середня вартість співробітника / Одиницю"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_account_analytic_line__employee_id
msgid ""
"Define an 'hourly cost' on the employee to track the cost of their time."
msgstr ""
"Визначте «погодинну вартість» для працівника, щоб відстежувати вартість його"
" часу."

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_account_analytic_line__employee_id
msgid "Employee"
msgstr "Співробітник"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_workorder__employee_analytic_account_line_ids
msgid "Employee Analytic Account Line"
msgstr "Бухгалтерський рядок аналітики співробітника"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_mrp_report__unit_employee_cost
msgid ""
"Employee Cost per unit produced (in product UoM) of manufacturing order"
msgstr ""
"Вартість працівника на одиницю виробленої продукції (в од. вим. товару) "
"замовлення на виробництво"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr "Звіт структури вартості MRP"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_report
msgid "Manufacturing Report"
msgstr "Звіт виробництва"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_report__employee_cost
msgid "Total Employee Cost"
msgstr "Загальна вартість співробітника"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_mrp_report__employee_cost
msgid "Total cost of employees for manufacturing order"
msgstr "Загальна вартість співробітників для замовлення на виробництво"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Використання робочого центру"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_workorder
msgid "Work Order"
msgstr "Робоче замовлення"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Журнал продуктивності робочого центру"

#. module: mrp_workorder_hr_account
#. odoo-python
#: code:addons/mrp_workorder_hr_account/models/mrp_workorder.py:0
msgid "[EMPL] %(work_order)s - %(employee)s"
msgstr ""
