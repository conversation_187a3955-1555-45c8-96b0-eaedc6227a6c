# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_hr_account
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analytische boeking"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_report__unit_employee_cost
msgid "Average Employee Cost / Unit"
msgstr "Gemiddelde personeelskosten / eenheid"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_account_analytic_line__employee_id
msgid ""
"Define an 'hourly cost' on the employee to track the cost of their time."
msgstr ""
"Bepaal een 'uurtarief' op de werknemer om de kost van hun tijd bij te "
"houden."

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_account_analytic_line__employee_id
msgid "Employee"
msgstr "Werknemer"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_workorder__employee_analytic_account_line_ids
msgid "Employee Analytic Account Line"
msgstr "Werknemer regel analytische rekening"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_mrp_report__unit_employee_cost
msgid ""
"Employee Cost per unit produced (in product UoM) of manufacturing order"
msgstr ""
"Personeelskosten per geproduceerde eenheid (in maateenheid van het product) "
"van een productieorder."

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr "MRP kostenstructuur rapport"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_report
msgid "Manufacturing Report"
msgstr "Productierapport"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,field_description:mrp_workorder_hr_account.field_mrp_report__employee_cost
msgid "Total Employee Cost"
msgstr "Totale personeelskosten"

#. module: mrp_workorder_hr_account
#: model:ir.model.fields,help:mrp_workorder_hr_account.field_mrp_report__employee_cost
msgid "Total cost of employees for manufacturing order"
msgstr "Totale personeelskosten voor productieorder"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Gebruik werkplek"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_workorder
msgid "Work Order"
msgstr "Werkorder"

#. module: mrp_workorder_hr_account
#: model:ir.model,name:mrp_workorder_hr_account.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "Werkplek effectiviteitslog"

#. module: mrp_workorder_hr_account
#. odoo-python
#: code:addons/mrp_workorder_hr_account/models/mrp_workorder.py:0
msgid "[EMPL] %(work_order)s - %(employee)s"
msgstr "[EMPL] %(work_order)s - %(employee)s"
