<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="partner_demo_company_iq" model="res.partner">
            <field name="name">IQ Company</field>
            <field name="country_id" ref="base.iq" />
            <field name="street">Falastin St. Al Anbari 123</field>
            <field name="city">Baghdad</field>
            <field name="vat">*********</field>
            <field name="phone">+964 1 234 5678</field>
            <field name="email"><EMAIL></field>
            <field name="website">www.iq-example.com</field>
            <field name="is_company" eval="True"/>
        </record>

        <record id="demo_company_iq" model="res.company">
            <field name="name">IQ Company</field>
            <field name="partner_id" ref="partner_demo_company_iq" />
        </record>

        <function model="res.company" name="_onchange_country_id">
            <value eval="[ref('demo_company_iq')]" />
        </function>

        <function model="res.users" name="write">
            <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]" />
            <value eval="{'company_ids': [(4, ref('l10n_iq.demo_company_iq'))]}" />
        </function>

        <function model="account.chart.template" name="try_loading">
            <value eval="[]" />
            <value>iq</value>
            <value model="res.company" eval="obj().env.ref('l10n_iq.demo_company_iq')" />
            <value name="install_demo" eval="True"/>
        </function>
    </data>
</odoo>
