# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_meet
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<i class=\"fa fa-spin fa-circle-o-notch me-3\"/>\n"
"                        <span>Loading your room...</span>"
msgstr ""
"<i class=\"fa fa-spin fa-circle-o-notch me-3\"/>\n"
"                        <span>Je ruimte wordt geladen...</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "<span class=\"badge text-bg-danger\">Unpublished</span>"
msgstr "<span class=\"badge text-bg-danger\">Niet gepubliceerd</span>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"<span>Oops! This room is full!</span><br/>Come back later to have a chat "
"with us!"
msgstr ""
"<span>Oeps! Deze ruimte is vol!</span><br/>Kom later terug om met ons te "
"praten!"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"<span>This room is not open right now!</span><br/>\n"
"                        Join us here on the"
msgstr ""
"<span>Deze ruimte is momenteel niet open!</span><br/>\n"
"                        Doe mee hier op de"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "A chat among"
msgstr "Een chat tussen"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__active
msgid "Active"
msgstr "Actief"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "All Languages"
msgstr "Alle talen"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meet_main
msgid "All Rooms"
msgstr "Alle Ruimtes"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,field_description:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Allow Room Creation"
msgstr "Ruimte maken toestaan"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
msgid "Are you sure you want to close this room?"
msgstr "Weet je zeker dat je deze ruimte wilt sluiten?"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/js/website_event_meeting_room.js:0
msgid "Are you sure you want to duplicate this room?"
msgstr "Weet je zeker dat je deze ruimte wilt dupliceren?"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__target_audience
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Audience"
msgstr "Publiek"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meet_main
msgid "Back to all Rooms"
msgstr "Terug naar alle ruimtes"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
msgid ""
"Be sure you are ready to spend at least 10 minutes in the room if you want "
"to initiate a new topic."
msgstr ""
"Zorg ervoor dat je klaar bent om ten minste 10 minuten in de ruimte door te "
"brengen als je een nieuw onderwerp wilt beginnen."

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_0
msgid "Best wood for furniture"
msgstr "Beste hout voor meubels"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__can_publish
msgid "Can Publish"
msgstr "Kan publiceren"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
msgid "Capacity"
msgstr "Capaciteit"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__chat_room_id
msgid "Chat Room"
msgstr "Chat room"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid ""
"Choose a topic that interests you and start talking with the community. "
"<br/> Don't forget to setup your camera and microphone."
msgstr ""
"Kies een onderwerp dat je interesseert en begin te praten met de community. "
"<br/> Vergeet niet je camera en microfoon in te stellen."

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Close"
msgstr "Afsluiten"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
msgid "Create"
msgstr "Aanmaken"

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Create a Room"
msgstr "Maak een ruimte"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Create one to get conversations going"
msgstr "Maak er een om gesprekken op gang te brengen"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meet
msgid ""
"DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL PROPOSAL PAGES "
"OF ALL EVENTS"
msgstr ""
"ZET HIER BOUWBLOKKEN NEER OM ZE VOOR ALLE VOORSTELPAGINA'S VAN EVENEMENTEN "
"BESCHIKBAAR TE MAKEN "

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
msgid "Discard"
msgstr "Negeren"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Dropdown menu"
msgstr "Dropdown menu"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Duplicate"
msgstr "Dupliceren"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_event
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__event_id
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Event"
msgstr "Evenement"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_meeting_room
msgid "Event Meeting Room"
msgstr "Evenementruimte"

#. module: website_event_meet
#: model:ir.model.fields.selection,name:website_event_meet.selection__website_event_menu__menu_type__meeting_room
msgid "Event Meeting Room Menus"
msgstr "Menu's voor evenementenruimtes"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.snippet_options
msgid "Event Page"
msgstr "Evenementpagina"

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.action_meeting_room_from_event
msgid "Event Rooms"
msgstr "Evenementenruimtes"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_event_type
msgid "Event Template"
msgstr "Evenementsjabloon"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "Full"
msgstr "Vol"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Group By"
msgstr "Groeperen op"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__id
msgid "ID"
msgstr "ID"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_pinned
msgid "Is Pinned"
msgstr "Is vastgezet"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__is_published
msgid "Is Published"
msgstr "Is gepubliceerd"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Join a room"
msgstr "Deelnemen aan een ruimte"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us next time to chat about"
msgstr "Doe de volgende keer mee om over te praten"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "Join us there to chat about"
msgstr "Sluit je daar bij ons aan om over te praten"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_lang_id
msgid "Language"
msgstr "Taal"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "Languages Menu"
msgstr "Talenmenu"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_last_activity
msgid "Last activity"
msgstr "Laatste Activiteit"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
msgid "Launch a new topic"
msgstr "Lanceer een nieuw onderwerp"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_event__meeting_room_allow_creation
#: model:ir.model.fields,help:website_event_meet.field_event_type__meeting_room_allow_creation
msgid "Let Visitors Create Rooms"
msgstr "Laat bezoekers ruimtes maken"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_0
msgid "Let's talk about wood types for furniture"
msgstr "Laten we het hebben over houtsoorten voor meubels"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_capacity
msgid "Max capacity"
msgstr "maximum capaciteit"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr "Maximaal aantal deelnemers tegelijk in de ruimte bereikt"

#. module: website_event_meet
#: model:ir.actions.act_window,name:website_event_meet.event_meeting_room_action
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Meeting Room"
msgstr "Vergaderzaal"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_ids
msgid "Meeting rooms"
msgstr "Vergaderruimtes"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Soort menu"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_main
msgid "No Room Open"
msgstr "Geen ruimte open"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_aside
msgid "Other Rooms"
msgstr "Andere ruimtes"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_participant_count
msgid "Participant count"
msgstr "Aantal deelnemers"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_max_participant_reached
msgid "Peak participants"
msgstr "Piekdeelnemers"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_1
msgid "Reducing the ecological footprint with wood?"
msgstr "De ecologische voetafdruk verkleinen met hout?"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
msgid "Reporting"
msgstr "Rapportages"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.snippet_options
msgid "Room Creation (Specific)"
msgstr "Ruimte maken (specifiek)"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_is_full
msgid "Room Is Full"
msgstr "Ruimte is vol"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__room_name
msgid "Room Name"
msgstr "Ruimtenaam"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
msgid "Room Topic"
msgstr "Ruimteonderwerp"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_event__meeting_room_count
msgid "Room count"
msgstr "Aantal ruimtes"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Room creation will be available when event starts at"
msgstr ""
"Het maken van een ruimte is beschikbaar wanneer het evenement begint om"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_event_view_form
msgid "Rooms"
msgstr "Ruimtes"

#. module: website_event_meet
#: model_terms:ir.actions.act_window,help:website_event_meet.action_meeting_room_from_event
#: model_terms:ir.actions.act_window,help:website_event_meet.event_meeting_room_action
msgid ""
"Rooms allow your event attendees to meet up and chat on different topics."
msgstr ""
"Met ruimtes kunnen bezoekers van je evenement elkaar ontmoeten en chatten "
"over verschillende onderwerpen."

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_1
msgid "Share your tips to reduce your ecological footprint using wood."
msgstr "Deel je tips om je ecologische voetafdruk te verkleinen met hout."

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
msgid "Short Summary"
msgstr "Korte samenvatting"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Start a topic"
msgstr "Een onderwerp starten"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__summary
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Summary"
msgstr "Samenvatting"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
msgid "Target People"
msgstr "Mensen targeten"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "The event"
msgstr "Het evenement"

#. module: website_event_meet
#. odoo-python
#: code:addons/website_event_meet/controllers/website_event_main.py:0
msgid ""
"The event %(name)s starts on %(date_begin)s (%(timezone)s).\n"
"Join us there to chat about \"%(subject)s\"!"
msgstr ""
"Het evenement %(name)s begint op %(date_begin)s (%(timezone)s).\n"
"Wees erbij om over \"%(subject)s\" te praten!"

#. module: website_event_meet
#: model:ir.model.fields,help:website_event_meet.field_event_meeting_room__website_url
msgid "The full URL to access the document through the website."
msgstr ""
"De volledige URL om toegang tot het document te krijgen via de website."

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__name
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Topic"
msgstr "Onderwerp"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_tree
msgid "Total Participant Count"
msgstr "Totaal aantal deelnemers"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_search
msgid "Unpublished"
msgstr "Niet gepubliceerd"

#. module: website_event_meet
#: model:event.meeting.room,summary:website_event_meet.event_meeting_room_2
msgid ""
"Venez partager vos meubles préférés et l'utilisation que vous en faites."
msgstr "Deel je favoriete meubels en hoe je deze gebruikt."

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_published
msgid "Visible on current website"
msgstr "Zichtbaar op huidige website"

#. module: website_event_meet
#: model:event.meeting.room,name:website_event_meet.event_meeting_room_2
msgid "Vos meubles préférés ?"
msgstr "Je favoriete meubelstuk?"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.community_aside
msgid "Want to create your own discussion room?"
msgstr "Wil je je eigen discussieruimte maken?"

#. module: website_event_meet
#: model:ir.model,name:website_event_meet.model_website_event_menu
msgid "Website Event Menu"
msgstr "Website event menu"

#. module: website_event_meet
#: model:ir.model.fields,field_description:website_event_meet.field_event_meeting_room__website_url
msgid "Website URL"
msgstr "Website URL"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "a few seconds"
msgstr "een paar seconden"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_2
msgid "client(s)"
msgstr "client(en)"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
msgid "e.g. Accountants"
msgstr "bijv. accountants"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
msgid "e.g. Finance"
msgstr "Bijv. Financiën"

#. module: website_event_meet
#. odoo-javascript
#: code:addons/website_event_meet/static/src/xml/website_event_meeting_room.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_meet.event_meeting_room_view_form
msgid "e.g. Let's talk about Corporate Finance"
msgstr "bijv. Laten we het hebben over bedrijfsfinanciën"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_1
msgid "ecologist(s)"
msgstr "ecolo(o)g(en)"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid ""
"is over.\n"
"                        <br/>"
msgstr ""
"is voorbij.\n"
"                        <br/>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""
"is voorbij.\n"
"                <br/>"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "participant(s)"
msgstr "deelnemer(s)"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts in"
msgstr "Begint in"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_main
msgid "starts on"
msgstr "begint om"

#. module: website_event_meet
#: model_terms:ir.ui.view,arch_db:website_event_meet.meeting_room_card
msgid "to have a chat with us!"
msgstr "om met ons te chatten!"

#. module: website_event_meet
#: model:event.meeting.room,target_audience:website_event_meet.event_meeting_room_0
msgid "wood expert(s)"
msgstr "hout expert(s)"
