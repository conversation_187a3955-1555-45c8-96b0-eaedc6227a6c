# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_helpdesk
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Hindi (https://app.transifex.com/odoo/teams/41243/hi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team
msgid "<b>About our team</b>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar
msgid "<i class=\"fa fa-chevron-left me-2\"/>Back to help"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid ""
"<span class=\"s_website_form_label_content\">Ask Your Question</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid "<span class=\"s_website_form_label_content\">Attachment</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid "<span class=\"s_website_form_label_content\">Company Name</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid ""
"<span class=\"s_website_form_label_content\">Email Address</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid ""
"<span class=\"s_website_form_label_content\">Full Name</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid "<span class=\"s_website_form_label_content\">Helpdesk Team</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid ""
"<span class=\"s_website_form_label_content\">Message Subject</span>\n"
"                                            <span class=\"s_website_form_mark\"> *</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid "<span class=\"s_website_form_label_content\">Phone Number</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar
msgid "<span class=\"text-muted\">Results for</span>"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_date
msgid "All"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_tag
msgid "All Tags"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_type
msgid "All Types"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_date
msgid "All dates"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/xml/website_helpdesk.xml:0
msgid "All results"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_tag
msgid "All tags"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_type
msgid "All types"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_form_editor.js:0
msgid "Ask Your Question"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_form_editor.js:0
msgid "Attach File"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar
#: model_terms:ir.ui.view,arch_db:website_helpdesk.search_results
msgid "Back to help"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_date
msgid "By Date"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_tag
msgid "By Tags"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_type
msgid "By Type"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__can_publish
msgid "Can Publish"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.footer
msgid "Can't find your answer?"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_form_editor.js:0
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_ticket__partner_company_name
msgid "Company Name"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.footer
msgid "Contact Us"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.footer
msgid "Contact us"
msgstr "हमसे संपर्क करें"

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.helpdesk_all_team
msgid "Contact us <i class=\"oi oi-arrow-right\"/>"
msgstr ""

#. module: website_helpdesk
#. odoo-python
#: code:addons/website_helpdesk/__init__.py:0
msgid "Customer Care (Public)"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team
msgid ""
"DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL HELPDESK TEAMS"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.knowledge_base
msgid ""
"DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS THE KNOWLEDGE PAGE "
"OF ALL HELPDESK TEAMS"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.snippet_options
msgid "Date Filter"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_form_editor.js:0
msgid "Email Address"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_date
msgid "Filter by date"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_tag
msgid "Filter by tags"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.navbar_search_type
msgid "Filter by type"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_form_editor.js:0
msgid "Full Name"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submited
msgid "Go to Homepage"
msgstr ""

#. module: website_helpdesk
#. odoo-python
#: code:addons/website_helpdesk/models/helpdesk.py:0
#: code:addons/website_helpdesk/models/website.py:0
msgid "Help"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.snippet_options
msgid "Help Center Page"
msgstr ""

#. module: website_helpdesk
#. odoo-python
#: code:addons/website_helpdesk/models/website.py:0
msgid "Helpdesk Customer Satisfaction"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_form_editor.js:0
#: model:ir.model,name:website_helpdesk.model_helpdesk_team
msgid "Helpdesk Team"
msgstr ""

#. module: website_helpdesk
#: model:ir.model,name:website_helpdesk.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.knowledge_base
msgid "How can we help you?"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.helpdesk_all_team
msgid "Image"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__is_published
msgid "Is Published"
msgstr ""

#. module: website_helpdesk
#. odoo-python
#: code:addons/website_helpdesk/controllers/main.py:0
msgid "Last Month"
msgstr ""

#. module: website_helpdesk
#. odoo-python
#: code:addons/website_helpdesk/controllers/main.py:0
msgid "Last Week"
msgstr ""

#. module: website_helpdesk
#. odoo-python
#: code:addons/website_helpdesk/controllers/main.py:0
msgid "Last Year"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_form_editor.js:0
msgid "Message Subject"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.search_results
msgid "No results found for"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/xml/website_helpdesk.xml:0
msgid "No results found. Please try another search."
msgstr ""

#. module: website_helpdesk
#. odoo-python
#: code:addons/website_helpdesk/controllers/main.py:0
msgid "Other Information"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.footer
msgid "Our support team is here to help."
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submited
msgid "Our team will get right on it."
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_form_editor.js:0
msgid "Phone Number"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.search_results
msgid "Please try another search  or"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__is_seo_optimized
msgid "SEO optimized"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.knowledge_base
msgid "Search for articles, best practices and more..."
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.helpdesk_all_team
msgid "Select your Team for help"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__seo_name
msgid "Seo name"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid "Submit Ticket"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team
#: model_terms:ir.ui.view,arch_db:website_helpdesk.team_form_1
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submit_form
msgid "Submit a Ticket"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.snippet_options
msgid "Tags Filter"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submited
msgid "Thank you!"
msgstr ""

#. module: website_helpdesk
#. odoo-javascript
#: code:addons/website_helpdesk/static/src/js/website_helpdesk_edit_menu.js:0
msgid ""
"The %s URL is reserved for the helpdesk team with the same name. \n"
"                        To use it, please enable the 'website form' feature on that team instead."
msgstr ""

#. module: website_helpdesk
#. odoo-python
#: code:addons/website_helpdesk/models/helpdesk.py:0
msgid "The companies of the team and the website should match."
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,help:website_helpdesk.field_helpdesk_team__website_url
msgid "The full URL to access the document through the website."
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submited
msgid "Ticket #"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.snippet_options
msgid "Type Filter"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__website_published
msgid "Visible on current website"
msgstr ""

#. module: website_helpdesk
#: model:ir.model,name:website_helpdesk.model_website
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__website_id
msgid "Website"
msgstr ""

#. module: website_helpdesk
#: model:ir.model,name:website_helpdesk.model_website_menu
msgid "Website Menu"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__website_url
msgid "Website URL"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__website_meta_description
msgid "Website meta description"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__website_meta_keywords
msgid "Website meta keywords"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__website_meta_title
msgid "Website meta title"
msgstr ""

#. module: website_helpdesk
#: model:ir.model.fields,field_description:website_helpdesk.field_helpdesk_team__website_meta_og_img
msgid "Website opengraph image"
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.ticket_submited
msgid "Your ticket has been sent."
msgstr ""

#. module: website_helpdesk
#: model_terms:ir.ui.view,arch_db:website_helpdesk.search_results
msgid "go back to help page"
msgstr ""
