# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr
#
# Translators:
# Sit<PERSON><PERSON>n LY <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:48+0000\n"
"PO-Revision-Date: 2018-10-08 06:48+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Khmer (https://www.transifex.com/odoo/teams/41243/km/)\n"
"Language: km\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email_amount
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email_amount
msgid "# emails to send"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/wizard/hr_plan_wizard.py:0
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_job.py:0
msgid "%s (copy)"
msgstr "%s (ចម្លង)"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "&amp;nbsp;"
msgstr ""

#. module: hr
#: model:ir.actions.report,print_report_name:hr.hr_employee_print_badge
msgid "'Badge - %s' % (object.name).replace('/', '')"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Onsite Interview"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Phone Call"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "12 days / year, including <br>6 of your choice."
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "2 open days"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "4 Days after Interview"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "<b>Congratulations!</b> May I recommend you to setup an <a href=\"%s\">onboarding plan?</a>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<small role=\"img\" class=\"fa fa-fw fa-circle text-success o_button_icon hr_presence align-middle\" attrs=\"{'invisible': [('hr_icon_display', '!=', 'presence_present')]}\" aria-label=\"Present\" title=\"Present\"/>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<small role=\"img\" class=\"fa fa-fw fa-circle text-warning o_button_icon hr_presence align-middle\" attrs=\"{'invisible': [('hr_icon_display', '!=', 'presence_to_define')]}\" aria-label=\"To define\" title=\"To define\" name=\"presence_to_define\"/>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<small role=\"img\" class=\"fa fa-fw fa-circle-o text-muted o_button_icon hr_presence align-middle\" attrs=\"{'invisible': [('hr_icon_display', '!=', 'presence_absent')]}\" aria-label=\"Absent\" title=\"Absent\" name=\"presence_absent\"/>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<small role=\"img\" class=\"fa fa-fw fa-circle-o text-success o_button_icon hr_presence align-middle\" attrs=\"{'invisible': [('hr_icon_display', '!=', 'presence_absent_active')]}\" aria-label=\"Present but not active\" title=\"Present but not active\" name=\"presence_absent_active\"/>"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<small><b>READ</b></small>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle text-success\" role=\"img\" aria-label=\"Present\" title=\"Present\" name=\"presence_present\">\n"
"                                                    </span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle text-warning\" role=\"img\" aria-label=\"To define\" title=\"To define\" name=\"presence_to_define\">\n"
"                                                    </span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle-o text-muted\" role=\"img\" aria-label=\"Absent\" title=\"Absent\" name=\"presence_absent\">\n"
"                                                    </span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid ""
"<span class=\"fa fa-circle-o text-success\" role=\"img\" aria-label=\"Present but not active\" title=\"Present but not active\" name=\"presence_absent_active\">\n"
"                                                    </span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"flex-shrink-0 ml8 me-2\">IP Addresses (comma-separated)</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"flex-shrink-0 ml8 me-2\">Minimum number of emails to send</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span class=\"fw-bolder\">Reporting</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label cursor-default\">Close Activities</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label cursor-default\">Detailed Reason</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label cursor-default\">HR Info</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Not Connected\n"
"                                </span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "<span class=\"o_stat_text\">Connected Since</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form_smartbutton
msgid "<span class=\"o_stat_text\">Contacts</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_form
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Process</span>"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "<span>Km</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "<span>new Employees</span>"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "A full-time position <br>Attractive salary package."
msgstr ""

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_user_uniq
msgid "A user cannot be linked to multiple employees in the same company."
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__absent
msgid "Absent"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Achieve monthly sales objectives"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_needaction
msgid "Action Needed"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__active
#: model:ir.model.fields,field_description:hr.field_hr_employee__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__active
#: model:ir.model.fields,field_description:hr.field_hr_job__active
#: model:ir.model.fields,field_description:hr.field_hr_plan__active
#: model:ir.model.fields,field_description:hr.field_hr_work_location__active
msgid "Active"
msgstr "សកម្ម"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_ids
#: model:ir.model.fields,field_description:hr.field_hr_plan__plan_activity_type_ids
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_ids
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_activity_type_view_tree
msgid "Activities"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "Activities To Generate"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_activity_type_view_form
msgid "Activity"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_decoration
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_plan
msgid "Activity Planning"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_state
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_state
msgid "Activity State"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__activity_type_id
msgid "Activity Type"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_icon
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Activity by"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Add a new employee"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_plan_action
msgid "Add a new plan"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_plan_activity_type_action
msgid "Add a new plan activity"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_description
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_description
msgid "Additional Information"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__additional_note
#: model:ir.model.fields,field_description:hr.field_res_users__additional_note
msgid "Additional Note"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Additional languages"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_home_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_home_id
msgid "Address"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Administrative Work"
msgstr ""

#. module: hr
#: model:res.groups,name:hr.group_hr_manager
msgid "Administrator"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_presence
msgid "Advanced Presence Control"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Advanced presence of employees"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "ការទំនាក់ទំនងដោយប្រុងប្រយ័ត្ន"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__lang
#: model:ir.model.fields,help:hr.field_res_users__private_lang
msgid "All the emails and documents sent to this contact will be translated in this language."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Application Settings"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Apply"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Approvers"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__archive_private_address
msgid "Archive Private Address"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Archived"
msgstr "ឯកសារ"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"As an employee of our company, you will <b>collaborate with each department to create and deploy\n"
"                                disruptive products.</b> Come work at a growing company that offers great benefits with opportunities to\n"
"                                moving forward and learn alongside accomplished leaders. We're seeking an experienced and outstanding member of staff.\n"
"                                <br><br>\n"
"                                This position is both <b>creative and rigorous</b> by nature you need to think outside the box.\n"
"                                We expect the candidate to be proactive and have a \"get it done\" spirit. To be successful,\n"
"                                you will have solid solving problem skills."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_job__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Attendance"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Attendance/Point of Sale"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__mail_alias__alias_contact__employees
msgid "Authenticated Employees"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.discuss_channel_view_form
msgid "Auto Subscribe Departments"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_discuss_channel__subscription_department_ids
msgid "Automatically subscribe members of those departments to the channel."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Autonomy"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Available"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1920
msgid "Avatar"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1024
msgid "Avatar 1024"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_128
msgid "Avatar 128"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_256
msgid "Avatar 256"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_512
msgid "Avatar 512"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Away"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__bachelor
msgid "Bachelor"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Bachelor Degree or Higher"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__barcode
#: model:ir.model.fields,field_description:hr.field_res_users__barcode
msgid "Badge ID"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__bank_account_id
msgid "Bank Account Number"
msgstr "លេខគណនីធនាគារ"

#. module: hr
#: model:ir.model,name:hr.model_base
msgid "Base"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip
msgid "Based on IP Address"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_attendance
msgid "Based on attendances"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email
msgid "Based on number of emails sent"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_login
msgid "Based on user status in system"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_base
msgid "Basic Employee"
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/background_image/background_image.xml:0
msgid "Binary file"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__can_edit
msgid "Can Edit"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.plan_wizard
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Cancel"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__certificate
#: model:ir.model.fields,field_description:hr.field_res_users__certificate
msgid "Certificate Level"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.res_users_action_my
msgid "Change my Preferences"
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/employee_chat/employee_chat.xml:0
msgid "Chat"
msgstr ""

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr ""

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__child_ids
msgid "Child Departments"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "City"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,field_description:hr.field_res_users__coach_id
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__coach
msgid "Coach"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_plan_activity_type.py:0
msgid "Coach of employee %s is not set."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__color
#: model:ir.model.fields,field_description:hr.field_hr_employee__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__color
msgid "Color Index"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_res_company
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "ក្រុមហ៊ុន"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__company_id
#: model:ir.model.fields,field_description:hr.field_hr_job__company_id
#: model:ir.model.fields,field_description:hr.field_hr_plan__company_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__company_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__company_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__company_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__company_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "ក្រុមហ៊ុន"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_id
msgid "Company Country"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Company Logo"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__resource_calendar_id
msgid "Company Working Hours"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_id
msgid "Company employee"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__complete_name
msgid "Complete Name"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Configuration"
msgstr "កំណត់ផ្លាស់ប្តូរ"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "ទំនាក់ទំនង"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Contact Information"
msgstr "ព័ត៌មានទំនាក់ទំនង"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_contact
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_contact
msgid "Contact Name"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_phone
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_phone
msgid "Contact Phone"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_contract_type
msgid "Contract Type"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_contract_type_view_tree
msgid "Contract Types"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__contractor
msgid "Contractor"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Country"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_code
msgid "Country Code"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__country_of_birth
msgid "Country of Birth"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_date
msgid "Create Date"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
msgid "Create Employee"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.server,name:hr.action_hr_employee_create_user
msgid "Create User"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid "Create a new department"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Create content that will help our users on a daily basis"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Create employee"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_uid
msgid "Created by"
msgstr "បង្កើតដោយ"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_date
#: model:ir.model.fields,field_description:hr.field_hr_department__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_date
#: model:ir.model.fields,field_description:hr.field_hr_job__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__create_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_date
msgid "Created on"
msgstr "បង្កើតនៅ"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__currency_id
msgid "Currency"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_employee
msgid "Current Number of Employees"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Customer Relationship"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__birthday
#: model:ir.model.fields,field_description:hr.field_res_users__birthday
msgid "Date of Birth"
msgstr "ថ្ងៃខែឆ្នាំកំណើត"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_departure_reason.py:0
msgid "Default departure reasons cannot be deleted."
msgstr ""

#. module: hr
#: model_terms:hr.job,description:hr.job_ceo
msgid ""
"Demonstration of different Odoo services for each client and convincing the client about functionality of the application.\n"
"The candidate should have excellent communication skills.\n"
"Relationship building and influencing skills\n"
"Expertise in New Client Acquisition (NCAs) and Relationship Management.\n"
"Gathering market and customer information.\n"
"Coordinating with the sales and support team for adopting different strategies\n"
"Reviewing progress and identifying opportunities and new areas for development.\n"
"Building strong relationships with clients / customers for business growth profitability.\n"
"Keep regular interaction with key clients for better extraction and expansion."
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_department
#: model:ir.model.fields,field_description:hr.field_hr_employee__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__department_id
#: model:ir.model.fields,field_description:hr.field_hr_job__department_id
#: model:ir.model.fields,field_description:hr.field_hr_plan__department_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__department_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__department_id
#: model:ir.model.fields,field_description:hr.field_res_users__department_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Department"
msgstr "ដេប៉ាតឺម៉ង់"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__name
msgid "Department Name"
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
msgid "Department Organization"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_department_kanban_action
#: model:ir.actions.act_window,name:hr.hr_department_tree_action
#: model:ir.ui.menu,name:hr.menu_hr_department_kanban
#: model:ir.ui.menu,name:hr.menu_hr_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Departure"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_date
msgid "Departure Date"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_reason
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_reason_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_reason_id
msgid "Departure Reason"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_departure_reason_action
#: model:ir.ui.menu,name:hr.menu_hr_departure_reason_tree
msgid "Departure Reasons"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Dependant"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__child_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__child_ids
msgid "Direct subordinates"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee
msgid "Directory"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Discard"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Discover our products."
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_discuss_channel
msgid "Discussion Channel"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__display_name
#: model:ir.model.fields,field_description:hr.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__display_name
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_work_location__display_name
msgid "Display Name"
msgstr "ឈ្មោះសំរាប់បង្ហាញ"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__divorced
msgid "Divorced"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__doctor
msgid "Doctor"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__driving_license
msgid "Driving License"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                        You can make a real contribution to the success of the company.\n"
"                        <br>\n"
"                        Several activities are often organized all over the year, such as weekly\n"
"                        sports sessions, team building events, monthly drink, and much more"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Eat &amp; Drink"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Education"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Email"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Emergency"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
#: model:ir.model,name:hr.model_hr_employee
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__employee_ids
#: model:ir.model.fields,field_description:hr.field_resource_resource__employee_id
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__employee
#: model:ir.ui.menu,name:hr.menu_config_employee
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee"
msgstr "បុគ្គលិក"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_count
msgid "Employee Count"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_employee_self_edit
msgid "Employee Editing"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Employee Image"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_lang
msgid "Employee Lang"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__name
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Employee Name"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.model.fields,field_description:hr.field_res_users__category_ids
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/archive_employee_hook.js:0
msgid "Employee Termination"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_type
#: model:ir.model.fields,field_description:hr.field_res_users__employee_type
msgid "Employee Type"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Employee Update Rights"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__bank_account_id
#: model:ir.model.fields,help:hr.field_res_users__employee_bank_account_id
msgid "Employee bank account to pay salaries"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_bank_account_id
msgid "Employee's Bank Account Number"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_country_id
msgid "Employee's Country"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_resource_calendar_id
msgid "Employee's Working Hours"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.act_employee_from_department
#: model:ir.actions.act_window,name:hr.hr_employee_public_action
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.model.fields,field_description:hr.field_res_partner__employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_employee_payroll
#: model:ir.ui.menu,name:hr.menu_hr_employee_user
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_activity
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Employees"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_partner__employees_count
#: model:ir.model.fields,field_description:hr.field_res_users__employees_count
msgid "Employees Count"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_employee_tree
msgid "Employees Structure"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__contract_type_id
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Employment Type"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_contract_type_action
#: model:ir.ui.menu,name:hr.menu_view_hr_contract_type
msgid "Employment Types"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Enrich employee profiles with skills and resumes"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__address_home_id
#: model:ir.model.fields,help:hr.field_res_users__address_home_id
msgid "Enter here the private address of the employee, not the one linked to your company."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Expand your knowledge of various business industries"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__expected_employees
msgid "Expected number of employees for this job position after new recruitment."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Experience in writing online content"
msgstr ""

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Family Status"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__female
msgid "Female"
msgstr "ភេទស្រី"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_field
#: model:ir.model.fields,field_description:hr.field_res_users__study_field
msgid "Field of Study"
msgstr ""

#. module: hr
#: model:hr.departure.reason,name:hr.departure_fired
msgid "Fired"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_follower_ids
msgid "Followers"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_type_icon
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/discuss_channel.py:0
msgid "For %(channels)s, channel_type should be 'channel' to have the department auto-subscription."
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__freelance
msgid "Freelancer"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Fruit, coffee and <br>snacks provided."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Future Activities"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__gender
#: model:ir.model.fields,field_description:hr.field_res_users__gender
msgid "Gender"
msgstr "ភេទ"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Generate"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Give more details about the reason of archiving the employee."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Google Adwords experience"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__graduate
msgid "Graduate"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Great team of smart people, in a friendly and open culture"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "ជា​ក្រុម​តាម"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_discuss_channel__subscription_department_ids
msgid "HR Departments"
msgstr ""

#. module: hr
#: model:ir.actions.server,name:hr.ir_cron_data_check_work_permit_validity_ir_actions_server
msgid "HR Employee: check work permit validity"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "HR Settings"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__has_message
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_message
#: model:ir.model.fields,field_description:hr.field_hr_job__has_message
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__has_message
msgid "Has Message"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Highly creative and autonomous"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_hired_employee
msgid "Hired Employees"
msgstr "បុគ្គលិកដែលបានជួល"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__km_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__km_home_work
msgid "Home-Work Distance"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_homeworking
msgid "Homeworking"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_icon_display
msgid "Hr Icon Display"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_res_users__hr_presence_state
msgid "Hr Presence State"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "ធនធានមនុស្ស"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__id
#: model:ir.model.fields,field_description:hr.field_hr_department__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_employee__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr.field_hr_job__id
#: model:ir.model.fields,field_description:hr.field_hr_plan__id
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__id
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__id
msgid "ID"
msgstr "ID"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__id_card
msgid "ID Card Copy"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__barcode
#: model:ir.model.fields,help:hr.field_res_users__barcode
msgid "ID used for employee identification."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_icon
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_icon
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
#: model:ir.model.fields,field_description:hr.field_res_users__identification_id
msgid "Identification No"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__active
msgid "If the active field is set to False, it will allow you to hide the resource record without removing it."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__private_car_plate
msgid "If you have more than one car, just separate the plates by a space."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1920
msgid "Image"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1024
msgid "Image 1024"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_128
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__image_128
msgid "Image 128"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_256
msgid "Image 256"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_512
msgid "Image 512"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Import Template for Employees"
msgstr ""

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_interim
msgid "Interim"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_job__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__is_system
msgid "Is System"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Job"
msgstr "ការងារ"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__description
msgid "Job Description"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_id
#: model:ir.model.fields,field_description:hr.field_hr_job__name
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job Position"
msgstr "តំណែង"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
#: model:ir.ui.menu,name:hr.menu_view_hr_job
msgid "Job Positions"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Job Summary"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_title
#: model:ir.model.fields,field_description:hr.field_res_users__job_title
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Job Title"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__lang
msgid "Lang"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Language"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity
msgid "Last Activity"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity_time
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity_time
msgid "Last Activity Time"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_uid
msgid "Last Updated by"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_date
#: model:ir.model.fields,field_description:hr.field_hr_department__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_date
#: model:ir.model.fields,field_description:hr.field_hr_job__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__write_date
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_date
msgid "Last Updated on"
msgstr "ផ្លាស់ប្តូរចុងក្រោយ"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Late Activities"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.plan_wizard_action
#: model_terms:ir.ui.view,arch_db:hr.plan_wizard
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Launch Plan"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/wizard/hr_plan_wizard.py:0
msgid "Launch Plans"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Lead the entire sales cycle"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__cohabitant
msgid "Legal Cohabitant"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Let's create a job position."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Location"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_number
msgid "Location Number"
msgstr ""

#. module: hr
#: model_terms:hr.job,description:hr.job_hrm
msgid "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__male
msgid "Male"
msgstr "ភេទប្រុស"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__parent_id
#: model:ir.model.fields,field_description:hr.field_res_users__employee_parent_id
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__manager
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Manager"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_plan_activity_type.py:0
msgid "Manager of employee %s is not set."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__marital
#: model:ir.model.fields,field_description:hr.field_res_users__marital
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Marital Status"
msgstr ""

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__married
msgid "Married"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__master
msgid "Master"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__master_department_id
msgid "Master Department"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Master demos of our software"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__member_of_department
msgid "Member of department"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__member_ids
msgid "Members"
msgstr "សមាជិក"

#. module: hr
#: model:ir.model,name:hr.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_ids
msgid "Messages"
msgstr "សារ"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Must Have"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Department"
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/user_menu/my_profile.js:0
msgid "My Profile"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Team"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__name
#: model:ir.model.fields,field_description:hr.field_hr_plan__name
msgid "Name"
msgstr "ឈ្មោះ"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_id
msgid "Nationality (Country)"
msgstr "សញ្ជាតិ (ប្រទេស)"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Negotiate and contract"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_date_deadline
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_summary
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_employee_activity_kanban
msgid "Next Activity:"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Nice to have"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_plan_activity_type.py:0
msgid "No specific user given on activity %s."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Not available"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__note
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__note
msgid "Note"
msgstr "កំណត់សំគាល់"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__notes
msgid "Notes"
msgstr "កំណត់សម្គាល់"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
#: model:ir.model.fields,field_description:hr.field_res_users__children
msgid "Number of Dependent Children"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_hired_employee
msgid "Number of hired employees for this job position during recruitment phase."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__related_contacts_count
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__related_contacts_count
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__related_contacts_count
msgid "Number of related contacts"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                    related to employees by departments: expenses, timesheets,\n"
"                    leaves, recruitments, etc."
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
msgid ""
"Odoo's department structure is used to manage all documents\n"
"                related to employees by departments: expenses, timesheets,\n"
"                time off, recruitments, etc."
msgstr ""

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer: Manage all employees"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_plan_plan
msgid "On/Offboarding Plans"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
msgid "Operation not supported"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__other
#: model:ir.model.fields.selection,name:hr.selection__hr_plan_activity_type__responsible__other
msgid "Other"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__responsible_id
msgid "Other Responsible"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Our Product"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__pin
#: model:ir.model.fields,field_description:hr.field_res_users__pin
msgid "PIN"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "PIN Code"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__pin
#: model:ir.model.fields,help:hr.field_res_users__pin
msgid "PIN used to Check In/Out in the Kiosk Mode of the Attendance application (if enabled in Configuration) and to change the cashier in the Point of Sale application."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_id
msgid "Parent Department"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_path
msgid "Parent Path"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__user_partner_id
msgid "Partner-related data of the user"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Passion for software products"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__passport_id
#: model:ir.model.fields,field_description:hr.field_res_users__passport_id
msgid "Passport No"
msgstr "លេខលិខិតឆ្លងដែន"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Payroll"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perfect written English"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perks"
msgstr ""

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_permanent
msgid "Permanent"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Personal Evolution"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "Personal information update."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Phone"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__place_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__place_of_birth
msgid "Place of Birth"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_plan_employee_activity
#: model:ir.model.fields,field_description:hr.field_hr_department__plan_ids
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__plan_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__plan_id
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_search
msgid "Plan"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_plan_activity_type_action
msgid "Plan Activities"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "Plan Name"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_plan_wizard
msgid "Plan Wizard"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_plan_activity_type
msgid "Plan activity type"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_tree
msgid "Planning"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_plan_action
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "Plans"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plans_count
msgid "Plans Count"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence Control"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence of employees"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence reporting screen, email and IP address control."
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__present
msgid "Present"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent_active
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent_active
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent_active
msgid "Present but not active"
msgstr ""

#. module: hr
#: model:ir.actions.report,name:hr.hr_employee_print_badge
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Print Badge"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Private Address"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_car_plate
msgid "Private Car Plate"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_city
msgid "Private City"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Contact"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_country_id
msgid "Private Country"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_email
#: model:ir.model.fields,field_description:hr.field_res_users__private_email
msgid "Private Email"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__phone
#: model:ir.model.fields,field_description:hr.field_res_users__employee_phone
msgid "Private Phone"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_state_id
msgid "Private State"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_street
msgid "Private Street"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_street2
msgid "Private Street2"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_zip
msgid "Private Zip"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_public
msgid "Public Employee"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Qualify the customer needs"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Ready to recruit more efficiently?"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__name
msgid "Reason"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_recruitment
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.act_window,name:hr.hr_departure_wizard_action
msgid "Register Departure"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__related_contact_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__related_contact_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__related_contact_ids
msgid "Related Contact"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
msgid "Related Contacts"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
msgid "Related Employees"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_ids
msgid "Related employee"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_res_partner__employee_ids
msgid "Related employees based on their private address"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_id
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__user_id
#: model:ir.model.fields,help:hr.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.hr_menu_hr_reports
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__requirements
msgid "Requirements"
msgstr "តម្រូវការ"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_resigned
msgid "Resigned"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_id
msgid "Resource"
msgstr "ធនធាន"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_calendar_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_calendar_id
msgid "Resource Calendar"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_resource_resource
msgid "Resources"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Responsibilities"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__responsible
msgid "Responsible"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_user_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: hr
#: model:hr.departure.reason,name:hr.departure_retired
msgid "Retired"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__sinid
msgid "SIN No"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__ssnid
msgid "SSN No"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Save"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Schedule"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_school
#: model:ir.model.fields,field_description:hr.field_res_users__study_school
msgid "School"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,help:hr.field_res_users__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__sequence
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__sequence
#: model:ir.model.fields,field_description:hr.field_hr_job__sequence
msgid "Sequence"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Set default company schedule to manage your employees working time"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
msgid "Settings"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__show_hr_icon_display
msgid "Show Hr Icon Display"
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
msgid "Show employees"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__single
msgid "Single"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_skills
msgid "Skills Management"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__sinid
msgid "Social Insurance Number"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__ssnid
msgid "Social Security Number"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_plan_activity_type__responsible_id
msgid "Specific responsible of activity if not linked to the employee."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Sport Activity"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_birthdate
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_birthdate
msgid "Spouse Birthdate"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_complete_name
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_complete_name
msgid "Spouse Complete Name"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "State"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Status"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_state
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan__steps_count
msgid "Steps Count"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Street 2..."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Street..."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Strong analytical skills"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__student
msgid "Student"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_plan_activity_type__summary
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__summary
msgid "Summary"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__name
msgid "Tag Name"
msgstr ""

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_category_name_uniq
msgid "Tag name already exists!"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__category_ids
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Tags"
msgstr "ធែក"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_recruitment
msgid "Target"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Technical Expertise"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee_id
msgid "Technical field, bind user to this employee on create"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee
msgid "Technical field, whether to create an employee"
msgstr ""

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_temporary
msgid "Temporary"
msgstr ""

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_barcode_uniq
msgid "The Badge ID must be unique, this one is already assigned to another employee."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The PIN must be a sequence of digits."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_plan_activity_type.py:0
msgid "The employee %s should be linked to a user."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_address_home_a_company
#: model:ir.model.fields,field_description:hr.field_res_users__is_address_home_a_company
msgid "The employee address has a company linked"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__employee_type
#: model:ir.model.fields,help:hr.field_res_users__employee_type
msgid "The employee type. Although the primary purpose may seem to categorize employees, this field has also an impact in the Contract History. Only Employee type is supposed to be under contract and will have a Contract History."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/wizard/hr_plan_wizard.py:0
msgid "The employees should belong to the same company."
msgstr ""

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_no_of_recruitment_positive
msgid "The expected number of new employees must be positive."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The fields \"%s\" you try to read is not available on the public employee profile."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "The following fields were modified by %s"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_plan_activity_type.py:0
msgid "The manager of %s should be linked to a user."
msgstr ""

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_name_company_uniq
msgid "The name of the job position must be unique per department in company!"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/wizard/hr_plan_wizard.py:0
msgid "The plan %s cannot be launched: <br><ul>"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/wizard/hr_plan_wizard.py:0
msgid "The plan %s has been started"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_plan_activity_type.py:0
msgid "The user of %s's coach is not set."
msgstr ""

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "The user will be able to approve document created by employees."
msgstr ""

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid "The user will have access to the human resources configuration as well as statistic reports."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The work permit of %(employee)s expires at %(date)s."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "This employee already has an user."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__tz
#: model:ir.model.fields,help:hr.field_hr_employee_base__tz
#: model:ir.model.fields,help:hr.field_hr_employee_public__tz
msgid "This field is used in order to define in which timezone the resources will work."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__tz
msgid "Timezone"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__to_define
msgid "To Define"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "To avoid multi company issues (losing the access to your previous contracts, leaves, ...), you should create another employee in the new company instead."
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_to_define
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_to_define
msgid "To define"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Today Activities"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__total_employee
msgid "Total Employee"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__expected_employees
msgid "Total Forecasted Employees"
msgstr ""

#. module: hr
#: model:hr.job,name:hr.job_trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__trainee
msgid "Trainee"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Trainings"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_decoration
#: model:ir.model.fields,help:hr.field_hr_plan_employee_activity__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_undetermined
msgid "Undetermined"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "សារមិនទាន់អាន"

#. module: hr
#: model:ir.model,name:hr.model_res_users
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_id
#: model:ir.model.fields,field_description:hr.field_hr_plan_employee_activity__user_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__user_id
msgid "User"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_partner_id
msgid "User's partner"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies:"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip_list
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip_list
msgid "Valid IP addresses"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Valid work permit for Belgium"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_expire
#: model:ir.model.fields,field_description:hr.field_res_users__visa_expire
msgid "Visa Expire Date"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_no
#: model:ir.model.fields,field_description:hr.field_res_users__visa_no
msgid "Visa No"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.model.fields,field_description:hr.field_hr_plan_wizard__warning
msgid "Warning"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What We Offer"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What's great in the job?"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_public__member_of_department
msgid "Whether the employee is a member of the active user's department or one of it's child department."
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__marital__widower
msgid "Widower"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.act_employee_from_department
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                    can easily find all the information you need for each person;\n"
"                    contact data, job position, availability, etc."
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__address_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__address_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_id
msgid "Work Address"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_contact_id
msgid "Work Contact"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_email
#: model:ir.model.fields,field_description:hr.field_res_users__work_email
msgid "Work Email"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_work_location
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_id
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_form_view
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_tree_view
msgid "Work Location"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_work_location_action
#: model:ir.ui.menu,name:hr.menu_hr_work_location_tree
msgid "Work Locations"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__mobile_phone
#: model:ir.model.fields,field_description:hr.field_res_users__mobile_phone
msgid "Work Mobile"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_work_permit
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_expiration_date
msgid "Work Permit Expiration Date"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_scheduled_activity
msgid "Work Permit Scheduled Activity"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_phone
#: model:ir.model.fields,field_description:hr.field_res_users__work_phone
msgid "Work Phone"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_calendar_id
msgid "Working Hours"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "You are only allowed to update your preferences. Please contact a HR officer to update other information."
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/thread_service_patch.js:0
msgid "You can only chat with employees that have a dedicated user."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_department.py:0
msgid "You cannot create recursive departments."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "You do not have access to this document."
msgstr ""

#. module: hr
#: model_terms:hr.job,description:hr.job_trainee
msgid ""
"You participate to the update of our tutorial tools and pre-sales tools after the launch of a new version of Odoo. Indeed, any new version of the software brings significant improvements in terms of functionalities, ergonomics and configuration.\n"
"You will have to become familiar with the existing tools (books, class supports, Odoo presentation’s slides, commercial tools),\n"
"to participate to the update of those tools in order to make them appropriate for the new version of the software and, for sure,\n"
"to suggest improvements in order to cover the new domains of the software.\n"
"You join the Implementation Assistance department. This team of 3 people go with Odoo’s clients in the set up of the software. Your role will be\n"
"to animate webinars in order to show the different functionalities of the software.\n"
"to be involved in the support of the customers and\n"
"to answer to their questions.\n"
"You help the support manager to set up new support services by\n"
"being involved in the treatment of new cases,\n"
"contributing to the set up of a new politic,\n"
"being involved into satisfaction surveys in order to have a better knowledge of how the support given is seen by the customers."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_config_settings.py:0
msgid "You should select at least one Advanced Presence Control option."
msgstr ""

#. module: hr
#: model_terms:hr.job,description:hr.job_cto
msgid ""
"You will take part in the consulting services we provide to our partners and customers: design, analysis, development, testing, project management, support/coaching. You will work autonomously as well as coordinate and supervise small distributed development teams for some projects. Optionally, you will deliver Odoo training sessions to partners and customers (8-10 people/session). You will report to the Head of Professional Services and work closely with all developers and consultants.\n"
"\n"
"The job is located in Grand-Rosière (1367), Belgium (between Louvain-La-Neuve and Namur)."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "ZIP"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_alias.py:0
msgid "addresses linked to registered employees"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "e.g. John Doe"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_plan_view_form
msgid "e.g. Onboarding"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_plan
msgid "plan"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/models.py:0
msgid "restricted to employees"
msgstr ""
