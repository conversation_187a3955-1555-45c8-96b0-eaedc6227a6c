# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr
# 
# Translators:
# f1b3a33e3b33fcf18004a5292e501f50_3500ca8 <373b677b151624c4521d9efc77b996fd_750224>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> Tibor <<EMAIL>>, 2024
# <PERSON><PERSON>gy <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# krnkris, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Hungarian (https://app.transifex.com/odoo/teams/41243/hu/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hu\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email_amount
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email_amount
msgid "# emails to send"
msgstr "# elküldendő e-mail"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_job.py:0
msgid "%s (copy)"
msgstr "%s (másolat)"

#. module: hr
#: model:ir.actions.report,print_report_name:hr.hr_employee_print_badge
msgid "'Badge - %s' % (object.name).replace('/', '')"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Onsite Interview"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "1 Phone Call"
msgstr "1 telefonhívás"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "12 days / year, including <br>6 of your choice."
msgstr "12 nap / év, amiből <br>6 nap választható."

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "2 open days"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "4 Days after Interview"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"<b>Congratulations!</b> May I recommend you to setup an <a "
"href=\"%s\">onboarding plan?</a>"
msgstr ""
"<b>Gratulálunk!</b> Beállítsunk egy <a href=\"%s\">beléptetési tervet?</a>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "<i class=\"fa fa-fw me-2 fa-envelope text-primary\" title=\"Email\"/>"
msgstr "<i class=\"fa fa-fw me-2 fa-envelope text-primary\" title=\"Email\"/>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
msgid "<i class=\"fa fa-fw me-2 fa-phone text-primary\" title=\"Phone\"/>"
msgstr "<i class=\"fa fa-fw me-2 fa-phone text-primary\" title=\"Phone\"/>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "<small><b>READ</b></small>"
msgstr "<small><b>OLVAS</b></small>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"flex-shrink-0 ml8 me-2\">IP Addresses</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "<span class=\"flex-shrink-0 ml8 me-2\">Sent Emails</span>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Close "
"Activities</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Lezárt "
"tevékenységek</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Detailed "
"Reason</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">Részletes "
"indok</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "<span class=\"o_form_label o_hr_form_label cursor-default\">HR Info</span>"
msgstr ""
"<span class=\"o_form_label o_hr_form_label cursor-default\">HR infó</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Not Connected\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Nincs belépve\n"
"                                </span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "<span class=\"o_stat_text\">Connected Since</span>"
msgstr "<span class=\"o_stat_text\">Belépve</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form_smartbutton_inherited
msgid "<span class=\"o_stat_text\">Contacts</span>"
msgstr "<span class=\"o_stat_text\">Kapcsolatok</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_form
msgid "<span class=\"o_stat_text\">Employee</span>"
msgstr "<span class=\"o_stat_value\">Munkavállaló</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr ""

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Folyamat</span>"

#. module: hr
#: model_terms:hr.job,job_details:hr.job_ceo
#: model_terms:hr.job,job_details:hr.job_consultant
#: model_terms:hr.job,job_details:hr.job_cto
#: model_terms:hr.job,job_details:hr.job_developer
#: model_terms:hr.job,job_details:hr.job_hrm
#: model_terms:hr.job,job_details:hr.job_marketing
#: model_terms:hr.job,job_details:hr.job_trainee
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Válaszolási idő</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Kimutatások</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "<span>View</span>"
msgstr "<span>Nézet</span>"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "<span>new Employees</span>"
msgstr "<span>új munkavállaló</span>"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "A full-time position <br>Attractive salary package."
msgstr "Teljes munkaidős állás <br>Vonzó bércsomag"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_user_uniq
msgid "A user cannot be linked to multiple employees in the same company."
msgstr ""
"Egy felhasználó nem kapcsolható össze több munkavállalóval ugyanazon a "
"vállalaton belül."

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_absent
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__absent
msgid "Absent"
msgstr "Távol lévő"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Achieve monthly sales objectives"
msgstr "Megvalósítani a havi értékesítési célokat"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction
msgid "Action Needed"
msgstr "Akció szükséges"

#. module: hr
#: model_terms:digest.tip,tip_description:hr.digest_tip_hr_0
msgid ""
"Activate Remote Work to let Employees specify where they are working from."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__active
#: model:ir.model.fields,field_description:hr.field_hr_employee__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__active
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__active
#: model:ir.model.fields,field_description:hr.field_hr_job__active
#: model:ir.model.fields,field_description:hr.field_hr_work_location__active
msgid "Active"
msgstr "Aktív"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_ids
msgid "Activities"
msgstr "Tevékenységek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_exception_decoration
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tevékenység kivétel dekoráció"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_plan
#: model:ir.ui.menu,name:hr.menu_config_plan_plan
msgid "Activity Plan"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_state
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_state
msgid "Activity State"
msgstr "Tevékenység állapot"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_type_icon
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tevékenység típus ikon"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Activity by"
msgstr "Tevékenység alapján"

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "Tevékenység terv sablon"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.mail_activity_plan_action
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Onboarding\", \"Offboarding\", ...)"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "Tevékenység ütemezés terv varázsló"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid "Add a new employee"
msgstr "Új munkavállaló"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_description
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_description
msgid "Additional Information"
msgstr "További információ"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"Additional Information: \n"
" %(description)s"
msgstr ""
"További információ: \n"
" %(description)s"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__additional_note
#: model:ir.model.fields,field_description:hr.field_res_users__additional_note
msgid "Additional Note"
msgstr "További megjegyzések"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Additional languages"
msgstr "További nyelvek"

#. module: hr
#: model:hr.department,name:hr.dep_administration
msgid "Administration"
msgstr "Adminisztráció"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Administrative Work"
msgstr "Adminisztrációs munka"

#. module: hr
#: model:res.groups,name:hr.group_hr_manager
msgid "Administrator"
msgstr "Adminisztrátor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_presence
msgid "Advanced Presence Control"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Advanced presence of employees"
msgstr "Haladó munkavállalók jelenlét"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_crm_team__alias_contact
#: model:ir.model.fields,field_description:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,field_description:hr.field_mail_group__alias_contact
#: model:ir.model.fields,field_description:hr.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "Álnév kapcsolat biztonság"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data"
msgstr "A munkavállalók módosíthatják saját adataikat"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Allow employees to update their own data."
msgstr "Engedélyezi a munkavállalóknak, hogy módosítsák saját adataikat."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Application Settings"
msgstr "Alkalmazás beállítások"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Apply"
msgstr "Alkalmazás"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_apprenticeship
msgid "Apprenticeship"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Approvers"
msgstr "Engedélyezők"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Archive"
msgstr "Archiválás"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_archive
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__archive
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Archived"
msgstr "Archivált"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"As an employee of our company, you will <b>collaborate with each department to create and deploy\n"
"                                disruptive products.</b> Come work at a growing company that offers great benefits with opportunities to\n"
"                                moving forward and learn alongside accomplished leaders. We're seeking an experienced and outstanding member of staff.\n"
"                                <br><br>\n"
"                                This position is both <b>creative and rigorous</b> by nature you need to think outside the box.\n"
"                                We expect the candidate to be proactive and have a \"get it done\" spirit. To be successful,\n"
"                                you will have solid solving problem skills."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_attachment_count
#: model:ir.model.fields,field_description:hr.field_hr_job__message_attachment_count
msgid "Attachment Count"
msgstr "Mellékletek száma"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Attendance"
msgstr "Jelenlét"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Attendance/Point of Sale"
msgstr "Jelenlét/Értékesítési pont"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__mail_alias__alias_contact__employees
msgid "Authenticated Employees"
msgstr "Hitelesített munkavállalók"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.discuss_channel_view_form
msgid "Auto Subscribe Departments"
msgstr "Részlegek automatikus feliratkoztatása"

#. module: hr
#: model:ir.model.fields,help:hr.field_discuss_channel__subscription_department_ids
msgid "Automatically subscribe members of those departments to the channel."
msgstr ""
"A részleghez tartozó munkatársak automatikus feliratkoztatása a csatornára"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Autonomy"
msgstr "Önállóság"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Available"
msgstr "Elérhető"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1920
msgid "Avatar"
msgstr "Avatár"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_1024
msgid "Avatar 1024"
msgstr "Avatár 1024"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_128
msgid "Avatar 128"
msgstr "Avatár 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_256
msgid "Avatar 256"
msgstr "Avatár 256"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__avatar_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__avatar_512
msgid "Avatar 512"
msgstr "Avatár 512"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Away"
msgstr "Távol"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__bachelor
msgid "Bachelor"
msgstr "Alapképzés"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Bachelor Degree or Higher"
msgstr "Alapképzés vagy magasabb"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__barcode
#: model:ir.model.fields,field_description:hr.field_res_users__barcode
msgid "Badge ID"
msgstr "Névkártya azonosító"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__bank_account_id
msgid "Bank Account"
msgstr "Bankszámla"

#. module: hr
#: model:ir.model,name:hr.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Bankszámlák"

#. module: hr
#: model:ir.model,name:hr.model_base
msgid "Base"
msgstr "Alap"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip
msgid "Based on IP Address"
msgstr "IP cím alapján"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_attendance
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_attendance
msgid "Based on attendances"
msgstr "Jelenlétek alapján"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_email
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_email
msgid "Based on number of emails sent"
msgstr "Elküldött e-mailek száma alapján"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_login
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_login
msgid "Based on user status in system"
msgstr "Felhasználó rendszerbeli állapota alapján"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_base
msgid "Basic Employee"
msgstr "Egyszerű munkavállaló"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/background_image/background_image.xml:0
msgid "Binary file"
msgstr "Bináris fájl"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__can_edit
msgid "Can Edit"
msgstr "Szerkeszthet"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Cancel"
msgstr "Visszavonás"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__employee_type
#: model:ir.model.fields,help:hr.field_res_users__employee_type
msgid ""
"Categorize your Employees by type. This field also has an impact on "
"contracts. Only Employees, Students and Trainee will have contract history."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__certificate
#: model:ir.model.fields,field_description:hr.field_res_users__certificate
msgid "Certificate Level"
msgstr "Végzettség"

#. module: hr
#: model:ir.actions.act_window,name:hr.res_users_action_my
msgid "Change my Preferences"
msgstr "Beállításaim megváltoztatása"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/employee_chat/employee_chat.xml:0
msgid "Chat"
msgstr "Csevegés"

#. module: hr
#: model:hr.job,name:hr.job_ceo
msgid "Chief Executive Officer"
msgstr "Vezérigazgató"

#. module: hr
#: model:hr.job,name:hr.job_cto
msgid "Chief Technical Officer"
msgstr "Műszaki igazgató"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__child_ids
msgid "Child Departments"
msgstr "Alrészlegek"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Child departments"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Citizenship"
msgstr "Állampolgárság"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "City"
msgstr "Város"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,field_description:hr.field_res_users__coach_id
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__coach
msgid "Coach"
msgstr "Tréner"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Coach of employee %s is not set."
msgstr "%s munkavállalóhoz nincs beállítva tréner."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__code
msgid "Code"
msgstr "Kód"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Color"
msgstr "Szín"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__color
#: model:ir.model.fields,field_description:hr.field_hr_employee__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__color
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__color
msgid "Color Index"
msgstr "Szín index"

#. module: hr
#: model:ir.model,name:hr.model_res_company
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
msgid "Companies"
msgstr "Vállalatok"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__company_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__company_id
#: model:ir.model.fields,field_description:hr.field_hr_job__company_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__company_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Company"
msgstr "Vállalat"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_id
msgid "Company Country"
msgstr "Vállalat országa"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Company Logo"
msgstr "Vállalati logó"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__resource_calendar_id
msgid "Company Working Hours"
msgstr "Vállalati munkaidő"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_id
msgid "Company employee"
msgstr "Vállalati munkavállaló"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__complete_name
msgid "Complete Name"
msgstr "Teljes név"

#. module: hr
#: model:ir.model,name:hr.model_res_config_settings
msgid "Config Settings"
msgstr "Beállítások"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_human_resources_configuration
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Configuration"
msgstr "Konfiguráció"

#. module: hr
#: model:hr.job,name:hr.job_consultant
msgid "Consultant"
msgstr "Tanácsadó"

#. module: hr
#: model:ir.model,name:hr.model_res_partner
msgid "Contact"
msgstr "Kapcsolat"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Contact Information"
msgstr "Elérhetőségek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_contact
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_contact
msgid "Contact Name"
msgstr "Kapcsolat neve"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__emergency_phone
#: model:ir.model.fields,field_description:hr.field_res_users__emergency_phone
msgid "Contact Phone"
msgstr "Kapcsolat telefonszám"

#. module: hr
#: model:ir.model,name:hr.model_hr_contract_type
msgid "Contract Type"
msgstr "Szerződés típus"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_contract_type_view_tree
msgid "Contract Types"
msgstr "Szerződéstípusok"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__contractor
msgid "Contractor"
msgstr "Szerződő"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__country_id
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Country"
msgstr "Ország"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__company_country_code
msgid "Country Code"
msgstr "Országkód"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__country_of_birth
msgid "Country of Birth"
msgstr "Születési ország"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_type
msgid "Cover Image"
msgstr "Borítókép"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_date
msgid "Create Date"
msgstr "Létrehozva"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
msgid "Create Employee"
msgstr "Munkavállaló létrehozás"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Create User"
msgstr "Új felhasználó"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid "Create a new department"
msgstr "Új részleg létrehozása"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_contract_type_action
msgid "Create a new employment type"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_work_location_action
msgid "Create a new work location"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.mail_activity_plan_action
msgid "Create an Activity Plan"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Create content that will help our users on a daily basis"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
msgid "Create employee"
msgstr "Munkavállaló létrehozás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__create_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_uid
msgid "Created by"
msgstr "Létrehozta"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__create_date
#: model:ir.model.fields,field_description:hr.field_hr_department__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__create_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__create_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__create_date
#: model:ir.model.fields,field_description:hr.field_hr_job__create_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__create_date
msgid "Created on"
msgstr "Létrehozva"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__currency_id
msgid "Currency"
msgstr "Pénznem"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_employee
msgid "Current Number of Employees"
msgstr "Jelenlegi munkavállalók száma"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Customer Relationship"
msgstr "Vevői kapcsolattartás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__birthday
#: model:ir.model.fields,field_description:hr.field_res_users__birthday
msgid "Date of Birth"
msgstr "Születési idő"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_departure_reason.py:0
msgid "Default departure reasons cannot be deleted."
msgstr "Az alapértelmezett távozási okokat nem lehet törölni."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Define the allowed IP to be displayed as Present. In case of multiple "
"addresses, separate them by a coma."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Define the minimum number of sent emails to be displayed as Present."
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__resource_calendar_id
#: model:ir.model.fields,help:hr.field_res_users__employee_resource_calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr ""

#. module: hr
#: model_terms:hr.job,description:hr.job_ceo
msgid ""
"Demonstration of different Odoo services for each client and convincing the client about functionality of the application.\n"
"The candidate should have excellent communication skills.\n"
"Relationship building and influencing skills\n"
"Expertise in New Client Acquisition (NCAs) and Relationship Management.\n"
"Gathering market and customer information.\n"
"Coordinating with the sales and support team for adopting different strategies\n"
"Reviewing progress and identifying opportunities and new areas for development.\n"
"Building strong relationships with clients / customers for business growth profitability.\n"
"Keep regular interaction with key clients for better extraction and expansion."
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card/avatar_card_popover_patch.xml:0
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model,name:hr.model_hr_department
#: model:ir.model.fields,field_description:hr.field_hr_employee__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__department_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__department_id
#: model:ir.model.fields,field_description:hr.field_hr_job__department_id
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan__department_id
#: model:ir.model.fields,field_description:hr.field_mail_activity_schedule__department_id
#: model:ir.model.fields,field_description:hr.field_res_users__department_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__department_id
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Department"
msgstr "Részleg"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_plan__department_assignable
msgid "Department Assignable"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__name
msgid "Department Name"
msgstr "Részleg neve"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
msgid "Department Organization"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_department_kanban_action
#: model:ir.actions.act_window,name:hr.hr_department_tree_action
#: model:ir.ui.menu,name:hr.menu_hr_department_kanban
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
msgid "Departments"
msgstr "Részlegek"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Departure"
msgstr "Távozás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_date
msgid "Departure Date"
msgstr "Távozás dátuma"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_reason
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__departure_reason_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__departure_reason_id
msgid "Departure Reason"
msgstr "Távozás oka"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_departure_reason_action
#: model:ir.ui.menu,name:hr.menu_hr_departure_reason_tree
msgid "Departure Reasons"
msgstr "Távozás okai"

#. module: hr
#: model:ir.model,name:hr.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Távozás varázsló"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Dependant"
msgstr "Eltartottak"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__child_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__child_ids
msgid "Direct subordinates"
msgstr "Közvetlen beosztottak"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_employee
msgid "Directory"
msgstr "Mappa"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Discard"
msgstr "Elvetés"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Discover our products."
msgstr "Fedezze fel termékeinket!"

#. module: hr
#: model:ir.model,name:hr.model_discuss_channel
msgid "Discussion Channel"
msgstr "Kommunikációs csatorna"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__display_name
#: model:ir.model.fields,field_description:hr.field_hr_department__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__display_name
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__display_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__display_name
#: model:ir.model.fields,field_description:hr.field_hr_job__display_name
#: model:ir.model.fields,field_description:hr.field_hr_work_location__display_name
msgid "Display Name"
msgstr "Megjelenített név"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid ""
"Display remote work settings for each employee and dedicated reports. "
"Presence icons will be updated with remote work location."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Divorced"
msgstr "Elvált"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__doctor
msgid "Doctor"
msgstr "Doktor"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__driving_license
msgid "Driving License"
msgstr "Jogosítvány"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"Each employee has a chance to see the impact of his work.\n"
"                            You can make a real contribution to the success of the company.\n"
"                            <br>\n"
"                            Several activities are often organized all over the year, such as weekly\n"
"                            sports sessions, team building events, monthly drink, and much more."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Eat &amp; Drink"
msgstr "Étel és ital"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Education"
msgstr "Tanulmányok"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__email
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Email"
msgstr "Email"

#. module: hr
#: model:ir.model,name:hr.model_mail_alias
msgid "Email Aliases"
msgstr "E-mail álnevek"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Emergency"
msgstr "Vész esetén"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
#: model:hr.contract.type,name:hr.contract_type_employee
#: model:ir.model,name:hr.model_hr_employee
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__employee_id
#: model:ir.model.fields,field_description:hr.field_hr_manager_department_report__employee_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__employee_id
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__employee
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__employee
#: model:ir.ui.menu,name:hr.menu_config_employee
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form_inherit_hr
msgid "Employee"
msgstr "Munkavállaló"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_category
msgid "Employee Category"
msgstr "Munkavállaló kategória"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_count
msgid "Employee Count"
msgstr "Munkavállalók száma"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_employee_self_edit
msgid "Employee Editing"
msgstr "Munkavállaló szerkesztés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.print_employee_badge
msgid "Employee Image"
msgstr "Munkavállaló fényképe"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__private_lang
msgid "Employee Lang"
msgstr "Munkavállaló nyelve"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__name
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "Employee Name"
msgstr "Munkavállaló neve"

#. module: hr
#: model:ir.actions.act_window,name:hr.mail_activity_plan_action
msgid "Employee Plans"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__employee_properties_definition
msgid "Employee Properties"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.open_view_categ_form
#: model:ir.model.fields,field_description:hr.field_res_users__category_ids
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_form
msgid "Employee Tags"
msgstr "Munkavállaló címkék"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/archive_employee_hook.js:0
msgid "Employee Termination"
msgstr "Munkavállaló elbocsátása"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_type
#: model:ir.model.fields,field_description:hr.field_res_users__employee_type
msgid "Employee Type"
msgstr "Munkavállaló típus"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Employee Update Rights"
msgstr "Munkavállaló hozzáférések frissítése"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__bank_account_id
#: model:ir.model.fields,help:hr.field_res_users__employee_bank_account_id
msgid "Employee bank account to pay salaries"
msgstr "Munkavállaló bankszámlaszáma, ahová fizetések érkeznek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_bank_account_id
msgid "Employee's Bank Account Number"
msgstr "Munkavállaló bankszámlaszáma"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_country_id
msgid "Employee's Country"
msgstr "Munkavállaló országa"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_kanban_view_employees
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Employee's Name"
msgstr "Munkavállaló neve"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_resource_calendar_id
msgid "Employee's Working Hours"
msgstr "Munkavállaló munkaórái"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_department.py:0
#: model:ir.actions.act_window,name:hr.hr_employee_public_action
#: model:ir.actions.act_window,name:hr.open_view_employee_list
#: model:ir.actions.act_window,name:hr.open_view_employee_list_my
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__employee_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__employee_ids
#: model:ir.model.fields,field_description:hr.field_res_partner__employee_ids
#: model:ir.ui.menu,name:hr.menu_hr_employee_payroll
#: model:ir.ui.menu,name:hr.menu_hr_employee_user
#: model:ir.ui.menu,name:hr.menu_hr_root
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_tree
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_activity
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_partner_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
#: model_terms:ir.ui.view,arch_db:hr.view_department_tree
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Employees"
msgstr "Munkavállalók"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_partner__employees_count
#: model:ir.model.fields,field_description:hr.field_res_users__employees_count
msgid "Employees Count"
msgstr "Munkavállalók száma"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_category_list
msgid "Employees Tags"
msgstr "Munkavállalók címkéi"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__contract_type_id
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Employment Type"
msgstr "Foglalkoztatás típus"

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_contract_type_action
#: model:ir.ui.menu,name:hr.menu_view_hr_contract_type
msgid "Employment Types"
msgstr "Foglalkoztatás típusok"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Enrich employee profiles with skills and resumes"
msgstr "Bővítse munkavállalói profilját kompetenciákkal és önéletrajzokkal"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Expand your knowledge of various business industries"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__expected_employees
msgid ""
"Expected number of employees for this job position after new recruitment."
msgstr "Új munkavállalók várható száma ebben a munkakörben a toborzás után."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Experience in writing online content"
msgstr "Tapasztalat online tartalmak készítésében"

#. module: hr
#: model:hr.job,name:hr.job_developer
msgid "Experienced Developer"
msgstr "Gyakorlott fejlesztő"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__share
#: model:ir.model.fields,help:hr.field_hr_employee_base__share
#: model:ir.model.fields,help:hr.field_hr_employee_public__share
msgid ""
"External user with limited access, created only for the purpose of sharing "
"data."
msgstr ""
"Külső felhasználó korlátozott hozzáféréssel, az adat megosztás céljából "
"létrehozva."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Family Status"
msgstr "Családi állapot"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__female
msgid "Female"
msgstr "Nő"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_field
#: model:ir.model.fields,field_description:hr.field_res_users__study_field
msgid "Field of Study"
msgstr "Tanulmányok"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_fired
msgid "Fired"
msgstr "Elbocsátott"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_follower_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_follower_ids
msgid "Followers"
msgstr "Követők"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_partner_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "Követők (Partnerek)"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_type_icon
#: model:ir.model.fields,help:hr.field_hr_employee__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome ikon pld: fa-tasks"

#. module: hr
#. odoo-python
#: code:addons/hr/models/discuss_channel.py:0
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the department "
"auto-subscription."
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__freelance
msgid "Freelancer"
msgstr "Szabadúszó"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Fruit, coffee and <br>snacks provided."
msgstr "Gyümölcs, kávé és <br>ennivaló biztosítva."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_full_time
msgid "Full-Time"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Future Activities"
msgstr "Jövőbeni tevékenységek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__gender
#: model:ir.model.fields,field_description:hr.field_res_users__gender
msgid "Gender"
msgstr "Neme"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Generate"
msgstr "Generálás"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_departure_wizard_view_form
msgid "Give more details about the reason of archiving the employee."
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Google Adwords experience"
msgstr "Google Adwords tapasztalat"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__graduate
msgid "Graduate"
msgstr "Diplomás"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Great team of smart people, in a friendly and open culture"
msgstr ""
"Zseniális emberekből álló nagyszerű csapat barátságos és nyitott "
"környezetben"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Group By"
msgstr "Csoportosítás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_discuss_channel__subscription_department_ids
msgid "HR Departments"
msgstr "Részlegek"

#. module: hr
#: model:ir.actions.server,name:hr.ir_cron_data_check_work_permit_validity_ir_actions_server
msgid "HR Employee: check work permit validity"
msgstr "HR Munkavállaló: munkavállalási engedély érvényesség ellenőrzése"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/hr_presence_status/hr_presence_status.js:0
msgid "HR Presence Status"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "HR Settings"
msgstr "Személyzeti beállítások"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_manager_department_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__has_message
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_message
#: model:ir.model.fields,field_description:hr.field_hr_job__has_message
msgid "Has Message"
msgstr "Van üzenet"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Highly creative and autonomous"
msgstr "Nagyon kreatív és önálló"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__home
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__home
msgid "Home"
msgstr "Kezdőlap"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__distance_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__distance_home_work
msgid "Home-Work Distance"
msgstr "Otthon-munkahely táv"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__km_home_work
#: model:ir.model.fields,field_description:hr.field_res_users__km_home_work
msgid "Home-Work Distance in Km"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__distance_home_work_unit
#: model:ir.model.fields,field_description:hr.field_res_users__distance_home_work_unit
msgid "Home-Work Distance unit"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_icon_display
#: model:ir.model.fields,field_description:hr.field_resource_resource__hr_icon_display
msgid "Hr Icon Display"
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_hr_manager_department_report
msgid "Hr Manager Department Report"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__hr_presence_state
#: model:ir.model.fields,field_description:hr.field_res_users__hr_presence_state
msgid "Hr Presence State"
msgstr "Jelenlét állapot"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_hr_main
msgid "Human Resources"
msgstr "Személyügy"

#. module: hr
#: model:hr.job,name:hr.job_hrm
msgid "Human Resources Manager"
msgstr "Személyügyi igazgató"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__id
#: model:ir.model.fields,field_description:hr.field_hr_department__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__id
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__id
#: model:ir.model.fields,field_description:hr.field_hr_employee__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__id
#: model:ir.model.fields,field_description:hr.field_hr_job__id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__id
msgid "ID"
msgstr "ID"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__id_card
msgid "ID Card Copy"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__barcode
#: model:ir.model.fields,help:hr.field_res_users__barcode
msgid "ID used for employee identification."
msgstr "Azonosító munkavállalók azonosításához."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__im_status
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__im_status
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__im_status
msgid "IM Status"
msgstr "Üzenetküldési állapot"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_exception_icon
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_exception_icon
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Kivétel tevékenységet jelző ikon"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__identification_id
#: model:ir.model.fields,field_description:hr.field_res_users__identification_id
msgid "Identification No"
msgstr "Szem. ig. száma"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ha be van jelölve, akkor az új üzenetek figyelmet igényelnek."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error
#: model:ir.model.fields,help:hr.field_hr_department__message_has_sms_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error
#: model:ir.model.fields,help:hr.field_hr_job__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Ha be van jelölve, akkor néhány üzenetnél kézbesítési hiba lépett fel."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Ha az aktív mező hamisra van állítva, akkor elrejtheti az erőforrást, "
"anélkül, hogy törölné azt."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__private_car_plate
msgid "If you have more than one car, just separate the plates by a space."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1920
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1920
msgid "Image"
msgstr "Kép"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_1024
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_1024
msgid "Image 1024"
msgstr "1024-es kép"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_128
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_128
msgid "Image 128"
msgstr "Kép 128"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_256
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_256
msgid "Image 256"
msgstr "256-os kép"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__image_512
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__image_512
msgid "Image 512"
msgstr "512-es kép"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Import Template for Employees"
msgstr "Munkavállaló importálás sablon"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_interim
msgid "Interim"
msgstr "Ideiglenes"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_part_time
msgid "Intern"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__is_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_flexible
msgid "Is Flexible"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_is_follower
#: model:ir.model.fields,field_description:hr.field_hr_job__message_is_follower
msgid "Is Follower"
msgstr "Követő"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__is_fully_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__is_fully_flexible
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_fully_flexible
msgid "Is Fully Flexible"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__is_manager
msgid "Is Manager"
msgstr "Menedzser"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__is_system
msgid "Is System"
msgstr "Rendszer"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Job"
msgstr "Munka"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__description
msgid "Job Description"
msgstr "Munkaköri leírás"

#. module: hr
#: model:ir.model,name:hr.model_hr_job
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_id
#: model:ir.model.fields,field_description:hr.field_hr_job__name
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Job Position"
msgstr "Munkakör"

#. module: hr
#: model:ir.actions.act_window,name:hr.action_hr_job
#: model:ir.ui.menu,name:hr.menu_view_hr_job
msgid "Job Positions"
msgstr "Munkakörök"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Job Summary"
msgstr "Munkaköri leírás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__job_title
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__job_title
#: model:ir.model.fields,field_description:hr.field_res_users__job_title
#: model:ir.model.fields,field_description:hr.field_resource_resource__job_title
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Job Title"
msgstr "Beosztás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__jobs_ids
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Jobs"
msgstr "Állások"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__lang
msgid "Lang"
msgstr "Nyelv"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Language"
msgstr "Nyelv"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity
msgid "Last Activity"
msgstr "Utolsó tevékenység"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__last_activity_time
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__last_activity_time
#: model:ir.model.fields,field_description:hr.field_res_users__last_activity_time
msgid "Last Activity Time"
msgstr "Utolsó tevékenység ideje"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_department__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_job__write_uid
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_uid
msgid "Last Updated by"
msgstr "Frissítette"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__write_date
#: model:ir.model.fields,field_description:hr.field_hr_department__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__write_date
#: model:ir.model.fields,field_description:hr.field_hr_departure_wizard__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__write_date
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__write_date
#: model:ir.model.fields,field_description:hr.field_hr_job__write_date
#: model:ir.model.fields,field_description:hr.field_hr_work_location__write_date
msgid "Last Updated on"
msgstr "Frissítve"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Late Activities"
msgstr "Késő tevékenységek"

#. module: hr
#: model:ir.actions.act_window,name:hr.plan_wizard_action
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_tree
msgid "Launch Plan"
msgstr "Terv indítása"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Lead the entire sales cycle"
msgstr "Felügyelet a teljes értékesítési cikluson"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Legal Cohabitant"
msgstr "Bejegyzett élettársi kapcsolat"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Let's create a job position."
msgstr "Hozzon létre egy munkakört!"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Location"
msgstr "Lokáció"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_work_location__location_number
msgid "Location Number"
msgstr "Lokáció szám"

#. module: hr
#: model:hr.department,name:hr.dep_rd_ltp
msgid "Long Term Projects"
msgstr ""

#. module: hr
#: model_terms:hr.job,description:hr.job_hrm
msgid ""
"Lorem Ipsum is simply dummy text of the printing and typesetting industry. "
"Lorem Ipsum has been the industry's standard dummy text ever since the "
"1500s, when an unknown printer took a galley of type and scrambled it to "
"make a type specimen book. It has survived not only five centuries, but also"
" the leap into electronic typesetting, remaining essentially unchanged. It "
"was popularised in the 1960s with the release of Letraset sheets containing "
"Lorem Ipsum passages, and more recently with desktop publishing software "
"like Aldus PageMaker including versions of Lorem Ipsum."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_main_attachment_id
msgid "Main Attachment"
msgstr "Fő melléklet"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__male
msgid "Male"
msgstr "Férfi"

#. module: hr
#: model:hr.department,name:hr.dep_management
msgid "Management"
msgstr "Vezetőség"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__manager_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__parent_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__parent_id
#: model:ir.model.fields,field_description:hr.field_res_users__employee_parent_id
#: model:ir.model.fields.selection,name:hr.selection__mail_activity_plan_template__responsible_type__manager
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Manager"
msgstr "Menedzser"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Manager of employee %s is not set."
msgstr "%s munkavállaló menedzsere nincs megadva."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__marital
#: model:ir.model.fields,field_description:hr.field_res_users__marital
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Marital Status"
msgstr "Családi állapot"

#. module: hr
#: model:hr.job,name:hr.job_marketing
msgid "Marketing and Community Manager"
msgstr "Marketing és közösségi menedzser"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Married"
msgstr "Házas"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__master
msgid "Master"
msgstr "Mester"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__master_department_id
msgid "Master Department"
msgstr "Fő részleg"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Master demos of our software"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__member_of_department
msgid "Member of department"
msgstr "Részleg tagjai"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__member_ids
msgid "Members"
msgstr "Tagok"

#. module: hr
#: model:ir.model,name:hr.model_ir_ui_menu
msgid "Menu"
msgstr "Menü"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error
msgid "Message Delivery error"
msgstr "Üzenetkézbesítési hiba"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__message_ids
msgid "Messages"
msgstr "Üzenetek"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Must Have"
msgstr "kötelező"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr.field_hr_employee__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Tevékenységeim határideje"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Department"
msgstr "Részlegem"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/user_menu/my_profile.js:0
msgid "My Profile"
msgstr "Profilom"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "My Team"
msgstr "Csapatom"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__name
msgid "Name"
msgstr "Név"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__country_id
msgid "Nationality (Country)"
msgstr "Állampolgárság (ország)"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Negotiate and contract"
msgstr "Tárgyalás és szerződéskötés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_graph
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_view_pivot
msgid "New Employees Over Time"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__newly_hired
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__newly_hired
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__newly_hired
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Newly Hired"
msgstr "Újonnan felvett"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Következő tevékenység naptár esemény"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_date_deadline
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Következő tevékenység határideje"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_summary
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_summary
msgid "Next Activity Summary"
msgstr "Következő tevékenység összegzése"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_type_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_type_id
msgid "Next Activity Type"
msgstr "Következő tevékenység típusa"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Nice to have"
msgstr "Jó ha van"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/hr_graph_controller.xml:0
#: code:addons/hr/static/src/views/hr_pivot_controller.xml:0
msgid "No Data"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_categ_form
msgid "No Tags found ! Let's create one"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "No dumb managers, no stupid tools to use, no rigid working hours"
msgstr ""
"Nincsenek: ostoba főnökök, használhatatlan eszközök és szigorú munkaidő"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid ""
"No waste of time in enterprise processes, real responsibilities and autonomy"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Not available"
msgstr "Nem elérhető"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__note
msgid "Note"
msgstr "Megjegyzés"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__notes
msgid "Notes"
msgstr "Megjegyzések"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_needaction_counter
msgid "Number of Actions"
msgstr "Akciók száma"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__children
#: model:ir.model.fields,field_description:hr.field_res_users__children
msgid "Number of Dependent Children"
msgstr "Eltartott gyerekek száma"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_tree
msgid "Number of Employees"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_employee
msgid "Number of employees currently occupying this job position."
msgstr "Munkavállalók száma, akik jelenleg betöltik ezt a munkakört."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_error_counter
msgid "Number of errors"
msgstr "Hibák száma"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_needaction_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Üzenetek száma, melyek akciót igényelnek"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_employee__message_has_error_counter
#: model:ir.model.fields,help:hr.field_hr_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Kézbesítési hibával rendelkező üzenetek száma"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_job__no_of_recruitment
msgid "Number of new employees you expect to recruit."
msgstr "A felvenni kívánt munkavállalók száma"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__office
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__office
msgid "Office"
msgstr ""

#. module: hr
#: model:res.groups,name:hr.group_hr_user
msgid "Officer: Manage all employees"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid ""
"Oops! It seems there is a problem with your team structure."
"                        We found a circular reporting loop and no one in "
"that loop is linked to a user.                        Please double-check "
"that everyone reports to the correct manager."
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/core/web/thread_actions.js:0
msgid "Open Profile"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
msgid "Operation not supported"
msgstr "A művelet nem támogatott"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__certificate__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__gender__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__work_location_type__other
#: model:ir.model.fields.selection,name:hr.selection__hr_work_location__location_type__other
msgid "Other"
msgstr "Egyéb"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Our Product"
msgstr "Termékeink"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
msgid "Out of Working Hours"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_out_of_working_hour
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__out_of_working_hour
msgid "Out of Working hours"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__pin
#: model:ir.model.fields,field_description:hr.field_res_users__pin
msgid "PIN"
msgstr "PIN"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "PIN Code"
msgstr "PIN kód"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__pin
#: model:ir.model.fields,help:hr.field_res_users__pin
msgid ""
"PIN used to Check In/Out in the Kiosk Mode of the Attendance application (if"
" enabled in Configuration) and to change the cashier in the Point of Sale "
"application."
msgstr ""
"A Jelenlét alkalmazásban kioszk módban a be- és kijelentkezéshez (ha be van "
"kapcsolva a beállításoknál), illetve az Értékesítési pont alkalmazásban a "
"pénztáros megváltoztatásához használt PIN"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_id
msgid "Parent Department"
msgstr "Főrészleg"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__parent_path
msgid "Parent Path"
msgstr "Szülő útvonal"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__user_partner_id
msgid "Partner-related data of the user"
msgstr "A felhasználó partner-kapcsolati adatai"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Passion for software products"
msgstr "Elhivatottság szoftveres termékek iránt"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__passport_id
#: model:ir.model.fields,field_description:hr.field_res_users__passport_id
msgid "Passport No"
msgstr "Útlevélszám"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perfect written English"
msgstr "Tökéletes angol tudás"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Perks"
msgstr "Jutalmak"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_permanent
msgid "Permanent"
msgstr "Állandó"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Personal Evolution"
msgstr "Személyes fejlődés"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "Personal information update."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__phone
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
msgid "Phone"
msgstr "Telefon"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__place_of_birth
#: model:ir.model.fields,field_description:hr.field_res_users__place_of_birth
msgid "Place of Birth"
msgstr "Születési hely"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plan_ids
msgid "Plan"
msgstr "Terv"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan.py:0
msgid ""
"Plan %(plan_names)s cannot use a department as it is used only for some HR "
"plans."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_mail_activity_schedule__plan_department_filterable
msgid "Plan Department Filterable"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan.py:0
msgid ""
"Plan activities %(template_names)s cannot use coach, manager or employee "
"responsible as it is used only for employee plans."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "Plans"
msgstr "Tervek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__plans_count
msgid "Plans Count"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Play any sport with colleagues, <br>the bill is covered."
msgstr "Sportoljon a kollégáival, <br>a számlát cégünk fizeti."

#. module: hr
#: model:ir.model.fields,help:hr.field_crm_team__alias_contact
#: model:ir.model.fields,help:hr.field_mail_alias__alias_contact
#: model:ir.model.fields,help:hr.field_mail_group__alias_contact
#: model:ir.model.fields,help:hr.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"A dokumentumra küldött üzenetek szabályai a levélátjáró használatával.\n"
"- mindenki: mindenki tud üzenni\n"
"- partnerek: csak a hitelesített partnerek\n"
"- követők: csak a kapcsolódó dokumentum követői vagy a következő csatornák tagjai\n"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence Condition"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence Display"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence of employees"
msgstr "Munkavállalók jelenléte"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Presence reporting screen, email and IP address control."
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_presence_state__present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_present
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_presence_state__present
msgid "Present"
msgstr "Jelen van"

#. module: hr
#: model:ir.actions.report,name:hr.hr_employee_print_badge
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Print Badge"
msgstr "Névkártya  nyomtatás"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Address"
msgstr "Lakcím"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_car_plate
msgid "Private Car Plate"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_city
#: model:ir.model.fields,field_description:hr.field_res_users__private_city
msgid "Private City"
msgstr "Város"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Contact"
msgstr "Személyes elérhetőség"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_country_id
#: model:ir.model.fields,field_description:hr.field_res_users__private_country_id
msgid "Private Country"
msgstr "Ország"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_email
#: model:ir.model.fields,field_description:hr.field_res_users__private_email
msgid "Private Email"
msgstr "Személyes e-mail"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Private Information"
msgstr "Személyes információk"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_phone
#: model:ir.model.fields,field_description:hr.field_res_users__private_phone
msgid "Private Phone"
msgstr "Személyes telefon"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_state_id
#: model:ir.model.fields,field_description:hr.field_res_users__private_state_id
msgid "Private State"
msgstr "Állam/megye"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_street
#: model:ir.model.fields,field_description:hr.field_res_users__private_street
msgid "Private Street"
msgstr "Utca"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_street2
#: model:ir.model.fields,field_description:hr.field_res_users__private_street2
msgid "Private Street2"
msgstr "Utca 2"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__private_zip
#: model:ir.model.fields,field_description:hr.field_res_users__private_zip
msgid "Private Zip"
msgstr "Irányítószám"

#. module: hr
#: model:hr.department,name:hr.dep_ps
msgid "Professional Services"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__employee_properties
msgid "Properties"
msgstr "Tulajdonságok"

#. module: hr
#: model:ir.model,name:hr.model_hr_employee_public
msgid "Public Employee"
msgstr "Nyilvános alkalmazott"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Qualify the customer needs"
msgstr ""

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_employee_public_action
#: model_terms:ir.actions.act_window,help:hr.open_view_employee_list_my
msgid ""
"Quickly find all the information you need for your employees such as contact"
" data, job position, availability, etc."
msgstr ""

#. module: hr
#: model:hr.department,name:hr.dep_rd_be
msgid "R&D USA"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__rating_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__rating_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__rating_ids
msgid "Ratings"
msgstr "Értékelések"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.action_hr_job
msgid "Ready to recruit more efficiently?"
msgstr "Készen áll a még hatékonyabb toborzásra?"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Real responsibilities and challenges in a fast evolving company"
msgstr "Igazi felelősségek és kihívások egy gyorsan fejlődő vállalatnál"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__name
msgid "Reason"
msgstr "Ok"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__reason_code
msgid "Reason Code"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.menu_config_recruitment
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "Recruitment"
msgstr "Toborzás"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model:ir.actions.act_window,name:hr.hr_departure_wizard_action
msgid "Register Departure"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form_smartbutton_inherited
msgid "Related Contacts"
msgstr "Kapcsolódó kapcsolatok"

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_partner.py:0 code:addons/hr/models/res_users.py:0
msgid "Related Employees"
msgstr "Kapcsolódó munkavállalók"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__related_partners_count
msgid "Related Partners Count"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Related User"
msgstr "Kapcsolódó felhasználó"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__employee_ids
msgid "Related employee"
msgstr "Kapcsolódó munkavállaló"

#. module: hr
#: model:ir.model.fields,help:hr.field_res_partner__employee_ids
msgid "Related employees based on their private address"
msgstr "Otthoni címük alapján kapcsoló munkavállalók"

#. module: hr
#: model:ir.model.fields,help:hr.field_resource_resource__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""
"Az erőforráshoz kapcsolódó felhasználó neve, aki annak hozzáférését kezeli."

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_homeworking
msgid "Remote Work"
msgstr ""

#. module: hr
#: model:ir.ui.menu,name:hr.hr_menu_hr_reports
#: model:ir.ui.menu,name:hr.menu_hr_reporting_timesheet
msgid "Reporting"
msgstr "Kimutatás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__requirements
msgid "Requirements"
msgstr "Követelmények"

#. module: hr
#: model:hr.department,name:hr.dep_rd
msgid "Research & Development"
msgstr "Kutatás, fejlesztés"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_resigned
msgid "Resigned"
msgstr "Felmondott"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_id
msgid "Resource"
msgstr "Erőforrás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__resource_calendar_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__resource_calendar_id
msgid "Resource Calendar"
msgstr "Erőforrás naptár"

#. module: hr
#: model:ir.model,name:hr.model_resource_resource
msgid "Resources"
msgstr "Erőforrások"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Responsibilities"
msgstr "Felelősségek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__activity_user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee__activity_user_id
msgid "Responsible User"
msgstr "Felelős felhasználó"

#. module: hr
#: model:hr.departure.reason,name:hr.departure_retired
msgid "Retired"
msgstr "Nyugdíjas"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__sinid
msgid "SIN No"
msgstr "TAJ szám"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__message_has_sms_error
#: model:ir.model.fields,field_description:hr.field_hr_employee__message_has_sms_error
#: model:ir.model.fields,field_description:hr.field_hr_job__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS kézbesítési hiba"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__ssnid
#: model:ir.model.fields,field_description:hr.field_res_users__ssnid
msgid "SSN No"
msgstr "Adószám"

#. module: hr
#: model:hr.department,name:hr.dep_sales
msgid "Sales"
msgstr "Értékesítés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_users_simple_form
msgid "Save"
msgstr "Mentés"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Schedule"
msgstr "Ütemezés"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__study_school
#: model:ir.model.fields,field_description:hr.field_res_users__study_school
msgid "School"
msgstr "Iskola"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_seasonal
msgid "Seasonal"
msgstr ""

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_base__coach_id
#: model:ir.model.fields,help:hr.field_hr_employee_public__coach_id
#: model:ir.model.fields,help:hr.field_res_users__coach_id
msgid ""
"Select the \"Employee\" who is the coach of this employee.\n"
"The \"Coach\" has no specific rights or responsibilities by default."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_contract_type__sequence
#: model:ir.model.fields,field_description:hr.field_hr_departure_reason__sequence
#: model:ir.model.fields,field_description:hr.field_hr_job__sequence
msgid "Sequence"
msgstr "Sorszám"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Set default company schedule to manage your employees working time"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_config_settings_action
#: model:ir.ui.menu,name:hr.hr_menu_configuration
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Settings"
msgstr "Beállítások"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__share
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__share
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__share
msgid "Share User"
msgstr "Felhasználó megosztás"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__show_hr_icon_display
#: model:ir.model.fields,field_description:hr.field_resource_resource__show_hr_icon_display
msgid "Show Hr Icon Display"
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/department_chart/department_chart.xml:0
msgid "Show employees"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Single"
msgstr "Egyedülálló"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_config_settings__module_hr_skills
msgid "Skills Management"
msgstr "Kompetencia kezelő"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__sinid
msgid "Social Insurance Number"
msgstr "Társadalombiztosítási azonosító jel"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__ssnid
#: model:ir.model.fields,help:hr.field_res_users__ssnid
msgid "Social Security Number"
msgstr "Társadalombiztosítási azonosító jel"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee_base.py:0
msgid "Some employee already have a work contact"
msgstr ""

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Sport Activity"
msgstr "Sporttevékenység"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_birthdate
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_birthdate
msgid "Spouse Birthdate"
msgstr "Házastárs sz.napja"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__spouse_complete_name
#: model:ir.model.fields,field_description:hr.field_res_users__spouse_complete_name
msgid "Spouse Complete Name"
msgstr "Házastárs neve"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Start Date"
msgstr "Kezdődátum"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "State"
msgstr "Állam/megye"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Status"
msgstr "Státusz"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_state
#: model:ir.model.fields,help:hr.field_hr_employee__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tevékenységeken alapuló állapot\n"
"Lejárt: A tevékenység határideje lejárt\n"
"Ma: A határidő ma van\n"
"Tervezett: Jövőbeli határidő."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_statutaire
msgid "Statutory"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Street 2..."
msgstr "Utca 2..."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Street..."
msgstr "Utca, házszám..."

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Strong analytical skills"
msgstr "Erős analitikus képességek"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.hr_department_kanban_action
#: model_terms:ir.actions.act_window,help:hr.hr_department_tree_action
msgid ""
"Structure Employees per department and have an overview of e.g.\n"
"                    expenses, timesheets, time off, recruitment, etc."
msgstr ""

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_student
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__student
msgid "Student"
msgstr "Tanuló"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee_category__name
msgid "Tag Name"
msgstr "Címke neve"

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_category_name_uniq
msgid "Tag name already exists!"
msgstr "A címke név már létezik!"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__category_ids
#: model:ir.ui.menu,name:hr.menu_view_employee_category_form
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Tags"
msgstr "Címkék"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__no_of_recruitment
msgid "Target"
msgstr "Cél"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Technical Expertise"
msgstr "Műszaki szakértő"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee_id
msgid "Technical field, bind user to this employee on create"
msgstr ""
"Technikai mező, a létrehozás során összeköti a felhasználót és a "
"munkavállalót"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_users__create_employee
msgid "Technical field, whether to create an employee"
msgstr "Technikai mező, létrehozásra kerüljön-e munkavállaló"

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_temporary
msgid "Temporary"
msgstr "Ideiglenes"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"The Badge ID must be alphanumeric without any accents and no longer than 18 "
"characters."
msgstr ""

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_employee_barcode_uniq
msgid ""
"The Badge ID must be unique, this one is already assigned to another "
"employee."
msgstr ""
"A névkártya azonosítónak egyedinek kell lennie, ezt már hozzárendelte egy "
"másik munkavállalóhoz."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__company_country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Kétbetűs ISO országkód.\n"
"Ezt a mezőt a gyors kereséshez használhatja."

#. module: hr
#: model_terms:hr.job,description:hr.job_marketing
msgid ""
"The Marketing Manager defines the mid- to long-term marketing strategy for his covered market segments in the World.\n"
"              He develops and monitors the annual budget in collaboration with Sales.\n"
"              He defines the products and customers portfolio according to the marketing plan.\n"
"              This mission requires strong collaboration with Technical Service and Sales."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The PIN must be a sequence of digits."
msgstr "A PIN sorrendbe állított számokat kell tartalmaznia."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "The default working hours are set in configuration."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The employee %s should be linked to a user."
msgstr "%s munkavállalónak kötődnie kéne egy felhasználóhoz."

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_no_of_recruitment_positive
msgid "The expected number of new employees must be positive."
msgstr "Az új munkavállalók várható számának pozitívnak kell lennie."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"The fields “%s”, which you are trying to read, are not available for "
"employee public profiles."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid "The following fields were modified by %s"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The manager of %s should be linked to a user."
msgstr ""

#. module: hr
#: model:ir.model.constraint,message:hr.constraint_hr_job_name_company_uniq
msgid "The name of the job position must be unique per department in company!"
msgstr ""
"A munkakör nevének egyedinek kell lennie részlegenként egy vállalkozáson "
"belül."

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "The user of %s's coach is not set."
msgstr ""

#. module: hr
#: model:res.groups,comment:hr.group_hr_user
msgid "The user will be able to approve document created by employees."
msgstr ""

#. module: hr
#: model:res.groups,comment:hr.group_hr_manager
msgid ""
"The user will have access to the human resources configuration as well as "
"statistic reports."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "The work permit of %(employee)s expires at %(date)s."
msgstr "%(employee)s munkavállalási engedélye lejár: %(date)s."

#. module: hr
#: model:hr.contract.type,name:hr.contract_type_thesis
msgid "Thesis"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "This employee already has an user."
msgstr "Ez a munkavállaló már felhasználóhoz van kötve."

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__tz
#: model:ir.model.fields,help:hr.field_hr_employee_base__tz
#: model:ir.model.fields,help:hr.field_hr_employee_public__tz
msgid ""
"This field is used in order to define in which timezone the employee will "
"work."
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/views/hr_graph_controller.xml:0
#: code:addons/hr/static/src/views/hr_pivot_controller.xml:0
msgid ""
"This report gives you an overview of your employees based on the measures of"
" your choice."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "This setting block is utilized to manage the frontend design."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_activity_plan_template.py:0
msgid "Those responsible types are limited to Employee plans."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__tz
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__tz
msgid "Timezone"
msgstr "Időzóna"

#. module: hr
#: model:digest.tip,name:hr.digest_tip_hr_0
#: model_terms:digest.tip,tip_description:hr.digest_tip_hr_0
msgid "Tip: Where's Bryan?"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid ""
"To avoid multi company issues (losing the access to your previous contracts,"
" leaves, ...), you should create another employee in the new company "
"instead."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
msgid "Today Activities"
msgstr "Mai tevékenységek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_job__expected_employees
msgid "Total Forecasted Employees"
msgstr "Összes előrejelzett munkavállaló"

#. module: hr
#: model:hr.job,name:hr.job_trainee
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__trainee
msgid "Trainee"
msgstr "Gyakornok"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Trainings"
msgstr "Képzések"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__activity_exception_decoration
#: model:ir.model.fields,help:hr.field_hr_employee__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kivétel tevékenység típusa a rekordon."

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_department_view_kanban
msgid "Unarchive"
msgstr "Visszatöltés"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_base__hr_icon_display__presence_undetermined
#: model:ir.model.fields.selection,name:hr.selection__hr_employee_public__hr_icon_display__presence_undetermined
msgid "Undetermined"
msgstr "Meghatározatlan"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_filter
#: model_terms:ir.ui.view,arch_db:hr.view_employee_filter
#: model_terms:ir.ui.view,arch_db:hr.view_job_filter
msgid "Unread Messages"
msgstr "Olvasatlan üzenetek"

#. module: hr
#: model_terms:ir.actions.act_window,help:hr.open_view_categ_form
msgid "Use tags to categorize your Employees."
msgstr ""

#. module: hr
#: model:ir.model,name:hr.model_res_users
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__user_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_id
#: model:ir.model.fields,field_description:hr.field_resource_resource__user_id
msgid "User"
msgstr "Felhasználó"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__user_partner_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__user_partner_id
msgid "User's partner"
msgstr "Felhasználó partnere"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_job_view_kanban
msgid "Vacancies:"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_res_company__hr_presence_control_ip_list
#: model:ir.model.fields,field_description:hr.field_res_config_settings__hr_presence_control_ip_list
msgid "Valid IP addresses"
msgstr "Valós IP címek"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "Valid work permit for Belgium"
msgstr "Érvényes munkavállalási engedély Belgiumban"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_expire
#: model:ir.model.fields,field_description:hr.field_res_users__visa_expire
msgid "Visa Expiration Date"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__visa_no
#: model:ir.model.fields,field_description:hr.field_res_users__visa_no
msgid "Visa No"
msgstr "Vízum száma"

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Warning"
msgstr "Figyelmeztetés"

#. module: hr
#: model_terms:hr.job,description:hr.job_consultant
msgid ""
"We are currently looking for someone like that to join our Consultant team."
msgstr ""

#. module: hr
#: model_terms:hr.job,description:hr.job_developer
msgid ""
"We are currently looking for someone like that to join our Web team.\n"
"                Someone who can snap out of coding and perform analysis or meet clients to explain the technical possibilities that can meet their needs."
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_department__website_message_ids
#: model:ir.model.fields,field_description:hr.field_hr_employee__website_message_ids
#: model:ir.model.fields,field_description:hr.field_hr_job__website_message_ids
msgid "Website Messages"
msgstr "Weboldal üzenetek"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_department__website_message_ids
#: model:ir.model.fields,help:hr.field_hr_employee__website_message_ids
#: model:ir.model.fields,help:hr.field_hr_job__website_message_ids
msgid "Website communication history"
msgstr "Weboldal kommunikációs előzmények"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What We Offer"
msgstr "Amit ajánlunk"

#. module: hr
#: model_terms:hr.job,website_description:hr.job_ceo
#: model_terms:hr.job,website_description:hr.job_consultant
#: model_terms:hr.job,website_description:hr.job_cto
#: model_terms:hr.job,website_description:hr.job_developer
#: model_terms:hr.job,website_description:hr.job_hrm
#: model_terms:hr.job,website_description:hr.job_marketing
#: model_terms:hr.job,website_description:hr.job_trainee
msgid "What's great in the job?"
msgstr "Miért nagyszerű ez a munka?"

#. module: hr
#: model:ir.model.fields,help:hr.field_hr_employee__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_base__member_of_department
#: model:ir.model.fields,help:hr.field_hr_employee_public__member_of_department
msgid ""
"Whether the employee is a member of the active user's department or one of "
"it's child department."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "Widower"
msgstr "Özvegy"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__address_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__address_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__address_id
#: model:ir.model.fields,field_description:hr.field_res_users__address_id
msgid "Work Address"
msgstr "Munkahely címe"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_contact_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_contact_id
#: model:ir.model.fields,field_description:hr.field_res_users__work_contact_id
msgid "Work Contact"
msgstr "Munkahelyi kapcsolat"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_email
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_email
#: model:ir.model.fields,field_description:hr.field_res_users__work_email
#: model:ir.model.fields,field_description:hr.field_resource_resource__work_email
msgid "Work Email"
msgstr "Munkahelyi e-mail"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Information"
msgstr "Munkahelyi adatok"

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/components/avatar_card/avatar_card_popover_patch.xml:0
#: model:ir.model,name:hr.model_hr_work_location
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_id
#: model:ir.model.fields,field_description:hr.field_hr_work_location__name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_id
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_form_view
#: model_terms:ir.ui.view,arch_db:hr.hr_work_location_tree_view
msgid "Work Location"
msgstr "Munkahely"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_name
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_name
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_name
msgid "Work Location Name"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_location_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_location_type
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_location_type
#: model:ir.model.fields,field_description:hr.field_res_users__work_location_type
msgid "Work Location Type"
msgstr ""

#. module: hr
#: model:ir.actions.act_window,name:hr.hr_work_location_action
#: model:ir.ui.menu,name:hr.menu_hr_work_location_tree
msgid "Work Locations"
msgstr "Munkahelyek"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__mobile_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__mobile_phone
#: model:ir.model.fields,field_description:hr.field_res_users__mobile_phone
msgid "Work Mobile"
msgstr "Munkahelyi mobil"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_config_settings_view_form
msgid "Work Organization"
msgstr "Munkahelyi szervezet"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__has_work_permit
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "Work Permit"
msgstr "Munkavállalási engedély"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_expiration_date
msgid "Work Permit Expiration Date"
msgstr "Munkavállalási engedély lejárati dátuma"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__permit_no
#: model:ir.model.fields,field_description:hr.field_res_users__permit_no
msgid "Work Permit No"
msgstr "Munkavállalási engedély száma"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_scheduled_activity
msgid "Work Permit Scheduled Activity"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_base__work_phone
#: model:ir.model.fields,field_description:hr.field_hr_employee_public__work_phone
#: model:ir.model.fields,field_description:hr.field_res_users__work_phone
#: model:ir.model.fields,field_description:hr.field_resource_resource__work_phone
msgid "Work Phone"
msgstr "Munkahelyi telefon"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__employee_type__worker
msgid "Worker"
msgstr ""

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__resource_calendar_id
msgid "Working Hours"
msgstr "Munkaórák"

#. module: hr
#: model:ir.ui.menu,name:hr.menu_resource_calendar_view
msgid "Working Schedules"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid ""
"You are not allowed to create an employee because the user does not have "
"access rights for %s"
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/res_users.py:0
msgid ""
"You are only allowed to update your preferences. Please contact a HR officer"
" to update other information."
msgstr ""

#. module: hr
#. odoo-javascript
#: code:addons/hr/static/src/store_service_patch.js:0
msgid "You can only chat with employees that have a dedicated user."
msgstr ""

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_department.py:0
msgid "You cannot create recursive departments."
msgstr "Rekurzív részlegek nem hozhatóak létre."

#. module: hr
#. odoo-python
#: code:addons/hr/models/hr_employee.py:0
msgid "You do not have access to this document."
msgstr "Nincs hozzáférése ehhez a dokumentumhoz."

#. module: hr
#: model_terms:hr.job,description:hr.job_trainee
msgid ""
"You participate to the update of our tutorial tools and pre-sales tools after the launch of a new version of Odoo. Indeed, any new version of the software brings significant improvements in terms of functionalities, ergonomics and configuration.\n"
"You will have to become familiar with the existing tools (books, class supports, Odoo presentation’s slides, commercial tools),\n"
"to participate to the update of those tools in order to make them appropriate for the new version of the software and, for sure,\n"
"to suggest improvements in order to cover the new domains of the software.\n"
"You join the Implementation Assistance department. This team of 3 people go with Odoo’s clients in the set up of the software. Your role will be\n"
"to animate webinars in order to show the different functionalities of the software.\n"
"to be involved in the support of the customers and\n"
"to answer to their questions.\n"
"You help the support manager to set up new support services by\n"
"being involved in the treatment of new cases,\n"
"contributing to the set up of a new politic,\n"
"being involved into satisfaction surveys in order to have a better knowledge of how the support given is seen by the customers."
msgstr ""

#. module: hr
#: model_terms:hr.job,description:hr.job_cto
msgid ""
"You will take part in the consulting services we provide to our partners and customers: design, analysis, development, testing, project management, support/coaching. You will work autonomously as well as coordinate and supervise small distributed development teams for some projects. Optionally, you will deliver Odoo training sessions to partners and customers (8-10 people/session). You will report to the Head of Professional Services and work closely with all developers and consultants.\n"
"\n"
"The job is located in Grand-Rosière (1367), Belgium (between Louvain-La-Neuve and Namur)."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.res_users_view_form_profile
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "ZIP"
msgstr "Irányítószám"

#. module: hr
#. odoo-python
#: code:addons/hr/models/mail_alias.py:0
msgid "addresses linked to registered employees"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_department_form
msgid "department"
msgstr "részleg"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. Building 2, Remote, etc."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.hr_employee_public_view_form
msgid "e.g. John Doe"
msgstr "pld: Gipsz Jakab"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Sales Manager"
msgstr "pl.: Értékesítési menedzser"

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_hr_job_form
msgid "e.g. Summarize the position in one or two lines..."
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: hr
#: model_terms:ir.ui.view,arch_db:hr.view_employee_form
msgid "e.g. <EMAIL>"
msgstr ""

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__distance_home_work_unit__kilometers
msgid "km"
msgstr "km"

#. module: hr
#: model:ir.model.fields.selection,name:hr.selection__hr_employee__distance_home_work_unit__miles
msgid "mi"
msgstr "mi"

#. module: hr
#. odoo-python
#: code:addons/hr/models/models.py:0
msgid "restricted to employees"
msgstr "korlátozva a munkavállalókra"

#. module: hr
#: model:ir.model.fields,field_description:hr.field_hr_employee__work_permit_name
msgid "work_permit_name"
msgstr ""
