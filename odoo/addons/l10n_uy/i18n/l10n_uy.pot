# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_uy
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~17.1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-21 13:32+0000\n"
"PO-Revision-Date: 2024-08-21 13:32+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/res_partner.py:0
msgid "3:402.010-2 or 93:402.010-1 (CI or NIE)"
msgstr ""

#. module: l10n_uy
#: model:ir.model,name:l10n_uy.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_uy
#: model:account.report.column,name:l10n_uy.tax_report_balance
msgid "Balance"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb_cmprs_0
msgid "Base Purchases 0%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb_cmprs_10
msgid "Base Purchases 10%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb_cmprs_22
msgid "Base Purchases 22%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb_vnts_0
msgid "Base Sales 0%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb_vnts_10
msgid "Base Sales 10%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb_vnts_22
msgid "Base Sales 22%"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_boleta_venta_contado
msgid "Boleta"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,name:l10n_uy.it_ci
msgid "CI"
msgstr ""

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/res_partner.py:0
msgid "CI/NIE"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_recibo_cobranza
msgid "Collection Receipt"
msgstr ""

#. module: l10n_uy
#: model:ir.model,name:l10n_uy.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_uy
#: model:ir.model,name:l10n_uy.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_remito_de_exportación_contingencia
msgid "Contingency Export e-Delivery Guide"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_factura_exportación_contingencia
msgid "Contingency Export e-Invoice"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_crédito_de_e_factura_exportación_contingencia
msgid "Contingency Export e-Invoice Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_débito_de_e_factura_exportación_contingencia
msgid "Contingency Export e-Invoice Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_boleta_contingencia
msgid "Contingency e-Boleta"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_credito_e_boleta_contingencia
msgid "Contingency e-Boleta Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_debito_e_boleta_contingencia
msgid "Contingency e-Boleta Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_remito_contingencia
msgid "Contingency e-Delivery Guide"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_factura_contingencia
msgid "Contingency e-Invoice"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_crédito_de_e_factura_contingencia
msgid "Contingency e-Invoice Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_débito_de_e_factura_contingencia
msgid "Contingency e-Invoice Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_factura_venta_por_cuenta_ajena_contingencia
msgid "Contingency e-Invoice Sale By Third Party"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_crédito_de_e_factura_venta_por_cuenta_ajena_contingencia
msgid "Contingency e-Invoice Sale By Third Party Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_débito_de_e_factura_venta_por_cuenta_ajena_contingencia
msgid "Contingency e-Invoice Sale By Third Party Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_resguardo_contingencia
msgid "Contingency e-Resguardo"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_ticket_cont
msgid "Contingency e-Ticket"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_cn_e_ticket_cont
msgid "Contingency e-Ticket Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_dn_e_ticket_cont
msgid "Contingency e-Ticket Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_ticket_venta_por_cuenta_ajena_contingencia
msgid "Contingency e-Ticket Sale By Third Party"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_crédito_de_e_ticket_venta_por_cuenta_ajena_contingencia
msgid "Contingency e-Ticket Sale By Third Party Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_débito_de_e_ticket_venta_por_cuenta_ajena_contingencia
msgid "Contingency e-Ticket Sale By Third Party Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_cn_inv
msgid "Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_credito_e_boleta
msgid "Credit Note e-Boleta"
msgstr ""

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/template_uy.py:0
msgid "Customer Invoices"
msgstr ""

#. module: l10n_uy
#: model:ir.model.fields,field_description:l10n_uy.field_l10n_latam_identification_type__l10n_uy_dgi_code
msgid "DGI Code"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,name:l10n_uy.it_dni
msgid "DNI"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_dn_inv
msgid "Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_debito_e_boleta
msgid "Debit Note e-Boleta"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_remito
msgid "Delivery Guide"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nc_expo
msgid "Export Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nd_expo
msgid "Export Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_inv_expo
msgid "Export Invoice"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_remito_expo
msgid "Export e-Delivery Guide"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_inv_exp
msgid "Export e-Invoice"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_cn_e_inv_exp
msgid "Export e-Invoice Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_dn_e_inv_exp
msgid "Export e-Invoice Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,description:l10n_uy.it_nife
msgid "Foreign tax identification number"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,description:l10n_uy.it_nie
msgid "Foreigner Identity Number"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,description:l10n_uy.it_ci
msgid "Identification Card"
msgstr ""

#. module: l10n_uy
#: model:ir.model,name:l10n_uy.model_l10n_latam_identification_type
msgid "Identification Types"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_inv
msgid "Invoice"
msgstr ""

#. module: l10n_uy
#: model:ir.model,name:l10n_uy.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_uy
#: model:ir.model,name:l10n_uy.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,name:l10n_uy.it_nie
msgid "NIE"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,name:l10n_uy.it_nife
msgid "NIFE"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,description:l10n_uy.it_dni
msgid "National identity document of Argentina, Brazil, Chile or Paraguay"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,name:l10n_uy.it_other
msgid "OTR"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,description:l10n_uy.it_other
msgid "Others"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,name:l10n_uy.it_pass
msgid "PAS"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,description:l10n_uy.it_pass
msgid "Passport (all countries)"
msgstr ""

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/l10n_latam_document_type.py:0
msgid ""
"Please introduce a valid Document number: 2 letters and 7 digits (*********)"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_cmprs_exnto_iva
msgid "Purchases Exempt from VAT"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,name:l10n_uy.it_rut
msgid "RUT / RUC"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_iva_vnts_10
msgid "Sales VAT 10%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_iva_vnts_22
msgid "Sales VAT 22%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_vnts_iva
msgid "Sales VAT exempt"
msgstr ""

#. module: l10n_uy
#: model:ir.model,name:l10n_uy.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb_cmprs
#: model:account.report.line,name:l10n_uy.account_tax_report_impb_cmprs
msgid "Tax Base Purchases"
msgstr ""

#. module: l10n_uy
#: model:ir.model.fields,field_description:l10n_uy.field_account_tax__l10n_uy_tax_category
msgid "Tax Category"
msgstr ""

#. module: l10n_uy
#: model:account.report,name:l10n_uy.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb_vnts
#: model:account.report.line,name:l10n_uy.account_tax_report_impb_vnts
msgid "Taxable Sales Base"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_base_impb
msgid "Taxable income"
msgstr ""

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] does not seem to be valid.\n"
"Note: the expected format is %(expected_format)s"
msgstr ""

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/res_partner.py:0
msgid ""
"The %(vat_label)s number [%(wrong_vat)s] for %(partner_label)s does not seem to be valid.\n"
"Note: the expected format is %(expected_format)s"
msgstr ""

#. module: l10n_uy
#: model:ir.model.fields,help:l10n_uy.field_account_tax__l10n_uy_tax_category
msgid ""
"UY: Use to group the transactions in the Financial Reports required by DGI"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.identification.type,description:l10n_uy.it_rut
msgid "Unique Tax Registry / Unique Taxpayer Registry"
msgstr ""

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/template_uy.py:0
msgid "Uruguayan Generic Chart of Accounts"
msgstr ""

#. module: l10n_uy
#: model:ir.model.fields.selection,name:l10n_uy.selection__account_tax__l10n_uy_tax_category__vat
msgid "VAT"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_cmprs_pagdo
#: model:account.report.line,name:l10n_uy.account_tax_report_iva_cmprs_pagdo
msgid "VAT Purchases - paid"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_iva_cmprs_10
msgid "VAT Purchases 10%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_iva_cmprs_22
msgid "VAT Purchases 22%"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_iva_vnts_prcbdo
#: model:account.report.line,name:l10n_uy.account_tax_report_vnts_prcbdo
msgid "VAT Sales - received"
msgstr ""

#. module: l10n_uy
#: model:account.report.line,name:l10n_uy.account_tax_report_sldo_iva
msgid "VAT balance"
msgstr ""

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/template_uy.py:0
msgid "Vendor Bills"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_boleta
msgid "e-Boleta"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_remito
msgid "e-Delivery Guide"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_inv
msgid "e-Invoice"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_cn_e_inv
msgid "e-Invoice Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_dn_e_inv
msgid "e-Invoice Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_factura_venta_por_cuenta_ajena
msgid "e-Invoice Sale By Third Party"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_credito_e_factura_venta_por_cuenta_ajena
msgid "e-Invoice Sale By Third Party Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_debito_e_factura_venta_por_cuenta_ajena
msgid "e-Invoice Sale By Third Party Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_resguardo
msgid "e-Resguardo"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_ticket
msgid "e-Ticket"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_cn_e_ticket
msgid "e-Ticket Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_dn_e_ticket
msgid "e-Ticket Debit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_e_ticket_venta_por_cuenta_ajena
msgid "e-Ticket Sale By Third Party"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_credito_e_ticket_venta_por_cuenta_ajena
msgid "e-Ticket Sale By Third Party Credit Note"
msgstr ""

#. module: l10n_uy
#: model:l10n_latam.document.type,name:l10n_uy.dc_nota_de_debito_e_ticket_venta_por_cuenta_ajena
msgid "e-Ticket Sale By Third Party Debit Note"
msgstr ""

#. module: l10n_uy
#. odoo-python
#: code:addons/l10n_uy/models/res_partner.py:0
msgid "partner [%s]"
msgstr ""
