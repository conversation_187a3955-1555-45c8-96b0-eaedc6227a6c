<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_uy" model="res.partner" forcecreate="1">
        <field name="name">UY Company</field>
        <field name='l10n_latam_identification_type_id' ref='it_rut'/>
        <field name="vat">218296790015</field>
        <field name='state_id' ref="base.state_uy_10"/>
        <field name="street">Calle Falsa 123</field>
        <field name="city">Montevideo</field>
        <field name="country_id" ref="base.uy"/>
        <field name="zip">12800</field>
        <field name="phone">+598 94 231 234</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.uyexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_uy" model="res.company" forcecreate="1">
        <field name="name">UY Company</field>
        <field name="partner_id" ref="base.partner_demo_company_uy"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_uy')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_uy'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>uy</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_uy')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
