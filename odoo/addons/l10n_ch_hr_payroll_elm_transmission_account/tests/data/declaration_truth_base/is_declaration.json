{"is_declaration_2021_11": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_LU", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2021-11-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Seestrasse 5", "Country": "SWITZERLAND", "ZIP-Code": "6353"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "separated", "ValidAsOf": "2017-04-28"}, "DateOfBirth": "1977-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "14", "MunicipalityID": "1069", "ResidenceCanton": "LU", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1927.3247.52"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "LU"}, "TaxAtSource": "44.05", "TaxableEarning": "2281.65", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2021-11-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "2281.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2021, 11, false], "institutionIDRef": "#QST_LU", "TaxAtSourceCanton": "LU", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "1069"}]}}, {"Work": {"EntryDate": "2021-11-16", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Hopfenweg 22", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "divorced", "ValidAsOf": "2020-06-20"}, "DateOfBirth": "1990-10-15", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "37", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "shortTerm-L", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6462.6899.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "641.20", "TaxableEarning": "5230.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2021-11-16"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "H1N"}, "AscertainedTaxableEarning": "10460.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2021, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2030-06-30", "Start": "2012-07-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2012-06-18"}], "Denomination": "jewishCommunity", "SingleParentFamily": {"Concubinage": {"SoleCustody": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "LU", "institutionID": "#QST_LU", "CustomerIdentity": "158.87.6"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2021, 11, false], "TotalCommission": "0.00", "TotalTaxAtSource": "44.05", "TotalTaxableEarning": "2281.65"}, "institutionIDRef": "#QST_LU"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2021, 11, false], "TotalCommission": "0.00", "TotalTaxAtSource": "641.20", "TotalTaxableEarning": "5230.00"}, "institutionIDRef": "#QST_BE"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 2}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2021, false]}}}, "is_declaration_2021_12": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_LU", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2021-11-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Seestrasse 5", "Country": "SWITZERLAND", "ZIP-Code": "6353"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "separated", "ValidAsOf": "2017-04-28"}, "DateOfBirth": "1977-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "14", "MunicipalityID": "1069", "ResidenceCanton": "LU", "ResidenceCategory": "settled-C", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1927.3247.52"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "LU"}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "DeclarationCategory": {"Withdrawal": [{"Reason": "settled-C", "ValidAsOf": "2021-11-01"}]}, "TaxAtSourceCategory": {"CategoryPredefined": "NON"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2021, 12, false], "institutionIDRef": "#QST_LU", "TaxAtSourceCanton": "LU", "CorrectionConfirmed": [{"Month": ["XSDGYEARMONTH", 2021, 11, false], "TaxAtSource": "-44.05", "TaxableEarning": "-2281.65"}], "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "1069"}]}}, {"Work": {"EntryDate": "2021-12-21", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2021-12-10"}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Hopfenweg 22", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "divorced", "ValidAsOf": "2020-06-20"}, "DateOfBirth": "1990-10-15", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "37", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "shortTerm-L", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6462.6899.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "756.75", "TaxableEarning": "6460.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2021-12-21"}], "Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2021-12-10"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "H1N"}, "AscertainedTaxableEarning": "9690.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2021, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2030-06-30", "Start": "2012-07-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2012-06-18"}], "Denomination": "jewishCommunity", "SingleParentFamily": {"Concubinage": {"SoleCustody": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "LU", "institutionID": "#QST_LU", "CustomerIdentity": "158.87.6"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2021, 12, false], "TotalCommission": "0.00", "TotalTaxAtSource": "0.00", "TotalTaxableEarning": "0.00"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2021, 11, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-44.05", "TotalTaxableEarning": "-2281.65"}], "institutionIDRef": "#QST_LU"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2021, 12, false], "TotalCommission": "0.00", "TotalTaxAtSource": "756.75", "TotalTaxableEarning": "6460.00"}, "institutionIDRef": "#QST_BE"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 2}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2021, false]}}}, "is_declaration_2022_01": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2021-12-21", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-01-18"}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Hopfenweg 22", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "divorced", "ValidAsOf": "2020-06-20"}, "DateOfBirth": "1990-10-15", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "37", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "shortTerm-L", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6462.6899.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "467.55", "TaxableEarning": "5230.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "2000.00", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-01-18"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "H1N"}, "AscertainedTaxableEarning": "7383.35"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2030-06-30", "Start": "2012-07-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2012-06-18"}], "Denomination": "jewishCommunity", "SingleParentFamily": {"Concubinage": {"SoleCustody": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "79.25", "TaxableEarning": "1179.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "4859.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "226.20", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4680.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "OtherActivities": {"TotalActivityRate": "40.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "16.00", "ActivityRate": "40.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Brünnenstrasse 66", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1993-06-17"}, "DateOfBirth": "1993-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "20", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "NotificationProcedureForShorttermWork90Days", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1859.2584.53"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "193.20", "TaxableEarning": "2000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "50.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "16.00", "ActivityRate": "40.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Campagna 5", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-01-01"}, "DateOfBirth": "1972-01-01", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "21", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "NotificationProcedureForShorttermWork120Days", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "164.00", "TaxableEarning": "2000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "50.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-06-06"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "495.75", "TaxableEarning": "5111.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5111.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1989-01-10"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "470.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "744.00", "TaxableEarning": "8000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "660.15", "TaxableEarning": "4500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "571.50", "TaxableEarning": "4500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Lilienstrasse 22", "Country": "GERMANY", "ZIP-Code": "81669"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "1622.50", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"CategoryPredefined": "MEN"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "single", "ValidAsOf": "1992-06-06"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "553.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-04-11"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "470.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "divorced", "ValidAsOf": "2018-06-15"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "470.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-06-17"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "158.70", "TaxableEarning": "3000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1996-06-17"}, "DateOfBirth": "1996-06-17", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "40", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3438.2653.71"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "221.65", "TaxableEarning": "4230.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A1N"}, "AscertainedTaxableEarning": "4230.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2038-05-31", "Start": "2020-06-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2020-05-04"}], "Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Mel<PERSON>", "Street": "Via Cantonale 31", "Country": "SWITZERLAND", "ZIP-Code": "6815"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Max", "CivilStatus": {"Status": "single", "ValidAsOf": "1990-02-22"}, "DateOfBirth": "1990-02-22", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "41", "MunicipalityID": "5198", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3572.1419.82"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1670.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-01-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5198"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "TotalCommission": "0.00", "TotalTaxAtSource": "4466.00", "TotalTaxableEarning": "35639.45"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "TotalCommission": "0.00", "TotalTaxAtSource": "5836.55", "TotalTaxableEarning": "51761.00"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 1, false], "TotalCommission": "0.00", "TotalTaxAtSource": "553.50", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 21}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_02": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_LU", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "79.25", "TaxableEarning": "1179.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "4859.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "226.20", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4680.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "OtherActivities": {"TotalActivityRate": "40.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "16.00", "ActivityRate": "40.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Brünnenstrasse 66", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1993-06-17"}, "DateOfBirth": "1993-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "20", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "NotificationProcedureForShorttermWork90Days", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1859.2584.53"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "193.20", "TaxableEarning": "2000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "50.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "16.00", "ActivityRate": "40.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Campagna 5", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-01-01"}, "DateOfBirth": "1972-01-01", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "21", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "NotificationProcedureForShorttermWork120Days", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "164.00", "TaxableEarning": "2000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "50.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-06-06"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "408.50", "TaxableEarning": "4717.85", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4914.45"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1989-01-10"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "5010.00", "TaxableEarning": "35000.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "30000.00", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "744.00", "TaxableEarning": "8000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "4470.20", "TaxableEarning": "15500.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "20000.00", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "28000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "2348.50", "TaxableEarning": "15500.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "20000.00", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Lilienstrasse 22", "Country": "GERMANY", "ZIP-Code": "81669"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "1622.50", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "MEN"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "single", "ValidAsOf": "1992-06-06"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "553.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-04-11"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "470.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "divorced", "ValidAsOf": "2018-06-15"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "5010.00", "TaxableEarning": "35000.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "30000.00", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-06-17"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "10766.00", "TaxableEarning": "35000.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "30000.00", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "35000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "158.70", "TaxableEarning": "3000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1996-06-17"}, "DateOfBirth": "1996-06-17", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "40", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3438.2653.71"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "6463.65", "TaxableEarning": "24530.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "20300.00", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A1N"}, "AscertainedTaxableEarning": "24530.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2038-05-31", "Start": "2020-06-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2020-05-04"}], "Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Mel<PERSON>", "Street": "Via Cantonale 31", "Country": "SWITZERLAND", "ZIP-Code": "6815"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Max", "CivilStatus": {"Status": "single", "ValidAsOf": "1990-02-22"}, "DateOfBirth": "1990-02-22", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "41", "MunicipalityID": "5198", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3572.1419.82"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "5730.00", "TaxableEarning": "30000.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "20000.00", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "11666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5198"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "42.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Milano", "Street": "Via Pisanello 2", "Country": "ITALY", "ZIP-Code": "20146"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Nadine", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-07-28"}, "DateOfBirth": "1997-07-28", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "25", "ResidenceCanton": "EX", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3558.3266.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "846.30", "TaxableEarning": "5714.30", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-02-10"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "11428.55"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_LU", "TaxAtSourceCanton": "LU", "AdditionalParticulars": {"Denomination": "christianCatholic"}, "TaxAtSourceMunicipalityID": "1061"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Milano", "Street": "viale misurata 56", "Country": "ITALY", "ZIP-Code": "20146"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-01-01"}, "DateOfBirth": "1972-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "26", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6408.6518.22"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "931.45", "TaxableEarning": "5714.30", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-02-10"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "11428.55"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "LU", "institutionID": "#QST_LU", "CustomerIdentity": "158.87.6"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "TotalCommission": "0.00", "TotalTaxAtSource": "21597.75", "TotalTaxableEarning": "148082.15"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "TotalCommission": "0.00", "TotalTaxAtSource": "24285.00", "TotalTaxableEarning": "91709.45"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "TotalCommission": "0.00", "TotalTaxAtSource": "553.50", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 2, false], "TotalCommission": "0.00", "TotalTaxAtSource": "846.30", "TotalTaxableEarning": "5714.30"}, "institutionIDRef": "#QST_LU"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 22}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_03": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_LU", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "79.25", "TaxableEarning": "1179.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "4859.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "226.20", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4680.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "OtherActivities": {"TotalActivityRate": "40.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "16.00", "ActivityRate": "40.00"}}, "WithdrawalDate": "2022-03-15"}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Brünnenstrasse 66", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1993-06-17"}, "DateOfBirth": "1993-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "20", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "NotificationProcedureForShorttermWork90Days", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1859.2584.53"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "255.70", "TaxableEarning": "1916.65", "workplaceIDRef": "#W_*********", "SporadicBenefits": "500.00", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-03-15"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6874.90"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "50.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "16.00", "ActivityRate": "40.00"}}, "WithdrawalDate": "2022-03-15"}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Campagna 5", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-01-01"}, "DateOfBirth": "1972-01-01", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "21", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "NotificationProcedureForShorttermWork120Days", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "216.35", "TaxableEarning": "1916.65", "workplaceIDRef": "#W_*********", "SporadicBenefits": "500.00", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-03-15"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4916.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "50.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-06-06"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "452.15", "TaxableEarning": "4914.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4914.45"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1989-01-10"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "685.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "744.00", "TaxableEarning": "8000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "484.10", "TaxableEarning": "3300.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "481.80", "TaxableEarning": "3300.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Lilienstrasse 22", "Country": "GERMANY", "ZIP-Code": "81669"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "1622.50", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "MEN"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "single", "ValidAsOf": "1992-06-06"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "553.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-04-11"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "470.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "divorced", "ValidAsOf": "2018-06-15"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "685.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-06-17"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "1173.60", "TaxableEarning": "8000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}, "WithdrawalDate": "2022-03-31"}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1996-06-17"}, "DateOfBirth": "1996-06-17", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "40", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3438.2653.71"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "221.65", "TaxableEarning": "4230.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "4000.00", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-03-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A1N"}, "AscertainedTaxableEarning": "4230.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2038-05-31", "Start": "2020-06-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2020-05-04"}], "Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-03-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Mel<PERSON>", "Street": "Via Cantonale 31", "Country": "SWITZERLAND", "ZIP-Code": "6815"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Max", "CivilStatus": {"Status": "single", "ValidAsOf": "1990-02-22"}, "DateOfBirth": "1990-02-22", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "41", "MunicipalityID": "5198", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3572.1419.82"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1850.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-03-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "11666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5198"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "42.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Milano", "Street": "Via Pisanello 2", "Country": "ITALY", "ZIP-Code": "20146"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Nadine", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-07-28"}, "DateOfBirth": "1997-07-28", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "25", "ResidenceCanton": "EX", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3558.3266.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "822.40", "TaxableEarning": "5400.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "12000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_LU", "TaxAtSourceCanton": "LU", "AdditionalParticulars": {"Denomination": "christianCatholic"}, "TaxAtSourceMunicipalityID": "1061"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Milano", "Street": "viale misurata 56", "Country": "ITALY", "ZIP-Code": "20146"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-01-01"}, "DateOfBirth": "1972-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "26", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6408.6518.22"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "924.65", "TaxableEarning": "5400.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "11764.70"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "LU", "institutionID": "#QST_LU", "CustomerIdentity": "158.87.6"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "TotalCommission": "0.00", "TotalTaxAtSource": "7290.25", "TotalTaxableEarning": "55681.10"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "TotalCommission": "0.00", "TotalTaxAtSource": "4899.80", "TotalTaxableEarning": "34126.10"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "TotalCommission": "0.00", "TotalTaxAtSource": "553.50", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 3, false], "TotalCommission": "0.00", "TotalTaxAtSource": "822.40", "TotalTaxableEarning": "5400.00"}, "institutionIDRef": "#QST_LU"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 22}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_04": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_LU", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "79.25", "TaxableEarning": "1179.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "4859.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "101.40", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4160.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-06-06"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "548.45", "TaxableEarning": "5307.60", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5012.75"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1989-01-10"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "685.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "744.00", "TaxableEarning": "8000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "616.15", "TaxableEarning": "4200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "613.20", "TaxableEarning": "4200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Junkerngasse 42", "Country": "SWITZERLAND", "ZIP-Code": "3011"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "1622.50", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "MEN"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "single", "ValidAsOf": "1992-06-06"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "553.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-04-11"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "470.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "divorced", "ValidAsOf": "2018-06-15"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "685.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-06-17"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "158.70", "TaxableEarning": "3000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "2300.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "10000.00", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "42.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Malters", "Street": "Hauptstrasse 4", "Country": "SWITZERLAND", "ZIP-Code": "6102"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Nadine", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-07-28"}, "DateOfBirth": "1997-07-28", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "25", "MunicipalityID": "1062", "ResidenceCanton": "LU", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3558.3266.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "1644.85", "TaxableEarning": "10800.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "12000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_LU", "TaxAtSourceCanton": "LU", "AdditionalParticulars": {"Denomination": "christianCatholic"}, "TaxAtSourceMunicipalityID": "1061"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Milano", "Street": "viale misurata 56", "Country": "ITALY", "ZIP-Code": "20146"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-01-01"}, "DateOfBirth": "1972-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "26", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6408.6518.22"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "1825.50", "TaxableEarning": "10800.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "11851.85"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "LU", "institutionID": "#QST_LU", "CustomerIdentity": "158.87.6"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "TotalCommission": "0.00", "TotalTaxAtSource": "6227.65", "TotalTaxableEarning": "50457.60"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "TotalCommission": "0.00", "TotalTaxAtSource": "5839.60", "TotalTaxableEarning": "33879.45"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "TotalCommission": "0.00", "TotalTaxAtSource": "553.50", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 4, false], "TotalCommission": "0.00", "TotalTaxAtSource": "1644.85", "TotalTaxableEarning": "10800.00"}, "institutionIDRef": "#QST_LU"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 18}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_05": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_LU", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "79.25", "TaxableEarning": "1179.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "4859.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "78.00", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3848.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-06-06"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "355.80", "TaxableEarning": "4521.30", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4914.45"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-08"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "685.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "744.00", "TaxableEarning": "8000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "880.20", "TaxableEarning": "6000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "876.00", "TaxableEarning": "6000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Junkerngasse 42", "Country": "SWITZERLAND", "ZIP-Code": "3011"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "633.05", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Mutation": [{"Reason": "residence", "ValidAsOf": "2022-05-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "single", "ValidAsOf": "1992-06-06"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "553.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-04-11"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "470.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "685.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "158.70", "TaxableEarning": "3000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-03-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Mel<PERSON>", "Street": "Via Cantonale 31", "Country": "SWITZERLAND", "ZIP-Code": "6815"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Max", "CivilStatus": {"Status": "single", "ValidAsOf": "1990-02-22"}, "DateOfBirth": "1990-02-22", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "41", "MunicipalityID": "5198", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3572.1419.82"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "6240.00", "TaxableEarning": "30000.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "30000.00", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "14166.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5198"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "42.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Malters", "Street": "Hauptstrasse 4", "Country": "SWITZERLAND", "ZIP-Code": "6102"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Nadine", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-07-28"}, "DateOfBirth": "1997-07-28", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "25", "MunicipalityID": "1062", "ResidenceCanton": "LU", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3558.3266.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "LU"}, "TaxAtSource": "1827.60", "TaxableEarning": "12000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "DeclarationCategory": {"Mutation": [{"Reason": "residence", "ValidAsOf": "2022-05-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "12000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_LU", "TaxAtSourceCanton": "LU", "AdditionalParticulars": {"Denomination": "christianCatholic"}, "TaxAtSourceMunicipalityID": "1062"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Milano", "Street": "viale misurata 56", "Country": "ITALY", "ZIP-Code": "20146"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-01-01"}, "DateOfBirth": "1972-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "26", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6408.6518.22"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "11891.90"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-05-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "LU", "institutionID": "#QST_LU", "CustomerIdentity": "158.87.6"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "10688.90", "TotalTaxableEarning": "70671.30"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "3264.20", "TotalTaxableEarning": "35679.45"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "553.50", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "1827.60", "TotalTaxableEarning": "12000.00"}, "institutionIDRef": "#QST_LU"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 20}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_06": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_LU", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "79.25", "TaxableEarning": "1179.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "4859.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "78.00", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3640.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-06-06"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "247.70", "TaxableEarning": "3931.55", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "4750.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-08"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "425.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-06-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1928.1347.70"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "744.00", "TaxableEarning": "8000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "484.10", "TaxableEarning": "3300.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "481.80", "TaxableEarning": "3300.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Junkerngasse 42", "Country": "SWITZERLAND", "ZIP-Code": "3011"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "633.05", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "303.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Correction": [{"New": {"TaxAtSource": "303.00", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-04-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-553.50", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 5, false]}, {"New": {"TaxAtSource": "303.00", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-04-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-553.50", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 4, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lausanne", "Street": "Route de chavannes 11", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "VD"}, "DateOfBirth": "1990-03-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "345.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Correction": [{"New": {"TaxAtSource": "345.50", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-04-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-531.50", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 5, false]}, {"New": {"TaxAtSource": "345.50", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-04-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-531.50", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 4, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-12-15", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6328.7099.17"}}}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "205.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Correction": [{"New": {"TaxAtSource": "205.00", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-04-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-470.00", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 5, false]}, {"New": {"TaxAtSource": "205.00", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-04-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-470.00", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 4, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1971-12-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "425.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-06-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1969-06-12", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3454.9922.51"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "345.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-06-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1994-09-27", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "1558.95", "TaxableEarning": "9500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "9500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "42.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-06-15"}, "Particulars": {"Sex": "F", "Address": {"City": "Malters", "Street": "Hauptstrasse 4", "Country": "SWITZERLAND", "ZIP-Code": "6102"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Nadine", "CivilStatus": {"Status": "single", "ValidAsOf": "1997-07-28"}, "DateOfBirth": "1997-07-28", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "25", "MunicipalityID": "1062", "ResidenceCanton": "LU", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3558.3266.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "LU"}, "TaxAtSource": "1877.75", "TaxableEarning": "9323.40", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-06-15"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "20333.30"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_LU", "TaxAtSourceCanton": "LU", "AdditionalParticulars": {"Denomination": "christianCatholic"}, "TaxAtSourceMunicipalityID": "1062"}]}}, {"Work": {"EntryDate": "2022-02-10", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-06-15"}, "Particulars": {"Sex": "M", "Address": {"City": "Milano", "Street": "viale misurata 56", "Country": "ITALY", "ZIP-Code": "20146"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-01-01"}, "DateOfBirth": "1972-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "26", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6408.6518.22"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "1298.65", "TaxableEarning": "6382.55", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-06-15"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "12896.80"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "LU", "institutionID": "#QST_LU", "CustomerIdentity": "158.87.6"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "TotalCommission": "0.00", "TotalTaxAtSource": "4460.25", "TotalTaxableEarning": "43764.10"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2022, 4, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-265.00", "TotalTaxableEarning": "0.00"}, {"Month": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-265.00", "TotalTaxableEarning": "0.00"}], "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "TotalCommission": "0.00", "TotalTaxAtSource": "3896.35", "TotalTaxableEarning": "39479.45"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2022, 4, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-186.00", "TotalTaxableEarning": "0.00"}, {"Month": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-186.00", "TotalTaxableEarning": "0.00"}], "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "TotalCommission": "0.00", "TotalTaxAtSource": "303.00", "TotalTaxableEarning": "5000.00"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2022, 4, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-250.50", "TotalTaxableEarning": "0.00"}, {"Month": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-250.50", "TotalTaxableEarning": "0.00"}], "institutionIDRef": "#QST_VD"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 6, false], "TotalCommission": "0.00", "TotalTaxAtSource": "1877.75", "TotalTaxableEarning": "9323.40"}, "institutionIDRef": "#QST_LU"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 19}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_07": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "79.25", "TaxableEarning": "1179.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "4859.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "65.00", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3491.45"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-06-26"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "564.35", "TaxableEarning": "5504.15", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Mutation": [{"Reason": "partnerWork", "ValidAsOf": "2022-07-01"}, {"Reason": "civilstate", "ValidAsOf": "2022-07-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "4858.25"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1995-03-15", "WorkOrCompensatory": {"Start": "2022-06-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-08"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "425.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1928.1347.70"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1377.00", "TaxableEarning": "11000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "8428.55"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "352.10", "TaxableEarning": "2400.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "350.40", "TaxableEarning": "2400.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Junkerngasse 42", "Country": "SWITZERLAND", "ZIP-Code": "3011"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "633.05", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "303.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lausanne", "Street": "Route de chavannes 11", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "VD"}, "DateOfBirth": "1990-03-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "348.10", "TaxableEarning": "5920.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "690.00", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5920.00"}, "Correction": [{"New": {"TaxAtSource": "187.00", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "childrenDeduction", "ValidAsOf": "2022-05-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-345.50", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 6, false]}, {"New": {"TaxAtSource": "187.00", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "childrenDeduction", "ValidAsOf": "2022-05-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-345.50", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 5, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-12-15", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6328.7099.17"}}}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "166.00", "TaxableEarning": "5800.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "600.00", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5078.55"}, "Correction": [{"New": {"TaxAtSource": "95.00", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "childrenDeduction", "ValidAsOf": "2022-05-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-205.00", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 6, false]}, {"New": {"TaxAtSource": "95.00", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "childrenDeduction", "ValidAsOf": "2022-05-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-205.00", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 5, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1971-12-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "425.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1969-06-12", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3454.9922.51"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "345.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1994-09-27", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "158.70", "TaxableEarning": "3000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-07-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-07-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Mel<PERSON>", "Street": "Via Cantonale 31", "Country": "SWITZERLAND", "ZIP-Code": "6815"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Max", "CivilStatus": {"Status": "single", "ValidAsOf": "1990-02-22"}, "DateOfBirth": "1990-02-22", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "41", "MunicipalityID": "5198", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3572.1419.82"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "3230.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-07-01"}], "Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-07-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "14166.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5198"}]}}, {"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "TotalCommission": "0.00", "TotalTaxAtSource": "7157.85", "TotalTaxableEarning": "51854.15"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-110.00", "TotalTaxableEarning": "0.00"}, {"Month": ["XSDGYEARMONTH", 2022, 6, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-110.00", "TotalTaxableEarning": "0.00"}], "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "TotalCommission": "0.00", "TotalTaxAtSource": "2366.70", "TotalTaxableEarning": "32999.45"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2022, 5, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-158.50", "TotalTaxableEarning": "0.00"}, {"Month": ["XSDGYEARMONTH", 2022, 6, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-158.50", "TotalTaxableEarning": "0.00"}], "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 7, false], "TotalCommission": "0.00", "TotalTaxAtSource": "303.00", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 18}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_08": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "79.25", "TaxableEarning": "1179.45", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "4859.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "59.80", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3380.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-06-26"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "354.85", "TaxableEarning": "4521.30", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "4816.15"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1995-03-15", "WorkOrCompensatory": {"Start": "2022-06-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-08"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "425.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1928.1347.70"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1439.00", "TaxableEarning": "11000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "8750.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "572.15", "TaxableEarning": "3900.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "569.40", "TaxableEarning": "3900.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Junkerngasse 42", "Country": "SWITZERLAND", "ZIP-Code": "3011"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "633.05", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "303.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lausanne", "Street": "Route de chavannes 11", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "VD"}, "DateOfBirth": "1990-03-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "227.50", "TaxableEarning": "5230.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5230.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-12-15", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6328.7099.17"}}}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "104.00", "TaxableEarning": "5200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5100.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1971-12-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "425.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Ospedale 10", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1969-06-12", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3454.9922.51"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "534.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Mutation": [{"Reason": "partnerWork", "ValidAsOf": "2022-08-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1994-09-27", "WorkOrCompensatory": {"Start": "2022-07-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "158.70", "TaxableEarning": "3000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "TotalCommission": "0.00", "TotalTaxAtSource": "3932.15", "TotalTaxableEarning": "41771.30"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "TotalCommission": "0.00", "TotalTaxAtSource": "2654.65", "TotalTaxableEarning": "33809.45"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 8, false], "TotalCommission": "0.00", "TotalTaxAtSource": "303.00", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 17}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_09": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "298.10", "TaxableEarning": "2948.65", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7241.75"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "85.80", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3293.35"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-06-26"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "439.55", "TaxableEarning": "5111.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "4848.90"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1995-03-15", "WorkOrCompensatory": {"Start": "2022-06-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-08"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "425.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1928.1347.70"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1468.00", "TaxableEarning": "11000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "9000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "880.20", "TaxableEarning": "6000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "876.00", "TaxableEarning": "6000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-09-30"}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Junkerngasse 42", "Country": "SWITZERLAND", "ZIP-Code": "3011"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "633.05", "TaxableEarning": "5500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-09-30"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "303.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lausanne", "Street": "Route de chavannes 11", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "VD"}, "DateOfBirth": "1990-03-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "227.50", "TaxableEarning": "5230.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5230.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-12-15", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6328.7099.17"}}}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "150.20", "TaxableEarning": "5200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5116.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1971-12-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "345.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "cantonChange", "ValidAsOf": "2022-09-01"}], "Mutation": [{"Reason": "residence", "ValidAsOf": "2022-09-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1969-06-12", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3454.9922.51"}}}, "TaxAtSourceMunicipalityID": "351"}, {"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Mutation": [{"Reason": "residence", "ValidAsOf": "2022-09-01"}], "Withdrawal": [{"Reason": "cantonChange", "ValidAsOf": "2022-08-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1969-06-12", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3454.9922.51"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Como", "Street": "Via Milano 26", "Country": "ITALY", "ZIP-Code": "22100"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "400.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "cantonChange", "ValidAsOf": "2022-09-01"}], "Mutation": [{"Reason": "residence", "ValidAsOf": "2022-09-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "T0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1994-09-27", "WorkOrCompensatory": {"Start": "2022-07-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5002"}, {"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Mutation": [{"Reason": "residence", "ValidAsOf": "2022-09-01"}], "Withdrawal": [{"Reason": "cantonChange", "ValidAsOf": "2022-08-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "T0N"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1994-09-27", "WorkOrCompensatory": {"Start": "2022-07-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Biel/Bienne", "Street": "Bahnhofplatz 1", "Country": "SWITZERLAND", "ZIP-Code": "2502"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "MunicipalityID": "371", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "901.00", "TaxableEarning": "6800.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6800.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-09-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Kehrgasse 8", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-10-04"}, "DateOfBirth": "1977-10-04", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "32", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3728.4917.63"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-09-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "TotalCommission": "0.00", "TotalTaxAtSource": "4399.65", "TotalTaxableEarning": "44461.00"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "TotalCommission": "0.00", "TotalTaxAtSource": "4266.85", "TotalTaxableEarning": "46478.65"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 9, false], "TotalCommission": "0.00", "TotalTaxAtSource": "303.00", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 20}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_10": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "555.10", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "232.05", "TaxableEarning": "2437.55", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "6730.25"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "75.40", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3224.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-06-26"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "191.25", "TaxableEarning": "3538.40", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "4717.85"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1995-03-15", "WorkOrCompensatory": {"Start": "2022-06-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-08"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "467.20", "TaxableEarning": "5200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7520.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1928.1347.70"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1871.00", "TaxableEarning": "11000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Mutation": [{"Reason": "partnerWork", "ValidAsOf": "2022-10-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "9200.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "WorkOrCompensatory": {"Start": "2022-09-20", "Workplace": "TI"}, "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "308.05", "TaxableEarning": "2100.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "306.60", "TaxableEarning": "2100.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "303.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lausanne", "Street": "Route de chavannes 11", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "VD"}, "DateOfBirth": "1990-03-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "227.50", "TaxableEarning": "5230.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5230.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-12-15", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6328.7099.17"}}}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "109.20", "TaxableEarning": "5200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5130.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1971-12-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "391.20", "TaxableEarning": "5230.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5230.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1969-06-12", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3454.9922.51"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Como", "Street": "Via Milano 26", "Country": "ITALY", "ZIP-Code": "22100"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "233.60", "TaxableEarning": "5200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Mutation": [{"Reason": "partnerWorkplaceChangeCHAbroad", "ValidAsOf": "2022-10-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "F0N"}, "AscertainedTaxableEarning": "5100.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Como", "Street": "Via Milano 26", "Country": "ITALY", "ZIP-Code": "22100"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1994-09-27", "WorkOrCompensatory": {"Start": "2022-07-28", "Workplace": "EX"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON>", "Street": "Grand Rue", "Country": "FRANCE", "ZIP-Code": "90100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "158.70", "TaxableEarning": "3000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "371"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-10-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-10-31"}, "Particulars": {"Sex": "F", "Address": {"City": "Vevey", "Street": "Rue des Moulins 13", "Country": "SWITZERLAND", "ZIP-Code": "1800"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1996-06-17"}, "DateOfBirth": "1996-06-17", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "40", "MunicipalityID": "5890", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3438.2653.71"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "198.25", "TaxableEarning": "4300.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-10-01"}], "Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-10-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A1N"}, "AscertainedTaxableEarning": "4300.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Children": [{"End": "2038-05-31", "Start": "2020-06-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2020-05-04"}], "Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5890"}]}}, {"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-09-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Kehrgasse 8", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-10-04"}, "DateOfBirth": "1977-10-04", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "32", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3728.4917.63"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "TotalCommission": "0.00", "TotalTaxAtSource": "3809.35", "TotalTaxableEarning": "39388.40"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "TotalCommission": "0.00", "TotalTaxAtSource": "2299.00", "TotalTaxableEarning": "32997.55"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 10, false], "TotalCommission": "0.00", "TotalTaxAtSource": "501.25", "TotalTaxableEarning": "9300.00"}, "institutionIDRef": "#QST_VD"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 18}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_11": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1007.30", "TaxableEarning": "6550.00", "workplaceIDRef": "#W_*********", "SporadicBenefits": "2000.00", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "189.85", "TaxableEarning": "2083.70", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "6377.05"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "93.60", "TaxableEarning": "2600.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3167.25"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-06-26"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "391.60", "TaxableEarning": "4717.85", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "4717.85"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1995-03-15", "WorkOrCompensatory": {"Start": "2022-06-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-08"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "291.20", "TaxableEarning": "5200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Mutation": [{"Reason": "childrenDeduction", "ValidAsOf": "2022-11-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "7536.35"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-10-31", "Start": "2022-11-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-10-09"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1928.1347.70"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Jan", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-25"}, "DateOfBirth": "1980-06-23", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "24", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "settled-C", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6555.6617.29"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1915.00", "TaxableEarning": "11000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "settled-C", "ValidAsOf": "2022-11-30"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "9363.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Via Lugano 40", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1980-07-07", "WorkOrCompensatory": {"Start": "2022-09-20", "Workplace": "TI"}, "Social-InsuranceIdentification": {"SV-AS-Number": "756.6549.9078.26"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "704.15", "TaxableEarning": "4800.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "8000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "700.80", "TaxableEarning": "4800.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "9666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-09-30"}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Junkerngasse 42", "Country": "SWITZERLAND", "ZIP-Code": "3011"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "30", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Correction": [{"New": {"TaxAtSource": "735.00", "TaxableEarning": "6000.00", "SporadicBenefits": "500.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6000.00"}, "Old": {"TaxAtSource": "-633.05", "TaxableEarning": "-5500.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5500.00"}, "Month": ["XSDGYEARMONTH", 2022, 9, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "303.00", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lausanne", "Street": "Route de chavannes 11", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "VD"}, "DateOfBirth": "1990-03-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "227.50", "TaxableEarning": "5230.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5230.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-12-15", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6328.7099.17"}}}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "109.20", "TaxableEarning": "5200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5140.90"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1971-12-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "227.50", "TaxableEarning": "5230.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Mutation": [{"Reason": "childrenDeduction", "ValidAsOf": "2022-11-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5230.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2040-10-31", "Start": "2022-11-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2022-10-10"}], "Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1969-06-12", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3454.9922.51"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Como", "Street": "Via Milano 26", "Country": "ITALY", "ZIP-Code": "22100"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "166.00", "TaxableEarning": "5200.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Mutation": [{"Reason": "childrenDeduction", "ValidAsOf": "2022-11-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "F1N"}, "AscertainedTaxableEarning": "5133.35"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-10-31", "Start": "2022-11-01", "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-10-22"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Como", "Street": "Via Milano 26", "Country": "ITALY", "ZIP-Code": "22100"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1994-09-27", "WorkOrCompensatory": {"Start": "2022-07-28", "Workplace": "EX"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON>", "Street": "Grand Rue", "Country": "FRANCE", "ZIP-Code": "90100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "FR", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "3000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Mutation": [{"Reason": "residence", "ValidAsOf": "2022-11-01"}]}, "TaxAtSourceCategory": {"CategoryPredefined": "SFN"}, "AscertainedTaxableEarning": "3000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-09-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Kehrgasse 8", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-10-04"}, "DateOfBirth": "1977-10-04", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "32", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3728.4917.63"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-11-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Torino", "Street": "<PERSON><PERSON><PERSON>, 14", "Country": "ITALY", "ZIP-Code": "10121"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1991-11-11"}, "DateOfBirth": "1991-11-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "42", "ResidenceCanton": "EX", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1949.3782.69"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "504.00", "TaxableEarning": "4000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-11-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-11-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "München", "Street": "Marienplatz 1", "Country": "GERMANY", "ZIP-Code": "80331"}, "Lastname": "Ochsenbein", "Firstname": "Lea", "CivilStatus": {"Status": "single", "ValidAsOf": "1993-10-10"}, "DateOfBirth": "1993-10-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "43", "ResidenceCanton": "EX", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6491.7043.37"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "524.80", "TaxableEarning": "4000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-11-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "TotalCommission": "0.00", "TotalTaxAtSource": "5178.70", "TotalTaxableEarning": "49267.85"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "TotalCommission": "0.00", "TotalTaxAtSource": "2855.30", "TotalTaxableEarning": "39343.70"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2022, 9, false], "TotalCommission": "0.00", "TotalTaxAtSource": "101.95", "TotalTaxableEarning": "500.00"}], "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 11, false], "TotalCommission": "0.00", "TotalTaxAtSource": "303.00", "TotalTaxableEarning": "5000.00"}, "institutionIDRef": "#QST_VD"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 20}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2022_12": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "28.00", "ActivityRate": "70.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON><PERSON>", "Street": "Via Monte Ceneri 17", "Country": "SWITZERLAND", "ZIP-Code": "6512"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1972-04-11"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "17", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3425.9630.75"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "573.30", "TaxableEarning": "4550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "30.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "277.45", "TaxableEarning": "2791.40", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "7084.75"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "20.00", "ActivityRate": "50.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "F", "Address": {"City": "Bellinzona", "Street": "Via Lugano 4", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-05-16"}, "DateOfBirth": "1967-05-16", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "19", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1848.4786.64"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "374.40", "TaxableEarning": "5200.05", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "3336.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-06-26"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "424.20", "TaxableEarning": "5111.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "4750.60"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1995-03-15", "WorkOrCompensatory": {"Start": "2022-06-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-08"}, "DateOfBirth": "1989-01-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "23", "MunicipalityID": "5002", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3539.3643.93"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "1200.80", "TaxableEarning": "10200.05", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "7966.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-10-31", "Start": "2022-11-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-10-09"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bellinzona", "Street": "Viale Stefano <PERSON> 17", "Country": "SWITZERLAND", "ZIP-Code": "6500"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1928.1347.70"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxAtSource": "1478.35", "TaxableEarning": "6550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "16000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "1394.15", "TaxableEarning": "6550.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "10333.35"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "F", "Address": {"City": "Lausanne", "Street": "Route de chavannes 11 ", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "Franca", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1992-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "31", "MunicipalityID": "5586", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6508.6893.67"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "1466.00", "TaxableEarning": "10000.05", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5416.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lausanne", "Street": "Route de chavannes 11", "Country": "SWITZERLAND", "ZIP-Code": "1007"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "VD"}, "DateOfBirth": "1990-03-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5586"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1972-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "33", "MunicipalityID": "355", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3434.5129.12"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "1148.85", "TaxableEarning": "10230.05", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "10230.05"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Kö<PERSON>z", "Street": "Wiesenstrasse 14", "Country": "SWITZERLAND", "ZIP-Code": "3098"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-12-15", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6328.7099.17"}}}, "TaxAtSourceMunicipalityID": "355"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-03-26"}, "DateOfBirth": "1967-04-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "34", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6412.9848.00"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "<PERSON><PERSON>", "Street": "Via Aeroporto 2", "Country": "SWITZERLAND", "ZIP-Code": "6982"}}}, "TaxAtSource": "576.40", "TaxableEarning": "10200.05", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "5566.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-04-30", "Start": "2022-05-01", "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-04-23"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Bergamo", "Street": "Piazza Marconi 7", "Country": "ITALY", "ZIP-Code": "24122"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1971-12-15", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5141"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1967-05-16", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "35", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6498.9438.07"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "1148.85", "TaxableEarning": "10230.05", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B1N"}, "AscertainedTaxableEarning": "10230.05"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Children": [{"End": "2040-10-31", "Start": "2022-11-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON>", "DateOfBirth": "2022-10-10"}], "Denomination": "reformedEvangelical", "MarriagePartner": {"Address": {"City": "Bern", "Street": "Blockweg 2", "Country": "SWITZERLAND", "ZIP-Code": "3007"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1969-06-12", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3454.9922.51"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Como", "Street": "Via Milano 26", "Country": "ITALY", "ZIP-Code": "22100"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-05-25"}, "DateOfBirth": "1988-06-17", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "36", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3641.0372.46"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "818.20", "TaxableEarning": "10200.05", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "F1N"}, "AscertainedTaxableEarning": "6400.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Children": [{"End": "2040-10-31", "Start": "2022-11-01", "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2022-10-22"}], "Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Como", "Street": "Via Milano 26", "Country": "ITALY", "ZIP-Code": "22100"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"AbroadCountry": "IT"}, "DateOfBirth": "1994-09-27", "WorkOrCompensatory": {"Start": "2022-07-28", "Workplace": "EX"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON>", "Street": "Grand Rue", "Country": "FRANCE", "ZIP-Code": "90100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-12-11"}, "DateOfBirth": "1977-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "38", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3514.6025.02"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "FR", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "11500.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"CategoryPredefined": "SFN"}, "AscertainedTaxableEarning": "11500.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "München", "Street": "Maffeistrasse 5", "Country": "GERMANY", "ZIP-Code": "80333"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1967-01-01"}, "DateOfBirth": "1967-01-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "39", "ResidenceCanton": "EX", "ResidenceCategory": "othersNotSwiss", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3466.0443.68"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "0.00", "TaxableEarning": "0.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-31"}]}, "TaxAtSourceCategory": {"CategoryPredefined": "HEN"}, "AscertainedTaxableEarning": "0.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-12-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Vevey", "Street": "Rue des Moulins 13", "Country": "SWITZERLAND", "ZIP-Code": "1800"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1996-06-17"}, "DateOfBirth": "1996-06-17", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "40", "MunicipalityID": "5890", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3438.2653.71"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "10370.75", "TaxableEarning": "42783.30", "workplaceIDRef": "#W_*********", "SporadicBenefits": "6650.00", "WorkMunicipalityID": "5890", "DeclarationCategory": {"Entry": [{"Reason": "entryCompany", "ValidAsOf": "2022-12-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A2N"}, "GrantTaxAtSourceCode": "XSDSKIP", "AscertainedTaxableEarning": "20770.80"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Children": [{"End": "2038-05-31", "Start": "2020-06-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2020-05-04"}], "Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5890"}]}}, {"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-09-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Kehrgasse 8", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1977-10-04"}, "DateOfBirth": "1977-10-04", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "32", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3728.4917.63"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "531.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "5000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-11-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-15"}, "Particulars": {"Sex": "M", "Address": {"City": "Torino", "Street": "<PERSON><PERSON><PERSON>, 14", "Country": "ITALY", "ZIP-Code": "10121"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1991-11-11"}, "DateOfBirth": "1991-11-11", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "42", "ResidenceCanton": "EX", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1949.3782.69"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "336.00", "TaxableEarning": "2666.70", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-15"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6666.65"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "TaxAtSourceMunicipalityID": "5002"}]}}, {"Work": {"EntryDate": "2022-11-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2022-12-15"}, "Particulars": {"Sex": "F", "Address": {"City": "München", "Street": "Marienplatz 1", "Country": "GERMANY", "ZIP-Code": "80331"}, "Lastname": "Ochsenbein", "Firstname": "Lea", "CivilStatus": {"Status": "single", "ValidAsOf": "1993-10-10"}, "DateOfBirth": "1993-10-10", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "43", "ResidenceCanton": "EX", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6491.7043.37"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "349.85", "TaxableEarning": "2666.70", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2022-12-15"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "6666.70"}, "CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "351"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "TotalCommission": "0.00", "TotalTaxAtSource": "5697.45", "TotalTaxableEarning": "54677.90"}, "institutionIDRef": "#QST_TI"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "TotalCommission": "0.00", "TotalTaxAtSource": "5384.85", "TotalTaxableEarning": "58968.20"}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2022, 12, false], "TotalCommission": "0.00", "TotalTaxAtSource": "11836.75", "TotalTaxableEarning": "52783.35"}, "institutionIDRef": "#QST_VD"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 19}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2022, false]}}}, "is_declaration_2023_01": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"Staff": {"Person": [{"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxAtSource": "450.00", "TaxableEarning": "10000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "AscertainedTaxableEarning": "10000.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-12-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Vevey", "Street": "Rue des Moulins 13", "Country": "SWITZERLAND", "ZIP-Code": "1800"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1996-06-17"}, "DateOfBirth": "1996-06-17", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "40", "MunicipalityID": "5890", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3438.2653.71"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "VD"}, "TaxAtSource": "86.00", "TaxableEarning": "4300.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "A2N"}, "GrantTaxAtSourceCode": "XSDSKIP", "AscertainedTaxableEarning": "4300.00"}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "institutionIDRef": "#QST_VD", "TaxAtSourceCanton": "VD", "AdditionalParticulars": {"Children": [{"End": "2038-05-31", "Start": "2020-06-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2020-05-04"}], "Denomination": "reformedEvangelical"}, "TaxAtSourceMunicipalityID": "5890"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "112.55", "TaxableEarning": "1376.05", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5669.50"}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-06-26"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "TI"}, "TaxAtSource": "0.00", "TaxableEarning": "1572.60", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "AscertainedTaxableEarning": "1572.60"}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1995-03-15", "WorkOrCompensatory": {"Start": "2022-06-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "5192"}]}}, {"Work": {"EntryDate": "2022-09-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Kehrgasse 8", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-10-26"}, "DateOfBirth": "1977-10-04", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "32", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3728.4917.63"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Current": {"Residence": {"CantonCH": "BE"}, "TaxAtSource": "345.50", "TaxableEarning": "5000.00", "workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Correction": [{"New": {"TaxAtSource": "345.50", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-11-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-531.50", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 12, false]}, {"New": {"TaxAtSource": "345.50", "TaxableEarning": "5000.00", "DeclarationCategory": {"Mutation": [{"Reason": "civilstate", "ValidAsOf": "2022-11-01"}]}, "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "AscertainedTaxableEarning": "5000.00"}, "Old": {"TaxAtSource": "-531.50", "TaxableEarning": "-5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-5000.00"}, "Month": ["XSDGYEARMONTH", 2022, 11, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Gerberweg 10", "Country": "SWITZERLAND", "ZIP-Code": "2560"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-09-18", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Correction": [{"New": {"TaxAtSource": "2520.60", "TaxableEarning": "9758.35", "SporadicBenefits": "5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "21000.00"}, "Old": {"TaxAtSource": "-1478.35", "TaxableEarning": "-6550.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "AscertainedTaxableEarning": "-16000.00"}, "Month": ["XSDGYEARMONTH", 2022, 12, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "institutionIDRef": "#QST_BE", "TaxAtSourceCanton": "BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "351"}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Via Como 12", "Country": "ITALY", "ZIP-Code": "21100"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"Correction": [{"New": {"TaxAtSource": "2356.00", "TaxableEarning": "10658.35", "SporadicBenefits": "5000.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "10972.20"}, "Old": {"TaxAtSource": "-1394.15", "TaxableEarning": "-6550.00", "TaxAtSourceCategory": {"TaxAtSourceCode": "R0N"}, "AscertainedTaxableEarning": "-10333.35"}, "Month": ["XSDGYEARMONTH", 2022, 12, false]}], "CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "institutionIDRef": "#QST_TI", "TaxAtSourceCanton": "TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "TaxAtSourceMunicipalityID": "5002"}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "institutionID": "#QST_TI", "CustomerIdentity": "83189.7"}, {"CantonID": "BE", "institutionID": "#QST_BE", "CustomerIdentity": "9217.8"}, {"CantonID": "VD", "institutionID": "#QST_VD", "CustomerIdentity": "23.957.55.6"}]}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "TotalCommission": "0.00", "TotalTaxAtSource": "908.05", "TotalTaxableEarning": "16376.05"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2022, 11, false], "TotalCommission": "0.00", "TotalTaxAtSource": "-186.00", "TotalTaxableEarning": "0.00"}, {"Month": ["XSDGYEARMONTH", 2022, 12, false], "TotalCommission": "0.00", "TotalTaxAtSource": "856.25", "TotalTaxableEarning": "3208.35"}], "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "TotalCommission": "0.00", "TotalTaxAtSource": "86.00", "TotalTaxableEarning": "4300.00"}, "institutionIDRef": "#QST_VD"}, {"TotalMonth": {"CurrentMonth": ["XSDGYEARMONTH", 2023, 1, false], "TotalCommission": "0.00", "TotalTaxAtSource": "0.00", "TotalTaxableEarning": "1572.60"}, "CorrectionMonth": [{"Month": ["XSDGYEARMONTH", 2022, 12, false], "TotalCommission": "0.00", "TotalTaxAtSource": "961.85", "TotalTaxableEarning": "4108.35"}], "institutionIDRef": "#QST_TI"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 7}, "CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "Address": {"City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Luzern", "Canton": "LU", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "ZIP-Code": "6003", "MunicipalityID": "1061", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bern", "Canton": "BE", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "ZIP-Code": "3011", "MunicipalityID": "351", "ComplementaryLine": "Werkhof/Büro"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Vevey", "Canton": "VD", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "ZIP-Code": "1800", "MunicipalityID": "5890", "ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"City": "Bellinzona", "Canton": "TI", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "ZIP-Code": "6500", "MunicipalityID": "5002", "ComplementaryLine": "Beratung"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2023, false]}}}, "is_declaration_2023_02": {"Job": {"Addressees": [{"TaxAtSource": [{"institutionIDRef": "#QST_TI", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_BE", "ProcessByDistributor": true}, {"institutionIDRef": "#QST_VD", "ProcessByDistributor": true}]}]}, "SalaryDeclaration": {"schemaVersion": "1.0", "Company": {"CompanyDescription": {"Name": {"HR-RC-Name": "Muster AG"}, "UID-BFS": {"UID": "CHE-999.999.996"}, "Address": {"ZIP-Code": "6003", "City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND"}, "Workplace": [{"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>", "ZIP-Code": "6003", "City": "Luzern", "Street": "Bahnhofstrasse 1", "Country": "SWITZERLAND", "Canton": "LU", "MunicipalityID": "1061"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "42.00", "WeeklyLessons": "21.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"ComplementaryLine": "Werkhof/Büro", "ZIP-Code": "3011", "City": "Bern", "Street": "Zeughausgasse 9", "Country": "SWITZERLAND", "Canton": "BE", "MunicipalityID": "351"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"ComplementaryLine": "<PERSON><PERSON><PERSON><PERSON>", "ZIP-Code": "1800", "City": "Vevey", "Street": "Rue des Moulins 9", "Country": "SWITZERLAND", "Canton": "VD", "MunicipalityID": "5890"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}, {"workplaceID": "#W_*********", "BUR-REE-Number": "*********", "AddressExtended": {"ComplementaryLine": "Beratung", "ZIP-Code": "6500", "City": "Bellinzona", "Street": "Via Canonico Ghiringhelli 19", "Country": "SWITZERLAND", "Canton": "TI", "MunicipalityID": "5002"}, "CompanyWorkingTime": {"WeeklyHoursAndLessons": {"WeeklyHours": "40.00", "WeeklyLessons": "20.00", "companyWeeklyHoursAndLessonsID": "#WT_#W_*********"}}}]}, "Staff": {"Person": [{"Work": {"EntryDate": "2022-05-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2023-02-28"}, "Particulars": {"Sex": "F", "Address": {"City": "Konstanz", "Street": "Opelstrasse 1", "Country": "GERMANY", "ZIP-Code": "78467"}, "Lastname": "<PERSON><PERSON>", "Firstname": "Eva", "CivilStatus": {"Status": "single", "ValidAsOf": "1988-11-01"}, "DateOfBirth": "1988-11-01", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "27", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3627.5282.70"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_BE", "AdditionalParticulars": {"Denomination": "otherOrNone"}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "351", "TaxAtSourceCanton": "BE", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "L0N"}, "Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxableEarning": "40000.00", "AscertainedTaxableEarning": "40000.00", "TaxAtSource": "1800.00", "SporadicBenefits": "30000.00", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2023-02-28"}]}}}]}}, {"Work": {"EntryDate": "2022-12-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2023-02-28"}, "Particulars": {"Sex": "F", "Address": {"City": "Vevey", "Street": "Rue des Moulins 13", "Country": "SWITZERLAND", "ZIP-Code": "1800"}, "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON><PERSON><PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1996-06-17"}, "DateOfBirth": "1996-06-17", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "40", "MunicipalityID": "5890", "ResidenceCanton": "VD", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3438.2653.71"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_VD", "AdditionalParticulars": {"Children": [{"End": "2038-05-31", "Start": "2020-06-01", "Lastname": "<PERSON><PERSON>", "Firstname": "<PERSON>", "DateOfBirth": "2020-05-04"}], "Denomination": "reformedEvangelical"}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "5890", "TaxAtSourceCanton": "VD", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5890", "TaxAtSourceCategory": {"TaxAtSourceCode": "A2N"}, "Residence": {"CantonCH": "VD"}, "GrantTaxAtSourceCode": "XSDSKIP", "TaxableEarning": "4300.00", "AscertainedTaxableEarning": "4300.00", "TaxAtSource": "86.00", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2023-02-28"}]}}}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "8.40", "ActivityRate": "20.00"}}, "WithdrawalDate": "2023-02-28"}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Freiburgstrasse 312", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2021-11-01"}, "DateOfBirth": "1982-12-11", "Nationality": "FR", "LanguageCode": "fr", "EmployeeNumber": "18", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "ProvisionallyAdmittedForeigners-F", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3729.5603.90"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Bäumlihofstrasse 385", "Country": "SWITZERLAND", "ZIP-Code": "4125"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BS"}, "DateOfBirth": "1991-06-29", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}, "OtherActivities": {"TotalActivityRate": "60.00"}}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "351", "TaxAtSourceCanton": "BE", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "1061", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "Residence": {"CantonCH": "BE"}, "TaxableEarning": "1376.05", "AscertainedTaxableEarning": "5669.50", "TaxAtSource": "112.55", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2023-02-28"}]}}}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Unsteady": "XSDSKIP"}}, "Particulars": {"Sex": "F", "Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-06-26"}, "DateOfBirth": "1997-06-06", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "22", "MunicipalityID": "5192", "ResidenceCanton": "TI", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6319.2565.36"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "Lugano", "Street": "Via Serafino Balestra 9", "Country": "SWITZERLAND", "ZIP-Code": "6900"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "TI"}, "DateOfBirth": "1995-03-15", "WorkOrCompensatory": {"Start": "2022-06-28", "Workplace": "TI"}, "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "5192", "TaxAtSourceCanton": "TI", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "C0N"}, "Residence": {"CantonCH": "TI"}, "TaxableEarning": "6974.30", "AscertainedTaxableEarning": "3023.45", "TaxAtSource": "256.40", "SporadicBenefits": "3000.00"}}]}}, {"Work": {"EntryDate": "2022-09-01", "WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "WithdrawalDate": "2023-02-28"}, "Particulars": {"Sex": "F", "Address": {"City": "Bern", "Street": "Kehrgasse 8", "Country": "SWITZERLAND", "ZIP-Code": "3018"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "married", "ValidAsOf": "2022-10-26"}, "DateOfBirth": "1977-10-04", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "32", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.3728.4917.63"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "MarriagePartner": {"Address": {"City": "<PERSON><PERSON><PERSON>", "Street": "Gerberweg 10", "Country": "SWITZERLAND", "ZIP-Code": "2560"}, "Lastname": "<PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "Residence": {"CantonCH": "BE"}, "DateOfBirth": "1976-09-18", "Social-InsuranceIdentification": {"unknown": "XSDSKIP"}}}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "351", "TaxAtSourceCanton": "BE", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "B0N"}, "Residence": {"CantonCH": "BE"}, "TaxableEarning": "5000.00", "AscertainedTaxableEarning": "5000.00", "TaxAtSource": "345.50", "DeclarationCategory": {"Withdrawal": [{"Reason": "withdrawalCompany", "ValidAsOf": "2023-02-28"}]}}}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "F", "Address": {"City": "Monza", "Street": "via Vedano 1", "Country": "ITALY", "ZIP-Code": "20900"}, "Lastname": "<PERSON><PERSON><PERSON><PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-04-13"}, "DateOfBirth": "1974-04-13", "Nationality": "DE", "LanguageCode": "fr", "EmployeeNumber": "28", "ResidenceCanton": "EX", "ResidenceCategory": "crossBorder-G", "Social-InsuranceIdentification": {"SV-AS-Number": "756.1853.0576.49"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "351", "TaxAtSourceCanton": "BE", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Weekly": {"City": "Bern", "Street": "Laupenstrasse 10", "Country": "SWITZERLAND", "ZIP-Code": "3008"}}}, "TaxableEarning": "9625.00", "AscertainedTaxableEarning": "31000.00", "TaxAtSource": "2863.45", "SporadicBenefits": "15000.00"}}]}}, {"Work": {"EntryDate": "2022-01-01", "WorkingTime": {"Steady": {"WeeklyHours": "24.00", "ActivityRate": "60.00"}}, "WithdrawalDate": "2022-12-31"}, "Particulars": {"Sex": "M", "Address": {"City": "Bern", "Street": "Laupenstrasse 5", "Country": "SWITZERLAND", "ZIP-Code": "3008"}, "Lastname": "<PERSON>", "Firstname": "<PERSON>", "CivilStatus": {"Status": "single", "ValidAsOf": "1974-07-13"}, "DateOfBirth": "1974-07-13", "Nationality": "IT", "LanguageCode": "fr", "EmployeeNumber": "29", "MunicipalityID": "351", "ResidenceCanton": "BE", "ResidenceCategory": "annual-B", "Social-InsuranceIdentification": {"SV-AS-Number": "756.6361.0022.59"}}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_BE", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "351", "TaxAtSourceCanton": "BE", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "Residence": {"CantonCH": "BE"}, "TaxableEarning": "9625.00", "AscertainedTaxableEarning": "15000.00", "TaxAtSource": "2096.35", "SporadicBenefits": "15000.00", "DeclarationCategory": {"Entry": [{"Reason": "cantonChange", "ValidAsOf": "2023-02-01"}], "Mutation": [{"Reason": "residence", "ValidAsOf": "2023-02-01"}]}}}, {"institutionIDRef": "#QST_TI", "AdditionalParticulars": {"Denomination": "romanCatholic", "OtherActivities": {"TotalActivityRate": "20.00"}}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "5002", "TaxAtSourceCanton": "TI", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "Residence": {"CantonCH": "BE"}, "TaxableEarning": "0.00", "AscertainedTaxableEarning": "0.00", "TaxAtSource": "0.00", "DeclarationCategory": {"Withdrawal": [{"Reason": "cantonChange", "ValidAsOf": "2023-01-31"}], "Mutation": [{"Reason": "residence", "ValidAsOf": "2023-02-01"}]}}}]}}, {"Particulars": {"EmployeeNumber": "42", "Lastname": "<PERSON>", "Firstname": "<PERSON>", "Sex": "M", "DateOfBirth": "1991-11-11", "Nationality": "IT", "ResidenceCanton": "EX", "LanguageCode": "fr", "ResidenceCategory": "annual-B", "CivilStatus": {"Status": "single", "ValidAsOf": "1991-11-11"}, "Address": {"ZIP-Code": "10121", "City": "Torino", "Country": "ITALY", "Street": "<PERSON><PERSON><PERSON>, 14"}, "Social-InsuranceIdentification": {"SV-AS-Number": "756.1949.3782.69"}}, "Work": {"WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "EntryDate": "2022-11-01", "WithdrawalDate": "2022-12-15"}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_TI", "AdditionalParticulars": {"Denomination": "romanCatholic"}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "5002", "TaxAtSourceCanton": "TI", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "5002", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "Residence": {"AbroadCountry": "IT", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxableEarning": "20000.00", "AscertainedTaxableEarning": "9166.65", "TaxAtSource": "3160.00", "SporadicBenefits": "30000.00"}}]}}, {"Particulars": {"EmployeeNumber": "43", "Lastname": "Ochsenbein", "Firstname": "Lea", "Sex": "F", "DateOfBirth": "1993-10-10", "Nationality": "DE", "ResidenceCanton": "EX", "LanguageCode": "fr", "ResidenceCategory": "annual-B", "CivilStatus": {"Status": "single", "ValidAsOf": "1993-10-10"}, "Address": {"ZIP-Code": "80331", "City": "München", "Country": "GERMANY", "Street": "Marienplatz 1"}, "Social-InsuranceIdentification": {"SV-AS-Number": "756.6491.7043.37"}}, "Work": {"WorkingTime": {"Steady": {"WeeklyHours": "40.00", "ActivityRate": "100.00"}}, "EntryDate": "2022-11-01", "WithdrawalDate": "2022-12-15"}, "TaxAtSourceSalaries": {"TaxAtSourceSalary": [{"institutionIDRef": "#QST_BE", "AdditionalParticulars": {"Denomination": "reformedEvangelical"}, "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false], "TaxAtSourceMunicipalityID": "351", "TaxAtSourceCanton": "BE", "Current": {"workplaceIDRef": "#W_*********", "WorkMunicipalityID": "351", "TaxAtSourceCategory": {"TaxAtSourceCode": "A0N"}, "Residence": {"AbroadCountry": "DE", "KindOfResidence": {"Daily": "XSDSKIP"}}, "TaxableEarning": "20000.00", "AscertainedTaxableEarning": "36666.70", "TaxAtSource": "6224.00", "SporadicBenefits": "30000.00"}}]}}]}, "Institutions": {"TaxAtSource": [{"CantonID": "TI", "CustomerIdentity": "83189.7", "institutionID": "#QST_TI"}, {"CantonID": "BE", "CustomerIdentity": "9217.8", "institutionID": "#QST_BE"}, {"CantonID": "VD", "CustomerIdentity": "23.957.55.6", "institutionID": "#QST_VD"}]}, "SalaryCounters": {"NumberOf-TaxAtSourceSalary-Tags": 10}, "SalaryTotals": {"TaxAtSourceTotals": [{"TotalMonth": {"TotalTaxableEarning": "85626.05", "TotalTaxAtSource": "13441.85", "TotalCommission": "0.00", "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false]}, "institutionIDRef": "#QST_BE"}, {"TotalMonth": {"TotalTaxableEarning": "4300.00", "TotalTaxAtSource": "86.00", "TotalCommission": "0.00", "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false]}, "institutionIDRef": "#QST_VD"}, {"TotalMonth": {"TotalTaxableEarning": "26974.30", "TotalTaxAtSource": "3416.40", "TotalCommission": "0.00", "CurrentMonth": ["XSDGYEARMONTH", 2023, 2, false]}, "institutionIDRef": "#QST_TI"}]}}, "GeneralSalaryDeclarationDescription": {"CreationDate": "2024-01-01T00:00:00Z", "AccountingPeriod": ["XSDGYEARMONTH", 2023, false]}}}}