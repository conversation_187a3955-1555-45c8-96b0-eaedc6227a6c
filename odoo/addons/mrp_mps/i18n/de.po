# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_mps
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "<span invisible=\"enable_batch_size\">No Batch Si<PERSON></span>"
msgstr "<span invisible=\"enable_batch_size\"><PERSON><PERSON></span>"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "<span invisible=\"enable_max_replenish\">No Maximum</span>"
msgstr "<span invisible=\"enable_max_replenish\">Höchstanzahl</span>"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_bom_form_view_inherit_mps
msgid "<span>Schedules</span>"
msgstr "<span>Zeitpläne</span>"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "ATP"
msgstr "Zusicherbarer Bestand"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Actual"
msgstr "Tatsächliche(r)"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand"
msgstr "Tatsächlicher Bedarf"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid "Actual Demand %(product)s %(date)s (%(date_start)s - %(date_end)s)"
msgstr ""
"Tatsächlicher Bedarf %(product)s %(date)s (%(date_start)s - %(date_end)s)"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand Y-1"
msgstr "Tatsächlicher Bedarf Y-1"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand Y-2"
msgstr "Tatsächlicher Bedarf Y-2"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__total_qty
msgid "Actual Replenishment"
msgstr "Tatsächliche Auffüllung"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid ""
"Actual Replenishment %(product)s %(date)s (%(date_start)s - %(date_end)s)"
msgstr ""
"Tatsächliche Auffüllung %(product)s %(date)s (%(date_start)s - %(date_end)s)"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
#: model:ir.actions.act_window,name:mrp_mps.action_mrp_mps_form_view
msgid "Add a Product"
msgstr "Produkt hinzufügen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__allowed_route_ids
msgid "Allowed Route"
msgstr "Erlaubte Route"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Are you sure you want to delete these records?"
msgstr "Sind Sie sicher, dass Sie diese Datensätze löschen möchten?"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Are you sure you want to delete this record?"
msgstr "Sind Sie sicher, dass Sie diesen Datensatz löschen möchten?"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__automated
msgid "Automatic"
msgstr "Automatisch"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Automatically Replenished"
msgstr "Automatisch aufgefüllt"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Available to Promise"
msgstr "Zusicherbarer Bestand"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__batch_size
msgid "Batch Size"
msgstr "Stapelgröße"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_bom
msgid "Bill of Material"
msgstr "Stückliste"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__bom_id
msgid "Bill of Materials"
msgstr "Stückliste"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Cancel"
msgstr "Abbrechen"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Close"
msgstr "Schließen"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen "

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Confirmation"
msgstr "Bestätigung"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__day
msgid "Daily"
msgstr "Täglich"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__date
msgid "Date"
msgstr "Datum"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Day"
msgstr "Tag"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Default Time Range"
msgstr "Standardzeitraum"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Standardmaßeinheit, die für alle Lagervorgänge verwendet wird."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,help:mrp_mps.field_res_config_settings__manufacturing_period
msgid ""
"Default value for the time ranges in Master Production Schedule report."
msgstr ""
"Standardwert für die Zeiträume im Bericht des Produktionszeitplanes (MPS)."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Delete"
msgstr "Löschen"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__forecast_qty
msgid "Demand Forecast"
msgstr "Bedarfsprognose"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand
msgid "Display Actual Demand"
msgstr "Tatsächlichen Bedarf anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_2
msgid "Display Actual Demand Before Year"
msgstr "Tatsächlichen Bedarf vor dem Jahr anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_1
msgid "Display Actual Demand Last Year"
msgstr "Tatsächlicher Bedarf des letzten Jahren anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_replenishment
msgid "Display Actual Replenishment"
msgstr "Tatsächliche Auffüllung anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_available_to_promise
msgid "Display Available to Promise"
msgstr "Zusicherbarer Bestand anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_demand_forecast
msgid "Display Demand Forecast"
msgstr "Bedarfsprognose anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_indirect_demand
msgid "Display Indirect Demand"
msgstr "Indirekten Bedarf anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_safety_stock
msgid "Display Safety Stock"
msgstr "Sicherheitsbestand anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_starting_inventory
msgid "Display Starting Inventory"
msgstr "Startinventar anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_to_replenish
msgid "Display To Replenish"
msgstr "Aufzufüllen anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__enable_batch_size
msgid "Enable Batch Size"
msgstr "Stapelgröße aktivieren"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__enable_max_replenish
msgid "Enable Max Replenish"
msgstr "Maximale Auffüllung aktivieren"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__excessive_replenishment
msgid "Excessive Replenishment"
msgstr "Übermäßige Auffüllung"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Export"
msgstr "Exportieren"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "External ID"
msgstr "Externe ID"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_mps_forecast_details
msgid "Forecast Demand Details"
msgstr "Bedarfsdetails prognostizieren"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecasted"
msgstr "Prognostiziert"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecasted Demand"
msgstr "Prognostizierter Bedarf"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Forecasted Stock"
msgstr "Prognostizierter Bestand"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_ids
msgid "Forecasted quantity at date"
msgstr "Prognostizierte Menge am Datum"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__id
msgid "ID"
msgstr "ID"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__bom_id
msgid "If set, the bill of materials components will also be imported."
msgstr "Wenn festgelegt, werden auch die Stücklistenkomponenten importiert."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__batch_size
msgid ""
"If set, the generated manufacturing orders will be split in quantities of "
"this value at maximum."
msgstr ""
"Wenn diese Option gesetzt ist, werden die erzeugten Fertigungsaufträge in "
"Mengen aufgeteilt, die maximal diesem Wert entsprechen."

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid "Import Template for Master Production Schedule"
msgstr "Importvorlage für Produktionszeitplan (MPS)"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Indirect Demand"
msgstr "Indirekter Bedarf"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Indirect Demand Forecast"
msgstr "Indirekte Bedarfsprognose"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__is_indirect
msgid "Indirect demand product"
msgstr "Indirektes Bedarfsprodukt"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__is_manufacture_route
msgid "Is Manufacture Route"
msgstr "Ist Fertigungsroute"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_menu_planning
msgid "MPS"
msgstr "MPS"

#. module: mrp_mps
#: model:ir.actions.server,name:mrp_mps.ir_cron_replenish_automated_mps_ir_actions_server
msgid "MPS: replenish automated schedules"
msgstr "MPS: automatische Zeitpläne auffüllen"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__manual
msgid "Manual"
msgstr "Manuell"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__replenish_trigger
msgid ""
"Manual: Product to be replenished manually from MPS.\n"
"Automatic: Product replenished automatically via scheduled action.\n"
"Never: Product is not replenished from MPS."
msgstr ""
"Manuell: Produkt wird manuell vom MPS aufgefüllt.\n"
"Automatisch: Produkt wird automatisch über eine geplante Aktion aufgefüllt.\n"
"Niemals: Produkt wird nicht aus dem MPS nachgefüllt."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Manually Replenished"
msgstr "Manuell aufgefüllt"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__manufacture_string
msgid "Manufacture String"
msgstr "Zeichenkette für Fertigung"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Manufacturing Order"
msgstr "Fertigungsauftrag"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Manufacturing Orders"
msgstr "Fertigungsaufträge"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period
msgid "Manufacturing Period"
msgstr "Fertigungszeitraum"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/res_company.py:0
msgid ""
"Manufacturing Settings: Your Master Production Schedule must always display "
"at least 1 period."
msgstr ""
"Fertigungseinstellungen: Ihr Produktionszeitplan muss immer mindestens einen"
" Zeitraum anzeigen."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
#: model:ir.actions.client,name:mrp_mps.action_mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_report_menu
#: model:ir.ui.menu,name:mrp_mps.stock_mrp_mps_report_menu
msgid "Master Production Schedule"
msgstr "Produktionszeitplan (MPS)"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid "Maximum to Replenish"
msgstr "Maximum aufzufüllen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid "Minimum to Replenish"
msgstr "Minimum aufzufüllen"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Month"
msgstr "Monat"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__month
msgid "Monthly"
msgstr "Monatlich"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__move_ids
msgid "Move"
msgstr "Buchung"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__moves_string
msgid "Moves String"
msgstr "Zeichenkette für Bewegung"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__never
msgid "Never"
msgstr "Niemals"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid "No product yet. Add one to start scheduling."
msgstr ""
"Noch kein Produkt. Bitte fügen Sie eins hinzu, um die Planung zu starten."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Not Replenished from MPS"
msgstr "Nicht aus dem MPS aufgefüllt"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_day
msgid "Number of Daily Manufacturing Period Columns"
msgstr "Anzahl der Spalten für Tageszeiträume der Fertigung"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_month
msgid "Number of Monthly Manufacturing Period Columns"
msgstr "Anzahl der Spalten für Monatszeiträume der Fertigung"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Number of Periods"
msgstr "Anzahl der Zeiträume"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_week
msgid "Number of Weekly Manufacturing Period Columns"
msgstr "Anzahl der Spalten für Wochenzeiträume der Fertigung"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_year
msgid "Number of Yearly Manufacturing Period Columns"
msgstr "Anzahl der Spalten für Jahreszeiträume der Fertigung"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_day
msgid ""
"Number of columns for the daily period to display in Master Production "
"Schedule"
msgstr ""
"Anzahl der Spalten für den Tageszeitraum, der im Produktionszeitplan (MPS) "
"angezeigt werden sollen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_month
msgid ""
"Number of columns for the monthly period to display in Master Production "
"Schedule"
msgstr ""
"Anzahl der Spalten für den Monatszeitraum, der im Produktionszeitplan (MPS) "
"angezeigt werden sollen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_week
msgid ""
"Number of columns for the weekly period to display in Master Production "
"Schedule"
msgstr ""
"Anzahl der Spalten für den Wochenzeitraum, der im Produktionszeitplan (MPS) "
"angezeigt werden sollen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_year
msgid ""
"Number of columns for the yearly period to display in Master Production "
"Schedule"
msgstr ""
"Anzahl der Spalten für den Jahreszeitraum, der im Produktionszeitplan (MPS) "
"angezeigt werden sollen"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/components/main.js:0
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid "Order"
msgstr "Bestellen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__route_id
msgid "Preferred Route"
msgstr "Bevorzugte Route"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__procurement_launched
msgid "Procurement has been run for this forecast"
msgstr "Die Beschaffung wurde für diese Prognose durchgeführt"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_template
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_id
msgid "Product"
msgstr "Produkt"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_category_id
msgid "Product Category"
msgstr "Produktkategorie"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_product_forecast
msgid "Product Forecast at Date"
msgstr "Produktvorhersage zum Datum"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_tmpl_id
msgid "Product Template"
msgstr "Produktvorlage"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Product UoM"
msgstr "ME des Produkts"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_product
msgid "Product Variant"
msgstr "Produktvariante"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__production_schedule_id
msgid "Production Schedule"
msgstr "Produktionszeitplan"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__warehouse_id
msgid "Production Warehouse"
msgstr "Produktionslager"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_purchase_order
msgid "Purchase Order"
msgstr "Bestellung"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__purchase_order_line_ids
msgid "Purchase Order Line"
msgstr "Bestellzeile"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__moves_qty
msgid "Quantity from Incoming Moves"
msgstr "Menge aus Eingangsbewegungen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__manufacture_qty
msgid "Quantity from Manufacturing Order"
msgstr "Menge aus Fertigungsauftrag"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__rfq_qty
msgid "Quantity from RFQ"
msgstr "Menge aus Angebotsanfragen"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"Quantity predicted to be available for sale at the end of the period (= to "
"replenish - actual demand)."
msgstr ""
"Vorhergesagte Menge, welche am Ende des Zeitraums für den Verkauf verfügbar "
"sein wird (= aufzufüllen - tatsächlicher Bedarf)"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Receipt"
msgstr "Eingang"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Receipts"
msgstr "Wareneingänge"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Replenish"
msgstr "Auffüllen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Replenish State"
msgstr "Auffüllstatus"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty_updated
msgid "Replenish_qty has been manually updated"
msgstr "Die Auffüllmenge ist manuell aktualisiert worden."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too High"
msgstr "Auffüllung zu hoch"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too Low"
msgstr "Auffüllung zu niedrig"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__replenish_trigger
msgid "Replenishment Trigger"
msgstr "Auffüllungsauslöser"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Replenishment:"
msgstr "Auffüllung:"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Request for quotation"
msgstr "Angebotsanfrage"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Requests for quotation"
msgstr "Angebotsanfragen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__rfq_string
msgid "Rfq String"
msgstr "Zeichenkette für Angebotsanfrage"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Route"
msgstr "Route"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__route_id
msgid "Route to replenish your product."
msgstr "Route zur Auffüllung Ihres Produkts."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Rows"
msgstr "Zeilen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid "Safety Stock Target"
msgstr "Zielvorgabe für Sicherheitsbestand"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Save"
msgstr "Speichern"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_production_schedule
msgid "Schedule the production of Product in a warehouse"
msgstr "Planen Sie die Produktion eines Produktes in einem Lager"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_purchase_order__date_planned_mps
msgid "Scheduled Date"
msgstr "Geplantes Datum"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_product_product__schedule_count
#: model:ir.model.fields,field_description:mrp_mps.field_product_template__schedule_count
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_normal_form_view_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_template_only_form_view_mps
msgid "Schedules"
msgstr "Zeitpläne"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__mps_sequence
msgid "Sequence"
msgstr "Sequenz"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__show_bom
msgid "Show Bom"
msgstr "Stückliste anzeigen"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__show_vendor
msgid "Show Vendor"
msgstr "Lieferant anzeigen"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Starting Inventory"
msgstr "Startinventar"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_stock_rule
msgid "Stock Rule"
msgstr "Bestandsregel"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Technical field to support filtering by replenish state"
msgstr ""
"Technisches Feld zur Unterstützung der Filterung nach Auffüllungsstatus"

#. module: mrp_mps
#: model:ir.model.constraint,message:mrp_mps.constraint_mrp_production_schedule_warehouse_product_ref_uniq
msgid "The combination of warehouse and product must be unique!"
msgstr "Die Kombination aus Lager und Produkt muss eindeutig sein!"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The confirmed demand, based on the confirmed sales orders."
msgstr ""
"Der bestätigte Bedarf, basierend auf den bestätigten Verkaufsaufträgen."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The forecasted demand to fulfill the needs in components of the "
"Manufacturing Orders."
msgstr ""
"Der prognostizierte Bedarf zur Deckung des Bedarfs an Komponenten der "
"Fertigungsaufträge."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted demand. This value has to be entered manually."
msgstr ""
"Der prognostizierte Bedarf. Dieser Wert muss manuell eingegeben werden."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted quantity in stock at the beginning of the period."
msgstr "Die prognostizierte Lagermenge am Beginn der Periode."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted quantity in stock at the end of the period."
msgstr "Die prognostizierte Lagermenge am Ende der Periode."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid ""
"The master schedule translates your sales and demand forecasts into a production and purchase planning for each component.\n"
"                    It ensures everything gets scheduled on time, based on constraints such as: safety stock, production capacity, lead times.\n"
"                    It's the perfect tool to support your S&OP meetings."
msgstr ""
"Der Produktionsplan übersetzt Ihre Verkaufs- und Bedarfsprognosen in eine Produktions- und Einkaufsplanung für jede Komponente.\n"
"                     Er stellt sicher, dass alles rechtzeitig eingeplant wird, basierend auf Einschränkungen wie: Sicherheitsbestand, Produktionskapazität, Vorlaufzeiten.\n"
"                    Es ist das perfekte Tool zur Unterstützung Ihrer Meetings zur Absatz- und Vertriebsplanung."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid ""
"The maximum replenishment you would like to launch for each period in the "
"MPS. This is only applied for the period defined in the settings. Note that "
"if the demand is higher than that amount, the remaining quantity will be "
"transferred to the next period automatically."
msgstr ""
"Die maximale Auffüllmenge, die Sie für jede Periode im Produktionszeitplan "
"(MPS) starten möchten. Dies gilt nur für den in den Einstellungen "
"festgelegten Zeitraum. Beachten Sie, dass die verbleibende Menge automatisch"
" auf die nächste Periode übertragen wird, wenn die Bedarf höher ist als "
"dieser Betrag."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The quantity being replenished, based on the Requests for Quotation and the "
"Manufacturing Orders."
msgstr ""
"Die Menge, die nachgefüllt wird, basierend auf Angebotsanfragen und "
"Fertigungsaufträgen"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The quantity to replenish through Purchase Orders or Manufacturing Orders."
msgstr ""
"Die durch eine Bestellung oder einen Fertigungsauftrag aufzufüllende Menge."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid ""
"This is the minimum free stock you want to keep for that product at all "
"times."
msgstr ""
"Dies ist der freie Mindestbestand, den Sie für dieses Produkt immer "
"vorhalten wollen."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "To Forecast"
msgstr "Zu prognostizieren"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__to_replenish
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "To Replenish"
msgstr "Aufzufüllen"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Toggle Indirect Demand"
msgstr "Indirekten Bedarf an/aus"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__under_replenishment
msgid "Under Replenishment"
msgstr "Wird aufgefüllt"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid ""
"Unless the demand is 0, Odoo will always at least replenish this quantity."
msgstr ""
"Wenn der Bedarf nicht gleich 0 ist, wird Odoo immer mindestens diese Menge "
"auffüllen."

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__supplier_id
msgid "Vendor"
msgstr "Lieferant"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Week"
msgstr "Woche"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/res_company.py:0
msgid "Week %(week_num)s (%(start_date)s-%(end_date)s/%(month)s)"
msgstr "Woche %(week_num)s (%(start_date)s-%(end_date)s/%(month)s)"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__week
msgid "Weekly"
msgstr "Wöchentlich"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__is_indirect
msgid ""
"When checked, this product will not appear in the 'To Forecast' filter."
msgstr ""
"Wenn markiert, wird es nicht im Filter „Zu prognostizieren“ angezeigt."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Year"
msgstr "Jahr"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__year
msgid "Yearly"
msgstr "Jährlich"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "by"
msgstr "von"
