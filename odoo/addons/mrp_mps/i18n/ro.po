# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_mps
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "<span invisible=\"enable_batch_size\">No Batch Size</span>"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "<span invisible=\"enable_max_replenish\">No Maximum</span>"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_bom_form_view_inherit_mps
msgid "<span>Schedules</span>"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "ATP"
msgstr "ATP"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Actual"
msgstr "Actual"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand"
msgstr "Cerere Actuală"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid "Actual Demand %(product)s %(date)s (%(date_start)s - %(date_end)s)"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand Y-1"
msgstr "Cerere Actuală A-1"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand Y-2"
msgstr "Cerere Actuală A-2"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__total_qty
msgid "Actual Replenishment"
msgstr "Reaprovizionare Actuală"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid ""
"Actual Replenishment %(product)s %(date)s (%(date_start)s - %(date_end)s)"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
#: model:ir.actions.act_window,name:mrp_mps.action_mrp_mps_form_view
msgid "Add a Product"
msgstr "Adaugă un produs"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__allowed_route_ids
msgid "Allowed Route"
msgstr "Rută permisă"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Are you sure you want to delete these records?"
msgstr "Sigur doriți să ștergeți aceste înregistrări?"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Are you sure you want to delete this record?"
msgstr "Sigur doriți să ștergeți această înregistrare?"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__automated
msgid "Automatic"
msgstr "Automat"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Automatically Replenished"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Available to Promise"
msgstr "Disponibil pentru a promite"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__batch_size
msgid "Batch Size"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_bom
msgid "Bill of Material"
msgstr "Listă de materiale"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__bom_id
msgid "Bill of Materials"
msgstr "Listă de materiale"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Cancel"
msgstr "Anulează"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Close"
msgstr "Închide"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__company_id
msgid "Company"
msgstr "Companie"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Confirmation"
msgstr "Confirmare"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__day
msgid "Daily"
msgstr "Zilnic"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__date
msgid "Date"
msgstr "Dată"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Day"
msgstr "Zi"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Default Time Range"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "Unitatea de măsură folosită implicit în toate operațiunile de stoc."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,help:mrp_mps.field_res_config_settings__manufacturing_period
msgid ""
"Default value for the time ranges in Master Production Schedule report."
msgstr ""
"Valoare implicită pentru intervalele de timp din raportul Master Production "
"Schedule."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Delete"
msgstr "Șterge"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__forecast_qty
msgid "Demand Forecast"
msgstr "Estimare necesar"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand
msgid "Display Actual Demand"
msgstr "Afișare Cerere Actuală"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_2
msgid "Display Actual Demand Before Year"
msgstr "Afișați Cererea Actuală înainte de An"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_1
msgid "Display Actual Demand Last Year"
msgstr "Afișați Cererea Actuală Anul Trecut"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_replenishment
msgid "Display Actual Replenishment"
msgstr "Afișați Reaprovizionarea Actuală"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_available_to_promise
msgid "Display Available to Promise"
msgstr "Afișați Disponibil pentru a promite"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_demand_forecast
msgid "Display Demand Forecast"
msgstr "Afișați Estimarea Cererii"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_indirect_demand
msgid "Display Indirect Demand"
msgstr "Afișați Cererea Indirectă"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_safety_stock
msgid "Display Safety Stock"
msgstr "Afișare Stoc de Siguranță"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_starting_inventory
msgid "Display Starting Inventory"
msgstr "Afișare Inventar de Pornire"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_to_replenish
msgid "Display To Replenish"
msgstr "Afișaj pentru Aprovizionare"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__enable_batch_size
msgid "Enable Batch Size"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__enable_max_replenish
msgid "Enable Max Replenish"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__excessive_replenishment
msgid "Excessive Replenishment"
msgstr "Reaprovizionare Excesivă"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Export"
msgstr "Export"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "External ID"
msgstr "ID extern"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_mps_forecast_details
msgid "Forecast Demand Details"
msgstr "Detalii prognoză cerere"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecasted"
msgstr "Prognozat"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecasted Demand"
msgstr "Cerere prognozată"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Forecasted Stock"
msgstr "Stoc prognozat"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_ids
msgid "Forecasted quantity at date"
msgstr "Cantitate prognozată la data"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__id
msgid "ID"
msgstr "ID"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__bom_id
msgid "If set, the bill of materials components will also be imported."
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__batch_size
msgid ""
"If set, the generated manufacturing orders will be split in quantities of "
"this value at maximum."
msgstr ""

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid "Import Template for Master Production Schedule"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Indirect Demand"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Indirect Demand Forecast"
msgstr "Prognoza cererii indirecte"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__is_indirect
msgid "Indirect demand product"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__is_manufacture_route
msgid "Is Manufacture Route"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_menu_planning
msgid "MPS"
msgstr "MPS"

#. module: mrp_mps
#: model:ir.actions.server,name:mrp_mps.ir_cron_replenish_automated_mps_ir_actions_server
msgid "MPS: replenish automated schedules"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__manual
msgid "Manual"
msgstr "Manual"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__replenish_trigger
msgid ""
"Manual: Product to be replenished manually from MPS.\n"
"Automatic: Product replenished automatically via scheduled action.\n"
"Never: Product is not replenished from MPS."
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Manually Replenished"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__manufacture_string
msgid "Manufacture String"
msgstr ""

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Manufacturing Order"
msgstr "Comanda de Producție"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Manufacturing Orders"
msgstr "Comandă de producție"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period
msgid "Manufacturing Period"
msgstr "Perioadă Producție"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/res_company.py:0
msgid ""
"Manufacturing Settings: Your Master Production Schedule must always display "
"at least 1 period."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
#: model:ir.actions.client,name:mrp_mps.action_mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_report_menu
#: model:ir.ui.menu,name:mrp_mps.stock_mrp_mps_report_menu
msgid "Master Production Schedule"
msgstr "Program Producție Master"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid "Maximum to Replenish"
msgstr "Maximum de Reaprovizionat"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid "Minimum to Replenish"
msgstr "Minim de Reaprovizionat"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Month"
msgstr "Luna"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__month
msgid "Monthly"
msgstr "Lunar"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__move_ids
msgid "Move"
msgstr "Mișcare"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__moves_string
msgid "Moves String"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_trigger__never
msgid "Never"
msgstr "Niciodată"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid "No product yet. Add one to start scheduling."
msgstr "Niciun produs încă. Adăugați unul pentru a începe programarea."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Not Replenished from MPS"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_day
msgid "Number of Daily Manufacturing Period Columns"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_month
msgid "Number of Monthly Manufacturing Period Columns"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Number of Periods"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_week
msgid "Number of Weekly Manufacturing Period Columns"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display_year
msgid "Number of Yearly Manufacturing Period Columns"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_day
msgid ""
"Number of columns for the daily period to display in Master Production "
"Schedule"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_month
msgid ""
"Number of columns for the monthly period to display in Master Production "
"Schedule"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_week
msgid ""
"Number of columns for the weekly period to display in Master Production "
"Schedule"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display_year
msgid ""
"Number of columns for the yearly period to display in Master Production "
"Schedule"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/components/main.js:0
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid "Order"
msgstr "Comandă"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__route_id
msgid "Preferred Route"
msgstr "Traseul preferat"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__procurement_launched
msgid "Procurement has been run for this forecast"
msgstr "Achizițiile au fost efectuate pentru această prognoză"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_template
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_id
msgid "Product"
msgstr "Produs"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_category_id
msgid "Product Category"
msgstr "Categorie produs"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_product_forecast
msgid "Product Forecast at Date"
msgstr "Prognoza produsului la data"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_tmpl_id
msgid "Product Template"
msgstr "Șablon produs"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Product UoM"
msgstr "UM produs"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_product
msgid "Product Variant"
msgstr "Variantă de produs"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__production_schedule_id
msgid "Production Schedule"
msgstr "Program de producție"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__warehouse_id
msgid "Production Warehouse"
msgstr "Depozit Producție"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_purchase_order
msgid "Purchase Order"
msgstr "Comandă de achiziție"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__purchase_order_line_ids
msgid "Purchase Order Line"
msgstr "Linie comandă de achiziție"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__moves_qty
msgid "Quantity from Incoming Moves"
msgstr "Cantitatea din mișcările primite"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__manufacture_qty
msgid "Quantity from Manufacturing Order"
msgstr "Cantitatea din comanda de fabricație"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__rfq_qty
msgid "Quantity from RFQ"
msgstr "Cantitatea din RFQ"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"Quantity predicted to be available for sale at the end of the period (= to "
"replenish - actual demand)."
msgstr ""
"Cantitatea estimată a fi disponibilă pentru vânzare la sfârșitul perioadei "
"(= pentru completare - cerere efectivă)."

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Receipt"
msgstr "Recepție"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Receipts"
msgstr "Recepții"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Replenish"
msgstr "Reaprovizionare"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Replenish State"
msgstr "Stare de reaprovizionare"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty_updated
msgid "Replenish_qty has been manually updated"
msgstr "Replenish_qty a fost actualizat manual"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too High"
msgstr "Reaprovizionare prea mare"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too Low"
msgstr "Reaprovizionare prea mică"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__replenish_trigger
msgid "Replenishment Trigger"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Replenishment:"
msgstr ""

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
msgid "Request for quotation"
msgstr ""

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/wizard/mrp_mps_forecast_details.py:0
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Requests for quotation"
msgstr "Cereri de ofertă"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__rfq_string
msgid "Rfq String"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Route"
msgstr "Rută"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__route_id
msgid "Route to replenish your product."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Rows"
msgstr "Rânduri"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid "Safety Stock Target"
msgstr "Țintă stoc de siguranță"

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Save"
msgstr "Salvează"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_production_schedule
msgid "Schedule the production of Product in a warehouse"
msgstr "Programați producția de produs într-un depozit"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_purchase_order__date_planned_mps
msgid "Scheduled Date"
msgstr "Dată planificată"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_product_product__schedule_count
#: model:ir.model.fields,field_description:mrp_mps.field_product_template__schedule_count
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_normal_form_view_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_template_only_form_view_mps
msgid "Schedules"
msgstr "Planificări"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__mps_sequence
msgid "Sequence"
msgstr "Secvență"

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__show_bom
msgid "Show Bom"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__show_vendor
msgid "Show Vendor"
msgstr "Afișare Furnizor"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Starting Inventory"
msgstr "Inventar de pornire"

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_stock_rule
msgid "Stock Rule"
msgstr "Regulă stoc"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Technical field to support filtering by replenish state"
msgstr "Câmp tehnic pentru a susține filtrarea după starea de reaprovizionare"

#. module: mrp_mps
#: model:ir.model.constraint,message:mrp_mps.constraint_mrp_production_schedule_warehouse_product_ref_uniq
msgid "The combination of warehouse and product must be unique!"
msgstr "Combinația de depozit și produs trebuie să fie unică!"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The confirmed demand, based on the confirmed sales orders."
msgstr "Cererea confirmată, pe baza comenzilor de vânzare confirmate."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The forecasted demand to fulfill the needs in components of the "
"Manufacturing Orders."
msgstr ""
"Cererea prognozată pentru a satisface nevoile componentelor comenzilor de "
"fabricație."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted demand. This value has to be entered manually."
msgstr "Cererea prognozată. Această valoare trebuie introdusă manual."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted quantity in stock at the beginning of the period."
msgstr "Cantitatea prognozată în stoc la începutul perioadei."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted quantity in stock at the end of the period."
msgstr "Cantitatea estimată în stoc la sfârșitul perioadei."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid ""
"The master schedule translates your sales and demand forecasts into a production and purchase planning for each component.\n"
"                    It ensures everything gets scheduled on time, based on constraints such as: safety stock, production capacity, lead times.\n"
"                    It's the perfect tool to support your S&OP meetings."
msgstr ""
"Programul principal traduce previziunile dvs. de vânzări și cereri într-o planificare a producției și achizițiilor pentru fiecare componentă.\n"
"                   Se asigură că totul este programat la timp, pe baza unor constrângeri precum: stocul de siguranță, capacitatea de producție, termenele de livrare.\n"
"                   Este instrumentul perfect pentru a vă susține întâlnirile S&OP."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid ""
"The maximum replenishment you would like to launch for each period in the "
"MPS. This is only applied for the period defined in the settings. Note that "
"if the demand is higher than that amount, the remaining quantity will be "
"transferred to the next period automatically."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The quantity being replenished, based on the Requests for Quotation and the "
"Manufacturing Orders."
msgstr ""
"Cantitatea completată, pe baza cererilor de ofertă și a comenzilor de "
"fabricație."

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid ""
"The quantity to replenish through Purchase Orders or Manufacturing Orders."
msgstr ""
"Cantitatea de completat prin comenzi de achiziție sau comenzi de fabricație."

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid ""
"This is the minimum free stock you want to keep for that product at all "
"times."
msgstr ""
"Acesta este stocul minim liber pe care doriți să-l păstrați pentru acel "
"produs în orice moment."

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "To Forecast"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__to_replenish
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "To Replenish"
msgstr "De Reaprovizionat"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Toggle Indirect Demand"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__under_replenishment
msgid "Under Replenishment"
msgstr "Sub-reaprovizionare"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid ""
"Unless the demand is 0, Odoo will always at least replenish this quantity."
msgstr ""
"Cu excepția cazului în care cererea este 0, Odoo va completa întotdeauna cel"
" puțin această cantitate."

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__supplier_id
msgid "Vendor"
msgstr "Furnizor"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Week"
msgstr "Săptămână"

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/res_company.py:0
msgid "Week %(week_num)s (%(start_date)s-%(end_date)s/%(month)s)"
msgstr "Săptămâna %(week_num)s (%(start_date)s-%(end_date)s/%(month)s)"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__week
msgid "Weekly"
msgstr "Săptămânal"

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__is_indirect
msgid ""
"When checked, this product will not appear in the 'To Forecast' filter."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Year"
msgstr "An"

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__year
msgid "Yearly"
msgstr "Anual"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "by"
msgstr "de"
