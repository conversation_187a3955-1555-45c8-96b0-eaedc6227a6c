# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_mps
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2019-08-26 09:37+0000\n"
"Language-Team: Luxembourgish (https://www.transifex.com/odoo/teams/41243/lb/)\n"
"Language: lb\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "- Indirect Demand Forecast"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "ATP"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Actual"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand"
msgstr ""

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid "Actual Demand %s %s (%s - %s)"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand Y-1"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Actual Demand Y-2"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__total_qty
msgid "Actual Replenishment"
msgstr ""

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/mrp_mps.py:0
msgid "Actual Replenishment %s %s (%s - %s)"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
#: model:ir.actions.act_window,name:mrp_mps.action_mrp_mps_form_view
msgid "Add a Product"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Are you sure you want to delete these records?"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Are you sure you want to delete this record?"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Available to Promise"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_bom
msgid "Bill of Material"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__bom_id
msgid "Bill of Materials"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Cancel"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Close"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_company
msgid "Companies"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__company_id
msgid "Company"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/models/master_production_schedule_model.js:0
msgid "Confirmation"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_uid
msgid "Created by"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__create_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__create_date
msgid "Created on"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__day
msgid "Daily"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__date
msgid "Date"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,help:mrp_mps.field_res_config_settings__manufacturing_period
msgid "Default value for the time ranges in Master Production Schedule report."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Delete"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__forecast_qty
msgid "Demand Forecast"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand
msgid "Display Actual Demand"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_2
msgid "Display Actual Demand Before Year"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_demand_year_minus_1
msgid "Display Actual Demand Last Year"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_actual_replenishment
msgid "Display Actual Replenishment"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_available_to_promise
msgid "Display Available to Promise"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_demand_forecast
msgid "Display Demand Forecast"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_indirect_demand
msgid "Display Indirect Demand"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__display_name
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__display_name
msgid "Display Name"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_safety_stock
msgid "Display Safety Stock"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_starting_inventory
msgid "Display Starting Inventory"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__mrp_mps_show_to_replenish
msgid "Display To Replenish"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__excessive_replenishment
msgid "Excessive Replenishment"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "Export"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
msgid "External ID"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_mps_forecast_details
msgid "Forecast Demand Details"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecast Report"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecasted"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Forecasted Demand"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Forecasted Stock"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_ids
msgid "Forecasted quantity at date"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__sequence
msgid "Gives the sequence order when displaying a product list"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__id
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__id
msgid "ID"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Indirect Demand Forecast"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_uid
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_uid
msgid "Last Updated by"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__write_date
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__write_date
msgid "Last Updated on"
msgstr ""

#. module: mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_menu_planning
msgid "MPS"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Manufacturing Orders"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period
msgid "Manufacturing Period"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.js:0
#: model:ir.actions.client,name:mrp_mps.action_mrp_mps
#: model:ir.ui.menu,name:mrp_mps.mrp_mps_report_menu
#: model:ir.ui.menu,name:mrp_mps.stock_mrp_mps_report_menu
msgid "Master Production Schedule"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid "Maximum to Replenish"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid "Minimum to Replenish"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__month
msgid "Monthly"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__move_ids
msgid "Move"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid "No product yet. Add one to start scheduling."
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Number of Columns"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_config_settings__manufacturing_period_to_display
msgid "Number of Manufacturing Period Columns"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_res_company__manufacturing_period_to_display
msgid "Number of columns for the        given period to display in Master Production Schedule"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__procurement_launched
msgid "Procurement has been run for this forecast"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_template
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_id
msgid "Product"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_category_id
msgid "Product Category"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_product_forecast
msgid "Product Forecast at Date"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_tmpl_id
msgid "Product Template"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__product_uom_id
msgid "Product UoM"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_product_product
msgid "Product Variant"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__production_schedule_id
msgid "Production Schedule"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__warehouse_id
msgid "Production Warehouse"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_purchase_order
msgid "Purchase Order"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__purchase_order_line_ids
msgid "Purchase Order Line"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__moves_qty
msgid "Quantity from Incoming Moves"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__manufacture_qty
msgid "Quantity from Manufacturing Order"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_mps_forecast_details__rfq_qty
msgid "Quantity from RFQ"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Quantity predicted to be available for sale at the end of the period (= to replenish - actual demand)."
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Receipts"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
#: code:addons/mrp_mps/static/src/components/main.js:0
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid "Replenish"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Replenish State"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty_updated
msgid "Replenish_qty has been manually updated"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too High"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "Replenishment Too Low"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_forecast_details_form_view
msgid "Requests for quotation"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Rows"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid "Safety Stock Target"
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_production_schedule_form_view
msgid "Save"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_mrp_production_schedule
msgid "Schedule the production of Product in a warehouse"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_purchase_order__date_planned_mps
msgid "Scheduled Date"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_bom__schedule_count
#: model:ir.model.fields,field_description:mrp_mps.field_product_product__schedule_count
#: model:ir.model.fields,field_description:mrp_mps.field_product_template__schedule_count
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_bom_form_view_inherit_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_normal_form_view_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.product_template_only_form_view_mps
msgid "Schedules"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_production_schedule__sequence
msgid "Sequence"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
msgid "Starting Inventory"
msgstr ""

#. module: mrp_mps
#: model:ir.model,name:mrp_mps.model_stock_rule
msgid "Stock Rule"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "Suggested Replenishment"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__replenish_state
msgid "Technical field to support filtering by replenish state"
msgstr ""

#. module: mrp_mps
#: model:ir.model.constraint,message:mrp_mps.constraint_mrp_production_schedule_warehouse_product_ref_uniq
msgid "The combination of warehouse and product must be unique!"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The confirmed demand, based on the confirmed sales orders."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted demand to fulfill the needs in components of the Manufacturing Orders."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted demand. This value has to be entered manually."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted quantity in stock at the beginning of the period."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The forecasted quantity in stock at the end of the period."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/main.xml:0
msgid ""
"The master schedule translates your sales and demand forecasts into a production and purchase planning for each component.\n"
"                    It ensures everything gets scheduled on time, based on constraints such as: safety stock, production capacity, lead times.\n"
"                    It's the perfect tool to support your S&OP meetings."
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__max_to_replenish_qty
msgid "The maximum replenishment you would like to launch for each period in the MPS. Note that if the demand is higher than that amount, the remaining quantity will be transferred to the next period automatically."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The quantity being replenished, based on the Requests for Quotation and the Manufacturing Orders."
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "The quantity to replenish through Purchase Orders or Manufacturing Orders."
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__forecast_target_qty
msgid "This is the minimum free stock you want to keep for that product at all times."
msgstr ""

#. module: mrp_mps
#: model_terms:ir.ui.view,arch_db:mrp_mps.res_config_settings_view_form
msgid "Time Range"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/search/group_menu.xml:0
#: model:ir.model.fields,field_description:mrp_mps.field_mrp_product_forecast__replenish_qty
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__to_replenish
#: model_terms:ir.ui.view,arch_db:mrp_mps.mrp_mps_search_view
msgid "To Replenish"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__mrp_production_schedule__replenish_state__under_replenishment
msgid "Under Replenishment"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields,help:mrp_mps.field_mrp_production_schedule__min_to_replenish_qty
msgid "Unless the demand is 0, Odoo will always at least replenish this quantity."
msgstr ""

#. module: mrp_mps
#. odoo-python
#: code:addons/mrp_mps/models/res_company.py:0
msgid "Week {week_num} ({start_date}-{end_date}/{month})"
msgstr ""

#. module: mrp_mps
#: model:ir.model.fields.selection,name:mrp_mps.selection__res_company__manufacturing_period__week
msgid "Weekly"
msgstr ""

#. module: mrp_mps
#. odoo-javascript
#: code:addons/mrp_mps/static/src/components/line.xml:0
msgid "by"
msgstr ""
