<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="pe_partner_address_form" model="ir.ui.view">
        <field name="name">pe.partner.form.address</field>
        <field name="model">res.partner</field>
        <field name="priority" eval="900"/>
        <field name="arch" type="xml">
            <form>
                <div class="o_address_format">
                    <field name="country_enforce_cities" invisible="1"/>
                    <field name="parent_id" invisible="1"/>
                    <field name="type" invisible="1"/>
                    <field name="street" placeholder="Street..." class="o_address_street"
                           readonly="type == 'contact' and parent_id"/>
                    <field name="street2" placeholder="Street 2..." class="o_address_street"
                           readonly="type == 'contact' and parent_id"/>
                    <field name="l10n_pe_district" placeholder="District..." class="o_address_street"
                           readonly="type == 'contact' and parent_id"/>
                    <field name="city_id"
                           placeholder="City"
                           class="o_address_city"
                           domain="[('country_id', '=', country_id)]"
                           invisible="not country_enforce_cities"
                           readonly="type == 'contact' and parent_id"
                           context="{'default_country_id': country_id, 'default_state_id': state_id, 'default_zipcode': zip}"/>
                    <field name="city"
                           placeholder="City"
                           class="o_address_city"
                           invisible="country_enforce_cities and (city_id or city in ['', False])"
                           readonly="type == 'contact' and parent_id"/>
                    <field name="state_id" class="o_address_state" placeholder="State" options="{'no_open': True, 'no_quick_create': True}"
                           context="{'default_country_id': country_id}"
                           readonly="type == 'contact' and parent_id"/>
                    <field name="zip" placeholder="ZIP" class="o_address_zip"
                           readonly="type == 'contact' and parent_id"/>
                    <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'
                           readonly="type == 'contact' and parent_id"/>
                </div>
            </form>
        </field>
    </record>
    <record id="base.pe" model="res.country">
        <field name="enforce_cities" eval="1" />
        <field name="address_view_id" ref="pe_partner_address_form" />
        <field name="address_format" eval="'%(street)s\n%(l10n_pe_district_name)s\n%(zip)s%(city)s\n%(state_name)s\n%(country_name)s'"/>
    </record>
</odoo>
