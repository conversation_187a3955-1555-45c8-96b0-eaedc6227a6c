# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_cd
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 09:36+0000\n"
"PO-Revision-Date: 2023-11-30 09:36+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_tax_goods
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable_goods
msgid "1. Goods delivery"
msgstr "1. Livraisons de biens"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_assets
msgid "11. Assets"
msgstr "11. Immobilisations"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_goods
msgid "12. Goods"
msgstr "12. Biens"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_raw_materials
msgid "13. Raw materials"
msgstr "13. Matières premières"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_other
msgid "14. Other goods and services"
msgstr "14. Autres biens et services"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_total_deductible
msgid "15. Total deductible VAT"
msgstr "15. Total TVA déductible"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_report
msgid "16. Report of last month's credit"
msgstr "16. Report de crédit du mois précédent"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_deductible_amount
msgid "17. Deductible VAT amount"
msgstr "17. Montant de la TVA déductible"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_regularisations_repayments
msgid "18. VAT Repayments"
msgstr "18. Reversement de TVA"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_regularisations_add_deduction
msgid "19. Additional Deductions"
msgstr "19. Complément de déductions"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_tax_services
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable_services
msgid "2. Services"
msgstr "2. Prestations de services"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_mining
msgid "20. VAT deducted at source by mining companies"
msgstr "20. TVA retenue à la source par les entreprises minières"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_regularisations_recovery_pm
msgid ""
"21. Recovery of deductible vat credit on externally financed public "
"contracts"
msgstr "21. Récupération de la TVA déductible sur marchés publics à financement extérieur"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_tax_calculation_to_pay
msgid "22. Net VAT to pay (b9+d10+18+20-17-19-20-b5)"
msgstr "22. TVA nette à verser (b9+d10+18+20-17-19-20-b5)"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_calculation_credit
msgid "23. VAT Credit (17+19+20+b5-b9-d10-18-20)"
msgstr "23. Crédit de TVA (17+19+20+b5-b9-d10-18-20)"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_calculation_repayment_asked
msgid "24. Repayment of VAT credit asked"
msgstr "24. Remboursement de crédit de TVA demandé"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_calculation_credit_reportable
msgid "25. VAT Credit reportable (23 - 24)"
msgstr "25. Crédit de TVA reportable (23 - 24)"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_calculation_pm
msgid "26. VAT on Public Markets with external financing (b5)"
msgstr "26. TVA sur marchés publics à financement extérieur (b5)"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_calculation_vat_third_party
msgid "27. VAT on behalf of third parties"
msgstr "27. TVA pour compte des tiers"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_calculation_to_pay
msgid "28. Amount to pay (22 + 26 + 27)"
msgstr "28. Montant à payer (22 + 26 + 27)"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_tax_goods_self
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable_goods_self
msgid "3. Goods self-delivery"
msgstr "3. Livraisons de biens à soi-même"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_tax_services_self
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable_services_self
msgid "4. Services self-delivery"
msgstr "4. Prestations de services à soi-même"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_tax_public_market
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable_public_market
msgid "5. Public Markets with external financing"
msgstr "5. Opérations afférentes aux marchés publics à financement extérieur"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable_export
msgid "6. Export"
msgstr "6. Exportations et opérations assimilées"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable_exempt
msgid "7. Exempted operations"
msgstr "7. Opérations exonérées"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable_non_imposable
msgid "8. Non imposable operations"
msgstr "8. Opérations non imposables"

#. module: l10n_cd
#: model:ir.model,name:l10n_cd.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de Plan Comptable"

#. module: l10n_cd
#: model:account.report.column,name:l10n_cd.account_tax_report_cd_balance
msgid "Balance"
msgstr "Solde"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales
msgid "II. Operations carried out"
msgstr "II. Opérations réalisées"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_importation_services
msgid "III. Services received from providers not established in DRC"
msgstr "III. Prestations reçues des prestataires non établis en RDC"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible
msgid "IV. Deductible tax on"
msgstr "IV. Déductions/Taxe déductible sur"

#. module: l10n_cd
#. odoo-python
#: code:addons/l10n_cd/models/template_cd_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr "SYSCEBNL pour Associations"

#. module: l10n_cd
#. odoo-python
#: code:addons/l10n_cd/models/template_cd.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr "SYSCOHADA pour Sociétés"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_regularisations
msgid "V. Regularisations"
msgstr "V. Régularisations"

#. module: l10n_cd
#: model:account.report,name:l10n_cd.account_tax_report_cd
msgid "VAT Report"
msgstr "Déclaration TVA"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_tax_calculation
msgid "VI. Tax calculation"
msgstr "VI. Calcul de l'impôt"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_taxable
msgid "a) Taxable Turnover"
msgstr "a) Chiffre d'affaires imposable"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_sales_tax
msgid "b) Tax collected"
msgstr "b) TVA collectée"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_importation_services_base
msgid "c) Invoice amounts"
msgstr "c) Montant des factures"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_importation_services_tax
msgid "d) Tax collected"
msgstr "d) TVA collectée"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_importations_assets
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_importations_goods
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_importations_other
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_importations_raw_materials
msgid "e) Importations"
msgstr "e) Importations"

#. module: l10n_cd
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_local_assets
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_local_goods
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_local_other
#: model:account.report.line,name:l10n_cd.account_tax_report_line_cd_deductible_local_raw_materials
msgid "f) Local"
msgstr "f) Local"
