# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ga
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 10:12+0000\n"
"PO-Revision-Date: 2023-11-30 10:12+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales
msgid "1- Realized sales"
msgstr "1- Chiffre d'affaires réalisé"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_non_impo
msgid "1. Non-imposable operations"
msgstr "1. Opérations non imposables"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_taxable
msgid "1. Taxable operations"
msgstr "1. Opérations imposables"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_total_payments
msgid "10. Total (10a+10b)"
msgstr "10. Total (10a+10b)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_refund_requested
msgid "10a. refund requested the previous month"
msgstr "10a. remboursement demandé le mois précédent"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_repayment
msgid "10b. repayment to be made"
msgstr "10b. reversement à effectuer "

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_report_credit
msgid "11. Credit carried over from previous month"
msgstr "11. Report de crédit du mois précédent"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_total
msgid "12. Total (7+8+9-10+11)"
msgstr "12. Total (7+8+9-10+11)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_gross_payable
msgid "13. Gross VAT"
msgstr "13. TVA brute"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_total_gross
msgid "14. Total gross VAT"
msgstr "14. Total TVA brute"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deductible
msgid "15. Deductible VAT"
msgstr "15. TVA déductible"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_net_to_pay
msgid "16. Net VAT to pay (14-15)"
msgstr "16. TVA nette à payer (14-15)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_credit_to_report
msgid "17. Credit to report (15-14)"
msgstr "17. Crédit à reporter (15-14)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction
msgid "2- Deductions"
msgstr "2- Déductions"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_export
msgid "2. Exports"
msgstr "2. Exportations"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_css_taxable_state
msgid "2. Taxable operations with state"
msgstr "2. Opérations imposables avec l’Etat"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_settlement
msgid "3- Settlement of VAT payable"
msgstr "3- Décompte de la TVA à payer"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_css_taxable_other
msgid "3. Other taxable operations"
msgstr "3. Autres opérations imposables"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_taxable
msgid "3. Taxable operations"
msgstr "3. Opérations imposables"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_css_base_taxable
msgid "4. Taxable base"
msgstr "4. Base imposable"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_total
msgid "4. Total of operations (line 1+2+3+5)"
msgstr "4. Total des opérations (lignes 1+2+3+5)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_additional_taxable
msgid "5. Additional taxable operations"
msgstr "5. Autres opérations imposables"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_css_rate
msgid "5. Rate: 1%"
msgstr "5. Taux: 1%"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_taxable_state
msgid "5a. Taxable operations with the State"
msgstr "5a. Opérations imposables avec l'Etat"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_additional_taxable_other
msgid "5b. Other taxable operations"
msgstr "5b. Autres opérations imposables"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_css_due
msgid "6. Amount due"
msgstr "6. Montant dû"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_gross
msgid "6. Gross VAT (line 3+5)"
msgstr "6. Base TVA (lignes 3 + 5)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_total
msgid "7. Total (7a+7b)"
msgstr "7. Total (7a + 7b)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_importation
msgid "7a. on importation"
msgstr "7a. sur importation"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_domestic
msgid "7b. on domestic markets"
msgstr "7b. sur marchés intérieurs "

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_total
msgid "8. Total (8a+8b)"
msgstr "8. Total (8a + 8b)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_importation
msgid "8a. on importation"
msgstr "8a. sur importation"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_domestic
msgid "8b. on domestic markets"
msgstr "8b. sur marchés intérieurs"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_total
msgid "9. Total (9a+9b+9c)"
msgstr "9. Total (9a+9b+9c)"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_withholding
msgid "9a. State withholding"
msgstr "9a. précompte Etat"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_exemptions
msgid "9b. VAT exemptions"
msgstr "9b. dispenses de TVA"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_additional_deduction
msgid "9c. Additional deduction"
msgstr "9c. complément de déductions"

#. module: l10n_ga
#: model:ir.model,name:l10n_ga.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de Plan Comptable"

#. module: l10n_ga
#: model:account.report.column,name:l10n_ga.account_tax_report_ga_base
msgid "Base"
msgstr "Base"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets
msgid "Deductions on assets"
msgstr "Déductions sur immobilisations"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service
msgid "Deductions on goods and services"
msgstr "Déductions sur biens et services"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_vat
msgid "I. Value added tax"
msgstr "I. Taxe sur la Valeur Ajoutée"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_css
msgid "II. Special solidarity contribution"
msgstr "II. Contribution Spéciale de Solidarité"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation
msgid "Regularisations"
msgstr "Régularisations "

#. module: l10n_ga
#. odoo-python
#: code:addons/l10n_ga/models/template_ga_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr "SYSCEBNL pour Associations"

#. module: l10n_ga
#. odoo-python
#: code:addons/l10n_ga/models/template_ga.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr "SYSCOHADA pour Sociétés"

#. module: l10n_ga
#: model:account.report.column,name:l10n_ga.account_tax_report_ga_tax
msgid "Tax"
msgstr "Taxe"

#. module: l10n_ga
#: model:account.report,name:l10n_ga.account_tax_report_ga
msgid "VAT Report"
msgstr "Déclaration TVA"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_domestic_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_importation_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_domestic_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_importation_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_gross_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_additional_deduction_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_exemptions_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_withholding_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_additional_taxable_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_additional_taxable_other_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_taxable_10
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_taxable_state_10
msgid "at 10%"
msgstr "à 10%"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_domestic_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_importation_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_domestic_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_importation_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_gross_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_additional_deduction_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_exemptions_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_withholding_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_additional_taxable_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_additional_taxable_other_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_taxable_18
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_taxable_state_18
msgid "at 18%"
msgstr "à 18%"

#. module: l10n_ga
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_domestic_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_assets_importation_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_domestic_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_deduction_goods_service_importation_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_gross_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_additional_deduction_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_exemptions_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_regularisation_withholding_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_additional_taxable_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_additional_taxable_other_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_taxable_5
#: model:account.report.line,name:l10n_ga.account_tax_report_line_ga_sales_taxable_state_5
msgid "at 5%"
msgstr "à 5%"
