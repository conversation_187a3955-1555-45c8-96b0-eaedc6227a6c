# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_account_enterprise
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<span>Quantity</span>"
msgstr "<span>الكمية</span>"

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<span>Subcontractor</span>"
msgstr "<span>متعاقد من الباطن</span> "

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<span>Total Cost</span>"
msgstr "<span>التكلفة الكلية</span>"

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<span>Unit Cost</span>"
msgstr "<span>سعر الوحدة</span>"

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<strong>Total Cost of Subcontracting</strong>"
msgstr "<strong>إجمالي تكاليف التعاقد من الباطن</strong> "

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_report_pivot_view_inherit_subcontracting
msgid "Average Subcontracting Cost / Unit"
msgstr "متوسط تكلفة التعاقد من الباطن لكل وحدة "

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "Cost of Subcontracting"
msgstr "تكلفة التعاقد من الباطن "

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "Cost of Subcontracting per unit (in"
msgstr "تكلفة التعاقد من الباطن لكل وحدة ("

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model,name:mrp_subcontracting_account_enterprise.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr "تقرير بنية التكلفة في نظام تخطيط متطلبات المواد"

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model,name:mrp_subcontracting_account_enterprise.model_mrp_report
msgid "Manufacturing Report"
msgstr "تقرير التصنيع "

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,help:mrp_subcontracting_account_enterprise.field_mrp_report__unit_subcontracting_cost
msgid ""
"Subcontracting cost per unit produced (in product UoM) of manufacturing "
"order"
msgstr ""
"تكلفة التعاقد من الباطن لكل وحدة منتَجة (في قائمة مواد المنتج) لأمر التصنيع "

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,field_description:mrp_subcontracting_account_enterprise.field_mrp_report__total_cost
msgid "Total Cost"
msgstr "إجمالي التكلفة"

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,field_description:mrp_subcontracting_account_enterprise.field_mrp_report__subcontracting_cost
msgid "Total Subcontracting Cost"
msgstr "التكلفة الكلية للتعاقد من الباطن "

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,field_description:mrp_subcontracting_account_enterprise.field_mrp_report__unit_subcontracting_cost
msgid "Total Subcontracting Cost / Unit"
msgstr "إجمالي تكلفة التعاقد من الباطن لكل وحدة "

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,help:mrp_subcontracting_account_enterprise.field_mrp_report__total_cost
msgid ""
"Total cost of manufacturing order (component + operation costs + "
"subcontracting cost)"
msgstr ""
"إجمالي تكاليف أمر التصنيع (المكون + تكاليف العمليات + تكاليف التعاقد من "
"الباطن) "

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,help:mrp_subcontracting_account_enterprise.field_mrp_report__subcontracting_cost
msgid "Total cost of subcontracting for manufacturing order"
msgstr "التكلفة الكلية للتعاقد من الباطن لأمر التصنيع "
