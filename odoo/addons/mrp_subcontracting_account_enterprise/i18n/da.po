# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_account_enterprise
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Danish (https://app.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<span>Quantity</span>"
msgstr "<span>Antal</span>"

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<span>Subcontractor</span>"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<span>Total Cost</span>"
msgstr "<span>Samlet Omkostning</span>"

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<span>Unit Cost</span>"
msgstr "<span>Enheds omkostning</span>"

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "<strong>Total Cost of Subcontracting</strong>"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_report_pivot_view_inherit_subcontracting
msgid "Average Subcontracting Cost / Unit"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "Cost of Subcontracting"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_account_enterprise.mrp_cost_structure_subcontracting
msgid "Cost of Subcontracting per unit (in"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model,name:mrp_subcontracting_account_enterprise.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr "MRP Omkostning struktur rapport"

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model,name:mrp_subcontracting_account_enterprise.model_mrp_report
msgid "Manufacturing Report"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,help:mrp_subcontracting_account_enterprise.field_mrp_report__unit_subcontracting_cost
msgid ""
"Subcontracting cost per unit produced (in product UoM) of manufacturing "
"order"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,field_description:mrp_subcontracting_account_enterprise.field_mrp_report__total_cost
msgid "Total Cost"
msgstr "Total kostbeløb"

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,field_description:mrp_subcontracting_account_enterprise.field_mrp_report__subcontracting_cost
msgid "Total Subcontracting Cost"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,field_description:mrp_subcontracting_account_enterprise.field_mrp_report__unit_subcontracting_cost
msgid "Total Subcontracting Cost / Unit"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,help:mrp_subcontracting_account_enterprise.field_mrp_report__total_cost
msgid ""
"Total cost of manufacturing order (component + operation costs + "
"subcontracting cost)"
msgstr ""

#. module: mrp_subcontracting_account_enterprise
#: model:ir.model.fields,help:mrp_subcontracting_account_enterprise.field_mrp_report__subcontracting_cost
msgid "Total cost of subcontracting for manufacturing order"
msgstr ""
