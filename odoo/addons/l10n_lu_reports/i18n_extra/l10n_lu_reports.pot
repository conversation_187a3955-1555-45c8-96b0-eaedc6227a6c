# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_lu_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-09 09:02+0000\n"
"PO-Revision-Date: 2022-11-09 09:02+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__avg_nb_employees_with_no_salary
msgid "  - with no salary (family members)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__avg_nb_employees_with_salary
msgid "  - with salary or wage"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_reports_report_appendix_expenditures
msgid "\"Operational Expenditures\" Appendix for LU"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.LTCodeNode
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.SCodeNode
msgid "0,00"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "001"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "002"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "003"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "004"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "005"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "007"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "008"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "009"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "010"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "011"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "013"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "077"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "078"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "079"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "081"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "082"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "083"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "085"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "086"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "087"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "098"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "099"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_1_1
msgid "1. Costs of development"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_1
msgid "1. Debenture loans"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_2_1
msgid "1. Land and buildings"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_4_1
msgid "1. Legal reserve"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_1_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_1
msgid "1. Net turnover"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_2_1
msgid "1. Provisions for pensions and similar obligations"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_1_1
msgid "1. Raw materials and consumables"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_3_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_3_1
msgid "1. Shares in affiliated undertakings"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_1
msgid "1. Trade debtors"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_1_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_1
msgid "1. to 5. Gross profit or loss"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.l10n_lu_electronic_report_template_1_1
msgid "1.1"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_10_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_10_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_10
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_10
msgid ""
"10. Income from other investments and loans forming part of the fixed assets"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "100"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "106"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "107"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_11_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_11_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_11
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_11
msgid "11. Other interest receivable and similar income"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "114"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "115"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "116"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "117"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "118"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "119"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_12_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_12
msgid "12. Share of profit or loss of undertakings accounted "
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_12_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_12
msgid ""
"12. Share of profit or loss of undertakings accounted for under the equity "
"method"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "120"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "121"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "124"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "128"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "129"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_13_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_13_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_13
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_13
msgid ""
"13. Value adjustments in respect of financial assets and of investments held"
" as current assets"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "130"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "131"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "134"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "136"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "137"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "138"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "139"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_14_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_14_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_14
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_14
msgid "14. Interest payable and similar expenses"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "142"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "144"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "145"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "146"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "147"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "148"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "149"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_15_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_15_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_15
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_15
msgid "15. Tax on profit or loss"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "150"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "151"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "153"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "154"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "155"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "158"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_16_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_16_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_16
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_16
msgid "16. Profit or loss after taxation"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "162"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "163"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "164"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "165"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "166"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "167"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "168"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_17_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_17_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_17
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_17
msgid "17. Other taxes not shown under items 1 to 16"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "171"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "175"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "176"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "177"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "178"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_18_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_18_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_18
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_18
msgid "18. Profit or loss for the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "180"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "181"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "183"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "184"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "185"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "186"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "187"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "188"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "189"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "190"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "191"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "192"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "193"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "197"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "198"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "199"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_2
msgid "2. Amounts owed by affiliated undertakings"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_2
msgid "2. Amounts owed to credit institutions"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_1_2
msgid ""
"2. Concessions, patents, licences, trade marks and similar rights and "
"assets, if they were"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_3_2
msgid "2. Loans to affiliated undertaking"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_3_2
msgid "2. Own shares"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_2_2
msgid "2. Plant and machinery"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_2_2
msgid "2. Provisions for taxation"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_4_2
msgid "2. Reserve for own shares"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_2
msgid "2. Variation in stocks of finished goods and in work in progress"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_1_2
msgid "2. Work in progress"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.EcSalesLuXMLReport
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.l10n_lu_electronic_report_template_2_0
msgid "2.0"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "200"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "201"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "202"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "203"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "206"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "229"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "239"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "240"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "241"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "242"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "243"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "244"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "245"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "246"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "247"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "248"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "249"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "250"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "251"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "252"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "253"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "254"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "255"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "256"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "257"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "258"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "259"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "260"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "261"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "262"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "263"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "264"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "265"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "266"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "267"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "268"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "269"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "270"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "271"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "272"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "273"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "274"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "275"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "276"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "277"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "278"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "279"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "280"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "281"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "282"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "283"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "284"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "285"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "286"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "287"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "288"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "289"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "290"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "291"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "292"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "293"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "294"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "295"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "296"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "297"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "298"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "299"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_3
msgid ""
"3. Amounts owed by undertakings with which the undertaking is linked by "
"virtue of participating interests"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_1_3
msgid "3. Finished goods and goods for resale"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_1_3
msgid ""
"3. Goodwill, to the extent that it was acquired for valuable consideration"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_2_3
msgid "3. Other fixtures and fittings, tools and equipment"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_3_3
msgid "3. Other investments"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_2_3
msgid "3. Other provisions"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_3_3
msgid "3. Participating interests"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_3
msgid ""
"3. Payments received on account of orders in so far as they are shown "
"separately as deductions from stocks"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_4_3
msgid "3. Reserves provided for by the articles of association"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_3_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_3
msgid ""
"3. Work performed by the undertaking for its own purposes and capitalised"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "300"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "301"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "302"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "303"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "304"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "305"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "306"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "307"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "308"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "309"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "310"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "311"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "312"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "313"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "314"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "315"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "316"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "317"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "318"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "319"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "320"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "321"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "322"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "323"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "324"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "325"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "326"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "327"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "328"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "329"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "330"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "331"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "332"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "333"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "334"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "335"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "336"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "337"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "338"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "343"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "344"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "345"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "346"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "347"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "348"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "349"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "350"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "351"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "352"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "353"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "354"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "355"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "356"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "357"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "358"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "359"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "361"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "362"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "363"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "364"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "365"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "366"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "367"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "368"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "369"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "372"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "373"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "374"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "375"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "376"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "377"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "378"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "379"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "380"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "381"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "382"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "383"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "384"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "385"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "386"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "387"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "388"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "389"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "394"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "396"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "397"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "398"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "399"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_3_4
msgid ""
"4. Loans to undertakings with which the undertaking is linked by virtue of "
"participating interests"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_4
msgid "4. Other debtors"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_4_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_4
msgid "4. Other operating income"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_4_4
msgid "4. Other reserves, including the fair value reserve"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_1_4
msgid "4. Payments on account"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_1_4
msgid "4. Payments on account and intangible assets under development"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_2_4
msgid ""
"4. Payments on account and tangible assets in the course of construction"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_4
msgid "4. Trade creditors"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "400"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "401"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "402"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "404"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "405"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "406"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "455"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "456"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "457"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "458"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "459"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "460"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "461"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "472"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_5
msgid "5. Bills of exchange payable"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_3_5
msgid "5. Investments held as fixed assets"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_5_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_5
msgid "5. Raw materials and consumables and other external expenses"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_6
msgid "6. Amounts owed to affiliated undertakings"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_3_6
msgid "6. Other loans"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6
msgid "6. Staff costs"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_7
msgid ""
"7. Amounts owed to undertakings with which the undertaking is linked by "
"virtue of participating interests"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_7_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_7_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_7
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_7
msgid "7. Value adjustments"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "771"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "772"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "773"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "774"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "776"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "777"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "778"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "781"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "782"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "783"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "791"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "792"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "793"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "794"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "795"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "796"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "797"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "798"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_8
msgid "8. Other creditors"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_8_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_8_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_8
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_8
msgid "8. Other operating expenses"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_9_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_9_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_9
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_9
msgid "9. Income from participating interests"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1
msgid "A. Capital and reserves"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_1
msgid "A. Subscribed capital unpaid"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1
msgid "ASSETS"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_accounts_report__bs__abr
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_accounts_report__pl__abr
msgid "Abridged"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Accident insurance"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Accountant (A9) details"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Accountant and Lessor"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_account_report
msgid "Accounting Report"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Accounting and bookkeeping fees"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Acquisitions during the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Acquisitions other than manufactured tobacco"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_needaction
msgid "Action Needed"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_ids
msgid "Activities"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_state
msgid "Activity State"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Adjustment of Deductions"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Adjustment of deductions in accordance with provisions of Art. 53"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Adjustment of deductions in accordance with provisions of the grand-ducal "
"Regulation of 7 March 1980 - option to tax land"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Advertising and publicity"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.column,name:l10n_lu_reports.account_financial_report_ec_sales_amount
msgid "Amount"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_tax_report__period__a
msgid "Annual"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.actions.act_window,name:l10n_lu_reports.action_l10n_lu_yearly_tax_report_manual
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_yearly_tax_report_manual
#: model:ir.ui.menu,name:l10n_lu_reports.menu_action_l10n_lu_yearly_tax_report_manual
msgid "Annual Tax Report"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__submitted_rcs
msgid "Annual accounts submitted to the Trade and Companies Register (RCS)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Appendix A"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Appendix B"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Appendix C"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Appendix D"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Appendix E"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Appendix F. Names and addresses to be specified"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__appendix_ids
msgid "Appendix fields"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.l10n_lu_yearly_tax_report_appendix_view_tree
msgid "Appendix line"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Appendix to Operational Expenditures"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Application for the purpose of business of goods produced in the course of "
"the business (009)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Application of goods for non-business use and for business purposes (455)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Application of goods for non-business use and for business purposes (455): "
"amount still to be allocated"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Application of goods for private use or for that of the staff (008)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Assessment total of the taxable amount for non-business use of assets "
"allocated to business"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_ir_attachment
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__attachment_id
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.l10n_lu_stored_intra_report_view_tree
msgid "Attachment"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__avg_nb_employees
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__avg_nb_employees
msgid "Average number of employees for the fiscal year"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_2
msgid "B. Formation expenses"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_2
msgid "B. Provisions"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.column,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_column
#: model:account.report.column,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_column
#: model:account.report.column,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_column
#: model:account.report.column,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_column
msgid "Balance"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report,name:l10n_lu_reports.account_financial_report_l10n_lu_bs
msgid "Balance Sheet"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr
msgid "Balance Sheet (abridged)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_generate_accounts_report
msgid "Balance sheet version"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_general_ledger.py:0
msgid ""
"Below products has duplicated `Internal Reference`, please make them unique:\n"
"`%s`."
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Book (net asset) value at the beginning of the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Book (net asset) value at the end of the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__books_records_documents
msgid "Books, records and documents"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_tax_report.py:0
msgid ""
"Both fields 369 and 368 must be either filled in or left empty (Appendix B)."
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__bs
msgid "Bs"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__report_section_412
msgid "Business portion expenditures VAT excluded (412)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__report_section_413
msgid "Business portion of VAT invoiced (413)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Business tax"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_3
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3
msgid "C. Creditors"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_3
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3
msgid "C. Fixed assets"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2
msgid "CAPITAL, RESERVES AND LIABILITIES"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_financial_report.py:0
msgid "Cannot export them."
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Car expenses"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid "Check from/to dates. XML must cover 1 full month or 1 full quarter."
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Cleaning and maintenance of business premises"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_coa_report.py:0
msgid "CoA report for Luxembourg not supported for years previous to 2019."
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__coa_only
msgid "Coa Only"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__codes
msgid "Codes"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Commissions"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_res_company
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__company_ids
msgid "Companies"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__company_id
msgid "Company"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_xml.py:0
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_xml.py:0
msgid "Company : %s"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_partner__l10n_lu_agent_rcs_number
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_users__l10n_lu_agent_rcs_number
msgid "Company Registry"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Compulsory social security contributions"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_xml.py:0
msgid "Configure"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.column,name:l10n_lu_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__create_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report__create_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__create_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__create_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml__create_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__create_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__create_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__create_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report__create_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__create_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__create_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml__create_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__create_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__create_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__create_date
msgid "Created on"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Custom"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Custom %"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Custom:"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_4
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4
msgid "D. Current assets"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_4
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_4
msgid "D. Deferred income"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Depreciation"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Depreciation during the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__report_section_411
msgid "Detail of expenses line 43 (411)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__display_name
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report__display_name
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__display_name
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__display_name
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml__display_name
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__display_name
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__display_name
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Disposal of tangible and intangible capital assets for ancillary "
"transactions"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Due in respect of Intra-Community acquistions of goods (459): amount still "
"to be allocated"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Due in respect of intra-Community acquisitions of goods (459)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Due or paid in respect of importation of goods (460)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Due or paid in respect of mportation of goods (460): amount still to be "
"allocated"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Due under the reverse charge (461)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Due under the reverse charge (461): amount still to be allocated"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_5
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_5
msgid "E. Prepayments"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.actions.act_window,name:l10n_lu_reports.action_l10n_lu_stored_intra_report
#: model:ir.ui.menu,name:l10n_lu_reports.menu_action_l10n_lu_stored_intra_report
msgid "EC Sales - Stored Reports"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_partner__l10n_lu_agent_ecdf_prefix
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_users__l10n_lu_agent_ecdf_prefix
msgid "ECDF Prefix"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_generate_accounts_report
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_generate_vat_intra_report
msgid "EXPORT XML"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Electricity"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Employer's travel and representation expenses"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "End of financial year"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid "Error in file "
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Exempt"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Expenses for other work carried out by third parties"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Expenses for work carried out by sub-contractors"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_financial_report.py:0
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid "Export"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Export XML"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_yearly_tax_report_manual__state__exported
msgid "Exported"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_general_ledger.py:0
msgid "FAIA"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Fees and subscriptions paid"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_tax_report.py:0
msgid ""
"Fields 164 and 165 are mandatory when 163 is filled in and must add up to "
"field 163 (Appendix E)."
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__filename
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report__filename
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__filename
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__filename
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml__filename
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__filename
msgid "Filename"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Fire insurance"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_follower_ids
msgid "Followers"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Fuel (petrol, diesel) and lubricants"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_accounts_report__bs__full
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_accounts_report__pl__full
msgid "Full"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Gas"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "General Information"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_generate_accounts_report
msgid "Generate Accounts Report"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_generate_accounts_report
msgid ""
"Generate Chart of Accounts only (Non-automated generation of the balance "
"sheet and the profit and loss account)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_generate_vat_intra_report
msgid "Generate Sales Report"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.actions.act_window,name:l10n_lu_reports.action_l10n_lu_generate_vat_intra_report
msgid "Generate Sales report"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_generate_tax_report
msgid "Generate Tax Report"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_generate_asset_report
msgid "Generate XML for Luxembourg"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_generate_xml
msgid "Generate Xml 2.0"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.actions.act_window,name:l10n_lu_reports.action_l10n_lu_generate_accounts_report
msgid "Generate accounts report"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Goods produced inhouse"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Gross salaries"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Gross wages"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__has_message
msgid "Has Message"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Heating"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_3_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_1
msgid "I. Intangible assets"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_4_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_1
msgid "I. Stocks"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_1
msgid "I. Subscribed capital"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_1_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_1_1
msgid "I. Subscribed capital not called"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"IC supply of goods to persons identified for VAT purposes in another Member "
"State (MS) (013)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"IC supply of new means of transport (Art. 43(1)(e)) to persons not "
"identified for VAT purposes who are established or reside in an other MS "
"(202)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__id
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report__id
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__id
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__id
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml__id
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__id
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__id
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__id
msgid "ID"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_4_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2
msgid "II. Debtors"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_2
msgid "II. Share premium account"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_1_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_1_2
msgid "II. Subscribed capital called but unpaid"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_3_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_2
msgid "II. Tangible assets"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_3_3
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_3
msgid "III. Financial assets"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_4_3
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_3
msgid "III. Investments"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_3
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_3
msgid "III. Revaluation reserve"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_4_4
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_4
msgid "IV. Cash at bank and in hand"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_4
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_4
msgid "IV. Reserves"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_has_error
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__import_notes_as_references
msgid "Import Notes As References"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Imports"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Imports other than manufactured tobacco"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Interest paid for long-term debts"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Interest paid for short-term debts"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Intra-Community"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Intra-Community acquisitions"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Intra-Community supply (457)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Intra-Community supply (457): amount still to be allocated"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Invoiced by other taxable persons for goods or services supplied (458)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Invoiced by other taxable persons for goods or services supplied (458): "
"amount still to be allocated"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Km travelled"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_partner__l10n_lu_is_representative
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_users__l10n_lu_is_representative
msgid "L10N Lu Is Representative"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__l10n_lu_stored_report_ids
msgid "L10N Lu Stored Report"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "LPG"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_accounts_report__size__large
msgid "Large-Sized Undertaking"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report____last_update
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report____last_update
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report____last_update
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report____last_update
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml____last_update
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures____last_update
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report____last_update
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__write_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report__write_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__write_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__write_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml__write_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__write_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__write_uid
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__write_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report__write_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__write_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__write_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml__write_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__write_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__write_date
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Leasing / renting"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Lessor (A18a) details"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Licensing (cabaretage) tax and other taxes"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.actions.server,name:l10n_lu_reports.ir_cron_load_xsd_file_ir_actions_server
#: model:ir.cron,cron_name:l10n_lu_reports.ir_cron_load_xsd_file
#: model:ir.cron,name:l10n_lu_reports.ir_cron_load_xsd_file
msgid "Load XSD File (Luxembourg Electronic Accounting Reports)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_partner_form
msgid "Luxembourg"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_ec_sales_report_handler
msgid "Luxembourgish EC Sales Report Custom Handler"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_report_handler
msgid "Luxembourgish Financial Report Custom Handler"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_tax_report_handler
msgid "Luxembourgish Tax Report Custom Handler"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report,name:l10n_lu_reports.lux_ec_sales_report
msgid "Luxemburgese EC sales Report"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.EcSalesLuXMLReport
msgid "MODL5"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_main_attachment_id
msgid "Main Attachment"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Manufactured tobacco"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Manufactured tobacco in stock"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_company__matr_number
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_partner__l10n_lu_agent_matr_number
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_users__l10n_lu_agent_matr_number
msgid "Matr Number"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_accounts_report__size__medium
msgid "Medium-Sized Undertaking"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_ids
msgid "Messages"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_tax_report__period__m
msgid "Monthly"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Motor vehicles"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_res_partner__l10n_lu_agent_matr_number
#: model:ir.model.fields,help:l10n_lu_reports.field_res_users__l10n_lu_agent_matr_number
msgid ""
"National ID number of the accounting firm (agent company) acting as the "
"declarer in eCDF declarations"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Net profit margin (special arrangement: Art. 56ter-1 and 56ter-2)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "New acquisitions (tools and equipment)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Non-business expenditures for motor vehicules"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Non-business part of expenditures for motor vehicules (VAT excluded)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Non-business portion (in %)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Non-business portion (km)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Non-business use of goods and supply of services free of charge (456)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Non-business use of goods and supply of services free of charge (456): "
"amount still to be allocated"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_yearly_tax_report_manual__state__not_exported
msgid "Not exported"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Not falling within the scope of Art. 56ter-1 and 56ter-2"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Number of km travelled during the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_needaction_counter
msgid "Number of messages requiring action"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Occasional salaries"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Of which goods for resale"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Office expenses"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_generate_vat_intra_report
msgid "Old declarations to update/correct"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_assets_report.py:0
msgid ""
"Only assets having EUR currency for companies using EUR currency can be "
"reported."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/l10n_lu_yearly_tax_report_manual.py:0
msgid "Only one tax report data record per year (per company) is allowed!"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__optional_remarks
msgid "Optional Remarks"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__other_acquisitions
msgid "Other Acquisitions"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Other Assimilated Supplies"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__other_imports
msgid "Other Imports"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__other_purchases
msgid "Other Purchases"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Other assets:"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Other financial costs"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Other repairs"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Other:"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Packaging"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Papers and periodicals for business purposes"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Payments made on account by clients (VAT excluded)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__period
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__period
msgid "Period"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__phone_number
msgid "Phone number for contacting the declaring person"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__pl
msgid "Pl"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_general_ledger.py:0
msgid ""
"Please define `Internal Reference` for below products:\n"
"`%s`."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_financial_report.py:0
msgid "Please set valid eCDF Prefix for your company."
msgstr ""

#. module: l10n_lu_reports
#: model:account.report,name:l10n_lu_reports.account_financial_report_l10n_lu_pl
#: model:ir.actions.client,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_action
msgid "Profit & Loss"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr
#: model:ir.actions.client,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_action
msgid "Profit & Loss (abridged)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_generate_accounts_report
msgid "Profit and loss report version"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Property tax"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Public and professional third party liability insurance"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Purchases of goods abroad which are not brought to Luxembourg"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Purchases of goods which don't give rise to a chargeable event neither for "
"the supplier nor for the taxable person acquiring the goods"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Purchases of goods which give rise to a chargeable event for the supplier or"
" for the taxable person acquiring the goods"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Purchases other than manufactured tobacco"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Purchases within the country"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_tax_report__period__t
msgid "Quarterly"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_res_partner__l10n_lu_agent_rcs_number
#: model:ir.model.fields,help:l10n_lu_reports.field_res_users__l10n_lu_agent_rcs_number
msgid ""
"RCS (Régistre de Commerce et des Sociétés) of the accounting firm (agent "
"company) acting as the declarer in eCDF declarations"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Receivables from clients (VAT excluded)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Renting/leasing of immovable property with VAT"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Renting/leasing of immovable property with no VAT"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Renting/leasing of installed equipment and machinery"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Repair and maintenance of equipment and machinery"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Repairs and servicing"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Repartition fields for the annual tax report"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_001
msgid "Report Section 001"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_002
msgid "Report Section 002"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_003
msgid "Report Section 003"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_004
msgid "Report Section 004"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_005
msgid "Report Section 005"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_007
msgid "Report Section 007"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_008
msgid "Report Section 008"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_009
msgid "Report Section 009"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_010
msgid "Report Section 010"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_011
msgid "Report Section 011"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_013
msgid "Report Section 013"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_077
msgid "Report Section 077"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_078
msgid "Report Section 078"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_079
msgid "Report Section 079"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_081
msgid "Report Section 081"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_082
msgid "Report Section 082"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_083
msgid "Report Section 083"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_085
msgid "Report Section 085"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_086
msgid "Report Section 086"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_087
msgid "Report Section 087"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_098
msgid "Report Section 098"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_099
msgid "Report Section 099"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_100
msgid "Report Section 100"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_106
msgid "Report Section 106"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_107
msgid "Report Section 107"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_114
msgid "Report Section 114"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_115
msgid "Report Section 115"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_116
msgid "Report Section 116"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_117
msgid "Report Section 117"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_118
msgid "Report Section 118"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_119
msgid "Report Section 119"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_120
msgid "Report Section 120"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_121
msgid "Report Section 121"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_124
msgid "Report Section 124"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_128
msgid "Report Section 128"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_129
msgid "Report Section 129"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_130
msgid "Report Section 130"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_131
msgid "Report Section 131"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_134
msgid "Report Section 134"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_136
msgid "Report Section 136"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_137
msgid "Report Section 137"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_138
msgid "Report Section 138"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_139
msgid "Report Section 139"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_142
msgid "Report Section 142"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_144
msgid "Report Section 144"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_145
msgid "Report Section 145"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_146
msgid "Report Section 146"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_147
msgid "Report Section 147"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_148
msgid "Report Section 148"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_149
msgid "Report Section 149"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_150
msgid "Report Section 150"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_151
msgid "Report Section 151"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_153
msgid "Report Section 153"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_154
msgid "Report Section 154"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_155
msgid "Report Section 155"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_158
msgid "Report Section 158"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_162
msgid "Report Section 162"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_163
msgid "Report Section 163"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_164
msgid "Report Section 164"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_165
msgid "Report Section 165"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_166
msgid "Report Section 166"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_167
msgid "Report Section 167"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_168
msgid "Report Section 168"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_171
msgid "Report Section 171"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_175
msgid "Report Section 175"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_176
msgid "Report Section 176"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_177
msgid "Report Section 177"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_178
msgid "Report Section 178"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_180
msgid "Report Section 180"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_181
msgid "Report Section 181"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_183
msgid "Report Section 183"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_184
msgid "Report Section 184"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_185
msgid "Report Section 185"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_186
msgid "Report Section 186"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_187
msgid "Report Section 187"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_188
msgid "Report Section 188"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_189
msgid "Report Section 189"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_190
msgid "Report Section 190"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_191
msgid "Report Section 191"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_192
msgid "Report Section 192"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_193
msgid "Report Section 193"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_197
msgid "Report Section 197"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_198
msgid "Report Section 198"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_199
msgid "Report Section 199"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_200
msgid "Report Section 200"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_201
msgid "Report Section 201"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_202
msgid "Report Section 202"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_203
msgid "Report Section 203"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_206
msgid "Report Section 206"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_229
msgid "Report Section 229"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_239
msgid "Report Section 239"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_240
msgid "Report Section 240"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_241
msgid "Report Section 241"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_242
msgid "Report Section 242"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_243
msgid "Report Section 243"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_244
msgid "Report Section 244"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_245
msgid "Report Section 245"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_246
msgid "Report Section 246"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_247
msgid "Report Section 247"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_248
msgid "Report Section 248"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_249
msgid "Report Section 249"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_250
msgid "Report Section 250"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_251
msgid "Report Section 251"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_252
msgid "Report Section 252"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_253
msgid "Report Section 253"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_254
msgid "Report Section 254"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_255
msgid "Report Section 255"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_256
msgid "Report Section 256"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_257
msgid "Report Section 257"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_258
msgid "Report Section 258"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_259
msgid "Report Section 259"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_260
msgid "Report Section 260"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_261
msgid "Report Section 261"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_262
msgid "Report Section 262"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_263
msgid "Report Section 263"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_264
msgid "Report Section 264"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_265
msgid "Report Section 265"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_266
msgid "Report Section 266"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_267
msgid "Report Section 267"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_268
msgid "Report Section 268"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_269
msgid "Report Section 269"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_270
msgid "Report Section 270"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_271
msgid "Report Section 271"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_272
msgid "Report Section 272"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_273
msgid "Report Section 273"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_274
msgid "Report Section 274"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_275
msgid "Report Section 275"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_276
msgid "Report Section 276"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_277
msgid "Report Section 277"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_278
msgid "Report Section 278"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_279
msgid "Report Section 279"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_280
msgid "Report Section 280"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_281
msgid "Report Section 281"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_282
msgid "Report Section 282"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_283
msgid "Report Section 283"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_284
msgid "Report Section 284"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_285
msgid "Report Section 285"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_286
msgid "Report Section 286"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_287
msgid "Report Section 287"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_288
msgid "Report Section 288"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_289
msgid "Report Section 289"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_290
msgid "Report Section 290"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_291
msgid "Report Section 291"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_292
msgid "Report Section 292"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_293
msgid "Report Section 293"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_294
msgid "Report Section 294"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_295
msgid "Report Section 295"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_296
msgid "Report Section 296"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_297
msgid "Report Section 297"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_298
msgid "Report Section 298"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_299
msgid "Report Section 299"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_300
msgid "Report Section 300"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_301
msgid "Report Section 301"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_302
msgid "Report Section 302"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_303
msgid "Report Section 303"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_304
msgid "Report Section 304"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_305
msgid "Report Section 305"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_306
msgid "Report Section 306"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_307
msgid "Report Section 307"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_308
msgid "Report Section 308"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_309
msgid "Report Section 309"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_310
msgid "Report Section 310"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_311
msgid "Report Section 311"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_312
msgid "Report Section 312"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_313
msgid "Report Section 313"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_314
msgid "Report Section 314"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_315
msgid "Report Section 315"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_316
msgid "Report Section 316"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_317
msgid "Report Section 317"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_318
msgid "Report Section 318"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_319
msgid "Report Section 319"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_320
msgid "Report Section 320"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_321
msgid "Report Section 321"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_322
msgid "Report Section 322"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_323
msgid "Report Section 323"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_324
msgid "Report Section 324"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_325
msgid "Report Section 325"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_326
msgid "Report Section 326"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_327
msgid "Report Section 327"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_328
msgid "Report Section 328"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_329
msgid "Report Section 329"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_330
msgid "Report Section 330"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_331
msgid "Report Section 331"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_332
msgid "Report Section 332"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_333
msgid "Report Section 333"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_334
msgid "Report Section 334"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_335
msgid "Report Section 335"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_336
msgid "Report Section 336"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_337
msgid "Report Section 337"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_338
msgid "Report Section 338"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_343
msgid "Report Section 343"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_344
msgid "Report Section 344"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_345
msgid "Report Section 345"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_346
msgid "Report Section 346"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_347
msgid "Report Section 347"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_348
msgid "Report Section 348"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_349
msgid "Report Section 349"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_350
msgid "Report Section 350"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_351
msgid "Report Section 351"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_352
msgid "Report Section 352"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_353
msgid "Report Section 353"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_354
msgid "Report Section 354"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_355
msgid "Report Section 355"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_356
msgid "Report Section 356"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_357
msgid "Report Section 357"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_358
msgid "Report Section 358"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_359
msgid "Report Section 359"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_361
msgid "Report Section 361"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_362
msgid "Report Section 362"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_363
msgid "Report Section 363"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_364
msgid "Report Section 364"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_365
msgid "Report Section 365"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_366
msgid "Report Section 366"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_367
msgid "Report Section 367"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_368
msgid "Report Section 368"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_369
msgid "Report Section 369"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_372
msgid "Report Section 372"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_373
msgid "Report Section 373"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_374
msgid "Report Section 374"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_375
msgid "Report Section 375"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_376
msgid "Report Section 376"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_377
msgid "Report Section 377"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_378
msgid "Report Section 378"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_379
msgid "Report Section 379"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_380
msgid "Report Section 380"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_381
msgid "Report Section 381"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_382
msgid "Report Section 382"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_383
msgid "Report Section 383"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_384
msgid "Report Section 384"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_385
msgid "Report Section 385"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_386
msgid "Report Section 386"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_387
msgid "Report Section 387"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_388
msgid "Report Section 388"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_389
msgid "Report Section 389"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_394
msgid "Report Section 394"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_396
msgid "Report Section 396"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_397
msgid "Report Section 397"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_398
msgid "Report Section 398"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_399
msgid "Report Section 399"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_400
msgid "Report Section 400"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_401
msgid "Report Section 401"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_402
msgid "Report Section 402"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_404
msgid "Report Section 404"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_405
msgid "Report Section 405"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_406
msgid "Report Section 406"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_414
msgid "Report Section 414"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_415
msgid "Report Section 415"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_455
msgid "Report Section 455"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_455_rest
msgid "Report Section 455 Rest"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_456
msgid "Report Section 456"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_456_rest
msgid "Report Section 456 Rest"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_457
msgid "Report Section 457"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_457_rest
msgid "Report Section 457 Rest"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_458
msgid "Report Section 458"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_458_rest
msgid "Report Section 458 Rest"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_459
msgid "Report Section 459"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_459_rest
msgid "Report Section 459 Rest"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_460
msgid "Report Section 460"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_460_rest
msgid "Report Section 460 Rest"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_461
msgid "Report Section 461"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_461_rest
msgid "Report Section 461 Rest"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_472
msgid "Report Section 472"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_472_rest
msgid "Report Section 472 Rest"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_771
msgid "Report Section 771"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_772
msgid "Report Section 772"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_773
msgid "Report Section 773"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_774
msgid "Report Section 774"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_776
msgid "Report Section 776"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_777
msgid "Report Section 777"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_778
msgid "Report Section 778"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_781
msgid "Report Section 781"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_782
msgid "Report Section 782"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_783
msgid "Report Section 783"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_791
msgid "Report Section 791"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_792
msgid "Report Section 792"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_793
msgid "Report Section 793"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_794
msgid "Report Section 794"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_795
msgid "Report Section 795"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_796
msgid "Report Section 796"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_797
msgid "Report Section 797"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_section_798
msgid "Report Section 798"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__report_data
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_asset_report__report_data
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__report_data
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__report_data
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_xml__report_data
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__report_data
msgid "Report file"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.l10n_lu_stored_intra_report_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.l10n_lu_yearly_tax_report_manual_view_tree
msgid "Reports"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Reset to Processing"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Sales / Receipts (472): amount still to be allocated"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Sales and Receipts"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Sales during the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Services supplied by the declaring person as a registered taxpayer in "
"Luxembourg"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Services to be declared and taxed by the registered taxpayer in Luxembourg"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Shipping and transport expenses"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_tax_report__simplified_declaration
msgid "Simplified Declaration"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_accounts_report__size
msgid "Size"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_generate_accounts_report
msgid ""
"Size (criteria specified by Articles 35 and 47 of the amended law of "
"December 19th 2002)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_generate_accounts_report__size__small
msgid "Small-Sized Undertaking"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_xml.py:0
msgid ""
"Some fields required for the export are missing or invalid. Please verify "
"them."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid ""
"Some problems with the comparison declarations occurred. The comparison "
"declarations must refer to earlier periods."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_financial_report.py:0
msgid "Some references are not in the requested format (max. 10 characters):"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Staff travel and representation expenses"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Start of financial year"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__state
msgid "Status"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__stock
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Stock"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Stock and business equipment insurance"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Stock for resale"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Stock not falling within the scope"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_generate_vat_intra_report__save_report
msgid "Store generated report"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Supplies carried out within the scope of the special arrangement"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_stored_intra_report__codes__lts
msgid ""
"Supply of goods (normal/in the context of triangular operations) and supply "
"of services"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_stored_intra_report__codes__lt
msgid ""
"Supply of goods and supply of goods made in the context of triangular "
"operations"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Supply of goods carried out to customers not identified for VAT"
"                                             purposes and established in the"
" MS where the dispatch ends, when these supplies are taxable in that MS"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Supply of goods not manufactured inhouse"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Supply of inhouse manufactured goods"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields.selection,name:l10n_lu_reports.selection__l10n_lu_stored_intra_report__codes__s
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Supply of services"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Supply of services carried out free of charge for purposes other than those "
"of the business (011)"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_6
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_6
msgid "TOTAL (ASSETS)"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_5
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_5
msgid "TOTAL (CAPITAL, RESERVES AND LIABILITIES)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "TOTAL if applicable VAT included"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.saft_template_inherit_l10n_lu_saft
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.tax_information_inherit_l10n_lu_saft
msgid "TVA-"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Tax"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.column,name:l10n_lu_reports.account_financial_report_ec_sales_tax
msgid "Tax Code"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_reports_report_appendix_expenditures__report_id
msgid "Tax Report"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Taxable amount (net of VAT)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__books_records_documents
msgid ""
"Taxable persons established in Luxembourg: place of storage of books, "
"records and documents the keeping, drafting and issuing of which are "
"required by the modified VAT law of 12 February 1979 and its implementing "
"provisions, when this place of storage is outside of the territory of "
"Luxemburg (Art. 65)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.saft_template_inherit_l10n_lu_saft
msgid "Taxe sur la valeur ajoutée"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Telecommunications"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/res_partner.py:0
msgid ""
"The Agent's ECDF Prefix is not valid. There should be exactly 6 characters."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/res_partner.py:0
msgid ""
"The Agent's Matr. Number is not valid. There should be between 11 and 13 "
"digits."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_xml.py:0
msgid ""
"The ECDF Prefix hasn't been defined. Please add the ECDF prefix in the "
"company's information."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/res_company.py:0
msgid ""
"The company's ECDF Prefix is not valid. There should be exactly 6 "
"characters."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_xml.py:0
msgid ""
"The company's Matr. Number hasn't been defined. Please configure it in the "
"company's information."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/res_company.py:0
msgid ""
"The company's Matr. Number is not valid. There should be between 11 and 13 "
"digits."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_tax_report.py:0
msgid ""
"The field 010 in 'Other Assimilated Supplies' is mandatory if you fill in "
"the field 389 in 'Appendix B'. Field 010 must be equal to field 389"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_tax_report.py:0
msgid "The field 369 must be smaller than field 368 (Appendix B)."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_tax_report.py:0
msgid ""
"The field 387 must be filled in if field 388 is filled in (Appendix B)."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_tax_report.py:0
msgid ""
"The field 388 must be filled in if field 387 is filled in (Appendix B)."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/l10n_lu_yearly_tax_report_manual.py:0
msgid ""
"The fiscal country of your active company is not Luxembourg. This export is "
"not available for other countries."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_tax_report.py:0
msgid ""
"The fiscal period you have selected is invalid, please verify. Valid periods"
" are : Month, Quarter and Year."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_tax_report.py:0
msgid "The following monthly fields haven't been completely allocated yet: "
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid ""
"The following must be set on your company:\n"
"- %s"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid "The provided comparison file is not a proper eCDF XML declaration!"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid "The provided comparison file is not a properly formatted XML."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_sales_report.py:0
msgid ""
"The report can't be saved, because it isn't a valid eCDF declaration. Either"
" both 'L' and 'T' codes should be selected, or none of them"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_generic_tax_report.py:0
msgid ""
"The selected period is not supported for the selected declaration type."
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid ""
"There are no Intracommunity VAT declarations for the declaring company in "
"the provided file!"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Total"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Total 'Appendix to Operational Expenditures'"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Total Input Tax"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Total Sales / Receipts (472)"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Total entry of stock for business purposes"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Total of expenses during the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Total stock and tobacco"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Tyres, etc."
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid ""
"Use of goods considered business assets for purposes other than those of the"
" business (010)"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_5_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_5_pre_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_5_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_5_pre_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_5
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_5
msgid "V. Profit or loss brought forward"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid "VAT"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.column,name:l10n_lu_reports.account_financial_report_ec_sales_vat
msgid "VAT Number"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "VAT excluded"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "VAT on capital expenditures"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "VAT on operational expenditures"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "VAT on stock entries"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_6_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_6_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_6
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_6
msgid "VI. Profit or loss for the financial year"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_7
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_7
msgid "VII. Interim dividends"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_1_8
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_8
msgid "VIII. Capital investment subsidies"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Values"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/wizard/l10n_lu_generate_xml.py:0
msgid "Verify"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Water"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Work clothes"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "Work in progress (VAT excluded)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model,name:l10n_lu_reports.model_l10n_lu_stored_intra_report
msgid "Wrapper for an attachment, adds the financial report data"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_assets_report.py:0
#: code:addons/l10n_lu_reports/models/account_assets_report.py:0
#: code:addons/l10n_lu_reports/models/account_financial_report.py:0
#: code:addons/l10n_lu_reports/models/account_general_ledger.py:0
#: code:addons/l10n_lu_reports/models/account_generic_tax_report.py:0
#: code:addons/l10n_lu_reports/models/account_generic_tax_report.py:0
#: code:addons/l10n_lu_reports/models/account_sales_report.py:0
msgid "XML"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_stored_intra_report__year
#: model:ir.model.fields,field_description:l10n_lu_reports.field_l10n_lu_yearly_tax_report_manual__year
msgid "Year"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/l10n_lu_yearly_tax_report_manual.py:0
msgid "Yearly Tax Report Manual Data"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_1_1
msgid "a) Convertible loans"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "a) Name"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_5_1_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_5_1
msgid "a) Raw materials and consumables"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_8_1
msgid "a) Tax authorities"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_1_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_1
msgid "a) Wages and salaries"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_1_2_1
msgid ""
"a) acquired for valuable consideration and need not be shown under C.I.3"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_4_2_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_3_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_1_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_2_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_3_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_4_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_2_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_3_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_4_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_5_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_6_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_7_1
msgid "a) becoming due and payable within one year"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_14_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_14_1_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_14_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_14_1
msgid "a) concerning affiliated undertakings"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_10_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_11_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_9_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_10_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_11_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_9_1_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_10_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_11_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_9_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_10_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_11_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_9_1
msgid "a) derived from affiliated undertakings"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_7_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_7_1_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_7_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_7_1
msgid ""
"a) in respect of formation expenses and of tangible and intangible fixed "
"assets"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_4_4_1
msgid "a) other available reserves"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "b) Address"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_1_2
msgid "b) Non convertible loans"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_5_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_5_2
msgid "b) Other external expenses"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_8_2
msgid "b) Social security authorities"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_2_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_2
msgid "b) Social security costs"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_1_4_2_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_abr_line_2_3_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_1_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_2_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_3_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_4_2_4_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_2_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_3_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_4_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_5_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_6_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_7_2
msgid "b) becoming due and payable after more than one year"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_1_3_1_2_2
msgid "b) created by the undertaking itself"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_7_2_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_7_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_7_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_7_2
msgid "b) in respect of current assets"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_9_2_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_9_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_9_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_9_2
msgid "b) other income from participating interests"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_10_2_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_10_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_10_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_10_2
msgid "b) other income not included under a)"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_14_2_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_14_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_14_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_14_2
msgid "b) other interest and similar expenses"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_11_2_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_11_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_11_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_11_2
msgid "b) other interest and similar income"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_1_4_4_2
msgid "b) other non available reserves"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_8_3
msgid "c) Other creditors"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_3_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_3_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_3
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_3
msgid "c) Other staff costs"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,field_description:l10n_lu_reports.field_res_company__ecdf_prefix
msgid "eCDF Prefix"
msgstr ""

#. module: l10n_lu_reports
#: code:addons/l10n_lu_reports/models/account_financial_report.py:0
msgid ""
"eCDF Prefix `{0}` associated with `{1}` company is invalid.\n"
"The expected format is ABCD12 (Six digits of numbers or capital letters)"
msgstr ""

#. module: l10n_lu_reports
#: model:ir.model.fields,help:l10n_lu_reports.field_res_partner__l10n_lu_agent_ecdf_prefix
#: model:ir.model.fields,help:l10n_lu_reports.field_res_users__l10n_lu_agent_ecdf_prefix
msgid ""
"eCDF prefix (identifier) of the accounting firm (agent company) acting as "
"the declarer in eCDF declarations"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_1_1_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_1_2_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_8_3_1
msgid "i) becoming due and payable within one year"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_2_1_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_2_1_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_2_1
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_2_1
msgid "i) relating to pensions"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_1_1_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_1_2_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_bs_line_2_3_8_3_2
msgid "ii) becoming due and payable after more than one year"
msgstr ""

#. module: l10n_lu_reports
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_2_2_balance
#: model:account.report.expression,report_line_name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_2_2_balance
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_abr_line_6_2_2
#: model:account.report.line,name:l10n_lu_reports.account_financial_report_l10n_lu_pl_line_6_2_2
msgid "ii) other social security costs"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "of VAT invoiced"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "of which productive salaries"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 12 %"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 12%"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 14 %"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 14%"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 17 %"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 17%"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 3 %"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 3%"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 8 %"
msgstr ""

#. module: l10n_lu_reports
#: model_terms:ir.ui.view,arch_db:l10n_lu_reports.view_l10n_lu_yearly_tax_report_manual
msgid "rate of 8%"
msgstr ""
