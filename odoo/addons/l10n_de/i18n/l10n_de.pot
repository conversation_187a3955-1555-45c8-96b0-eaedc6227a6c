# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_de
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~17.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-25 14:42+0000\n"
"PO-Revision-Date: 2024-02-25 14:42+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_de
#. odoo-python
#: code:addons/l10n_de/models/ir_attachment.py:0
#, python-format
msgid "%(attachment_name)s (detached by %(user)s on %(date)s)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_21
msgid "21. non-taxable other services (line 34)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_35
msgid "35. at other tax rates (line 15)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_36
msgid "36. at other tax rates (line 15)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_37_74
msgid "37. Reduction of deductible input tax amounts (line 51)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_39
msgid ""
"39. deduction of the special advance payment for the extension of the "
"standing period (line 48)"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,help:l10n_de.field_account_tax__l10n_de_datev_code
msgid "4 digits code use by Datev"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_41
msgid "41. to customer with VAT number (line 18)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_42
msgid "42. triangular transactions (line 32)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_43
msgid "43. other tax-exempt transactions with input tax deduction (line 21)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_44
msgid "44. new vehicles to customers without VAT number (line 19)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_45
msgid "45. other non-taxable transactions (line 35)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_48
msgid ""
"46. other taxable supplies by a trader established in the rest of the "
"Community (line 29)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_47
msgid ""
"47. other taxable supplies by a trader established in the rest of the "
"Community (line 29)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_24
msgid "48. tax-exempt transactions without input tax deduction (line 22)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_49
msgid "49. new vehicles outside a company (line 20)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_50
msgid "50. Reduction of the tax base (line 50)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_59
msgid ""
"59. input tax deduction for intra-Community supplies of new vehicles outside"
" a business (line 42)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_60
msgid ""
"60. other taxable transactions for which the recipient of the service is "
"liable for the tax in accordance with Section 13b (5) UStG (line 33)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_61
msgid ""
"61. input tax amounts from the intra-Community acquisition of goods (line "
"38)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_62
msgid "62. import turnover tax incurred (line 39)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_63
msgid ""
"63. input tax amounts calculated according to general average rates (line "
"41)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_64
msgid "64. adjustment of the input tax deduction (line 43)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_65
msgid ""
"65. Tax due to change in the form of taxation and after-tax on taxed "
"prepayments and similar due to change in tax rate (line 45)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_66
msgid "66. input tax amounts from invoices from other traders (line 37)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_67
msgid ""
"67. input tax amounts from services within the meaning of § 13b UStG (line "
"40)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_69
msgid ""
"69. tax amounts shown incorrectly or unjustifiably in invoices (line 46)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_73
msgid ""
"73. supplies of goods transferred by way of security and transactions "
"falling under the GrEStG (line 30)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_74
msgid ""
"74. supplies of goods transferred by way of security and transactions "
"falling under the GrEStG (line 30)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_76
msgid "76. transactions for which tax is payable under § 24 UStG (line 17)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_77
msgid ""
"77. supplies of agricultural and forestry operations according to § 24 UStG "
"to customers with VAT identification number (line 16)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_80
msgid "80. turnover for which tax is payable under § 24 UStG (line 17)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_26
#: model:account.report.line,name:l10n_de.tax_report_de_tag_81
msgid "81. at the tax rate of 19 % (line 12)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_83
msgid "83. remaining advance payment of sales tax (line 49)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_84
msgid "84. other benefits (line 31)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_85
msgid "85. other benefits (line 31)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_27
#: model:account.report.line,name:l10n_de.tax_report_de_tag_86
msgid "86. at the tax rate of 7 % (line 13)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_87
msgid "87. at the tax rate of 0 % (line 14)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_33
msgid "89. at the tax rate of 19 % (line 24)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_89
msgid "89. taxable intra-Community acquisitions at the rate of 19 % (line 24)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_90
msgid "90. at the tax rate of 0 % (line 26)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_91
msgid "91. tax-free intra-Community acquisitions (line 23)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_34
#: model:account.report.line,name:l10n_de.tax_report_de_tag_93
msgid "93. at the tax rate of 7 % (line 25)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_94
msgid "94. new vehicles from suppliers without (line 28)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_95
msgid "95. at other tax rates (line 27)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_96
msgid ""
"96. new vehicles from suppliers without VAT number at the general tax rate "
"(line 28)"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_98
msgid "98. at other tax rates (line 27)"
msgstr ""

#. module: l10n_de
#: model_terms:ir.ui.view,arch_db:l10n_de.view_account_secure_entries_wizard
msgid ""
"<i>By securing entries, you make them unchangeable.</i>\n"
"                        <br/>\n"
"                        <i>This is required by law to ensure complete and traceable bookkeeping in accordance with GoBD.</i>"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_account
msgid "Account"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_01
msgid "Assessment basis"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_ir_attachment
msgid "Attachment"
msgstr ""

#. module: l10n_de
#: model:account.report.column,name:l10n_de.tax_report_balance
msgid "Balance"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_I_1
msgid ""
"Balance sheet active: A I 1-Self-generated industrial property rights and "
"similar rights and active"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_I_2
msgid ""
"Balance sheet active: A I 2-Concessions, licences and similar rights and "
"active"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_I_3
msgid "Balance sheet active: A I 3-Goodwill"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_I_4
msgid "Balance sheet active: A I 4-Payments on account"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_II_1
msgid ""
"Balance sheet active: A II 1-Property. rights equivalent to real property "
"and buildings"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_II_2
msgid "Balance sheet active: A II 2-Technical equipment and machinery"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_II_3
msgid ""
"Balance sheet active: A II 3-Other active. Operating and office equipment"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_II_4
msgid ""
"Balance sheet active: A II 4-Payments on account and active under "
"construction"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_III_1
msgid "Balance sheet active: A III 1-Shares in affiliated companies"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_III_2
msgid "Balance sheet active: A III 2-Loans to affiliated companies"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_III_3
msgid "Balance sheet active: A III 3-Investments"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_III_4
msgid ""
"Balance sheet active: A III 4-Loans to companies in which participations are"
" held"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_III_5
msgid "Balance sheet active: A III 5-Securities held as fixed active"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_A_III_6
msgid "Balance sheet active: A III 6-Other loans"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_I_1
msgid "Balance sheet active: B I 1-Raw materials and supplies"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_I_2
msgid "Balance sheet active: B I 2-Finished goods, work in progress"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_I_3
msgid "Balance sheet active: B I 3-Finished goods and merchandise"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_I_4
msgid "Balance sheet active: B I 4-Payments on account"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_II_1
msgid "Balance sheet active: B II 1-Trade receivables"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_II_2
msgid "Balance sheet active: B II 2-Receivables from affiliated companies"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_II_3
msgid ""
"Balance sheet active: B II 3-Receivables from companies in which "
"participations are held"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_II_4
msgid "Balance sheet active: B II 4-Other active"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_III_1
msgid "Balance sheet active: B III 1-Shares in affiliated companies"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_III_2
msgid "Balance sheet active: B III 2-Other securities"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_B_IV
msgid ""
"Balance sheet active: B IV cash on hand, Bundesbank balances, bank balances "
"and cheques"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_C
msgid "Balance sheet active: C accruals and deferrals"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_D
msgid "Balance sheet active: D-Deferred tax active"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_asset_bs_E
msgid "Balance sheet active: E-Active difference from asset offsetting"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_A_I
msgid "Balance sheet passive: A I-Subscribed capital"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_A_II
msgid "Balance sheet passive: A II capital reserve"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_A_III_1
msgid "Balance sheet passive: A III 1-Legal reserve"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_A_III_2
msgid ""
"Balance sheet passive: A III 2-Reserve for shares in a controlling or "
"majority-owned company"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_A_III_3
msgid "Balance sheet passive: A III 3-Statutory reserves"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_A_III_4
msgid "Balance sheet passive: A III 4-Other revenue reserves"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_A_IV
msgid "Balance sheet passive: A IV profit/loss carried forward"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_A_V
msgid "Balance sheet passive: A V net profit/loss for the year"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_B_1
msgid ""
"Balance sheet passive: B 1-Provisions for pensions and similar obligations"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_B_2
msgid "Balance sheet passive: B 2 tax provisions"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_B_3
msgid "Balance sheet passive: B 3-Other provisions"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_C_1
msgid "Balance sheet passive: C 1 bonds, of which convertible"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_C_2
msgid "Balance sheet passive: C 2-Payables to credit institutions"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_C_3
msgid "Balance sheet passive: C 3-Payments received on account of orders"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_C_4
msgid "Balance sheet passive: C 4-trade payables"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_C_5
msgid ""
"Balance sheet passive: C 5-passive from the acceptance of bills of exchange "
"drawn and the issue of own bills of exchange"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_C_6
msgid "Balance sheet passive: C 6-Payables to affiliated companies"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_C_7
msgid ""
"Balance sheet passive: C 7 passive to companies in which participations are "
"held"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_C_8
msgid ""
"Balance sheet passive: C 8-Other passive, of which from taxes, of which in "
"the context of social security"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_D
msgid "Balance sheet passive: D-accruals and deferrals"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_liabilities_bs_E
msgid "Balance sheet passive: E-Deferred tax passive"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,help:l10n_de.field_res_company__l10n_de_widnr
msgid "Business identification number."
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_17
#: model:account.report.line,name:l10n_de.tax_report_de_tax_tag_17
msgid "Declaration of the advance payment of turnover tax"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tax_tag_55
msgid "Deductible input tax amounts"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_01
msgid "G&V: 1-Turnover revenue"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_10
msgid ""
"G&V: 10-Income from other securities and loans held as financial assets"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_11
msgid "G&V: 11-Other interest and similar income"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_12
msgid ""
"G&V: 12-Depreciations on financial assets and on securities held as current "
"assets"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_13
msgid "G&V: 13-Interest and similar expenses"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_14
msgid "G&V: 14-Taxes on income and earnings"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_15
msgid "G&V: 15-Other taxes"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_02
msgid ""
"G&V: 2-Increase or decrease in inventories of finished goods and work in "
"progress"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_03
msgid "G&V: 3-Other own work capitalised"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_04
msgid "G&V: 4-Other operating income"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_05
msgid "G&V: 5-Material Effort"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_06
msgid "G&V: 6-Personnel costs"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_07
msgid "G&V: 7-Depreciations"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_08_1
msgid "G&V: 8.1-Spatial costs"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_08_2
msgid "G&V: 8.2-Insurances, contributions and levies"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_08_3
msgid "G&V: 8.3-Repairs and maintenance"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_08_4
msgid "G&V: 8.4-Vehicle costs"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_08_5
msgid "G&V: 8.5-Advertising and travel expenses"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_08_6
msgid "G&V: 8.6-Cost of goods delivery"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_08_7
msgid "G&V: 8.7-miscellaneous operating costs"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_pl_09
msgid "G&V: 9-Income from participations"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_18
#: model:account.report.line,name:l10n_de.tax_report_de_tax_tag_18
msgid "Goods and services"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_31
#: model:account.report.line,name:l10n_de.tax_report_de_tax_tag_31
msgid "Intra-Community acquisitions"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_intracom_community_delivery
msgid "Intra-Community supply"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_account_tax__l10n_de_datev_code
msgid "L10N De Datev Code"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_intracom_community_supplies
msgid "Other services"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tax_tag_64
msgid "Other tax amounts"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_product_template
msgid "Product"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tax_tag_46
msgid "Recipient of the service as the person liable to pay tax"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_71
msgid "Reduction"
msgstr ""

#. module: l10n_de
#: model:ir.model,name:l10n_de.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_res_company__l10n_de_stnr
msgid "St.-Nr."
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_37
msgid "Supplementary information on turnover"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_02
#: model:ir.model,name:l10n_de.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_de
#: model:account.report,name:l10n_de.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,help:l10n_de.field_res_company__l10n_de_stnr
msgid ""
"Tax number. Scheme: ??FF0BBBUUUUP, e.g.: ************* "
"https://de.wikipedia.org/wiki/Steuernummer"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_25
msgid "Tax-exempt transactions with input tax deduction"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_19
#: model:account.report.line,name:l10n_de.tax_report_de_tax_tag_19
msgid "Taxable turnover"
msgstr ""

#. module: l10n_de
#: model:account.report.line,name:l10n_de.tax_report_de_tag_46
msgid "The recipient of the service as the person liable to pay tax"
msgstr ""

#. module: l10n_de
#: model:account.account.tag,name:l10n_de.tag_de_intracom_ABC
msgid "Triangular deals"
msgstr ""

#. module: l10n_de
#: model:ir.model.fields,field_description:l10n_de.field_res_company__l10n_de_widnr
msgid "W-IdNr."
msgstr ""

#. module: l10n_de
#. odoo-python
#: code:addons/l10n_de/models/account_account.py:0
#, python-format
msgid "You can not change the code of an account."
msgstr ""

#. module: l10n_de
#. odoo-python
#: code:addons/l10n_de/models/ir_attachment.py:0
#, python-format
msgid "You cannot remove parts of the audit trail."
msgstr ""

#. module: l10n_de
#. odoo-python
#: code:addons/l10n_de/models/res_company.py:0
#, python-format
msgid "Your company's SteuerNummer is not compatible with your state"
msgstr ""

#. module: l10n_de
#. odoo-python
#: code:addons/l10n_de/models/res_company.py:0
#, python-format
msgid "Your company's SteuerNummer is not valid"
msgstr ""
