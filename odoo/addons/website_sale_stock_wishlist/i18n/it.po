# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_stock_wishlist
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-bell\"/>\n"
"                        We'll notify you once the product is back in stock."
msgstr ""
"<i class=\"fa fa-bell\"/>\n"
"                        Ti avviseremo quando il prodotto sarà di nuovo disponibile."

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                Invalid email"
msgstr ""
"<i class=\"fa fa-times text-danger\"/>\n"
"                                E-mail non valida"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            Get notified when back in stock\n"
"                        </small>"
msgstr ""
"<small>\n"
"                            <i class=\"fa fa-envelope-o\"/>\n"
"                            Ricevi una notifica quando di nuovo disponibile\n"
"                        </small>"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid ""
"Add\n"
"                <span class=\"d-none d-md-inline\">to Cart</span>"
msgstr ""
"Aggiungi\n"
"                <span class=\"d-none d-md-inline\">al carrello</span>"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Add to wishlist"
msgstr "Aggiungi alla lista dei desideri"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Added to your wishlist"
msgstr "Aggiunto alla tua lista desideri"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_template
msgid "Product"
msgstr "Prodotto"

#. module: website_sale_stock_wishlist
#: model:ir.model,name:website_sale_stock_wishlist.model_product_wishlist
msgid "Product Wishlist"
msgstr "Lista dei desideri prodotto"

#. module: website_sale_stock_wishlist
#. odoo-javascript
#: code:addons/website_sale_stock_wishlist/static/src/xml/product_availability.xml:0
msgid "Save for later"
msgstr "Salva per dopo"

#. module: website_sale_stock_wishlist
#: model:ir.model.fields,field_description:website_sale_stock_wishlist.field_product_wishlist__stock_notification
msgid "Stock Notification"
msgstr "Notifica giacenze"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "Temporarily out of stock"
msgstr "Temporaneamente esaurito"

#. module: website_sale_stock_wishlist
#: model_terms:ir.ui.view,arch_db:website_sale_stock_wishlist.product_wishlist
msgid "<EMAIL>"
msgstr "<EMAIL>"
