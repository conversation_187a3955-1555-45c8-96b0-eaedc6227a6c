<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_mx" model="res.partner" forcecreate="1">
        <field name="name">ESCUELA KEMPER URGATE</field>
        <field name="vat">EKU9003173C9</field>
        <field name="street">Campobasso Norte 3206 - 9000</field>
        <field name="street2">Fraccionamiento Montecarlo</field>
        <field name="zip">20914</field>
        <field name="city">Jesús María</field>
        <field name="state_id" ref="base.state_mx_ags"/>
        <field name="country_id" ref="base.mx"/>
        <field name="phone">+52 ************</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.mxexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_mx" model="res.company" forcecreate="1">
        <field name="name">ESCUELA KEMPER URGATE</field>
        <field name="partner_id" ref="base.partner_demo_company_mx"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_mx')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_mx'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>mx</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_mx')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
