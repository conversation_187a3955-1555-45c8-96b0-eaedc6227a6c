<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_dk" model="res.partner" forcecreate="1">
        <field name="name">DK Company</field>
        <field name="vat">DK58403288</field>
        <field name="street">G</field>
        <field name="city">Aalborg</field>
        <field name="country_id" ref="base.dk"/>

        <field name="zip">9430</field>
        <field name="phone">+45 32 12 34 56</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.dkexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_dk" model="res.company" forcecreate="1">
        <field name="name">DK Company</field>
        <field name="partner_id" ref="base.partner_demo_company_dk"/>
    </record>

    <record id="base.demo_bank_dk" model="res.partner.bank" forcecreate="1">
        <field name="acc_number">DK8250514116589944</field>
        <field name="partner_id" ref="base.partner_demo_company_dk"/>
        <field name="company_id" ref="base.demo_company_dk"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_dk')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_dk'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>dk</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_dk')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
