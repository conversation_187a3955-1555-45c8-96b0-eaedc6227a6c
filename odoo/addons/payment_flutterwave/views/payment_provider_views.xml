<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form" model="ir.ui.view">
        <field name="name">Flutterwave Provider Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment.payment_provider_form"/>
        <field name="arch" type="xml">
            <group name="provider_credentials" position="inside">
                <group invisible="code != 'flutterwave'"
                       name="flutterwave_credentials">
                    <field name="flutterwave_public_key"
                           string="Public Key"
                           required="code == 'flutterwave' and state != 'disabled'"/>
                    <field name="flutterwave_secret_key"
                           string="Secret Key"
                           required="code == 'flutterwave' and state != 'disabled'"
                           password="True"/>
                    <field name="flutterwave_webhook_secret"
                           string="Webhook Secret"
                           required="code == 'flutterwave' and state != 'disabled'"
                           password="True"/>
                </group>
            </group>
        </field>
    </record>

</odoo>
