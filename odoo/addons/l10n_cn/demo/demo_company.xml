<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_cn" model="res.partner" forcecreate="1">
        <field name="name">CN Company</field>
        <field name="vat"/>
        <field name="street">德政中路</field>
        <field name="city">大塘街道</field>
        <field name="country_id" ref="base.cn"/>
        <field name="state_id" ref="base.state_cn_MO"/>
        <field name="zip">510375</field>
        <field name="phone">+86 131 2345 6789</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.cnexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_cn" model="res.company" forcecreate="1">
        <field name="name">CN Company</field>
        <field name="partner_id" ref="base.partner_demo_company_cn"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_cn')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_cn'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>cn</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_cn')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
