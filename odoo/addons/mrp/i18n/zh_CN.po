# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp
# 
# Translators:
# <PERSON><PERSON> CHEN <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON>do<PERSON>哥 <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_state
msgid ""
" * Draft: The MO is not confirmed yet.\n"
" * Confirmed: The MO is confirmed, the stock rules and the reordering of the components are trigerred.\n"
" * In Progress: The production has started (on the MO or on the WO).\n"
" * To Close: The production is done, the MO has to be closed.\n"
" * Done: The MO is closed, the stock moves are posted. \n"
" * Cancelled: The MO has been cancelled, can't be confirmed anymore."
msgstr ""
" * 草稿：制造订单（MO）尚未确认。\n"
"* 已确认：制造订单已确认，触发库存规则和组件重新订购。\n"
"* 进行中：生产已开始（制造订单或工单（WO））。\n"
"* 待关闭：生产已完成，须关闭制造订单。\n"
"* 已完成：制造订单已关闭，库存分录已过账。\n"
"* 已取消：制造订单已取消，无法确认。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid " <br/><br/> The components will be taken from <b>%s</b>."
msgstr " <br/><br/>组件将取自<b>%s</b>。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid ""
" <br/><br/> The manufactured products will be moved towards "
"<b>%(destination)s</b>, <br/> as specified from <b>%(operation)s</b> "
"destination."
msgstr ""
"<br/><br/>制成产品将<br/>按照<b>%(operation)s</b>定义的目的地，被运往<b>%(destination)s</b>。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__all_available
msgid " When all components are available"
msgstr " 当所有组件都可用时"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_count
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_count
msgid "# Bill of Material"
msgstr "# 物料清单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__used_in_bom_count
msgid "# BoM Where Used"
msgstr "#使用的物料清单（BOM）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_ready_count
msgid "# Ready Work Orders"
msgstr "就绪工单数目"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_count
msgid "# Work Orders"
msgstr "#工单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_template__used_in_bom_count
msgid "# of BoM Where is Used"
msgstr "#使用的物料清单（BOM）"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking before manufacturing"
msgstr " 制造前拣货序列%(name)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence production"
msgstr " 生产序列%(name)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "%(name)s Sequence stock after manufacturing"
msgstr "制造后入库序列%(name)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "%(producible_qty)s Ready"
msgstr "%(producible_qty)s 就绪"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(product)s: Insufficient Quantity To Unbuild"
msgstr "%(product)s: 数量不足无法分解"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "%(product_name)s (new) %(number_of_boms)s"
msgstr "%(product_name)s（新）%(number_of_boms)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "%(qty)s %(measure)s unbuilt in %(order)s"
msgstr "%(qty)s %(measure)s未建立于%(order)s"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "%i work orders"
msgstr "%i工单"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s Child MO's"
msgstr "%s子级制造订单（MO）的"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "%s cannot be deleted. Try to cancel them before."
msgstr "%s无法删除。请先取消。"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_bom_structure
msgid "'Bom Overview - %s' % object.display_name"
msgstr "'物料清单（BOM）概述-%s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_finished_product
msgid "'Finished products - %s' % object.name"
msgstr "'成品-%s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_mrp_mo_overview
msgid "'MO Overview - %s' % object.display_name"
msgstr "'物料清单（BOM）概述-%s' % object.display_name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_production_order
msgid "'Production Order - %s' % object.name"
msgstr "'制造订单-%s' % object.name"

#. module: mrp
#: model:ir.actions.report,print_report_name:mrp.action_report_workorder
msgid "'Work Order - %s' % object.name"
msgstr "'工单-%s' % object.name"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "(in"
msgstr "（在"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d天"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "- Overview"
msgstr "- 概述"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
"。\n"
"           可能需要手动操作。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_steps
msgid ""
"1 Step: Consume components from stock and produce.\n"
"              2 Steps: Pick components from stock and then produce.\n"
"              3 Steps: Pick components from stock, produce, and then move final product(s) from production area to stock."
msgstr ""
"1 步：直接消耗库存中的组件，并进行生产。 \n"
"2 步：从库存中提取组件，然后进行生产。 \n"
"3 步：从库存中提取组件，进行生产，然后将最终成品从生产区域移入库存。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "100"
msgstr "100"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "12345678901"
msgstr "12345678901"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_leg_product_template
msgid "18″ x 2½″ Square Leg"
msgstr "18″ x 2½″方腿"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "2023-09-15"
msgstr "2023-09-15"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "25"
msgstr "25"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "50"
msgstr "50"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "58"
msgstr "58"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "60"
msgstr "60"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "8 GB RAM"
msgstr "8G 内存"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "987654321098"
msgstr "987654321098"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/workcenter_dashboard_graph/workcenter_dashboard_graph_field.js:0
msgid ":  hours"
msgstr "：小时"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"
msgstr "<i class=\"fa fa-pause\" role=\"img\" aria-label=\"Pause\" title=\"Pause\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-play fs-6\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"
msgstr "<i class=\"fa fa-play fs-6\" role=\"img\" aria-label=\"Run\" title=\"Run\"/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"
msgstr "<i class=\"fa fa-stop\" role=\"img\" aria-label=\"Stop\" title=\"Stop\"/>"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your product\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        上传文件至产品\n"
"                    </p><p>\n"
"                        使用该功能保存任何文件，如图纸或规范。\n"
"                    </p>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">In Progress</span>"
msgstr "<span class=\"col-6\">进行中</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">迟到</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">To Close</span>"
msgstr "<span class=\"col-6\">关闭</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr "<span class=\"col-6\">等待中</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"fw-bold text-nowrap\">To Produce</span>"
msgstr "<span class=\"fw-bold text-nowrap\">待生产</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Backorders</span>"
msgstr "<span class=\"o_stat_text\">欠单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">BoM Overview</span>"
msgstr "<span class=\"o_stat_text\">BoM 预览</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Child MO</span>"
msgstr "<span class=\"o_stat_text\">子级制造订单（MO）</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Load</span>"
msgstr "<span class=\"o_stat_text\">加载</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Lost</span>"
msgstr "<span class=\"o_stat_text\">已丢失</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "<span class=\"o_stat_text\">Manufactured</span>"
msgstr "<span class=\"o_stat_text\">已制造</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_form_inherit_mrp
msgid "<span class=\"o_stat_text\">Manufacturing</span>"
msgstr "<span class=\"o_stat_text\">制造</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">OEE</span>"
msgstr "<span class=\"o_stat_text\">设备综合效率</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">作业</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "<span class=\"o_stat_text\">Operations<br/>Performance</span>"
msgstr "<span class=\"o_stat_text\">作业<br/>效能</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Overview</span>"
msgstr "<span class=\"o_stat_text\">预览</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "<span class=\"o_stat_text\">Performance</span>"
msgstr "<span class=\"o_stat_text\">性能</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr "<span class=\"o_stat_text\">产品移动</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span class=\"o_stat_text\">Scraps</span>"
msgstr "<span class=\"o_stat_text\">报废</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Source MO</span>"
msgstr "<span class=\"o_stat_text\">源制造订单（MO）</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr "<span class=\"o_stat_text\">追踪</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span class=\"o_stat_text\">Unbuilds</span>"
msgstr "<span class=\"o_stat_text\">分解</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid ""
"<span class=\"text-muted\">Modifying the quantity to produce will also "
"modify the quantities of components to consume for this manufacturing "
"order.</span>"
msgstr "<span class=\"text-muted\">修改制造数量将同时修改该制造订单所需的组件数量。</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid ""
"<span>\n"
"            Components\n"
"        </span>"
msgstr ""
"<span>\n"
"            组件\n"
"        </span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>&amp;nbsp;(minutes)</span>"
msgstr "<span>&amp;nbsp;(分钟)</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Actions</span>"
msgstr "<span>操作</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "<span>Generate BOM</span>"
msgstr "<span>生成物料清单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>New</span>"
msgstr "<span>新</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "<span>Orders</span>"
msgstr "<span>订单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>PLAN ORDERS</span>"
msgstr "<span>安排订单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_report_delivery_no_kit_section
msgid "<span>Products not associated with a kit</span>"
msgstr "<span>与套件无关的产品</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>Reporting</span>"
msgstr "<span>报表</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "<span>WORK ORDERS</span>"
msgstr "<span>工单</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<span>minutes</span>"
msgstr "<span>分钟</span>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "<strong class=\"mr8 oe_inline\">to</strong>"
msgstr "<strong class=\"mr8 oe_inline\">到</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Actual Duration (minutes)</strong>"
msgstr "<strong>实际时长（分钟）</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Barcode</strong>"
msgstr "<strong>条码</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Deadline:</strong><br/>"
msgstr "<strong>截止日期：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Description:</strong><br/>"
msgstr "<strong>描述：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Duration (minutes)</strong>"
msgstr "<strong>时长（分钟）</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Effectiveness Category: </strong>"
msgstr "<strong>有效性类别：</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Finished Product:</strong><br/>"
msgstr "<strong>成品：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Is a Blocking Reason? </strong>"
msgstr "<strong>是否为阻碍原因？</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "<strong>Manufacturing Order:</strong><br/>"
msgstr "<strong>制造订单：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Operation</strong>"
msgstr "<strong>作业</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Product:</strong><br/>"
msgstr "<strong>产品：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity Producing:</strong><br/>"
msgstr "<strong>正在生产的数量：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Quantity to Produce:</strong><br/>"
msgstr "<strong>待生产数量：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_productivity_loss_kanban
msgid "<strong>Reason: </strong>"
msgstr "<strong>原因：</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Responsible:</strong><br/>"
msgstr "<strong>负责人：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>Source:</strong><br/>"
msgstr "<strong>来源：</strong><br/>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "<strong>Unit Cost</strong>"
msgstr "<strong>单位成本</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "<strong>WorkCenter</strong>"
msgstr "<strong>工作中心</strong>"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "？这可能会导致与库存不一致。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"A BoM of type Kit is not produced with a manufacturing order.<br/>\n"
"                                Instead, it is used to decompose a BoM into its components when:"
msgstr ""
"类型为\"套件\"的物料清单（BoM）并非在制造订单下制造。<br/>\n"
"                                相反，在以下情况下，用于将物料清单分解为组件："

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "A Manufacturing Order is already done or cancelled."
msgstr "制造订单已完成或已取消。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid ""
"A product with a kit-type bill of materials can not have a reordering rule."
msgstr "具有套件类型物料清单（BOM）的产品不能具有重新订购规则。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_rule__action
msgid "Action"
msgstr "操作"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_needaction
msgid "Action Needed"
msgstr "待处理"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__active
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__active
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__active
msgid "Active"
msgstr "有效"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_ids
msgid "Activities"
msgstr "活动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_exception_decoration
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常标示"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_state
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_type_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Add a description..."
msgstr "添加描述......"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Add a line"
msgstr "添加行"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Add by-products to bills of materials. This can be used to get several "
"finished products as well. Without this option you only do: A + B = C. With "
"the option: A + B = C + D."
msgstr "添加副产品至物料清单（BOM）。该选项也可用于获得成品。如果没有该选项，您须：A + B = C。选项：A + B = C + D。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Add quality checks to your work orders"
msgstr "为工单增加质量检查"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_tag
msgid "Add tag for the workcenter"
msgstr "为工作中心添加标签"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_manager
msgid "Administrator"
msgstr "管理员"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__after
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "After"
msgstr "之后"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "All"
msgstr "全部"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__all_move_ids
msgid "All Move"
msgstr "所有 移动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__all_move_raw_ids
msgid "All Move Raw"
msgstr "所有 移动"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_line_bom_qty_zero
msgid ""
"All product quantities must be greater or equal to 0.\n"
"Lines with 0 quantities can be used as optional lines. \n"
"You should install the mrp_byproduct module if you want to manage extra products on BoMs!"
msgstr ""
"所有产品数量须大于或等于0。\n"
"数量为0的行可以用作可选行。\n"
"如果想在物料清单（BOM）中管理额外产品，应安装mrp_byproduct模块！"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Allocation"
msgstr "分配"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Allocation Report"
msgstr "分配报表"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Allocation Report Labels"
msgstr "分配报表标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_reception_report
msgid "Allocation Report for Manufacturing Orders"
msgstr "制造订单分配报表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__allow_workorder_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__allow_workorder_dependencies
msgid "Allow Work Order Dependencies"
msgstr "容许工单依赖性"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Allow manufacturing users to modify quantities to consume, without the need "
"for prior approval"
msgstr "允许制造用户修改待消耗数量，无需要事先批准"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,help:mrp.field_stock_picking_type__use_create_components_lots
msgid "Allow to create new lot/serial numbers for the components"
msgstr "允许为组件创建新批号/序列号"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__flexible
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__flexible
msgid "Allowed"
msgstr "已允许"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reserve_visible
msgid "Allowed to Reserve Production"
msgstr "允许预留组件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unreserve_visible
msgid "Allowed to Unreserve Production"
msgstr "允许取消组件预留"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__warning
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__warning
msgid "Allowed with warning"
msgstr "允许但警告"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid "Alternative Workcenters"
msgstr "替代工作中心"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__alternative_workcenter_ids
msgid ""
"Alternative workcenters that can be substituted to this one in order to "
"dispatch production"
msgstr "可以替代该工作中心进行调度生产的其他工作中心"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid ""
"An unbuild order is used to break down a finished product into its "
"components."
msgstr "分解单用于将成品分解为组件。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "Apply on Variants"
msgstr "应用于变体"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Archive Operation"
msgstr "存档作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Archived"
msgstr "已归档"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Are you sure you want to cancel this manufacturing order?"
msgstr "您确定要取消此制造订单吗？"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Assembling"
msgstr "组装"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_attachment_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Attachments"
msgstr "附件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__attachments_count
msgid "Attachments Count"
msgstr "附件数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_variant_attributes
msgid "Attribute Values"
msgstr "属性值"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_mrp_reception_report
msgid "Auto Print Allocation Report"
msgstr "自动打印分配报告"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_mrp_reception_report_labels
msgid "Auto Print Allocation Report Labels"
msgstr "自动打印分配报表标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_production_order
msgid "Auto Print Done Production Order"
msgstr "自动打印已完成的制造订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_generated_mrp_lot
msgid "Auto Print Generated Lot/SN Label"
msgstr "自动打印生成的批次/SN 标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_mrp_lot
msgid "Auto Print Produced Lot Label"
msgstr "自动打印生产批次标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__auto_print_done_mrp_product_labels
msgid "Auto Print Produced Product Labels"
msgstr "自动打印生产的产品标签"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_generated_mrp_lot
msgid ""
"Automatically print the lot/SN label when the \"Create a new serial/lot "
"number\" button is used."
msgstr "使用 \"创建新序列号/批号 \"按钮时，自动打印批号/SN 标签。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Availabilities"
msgstr "可用性"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Availabilities on products."
msgstr "产品的可用性。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__availability
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Availability"
msgstr "可用性"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Availability Losses"
msgstr "可用性损失"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__available
msgid "Available"
msgstr "可用"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_kanban
msgid "Avatar"
msgstr "头像"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__produce_delay
msgid ""
"Average lead time in days to manufacture this product. In the case of multi-"
"level BOM, the manufacturing lead times of the components will be added. In "
"case the product is subcontracted, this can be used to determine the date at"
" which components should be sent to the subcontractor."
msgstr "制造该产品的平均提前期。如果是多级物料清单（BOM），则加上组件的制造提前期。如果是分包产品，可用于确定向分包商发送组件所用的天数。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Cost of Components per Unit"
msgstr "单位组件平均成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Cost of Operations per Unit"
msgstr "单位平均运营成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Avg Total Cost per Unit"
msgstr "单位平均总成本"

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "物料清单（BOM）概述报表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__variant_bom_ids
msgid "BOM Product Variants"
msgstr "物料清单（BOM）产品变体"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__bom_product_template_attribute_value_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__bom_product_template_attribute_value_ids
msgid "BOM Product Variants needed to apply this line."
msgstr "需应用至该行的物料清单（BOM）产品变体。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_line_ids
msgid "BOM lines of the referred bom"
msgstr "参考物料清单（BOM）中的物料清单行"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to BoM"
msgstr "返回物料清单"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/product_catalog/kanban_controller.js:0
msgid "Back to Production"
msgstr "返回生产"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder_line
msgid "Backorder Confirmation Line"
msgstr "欠单确认行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_backorder_line_ids
msgid "Backorder Confirmation Lines"
msgstr "欠单确认行"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO"
msgstr "欠单制造订单（MO）"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Backorder MO's"
msgstr "欠单制造订单（MO）的"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__backorder_sequence
msgid "Backorder Sequence"
msgstr "欠单序列"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__backorder_sequence
msgid ""
"Backorder sequence, if equals to 0 means there is not related backorder"
msgstr "欠单序列，如果等于0，则表示无相关欠单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__barcode
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Barcode"
msgstr "条码"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode_batch
msgid "Based on"
msgstr "基于"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_batch_produce
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_text
msgid "Batch Production"
msgstr "批量生产"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__before
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Before"
msgstr "在之前"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom
#: model:ir.model.fields,field_description:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__bom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_bom_id
#: model:ir.model.fields,field_description:mrp.field_product_replenish__bom_id
#: model:ir.model.fields,field_description:mrp.field_stock_replenish_mixin__bom_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Bill of Material"
msgstr "物料清单"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "物料清单行"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
msgid "Bill of Material line"
msgstr "物料清单（BOM）行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_bom_id
msgid "Bill of Material used on the Production Order"
msgstr "制造订单上使用的物料清单（BOM）"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_open_bom
#: model:ir.actions.act_window,name:mrp.template_open_bom
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_ids
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__bom_id
#: model:ir.model.fields.selection,name:mrp.selection__product_document__attached_on_mrp__bom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Bill of Materials"
msgstr "物料清单"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_bom_form_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom_form_action
msgid "Bills of Materials"
msgstr "物料清单"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__bom_id
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__mo_bom_id
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_bom_id
msgid ""
"Bills of Materials, also called recipes, are used to autocomplete components"
" and work order instructions."
msgstr "物料清单，也称为配方，用于自动完成组件和工单指令。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid ""
"Bills of materials allow you to define the list of required raw\n"
"                materials used to make a finished product; through a manufacturing\n"
"                order or a pack of products."
msgstr ""
"可以通过物料清单规定制造成品所用原材料，\n"
"                的清单；通过制造\n"
"                订单或产品包。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.js:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block"
msgstr "阻止"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter
#: model:ir.actions.act_window,name:mrp.act_mrp_block_workcenter_wo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Block Workcenter"
msgstr "阻塞工作中心"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_consumption_warning__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__consumption__strict
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__blocked
msgid "Blocked"
msgstr "受阻"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__blocked_by_workorder_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Blocked By"
msgstr "受阻于"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked Time"
msgstr "受阻日期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__blocked_time
msgid "Blocked hours over the last month"
msgstr "上个月受阻小时数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__name
msgid "Blocking Reason"
msgstr "受阻原因"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__needed_by_workorder_ids
msgid "Blocks"
msgstr "阻塞"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__bom_id
msgid "BoM"
msgstr "物料清单（BOM）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__bom_line_ids
#: model:ir.model.fields,field_description:mrp.field_product_template__bom_line_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "BoM Components"
msgstr "物料清单（BOM）组件"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Cost"
msgstr "物料清单（BOM）成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "BoM Costs"
msgstr "物料清单（BOM）成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__bom_line_id
msgid "BoM Line"
msgstr "物料清单（BOM）行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__bom_line_ids
msgid "BoM Lines"
msgstr "物料清单（BOM）行"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mrp_bom
#: model:ir.actions.report,name:mrp.action_report_bom_structure
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "BoM Overview"
msgstr "物料清单（BOM）概述"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__type
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "BoM Type"
msgstr "物料清单（BOM）类型"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_bolt_product_template
msgid "Bolt"
msgstr "螺栓"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_byproducts_block/mrp_mo_overview_byproducts_block.xml:0
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_byproducts
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "By-Products"
msgstr "副产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_id
msgid "By-product"
msgstr "副产品"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "By-product %s should not be the same as BoM product."
msgstr "副产品%s不应与物料清单（BOM）产品相同。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__byproduct_id
msgid "By-product line that generated the move in a manufacturing order"
msgstr "在制造订单中生成库存分录的副产品行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__byproduct_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__byproduct_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "By-products"
msgstr "副产品"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
msgid "By-products cost shares must be positive."
msgstr "副产品成本分摊须为正数。"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_bom_byproduct
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_byproduct_form_view
msgid "Byproduct"
msgstr "副产品"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Byproducts"
msgstr "副产品"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Can't find any production location."
msgstr "未找到任何制造使用的库存位置。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_block_wizard_form
msgid "Cancel"
msgstr "取消"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__cancel
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__cancel
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Cancelled"
msgstr "已取消"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"Cannot compute days to prepare due to missing route info for at least 1 "
"component or for the final product."
msgstr "由于缺少至少一个部件或最终产品的路线信息，无法计算准备天数。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Cannot delete a manufacturing order in done state."
msgstr "无法删除状态为已完成的制造订单。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__default_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__capacity
msgid "Capacity"
msgstr "产能"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_positive_capacity
msgid "Capacity should be a positive number."
msgstr "产能应为正数。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid "Carried Quantity"
msgstr "携带数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Catalog"
msgstr "目录"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_category_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__loss_type
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Category"
msgstr "类别"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Center A"
msgstr "中心 A"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Change Product Qty"
msgstr "变更产品数量"

#. module: mrp
#: model:ir.model,name:mrp.model_change_production_qty
msgid "Change Production Qty"
msgstr "变更生产数量"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_change_production_qty
msgid "Change Quantity To Produce"
msgstr "变更待生产数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Check availability"
msgstr "检查可用性"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr "选择标签布局"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Choose Type of Labels To Print"
msgstr "选择要打印的标签类型"

#. module: mrp
#: model:ir.model,name:mrp.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr "选择打印产品标签还是批次标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_stop
msgid "Cleanup Time"
msgstr "清理日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Cleanup Time (minutes)"
msgstr "清理用时（分钟）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__code
msgid "Code"
msgstr "代码"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__color
msgid "Color"
msgstr "颜色"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__color
msgid "Color Index"
msgstr "颜色指数"

#. module: mrp
#: model:ir.model,name:mrp.model_res_company
msgid "Companies"
msgstr "公司"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__company_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__company_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Company"
msgstr "公司"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Component"
msgstr "组件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability
msgid "Component Status"
msgstr "组件状态"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Component of Draft MO"
msgstr "制造订单（MO）草稿的组件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__component_separator
msgid "Component separator"
msgstr "组件分割器"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_raw_ids
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Components"
msgstr "组件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__components_availability_state
msgid "Components Availability State"
msgstr "组件可用性状态"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Components Available"
msgstr "有组件可用"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_src_id
msgid "Components Location"
msgstr "组件位置"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__priority
msgid ""
"Components will be reserved first for the MO with the highest priorities."
msgstr "为优先级最高的制造订单（MO）预留组件。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Compute"
msgstr "计算"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__auto
msgid "Compute based on tracked time"
msgstr "根据跟踪时间进行计算"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"Compute the days required to resupply all components from BoM, by either "
"buying or manufacturing the components and/or subassemblies."
"                                                       Also note that "
"purchase security lead times will be added when appropriate."
msgstr "通过采购或制造组件和/或子组件，计算从 BoM 补给所有组件所需的天数。 还需注意的是，在适当情况下还需加上采购保障周转时间。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_computed_on
msgid "Computed on last"
msgstr "最后计算"

#. module: mrp
#: model:ir.model,name:mrp.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_configuration
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Configuration"
msgstr "配置"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Confirm"
msgstr "确认"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Confirmed"
msgstr "已确认"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_consumed_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.production_message
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Consumed"
msgstr "已消耗"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__consume_line_ids
msgid "Consumed Disassembly Lines"
msgstr "已消耗的拆解行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__consume_unbuild_id
msgid "Consumed Disassembly Order"
msgstr "已消耗的拆解单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__operation_id
msgid "Consumed in Operation"
msgstr "消耗在作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_production__consumption
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__consumption
msgid "Consumption"
msgstr "消耗"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_consumption_warning
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Consumption Warning"
msgstr "消耗警告"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_category_id
#: model:ir.model.fields,help:mrp.field_mrp_production__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "仅同类别计量单位可以互相转换。根据比例进行转换。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Copy Existing Operations"
msgstr "复制现有作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_copy_to_bom_tree_view
msgid "Copy selected operations"
msgstr "复制所选作业"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost Breakdown of Products"
msgstr "产品成本细目"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__cost_share
#: model:ir.model.fields,field_description:mrp.field_stock_move__cost_share
msgid "Cost Share (%)"
msgstr "成本分摊（%）"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost as it is currently accumulated"
msgstr "目前累计成本的情况"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on cost projection"
msgstr "基于成本预测的成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on related replenishments. Otherwise cost from product form"
msgstr "基于相关补货的成本。否则按产品形式计算成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Cost based on the BoM"
msgstr "基于物料清单的成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost of Components per unit"
msgstr "单位组件成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Cost of Operations per unit"
msgstr "单位业务成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__costs_hour
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__costs_hour
msgid "Cost per hour"
msgstr "每小时成本"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Costing Information"
msgstr "成本信息"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Costs"
msgstr "成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__production_count
msgid "Count of MO generated"
msgstr "MO 生成计数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_backorder_count
msgid "Count of linked backorder"
msgstr "已连接欠单计数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__use_create_components_lots
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__use_create_components_lots
msgid "Create New Lots/Serial Numbers for Components"
msgstr "创建新的组件批号/序列号"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create a Backorder"
msgstr "创建欠单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid ""
"Create a backorder if you expect to process the remaining products later. Do"
" not create a backorder if you will not process the remaining products."
msgstr "如果希望之后处理剩余产品，请创建欠单。如果不处理剩余产品，则无需创建欠单。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid "Create a new operation"
msgstr "创建新作业"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid "Create a new work center"
msgstr "创建新的工作中心"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_workcenter_report
msgid "Create a new work orders performance"
msgstr "创建新的工单绩效"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__days_to_prepare_mo
msgid ""
"Create and confirm Manufacturing Orders this many days in advance, to have enough time to replenish components or manufacture semi-finished products.\n"
"Note that security lead times will also be considered when appropriate."
msgstr ""
"提前创建并确认制造订单这么多天，以确保有足够的时间来补充零部件或制造半成品。\n"
"请注意，在适当的情况下，还将考虑安全提前时间。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Create backorder"
msgstr "创建欠单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "为质量检查创建可定制的工作表"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid ""
"Create operation level dependencies that will influence both planning and "
"the status of work orders upon MO confirmation. If this feature is ticked, "
"and nothing is specified, Odoo will assume that all operations can be "
"started simultaneously."
msgstr "创建会影响制造订单（MO）确定后安排和工单状态的作业级依赖项。如果勾选该功能，且未指定任何内容，Odoo将假定所有作业可以同时开始。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__created_production_id
msgid "Created Production Order"
msgstr "创建制造订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_uid
msgid "Created by"
msgstr "创建人"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__create_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__create_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__create_date
msgid "Created on"
msgstr "创建日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Creates a new serial/lot number"
msgstr "创建新序列号/批号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__currency_id
msgid "Currency"
msgstr "币别"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"当前产品数量。\n"
"如果是单一库存位置，则包括该位置或其任何子位置存储的产品。\n"
"如果是单一仓库，则包括了该仓库或其任何子仓库的库存位置存储的产品。\n"
"否则，包括任何“内部”类型库存位置存储的产品。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_producing
msgid "Currently Produced Quantity"
msgstr "当前已生产数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_description_variants
msgid "Custom Description"
msgstr "自定义描述"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Date"
msgstr "日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__search_date_category
msgid "Date Category"
msgstr "日期类别"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date by Month"
msgstr "按月日期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_finished
msgid ""
"Date you expect to finish production or actual date you finished production."
msgstr "预计或实际完成生产的日期。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_start
msgid ""
"Date you plan to start production or date you actually started production."
msgstr "计划或实际开始生产的日期。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Date: Last 365 Days"
msgstr "日期：过去365天"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom_pdf_line
msgid "Days"
msgstr "天"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Days to Supply Components"
msgstr "供应组件的天数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__days_to_prepare_mo
msgid "Days to prepare Manufacturing Order"
msgstr "制造订单准备天数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_deadline
msgid "Deadline"
msgstr "截止日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Default Duration"
msgstr "默认时长"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__use_manufacturing_lead
msgid "Default Manufacturing Lead Time"
msgstr "默认制造提前期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__default_capacity
msgid ""
"Default number of pieces (in product UoM) that can be produced in parallel "
"(at the same time) at this work center. For example: the capacity is 5 and "
"you need to produce 10 units, then the operation time listed on the BOM will"
" be multiplied by two. However, note that both time before and after "
"production will only be counted once."
msgstr ""
"可以在该工作中心并行（同时）生产的团队件数（产品计量单位）。例如：产能为5，但需要生产10件，则物料清单（BOM）中列出的作业时间应乘以2。但请注意，生产前后时间只计算一次。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Default unit of measure used for all stock operations."
msgstr "所有库存作业的默认单位。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid ""
"Define the components and finished products you wish to use in\n"
"                bill of materials and manufacturing orders."
msgstr ""
"规定希望在物料清单和制造订单中使用的\n"
"                组件和成品。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__resource_calendar_id
msgid ""
"Define the working schedule of the resource. If not set, the resource will "
"have fully flexible working hours."
msgstr "定义资源的工作计划。如果未设置，资源将具有完全灵活的工作时间。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__consumption
msgid ""
"Defines if you can consume more or less components than the quantity defined on the BoM:\n"
"  * Allowed: allowed for all manufacturing users.\n"
"  * Allowed with warning: allowed for all manufacturing users with summary of consumption differences when closing the manufacturing order.\n"
"  Note that in the case of component Highlight Consumption, where consumption is registered manually exclusively, consumption warnings will still be issued when appropriate also.\n"
"  * Blocked: only a manager can close a manufacturing order when the BoM consumption is not respected."
msgstr ""
"规定组件消耗数量是否可以多于或少于物料清单（BOM）中规定的数量：\n"
"  * 允许：允许所有制造用户。\n"
"  * 允许但有警告：允许所有制造用户在关闭制造订单时提供消耗差异摘要。\n"
"  请注意，对于高亮消耗组件，即手动登记消耗，会在适当时间发出消耗警告。\n"
"  * 阻止：仅在不遵守物料清单消耗时经理可以关闭制造订单。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delay_alert_date
msgid "Delay Alert Date"
msgstr "延迟警报日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Delayed Productions"
msgstr "已延误生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Delegate part of the production process to subcontractors"
msgstr "将部分生产流程委托给分包商"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__delivery_count
msgid "Delivery Orders"
msgstr "交货单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__note
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__description
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_note
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description"
msgstr "描述"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Description of the work center..."
msgstr "工作中心描述......"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_dest_id
msgid "Destination Location"
msgstr "目标库位"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unbuild_id
msgid "Disassembly Order"
msgstr "拆解单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Discard"
msgstr "丢弃"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.xml:0
msgid "Display"
msgstr "显示"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__display_name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__display_name
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lot_ids
msgid "Display the serial number shortcut on the moves"
msgstr "显示分录序列号快捷方式"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the consumed Lot/Serial Numbers."
msgstr "显示消耗的批号/序列号。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Displays the produced Lot/Serial Numbers."
msgstr "显示生产批次/序列号。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "Do you confirm you want to unbuild"
msgstr "是否确定分解"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Documentation"
msgstr "文档"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
#: model:ir.model.fields,field_description:mrp.field_stock_move__is_done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__done
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Done"
msgstr "已完成"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__draft
#: model:ir.model.fields.selection,name:mrp.selection__mrp_unbuild__state__draft
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Draft"
msgstr "草稿"

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_drawer_product_template
msgid "Drawer Black"
msgstr "黑色抽屉"

#. module: mrp
#: model:product.template,name:mrp.product_product_drawer_case_product_template
msgid "Drawer Case Black"
msgstr "黑色抽屉柜"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_drawer_drawer_product_template
msgid "Drawer on casters for great usability."
msgstr "方便使用的脚轮抽屉。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__duration
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Duration"
msgstr "时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Duration (minutes)"
msgstr "时长（分钟）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_mode
msgid "Duration Computation"
msgstr "时长计算"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_percent
msgid "Duration Deviation (%)"
msgstr "时长偏差（%）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_unit
msgid "Duration Per Unit"
msgstr "每单元时长"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_type
msgid "Effectiveness"
msgstr "有效性"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__loss_type
msgid "Effectiveness Category"
msgstr "有效性类别"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_finished
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_finished
msgid "End"
msgstr "结束"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_end
msgid "End Date"
msgstr "结束日期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,help:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,help:mrp.field_mrp_workorder__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "确保仓库中可储存产品的可追溯性。"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason1
msgid "Equipment Failure"
msgstr "设备故障"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Estimated %s"
msgstr "预估%s"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s) occurred on the manufacturing order(s):"
msgstr "制造订单发生的例外情况："

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Exception(s):"
msgstr "异常："

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Exp %s"
msgstr "预计%s"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__expected
msgid "Expected"
msgstr "预计"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Expected %s"
msgstr "预计：%s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration_expected
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration_expected
msgid "Expected Duration"
msgstr "预计时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Expected Duration (minutes)"
msgstr "预计时长（分钟）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_text_help
msgid "Explanation for batch production"
msgstr "大批生产注释"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Files attached to the product."
msgstr "附至产品的文件。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Filters"
msgstr "筛选器"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_final_id
msgid "Final Location from procurement"
msgstr "采购物资最终位置"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__done
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Finished"
msgstr "已完成"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__order_finished_lot_id
msgid "Finished Lot/Serial Number"
msgstr "已完成批号/序列号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_finished_ids
msgid "Finished Moves"
msgstr "已完成分录"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__finished_move_line_ids
msgid "Finished Product"
msgstr "成品"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_finished_product
msgid "Finished Product Label (PDF)"
msgstr "成品标签（PDF）"

#. module: mrp
#: model:ir.actions.report,name:mrp.label_manufacture_template
msgid "Finished Product Label (ZPL)"
msgstr "成品标签（ZPL）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_finished_ids
msgid "Finished Products"
msgstr "成品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__location_dest_id
msgid "Finished Products Location"
msgstr "成品位置"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lot_name
msgid "First Lot/SN"
msgstr "首个批次/序号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__consumption
msgid "Flexible Consumption"
msgstr "灵活消耗"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Fold"
msgstr "折叠"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_follower_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_partner_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者（合作伙伴）"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_type_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome图标，例如：fa-tasks"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Force"
msgstr "强制"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast"
msgstr "预测"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Forecast Report"
msgstr "预测报表"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__product_virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"预测数量（计算：已有数量 - 出货 + 进货）\n"
"如果是单一库存位置，则包括该位置或其任何子位置存储的产品。\n"
"如果是单一仓库，则包括该仓库库存位置或其任何子位置存储的产品。\n"
"否则，包括任何“内部”类型库存位置存储的产品。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Forecasted"
msgstr "预测"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__forecasted_issue
msgid "Forecasted Issue"
msgstr "预测的问题"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Free to Use"
msgstr "可用"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Free to Use / On Hand"
msgstr "可用/在手"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Free to use / On Hand"
msgstr "可用/在手"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "From"
msgstr "来自"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Fully Productive"
msgstr "全面生产"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason7
msgid "Fully Productive Time"
msgstr "全面生产日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Future Activities"
msgstr "未来活动"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "General Information"
msgstr "基本信息"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Generate"
msgstr "生成"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Generate Serial Numbers"
msgstr "生成序列号"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Generate a new BoM from this Manufacturing Order"
msgstr "从该制造订单生成新的 BoM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__generated_mrp_lot_label_to_print
msgid "Generated Lot/SN Label to Print"
msgstr "打印生成的批次/SN 标签"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
msgid "Get statistics about the work orders duration related to this routing."
msgstr "获取有关与该工艺路线相关的工单时长统计信息。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__sequence
msgid ""
"Gives the sequence order when displaying a list of routing Work Centers."
msgstr "指定工作中心工艺路线列表的显示顺序。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__sequence
msgid "Gives the sequence order when displaying a list of work centers."
msgstr "指定工作中心列表的显示顺序。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__sequence
msgid "Gives the sequence order when displaying."
msgstr "指定显示顺序。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__google_slide
msgid "Google Slide"
msgstr "谷歌幻灯片"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Google Slide Link"
msgstr "谷歌幻灯片链接"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Group By"
msgstr "分组方式"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Group By..."
msgstr "分组方式......"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Group by..."
msgstr "分组方式......"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Hardware"
msgstr "硬件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_produced
msgid "Has Been Produced"
msgstr "已生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking__has_kits
msgid "Has Kits"
msgstr "有套件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_production__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_message
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_message
msgid "Has Message"
msgstr "有消息"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__has_routing_lines
msgid "Has Routing Lines"
msgstr "有工艺路线行"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__product_document__attached_on_mrp__hidden
msgid "Hidden"
msgstr "隐藏"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__manual_consumption
msgid "Highlight Consumption"
msgstr "高亮消耗"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__costs_hour
msgid "Hourly processing cost."
msgstr "每小时加工成本。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Hours"
msgstr "小时"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__id
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__id
msgid "ID"
msgstr "ID"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_exception_icon
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_exception_icon
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_id
msgid ""
"If a product variant is defined the BOM is available only for this product."
msgstr "如果规定了产品变体，则物料清单（BOM）仅可用于该产品。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_needaction
msgid "If checked, new messages require your attention."
msgstr "如果勾选此项，则需要查看新消息。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_sms_error
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_error
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将出现发送错误。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__propagate_cancel
msgid ""
"If checked, when the previous move of the move (which was generated by a "
"next procurement) is cancelled or split, the move generated by this move "
"will too"
msgstr "如果勾选此项，当该移动的之前移动（由下一采购生成）取消或拆分时，由该分录生成的移动也将取消或拆分"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr "如果该有效字段设置为假，则可以隐藏而非删除源记录。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_mrp_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the allocation "
"report labels of a MO when it is done."
msgstr "如果勾选该复选框，Odoo 将在完成 MO 后自动打印分配报告标签。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_mrp_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the allocation "
"report of a MO when it is done and has assigned moves."
msgstr "如果勾选该复选框，Odoo 将在 MO 完成并分配移动后自动打印分配报告。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_mrp_lot
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN label "
"of a MO when it is done."
msgstr "如果勾选该复选框，Odoo 将在完成 MO 时自动打印批次/SN 标签。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_mrp_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a MO when it is done."
msgstr "如果勾选该复选框，Odoo 将在完成 MO 时自动打印产品标签。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_picking_type__auto_print_done_production_order
msgid ""
"If this checkbox is ticked, Odoo will automatically print the production "
"order of a MO when it is done."
msgstr "如果勾选该复选框，Odoo 将在 MO 完成后自动打印生产单。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "Impacted Transfer(s):"
msgstr "受影响的转移："

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "Import Template for Bills of Materials"
msgstr "物料清单（BOM）导入模板"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"Impossible to plan the workorder. Please check the workcenter "
"availabilities."
msgstr "无法安排工单。 请检查工作中心可用性。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "Impossible to plan. Please check the workcenter availabilities."
msgstr "无法安排计划。请检查工作中心的可用性。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__progress
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__done
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__progress
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "In Progress"
msgstr "进行中"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "In Transit"
msgstr "中转"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_view_search_catalog
msgid "In the BoM"
msgstr "在物料清单中"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_view_search_catalog
msgid "In the MO"
msgstr "在制造订单中"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__date_deadline
msgid ""
"Informative date allowing to define when the manufacturing order should be "
"processed at the latest to fulfill delivery on time."
msgstr "指定处理制造订单、以按时交货的最迟日期信息。"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_moves
msgid "Inventory Moves"
msgstr "库存移动"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__move_line_ids
msgid ""
"Inventory moves for which you must scan a lot number at this work order"
msgstr "须在该工单扫描批号的库存移动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_delayed
msgid "Is Delayed"
msgstr "是已延迟"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_is_follower
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__is_kits
#: model:ir.model.fields,field_description:mrp.field_product_template__is_kits
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__product_is_kit
msgid "Is Kits"
msgstr "是套件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_locked
msgid "Is Locked"
msgstr "已锁定"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__manual
msgid "Is a Blocking Reason"
msgstr "是受阻原因"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_user_working
msgid "Is the Current User Working"
msgstr "当前用户是否正在工作"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "It has already been unblocked."
msgstr "已解除受阻。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"It is not possible to unplan one single Work Order. You should unplan the "
"Manufacturing Order instead in order to unplan all the linked operations."
msgstr "无法取消安排单个工单。应取消安排制造订单，以取消安排所有关联作业。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_planned
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__is_planned
msgid "Its Operations are Planned"
msgstr "其作业已安排"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__json_popover
msgid "JSON data for the popover widget"
msgstr "弹出工具的JSON数据"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "John Doe"
msgstr "John Doe"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "看板仪表板图表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__description_bom_line
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__bom_id
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__phantom
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Kit"
msgstr "套件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Laptop"
msgstr "笔记本电脑"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Laptop Model X"
msgstr "X 型笔记本电脑"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Laptop model X"
msgstr "笔记本电脑型号 X"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Laptop with 16GB RAM"
msgstr "配备 16GB 内存的笔记本电脑"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_uid
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_uid
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_uid
msgid "Last Updated by"
msgstr "上一更新人"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss_type__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__write_date
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__write_date
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__last_working_user_id
msgid "Last user that worked on this work order."
msgstr "在此工单工作的上一用户。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__late
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Late"
msgstr "延迟"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Activities"
msgstr "最近活动"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late Availability"
msgstr "迟到的可用性"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Late MO or Late delivery of components"
msgstr "延迟物料清单（MO）或延迟交付组件"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__components_availability
msgid ""
"Latest component availability status for this MO. If green, then the MO's "
"readiness status is ready, as per BOM configuration."
msgstr "该物料清单（MO）的最新组件可用性状态。如果是绿色，则根据物料清单的状态为准备就绪，根据物料清单进行配置。"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_ply_product_template
msgid "Layers that are stick together to assemble wood panels."
msgstr "组装木板的粘合层。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Lead Time"
msgstr "提前期"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
msgid "Lead Times"
msgstr "提前期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__leave_id
msgid "Leave"
msgstr "离开"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_product_document__attached_on_mrp
msgid ""
"Leave hidden if document only accessible on product form.\n"
"Select Bill of Materials to visualise this document as a product attachment when this product is in a bill of material."
msgstr ""
"如果文档只能在产品表单中访问，则保持隐藏。\n"
"选择“物料清单”，当该产品在物料清单中时，将此文档可视化为产品附件。"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning_line
msgid "Line of issue consumption"
msgstr "问题消耗行"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__location_id
msgid "Location"
msgstr "位置"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_id
msgid "Location where the product you want to unbuild is."
msgstr "希望分解产品的位置。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_src_id
msgid "Location where the system will look for components."
msgstr "系统搜索组件的位置。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__location_dest_id
msgid "Location where the system will stock the finished products."
msgstr "系统存储成品的位置。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__location_dest_id
msgid ""
"Location where you want to send the components resulting from the unbuild "
"order."
msgstr "希望向其发送分解单产生的组件的位置。"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_lock_unlock
msgid "Lock/Unlock"
msgstr "锁定/解锁"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__loss_id
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Loss Reason"
msgstr "损失原因"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Lot %s does not exist."
msgstr "批次%s不存在。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lots_quantity_separator
msgid "Lot quantity separator"
msgstr "批次数量分割符"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lots_separator
msgid "Lot separator"
msgstr "批次分隔器"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Lot/SN Label"
msgstr "批号/SN 标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__done_mrp_lot_label_to_print
msgid "Lot/SN Label to Print"
msgstr "要打印的批次/SN 标签"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Lot/SN Labels"
msgstr "批号/SN 标签"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_lot
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Lot/Serial"
msgstr "批/序列"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__lot_producing_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__lot_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__finished_lot_id
msgid "Lot/Serial Number"
msgstr "批号/序列号"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Lot/Serial Numbers"
msgstr "批号/序列号"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_traceability
msgid "Lots/Serial Numbers"
msgstr "批号/序列号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_backorder_id
msgid "MO Backorder"
msgstr "制造订单（MO）欠单"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_cancelled
#: model:mail.message.subtype,name:mrp.mrp_mo_in_cancelled
msgid "MO Cancelled"
msgstr "MO 已取消"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_confirmed
#: model:mail.message.subtype,name:mrp.mrp_mo_in_confirmed
msgid "MO Confirmed"
msgstr "MO 已确认"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "MO Cost"
msgstr "制造订单（MO）成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "MO Costs"
msgstr "制造订单（MO）成本"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_done
#: model:mail.message.subtype,name:mrp.mrp_mo_in_done
msgid "MO Done"
msgstr "MO 已完成"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "MO Generated by %s"
msgstr "%s生成的制造订单（MO）"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_report_mo_overview
#: model:ir.actions.report,name:mrp.action_report_mrp_mo_overview
msgid "MO Overview"
msgstr "制造订单（MO）概述"

#. module: mrp
#: model:ir.model,name:mrp.model_report_mrp_report_mo_overview
msgid "MO Overview Report"
msgstr "制造订单（MO）概述报表"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "MO Pending"
msgstr "制造订单等待中"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_progress
#: model:mail.message.subtype,name:mrp.mrp_mo_in_progress
msgid "MO Progress"
msgstr "制造订单进度"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__reservation_state
msgid "MO Readiness"
msgstr "制造订单（MO）准备就绪"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "MO Ready"
msgstr "MO 准备就绪"

#. module: mrp
#: model:mail.message.subtype,description:mrp.mrp_mo_in_to_close
#: model:mail.message.subtype,name:mrp.mrp_mo_in_to_close
msgid "MO To Close"
msgstr "待关闭制造订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_document__attached_on_mrp
msgid "MRP : Visible at"
msgstr "MRP ：可见于"

#. module: mrp
#: model:ir.actions.client,name:mrp.mrp_reception_action
msgid "MRP Reception Report"
msgstr "物料需求计划（MRP）接收报表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_routings
msgid "MRP Work Orders"
msgstr "物料需求计划（MRP）工单"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss_type
msgid "MRP Workorder productivity losses"
msgstr "物料需求计划（MRP）工单生产力损失"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "MRP-001"
msgstr "MRP-001"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_routings
msgid "Manage Work Order Operations"
msgstr "管理工单作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__manual_consumption
msgid "Manual Consumption"
msgstr "手动消耗"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid "Manual Duration"
msgstr "手动时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manuf. Lead Time"
msgstr "制造前置时间"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_steps
#: model:ir.model.fields.selection,name:mrp.selection__stock_rule__action__manufacture
#: model:stock.route,name:mrp.route_warehouse0_manufacture
#: model_terms:ir.ui.view,arch_db:mrp.mrp_report_stock_rule
msgid "Manufacture"
msgstr "制造"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__mrp_one_step
msgid "Manufacture (1 step)"
msgstr "制造（1个步骤）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_mto_pull_id
msgid "Manufacture MTO Rule"
msgstr "制造的订货制造（MTO）规则"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_pull_id
msgid "Manufacture Rule"
msgstr "制造规则"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid "Manufacture Security Lead Time"
msgstr "制造安全提前期"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__type__normal
msgid "Manufacture this product"
msgstr "制造该产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manufacture_to_resupply
msgid "Manufacture to Resupply"
msgstr "制造补给"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__mrp_product_qty
#: model:ir.model.fields,field_description:mrp.field_product_template__mrp_product_qty
msgid "Manufactured"
msgstr "已制造"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_product_search_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_product_template_search_view
msgid "Manufactured Products"
msgstr "已制造产品"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Manufactured in the last 365 days"
msgstr "过去365天已制造"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
#: model:ir.actions.client,name:mrp.action_mrp_display_fullscreen
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__code__mrp_operation
#: model:ir.ui.menu,name:mrp.menu_mrp_root
#: model_terms:ir.ui.view,arch_db:mrp.product_document_form
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Manufacturing"
msgstr "制造"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_buttons.xml:0
msgid "Manufacturing Forecast"
msgstr "制造预测"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__produce_delay
#: model:ir.model.fields,field_description:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__manufacturing_lead
msgid "Manufacturing Lead Time"
msgstr "制造提前期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__manu_type_id
msgid "Manufacturing Operation Type"
msgstr "制造作业类型"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__mrp_production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__mo_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__production_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__production_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.stock_scrap_search_view_inherit_mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Manufacturing Order"
msgstr "制造订单"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.act_product_mrp_production_workcenter
#: model:ir.actions.act_window,name:mrp.action_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_production_action
#: model:ir.actions.act_window,name:mrp.mrp_production_action_picking_deshboard
#: model:ir.ui.menu,name:mrp.menu_mrp_production_action
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_view_activity
#: model_terms:ir.ui.view,arch_db:mrp.view_production_calendar
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_production_pivot
msgid "Manufacturing Orders"
msgstr "制造订单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Manufacturing Orders which are in confirmed state."
msgstr "状态为已确认的制造订单。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__ready_to_produce
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Manufacturing Readiness"
msgstr "制造准备就绪"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Manufacturing Reference"
msgstr "制造参考号"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Manufacturing Visibility Days"
msgstr "制造可见性天数"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc."
msgstr ""
"在工作中心处理的制造作业。工作中心可由\n"
"                工作和/或机器组成，用于成本计算、调度和产能规划等。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_kanban_action
msgid ""
"Manufacturing operations are processed at Work Centers. A Work Center can be composed of\n"
"                workers and/or machines, they are used for costing, scheduling, capacity planning, etc.\n"
"                They can be defined via the configuration menu."
msgstr ""
"在工作中心处理的制造作业。工作中心可由\n"
"                工作和/或机器组成，用于成本计算、调度和产能规划等。\n"
"                可以通过配置菜单规定。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reservation_state
#: model:ir.model.fields,help:mrp.field_mrp_workorder__production_availability
msgid ""
"Manufacturing readiness for this MO, as per bill of material configuration:\n"
"            * Ready: The material is available to start the production.\n"
"            * Waiting: The material is not available to start the production.\n"
msgstr ""
"根据物料清单配置，该制造订单（MO）准备就绪：\n"
"            * 准备就绪：材料可用，可以开始制造。\n"
"            * 等待中：材料不可用，不能开始制造。\n"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation
#: model:ir.actions.act_window,name:mrp.action_picking_tree_mrp_operation_graph
#: model:ir.ui.menu,name:mrp.mrp_operation_picking
msgid "Manufacturings"
msgstr "制造"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Marc Demo"
msgstr "马克-迪莫"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_mark_done
msgid "Mark as Done"
msgstr "标记为已完成"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Mass Produce"
msgstr "批量生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_mps
msgid "Master Production Schedule"
msgstr "主生产计划"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason0
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Material Availability"
msgstr "物料可用性"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_merge
msgid "Merge"
msgstr "合并"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_error
msgid "Message Delivery error"
msgstr "消息发送错误"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_ids
msgid "Messages"
msgstr "消息"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "最小库存规则"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Minutes"
msgstr "分钟"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Miscellaneous"
msgstr "杂项"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_byproduct_ids
msgid "Move Byproduct"
msgstr "移动副产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_line_ids
msgid "Moves to Track"
msgstr "待追踪移动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_consumption_warning_line_ids
msgid "Mrp Consumption Warning Line"
msgstr "MRP消耗警告行"

#. module: mrp
#: model:ir.actions.client,name:mrp.action_mrp_display
msgid "Mrp Display"
msgstr "MRP 显示"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__mrp_production_ids
#: model:ir.model.fields,field_description:mrp.field_procurement_group__mrp_production_ids
msgid "Mrp Production"
msgstr "MRP生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning__mrp_production_count
msgid "Mrp Production Count"
msgstr "MRP生产计数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__my_activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "My MOs"
msgstr "我的制造订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__never_product_template_attribute_value_ids
msgid "Never attribute values"
msgstr "切勿赋值"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_unbuild.py:0
#: code:addons/mrp/models/stock_rule.py:0
msgid "New"
msgstr "新建"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "New BoM from %(mo_name)s"
msgstr "来自%(mo_name)s的新BoM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Next Activity"
msgstr "下一个活动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_calendar_event_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一活动日历事件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_date_deadline
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_summary
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "No Backorder"
msgstr "无欠单"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_bom_form_action
msgid "No bill of materials found. Let's create one!"
msgstr "未找到物料清单（BOM）。请创建！"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "No data available."
msgstr "无可用数据。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_routing_time
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workcenter_load_report_graph
msgid "No data yet!"
msgstr "无数据！"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid "No manufacturing order found. Let's create one."
msgstr "未找到制造订单。请创建。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.product_template_action
msgid "No product found. Let's create one!"
msgstr "未找到产品。请创建！"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_blocked
msgid "No productivity loss for this equipment"
msgstr "该设备无生产力损失"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_unbuild
msgid "No unbuild order found"
msgstr "未找到分解单"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid "No work orders to do!"
msgstr "无待办工单！"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid ""
"No workorder currently in progress. Click to mark work center as blocked."
msgstr "没有正在进行的工单。点击，将工作中心标记为受阻。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter__working_state__normal
msgid "Normal"
msgstr "正常"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__components_availability_state__unavailable
msgid "Not Available"
msgstr "不可用"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "Not Ready"
msgstr "未准备好"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Note that another version of this BOM is available."
msgstr "请注意，该 BOM 还有另一个版本。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid ""
"Note that archived work center(s): '%s' is/are still linked to active Bill "
"of Materials, which means that operations can still be planned on it/them. "
"To prevent this, deletion of the work center is recommended instead."
msgstr "请注意，已归档工作中心：“%s”仍关联至有效的物料清单，这意味着仍可以为其计划作业。为防止这种情况，建议删除工作中心。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/product.py:0
msgid ""
"Note that product(s): '%s' is/are still linked to active Bill of Materials, "
"which means that the product can still be used on it/them."
msgstr "请注意，产品：“%s”仍关联至有效的物料清单, 这意味着仍可为其使用该产品。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_needaction_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_needaction_counter
msgid "Number of Actions"
msgstr "动作数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_in_progress
msgid "Number of Manufacturing Orders In Progress"
msgstr "在进行中的制造订单数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_late
msgid "Number of Manufacturing Orders Late"
msgstr "延迟制造订单的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_to_close
msgid "Number of Manufacturing Orders To Close"
msgstr "制造订单关闭数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_waiting
msgid "Number of Manufacturing Orders Waiting"
msgstr "等待中制造订单的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__count_mo_todo
msgid "Number of Manufacturing Orders to Process"
msgstr "待处理制造订单的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__lot_qty
msgid "Number of SN"
msgstr "SN编码"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_count
msgid "Number of Unbuilds"
msgstr "分解数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_error_counter
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_child_count
msgid "Number of generated MO"
msgstr "已生成物料清单（MO）的数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_needaction_counter
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的消息数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_production__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__message_has_error_counter
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "出现发送错误的消息的数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__capacity
msgid "Number of pieces that can be produced in parallel for this product."
msgstr "可以为该产品并行生产的件数。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__mrp_production_source_count
msgid "Number of source MO"
msgstr "源物料清单（MO）的数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "OEE"
msgstr "设备综合效率（OEE）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee_target
msgid "OEE Target"
msgstr "设备综合效率（OEE）目标"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid ""
"Odoo opens a PDF preview by default. If you want to print instantly,\n"
"                                install the IoT App on a computer that is on the same local network as the\n"
"                                barcode operator and configure the routing of the reports."
msgstr ""
"Odoo 默认打开 PDF 预览。如果您想立即打印、\n"
"                                在与条码操作员位于同一本地网络的计算机上安装 IoT 应用程序，并配置报表的路由。\n"
"                                条码操作员的计算机上安装物联网应用程序，并配置报表的路由。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__oee
msgid "Oee"
msgstr "设备综合效率（OEE）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "On Hand"
msgstr "现有"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Only manufacturing orders in either a draft or confirmed state can be %s."
msgstr "仅处于草稿或已确认状态的制造订单可以%s。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Only manufacturing orders with a Bill of Materials can be %s."
msgstr "仅具有物料清单的制造订单可以%s。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Open"
msgstr "开启"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view_mo_form
msgid "Open Work Order"
msgstr "打开工单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__operation_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Operation"
msgstr "作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__allow_operation_dependencies
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__allow_operation_dependencies
msgid "Operation Dependencies"
msgstr "作业依赖性"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__operation_id
msgid "Operation To Consume"
msgstr "待消耗作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__picking_type_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_type_id
msgid "Operation Type"
msgstr "作业类型"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_routing_action
msgid ""
"Operation define that need to be done to realize a Work Order.\n"
"                Each operation is done at a specific Work Center and has a specific duration."
msgstr ""
"作业规定完成工单需完成的工作。\n"
"                各项作业在特定工作中心完成，且有特定时长。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/product.py:0
msgid "Operation not supported"
msgstr "不支持作业"

#. module: mrp
#. odoo-javascript
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: code:addons/mrp/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
#: code:addons/mrp/static/src/components/mo_overview_operations_block/mrp_mo_overview_operations_block.xml:0
#: model:ir.actions.act_window,name:mrp.mrp_routing_action
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__allowed_operation_ids
#: model:ir.model.fields,field_description:mrp.field_stock_move__allowed_operation_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_manufacturing
#: model:ir.ui.menu,name:mrp.menu_mrp_routing_action
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_operations
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_search_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_calendar
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_graph
#: model_terms:ir.ui.view,arch_db:mrp.workcenter_line_pivot
msgid "Operations"
msgstr "作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Done"
msgstr "已完成作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Operations Planned"
msgstr "已安排的作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
msgid "Operations Search Filters"
msgstr "作业搜索筛选器"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__needed_by_operation_ids
msgid "Operations that cannot start before this operation is completed."
msgstr "该作业完成前无法开始的作业。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__blocked_by_operation_ids
msgid "Operations that need to be completed before this operation can start."
msgstr "该作业开始前需完成的作业。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__orderpoint_id
msgid "Orderpoint"
msgstr "订购点"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__order_ids
msgid "Orders"
msgstr "订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_production
msgid "Original Production Quantity"
msgstr "原始生产数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__is_outdated_bom
msgid "Outdated BoM"
msgstr "过时的 BoM"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee_target
msgid "Overall Effective Efficiency Target in percentage"
msgstr "总体有效效率目标百分比"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_oee
#: model:ir.ui.menu,name:mrp.menu_mrp_workcenter_productivity_report
msgid "Overall Equipment Effectiveness"
msgstr "设备综合效率"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__oee
msgid "Overall Equipment Effectiveness, based on the last month"
msgstr "基于上个月的设备综合效率"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report
#: model_terms:ir.actions.act_window,help:mrp.mrp_workcenter_productivity_report_oee
msgid "Overall Equipment Effectiveness: no working or blocked time"
msgstr "设备综合效率：无工作或受阻日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__done_mrp_lot_label_to_print__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__generated_mrp_lot_label_to_print__pdf
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__mrp_product_label_to_print__pdf
msgid "PDF"
msgstr "PDF"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Package barcode"
msgstr "包装条形码"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__bom_id
msgid "Parent BoM"
msgstr "上级物料清单（BOM）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__parent_product_tmpl_id
msgid "Parent Product Template"
msgstr "上级产品模板"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__mrp_consumption_warning_id
msgid "Parent Wizard"
msgstr "上级向导"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__worksheet_google_slide
#: model:ir.model.fields,help:mrp.field_mrp_workorder__worksheet_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr "粘贴谷歌幻灯片网址。 确保文件可公开访问。"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_pause_workorders
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Pause"
msgstr "暂停"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Pending"
msgstr "待定"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__performance
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__performance
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Performance"
msgstr "效能"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Performance Losses"
msgstr "绩效损失"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__performance
msgid "Performance over the last month"
msgstr "上个月绩效"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick Components"
msgstr "拣取组件"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components and then manufacture"
msgstr "拣取组件，进行制造"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm
msgid "Pick components then manufacture (2 steps)"
msgstr "挑选组件然后制造（2步）"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pick components, manufacture and then store products (3 steps)"
msgstr "拣取组件，进行制造，储存产品（3个步骤）"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_warehouse__manufacture_steps__pbm_sam
msgid "Pick components, manufacture, then store products (3 steps)"
msgstr "挑选组件，制造，并入库（3步）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_mto_pull_id
msgid "Picking Before Manufacturing MTO Rule"
msgstr "在订货制造（MTO）规则前拣取"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_type_id
msgid "Picking Before Manufacturing Operation Type"
msgstr "在制造作业类型前拣取"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_route_id
msgid "Picking Before Manufacturing Route"
msgstr "在制造路线前拣取"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking_type
msgid "Picking Type"
msgstr "拣货类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__picking_ids
msgid "Picking associated to this manufacturing order"
msgstr "与该制造订单相关的拣取"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__pbm_loc_id
msgid "Picking before Manufacturing Location"
msgstr "在制造位置前拣取"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "Pieces"
msgstr "暂停"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Plan"
msgstr "安排"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Plan Orders"
msgstr "安排订单"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_plan_with_components_availability
msgid "Plan based on Components Availability"
msgstr "基于组件可用性的计划"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Plan manufacturing or purchase orders based on forecasts"
msgstr "根据预测安排制造订单或采购订单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Planned"
msgstr "已安排"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Planned at the same time as other workorder(s) at %s"
msgstr "与%s的其它工单安排在同期"

#. module: mrp
#: model:ir.ui.menu,name:mrp.mrp_planning_menu_root
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Planning"
msgstr "排期"

#. module: mrp
#: model:product.template,name:mrp.product_product_plastic_laminate_product_template
msgid "Plastic Laminate"
msgstr "塑料层压材料"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_move.py:0
msgid "Please enter a positive quantity."
msgstr "请输入正数。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Please set the first Serial Number or a default sequence"
msgstr "请设置第一个序列号或默认序列"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Please specify the first serial number you would like to use."
msgstr "请指定您要使用的第一个序列号。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid "Please specify the serial number you would like to use."
msgstr "请指定您要使用的序列号。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Please unblock the work center to start the work order."
msgstr "请解除对工作中心的封锁，以启动工单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Please unblock the work center to validate the work order"
msgstr "请解除对工作中心的封锁以验证工单"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_ply_product_template
msgid "Ply Layer"
msgstr "帘布层"

#. module: mrp
#: model:product.template,name:mrp.product_product_ply_veneer_product_template
msgid "Ply Veneer"
msgstr "帘布板"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__json_popover
msgid "Popover Data JSON"
msgstr "JSON数据弹窗"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__possible_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__possible_bom_product_template_attribute_value_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__possible_bom_product_template_attribute_value_ids
msgid "Possible Product Template Attribute Value"
msgstr "可能的产品模板属性值"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Post-Production"
msgstr "生产后"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Pre-Production"
msgstr "生产前"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Prepare MO"
msgstr "准备制造订单"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
msgid "Print"
msgstr "打印"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Print All Variants"
msgstr "打印所有变体"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_print_labels
msgid "Print Labels"
msgstr "标签打印"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.xml:0
msgid "Print Work Order"
msgstr "打开工单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print labels as:"
msgstr "打印标签为"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print when \"Create new Lot/SN\""
msgstr "创建新批次/SN \"时打印"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Print when done"
msgstr "完成后打印"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__priority
msgid "Priority"
msgstr "优先级"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason5
msgid "Process Defect"
msgstr "工艺缺陷"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process manufacturing orders from the barcode application"
msgstr "通过条形码应用程序处理制造订单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Process operations at specific work centers"
msgstr "特定工作中心的过程作业"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__produce_line_ids
msgid "Processed Disassembly Lines"
msgstr "已处理的拆解行"

#. module: mrp
#: model:ir.model,name:mrp.model_procurement_group
#: model:ir.model.fields,field_description:mrp.field_mrp_production__procurement_group_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Procurement Group"
msgstr "采购组"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_procurement_compute_mrp
msgid "Procurement: run scheduler"
msgstr "补货: 运行调度器"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_batch_produce_form
msgid "Produce"
msgstr "生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Produce All"
msgstr "生产全部"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_batch_produce
msgid "Produce a batch of production order"
msgstr "生产一批制造订单"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_byproducts
msgid "Produce residual products"
msgstr "生产剩余产品"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Produce residual products (A + B -> C + D)"
msgstr "生产剩余产品（A + B -> C + D）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "Produced"
msgstr "已生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__operation_id
msgid "Produced in Operation"
msgstr "在作业口已生产"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model,name:mrp.model_product_template
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_id
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Product"
msgstr "产品"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Product Attachments"
msgstr "产品附件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__capacity_ids
msgid "Product Capacities"
msgstr "产品产能"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__product_catalog_product_is_in_bom
msgid "Product Catalog Product Is In Bom"
msgstr "产品目录中的产品在物料清单中"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_product__product_catalog_product_is_in_mo
msgid "Product Catalog Product Is In Mo"
msgstr "产品目录中的产品在制造订单中"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Product Cost"
msgstr "产品成本"

#. module: mrp
#: model:ir.model,name:mrp.model_product_document
msgid "Product Document"
msgstr "产品文件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_virtual_available
msgid "Product Forecasted Quantity"
msgstr "预测的产品数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__mrp_product_label_to_print
msgid "Product Label to Print"
msgstr "要打印的产品标签"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Product Labels"
msgstr "产品标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_plm
msgid "Product Lifecycle Management (PLM)"
msgstr "产品生命周期管理（PLM）"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "产品移动（库存移动行）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__product_qty_available
msgid "Product On Hand Quantity"
msgstr "现有产品数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Product Quantity"
msgstr "产品数量"

#. module: mrp
#: model:ir.model,name:mrp.model_product_replenish
msgid "Product Replenish"
msgstr "补料"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "产品补货混入程序"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tmpl_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__product_template
msgid "Product Template"
msgstr "产品模板"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_uom_id
msgid "Product Unit of Measure"
msgstr "产品计量单位"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__product_uom_id
msgid "Product UoM"
msgstr "产品计量单位"

#. module: mrp
#: model:ir.model,name:mrp.model_product_product
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_id
msgid "Product Variant"
msgstr "产品变体"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_product_variant_action
#: model:ir.ui.menu,name:mrp.product_variant_mrp
msgid "Product Variants"
msgstr "产品变体"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_capacity_unique_product
msgid "Product capacity should be unique for each workcenter."
msgstr "各工作中心的产品产能应唯一。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Product to build..."
msgstr "待构建产品......"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_batch_produce__production_id
#: model:ir.model.fields,field_description:mrp.field_picking_label_type__production_ids
#: model:ir.model.fields,field_description:mrp.field_stock_picking__production_ids
msgid "Production"
msgstr "生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_capacity
msgid "Production Capacity"
msgstr "产能"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_date
msgid "Production Date"
msgstr "生产日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Production Information"
msgstr "生产信息"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__production_location_id
msgid "Production Location"
msgstr "生产位置"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_production_order
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__production_id
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Production Order"
msgstr "制造订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__raw_material_production_id
msgid "Production Order for components"
msgstr "组件制造订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__production_id
msgid "Production Order for finished products"
msgstr "成品制造订单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_state
msgid "Production State"
msgstr "生产状态"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Production Workcenter"
msgstr "生产工作中心"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/mrp_forecasted/forecasted_details.xml:0
msgid "Production of Draft MO"
msgstr "草稿状态制造订单（MO）的生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Production started late"
msgstr "延迟开始的生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_multi__production_ids
msgid "Productions To Split"
msgstr "待拆分生产"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__productive
msgid "Productive"
msgstr "生产力"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__productive_time
msgid "Productive Time"
msgstr "生产日期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__productive_time
msgid "Productive hours over the last month"
msgstr "上个月生产小时数"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Productivity"
msgstr "生产力"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_productivity_report_blocked
msgid "Productivity Losses"
msgstr "生产力损失"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.product_template_action
#: model:ir.ui.menu,name:mrp.menu_mrp_bom
#: model:ir.ui.menu,name:mrp.menu_mrp_product_form
msgid "Products"
msgstr "产品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__progress
msgid "Progress Done (%)"
msgstr "完成进度（%）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__propagate_cancel
msgid "Propagate cancel and split"
msgstr "传播取消和拆分"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workcenter_productivity_loss_type__loss_type__quality
msgid "Quality"
msgstr "质量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Quality Losses"
msgstr "质量损失"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "质量工作表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quant_ids
msgid "Quant"
msgstr "量化"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_produced
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__quantity
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_line_view_form
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Quantity"
msgstr "数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view_mo_form
#: model_terms:ir.ui.view,arch_db:mrp.view_production_graph
msgid "Quantity Produced"
msgstr "已生产的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__qty_producing
msgid "Quantity Producing"
msgstr "正在生产的数量"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Quantity Remaining"
msgstr "剩余数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__qty_remaining
msgid "Quantity To Be Produced"
msgstr "待生产的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__should_consume_qty
msgid "Quantity To Consume"
msgstr "待消耗的数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_change_production_qty__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__product_qty
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__quantity
msgid "Quantity To Produce"
msgstr "待生产的数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__production_capacity
#: model:ir.model.fields,help:mrp.field_mrp_production_split__production_capacity
msgid "Quantity that can be produced with the current stock of components"
msgstr "使用当前组件库存可以生产的数量"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_quant
msgid "Quants"
msgstr "量化"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "RAM"
msgstr "RAM"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__rating_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__rating_ids
msgid "Ratings"
msgstr "评分"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__move_raw_ids
msgid "Raw Moves"
msgstr "原材料移动"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__assigned
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__ready
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Ready"
msgstr "准备就绪"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Ready to Produce"
msgstr "生产准备就绪"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Real Cost"
msgstr "实际成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Real Costs"
msgstr "实际成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__duration
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__duration
msgid "Real Duration"
msgstr "实际时长"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Receipt"
msgstr "收据"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Receipts"
msgstr "收据"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Reception time estimation."
msgstr "估计接收日期。"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason4
msgid "Reduced Speed"
msgstr "减慢的速度"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason6
msgid "Reduced Yield"
msgstr "减少的产量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__code
#: model:ir.model.fields,field_description:mrp.field_mrp_production__name
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__name
msgid "Reference"
msgstr "参考号"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_name_uniq
msgid "Reference must be unique per Company!"
msgstr "公司参考号须唯一！"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__origin
msgid ""
"Reference of the document that generated this production order request."
msgstr "生成该制造订单申请的文件的参考号。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Reference:"
msgstr "参考号："

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_remaining_days_unformatted_field.js:0
msgid "Remaining Days"
msgstr "剩余天数"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Replan"
msgstr "重新安排"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
msgid "Replenish"
msgstr "补货"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Replenish on Order (MTO)"
msgstr "订单补货补给（订货生产（MTO））"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Replenishments"
msgstr "补货"

#. module: mrp
#: model:ir.ui.menu,name:mrp.menu_mrp_reporting
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "Reporting"
msgstr "报表"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Reserved"
msgstr "已预留"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_id
msgid "Resource"
msgstr "资源"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__user_id
msgid "Responsible"
msgstr "负责人"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__activity_user_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__activity_user_id
msgid "Responsible User"
msgstr "负责用户"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "Resupply lead time."
msgstr "重新供应提前期。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_bom
msgid "Route"
msgstr "路线"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "Routing"
msgstr "工艺路线"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__routing_line_ids
msgid "Routing Lines"
msgstr "工艺路线行"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_tree_view
msgid "Routing Work Centers"
msgstr "工艺路线工作中心"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_production__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__message_has_sms_error
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信发送错误"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__date
msgid "Schedule Date"
msgstr "安排日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Schedule manufacturing orders earlier to avoid delays"
msgstr "提前安排制造订单，以避免延误"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scheduled Date"
msgstr "安排的日期"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Scheduled End"
msgstr "完成日期"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"Scheduled before the previous work order, planned from %(start)s to %(end)s"
msgstr "在上一工单前已安排，安排为%(start)s至%(end)s"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/widgets/mrp_workorder_popover.xml:0
msgid "Scheduling Information"
msgstr "安排信息"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_scrap
#: model:ir.model,name:mrp.model_stock_scrap
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_scrap
msgid "Scrap"
msgstr "报废"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_count
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__scrap_count
msgid "Scrap Move"
msgstr "报废移动"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Scrap Products"
msgstr "废品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__scrap_ids
msgid "Scraps"
msgstr "报废"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_screw_product_template
msgid "Screw"
msgstr "螺丝"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
msgid "Search"
msgstr "搜索"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Search Bill Of Material"
msgstr "搜索物料清单（BOM）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Search Production"
msgstr "搜索生产"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Search Work Orders"
msgstr "搜索工单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Search for mrp workcenter"
msgstr "搜索MRP工作中心"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Security Lead Time"
msgstr "安全提前期"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_res_company__manufacturing_lead
#: model:ir.model.fields,help:mrp.field_res_config_settings__manufacturing_lead
msgid "Security days for each manufacturing operation."
msgstr "各制造作业的安全天数。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
msgid "Select Operations to Copy"
msgstr "选择要复制的作业"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Selection not supported."
msgstr "不支持选择。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity_loss__sequence
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__sequence
msgid "Sequence"
msgstr "序列"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid "Set Quantities & Validate"
msgstr "设置数量和验证"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_change_production_qty_wizard
msgid "Set Quantity"
msgstr "设置数量"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__time_mode__manual
msgid "Set duration manually"
msgstr "手动设置时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Set the order that work orders should be processed in. Activate the feature "
"within each BoM's Miscellaneous tab"
msgstr "设置工单处理顺序。在各物料清单（BOM）的杂项标签中启用该功能"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_configuration
#: model:ir.ui.menu,name:mrp.menu_mrp_config
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Settings"
msgstr "设置"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_start
msgid "Setup Time"
msgstr "设置时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__time_start
msgid "Setup Time (minutes)"
msgstr "设置时间 ( 分钟 )"

#. module: mrp
#: model:mrp.workcenter.productivity.loss,name:mrp.block_reason2
msgid "Setup and Adjustments"
msgstr "设置和调整"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_allocation
msgid "Show Allocation"
msgstr "显示分配"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse_orderpoint__show_bom
msgid "Show BoM column"
msgstr "显示物料清单（BOM）列"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_product_replenish__show_bom
#: model:ir.model.fields,field_description:mrp.field_stock_replenish_mixin__show_bom
msgid "Show Bom"
msgstr "显示用料清单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_final_lots
msgid "Show Final Lots"
msgstr "显示最终批次"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_lock
msgid "Show Lock/unlock buttons"
msgstr "显示锁定/解锁按钮"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__show_json_popover
msgid "Show Popover?"
msgstr "是否显示弹窗？"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce
msgid "Show Produce"
msgstr "显示生产"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__show_produce_all
msgid "Show Produce All"
msgstr "显示生产全部"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Show all records which has next action date is before today"
msgstr "显示下一操作日期早于今天的所有记录"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder__show_backorder_lines
msgid "Show backorder lines"
msgstr "显示欠单行"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__leave_id
msgid "Slot into workcenter calendar once planned"
msgstr "安排后插入工作中心日历"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_head_product_template
msgid "Solid wood is a durable natural material."
msgstr "实木是一种耐用的天然材料。"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_product_template
msgid "Solid wood table."
msgstr "实木桌子。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ""
"Some of your byproducts are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct byproducts."
msgstr "部分副产品已跟踪，须指定制造订单，以检索正确副产品。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid ""
"Some of your components are tracked, you have to specify a manufacturing "
"order in order to retrieve the correct components."
msgstr "部分副产品已跟踪，须指定制造订单，以检索正确组件。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Some work orders are already done, so you cannot unplan this manufacturing order.\n"
"\n"
"It’d be a shame to waste all that progress, right?"
msgstr ""
"有些工单已经完成，因此不能取消该制造单。\n"
"\n"
"如果浪费了所有的进度就太可惜了，不是吗？"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"Some work orders have already started, so you cannot unplan this manufacturing order.\n"
"\n"
"It’d be a shame to waste all that progress, right?"
msgstr ""
"有些工单已经开始，因此不能取消该制造单。\n"
"\n"
"如果浪费了所有的进度就太可惜了，不是吗？"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__origin
msgid "Source"
msgstr "来源"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__location_id
msgid "Source Location"
msgstr "源位置"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Specific Capacities"
msgstr "特定产能"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__capacity_ids
msgid ""
"Specific number of pieces that can be produced in parallel per product."
msgstr "各产品可以并行生产的特定件数。"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_production_order_split
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "Split"
msgstr "拆分"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__counter
msgid "Split #"
msgstr "拆分#"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_detailed_vals_ids
msgid "Split Details"
msgstr "拆分详情"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split_line__mrp_production_split_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Production"
msgstr "拆分生产"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_line
msgid "Split Production Detail"
msgstr "拆分生产详情"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__production_split_multi_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_multi_form
msgid "Split Productions"
msgstr "拆分生产"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split
msgid "Split production"
msgstr "拆分生产"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_split_multi
msgid "Split productions"
msgstr "拆分生产"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_screw_product_template
msgid "Stainless steel screw"
msgstr "不锈钢螺丝"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_computer_desk_bolt_product_template
msgid "Stainless steel screw full (dia - 5mm, Length - 10mm)"
msgstr "不锈钢螺丝满（直径5毫米，长度10毫米）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Starred"
msgstr "标星"

#. module: mrp
#: model:ir.actions.server,name:mrp.action_start_workorders
#: model:ir.model.fields,field_description:mrp.field_mrp_production__date_start
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "Start"
msgstr "开始"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__date_start
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Start Date"
msgstr "开始日期"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__state
msgid "State"
msgstr "状态"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__state
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Status"
msgstr "状态"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_state
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：超出到期日期\n"
"今天：活动日期是今天\n"
"计划：未来活动。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_type_id
msgid "Stock After Manufacturing Operation Type"
msgstr "制造后库存作业类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_rule_id
msgid "Stock After Manufacturing Rule"
msgstr "制造后库存规则"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__production_availability
msgid "Stock Availability"
msgstr "库存可用性"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_move
msgid "Stock Move"
msgstr "库存移动"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__move_dest_ids
msgid "Stock Movements of Produced Goods"
msgstr "已生产货物的库存移动"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_unbuild_moves
msgid "Stock Moves"
msgstr "库存移动"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "库存接收报表"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "库存补货报表"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_rule
msgid "Stock Rule"
msgstr "库存规则"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warehouse__sam_loc_id
msgid "Stock after Manufacturing Location"
msgstr "制造后库存位置"

#. module: mrp
#: model:ir.model,name:mrp.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "库存规则报表"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_warehouse.py:0
msgid "Store Finished Product"
msgstr "存储成品"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__child_bom_id
msgid "Sub BoM"
msgstr "子物料清单（BOM）"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__module_mrp_subcontracting
msgid "Subcontracting"
msgstr "外包"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_product_template
msgid "Table"
msgstr "桌子"

#. module: mrp
#: model:product.template,name:mrp.product_product_table_kit_product_template
msgid "Table Kit"
msgstr "桌子套件"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_leg_product_template
msgid "Table Leg"
msgstr "桌腿"

#. module: mrp
#: model:product.template,name:mrp.product_product_computer_desk_head_product_template
msgid "Table Top"
msgstr "桌面"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_table_kit_product_template
msgid "Table kit"
msgstr "桌子套件"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tag_ids
msgid "Tag"
msgstr "标签"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_tag__name
msgid "Tag Name"
msgstr "标签名称"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr "用于确定是否显示“分配”按钮的技术字段。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__has_routing_lines
msgid "Technical field for workcenter views"
msgstr "工作中心视图的技术字段"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce_all
msgid "Technical field to check if produce all button can be shown"
msgstr "查看是否显示“生产全部”按钮的技术字段"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__show_produce
msgid "Technical field to check if produce button can be shown"
msgstr "查看是否显示“生产”按钮的技术字段"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__reserve_visible
msgid "Technical field to check when we can reserve quantities"
msgstr "查看保留数量日期的技术字段"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__unreserve_visible
msgid "Technical field to check when we can unreserve"
msgstr "查看取消保留日期的技术字段"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_routing_workcenter__worksheet_type__text
msgid "Text"
msgstr "文本"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__is_outdated_bom
msgid "The BoM has been updated since creation of the MO"
msgstr "自创建 MO 以来，BoM 已经更新"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The Product Unit of Measure you chose has a different category than in the "
"product form."
msgstr "您选择的产品计量单位与产品表单中的计量单位类别不同。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The Workorder (%s) cannot be started twice!"
msgstr "工单（%s）不能开始两次！"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The attribute value %(attribute)s set on product %(product)s does not match "
"the BoM product %(bom_product)s."
msgstr "产品%(product)s中设置的属性值%(attribute)s与物料清单（BOM）产品%(bom_product)s不匹配。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "The capacity must be strictly positive."
msgstr "产能须为正数。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "The component %s should not be the same as the product to produce."
msgstr "组件%s不应与待生产产品相同。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The current configuration is incorrect because it would create a cycle "
"between these products: %s."
msgstr "当前的配置是不正确的，因为它会在这些产品之间创建一个循环：%s。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "The day after tomorrow"
msgstr "后天"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_orderpoint.py:0
msgid "The following replenishment order has been generated"
msgstr "已生成以下补货订单"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_produced
msgid "The number of products already handled by this work order"
msgstr "该工单已处理的产品数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__operation_id
msgid ""
"The operation where the components are consumed, or the finished products "
"created."
msgstr "消耗该等组件或创建成品的作业。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_byproduct__cost_share
msgid ""
"The percentage of the final production cost for this by-product line "
"(divided between the quantity produced).The total of all by-products' cost "
"share must be less than or equal to 100."
msgstr "该副产品线的最终生产成本百分比（由已生产数量分摊）。所有副产品的成本份额总和须小于或等于100。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_move__cost_share
msgid ""
"The percentage of the final production cost for this by-product. The total "
"of all by-products' cost share must be smaller or equal to 100."
msgstr "该副产品线的最终生产成本百分比。所有副产品的成本份额总和须小于或等于100。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"The planned end date of the work order cannot be prior to the planned start "
"date, please correct this to save the work order."
msgstr "工单计划结束日期不能早于计划开始日期，请更正，以保存工单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"The product has already been used at least once, editing its structure may "
"lead to undesirable behaviours. You should rather archive the product and "
"create a new one with a new bill of materials."
msgstr "该产品已至少使用一次，编辑产品结构可能导致非预期行为。应归档产品，并使用新的物料清单创建新产品。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workorder__qty_reported_from_previous_wo
msgid ""
"The quantity already produced awaiting allocation in the backorders chain."
msgstr "欠单链中待分配的已生产数量。"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_bom_qty_positive
#: model:ir.model.constraint,message:mrp.constraint_mrp_production_qty_positive
msgid "The quantity to produce must be positive!"
msgstr "待生产数量须为正数！"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_unbuild_qty_positive
msgid "The quantity to unbuild must be positive!"
msgstr "要拆解的数量必须是正数！"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The serial number %(number)s used for byproduct %(product_name)s has already"
" been produced"
msgstr "用于副产品%(product_name)s的序列号%(number)s已生产"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The serial number %(number)s used for component %(component)s has already "
"been consumed"
msgstr "用于组件%(component)s的序列号%(number)s已消耗"

#. module: mrp
#: model:ir.model.constraint,message:mrp.constraint_mrp_workcenter_tag_tag_name_unique
msgid "The tag name must be unique."
msgstr "标签名称须唯一。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "The total cost share for a BoM's by-products cannot exceed 100."
msgstr "单个物料清单（BOM）的副产品总成本份额不能超出100。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"The total cost share for a manufacturing order's by-products cannot exceed "
"100."
msgstr "单个制造订单的副产品总成本份额不能超出100。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_split_form
msgid "The total should be equal to the quantity to produce."
msgstr "总数应等于生产数量。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "The work order should have already been processed."
msgstr "该工单应已被处理。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid ""
"There are no components to consume. Are you still sure you want to continue?"
msgstr "没有待消耗组件。确定继续？"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
msgid "There is no defined calendar on workcenter %s."
msgstr "工作中心没有规定的日历%s。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid "There's no product move yet"
msgstr "无产品移动"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "This Week"
msgstr "本周"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__tz
msgid ""
"This field is used in order to define in which timezone the resources will "
"work."
msgstr "该字段用于规定资源工作的时区。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__time_efficiency
msgid ""
"This field is used to calculate the expected duration of a work order at "
"this work center. For example, if a work order takes one hour and the "
"efficiency factor is 100%, then the expected duration will be one hour. If "
"the efficiency factor is 200%, however the expected duration will be 30 "
"minutes."
msgstr ""
"该字段用于计算该工作中心的工单预期用时。 例如，如果工单用时1小时，且效率系数为100％，则预期用时为1小时， "
"如果效率系数为200％，则预期用时为30分钟。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
msgid "This is a BoM of type Kit!"
msgstr "这是类型套件的物料清单（BOM）！"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid ""
"This is the cost based on the BoM of the product. It is computed by summing "
"the costs of the components and operations needed to build the product."
msgstr "这是基于产品物料清单（BOM）的成本，通过汇总构建产品所需组件和作业的成本计算而得。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
msgid "This is the cost defined on the product."
msgstr "这是产品中规定的成本。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_unbuild_moves
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                You can filter on the product to see all the past movements for the product."
msgstr ""
"通过该菜单，可以完全跟踪特定产品的库存作业。\n"
"                可以在产品中过滤，以查看产品之前的所有移动。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production has been merge in %s"
msgstr "该生产已在%s中合并"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This production order has been created from Replenishment Report."
msgstr "该制造订单已由补货报表创建。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "This serial number for product %s has already been produced"
msgstr "产品%s的序列号已生产"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_qty
msgid ""
"This should be the smallest quantity that this product can be produced in. "
"If the BOM contains operations, make sure the work center capacity is "
"accurate."
msgstr "这应该是该产品可以生产的最小数量。如果物料清单（BOM）包含工作业，应确保工作中心产能准确无误。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__time_ids
msgid "Time"
msgstr "时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_efficiency
msgid "Time Efficiency"
msgstr "时间效率"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__time_ids
msgid "Time Logs"
msgstr "时间日志"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Time Tracking"
msgstr "时间跟踪"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Time Tracking: %(user)s"
msgstr "时间跟踪：%(user)s"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_stop
msgid "Time in minutes for the cleaning."
msgstr "清理时间，以分钟计。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_workcenter_capacity__time_start
msgid "Time in minutes for the setup."
msgstr "设置时间，以分钟计。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__time_cycle_manual
msgid ""
"Time in minutes:- In manual mode, time used- In automatic mode, supposed "
"first time when there aren't any work orders yet"
msgstr "时间，以分钟计：-在手动模式下，为所用时间-在自动模式下，为无任何工单的假设第一次所用时间"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__tz
msgid "Timezone"
msgstr "时区"

#. module: mrp
#: model:digest.tip,name:mrp.digest_tip_mrp_0
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid "Tip: Use tablets in the shop to control manufacturing"
msgstr "提示：在店铺中使用平板电脑控制制造"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "To"
msgstr "至"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_backorder_line__to_backorder
msgid "To Backorder"
msgstr "至欠单"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__state__to_close
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Close"
msgstr "待关闭"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_expected_qty_uom
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_production_components
msgid "To Consume"
msgstr "待消耗"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "To Do"
msgstr "待办"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "To Launch"
msgstr "待启动"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_production_type_kanban
msgid "To Manufacture"
msgstr "制造"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_mo_overview.py:0
msgid "To Order"
msgstr "待订购"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_finished
msgid "To Produce"
msgstr "待生产"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__today
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today"
msgstr "今天"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_search_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Today Activities"
msgstr "今日活动"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Tomorrow"
msgstr "明天"

#. module: mrp
#: model_terms:product.template,description:mrp.product_product_wood_wear_product_template
msgid "Top layer of a wood panel."
msgstr "木板顶层."

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Components"
msgstr "组件总成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Operations"
msgstr "业务总成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Total Cost of Production"
msgstr "总生产成本"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_bom_tree_view
msgid "Total Duration"
msgstr "总时长"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_late_count
msgid "Total Late Orders"
msgstr "延迟订单合计"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_pending_count
msgid "Total Pending Orders"
msgstr "未结订单合计"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total Qty"
msgstr "总数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_uom_qty
msgid "Total Quantity"
msgstr "总数量"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workorder_progress_count
msgid "Total Running Orders"
msgstr "运行订单合计"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_stock_move_operations_raw
msgid "Total To Consume"
msgstr "待消耗总计"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total expected duration"
msgstr "总预计时长"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration_expected
msgid "Total expected duration (in minutes)"
msgstr "总预计时长（分钟）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "Total real duration"
msgstr "总实际时长"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__duration
msgid "Total real duration (in minutes)"
msgstr "总实际时长（分钟）"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.view_picking_type_form_inherit_mrp
msgid "Traceability"
msgstr "可追溯性"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_traceability_report
msgid "Traceability Report"
msgstr "可追溯性报表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_line__tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_production__product_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__has_tracking
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_tracking
msgid "Tracking"
msgstr "跟踪"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_picking
msgid "Transfer"
msgstr "调拨"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Transfers"
msgstr "转移"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_picking_type__code
msgid "Type of Operation"
msgstr "作业类型"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_production__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__activity_exception_decoration
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录中异常活动的类型。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unable to split with more than the quantity to produce."
msgstr "分拆不能超出待生产数量。"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/wo_list_view_dropdown/wo_list_view_dropdown.js:0
msgid "Unblock"
msgstr "解除阻塞"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__unbuild_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild"
msgstr "分解"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view_simplified
msgid "Unbuild Order"
msgstr "分解单"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_unbuild
#: model:ir.ui.menu,name:mrp.menu_mrp_unbuild
#: model_terms:ir.ui.view,arch_db:mrp.mrp_unbuild_form_view
msgid "Unbuild Orders"
msgstr "分解单"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "Unbuild: %s"
msgstr "分解：%s"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__unbuild_ids
msgid "Unbuilds"
msgstr "分解"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Unbuilt"
msgstr "已分解"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.js:0
msgid "Unfold"
msgstr "展开"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrp_workorder
msgid "Unit"
msgstr "单元"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
msgid "Unit Cost"
msgstr "单位成本"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_display_filter/mrp_mo_overview_display_filter.js:0
msgid "Unit Costs"
msgstr "单位成本"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__unit_factor
msgid "Unit Factor"
msgstr "单位因子"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview/mrp_mo_overview.xml:0
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_bom_byproduct__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_consumption_warning_line__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__product_uom_id
#: model:ir.model.fields,field_description:mrp.field_stock_warn_insufficient_qty_unbuild__product_uom_name
#: model_terms:ir.ui.view,arch_db:mrp.mo_overview_content
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_bom_filter
msgid "Unit of Measure"
msgstr "计量单位"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__product_uom_id
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__product_uom_id
msgid ""
"Unit of Measure (Unit of Measure) is the unit of measurement for the "
"inventory control"
msgstr "计量单位是库存控制的计量单位"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Units"
msgstr "单位"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_unlocked_by_default
msgid "Unlock Manufacturing Orders"
msgstr "解锁制造订单"

#. module: mrp
#: model:res.groups,name:mrp.group_unlocked_by_default
msgid "Unlocked by default"
msgstr "默认解锁"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unplan"
msgstr "取消安排"

#. module: mrp
#: model:ir.actions.server,name:mrp.mrp_production_action_unreserve_tree
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Unreserve"
msgstr "取消保留"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_tree_view
msgid "UoM"
msgstr "计量单位"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
msgid "Update BoM"
msgstr "更新BoM"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Upload your PDF file."
msgstr "上传PDF文件。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__priority__1
msgid "Urgent"
msgstr "紧急"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.mrp_production_action
msgid ""
"Use Manufacturing Orders (MO) to build finished products while consuming "
"components: i.e. 1 Table = 4 Table Legs + 1 Table Top"
msgstr "使用制造订单（MO）构建成品，同时消耗组件：例如，1张桌子 = 4个桌腿 + 1个桌面"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_workorder_dependencies
msgid "Use Operation Dependencies"
msgstr "使用作业依赖性"

#. module: mrp
#: model:res.groups,name:mrp.group_mrp_reception_report
msgid "Use Reception Report with Manufacturing Orders"
msgstr "将接收报表与制造订单一起使用"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.product_product_form_view_bom_button
#: model_terms:ir.ui.view,arch_db:mrp.product_template_form_view_bom_button
msgid "Used In"
msgstr "用于"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__user_id
#: model:res.groups,name:mrp.group_mrp_user
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "User"
msgstr "用户"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Using a MPS report to schedule your reordering and manufacturing operations "
"is useful if you have long lead time and if you produce based on sales "
"forecasts."
msgstr "如果提前期较长，且根据销售预测进行制造，则使用主制造计划（MPS）报告安排重新订购和制造作业很有用。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production_split__valid_details
msgid "Valid"
msgstr "有效"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__valid_product_template_attribute_line_ids
msgid "Valid Product Attribute Lines"
msgstr "有效产品属性明细行"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_backorder_form
msgid "Validate"
msgstr "验证"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_consumption_warning.py:0
msgid ""
"Values cannot be set and validated because a Lot/Serial Number needs to be "
"specified for a tracked product that is having its consumed amount "
"increased:%(products)s"
msgstr "由于需要为消耗量增加的跟踪产品指定批次/序列号，因此无法设置和验证值：%(products)s"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
msgid "Variant"
msgstr "变体"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.report_mrporder
msgid "Vendor ABC"
msgstr "供应商 ABC"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "View WorkOrder"
msgstr "查看工单"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"View and allocate production quantities to customer orders or other "
"manufacturing orders"
msgstr "查看并分配客户订单或其他制造订单的生产数量"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse_orderpoint__manufacturing_visibility_days
msgid "Visibility Days applied on the manufacturing routes."
msgstr "应用于制造路线的可见性天数。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Waiting"
msgstr "等待中"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__reservation_state__waiting
msgid "Waiting Another Operation"
msgstr "等待另一作业"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Waiting Availability"
msgstr "等待可用性"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__pending
msgid "Waiting for another WO"
msgstr "等待另一工单（WO）"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_workorder__state__waiting
msgid "Waiting for components"
msgstr "等待组件"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "Waiting the previous work order, planned from %(start)s to %(end)s"
msgstr "等待上一工单，安排从%(start)s到%(end)s"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_control_panel/mrp_bom_overview_control_panel.xml:0
#: model:ir.model,name:mrp.model_stock_warehouse
#: model:ir.model.fields,field_description:mrp.field_mrp_production__warehouse_id
msgid "Warehouse"
msgstr "仓库"

#. module: mrp
#: model:ir.model,name:mrp.model_stock_warn_insufficient_qty_unbuild
msgid "Warn Insufficient Unbuild Quantity"
msgstr "分解数量不足警告"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
#: code:addons/mrp/models/mrp_production.py:0
#: code:addons/mrp/models/stock_scrap.py:0
msgid "Warning"
msgstr "警告"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Warnings"
msgstr "警告"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_wear_product_template
msgid "Wear Layer"
msgstr "耐磨层"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_unbuild__website_message_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__website_message_ids
msgid "Website Messages"
msgstr "网站消息"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_production__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_routing_workcenter__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_unbuild__website_message_ids
#: model:ir.model.fields,help:mrp.field_mrp_workcenter__website_message_ids
msgid "Website communication history"
msgstr "网站沟通记录"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom__picking_type_id
msgid ""
"When a procurement has a ‘produce’ route with a operation type set, it will "
"try to create a Manufacturing Order for that product using a BoM of the same"
" operation type. That allows to define stock rules which trigger different "
"manufacturing orders with different BoMs."
msgstr ""
"采购具有设定作业类型的“制造”路线时，将尝试使用相同作业类型的物料清单（BOM）为产品创建制造订单，可以规定触发具有不同物料清单的不同制造订单的采购规则。"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_mrp_bom_line__manual_consumption
#: model:ir.model.fields,help:mrp.field_stock_move__manual_consumption
msgid ""
"When activated, then the registration of consumption for that component is recorded manually exclusively.\n"
"If not activated, and any of the components consumption is edited manually on the manufacturing order, Odoo assumes manual consumption also."
msgstr ""
"如果启用，该组件的消耗登记将完全手动记录。\n"
"如果未启用，并且在生产订单中对任何组件的消耗进行了手动编辑，Odoo也会视其为手动消耗。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_bom__ready_to_produce__asap
msgid "When components for 1st operation are available"
msgstr "当首次作业的组件可用时"

#. module: mrp
#: model:ir.model.fields,help:mrp.field_stock_warehouse__manufacture_to_resupply
msgid ""
"When products are manufactured, they can be manufactured in this warehouse."
msgstr "如果产品已制造，则可由该仓库制造。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%s</b>, <br/> a manufacturing order is "
"created to fulfill the need."
msgstr "当<b>%s</b>中需要产品时，<br/>创建制造订单，以满足需要。"

#. module: mrp
#: model_terms:digest.tip,tip_description:mrp.digest_tip_mrp_0
msgid ""
"With the Odoo work center control panel, your worker can start work orders "
"in the shop and follow instructions of the worksheet. Quality tests are "
"perfectly integrated into the process. Workers can trigger feedback loops, "
"maintenance alerts, scrap products, etc."
msgstr ""
"通过Odoo工作中心控制面板，工作人员可以在店铺启动工单，并遵循工作表中的指示。质量测试已集成至过程。工作人员可以触发反馈回路、维护警报、报废产品等。"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_consumption_warning
msgid ""
"Wizard in case of consumption in warning/strict and more component has been "
"used for a MO (related to the bom)"
msgstr "消耗处于警告/严格状态、且工单（与物料清单（BOM）相关）使用更多组件时的向导"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split_multi
msgid "Wizard to Split Multiple Productions"
msgstr "多项生产拆分向导"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_split
msgid "Wizard to Split a Production"
msgstr "单项生产拆分向导"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_production_backorder
msgid "Wizard to mark as done or create back order"
msgstr "标记为已完成或创建欠单的向导"

#. module: mrp
#: model:product.template,name:mrp.product_product_wood_panel_product_template
msgid "Wood Panel"
msgstr "木板"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__name
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_capacity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workcenter_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_tree_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_workcenter_search
msgid "Work Center"
msgstr "工作中心"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_capacity
msgid "Work Center Capacity"
msgstr "工作中心产能"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__workcenter_load
msgid "Work Center Load"
msgstr "工作中心负载"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workcenter_load_report_graph
#: model_terms:ir.ui.view,arch_db:mrp.view_workcenter_load_pivot
msgid "Work Center Loads"
msgstr "工作中心负载"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "Work Center Name"
msgstr "工作中心名称"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "工作中心使用情况"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_work_center_load_graph
msgid "Work Center load"
msgstr "工作中心负载"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_action
#: model:ir.ui.menu,name:mrp.menu_view_resource_search_mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Centers"
msgstr "工作中心"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workcenter_kanban_action
msgid "Work Centers Overview"
msgstr "工作中心概述"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
msgid "Work Instruction"
msgstr "工作指示"

#. module: mrp
#: model:ir.actions.report,name:mrp.action_report_workorder
#: model:ir.model,name:mrp.model_mrp_workorder
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter_productivity__workorder_id
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__name
#: model:ir.model.fields,field_description:mrp.field_stock_move_line__workorder_id
#: model:ir.model.fields,field_description:mrp.field_stock_scrap__workorder_id
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_workorder_form_view_filter
msgid "Work Order"
msgstr "工单"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_res_config_settings__group_mrp_workorder_dependencies
msgid "Work Order Dependencies"
msgstr "工单依赖性"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid ""
"Work Order Operations allow you to create and manage the manufacturing "
"operations that should be followed within your work centers in order to "
"produce a product. They are attached to bills of materials that will define "
"the required components."
msgstr "通过工单作业，可以创建并管理制造中心应遵循的制造作业，附于规定所需组件的物料清单（BOM）。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_stock_move__workorder_id
msgid "Work Order To Consume"
msgstr "待消耗工单"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_routing_time
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production_specific
#: model:ir.actions.act_window,name:mrp.action_work_orders
#: model:ir.actions.act_window,name:mrp.mrp_workorder_mrp_production_form
#: model:ir.actions.act_window,name:mrp.mrp_workorder_report
#: model:ir.actions.act_window,name:mrp.mrp_workorder_todo
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workorder_ids
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__workorder_ids
#: model:ir.ui.menu,name:mrp.menu_mrp_work_order_report
#: model:ir.ui.menu,name:mrp.menu_mrp_workorder_todo
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_form_view
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "Work Orders"
msgstr "工单"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.mrp_workorder_workcenter_report
msgid "Work Orders Performance"
msgstr "工单绩效"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_production
#: model:ir.actions.act_window,name:mrp.action_mrp_workorder_workcenter
msgid "Work Orders Planning"
msgstr "工单安排"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "Work Sheet"
msgstr "工作表"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_work_order_search
msgid "Work center"
msgstr "工作中心"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_work_orders
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"                    Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"工单是作为制造订单一部分、要执行的作业。\n"
"                    作业在物料清单中规定，或直接添加至制造订单。"

#. module: mrp
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_production_specific
#: model_terms:ir.actions.act_window,help:mrp.action_mrp_workorder_workcenter
#: model_terms:ir.actions.act_window,help:mrp.mrp_workorder_todo
msgid ""
"Work orders are operations to do as part of a manufacturing order.\n"
"            Operations are defined in the bill of materials or added in the manufacturing order directly."
msgstr ""
"工单是作为制造订单一部分、要执行的作业。\n"
"            作业在物料清单中规定，或直接添加至制造订单。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Work orders in progress. Click to block work center."
msgstr "工单进行中。点击，以阻止工作中心。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_production__workcenter_id
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_filter
#: model_terms:ir.ui.view,arch_db:mrp.oee_search_view
msgid "Workcenter"
msgstr "工作中心"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid "Workcenter %s cannot be an alternative of itself."
msgstr "工作中心%s不能成为其自身的替代品。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_graph_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pie_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_pivot_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_tree_view
msgid "Workcenter Productivity"
msgstr "工作中心生产力"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity
msgid "Workcenter Productivity Log"
msgstr "工作中心生产力日志"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_form_view
#: model_terms:ir.ui.view,arch_db:mrp.oee_loss_tree_view
msgid "Workcenter Productivity Loss"
msgstr "工作中心生产力损失"

#. module: mrp
#: model:ir.model,name:mrp.model_mrp_workcenter_productivity_loss
msgid "Workcenter Productivity Losses"
msgstr "工作中心生产力损失"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__working_state
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_state
msgid "Workcenter Status"
msgstr "工作中心状态"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_kanban
msgid "Workcenter blocked, click to unblock."
msgstr "工作中心已阻止，点击，以解除阻止。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workcenter__resource_calendar_id
msgid "Working Hours"
msgstr "工作小时数"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__working_user_ids
msgid "Working user on this work order."
msgstr "该工单的工作用户。"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_routing_workcenter__worksheet_type
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet
msgid "Worksheet"
msgstr "工作表"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_type
msgid "Worksheet Type"
msgstr "工作表类型"

#. module: mrp
#: model:ir.model.fields,field_description:mrp.field_mrp_workorder__worksheet_google_slide
msgid "Worksheet URL"
msgstr "工作表URL"

#. module: mrp
#. odoo-python
#: code:addons/mrp/wizard/mrp_batch_produce.py:0
msgid ""
"Write one line per finished product to produce, with serial numbers as "
"follows:\n"
msgstr ""
"每个要生产的成品写一行，序号如下： \n"
"\n"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__mrp_production__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_production_filter
msgid "Yesterday"
msgstr "昨天"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_lot.py:0
msgid ""
"You are not allowed to create or edit a lot or serial number for the "
"components with the operation type \"Manufacturing\". To change this, go on "
"the operation type and tick the box \"Create New Lots/Serial Numbers for "
"Components\"."
msgstr "不允许为使用“制造”作业类型创建或编辑组件批号或序列号。要更改该设置，转到作业类型，勾选“创建新的组件批号/序列号”。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not create a kit-type bill of materials for products that have at "
"least one reordering rule."
msgstr "不能为具有至少一个重新订购规则的产品创建套件类型物料清单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You can not delete a Bill of Material with running manufacturing orders.\n"
"Please close or cancel it first."
msgstr ""
"不能删除具有正在运行制造订单的物料清单。\n"
"请先关闭或取消制造订单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You can only merge manufacturing orders of identical products with same BoM."
msgstr "只能合并物料清单（BOM）相同的、同一产品的制造订单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You can only merge manufacturing orders with no additional components or by-"
"products."
msgstr "只能合并没有其他组件或副产品的制造订单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same operation type"
msgstr "只能合并具有相同作业类型的制造"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You can only merge manufacturing with the same state."
msgstr "只能合并状态相同的制造。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You cannot change the workcenter of a work order that is in progress or "
"done."
msgstr "不能更改用正在进行或已完成工单的工作中心。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid "You cannot create a new Bill of Material from here."
msgstr "不能在此处创建新的物料清单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_routing.py:0
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot create cyclic dependency."
msgstr "不能创建周期性依赖性。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot delete an unbuild order if the state is 'Done'."
msgstr "不能删除状态为“已完成”的分解单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot have %s  as the finished product and in the Byproducts"
msgstr "不能将%s作为完工产品和副产品"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot link this work order to another manufacturing order."
msgstr "不能将该工单链接至另一制造订单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You cannot move a manufacturing order once it is cancelled or done."
msgstr "不能移动已取消或已完成的制造订单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot produce the same serial number twice."
msgstr "同一序列号不能生产两次。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You cannot start a work order that is already done or cancelled"
msgstr "不能启动已完成或已取消的工单"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You cannot unbuild a undone manufacturing order."
msgstr "不能分解未完成的制造订单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_bom.py:0
msgid ""
"You cannot use the 'Apply on Variant' functionality and simultaneously "
"create a BoM for a specific variant."
msgstr "不能在使用“应用于变体”功能的同时为特定变体创建物料清单（BOM）。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.view_mrp_consumption_warning_form
msgid ""
"You consumed a different quantity than expected for the following products.\n"
"                        <b invisible=\"consumption == 'strict'\">\n"
"                            Please confirm it has been done on purpose.\n"
"                        </b>\n"
"                        <b invisible=\"consumption != 'strict'\">\n"
"                            Please review your component consumption or ask a manager to validate\n"
"                            <span invisible=\"mrp_production_count != 1\">this manufacturing order</span>\n"
"                            <span invisible=\"mrp_production_count == 1\">these manufacturing orders</span>.\n"
"                        </b>"
msgstr ""
"您消费的以下产品数量与预期不同。\n"
"                       <b invisible=\"consumption == 'strict'\">\n"
"                            请确认这是故意为之。\n"
"                       </b>\n"
"                       <b invisible=\"consumption != 'strict'\">\n"
"                            请检查您的元件消耗量，或请经理确认\n"
"                           <span invisible=\"mrp_production_count != 1\">此制造单</span>\n"
"                           <span invisible=\"mrp_production_count == 1\">这些制造订单</span>。\n"
"                       </b>"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "You need at least two production orders to merge them."
msgstr "合并需要至少两个制造订单。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr "需在“绩效”类别中规定至少一个制造力损失。在制造应用程序中创建，菜单：配置/制造力损失。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid ""
"You need to define at least one productivity loss in the category "
"'Productivity'. Create one from the Manufacturing app, menu: Configuration /"
" Productivity Losses."
msgstr "需在“制造力”类别中规定至少一个制造力损失。在制造应用程序中创建，菜单：配置/制造力损失。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workcenter.py:0
msgid ""
"You need to define at least one unactive productivity loss in the category "
"'Performance'. Create one from the Manufacturing app, menu: Configuration / "
"Productivity Losses."
msgstr "需在“绩效”类别中规定至少一个无效的制造力损失。在制造应用程序创建，菜单：配置/制造力损失。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_workorder.py:0
msgid "You need to provide a lot for the finished product."
msgstr "需提供成品批次。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid ""
"You need to supply Lot/Serial Number for products and 'consume' them: "
"%(missing_products)s"
msgstr "您需要提供产品的批次/序列号并 “消耗” 它们： %(missing_products)s"

#. module: mrp
#: model:ir.actions.act_window,name:mrp.action_mrp_production_backorder
msgid "You produced less than initial demand"
msgstr "生产的数量低于初始需求"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_unbuild.py:0
msgid "You should provide a lot number for the final product."
msgstr "需提供最终产品批号。"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/stock_quant.py:0
msgid ""
"You should update the components quantity instead of directly updating the "
"quantity of the kit product."
msgstr "您应更新组件数量，而不是直接更新工具套装产品的数量。"

#. module: mrp
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__done_mrp_lot_label_to_print__zpl
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__generated_mrp_lot_label_to_print__zpl
#: model:ir.model.fields.selection,name:mrp.selection__stock_picking_type__mrp_product_label_to_print__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "cancelled"
msgstr "已取消"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "days"
msgstr "天"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.res_config_settings_view_form
msgid "days before"
msgstr "天前"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_form_view_inherit
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "expected duration"
msgstr "预计时长"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.stock_warn_insufficient_qty_unbuild_form_view
msgid "from location"
msgstr "来自位置"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid "it is added as a component in a manufacturing order"
msgstr "添加为制造订单中的组件"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_bom_form_view
msgid ""
"it is moved via a transfer, such as a receipt or a delivery order for "
"instance."
msgstr "通过转移移动，如收据或交货单。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "last"
msgstr "最后"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "manufacturing order"
msgstr "制造订单"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "merged"
msgstr "已合并"

#. module: mrp
#. odoo-python
#: code:addons/mrp/report/mrp_report_bom_structure.py:0
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "minutes"
msgstr "分钟"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "of"
msgstr "的"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.exception_on_mo
msgid "ordered instead of"
msgstr "已订购，而非"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_workcenter_view
msgid "per workcenter"
msgstr "各工作中心"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.production_message
msgid "quantity has been updated."
msgstr "数量已更新。"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_production_workorder_tree_editable_view
msgid "real duration"
msgstr "实际时长"

#. module: mrp
#. odoo-python
#: code:addons/mrp/models/mrp_production.py:0
msgid "split"
msgstr "拆分"

#. module: mrp
#: model_terms:ir.ui.view,arch_db:mrp.mrp_routing_workcenter_form_view
msgid "work orders"
msgstr "工单"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_line/mrp_mo_overview_line.xml:0
msgid "{{ foldButtonTitle }}"
msgstr "{{ foldButtonTitle }}"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/bom_overview_line/mrp_bom_overview_line.xml:0
#: code:addons/mrp/static/src/components/bom_overview_special_line/mrp_bom_overview_special_line.xml:0
msgid "{{ props.isFolded ? 'Unfold' : 'Fold' }}"
msgstr "{{ props.isFolded ? 'Unfold' : 'Fold' }}"

#. module: mrp
#. odoo-javascript
#: code:addons/mrp/static/src/components/mo_overview_byproducts_block/mrp_mo_overview_byproducts_block.xml:0
#: code:addons/mrp/static/src/components/mo_overview_operations_block/mrp_mo_overview_operations_block.xml:0
msgid "{{ state.isFolded ? 'Unfold' : 'Fold' }}"
msgstr "{{ state.isFolded ? 'Unfold' : 'Fold' }}"
