<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tax_report" model="account.report">
        <field name="name">Tax Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.tn"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="l10n_tn_tr_column_base_amount" model="account.report.column">
                <field name="name">Amount</field>
                <field name="expression_label">base_amount</field>
            </record>
            <record id="l10n_tn_tr_column_vat_due" model="account.report.column">
                <field name="name">VAT due (I)</field>
                <field name="expression_label">vat_due</field>
            </record>
            <record id="l10n_tn_tr_column_deductible_vat" model="account.report.column">
                <field name="name">Deductible VAT (II)</field>
                <field name="expression_label">deductible_vat</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_tn_tr_VAT" model="account.report.line">
                <field name="name">Value Added Tax</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_tn_tr_VAT_sale" model="account.report.line">
                        <field name="name">1-Turnover taxable for VAT, net of VAT</field>
                        <field name="hierarchy_level">1</field>
                        <field name="children_ids">
                            <record id="l10n_tn_tr_VAT_sale_7" model="account.report.line">
                                <field name="name">VAT 7%</field>
                                <field name="code">l10n_tn_sale_7</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_sale_7_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_7_base_amount_tag</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_sale_7_vat_due_tag" model="account.report.expression">
                                        <field name="label">vat_due</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_7_vat_due_tag</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_sale_13" model="account.report.line">
                                <field name="name">VAT 13%</field>
                                <field name="code">l10n_tn_sale_13</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_sale_13_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_13_base_amount_tag</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_sale_13_vat_due_tag" model="account.report.expression">
                                        <field name="label">vat_due</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_13_vat_due_tag</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_sale_19" model="account.report.line">
                                <field name="name">VAT 19%</field>
                                <field name="code">l10n_tn_sale_19</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_sale_19_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_19_base_amount_tag</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_sale_19_vat_due_tag" model="account.report.expression">
                                        <field name="label">vat_due</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_19_vat_due_tag</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_sale_6" model="account.report.line">
                                <field name="name">VAT 6%</field>
                                <field name="code">l10n_tn_sale_6</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_sale_6_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_6_base_amount_tag</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_sale_6_vat_due_tag" model="account.report.expression">
                                        <field name="label">vat_due</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_6_vat_due_tag</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_sale_12" model="account.report.line">
                                <field name="name">VAT 12%</field>
                                <field name="code">l10n_tn_sale_12</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_sale_12_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_12_base_amount_tag</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_sale_12_vat_due_tag" model="account.report.expression">
                                        <field name="label">vat_due</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_12_vat_due_tag</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_sale_18" model="account.report.line">
                                <field name="name">VAT 18%</field>
                                <field name="code">l10n_tn_sale_18</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_sale_18_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_18_base_amount_tag</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_sale_18_vat_due_tag" model="account.report.expression">
                                        <field name="label">vat_due</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">sale_18_vat_due_tag</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_tn_tr_VAT_purchase" model="account.report.line">
                        <field name="name">2- Purchases taxable for VAT, net of VAT and giving right to deduction</field>
                        <field name="hierarchy_level">1</field>
                        <field name="children_ids">
                            <record id="l10n_tn_tr_VAT_purchase_fixed_assets" model="account.report.line">
                                <field name="name">Purchase of fixed assets</field>
                                <field name="code">l10n_tn_purchase_vat_fixed_assets</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_purchase_fixed_assets_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">purchase_fixed_assets_base_amount_tag</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_purchase_fixed_assets_deductible_vat_tag" model="account.report.expression">
                                        <field name="label">deductible_vat</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">purchase_fixed_assets_deductible_vat_tag</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_purchase_equipment" model="account.report.line">
                                <field name="name">Purchase of equipment</field>
                                <field name="children_ids">
                                    <record id="l10n_tn_tr_VAT_purchase_local_equipment" model="account.report.line">
                                        <field name="name">Local</field>
                                        <field name="code">l10n_tn_purchase_vat_local_equipment</field>
                                        <field name="expression_ids">
                                            <record id="l10n_tn_tr_VAT_purchase_local_equipment_base_amount_tag" model="account.report.expression">
                                                <field name="label">base_amount</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">purchase_local_equipment_base_amount_tag</field>
                                            </record>
                                            <record id="l10n_tn_tr_VAT_purchase_local_equipment_deductible_vat_tag" model="account.report.expression">
                                                <field name="label">deductible_vat</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">purchase_local_equipment_deductible_vat_tag</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_purchase_imported_equipment" model="account.report.line">
                                        <field name="name">Imported</field>
                                        <field name="code">l10n_tn_purchase_vat_imported_equipment</field>
                                        <field name="expression_ids">
                                            <record id="l10n_tn_tr_VAT_purchase_imported_equipment_base_amount_tag" model="account.report.expression">
                                                <field name="label">base_amount</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">purchase_imported_equipment_base_amount_tag</field>
                                            </record>
                                            <record id="l10n_tn_tr_VAT_purchase_imported_equipment_deductible_vat_tag" model="account.report.expression">
                                                <field name="label">deductible_vat</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">purchase_imported_equipment_deductible_vat_tag</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_purchase_other" model="account.report.line">
                                <field name="name">Other purchases</field>
                                <field name="children_ids">
                                    <record id="l10n_tn_tr_VAT_purchase_other_local" model="account.report.line">
                                        <field name="name">Local</field>
                                        <field name="code">l10n_tn_purchase_vat_other_local</field>
                                        <field name="expression_ids">
                                            <record id="l10n_tn_tr_VAT_purchase_other_local_base_amount_tag" model="account.report.expression">
                                                <field name="label">base_amount</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">purchase_other_local_base_amount_tag</field>
                                            </record>
                                            <record id="l10n_tn_tr_VAT_purchase_other_local_deductible_vat_tag" model="account.report.expression">
                                                <field name="label">deductible_vat</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">purchase_other_local_deductible_vat_tag</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_purchase_other_imported" model="account.report.line">
                                        <field name="name">Imported</field>
                                        <field name="code">l10n_tn_purchase_vat_other_imported</field>
                                        <field name="expression_ids">
                                            <record id="l10n_tn_tr_VAT_purchase_other_imported_base_amount_tag" model="account.report.expression">
                                                <field name="label">base_amount</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">purchase_other_imported_base_amount_tag</field>
                                            </record>
                                            <record id="l10n_tn_tr_VAT_purchase_other_imported_deductible_vat_tag" model="account.report.expression">
                                                <field name="label">deductible_vat</field>
                                                <field name="engine">tax_tags</field>
                                                <field name="formula">purchase_other_imported_deductible_vat_tag</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_tn_tr_VAT_other_deductions" model="account.report.line">
                        <field name="name">3- Other deductions</field>
                        <field name="hierarchy_level">1</field>
                        <field name="children_ids">
                            <record id="l10n_tn_tr_VAT_thousand" model="account.report.line">
                                <field name="name">VAT due on amounts that are ≥ 1000 D including VAT and the amount of withholding tax (25%)</field>
                                <field name="code">l10n_tn_purchase_vat_thousand</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_purchase_thousand_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_purchase_thousand_deductible_vat_tag" model="account.report.expression">
                                        <field name="label">deductible_vat</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">0.25 * l10n_tn_purchase_vat_thousand.base_amount</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_foreign_institution" model="account.report.line">
                                <field name="name">VAT due on transactions with persons not having an establishment in Tunisia (100%)</field>
                                <field name="code">l10n_tn_purchase_vat_foreign_institution</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_purchase_foreign_institution_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_purchase_foreign_institution_deductible_vat_tag" model="account.report.expression">
                                        <field name="label">deductible_vat</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">l10n_tn_purchase_vat_foreign_institution.base_amount</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_transport" model="account.report.line">
                                <field name="name">Flat-rate VAT on means of transport</field>
                                <field name="code">l10n_tn_purchase_vat_transport</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_purchase_transport_base_amount_tag" model="account.report.expression">
                                        <field name="label">base_amount</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_purchase_transport_deductible_vat_tag" model="account.report.expression">
                                        <field name="label">deductible_vat</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_tn_tr_VAT_regularization" model="account.report.line">
                        <field name="name">4- Regularisation</field>
                        <field name="hierarchy_level">1</field>
                        <field name="children_ids">
                            <record id="l10n_tn_tr_VAT_additional_deduction" model="account.report.line">
                                <field name="name">Additional deduction</field>
                                <field name="children_ids">
                                    <record id="l10n_tn_tr_VAT_additional_deduction_cancel" model="account.report.line">
                                        <field name="name">for termination and cancellation transactions</field>
                                        <field name="code">l10n_tn_purchase_vat_additional_deduction_cancel</field>
                                        <field name="expression_ids">
                                            <record id="l10n_tn_tr_VAT_purchase_additional_deduction_cancel_deductible_vat_tag" model="account.report.expression">
                                                <field name="label">deductible_vat</field>
                                                <field name="engine">external</field>
                                                <field name="formula">sum</field>
                                                <field name="subformula">editable;rounding=2</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="l10n_tn_tr_VAT_additional_deduction_other" model="account.report.line">
                                        <field name="name">for other operations</field>
                                        <field name="code">l10n_tn_purchase_vat_additional_deduction_other</field>
                                        <field name="expression_ids">
                                            <record id="l10n_tn_tr_VAT_purchase_additional_deduction_other_deductible_vat_tag" model="account.report.expression">
                                                <field name="label">deductible_vat</field>
                                                <field name="engine">external</field>
                                                <field name="formula">sum</field>
                                                <field name="subformula">editable;rounding=2</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_tn_tr_VAT_repayment" model="account.report.line">
                                <field name="name">Reversal</field>
                                <field name="code">l10n_tn_vat_repayment</field>
                                <field name="expression_ids">
                                    <record id="l10n_tn_tr_VAT_repayment_vat_due_tag" model="account.report.expression">
                                        <field name="label">vat_due</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_tn_tr_VAT_total" model="account.report.line">
                        <field name="name">TOTAL</field>
                        <field name="hierarchy_level">1</field>
                        <field name="code">l10n_tn_vat_total</field>
                        <field name="expression_ids">
                            <record id="l10n_tn_tr_VAT_total_vat_due_tag" model="account.report.expression">
                                <field name="label">vat_due</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_tn_sale_7.vat_due +
                                                      l10n_tn_sale_13.vat_due +
                                                      l10n_tn_sale_19.vat_due +
                                                      l10n_tn_sale_6.vat_due +
                                                      l10n_tn_sale_12.vat_due +
                                                      l10n_tn_sale_18.vat_due +
                                                      l10n_tn_vat_repayment.vat_due</field>
                            </record>
                            <record id="l10n_tn_tr_VAT_total_deductible_vat_tag" model="account.report.expression">
                                <field name="label">deductible_vat</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_tn_purchase_vat_fixed_assets.deductible_vat +
                                                      l10n_tn_purchase_vat_local_equipment.deductible_vat +
                                                      l10n_tn_purchase_vat_imported_equipment.deductible_vat +
                                                      l10n_tn_purchase_vat_other_local.deductible_vat +
                                                      l10n_tn_purchase_vat_other_imported.deductible_vat +
                                                      l10n_tn_purchase_vat_thousand.deductible_vat +
                                                      l10n_tn_purchase_vat_foreign_institution.deductible_vat +
                                                      l10n_tn_purchase_vat_transport.deductible_vat +
                                                      l10n_tn_purchase_vat_additional_deduction_cancel.deductible_vat +
                                                      l10n_tn_purchase_vat_additional_deduction_other.deductible_vat</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_tn_tr_VAT_conclusion_difference" model="account.report.line">
                        <field name="name">Outstanding or to be carried forward (I-II)</field>
                        <field name="code">l10n_tn_vat_remaining</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_tn_tr_VAT_conclusion_vat_due_tag" model="account.report.expression">
                                <field name="label">vat_due</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_tn_vat_total.vat_due - l10n_tn_vat_total.deductible_vat</field>
                                <field name="subformula">if_above(TND(0))</field>
                            </record>
                            <record id="l10n_tn_tr_VAT_conclusion_deductible_vat_tag" model="account.report.expression">
                                <field name="label">deductible_vat</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-l10n_tn_vat_total.vat_due + l10n_tn_vat_total.deductible_vat</field>
                                <field name="subformula">if_above(TND(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_tn_tr_VAT_conclusion_carryover" model="account.report.line">
                        <field name="name">Carry-over from previous month</field>
                        <field name="code">l10n_tn_vat_carryover</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_tn_tr_VAT_conclusion_carryover_deductible_vat_tag" model="account.report.expression">
                                <field name="label">deductible_vat</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_tn_tr_VAT_conclusion_returned_amount" model="account.report.line">
                        <field name="name">Amount returned</field>
                        <field name="code">l10n_tn_vat_returned_amount</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_tn_tr_VAT_conclusion_returned_amount_deductible_vat_tag" model="account.report.expression">
                                <field name="label">deductible_vat</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_tn_tr_VAT_conclusion_difference_after_carryover" model="account.report.line">
                        <field name="name">Outstanding or to be carried forward</field>
                        <field name="code">l10n_tn_vat_remaining_total</field>
                        <field name="hierarchy_level">3</field>
                        <field name="expression_ids">
                            <record id="l10n_tn_tr_VAT_conclusion_after_carryover_vat_due_tag" model="account.report.expression">
                                <field name="label">vat_due</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">l10n_tn_vat_remaining.vat_due - l10n_tn_vat_remaining.deductible_vat -
                                            l10n_tn_vat_carryover.deductible_vat + l10n_tn_vat_returned_amount.deductible_vat</field>
                                <field name="subformula">if_above(TND(0))</field>
                            </record>
                            <record id="l10n_tn_tr_VAT_conclusion_after_carryover_deductible_vat_tag" model="account.report.expression">
                                <field name="label">deductible_vat</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-l10n_tn_vat_remaining.vat_due + l10n_tn_vat_remaining.deductible_vat +
                                                    l10n_tn_vat_carryover.deductible_vat - l10n_tn_vat_returned_amount.deductible_vat</field>
                                <field name="subformula">if_above(TND(0))</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
