# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# a75f12d3d37ea5bf159c4b3e85eb30e7_0fa6927, 2024
# <PERSON>, 2024
# W<PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Portuguese (Brazil) (https://app.transifex.com/odoo/teams/41243/pt_BR/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pt_BR\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Dados coletados"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "(ID:"
msgstr "(ID:"

#. module: payment
#: model:payment.method,name:payment.payment_method_7eleven
msgid "7Eleven"
msgstr "7Eleven"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %(bank)s</li><li>Account "
"Number: %(account_number)s</li><li>Account Holder: "
"%(account_holder)s</li></ul>"
msgstr ""
"<h3>Efetue o pagamento para: </h3><ul><li>Banco: %(bank)s</li><li>Número da "
"conta: %(account_number)s</li><li>Titular da conta: "
"%(account_holder)s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> These properties are set to\n"
"                                match the behavior of providers and that of their integration with\n"
"                                Odoo regarding this payment method. Any change may result in errors\n"
"                                and should be tested on a test database first."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Essas propriedades estão definidas para\n"
"                                corresponder ao comportamento dos provedores e da integração deles com o\n"
"                                Odoo em relação a esta forma de pagamento. Alterações podem causar erros\n"
"                                e primeiro devem ser testadas em uma base de dados de teste."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Some of the transactions you intend to capture can"
" only be captured in full. Handle the transactions individually to capture a"
" partial amount.\"/>"
msgstr ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Some of the transactions you intend to capture can"
" only be captured in full. Handle the transactions individually to capture a"
" partial amount.\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid ""
"<i class=\"fa fa-trash\" title=\"Delete payment method\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"
msgstr ""
"<i class=\"fa fa-trash\" title=\"Delete payment method\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "<i class=\"oi oi-arrow-right me-1\"></i> Configure a payment provider"
msgstr ""
"<i class=\"oi oi-arrow-right me-1\"></i> Configurar um provedor de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Enable Payment Methods"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Habilitar métodos de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
msgid "<small class=\"text-600\">Save my payment details</small>"
msgstr "<small class=\"text-600\">Salvar minhas informações de pagamento</small>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-danger\">Unpublished</span>"
msgstr "<span class=\"o_stat_text text-danger\">Não publicado</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-success\">Published</span>"
msgstr "<span class=\"o_stat_text text-success\">Publicado</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">Métodos de pagamento salvos</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                All countries are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                Todos os países são suportados.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                All currencies are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                Todas moedas são suportadas.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid "<span><i class=\"fa fa-lock\"/> Secured by</span>"
msgstr "<span><i class=\"fa fa-lock\"/> Protegido por</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"fa fa-file-text\"/> Show availability report</strong>"
msgstr ""
"<strong><i class=\"fa fa-file-text\"/>Mostrar relatório de "
"disponibilidade</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"oi oi-arrow-right\"/> Payment Methods</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> Formas de pagamento</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"oi oi-arrow-right\"/> Payment Providers</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> Provedores de pagamento</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong>No payment method available</strong>"
msgstr "<strong>Nenhuma forma de pagamento disponível</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a partial capture pending. Please wait a\n"
"                    moment for it to be processed. Check your payment provider configuration if\n"
"                    the capture is still pending after a few minutes."
msgstr ""
"<strong>Aviso:</strong> há uma captura parcial pendente. Espere até\n"
"                    que seja processada. Verifique a configuração do seu provedor de pagamento se\n"
"                    a captura ainda estiver pendente após alguns minutos."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> You can not capture a negative amount nor more\n"
"                    than"
msgstr ""
"<strong>Aviso:</strong> não é possível capturar uma quantia negativa ou mais\n"
"                    que"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<strong>Warning</strong> Creating a payment provider from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>Aviso:</strong> A criação de um provedor de pagamento a partir do botão <em>CRIAR</em> não é suportada.\n"
"                        Utilize a ação <em>Duplicar</em>."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure you are logged in as the\n"
"                                    correct partner before making this payment."
msgstr ""
"<strong>Aviso</strong> Certifique-se de estar conectado com o\n"
"                                    usuário correto antes de fazer este pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr "<strong>Aviso:</strong> a moeda está faltando ou é incorreta."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>Aviso:</strong> você deve estar logado para pagar."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(provider_name)s)."
msgstr ""
"Uma solicitação de reembolso no valor de %(amount)s foi enviada. O pagamento"
" será criado em breve. Referência de transação de reembolso: %(ref)s "
"(%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated (%(provider_name)s)."
msgstr ""
"Uma transação com a referência %(ref)s foi iniciada (%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated to save a new "
"payment method (%(provider_name)s)"
msgstr ""
"Uma transação com a referência %(ref)s foi iniciada para salvar um novo "
"método de pagamento (%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token)s (%(provider_name)s)."
msgstr ""
"Uma transação com a referência %(ref)s foi iniciada utilizando o método de "
"pagamento %(token)s (%(provider_name)s)."

#. module: payment
#: model:payment.method,name:payment.payment_method_ach_direct_debit
msgid "ACH Direct Debit"
msgstr "Débito direto ACH"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "ACTIVATE STRIPE"
msgstr "ATIVAR STRIPE"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Account"
msgstr "Conta"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Número da conta"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Activate"
msgstr "Ativar"

#. module: payment
#: model:ir.actions.server,name:payment.action_activate_stripe
msgid "Activate Stripe"
msgstr "Ativar Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__active
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Ativo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "Endereço"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.method,name:payment.payment_method_affirm
msgid "Affirm"
msgstr "Affirm"

#. module: payment
#: model:payment.method,name:payment.payment_method_afterpay_riverty
msgid "AfterPay"
msgstr "AfterPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_afterpay
msgid "Afterpay"
msgstr "Afterpay"

#. module: payment
#: model:payment.method,name:payment.payment_method_akulaku
msgid "Akulaku PayLater"
msgstr "Akulaku PayLater"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay_hk
msgid "AliPayHK"
msgstr "AliPayHK"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay_plus
msgid "Alipay+"
msgstr "Alipay+"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_express_checkout
msgid "Allow Express Checkout"
msgstr "Permitir checkout expresso"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "Permitir salvar métodos de pagamento"

#. module: payment
#: model:payment.method,name:payment.payment_method_alma
msgid "Alma"
msgstr "Alma"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__captured_amount
msgid "Already Captured"
msgstr "Já capturado"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__voided_amount
msgid "Already Voided"
msgstr "Já anulado"

#. module: payment
#: model:payment.method,name:payment.payment_method_amazon_pay
msgid "Amazon Pay"
msgstr "Amazon Pay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_aps
msgid "Amazon Payment Services"
msgstr "Amazon Payment Services"

#. module: payment
#: model:payment.method,name:payment.payment_method_amex
msgid "American Express"
msgstr "American Express"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Amount"
msgstr "Valor"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "Valor máx"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__amount_to_capture
msgid "Amount To Capture"
msgstr "Valor a capturar"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "An error occurred during the processing of your payment."
msgstr "Ocorreu um erro durante o processamento do seu pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "An error occurred while saving your payment method."
msgstr "Ocorreu um erro ao salvar a forma de pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Apply"
msgstr "Aplicar"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "Arquivado"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
msgid "Are you sure you want to delete this payment method?"
msgstr "Tem certeza de que deseja excluir esta forma de pagamento?"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Tem certeza de que deseja anular a transação autorizada? Esta ação não pode "
"ser desfeita."

#. module: payment
#: model:payment.method,name:payment.payment_method_argencard
msgid "Argencard"
msgstr "Argencard"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_asiapay
msgid "Asiapay"
msgstr "Asiapay"

#. module: payment
#: model:payment.method,name:payment.payment_method_atome
msgid "Atome"
msgstr "Atome"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__auth_msg
msgid "Authorize Message"
msgstr "Mensagem de autorização"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Autorizado"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__authorized_amount
msgid "Authorized Amount"
msgstr "Quantia autorizada"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Availability"
msgstr "Disponibilidade"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
msgid "Availability report"
msgstr "Relatório de disponibilidade"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
msgid "Available methods"
msgstr "Formas disponíveis"

#. module: payment
#: model:payment.method,name:payment.payment_method_axis
msgid "Axis"
msgstr "Eixo"

#. module: payment
#: model:payment.method,name:payment.payment_method_bacs_direct_debit
msgid "BACS Direct Debit"
msgstr "Débito direto BACS"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancomat_pay
msgid "BANCOMAT Pay"
msgstr "BANCOMAT Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_bca
msgid "BCA"
msgstr "BCA"

#. module: payment
#: model:payment.method,name:payment.payment_method_becs_direct_debit
msgid "BECS Direct Debit"
msgstr "Débito direto BECS"

#. module: payment
#: model:payment.method,name:payment.payment_method_blik
msgid "BLIK"
msgstr "BLIK"

#. module: payment
#: model:payment.method,name:payment.payment_method_brankas
msgid "BRANKAS"
msgstr "BRANKAS"

#. module: payment
#: model:payment.method,name:payment.payment_method_bri
msgid "BRI"
msgstr "BRI"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancnet
msgid "BancNet"
msgstr "BancNet"

#. module: payment
#: model:payment.method,name:payment.payment_method_banco_de_bogota
msgid "Banco de Bogota"
msgstr "Banco de Bogotá"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancolombia
msgid "Bancolombia"
msgstr "Bancolombia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancontact
msgid "Bancontact"
msgstr "Bancontact"

#. module: payment
#: model:payment.method,name:payment.payment_method_bangkok_bank
msgid "Bangkok Bank"
msgstr "Bangkok Bank"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Bank"
msgstr "Banco"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_account
msgid "Bank Account"
msgstr "Conta bancária"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Nome do banco"

#. module: payment
#: model:payment.method,name:payment.payment_method_bni
msgid "Bank Negara Indonesia"
msgstr "Bank Negara Indonesia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_permata
msgid "Bank Permata"
msgstr "Bank Permata"

#. module: payment
#: model:payment.method,name:payment.payment_method_bsi
msgid "Bank Syariah Indonesia"
msgstr "Bank Syariah Indonesia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_transfer
msgid "Bank Transfer"
msgstr "Transferência bancária"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_of_ayudhya
msgid "Bank of Ayudhya"
msgstr "Bank of Ayudhya"

#. module: payment
#: model:payment.method,name:payment.payment_method_bpi
msgid "Bank of the Philippine Islands"
msgstr "Bank of the Philippine Islands"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_reference
msgid "Bank reference"
msgstr "Referência bancária"

#. module: payment
#: model:payment.method,name:payment.payment_method_belfius
msgid "Belfius"
msgstr "Belfius"

#. module: payment
#: model:payment.method,name:payment.payment_method_benefit
msgid "Benefit"
msgstr "Benefício"

#. module: payment
#: model:payment.method,name:payment.payment_method_bharatqr
msgid "BharatQR"
msgstr "BharatQR"

#. module: payment
#: model:payment.method,name:payment.payment_method_billease
msgid "BillEase"
msgstr "BillEase"

#. module: payment
#: model:payment.method,name:payment.payment_method_billink
msgid "Billink"
msgstr "Billink"

#. module: payment
#: model:payment.method,name:payment.payment_method_bizum
msgid "Bizum"
msgstr "Bizum"

#. module: payment
#: model:payment.method,name:payment.payment_method_boleto
msgid "Boleto"
msgstr "Boleto"

#. module: payment
#: model:payment.method,name:payment.payment_method_boost
msgid "Boost"
msgstr "Boost"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__brand_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Brands"
msgstr "Marcas"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:payment.method,name:payment.payment_method_cimb_niaga
msgid "CIMB Niaga"
msgstr "CIMB Niaga"

#. module: payment
#: model:payment.method,name:payment.payment_method_cmr
msgid "CMR"
msgstr "CMR"

#. module: payment
#: model:payment.method,name:payment.payment_method_cabal
msgid "Cabal"
msgstr "Cabal"

#. module: payment
#: model:payment.method,name:payment.payment_method_caixa
msgid "Caixa"
msgstr "Caixa"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Cancel"
msgstr "Cancelar"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Cancelada"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__cancel_msg
msgid "Cancelled Message"
msgstr "Mensagem cancelada"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Cannot delete payment method"
msgstr "Não foi possível excluir a forma de pagamento"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Cannot save payment method"
msgstr "Não foi possível salvar a forma de pagamento"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid "Capture"
msgstr "Captura"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__capture_manually
msgid "Capture Amount Manually"
msgstr "Capturar quantia manualmente"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "Capturar transação"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"Capture o valor a partir do Odoo quando a entrega for concluída.\n"
"Use isso se você quiser cobrar os cartões dos clientes somente quando\n"
"tiver certeza de que pode enviar as mercadorias para eles."

#. module: payment
#: model:payment.method,name:payment.payment_method_card
msgid "Card"
msgstr "Quadro"

#. module: payment
#: model:payment.method,name:payment.payment_method_carnet
msgid "Carnet"
msgstr "Carnet"

#. module: payment
#: model:payment.method,name:payment.payment_method_cartes_bancaires
msgid "Cartes Bancaires"
msgstr "Cartes Bancaires"

#. module: payment
#: model:payment.method,name:payment.payment_method_cash_app_pay
msgid "Cash App Pay"
msgstr "Cash App Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_cashalo
msgid "Cashalo"
msgstr "Cashalo"

#. module: payment
#: model:payment.method,name:payment.payment_method_cebuana
msgid "Cebuana"
msgstr "Cebuana"

#. module: payment
#: model:payment.method,name:payment.payment_method_cencosud
msgid "Cencosud"
msgstr "Cencosud"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__child_transaction_ids
msgid "Child Transactions"
msgstr "Transações secundárias"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Child transactions"
msgstr "Transações secundárias"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "Escolha uma forma de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Choose another method <i class=\"oi oi-arrow-down\"/>"
msgstr "Selecione outra forma <i class=\"oi oi-arrow-down\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_cirrus
msgid "Cirrus"
msgstr "Cirrus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "Cidade"

#. module: payment
#: model:payment.method,name:payment.payment_method_clearpay
msgid "Clearpay"
msgstr "Clearpay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Close"
msgstr "Fechar"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__code
#: model:ir.model.fields,field_description:payment.field_payment_provider__code
msgid "Code"
msgstr "Código"

#. module: payment
#: model:payment.method,name:payment.payment_method_codensa
msgid "Codensa"
msgstr "Codensa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__color
msgid "Color"
msgstr "Cor"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "Empresa"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Configuration"
msgstr "Configuração"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Confirm Deletion"
msgstr "Confirmar exclusão"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "Confirmado"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Contato"

#. module: payment
#: model:payment.method,name:payment.payment_method_cordial
msgid "Cordial"
msgstr "Cordial"

#. module: payment
#: model:payment.method,name:payment.payment_method_cordobesa
msgid "Cordobesa"
msgstr "Cordobesa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_id
msgid "Corresponding Module"
msgstr "Módulo correspondente"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_country_ids
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_country_ids
msgid "Countries"
msgstr "Países"

#. module: payment
#: model:ir.model,name:payment.model_res_country
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "País"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "Criar token"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_provider
msgid "Create a new payment provider"
msgstr "Criar um novo provedor de pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Criado por"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_method__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Criado em"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Creating a transaction from an archived token is forbidden."
msgstr "É proibido criar uma transação a partir de um token arquivado."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Credentials"
msgstr "Credenciais"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__stripe
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "Cartão de crédito e débito (via Stripe)"

#. module: payment
#: model:payment.method,name:payment.payment_method_credit
msgid "Credit Payment"
msgstr "Pagamento no crédito"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_currency_ids
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_currency_ids
msgid "Currencies"
msgstr "Moedas"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__main_currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Moeda"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__manual
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Instruções de pagamento personalizadas"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Cliente"

#. module: payment
#: model:payment.method,name:payment.payment_method_dana
msgid "Dana"
msgstr "Dana"

#. module: payment
#: model:payment.method,name:payment.payment_method_dankort
msgid "Dankort"
msgstr "Dankort"

#. module: payment
#: model:payment.method,name:payment.payment_method_davivienda
msgid "Davivienda"
msgstr "Davivienda"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__sequence
msgid "Define the display order"
msgstr "Defina a ordem de exibição"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_demo
msgid "Demo"
msgstr "Demo"

#. module: payment
#: model:payment.method,name:payment.payment_method_diners
msgid "Diners Club International"
msgstr "Diners Club International"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Disabled"
msgstr "Desabilitado"

#. module: payment
#: model:payment.method,name:payment.payment_method_discover
msgid "Discover"
msgstr "Descobrir"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_method__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Nome exibido"

#. module: payment
#: model:payment.method,name:payment.payment_method_dolfin
msgid "Dolfin"
msgstr "Dolfin"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Don't hesitate to contact us if you don't receive it."
msgstr "Não hesite em entrar em contato conosco se não receber."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__done_msg
msgid "Done Message"
msgstr "Mensagem de conclusão"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Rascunho"

#. module: payment
#: model:payment.method,name:payment.payment_method_duitnow
msgid "DuitNow"
msgstr "DuitNow"

#. module: payment
#: model:payment.method,name:payment.payment_method_emi_india
msgid "EMI"
msgstr "EMI"

#. module: payment
#: model:payment.method,name:payment.payment_method_eps
msgid "EPS"
msgstr "EPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_elo
msgid "Elo"
msgstr "Elo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Email"
msgstr "E-mail"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__enabled
msgid "Enabled"
msgstr "Habilitado"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Enterprise"
msgstr "Enterprise"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Erro"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Error: %s"
msgstr "Erro: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_express_checkout
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_express_checkout
msgid "Express Checkout"
msgstr "Checkout expresso"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__express_checkout_form_view_id
msgid "Express Checkout Form Template"
msgstr "Modelo de formulário de checkout expresso"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_express_checkout
msgid ""
"Express checkout allows customers to pay faster by using a payment method "
"that provides all required billing and shipping information, thus allowing "
"to skip the checkout process."
msgstr ""
"O check-out expresso permite que os clientes paguem mais rápido usando uma "
"forma de pagamento que fornece todas as informações de entrega e de cobrança"
" necessárias, possibilitando assim pular o processo de checkout."

#. module: payment
#: model:payment.method,name:payment.payment_method_fps
msgid "FPS"
msgstr "FPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_fpx
msgid "FPX"
msgstr "FPX"

#. module: payment
#: model:payment.method,name:payment.payment_method_floa_bank
msgid "Floa Bank"
msgstr "Floa Bank"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_flutterwave
msgid "Flutterwave"
msgstr "Flutterwave"

#. module: payment
#: model:payment.method,name:payment.payment_method_frafinance
msgid "Frafinance"
msgstr "Frafinance"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__partial
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__partial
msgid "Full & Partial"
msgstr "Total e parcial"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__full_only
msgid "Full Only"
msgstr "Total apenas"

#. module: payment
#: model:payment.method,name:payment.payment_method_gcash
msgid "GCash"
msgstr "GCash"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "Gerar link de pagamento "

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Gerar link de pagamento da venda"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate and Copy Payment Link"
msgstr "Gerar e copiar link de pagamento"

#. module: payment
#: model:payment.method,name:payment.payment_method_giropay
msgid "Giropay"
msgstr "Giropay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Go to my Account <i class=\"oi oi-arrow-right ms-2\"/>"
msgstr "Ir para minha conta <i class=\"oi oi-arrow-right ms-2\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_gopay
msgid "GoPay"
msgstr "GoPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_gsb
msgid "Government Savings Bank"
msgstr "Government Savings Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_grabpay
msgid "GrabPay"
msgstr "GrabPay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "Agrupar por"

#. module: payment
#: model:payment.method,name:payment.payment_method_hd
msgid "HD Bank"
msgstr "HD Bank"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "Roteamento HTTP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_draft_children
msgid "Has Draft Children"
msgstr "Tem secundários em rascunho"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_remaining_amount
msgid "Has Remaining Amount"
msgstr "Tem quantia remanescente"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "O pagamento foi pós-processado"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pre_msg
msgid "Help Message"
msgstr "Mensagem de ajuda"

#. module: payment
#: model:payment.method,name:payment.payment_method_hipercard
msgid "Hipercard"
msgstr "Hipercard"

#. module: payment
#: model:payment.method,name:payment.payment_method_hoolah
msgid "Hoolah"
msgstr "Hoolah"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "How to configure your PayPal account"
msgstr "Como configurar sua conta do PayPal"

#. module: payment
#: model:payment.method,name:payment.payment_method_humm
msgid "Humm"
msgstr "Humm"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_method__id
#: model:ir.model.fields,field_description:payment.field_payment_provider__id
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr ""
"Se achar que se trata de um erro, entre em contato com o administrador do "
"site."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image
#: model:ir.model.fields,field_description:payment.field_payment_provider__image_128
msgid "Image"
msgstr "Imagem"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"No modo de teste, um pagamento falso é processado por meio de uma interface de pagamento de teste.\n"
"Este modo é recomendado ao configurar o provedor."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__inline_form_view_id
msgid "Inline Form Template"
msgstr "Modelo de formulário em linha"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Install"
msgstr "Instalar"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_state
msgid "Installation State"
msgstr "Estado da instalação"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "Installed"
msgstr "Instalado"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__is_amount_to_capture_valid
msgid "Is Amount To Capture Valid"
msgstr "A quantia a capturar é válida"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "Foi pós-processado"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__is_primary
msgid "Is Primary Payment Method"
msgstr "É a forma de pagamento primária"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr "É um país aceito pelo Stripe"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
msgid "It is currently linked to the following documents:"
msgstr "Está atualmente vinculado aos seguintes documentos:"

#. module: payment
#: model:payment.method,name:payment.payment_method_jcb
msgid "JCB"
msgstr "JCB"

#. module: payment
#: model:payment.method,name:payment.payment_method_jeniuspay
msgid "JeniusPay"
msgstr "JeniusPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_jkopay
msgid "Jkopay"
msgstr "Jkopay"

#. module: payment
#: model:payment.method,name:payment.payment_method_kbc_cbc
msgid "KBC/CBC"
msgstr "KBC/CBC"

#. module: payment
#: model:payment.method,name:payment.payment_method_knet
msgid "KNET"
msgstr "KNET"

#. module: payment
#: model:payment.method,name:payment.payment_method_kakaopay
msgid "KakaoPay"
msgstr "KakaoPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_kasikorn_bank
msgid "Kasikorn Bank"
msgstr "Kasikorn Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna
msgid "Klarna"
msgstr "Klarna"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna_paynow
msgid "Klarna - Pay Now"
msgstr "Klarna - Pagar agora"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna_pay_over_time
msgid "Klarna - Pay over time"
msgstr "Klarna - Pagar depois"

#. module: payment
#: model:payment.method,name:payment.payment_method_kredivo
msgid "Kredivo"
msgstr "Kredivo"

#. module: payment
#: model:payment.method,name:payment.payment_method_krungthai_bank
msgid "KrungThai Bank"
msgstr "KrungThai Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_linepay
msgid "LINE Pay"
msgstr "LINE Pay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "Rota de destino"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Idioma"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "Data da última mudança de situação"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Última atualização por"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_method__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Última atualização em"

#. module: payment
#: model:onboarding.onboarding.step,button_text:payment.onboarding_onboarding_step_payment_provider
msgid "Let's do it"
msgstr "Vamos lá"

#. module: payment
#: model:payment.method,name:payment.payment_method_lider
msgid "Lider"
msgstr "Lider"

#. module: payment
#: model:payment.method,name:payment.payment_method_linkaja
msgid "LinkAja"
msgstr "LinkAja"

#. module: payment
#: model:payment.method,name:payment.payment_method_lydia
msgid "Lydia"
msgstr "Lydia"

#. module: payment
#: model:payment.method,name:payment.payment_method_lyfpay
msgid "LyfPay"
msgstr "LyfPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_mpesa
msgid "M-Pesa"
msgstr "M-Pesa"

#. module: payment
#: model:payment.method,name:payment.payment_method_mbway
msgid "MB WAY"
msgstr "MB WAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_mada
msgid "Mada"
msgstr "Mada"

#. module: payment
#: model:payment.method,name:payment.payment_method_maestro
msgid "Maestro"
msgstr "Maestro"

#. module: payment
#: model:payment.method,name:payment.payment_method_magna
msgid "Magna"
msgstr "Magna"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"Making a request to the provider is not possible because the provider is "
"disabled."
msgstr ""
"Não é possível fazer uma solicitação ao provedor porque o provedor está "
"desabilitado."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Manage your payment methods"
msgstr "Gerencie seus métodos de pagamento"

#. module: payment
#: model:payment.method,name:payment.payment_method_mandiri
msgid "Mandiri"
msgstr "Mandiri"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Manual"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_manual_capture
msgid "Manual Capture Supported"
msgstr "Suporta captura manual"

#. module: payment
#: model:payment.method,name:payment.payment_method_mastercard
msgid "MasterCard"
msgstr "MasterCard"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__maximum_amount
msgid "Maximum Amount"
msgstr "Valor máximo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__available_amount
msgid "Maximum Capture Allowed"
msgstr "Captura máxima permitida"

#. module: payment
#: model:payment.method,name:payment.payment_method_maya
msgid "Maya"
msgstr "Maya"

#. module: payment
#: model:payment.method,name:payment.payment_method_maybank
msgid "Maybank"
msgstr "Maybank"

#. module: payment
#: model:payment.method,name:payment.payment_method_meeza
msgid "Meeza"
msgstr "Meeza"

#. module: payment
#: model:payment.method,name:payment.payment_method_mercado_livre
msgid "Mercado Livre"
msgstr "Mercado Livre"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mercado_pago
msgid "Mercado Pago"
msgstr "Mercado Pago"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "Mensagem"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Messages"
msgstr "Mensagens"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "Método"

#. module: payment
#: model:payment.method,name:payment.payment_method_momo
msgid "MoMo"
msgstr "MoMo"

#. module: payment
#: model:payment.method,name:payment.payment_method_mobile_money
msgid "Mobile money"
msgstr "Mobile money"

#. module: payment
#: model:payment.method,name:payment.payment_method_mobile_pay
msgid "MobilePay"
msgstr "MobilePay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#: model:payment.method,name:payment.payment_method_multibanco
msgid "Multibanco"
msgstr "Multibanco"

#. module: payment
#: model:payment.method,name:payment.payment_method_mybank
msgid "MyBank"
msgstr "MyBank"

#. module: payment
#: model:payment.method,name:payment.payment_method_naps
msgid "NAPS"
msgstr "NAPS"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__name
#: model:ir.model.fields,field_description:payment.field_payment_provider__name
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Name"
msgstr "Nome"

#. module: payment
#: model:payment.method,name:payment.payment_method_napas_card
msgid "Napas Card"
msgstr "Napas Card"

#. module: payment
#: model:payment.method,name:payment.payment_method_naranja
msgid "Naranja"
msgstr "Naranja"

#. module: payment
#: model:payment.method,name:payment.payment_method_nativa
msgid "Nativa"
msgstr "Nativa"

#. module: payment
#: model:payment.method,name:payment.payment_method_naver_pay
msgid "Naver Pay"
msgstr "Naver Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_netbanking
msgid "Netbanking"
msgstr "Netbanking"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__code__none
msgid "No Provider Set"
msgstr "Nenhum provedor definido"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Provider menu."
msgstr ""
"Nenhuma forma de pagamento manual foi encontrada para esta empresa. Crie uma"
" no menu de provedores de serviços de pagamentos."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "No payment methods found for your payment providers."
msgstr ""
"Nenhuma forma de pagamento encontrada para seus provedores de pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "No payment providers are configured."
msgstr "Não há provedores de pagamento configurados."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid "No token can be assigned to the public partner."
msgstr "Nenhum token pode ser atribuído ao usuário público."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "None is configured for:"
msgstr "Nenhum está configurado para:"

#. module: payment
#: model:payment.method,name:payment.payment_method_ovo
msgid "OVO"
msgstr "OVO"

#. module: payment
#: model:payment.method,name:payment.payment_method_oca
msgid "Oca"
msgstr "Oca"

#. module: payment
#: model:payment.method,name:payment.payment_method_octopus
msgid "Octopus"
msgstr "Octopus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Módulo do Odoo Enterprise"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "Pagamento offline por token"

#. module: payment
#: model:payment.method,name:payment.payment_method_omannet
msgid "OmanNet"
msgstr "OmanNet"

#. module: payment
#: model:ir.model,name:payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Etapa de onboarding "

#. module: payment
#: model:onboarding.onboarding.step,step_image_alt:payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Step Image"
msgstr "Imagem da etapa de integração"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_czech_republic
msgid "Online Banking Czech Republic"
msgstr "Online Banking Czech Republic"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_india
msgid "Online Banking India"
msgstr "Online Banking India"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_slovakia
msgid "Online Banking Slovakia"
msgstr "Online Banking Slovakia"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_thailand
msgid "Online Banking Thailand"
msgstr "Online Banking Thailand"

#. module: payment
#: model:onboarding.onboarding.step,title:payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "Pagamentos online"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "Pagamento direto online"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "Pagamento online por token"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "Pagamento online com redirecionamento"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Only administrators can access this data."
msgstr "Somente administradores podem acessar esses dados."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Only authorized transactions can be voided."
msgstr "Somente transações autorizadas podem ser anuladas."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Only confirmed transactions can be refunded."
msgstr "Somente transações confirmadas podem ser reembolsadas."

#. module: payment
#: model:payment.method,name:payment.payment_method_open_banking
msgid "Open banking"
msgstr "Banco aberto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "Operação"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "Operation not supported."
msgstr "Operação não suportada."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Outro"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Other payment methods"
msgstr "Outras formas de pagamento"

#. module: payment
#: model:payment.method,name:payment.payment_method_p24
msgid "P24"
msgstr "P24"

#. module: payment
#: model:payment.method,name:payment.payment_method_poli
msgid "POLi"
msgstr "POLi"

#. module: payment
#: model:payment.method,name:payment.payment_method_pps
msgid "PPS"
msgstr "PPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_pse
msgid "PSE"
msgstr "PSE"

#. module: payment
#: model:payment.method,name:payment.payment_method_pace
msgid "Pace."
msgstr "Pace."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__partial
msgid "Partial"
msgstr "Parcial"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "Usuário"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Nome do usuário"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Pay"
msgstr "Pagar"

#. module: payment
#: model:payment.method,name:payment.payment_method_paylater_india
msgid "Pay Later"
msgstr "Pagar depois"

#. module: payment
#: model:payment.method,name:payment.payment_method_pay_easy
msgid "Pay-easy"
msgstr "Pay-easy"

#. module: payment
#: model:payment.method,name:payment.payment_method_paybright
msgid "PayBright"
msgstr "PayBright"

#. module: payment
#: model:payment.method,name:payment.payment_method_pay_id
msgid "PayID"
msgstr "PayID"

#. module: payment
#: model:payment.method,name:payment.payment_method_payme
msgid "PayMe"
msgstr "PayMe"

#. module: payment
#: model:payment.method,name:payment.payment_method_paynow
msgid "PayNow"
msgstr "PayNow"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
#: model:payment.provider,name:payment.payment_provider_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.method,name:payment.payment_method_paypay
msgid "PayPay"
msgstr "PayPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_paysafecard
msgid "PaySafeCard"
msgstr "PaySafeCard"

#. module: payment
#: model:payment.method,name:payment.payment_method_payu
msgid "PayU"
msgstr "PayU"

#. module: payment
#: model:payment.method,name:payment.payment_method_paylib
msgid "Paylib"
msgstr "Paylib"

#. module: payment
#: model:ir.model,name:payment.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr "Assistente de captura de pagamentos"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_details
msgid "Payment Details"
msgstr "Informações de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Followup"
msgstr "Acompanhamento do pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Form"
msgstr "Formulário de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Payment Info"
msgstr "Informação de Pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Instruções de pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "Link de pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_method
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__payment_method
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Payment Method"
msgstr "Método de pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_code
msgid "Payment Method Code"
msgstr "Código da forma de pagamento"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model:ir.actions.act_window,name:payment.action_payment_method
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Methods"
msgstr "Formas de pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_provider
msgid "Payment Provider"
msgstr "Provedor de serviços de pagamento"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_provider
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_list
msgid "Payment Providers"
msgstr "Provedores de serviços de pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "Token de pagamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "Contagem de tokens de pagamentos"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "Tokens de pagamentos"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transação do pagamento"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "Transações de pagamento"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "Transações de pagamento vinculadas ao token"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid "Payment details saved on %(date)s"
msgstr "Informações de pagamento salvas em %(date)s"

#. module: payment
#: model:payment.method,name:payment.payment_method_unknown
msgid "Payment method"
msgstr "Forma de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Payment methods"
msgstr "Formas de pagamento"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "Falha no processamento do pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment provider"
msgstr "Provedor de serviços de pagamento"

#. module: payment
#: model:ir.model,name:payment.model_payment_provider_onboarding_wizard
msgid "Payment provider onboarding wizard"
msgstr "Assistente de integração de provedor de serviços de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
msgid "Payment providers"
msgstr "Prestadores de serviços de pagamento"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
msgid "Payment: Post-process transactions"
msgstr "Pagamento: Transações de pós-processo"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "Pagamentos"

#. module: payment
#: model:payment.method,name:payment.payment_method_paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment
#: model:payment.method,name:payment.payment_method_paytm
msgid "Paytm"
msgstr "Paytm"

#. module: payment
#: model:payment.method,name:payment.payment_method_paytrail
msgid "Paytrail"
msgstr "Paytrail"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "Pendente"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pending_msg
msgid "Pending Message"
msgstr "Mensagem de pendência"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Telefone"

#. module: payment
#: model:payment.method,name:payment.payment_method_pix
msgid "Pix"
msgstr "Pix"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "Please make sure that %(payment_method)s is supported by %(provider)s."
msgstr "Certifique-se de que %(payment_method)s é suportado por %(provider)s."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "Please set a positive amount."
msgstr "Defina um valor positivo."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "Please set an amount lower than %s."
msgstr "Defina um valor menor que %s."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid "Please switch to company"
msgstr "Alterne para empresa"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Please wait..."
msgstr "Aguarde…"

#. module: payment
#: model:payment.method,name:payment.payment_method_post_finance
msgid "PostFinance Pay"
msgstr "PostFinance Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_poste_pay
msgid "PostePay"
msgstr "PostePay"

#. module: payment
#: model:payment.method,name:payment.payment_method_presto
msgid "Presto"
msgstr "Presto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__primary_payment_method_id
msgid "Primary Payment Method"
msgstr "Forma de pagamento primária"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Processado por"

#. module: payment
#: model:payment.method,name:payment.payment_method_promptpay
msgid "Prompt Pay"
msgstr "Prompt Pay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Provider"
msgstr "Provedor"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_code
msgid "Provider Code"
msgstr "Código do provedor"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_reference
msgid "Provider Reference"
msgstr "Referência do provedor"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__provider_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Providers"
msgstr "Provedores"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__is_published
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Published"
msgstr "Publicado"

#. module: payment
#: model:payment.method,name:payment.payment_method_qris
msgid "QRIS"
msgstr "QRIS"

#. module: payment
#: model:payment.method,name:payment.payment_method_rabbit_line_pay
msgid "Rabbit LINE Pay"
msgstr "Rabbit LINE Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_ratepay
msgid "Ratepay"
msgstr "Ratepay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_razorpay
msgid "Razorpay"
msgstr "Razorpay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "Reason:"
msgstr "Motivo:"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Reason: %s"
msgstr "Motivo: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "Modelo de formulário de redirecionamento"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Reference"
msgstr "Referência"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "A referência deve ser única."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_method__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_refund
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
msgid "Refund"
msgstr "Reembolso"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_refund
#: model:ir.model.fields,help:payment.field_payment_provider__support_refund
msgid ""
"Refund is a feature allowing to refund customers directly from the payment "
"in Odoo."
msgstr ""
"O reembolso é um recurso que permite reembolsar os clientes diretamente pelo"
" pagamento no Odoo"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Refunds"
msgstr "Reembolsos"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "Contagem de reembolsos"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "ID do documento relacionado"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Modelo do documento relacionado"

#. module: payment
#: model:payment.method,name:payment.payment_method_revolut_pay
msgid "Revolut Pay"
msgstr "Revolut Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_rupay
msgid "RuPay"
msgstr "RuPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_sepa_direct_debit
#: model:payment.provider,name:payment.payment_provider_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "Débito direto SEPA"

#. module: payment
#: model:payment.method,name:payment.payment_method_samsung_pay
msgid "Samsung Pay"
msgstr "Samsung Pay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Save"
msgstr "Salvar"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Saving your payment method."
msgstr "Salvar sua forma de pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select countries. Leave empty to allow any."
msgstr "Selecione os países. Deixe em branco para permitir qualquer um."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select countries. Leave empty to make available everywhere."
msgstr ""
"Selecione os países. Deixe vazio para disponibilizar em todas as regiões."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select currencies. Leave empty not to restrict any."
msgstr "Selecione moedas. Deixe vazio para não ter restrições."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select currencies. Leave empty to allow any."
msgstr "Selecione moedas. Deixe em branco para permitir qualquer uma."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "Método de pagamento de integração selecionado"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__sequence
#: model:ir.model.fields,field_description:payment.field_payment_provider__sequence
msgid "Sequence"
msgstr "Sequência"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopback
msgid "ShopBack"
msgstr "ShopBack"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopeepay
msgid "ShopeePay"
msgstr "ShopeePay"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopping
msgid "Shopping Card"
msgstr "Shopping Card"

#. module: payment
#: model:payment.method,name:payment.payment_method_scb
msgid "Siam Commerical Bank"
msgstr "Siam Commerical Bank"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Skip <i class=\"oi oi-arrow-right ms-1 small\"/>"
msgstr "Skip <i class=\"oi oi-arrow-right ms-1 small\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_sofort
msgid "Sofort"
msgstr "Sofort"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
msgid ""
"Some of the transactions you intend to capture can only be captured in full."
" Handle the transactions individually to capture a partial amount."
msgstr ""
"Algumas das transações que você pretende capturar só podem ser capturadas na"
" íntegra. Processe as transações individualmente para capturar um valor "
"parcial."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "Transação de origem"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "Estado"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "Status"

#. module: payment
#: model:onboarding.onboarding.step,done_text:payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "Passo Completado!"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.provider,name:payment.payment_provider_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__support_partial_capture
msgid "Support Partial Capture"
msgstr "Suporte à captura parcial"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__payment_method_ids
msgid "Supported Payment Methods"
msgstr "Formas de pagamento aceitas"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Supported by"
msgstr "Suportado por"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "Supported providers:"
msgstr "Provedores compatíveis:"

#. module: payment
#: model:payment.method,name:payment.payment_method_swish
msgid "Swish"
msgstr "Swish"

#. module: payment
#: model:payment.method,name:payment.payment_method_tenpay
msgid "TENPAY"
msgstr "TENPAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_ttb
msgid "TTB"
msgstr "TTB"

#. module: payment
#: model:payment.method,name:payment.payment_method_tmb
msgid "Tamilnad Mercantile Bank Limited"
msgstr "Tamilnad Mercantile Bank Limited"

#. module: payment
#: model:payment.method,name:payment.payment_method_tarjeta_mercadopago
msgid "Tarjeta MercadoPago"
msgstr "Tarjeta MercadoPago"

#. module: payment
#: model:payment.method,name:payment.payment_method_techcom
msgid "Techcombank"
msgstr "Techcombank"

#. module: payment
#: model:payment.method,name:payment.payment_method_tendopay
msgid "TendoPay"
msgstr "TendoPay"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__test
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Test Mode"
msgstr "Modo de teste"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Thank you!"
msgstr "Agradecemos!"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "The access token is invalid."
msgstr "O token de acesso é inválido."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
msgid "The amount to capture must be positive and cannot be superior to %s."
msgstr ""
"O valor a ser capturado deve ser positivo e não pode ser superior a %s."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__image
#: model:ir.model.fields,help:payment.field_payment_method__image_payment_form
msgid "The base image used for this payment method; in a 64x64 px format."
msgstr ""
"A imagem de base usada para essa forma de pagamento (no formato 64x64 px)"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__brand_ids
msgid ""
"The brands of the payment methods that will be displayed on the payment "
"form."
msgstr ""
"As marcas da forma de pagamento que serão exibidas no formulário de "
"pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__child_transaction_ids
msgid "The child transactions of the transaction."
msgstr "As transações secundárias da transação. "

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__payment_details
msgid "The clear part of the payment method's payment details."
msgstr "A parte vazia das informações de pagamento da forma de pagamento."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__color
msgid "The color of the card in kanban view"
msgstr "A cor do cartão na visualização kanban"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "A mensagem de informações complementares sobre a situação"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_country_ids
msgid ""
"The countries in which this payment provider is available. Leave blank to "
"make it available in all countries."
msgstr ""
"Os países nos quais esta forma de pagamento está disponível. Deixe em branco"
" para disponibilizar a todos os países."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_currency_ids
msgid ""
"The currencies available with this payment provider. Leave empty not to "
"restrict any."
msgstr ""
"As moedas disponíveis com este provedor de pagamentos. Deixe em branco para "
"não ter restrições."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid "The following fields must be filled: %s"
msgstr "Os campos seguintes devem ser preenchidos: %s"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "The following kwargs are not whitelisted: %s"
msgstr "Os seguintes kwargs não estão na lista de permissões: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "A referência interna da transação"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_country_ids
msgid ""
"The list of countries in which this payment method can be used (if the "
"provider allows it). In other countries, this payment method is not "
"available to customers."
msgstr ""
"A lista de países em que esta forma de pagamento pode ser usada (se o "
"provedor permitir). Em outros países, esta forma de pagamento não estará "
"disponível para os clientes."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_currency_ids
msgid ""
"The list of currencies for that are supported by this payment method (if the"
" provider allows it). When paying with another currency, this payment method"
" is not available to customers."
msgstr ""
"A lista de moedas que são suportadas por essa forma de pagamento (se o "
"provedor permitir). Ao pagar com outra moeda, esta forma de pagamento não "
"estará disponível para os clientes"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__provider_ids
msgid "The list of providers supporting this payment method."
msgstr "A lista de provedores que dão suporte a essa forma de pagamento."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__main_currency_id
msgid "The main currency of the company, used to display monetary fields."
msgstr "A moeda principal da empresa, usada para exibir campos monetários."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__maximum_amount
msgid ""
"The maximum payment amount that this payment provider is available for. "
"Leave blank to make it available for any payment amount."
msgstr ""
"O valor máximo de pagamento para a qual este provedor de pagamento está "
"disponível. Deixe em branco para disponibilizar para qualquer valor de "
"pagamento."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "A mensagem exibida se o pagamento for autorizado"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__cancel_msg
msgid ""
"The message displayed if the order is cancelled during the payment process"
msgstr ""
"A mensagem exibida se o pedido for cancelado durante o processo de pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr ""
"A mensagem exibida se o pedido for concluído com sucesso após o pagamento "
"ser processado"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr ""
"A mensagem exibida se o pedido estiver pendente após o pagamento ser "
"processado"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr "A mensagem exibida para explicar a auxiliar no processo de pagamento"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"O pagamento deve ser direto, com redirecionamento ou feito através de um "
"token."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__primary_payment_method_id
msgid ""
"The primary payment method of the current payment method, if the latter is a brand.\n"
"For example, \"Card\" is the primary payment method of the card brand \"VISA\"."
msgstr ""
"A forma de pagamento primária da forma de pagamento atual, se esta última for uma marca.\n"
"Por exemplo, \"cartão\" é a forma de pagamento primária da marca de cartões \"VISA\"."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__provider_ref
msgid "The provider reference of the token of the transaction."
msgstr "A referência do provedor do token da transação."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_reference
msgid "The provider reference of the transaction"
msgstr "A referência do provedor da transação"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image_payment_form
msgid "The resized image displayed on the payment form."
msgstr "A imagem redimensionada exibida no formulário de pagamento.."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "A rota para a qual o usuário é redirecionado após a transação"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "The saving of your payment method has been canceled."
msgstr "O salvamento de sua forma de pagamento foi cancelado."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of the related child transactions"
msgstr "A transação de origem das transações secundárias relacionadas"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__code
#: model:ir.model.fields,help:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,help:payment.field_payment_transaction__payment_method_code
msgid "The technical code of this payment method."
msgstr "O código técnico desta forma de pagamento."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__code
#: model:ir.model.fields,help:payment.field_payment_token__provider_code
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_code
msgid "The technical code of this payment provider."
msgstr "O código técnico deste provedor de pagamento."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""
"O modelo renderizando um formulário enviado para redirecionar o usuário ao "
"fazer um pagamento"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__express_checkout_form_view_id
msgid "The template rendering the express payment methods' form."
msgstr "O modelo renderizando o formulário das formas de pagamento expresso."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr ""
"O modelo renderizando o formulário em linha de pagamento ao fazer um "
"pagamento direto"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__token_inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a payment by "
"token."
msgstr ""
"O modelo renderizando o formulário em linha de pagamento ao fazer um "
"pagamento por token."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(provider_name)s)."
msgstr ""
"A transação com a referência %(ref)s no valor de %(amount)s encontrou um "
"erro (%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(provider_name)s)."
msgstr ""
"A transação com a referência %(ref)s no valor de %(amount)s foi autorizada "
"(%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(provider_name)s)."
msgstr ""
"A transação com a referência %(ref)s no valor de %(amount)s foi confirmada "
"(%(provider_name)s)."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "Não há transações a serem exibidas"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "There is no token created yet."
msgstr "Ainda não há tokens criados."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "There is nothing to be paid."
msgstr "Não há nada a pagar."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "Não há nada a pagar."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid ""
"This action will also archive %s tokens that are registered with this "
"payment method."
msgstr ""
"Essa ação também arquivará %s tokens que estão registrados com essa forma de"
" pagamento."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"This action will also archive %s tokens that are registered with this "
"provider. "
msgstr ""
"Essa ação também arquivará %s tokens que estão registrados com essa forma de"
" pagamento."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"provider's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"Isso controla se os clientes podem salvar suas formas de pagamento como tokens de pagamento.\n"
"Um token de pagamento é um link anônimo para as informações da forma de pagamento salva no\n"
"banco de dados do provedor, permitindo que o cliente o reutilize em uma próxima compra."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_express_checkout
msgid ""
"This controls whether customers can use express payment methods. Express "
"checkout enables customers to pay with Google Pay and Apple Pay from which "
"address information is collected at payment."
msgstr ""
"Isso controla se os clientes podem usar formas de pagamento expresso. O "
"checkout expresso permite que os clientes paguem com Google Pay e Apple Pay,"
" de onde o endereço é coletado no momento do pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment providers.\n"
"                     Setting an email for this partner is advised."
msgstr ""
"Este usuário não tem e-mail informado, o que pode causar problemas com alguns provedores de serviços de pagamento.\n"
"                     É recomendado definir um e-mail para este usuário."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid ""
"This payment method needs a partner in crime; you should enable a payment "
"provider supporting this method first."
msgstr ""
"Esta forma de pagamento precisa de um parceiro; você deve primeiro habilitar"
" um provedor de pagamentos que suporte este método."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"This transaction has been confirmed following the processing of its partial "
"capture and partial void transactions (%(provider)s)."
msgstr ""
"Esta transação foi confirmada em decorrência do processamento das transações"
" de captura parcial e de cancelamento parcial (%(provider)s)."

#. module: payment
#: model:payment.method,name:payment.payment_method_tienphong
msgid "Tienphong"
msgstr "Tienphong"

#. module: payment
#: model:payment.method,name:payment.payment_method_tinka
msgid "Tinka"
msgstr "Tinka"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__token_inline_form_view_id
msgid "Token Inline Form Template"
msgstr "Modelo de formulário em linha do token"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_tokenization
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_tokenization
msgid "Tokenization"
msgstr "Tokenização"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_tokenization
msgid ""
"Tokenization is the process of saving the payment details as a token that "
"can later be reused without having to enter the payment details again."
msgstr ""
"A tokenização é o processo de salvar as informações de pagamento como um "
"token que pode ser reutilizado sem necessidade de inserir as informações "
"novamente."

#. module: payment
#: model:payment.method,name:payment.payment_method_toss_pay
msgid "Toss Pay"
msgstr "Toss Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_touch_n_go
msgid "Touch'n Go"
msgstr "Touch'n Go"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__transaction_ids
msgid "Transaction"
msgstr "Transação"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"Transaction authorization is not supported by the following payment "
"providers: %s"
msgstr ""
"A autorização da transação não é suportada pelos seguintes provedores de "
"pagamento: %s"

#. module: payment
#: model:payment.method,name:payment.payment_method_truemoney
msgid "TrueMoney"
msgstr "TrueMoney"

#. module: payment
#: model:payment.method,name:payment.payment_method_trustly
msgid "Trustly"
msgstr "Trustly"

#. module: payment
#: model:payment.method,name:payment.payment_method_twint
msgid "Twint"
msgstr "Twint"

#. module: payment
#: model:payment.method,name:payment.payment_method_upi
msgid "UPI"
msgstr "UPI"

#. module: payment
#: model:payment.method,name:payment.payment_method_ussd
msgid "USSD"
msgstr "USSD"

#. module: payment
#: model:payment.method,name:payment.payment_method_unionpay
msgid "UnionPay"
msgstr "UnionPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_uob
msgid "United Overseas Bank"
msgstr "United Overseas Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_uatp
msgid "Universal Air Travel Plan"
msgstr "Universal Air Travel Plan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Unpublished"
msgstr "Não publicado"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__none
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__none
msgid "Unsupported"
msgstr "Sem suporte"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Upgrade"
msgstr "Upgrade"

#. module: payment
#: model:payment.method,name:payment.payment_method_vpay
msgid "V PAY"
msgstr "V PAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_visa
msgid "VISA"
msgstr "VISA"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "Validação da forma de pagamento"

#. module: payment
#: model:payment.method,name:payment.payment_method_venmo
msgid "Venmo"
msgstr "Venmo"

#. module: payment
#: model:payment.method,name:payment.payment_method_vietcom
msgid "Vietcombank"
msgstr "Vietcombank"

#. module: payment
#: model:payment.method,name:payment.payment_method_vipps
msgid "Vipps"
msgstr "Vipps"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__void_remaining_amount
msgid "Void Remaining Amount"
msgstr "Cancelar quantia restante"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "Transação nula"

#. module: payment
#: model:payment.method,name:payment.payment_method_wallets_india
msgid "Wallets India"
msgstr "Wallets India"

#. module: payment
#: model:payment.method,name:payment.payment_method_walley
msgid "Walley"
msgstr "Walley"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#: code:addons/payment/models/payment_provider.py:0
msgid "Warning"
msgstr "Aviso"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__warning_message
msgid "Warning Message"
msgstr "Mensagem de aviso"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Warning!"
msgstr "Aviso!"

#. module: payment
#: model:payment.method,name:payment.payment_method_wechat_pay
msgid "WeChat Pay"
msgstr "WeChat Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_welend
msgid "WeLend"
msgstr "WeLend"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr "Se um token de pagamento deve ser criado ao pós-processar a transação"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_capture_wizard__support_partial_capture
msgid ""
"Whether each of the transactions' provider supports the partial capture."
msgstr "Se o provedor de cada transação permite captura parcial."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__is_published
msgid ""
"Whether the provider is visible on the website or not. Tokens remain "
"functional but are only visible on manage forms."
msgstr ""
"Se o provedor está visível no site ou não. Os tokens permanecem funcionais, "
"mas só ficam visíveis em formulários de gerenciamento."

#. module: payment
#: model:payment.provider,name:payment.payment_provider_transfer
msgid "Wire Transfer"
msgstr "Transferência bancária"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_worldline
msgid "Worldline"
msgstr "Worldline"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_xendit
msgid "Xendit"
msgstr "Xendit"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid ""
"You can't unarchive tokens linked to inactive payment methods or disabled "
"providers."
msgstr ""
"Não é possível desarquivar tokens vinculados a formas de pagamento inativs "
"ou provedores desativados."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"You cannot change the company of a payment provider with existing "
"transactions."
msgstr ""
"Não é possível alterar a empresa de um provedor de pagamento com transações "
"existentes."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"You cannot delete the payment provider %s; disable it or uninstall it "
"instead."
msgstr ""
"Você não pode excluir o provedor de pagamento %s; em vez disso, desabilite-o"
" ou desinstale-o."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid "You cannot publish a disabled provider."
msgstr "Não é possível publicar um provedor desabilitado."

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "You do not have access to this payment token."
msgstr "Você não tem acesso a este token de pagamento."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid ""
"You should receive an email confirming your payment within a few\n"
"                                    minutes."
msgstr ""
"Você deverá receber um e-mail confirmando seu pagamento em alguns\n"
"minutos."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,auth_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,auth_msg:payment.payment_provider_aps
#: model_terms:payment.provider,auth_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,auth_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_demo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,auth_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,auth_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,auth_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,auth_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,auth_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,auth_msg:payment.payment_provider_xendit
msgid "Your payment has been authorized."
msgstr "Seu pagamento foi autorizado."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_aps
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_demo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_xendit
msgid "Your payment has been cancelled."
msgstr "Seu pagamento foi cancelado."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,pending_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,pending_msg:payment.payment_provider_aps
#: model_terms:payment.provider,pending_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,pending_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_demo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,pending_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,pending_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,pending_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,pending_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,pending_msg:payment.payment_provider_xendit
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr ""
"Seu pagamento foi processado com sucesso, mas está aguardando por aprovação."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,done_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,done_msg:payment.payment_provider_aps
#: model_terms:payment.provider,done_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,done_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,done_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,done_msg:payment.payment_provider_demo
#: model_terms:payment.provider,done_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,done_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,done_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,done_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,done_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,done_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,done_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,done_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,done_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,done_msg:payment.payment_provider_xendit
msgid "Your payment has been successfully processed."
msgstr "Seu pagamento foi processado com sucesso."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Your payment has not been processed yet."
msgstr "O seu pagamento ainda não foi processado."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Your payment is on its way!"
msgstr "Seu pagamento está a caminho!"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Your payment method has been saved."
msgstr "A forma de pagamento foi salva."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Your payment methods"
msgstr "Suas formas de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "CEP"

#. module: payment
#: model:payment.method,name:payment.payment_method_zalopay
msgid "Zalopay"
msgstr "Zalopay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
#: model:payment.method,name:payment.payment_method_zip
msgid "Zip"
msgstr "CEP"

#. module: payment
#: model:payment.method,name:payment.payment_method_cofidis
msgid "cofidis"
msgstr "cofidis"

#. module: payment
#: model:payment.method,name:payment.payment_method_enets
msgid "eNETS"
msgstr "eNETS"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "express checkout not supported"
msgstr "não há suporte para checkout expresso"

#. module: payment
#: model:payment.method,name:payment.payment_method_ideal
msgid "iDEAL"
msgstr "iDEAL"

#. module: payment
#: model:payment.method,name:payment.payment_method_in3
msgid "in3"
msgstr "in3"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible country"
msgstr "país incompatível"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible currency"
msgstr "moeda incompatível"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible website"
msgstr "site incompatível"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "manual capture not supported"
msgstr "sem suporte para captura manual"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "maximum amount exceeded"
msgstr "valor máximo excedido"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "no supported provider available"
msgstr "nenhum provedor suportado disponível"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "payment method"
msgstr "forma de pagamento"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "provider"
msgstr "provedor"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid ""
"to make this\n"
"                    payment."
msgstr ""
"para realizar este\n"
"                    pagamento."

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "tokenization not supported"
msgstr "sem suporte para tokenização"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "tokenization without payment no supported"
msgstr "tokenização sem pagamento não suportada"
