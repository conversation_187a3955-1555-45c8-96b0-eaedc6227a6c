# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment
# 
# Translators:
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr "Dữ liệu đã được nạp"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "(ID:"
msgstr "(ID:"

#. module: payment
#: model:payment.method,name:payment.payment_method_7eleven
msgid "7Eleven"
msgstr "7Eleven"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid ""
"<h3>Please make a payment to: </h3><ul><li>Bank: %(bank)s</li><li>Account "
"Number: %(account_number)s</li><li>Account Holder: "
"%(account_holder)s</li></ul>"
msgstr ""
"<h3>Vui lòng thực hiện thanh toán tới: </h3><ul><li>Ngân hàng: "
"%(bank)s</li><li>Số tài khoản: %(account_number)s</li><li>Chủ tài khoản: "
"%(account_holder)s</li></ul>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/> These properties are set to\n"
"                                match the behavior of providers and that of their integration with\n"
"                                Odoo regarding this payment method. Any change may result in errors\n"
"                                and should be tested on a test database first."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/> Các thuộc tính này được thiết lập để\n"
"                                khớp hành vi của nhà cung cấp và hành vi tích hợp của họ với\n"
"                                Odoo liên quan đến phương thức thanh toán này. Mọi thay đổi đều có thể\n"
"                                tạo ra lỗi và trước tiên phải được kiểm tra trên cơ sở dữ liệu kiểm thử."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_breadcrumb
msgid "<i class=\"fa fa-home\" role=\"img\" title=\"Home\" aria-label=\"Home\"/>"
msgstr "<i class=\"fa fa-home\" role=\"img\" title=\"Trang chủ\" aria-label=\"Home\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Some of the transactions you intend to capture can"
" only be captured in full. Handle the transactions individually to capture a"
" partial amount.\"/>"
msgstr ""
"<i class=\"fa fa-info-circle oe_inline\" invisible=\"support_partial_capture"
" != 'full_only'\" title=\"Một số giao dịch bạn định thu hồi chỉ có thể được "
"thu đầy đủ. Hãy xử lý các giao dịch một cách riêng lẻ để thu tiền từng "
"phần.\"/>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid ""
"<i class=\"fa fa-trash\" title=\"Delete payment method\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"
msgstr ""
"<i class=\"fa fa-trash\" title=\"Xoá phương thức thanh toán\" data-bs-"
"toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-delay=\"0\"/>"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "<i class=\"oi oi-arrow-right me-1\"></i> Configure a payment provider"
msgstr ""
"<i class=\"oi oi-arrow-right me-1\"></i> Cấu hình nhà cung cấp dịch vụ thanh"
" toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Enable Payment Methods"
msgstr ""
"<i class=\"oi oi-fw o_button_icon oi-arrow-right\"/>\n"
"                                            Bật phương thức thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
msgid "<small class=\"text-600\">Save my payment details</small>"
msgstr "<small class=\"text-600\">Lưu thông tin thanh toán của tôi</small>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-danger\">Unpublished</span>"
msgstr "<span class=\"o_stat_text text-danger\">Đã huỷ hiển thị</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "<span class=\"o_stat_text text-success\">Published</span>"
msgstr "<span class=\"o_stat_text text-success\">Đã hiển thị</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.view_partners_form_payment_defaultcreditcard
msgid "<span class=\"o_stat_text\">Saved Payment Methods</span>"
msgstr "<span class=\"o_stat_text\">Đã lưu phương thức thanh toán</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                All countries are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_country_ids\">\n"
"                                Mọi quốc gia đều được hỗ trợ.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                All currencies are supported.\n"
"                            </span>"
msgstr ""
"<span class=\"oe_inline text-muted\" invisible=\"supported_currency_ids\">\n"
"                                Mọi loại tiền tệ đều được hỗ trợ.\n"
"                            </span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.method_form
#: model_terms:ir.ui.view,arch_db:payment.token_form
msgid "<span><i class=\"fa fa-lock\"/> Secured by</span>"
msgstr "<span><i class=\"fa fa-lock\"/> Bảo mật bởi</span>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"fa fa-file-text\"/> Show availability report</strong>"
msgstr ""
"<strong><i class=\"fa fa-file-text\"/> Hiện báo cáo tình trạng khả "
"dụng</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"oi oi-arrow-right\"/> Payment Methods</strong>"
msgstr "<strong><i class=\"oi oi-arrow-right\"/> Phương thức thanh toán</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong><i class=\"oi oi-arrow-right\"/> Payment Providers</strong>"
msgstr ""
"<strong><i class=\"oi oi-arrow-right\"/> Nhà cung cấp dịch vụ thanh "
"toán</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "<strong>No payment method available</strong>"
msgstr "<strong>Không có phương thức thanh toán nào khả dụng</strong>"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> There is a partial capture pending. Please wait a\n"
"                    moment for it to be processed. Check your payment provider configuration if\n"
"                    the capture is still pending after a few minutes."
msgstr ""
"<strong>Cảnh báo!</strong> Có khoản thu hồi thanh toán một phần đang treo. Vui lòng chờ\n"
"                       xử lý trong giây lát. Ngoài ra, hãy kiểm tra cấu hình nhà cung cấp dịch vụ \n"
"                       thanh toán của bạn nếu khoản thu hồi vẫn còn treo sau vài phút."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid ""
"<strong>Warning!</strong> You can not capture a negative amount nor more\n"
"                    than"
msgstr ""
"<strong>Cảnh báo!</strong> Bạn không thể thu số tiền nhỏ hơn 0 hoặc nhiều\n"
"                    hơn"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid ""
"<strong>Warning</strong> Creating a payment provider from the <em>CREATE</em> button is not supported.\n"
"                        Please use the <em>Duplicate</em> action instead."
msgstr ""
"<strong>Cảnh báo</strong> Không hỗ trợ tạo nhà cung cấp dịch vụ thanh toán từ nút <em>TẠO</em>.\n"
"                        Thay vào đó, vui lòng sử dụng thao tác <em>Nhân bản</em>."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid ""
"<strong>Warning</strong> Make sure you are logged in as the\n"
"                                    correct partner before making this payment."
msgstr ""
"<strong>Cảnh báo</strong> Bảo đảm bạn đã đăng nhập với tư cách\n"
"                                      đối tác thích hợp trước khi thực hiện thanh toán."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> The currency is missing or incorrect."
msgstr ""
"<strong>Cảnh báo</strong> Đơn vị tiền tệ bị thiếu hoặc không chính xác."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "<strong>Warning</strong> You must be logged in to pay."
msgstr "<strong>Cảnh báo</strong> Bạn phải đăng nhập để thanh toán."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A refund request of %(amount)s has been sent. The payment will be created "
"soon. Refund transaction reference: %(ref)s (%(provider_name)s)."
msgstr ""
"Yêu cầu hoàn trả số tiền %(amount)s đã được gửi đi. Thanh toán sẽ sớm được "
"tạo. Mã giao dịch hoàn tiền: %(ref)s (%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated (%(provider_name)s)."
msgstr "Giao dịch với mã %(ref)s đã được khởi tạo (%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated to save a new "
"payment method (%(provider_name)s)"
msgstr ""
"Giao dịch với mã %(ref)s đã được khởi tạo để lưu một phương thức thanh toán "
"mới (%(provider_name)s)"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"A transaction with reference %(ref)s has been initiated using the payment "
"method %(token)s (%(provider_name)s)."
msgstr ""
"Giao dịch với mã %(ref)s đã được khởi tạo bằng phương thức thanh toán "
"%(token)s (%(provider_name)s)."

#. module: payment
#: model:payment.method,name:payment.payment_method_ach_direct_debit
msgid "ACH Direct Debit"
msgstr "Ghi nợ trực tiếp ACH"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "ACTIVATE STRIPE"
msgstr "KÍCH HOẠT STRIPE"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Account"
msgstr "Tài khoản"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Số tài khoản"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Activate"
msgstr "Kích hoạt"

#. module: payment
#: model:ir.actions.server,name:payment.action_activate_stripe
msgid "Activate Stripe"
msgstr "Kích hoạt Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__active
#: model:ir.model.fields,field_description:payment.field_payment_token__active
msgid "Active"
msgstr "Đang hoạt động"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_address
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Address"
msgstr "Địa chỉ"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_adyen
msgid "Adyen"
msgstr "Adyen"

#. module: payment
#: model:payment.method,name:payment.payment_method_affirm
msgid "Affirm"
msgstr "Affirm"

#. module: payment
#: model:payment.method,name:payment.payment_method_afterpay_riverty
msgid "AfterPay"
msgstr "AfterPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_afterpay
msgid "Afterpay"
msgstr "Afterpay"

#. module: payment
#: model:payment.method,name:payment.payment_method_akulaku
msgid "Akulaku PayLater"
msgstr "Akulaku PayLater"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay_hk
msgid "AliPayHK"
msgstr "AliPayHK"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay
msgid "Alipay"
msgstr "Alipay"

#. module: payment
#: model:payment.method,name:payment.payment_method_alipay_plus
msgid "Alipay+"
msgstr "Alipay+"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_express_checkout
msgid "Allow Express Checkout"
msgstr "Cho phép thanh toán nhanh"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__allow_tokenization
msgid "Allow Saving Payment Methods"
msgstr "Cho phép lưu phương thức thanh toán"

#. module: payment
#: model:payment.method,name:payment.payment_method_alma
msgid "Alma"
msgstr "Alma"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__captured_amount
msgid "Already Captured"
msgstr "Đã thu"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__voided_amount
msgid "Already Voided"
msgstr "Đã vô hiệu"

#. module: payment
#: model:payment.method,name:payment.payment_method_amazon_pay
msgid "Amazon Pay"
msgstr "Amazon Pay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_aps
msgid "Amazon Payment Services"
msgstr "Dịch vụ thanh toán Amazon"

#. module: payment
#: model:payment.method,name:payment.payment_method_amex
msgid "American Express"
msgstr "American Express"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount
#: model:ir.model.fields,field_description:payment.field_payment_transaction__amount
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Amount"
msgstr "Số tiền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__amount_max
msgid "Amount Max"
msgstr "Amount Max"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__amount_to_capture
msgid "Amount To Capture"
msgstr "Số tiền cần thu"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "An error occurred during the processing of your payment."
msgstr "Đã xuất hiện lỗi trong quá trình xử lý thanh toán của bạn."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "An error occurred while saving your payment method."
msgstr "Đã xảy ra lỗi khi lưu phương thức thanh toán của bạn."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Apply"
msgstr "Áp dụng"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
msgid "Are you sure you want to delete this payment method?"
msgstr "Bạn có chắc muốn xóa phương thức thanh toán này không?"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid ""
"Are you sure you want to void the authorized transaction? This action can't "
"be undone."
msgstr ""
"Bạn có chắc chắn muốn vô hiệu giao dịch được ủy quyền không? Bạn không thể "
"hoàn tác tác vụ này."

#. module: payment
#: model:payment.method,name:payment.payment_method_argencard
msgid "Argencard"
msgstr "Argencard"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_asiapay
msgid "Asiapay"
msgstr "Asiapay"

#. module: payment
#: model:payment.method,name:payment.payment_method_atome
msgid "Atome"
msgstr "Atome"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__auth_msg
msgid "Authorize Message"
msgstr "Tin nhắn uỷ quyền"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_authorize
msgid "Authorize.net"
msgstr "Authorize.net"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__authorized
msgid "Authorized"
msgstr "Cơ chế ủy quyền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__authorized_amount
msgid "Authorized Amount"
msgstr "Số tiền được ủy quyền"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Availability"
msgstr "Trạng thái khả dụng"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
msgid "Availability report"
msgstr "Báo cáo về trạng thái khả dụng"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
msgid "Available methods"
msgstr "Phương thức khả dụng"

#. module: payment
#: model:payment.method,name:payment.payment_method_axis
msgid "Axis"
msgstr "Trục"

#. module: payment
#: model:payment.method,name:payment.payment_method_bacs_direct_debit
msgid "BACS Direct Debit"
msgstr "Ghi nợ trực tiếp BACS"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancomat_pay
msgid "BANCOMAT Pay"
msgstr "BANCOMAT Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_bca
msgid "BCA"
msgstr "BCA"

#. module: payment
#: model:payment.method,name:payment.payment_method_becs_direct_debit
msgid "BECS Direct Debit"
msgstr "Ghi nợ trực tiếp BECS"

#. module: payment
#: model:payment.method,name:payment.payment_method_blik
msgid "BLIK"
msgstr "BLIK"

#. module: payment
#: model:payment.method,name:payment.payment_method_brankas
msgid "BRANKAS"
msgstr "BRANKAS"

#. module: payment
#: model:payment.method,name:payment.payment_method_bri
msgid "BRI"
msgstr "BRI"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancnet
msgid "BancNet"
msgstr "BancNet"

#. module: payment
#: model:payment.method,name:payment.payment_method_banco_de_bogota
msgid "Banco de Bogota"
msgstr "Banco de Bogota"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancolombia
msgid "Bancolombia"
msgstr "Bancolombia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bancontact
msgid "Bancontact"
msgstr "Bancontact"

#. module: payment
#: model:payment.method,name:payment.payment_method_bangkok_bank
msgid "Bangkok Bank"
msgstr "Bangkok Bank"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Bank"
msgstr "Ngân hàng"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_account
msgid "Bank Account"
msgstr "Tài khoản ngân hàng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Tên ngân hàng"

#. module: payment
#: model:payment.method,name:payment.payment_method_bni
msgid "Bank Negara Indonesia"
msgstr "Bank Negara Indonesia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_permata
msgid "Bank Permata"
msgstr "Bank Permata"

#. module: payment
#: model:payment.method,name:payment.payment_method_bsi
msgid "Bank Syariah Indonesia"
msgstr "Bank Syariah Indonesia"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_transfer
msgid "Bank Transfer"
msgstr "Chuyển khoản ngân hàng"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_of_ayudhya
msgid "Bank of Ayudhya"
msgstr "Bank of Ayudhya"

#. module: payment
#: model:payment.method,name:payment.payment_method_bpi
msgid "Bank of the Philippine Islands"
msgstr "Bank of the Philippine Islands"

#. module: payment
#: model:payment.method,name:payment.payment_method_bank_reference
msgid "Bank reference"
msgstr "Mã tham chiếu ngân hàng"

#. module: payment
#: model:payment.method,name:payment.payment_method_belfius
msgid "Belfius"
msgstr "Belfius"

#. module: payment
#: model:payment.method,name:payment.payment_method_benefit
msgid "Benefit"
msgstr "Quyền lợi"

#. module: payment
#: model:payment.method,name:payment.payment_method_bharatqr
msgid "BharatQR"
msgstr "BharatQR"

#. module: payment
#: model:payment.method,name:payment.payment_method_billease
msgid "BillEase"
msgstr "BillEase"

#. module: payment
#: model:payment.method,name:payment.payment_method_billink
msgid "Billink"
msgstr "Billink"

#. module: payment
#: model:payment.method,name:payment.payment_method_bizum
msgid "Bizum"
msgstr "Bizum"

#. module: payment
#: model:payment.method,name:payment.payment_method_boleto
msgid "Boleto"
msgstr "Boleto"

#. module: payment
#: model:payment.method,name:payment.payment_method_boost
msgid "Boost"
msgstr "Boost"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__brand_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Brands"
msgstr "Thương hiệu"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_buckaroo
msgid "Buckaroo"
msgstr "Buckaroo"

#. module: payment
#: model:payment.method,name:payment.payment_method_cimb_niaga
msgid "CIMB Niaga"
msgstr "CIMB Niaga"

#. module: payment
#: model:payment.method,name:payment.payment_method_cmr
msgid "CMR"
msgstr "CMR"

#. module: payment
#: model:payment.method,name:payment.payment_method_cabal
msgid "Cabal"
msgstr "Cabal"

#. module: payment
#: model:payment.method,name:payment.payment_method_caixa
msgid "Caixa"
msgstr "Caixa"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Cancel"
msgstr "Hủy"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__cancel
msgid "Canceled"
msgstr "Đã huỷ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__cancel_msg
msgid "Cancelled Message"
msgstr "Tin nhắn đã bị hủy"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Cannot delete payment method"
msgstr "Không thể xoá phương thức thanh toán"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Cannot save payment method"
msgstr "Không thể lưu phương thức thanh toán"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
msgid "Capture"
msgstr "Thu hồi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__capture_manually
msgid "Capture Amount Manually"
msgstr "Ghi số tiền theo cách thủ công"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Capture Transaction"
msgstr "Chấp nhận giao dịch"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""
"Ghi nhận số tiền từ Odoo, khi giao hàng hoàn tất.\n"
"Dùng tính năng này nếu bạn muốn tính phí khách hàng chỉ khi\n"
"bạn chắc chắn có thể giao hàng cho họ."

#. module: payment
#: model:payment.method,name:payment.payment_method_card
msgid "Card"
msgstr "Thẻ"

#. module: payment
#: model:payment.method,name:payment.payment_method_carnet
msgid "Carnet"
msgstr "Carnet"

#. module: payment
#: model:payment.method,name:payment.payment_method_cartes_bancaires
msgid "Cartes Bancaires"
msgstr "Cartes Bancaires"

#. module: payment
#: model:payment.method,name:payment.payment_method_cash_app_pay
msgid "Cash App Pay"
msgstr "Cash App Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_cashalo
msgid "Cashalo"
msgstr "Cashalo"

#. module: payment
#: model:payment.method,name:payment.payment_method_cebuana
msgid "Cebuana"
msgstr "Cebuana"

#. module: payment
#: model:payment.method,name:payment.payment_method_cencosud
msgid "Cencosud"
msgstr "Cencosud"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__child_transaction_ids
msgid "Child Transactions"
msgstr "Giao dịch phụ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Child transactions"
msgstr "Giao dịch phụ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Choose a payment method"
msgstr "Chọn phương thức thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Choose another method <i class=\"oi oi-arrow-down\"/>"
msgstr "Chọn phương thức khác <i class=\"oi oi-arrow-down\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_cirrus
msgid "Cirrus"
msgstr "Cirrus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_city
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "City"
msgstr "Thành phố"

#. module: payment
#: model:payment.method,name:payment.payment_method_clearpay
msgid "Clearpay"
msgstr "Clearpay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_capture_wizard_view_form
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Close"
msgstr "Đóng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__code
#: model:ir.model.fields,field_description:payment.field_payment_provider__code
msgid "Code"
msgstr "Mã"

#. module: payment
#: model:payment.method,name:payment.payment_method_codensa
msgid "Codensa"
msgstr "Codensa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__color
msgid "Color"
msgstr "Màu sắc"

#. module: payment
#: model:ir.model,name:payment.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__company_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__company_id
#: model:ir.model.fields,field_description:payment.field_payment_token__company_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__company_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Company"
msgstr "Công ty"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Configuration"
msgstr "Cấu hình"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Confirm Deletion"
msgstr "Xác nhận xóa"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__done
msgid "Confirmed"
msgstr "Đã xác nhận"

#. module: payment
#: model:ir.model,name:payment.model_res_partner
msgid "Contact"
msgstr "Liên hệ"

#. module: payment
#: model:payment.method,name:payment.payment_method_cordial
msgid "Cordial"
msgstr "Cordial"

#. module: payment
#: model:payment.method,name:payment.payment_method_cordobesa
msgid "Cordobesa"
msgstr "Cordobesa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_id
msgid "Corresponding Module"
msgstr "Phân hệ tương ứng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_country_ids
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_country_ids
msgid "Countries"
msgstr "Quốc gia"

#. module: payment
#: model:ir.model,name:payment.model_res_country
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_country_id
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Country"
msgstr "Quốc gia"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__tokenize
msgid "Create Token"
msgstr "Tạo token"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_provider
msgid "Create a new payment provider"
msgstr "Tạo một nhà cung cấp dịch vụ thanh toán mới"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__create_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_method__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__create_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__create_date
#: model:ir.model.fields,field_description:payment.field_payment_token__create_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Creating a transaction from an archived token is forbidden."
msgstr "Cấm tạo giao dịch từ token đã lưu trữ."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Credentials"
msgstr "Thông tin đăng nhập"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__stripe
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr "Thẻ tín dụng và ghi nợ (qua Stripe)"

#. module: payment
#: model:payment.method,name:payment.payment_method_credit
msgid "Credit Payment"
msgstr "Thanh toán tín dụng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__supported_currency_ids
#: model:ir.model.fields,field_description:payment.field_payment_provider__available_currency_ids
msgid "Currencies"
msgstr "Tiền tệ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__currency_id
#: model:ir.model.fields,field_description:payment.field_payment_provider__main_currency_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__currency_id
msgid "Currency"
msgstr "Tiền tệ"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__manual
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr "Hướng dẫn thanh toán tùy chỉnh"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_id
msgid "Customer"
msgstr "Khách hàng"

#. module: payment
#: model:payment.method,name:payment.payment_method_dana
msgid "Dana"
msgstr "Dana"

#. module: payment
#: model:payment.method,name:payment.payment_method_dankort
msgid "Dankort"
msgstr "Dankort"

#. module: payment
#: model:payment.method,name:payment.payment_method_davivienda
msgid "Davivienda"
msgstr "Davivienda"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__sequence
msgid "Define the display order"
msgstr "Xác định trình tự xuất hiện"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_demo
msgid "Demo"
msgstr "Demo"

#. module: payment
#: model:payment.method,name:payment.payment_method_diners
msgid "Diners Club International"
msgstr "Diners Club International"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__disabled
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Disabled"
msgstr "Đã vô hiệu"

#. module: payment
#: model:payment.method,name:payment.payment_method_discover
msgid "Discover"
msgstr "Khám phá"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_method__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider__display_name
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:payment.field_payment_token__display_name
#: model:ir.model.fields,field_description:payment.field_payment_transaction__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: payment
#: model:payment.method,name:payment.payment_method_dolfin
msgid "Dolfin"
msgstr "Dolfin"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Don't hesitate to contact us if you don't receive it."
msgstr ""
"Đừng ngần ngại liên hệ với chúng tôi nếu bạn chưa nhận được thanh toán."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__done_msg
msgid "Done Message"
msgstr "Hoàn tất tin nhắn"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__draft
msgid "Draft"
msgstr "Nháp"

#. module: payment
#: model:payment.method,name:payment.payment_method_duitnow
msgid "DuitNow"
msgstr "DuitNow"

#. module: payment
#: model:payment.method,name:payment.payment_method_emi_india
msgid "EMI"
msgstr "EMI"

#. module: payment
#: model:payment.method,name:payment.payment_method_eps
msgid "EPS"
msgstr "EPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_elo
msgid "Elo"
msgstr "Elo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_email
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__paypal_email_account
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_email
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "Email"
msgstr "Email"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__enabled
msgid "Enabled"
msgstr "Đã bật"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Enterprise"
msgstr "Doanh nghiệp"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__error
msgid "Error"
msgstr "Lỗi"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Error: %s"
msgstr "Lỗi: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_express_checkout
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_express_checkout
msgid "Express Checkout"
msgstr "Thanh toán nhanh"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__express_checkout_form_view_id
msgid "Express Checkout Form Template"
msgstr "Mẫu biểu mẫu thanh toán nhanh"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_express_checkout
msgid ""
"Express checkout allows customers to pay faster by using a payment method "
"that provides all required billing and shipping information, thus allowing "
"to skip the checkout process."
msgstr ""
"Thanh toán nhanh cho phép khách hàng thanh toán nhanh hơn bằng cách sử dụng "
"một phương thức thanh toán cung cấp tất cả thông tin thanh toán và giao hàng"
" bắt buộc, do đó có thể bỏ qua quá trình này khi thanh toán."

#. module: payment
#: model:payment.method,name:payment.payment_method_fps
msgid "FPS"
msgstr "FPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_fpx
msgid "FPX"
msgstr "FPX"

#. module: payment
#: model:payment.method,name:payment.payment_method_floa_bank
msgid "Floa Bank"
msgstr "Floa Bank"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_flutterwave
msgid "Flutterwave"
msgstr "Flutterwave"

#. module: payment
#: model:payment.method,name:payment.payment_method_frafinance
msgid "Frafinance"
msgstr "Frafinance"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__partial
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__partial
msgid "Full & Partial"
msgstr "Toàn bộ & một phần"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__full_only
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__full_only
msgid "Full Only"
msgstr "Full Only"

#. module: payment
#: model:payment.method,name:payment.payment_method_gcash
msgid "GCash"
msgstr "GCash"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate Payment Link"
msgstr "Tạo liên kết thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr "Tạo liên kết thanh toán bán hàng"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Generate and Copy Payment Link"
msgstr "Tạo và sao chép liên kết thanh toán"

#. module: payment
#: model:payment.method,name:payment.payment_method_giropay
msgid "Giropay"
msgstr "Giropay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Go to my Account <i class=\"oi oi-arrow-right ms-2\"/>"
msgstr "Đi đến tài khoản của tôi <i class=\"oi oi-arrow-right ms-2\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_gopay
msgid "GoPay"
msgstr "GoPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_gsb
msgid "Government Savings Bank"
msgstr "Government Savings Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_grabpay
msgid "GrabPay"
msgstr "GrabPay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Group By"
msgstr "Nhóm theo"

#. module: payment
#: model:payment.method,name:payment.payment_method_hd
msgid "HD Bank"
msgstr "HD Bank"

#. module: payment
#: model:ir.model,name:payment.model_ir_http
msgid "HTTP Routing"
msgstr "Định tuyến HTTP"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_draft_children
msgid "Has Draft Children"
msgstr "Có giao dịch phụ nháp"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__has_remaining_amount
msgid "Has Remaining Amount"
msgstr "Có số tiền còn lại"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__is_post_processed
msgid "Has the payment been post-processed"
msgstr "Khoản thanh toán đã được xử lý sau"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pre_msg
msgid "Help Message"
msgstr "Thông điệp trợ giúp"

#. module: payment
#: model:payment.method,name:payment.payment_method_hipercard
msgid "Hipercard"
msgstr "Hipercard"

#. module: payment
#: model:payment.method,name:payment.payment_method_hoolah
msgid "Hoolah"
msgstr "Hoolah"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_onboarding_wizard_form
msgid "How to configure your PayPal account"
msgstr "Cách cấu hình tài khoản PayPal của bạn"

#. module: payment
#: model:payment.method,name:payment.payment_method_humm
msgid "Humm"
msgstr "Humm"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_method__id
#: model:ir.model.fields,field_description:payment.field_payment_provider__id
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:payment.field_payment_token__id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__id
msgid "ID"
msgstr "ID"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid ""
"If you believe that it is an error, please contact the website "
"administrator."
msgstr "Nếu bạn cho rằng đây là lỗi, hãy liên hệ quản trị website."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image
#: model:ir.model.fields,field_description:payment.field_payment_provider__image_128
msgid "Image"
msgstr "Hình ảnh"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__state
msgid ""
"In test mode, a fake payment is processed through a test payment interface.\n"
"This mode is advised when setting up the provider."
msgstr ""
"Trong chế độ kiểm thử, một khoản thanh toán giả sẽ được xử lý thông qua giao diện thanh toán kiểm thử.\n"
"Nên dùng chế độ này khi thiết lập nhà cung cấp dịch vụ thanh toán."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__inline_form_view_id
msgid "Inline Form Template"
msgstr "Mẫu biểu mẫu nội tuyến"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Install"
msgstr "Cài đặt"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_state
msgid "Installation State"
msgstr "Trạng thái cài đặt"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "Installed"
msgstr "Đã cài đặt"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__is_amount_to_capture_valid
msgid "Is Amount To Capture Valid"
msgstr "Số tiền cần thu hợp lệ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__is_post_processed
msgid "Is Post-processed"
msgstr "Được xử lý sau"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__is_primary
msgid "Is Primary Payment Method"
msgstr "Là phương thức thanh toán chính"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_country__is_stripe_supported_country
msgid "Is Stripe Supported Country"
msgstr "Là quốc gia có hỗ trợ Stripe"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/xml/payment_form_templates.xml:0
msgid "It is currently linked to the following documents:"
msgstr "Nó hiện được liên kết với các tài liệu sau:"

#. module: payment
#: model:payment.method,name:payment.payment_method_jcb
msgid "JCB"
msgstr "JCB"

#. module: payment
#: model:payment.method,name:payment.payment_method_jeniuspay
msgid "JeniusPay"
msgstr "JeniusPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_jkopay
msgid "Jkopay"
msgstr "Jkopay"

#. module: payment
#: model:payment.method,name:payment.payment_method_kbc_cbc
msgid "KBC/CBC"
msgstr "KBC/CBC"

#. module: payment
#: model:payment.method,name:payment.payment_method_knet
msgid "KNET"
msgstr "KNET"

#. module: payment
#: model:payment.method,name:payment.payment_method_kakaopay
msgid "KakaoPay"
msgstr "KakaoPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_kasikorn_bank
msgid "Kasikorn Bank"
msgstr "Kasikorn Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna
msgid "Klarna"
msgstr "Klarna"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna_paynow
msgid "Klarna - Pay Now"
msgstr "Klarna - Thanh toán ngay"

#. module: payment
#: model:payment.method,name:payment.payment_method_klarna_pay_over_time
msgid "Klarna - Pay over time"
msgstr "Klarna - Trả góp"

#. module: payment
#: model:payment.method,name:payment.payment_method_kredivo
msgid "Kredivo"
msgstr "Kredivo"

#. module: payment
#: model:payment.method,name:payment.payment_method_krungthai_bank
msgid "KrungThai Bank"
msgstr "KrungThai Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_linepay
msgid "LINE Pay"
msgstr "LINE Pay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__landing_route
msgid "Landing Route"
msgstr "Trang chuyển hướng"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_lang
msgid "Language"
msgstr "Ngôn ngữ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__last_state_change
msgid "Last State Change Date"
msgstr "Ngày cập nhật trạng thái gần nhất"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_method__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_token__write_uid
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_method__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider__write_date
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__write_date
#: model:ir.model.fields,field_description:payment.field_payment_token__write_date
#: model:ir.model.fields,field_description:payment.field_payment_transaction__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: payment
#: model:onboarding.onboarding.step,button_text:payment.onboarding_onboarding_step_payment_provider
msgid "Let's do it"
msgstr "Hãy làm nó"

#. module: payment
#: model:payment.method,name:payment.payment_method_lider
msgid "Lider"
msgstr "Lider"

#. module: payment
#: model:payment.method,name:payment.payment_method_linkaja
msgid "LinkAja"
msgstr "LinkAja"

#. module: payment
#: model:payment.method,name:payment.payment_method_lydia
msgid "Lydia"
msgstr "Lydia"

#. module: payment
#: model:payment.method,name:payment.payment_method_lyfpay
msgid "LyfPay"
msgstr "LyfPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_mpesa
msgid "M-Pesa"
msgstr "M-Pesa"

#. module: payment
#: model:payment.method,name:payment.payment_method_mbway
msgid "MB WAY"
msgstr "MB WAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_mada
msgid "Mada"
msgstr "Mada"

#. module: payment
#: model:payment.method,name:payment.payment_method_maestro
msgid "Maestro"
msgstr "Maestro"

#. module: payment
#: model:payment.method,name:payment.payment_method_magna
msgid "Magna"
msgstr "Magna"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"Making a request to the provider is not possible because the provider is "
"disabled."
msgstr ""
"Không thể tạo yêu cầu cho nhà cung cấp vì nhà cung cấp đã bị vô hiệu hóa."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Manage your payment methods"
msgstr "Quản lý phương thức thanh toán của bạn"

#. module: payment
#: model:payment.method,name:payment.payment_method_mandiri
msgid "Mandiri"
msgstr "Mandiri"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__manual
msgid "Manual"
msgstr "Thủ công"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_manual_capture
msgid "Manual Capture Supported"
msgstr "Hỗ trợ thu hồi thủ công"

#. module: payment
#: model:payment.method,name:payment.payment_method_mastercard
msgid "MasterCard"
msgstr "MasterCard"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__maximum_amount
msgid "Maximum Amount"
msgstr "Số tiền tối đa"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__available_amount
msgid "Maximum Capture Allowed"
msgstr "Thu hồi tối đa được phép"

#. module: payment
#: model:payment.method,name:payment.payment_method_maya
msgid "Maya"
msgstr "Maya"

#. module: payment
#: model:payment.method,name:payment.payment_method_maybank
msgid "Maybank"
msgstr "Maybank"

#. module: payment
#: model:payment.method,name:payment.payment_method_meeza
msgid "Meeza"
msgstr "Meeza"

#. module: payment
#: model:payment.method,name:payment.payment_method_mercado_livre
msgid "Mercado Livre"
msgstr "Mercado Livre"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mercado_pago
msgid "Mercado Pago"
msgstr "Mercado Pago"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state_message
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Message"
msgstr "Tin nhắn"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Messages"
msgstr "Tin nhắn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "Phương thức"

#. module: payment
#: model:payment.method,name:payment.payment_method_momo
msgid "MoMo"
msgstr "MoMo"

#. module: payment
#: model:payment.method,name:payment.payment_method_mobile_money
msgid "Mobile money"
msgstr "Ví điện tử viễn thông"

#. module: payment
#: model:payment.method,name:payment.payment_method_mobile_pay
msgid "MobilePay"
msgstr "MobilePay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_mollie
msgid "Mollie"
msgstr "Mollie"

#. module: payment
#: model:payment.method,name:payment.payment_method_multibanco
msgid "Multibanco"
msgstr "Multibanco"

#. module: payment
#: model:payment.method,name:payment.payment_method_mybank
msgid "MyBank"
msgstr "MyBank"

#. module: payment
#: model:payment.method,name:payment.payment_method_naps
msgid "NAPS"
msgstr "NAPS"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__name
#: model:ir.model.fields,field_description:payment.field_payment_provider__name
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
#: model_terms:ir.ui.view,arch_db:payment.payment_method_search
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Name"
msgstr "Tên"

#. module: payment
#: model:payment.method,name:payment.payment_method_napas_card
msgid "Napas Card"
msgstr "Thẻ Napas"

#. module: payment
#: model:payment.method,name:payment.payment_method_naranja
msgid "Naranja"
msgstr "Naranja"

#. module: payment
#: model:payment.method,name:payment.payment_method_nativa
msgid "Nativa"
msgstr "Nativa"

#. module: payment
#: model:payment.method,name:payment.payment_method_naver_pay
msgid "Naver Pay"
msgstr "Naver Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_netbanking
msgid "Netbanking"
msgstr "Ngân hàng trực tuyến"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__code__none
msgid "No Provider Set"
msgstr "Không có nhà cung cấp được đặt"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid ""
"No manual payment method could be found for this company. Please create one "
"from the Payment Provider menu."
msgstr ""
"Không thể tìm thấy phương thức thanh toán thủ công nào cho công ty này. Vui "
"lòng tạo trong menu Nhà cung cấp dịch vụ thanh toán."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_method
msgid "No payment methods found for your payment providers."
msgstr ""
"Không tìm thấy phương thức thanh toán nào cho nhà cung cấp dịch vụ thanh "
"toán của bạn."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "No payment providers are configured."
msgstr "Chưa cấu hình phương thức thanh toán nào."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid "No token can be assigned to the public partner."
msgstr "Không thể gán token cho đối tác công khai."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.no_pms_available_warning
msgid "None is configured for:"
msgstr "Chưa có gì được cấu hình cho:"

#. module: payment
#: model:payment.method,name:payment.payment_method_ovo
msgid "OVO"
msgstr "OVO"

#. module: payment
#: model:payment.method,name:payment.payment_method_oca
msgid "Oca"
msgstr "Oca"

#. module: payment
#: model:payment.method,name:payment.payment_method_octopus
msgid "Octopus"
msgstr "Octopus"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__module_to_buy
msgid "Odoo Enterprise Module"
msgstr "Phân hệ Odoo Enterprise"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__offline
msgid "Offline payment by token"
msgstr "Thanh toán offline bằng token"

#. module: payment
#: model:payment.method,name:payment.payment_method_omannet
msgid "OmanNet"
msgstr "OmanNet"

#. module: payment
#: model:ir.model,name:payment.model_onboarding_onboarding_step
msgid "Onboarding Step"
msgstr "Trình tự onboarding"

#. module: payment
#: model:onboarding.onboarding.step,step_image_alt:payment.onboarding_onboarding_step_payment_provider
msgid "Onboarding Step Image"
msgstr "Hình ảnh trình tự onboarding"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_czech_republic
msgid "Online Banking Czech Republic"
msgstr "Ngân hàng trực tuyến Cộng hoà Séc"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_india
msgid "Online Banking India"
msgstr "Ngân hàng trực tuyến Ấn Độ"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_slovakia
msgid "Online Banking Slovakia"
msgstr "Ngân hàng trực tuyến Slovakia"

#. module: payment
#: model:payment.method,name:payment.payment_method_online_banking_thailand
msgid "Online Banking Thailand"
msgstr "Ngân hàng trực tuyến Thái Lan"

#. module: payment
#: model:onboarding.onboarding.step,title:payment.onboarding_onboarding_step_payment_provider
msgid "Online Payments"
msgstr "Thanh toán online"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_direct
msgid "Online direct payment"
msgstr "Thanh toán trực tiếp online"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_token
msgid "Online payment by token"
msgstr "Thanh toán online bằng token"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__online_redirect
msgid "Online payment with redirection"
msgstr "Thanh toán online với chuyển hướng"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_onboarding_wizard.py:0
msgid "Only administrators can access this data."
msgstr "Chỉ có admin mới có thể truy cập vào dữ liệu này."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Only authorized transactions can be voided."
msgstr "Chỉ các giao dịch được ủy quyền mới được hủy bỏ."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Only confirmed transactions can be refunded."
msgstr "Chỉ các giao dịch đã xác nhận mới có thể được hoàn tiền."

#. module: payment
#: model:payment.method,name:payment.payment_method_open_banking
msgid "Open banking"
msgstr "Ngân hàng mở"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__operation
msgid "Operation"
msgstr "Hoạt động"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "Operation not supported."
msgstr "Thao tác không được hỗ trợ."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__other
msgid "Other"
msgstr "Khác"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Other payment methods"
msgstr "Phương thức thanh toán khác"

#. module: payment
#: model:payment.method,name:payment.payment_method_p24
msgid "P24"
msgstr "P24"

#. module: payment
#: model:payment.method,name:payment.payment_method_poli
msgid "POLi"
msgstr "POLi"

#. module: payment
#: model:payment.method,name:payment.payment_method_pps
msgid "PPS"
msgstr "PPS"

#. module: payment
#: model:payment.method,name:payment.payment_method_pse
msgid "PSE"
msgstr "PSE"

#. module: payment
#: model:payment.method,name:payment.payment_method_pace
msgid "Pace."
msgstr "Pace."

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_manual_capture__partial
msgid "Partial"
msgstr "Một phần"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__partner_id
#: model:ir.model.fields,field_description:payment.field_payment_token__partner_id
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Partner"
msgstr "Đối tác"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_name
msgid "Partner Name"
msgstr "Tên đối tác"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Pay"
msgstr "Thanh toán"

#. module: payment
#: model:payment.method,name:payment.payment_method_paylater_india
msgid "Pay Later"
msgstr "Trả sau"

#. module: payment
#: model:payment.method,name:payment.payment_method_pay_easy
msgid "Pay-easy"
msgstr "Pay-easy"

#. module: payment
#: model:payment.method,name:payment.payment_method_paybright
msgid "PayBright"
msgstr "PayBright"

#. module: payment
#: model:payment.method,name:payment.payment_method_pay_id
msgid "PayID"
msgstr "PayID"

#. module: payment
#: model:payment.method,name:payment.payment_method_payme
msgid "PayMe"
msgstr "PayMe"

#. module: payment
#: model:payment.method,name:payment.payment_method_paynow
msgid "PayNow"
msgstr "PayNow"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider_onboarding_wizard__payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:payment.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
#: model:payment.provider,name:payment.payment_provider_paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment
#: model:payment.method,name:payment.payment_method_paypay
msgid "PayPay"
msgstr "PayPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_paysafecard
msgid "PaySafeCard"
msgstr "PaySafeCard"

#. module: payment
#: model:payment.method,name:payment.payment_method_payu
msgid "PayU"
msgstr "PayU"

#. module: payment
#: model:payment.method,name:payment.payment_method_paylib
msgid "Paylib"
msgstr "Paylib"

#. module: payment
#: model:ir.model,name:payment.model_payment_capture_wizard
msgid "Payment Capture Wizard"
msgstr "Công cụ thu hồi thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_details
msgid "Payment Details"
msgstr "Thông tin thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Followup"
msgstr "Follow-up thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Form"
msgstr "Biểu mẫu thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid "Payment Info"
msgstr "Thông tin thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr "Hướng dẫn thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__link
msgid "Payment Link"
msgstr "Liên kết thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_method
#: model:ir.model.fields,field_description:payment.field_payment_provider_onboarding_wizard__payment_method
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_id
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Payment Method"
msgstr "Phương thức thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__payment_method_code
msgid "Payment Method Code"
msgstr "Mã phương thức thanh toán"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model:ir.actions.act_window,name:payment.action_payment_method
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment Methods"
msgstr "Phương thức thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_provider
msgid "Payment Provider"
msgstr "Nhà cung cấp dịch vụ thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_provider
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_list
msgid "Payment Providers"
msgstr "Nhà cung cấp dịch vụ thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_token
#: model:ir.model.fields,field_description:payment.field_payment_transaction__token_id
msgid "Payment Token"
msgstr "Mã thanh toán"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_count
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_count
msgid "Payment Token Count"
msgstr "Số token thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_token
#: model:ir.model.fields,field_description:payment.field_res_partner__payment_token_ids
#: model:ir.model.fields,field_description:payment.field_res_users__payment_token_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
#: model_terms:ir.ui.view,arch_db:payment.payment_token_list
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
msgid "Payment Tokens"
msgstr "Mã thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_transaction
msgid "Payment Transaction"
msgstr "Giao dịch thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction
#: model:ir.model.fields,field_description:payment.field_payment_token__transaction_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_list
msgid "Payment Transactions"
msgstr "Giao dịch thanh toán"

#. module: payment
#: model:ir.actions.act_window,name:payment.action_payment_transaction_linked_to_token
msgid "Payment Transactions Linked To Token"
msgstr "Giao dịch thanh toán liên kết với token"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid "Payment details saved on %(date)s"
msgstr "Thông tin thanh toán đã được lưu vào %(date)s"

#. module: payment
#: model:payment.method,name:payment.payment_method_unknown
msgid "Payment method"
msgstr "Phương thức thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
#: model_terms:ir.ui.view,arch_db:payment.portal_my_home_payment
msgid "Payment methods"
msgstr "Phương thức thanh toán"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "Xử lý thanh toán không thành công"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Payment provider"
msgstr "Nhà cung cấp dịch vụ thanh toán"

#. module: payment
#: model:ir.model,name:payment.model_payment_provider_onboarding_wizard
msgid "Payment provider onboarding wizard"
msgstr "Công cụ onboarding về nhà cung cấp dịch vụ thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report
msgid "Payment providers"
msgstr "Nhà cung cấp dịch vụ thanh toán"

#. module: payment
#: model:ir.actions.server,name:payment.cron_post_process_payment_tx_ir_actions_server
msgid "Payment: Post-process transactions"
msgstr "Thanh toán: Xử lý sau giao dịch"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_token_form
msgid "Payments"
msgstr "Thanh toán"

#. module: payment
#: model:payment.method,name:payment.payment_method_paypal
msgid "Paypal"
msgstr "Paypal"

#. module: payment
#: model:payment.method,name:payment.payment_method_paytm
msgid "Paytm"
msgstr "Paytm"

#. module: payment
#: model:payment.method,name:payment.payment_method_paytrail
msgid "Paytrail"
msgstr "Paytrail"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__state__pending
msgid "Pending"
msgstr "Đang chờ"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__pending_msg
msgid "Pending Message"
msgstr "Thông báo treo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_phone
msgid "Phone"
msgstr "Điện thoại"

#. module: payment
#: model:payment.method,name:payment.payment_method_pix
msgid "Pix"
msgstr "Pix"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid "Please make sure that %(payment_method)s is supported by %(provider)s."
msgstr "Hãy đảm bảo rằng %(payment_method)s được %(provider)s hỗ trợ."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "Please set a positive amount."
msgstr "Vui lòng nhập số tiền lớn hơn 0."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "Please set an amount lower than %s."
msgstr "Vui lòng nhập số tiền nhỏ hơn %s."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid "Please switch to company"
msgstr "Vui lòng chuyển sang công ty"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Please wait..."
msgstr "Vui lòng đợi..."

#. module: payment
#: model:payment.method,name:payment.payment_method_post_finance
msgid "PostFinance Pay"
msgstr "PostFinance Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_poste_pay
msgid "PostePay"
msgstr "PostePay"

#. module: payment
#: model:payment.method,name:payment.payment_method_presto
msgid "Presto"
msgstr "Presto"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__primary_payment_method_id
msgid "Primary Payment Method"
msgstr "Phương thức thanh toán chính"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.confirm
msgid "Processed by"
msgstr "Xử lý bởi"

#. module: payment
#: model:payment.method,name:payment.payment_method_promptpay
msgid "Prompt Pay"
msgstr "PromptPay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_id
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_token_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Provider"
msgstr "Nhà cung cấp"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_code
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_code
msgid "Provider Code"
msgstr "Mã nhà cung cấp"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_token__provider_ref
#: model:ir.model.fields,field_description:payment.field_payment_transaction__provider_reference
msgid "Provider Reference"
msgstr "Tham chiếu nhà cung cấp"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__provider_ids
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Providers"
msgstr "Nhà cung cấp "

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__is_published
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Published"
msgstr "Đã hiển thị"

#. module: payment
#: model:payment.method,name:payment.payment_method_qris
msgid "QRIS"
msgstr "QRIS"

#. module: payment
#: model:payment.method,name:payment.payment_method_rabbit_line_pay
msgid "Rabbit LINE Pay"
msgstr "Rabbit LINE Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_ratepay
msgid "Ratepay"
msgstr "Ratepay"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_razorpay
msgid "Razorpay"
msgstr "Razorpay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "Reason:"
msgstr "Lý do:"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid "Reason: %s"
msgstr "Lý do: %s"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__redirect_form_view_id
msgid "Redirect Form Template"
msgstr "Chuyển hướng biểu mẫu"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__reference
#: model_terms:ir.ui.view,arch_db:payment.confirm
#: model_terms:ir.ui.view,arch_db:payment.pay
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Reference"
msgstr "Tham chiếu"

#. module: payment
#: model:ir.model.constraint,message:payment.constraint_payment_transaction_reference_uniq
msgid "Reference must be unique!"
msgstr "Mã cần phải duy nhất!"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
#: model:ir.model.fields,field_description:payment.field_payment_method__support_refund
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_refund
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__refund
msgid "Refund"
msgstr "Hoàn tiền"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_refund
#: model:ir.model.fields,help:payment.field_payment_provider__support_refund
msgid ""
"Refund is a feature allowing to refund customers directly from the payment "
"in Odoo."
msgstr ""
"Hoàn tiền là tính năng cho phép hoàn tiền trực tiếp cho khách hàng từ thanh "
"toán trong Odoo."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Refunds"
msgstr "Hoàn tiền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__refunds_count
msgid "Refunds Count"
msgstr "Số hoàn tiền"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_id
msgid "Related Document ID"
msgstr "ID tài liệu liên quan"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__res_model
msgid "Related Document Model"
msgstr "Mô hình tài liệu liên quan"

#. module: payment
#: model:payment.method,name:payment.payment_method_revolut_pay
msgid "Revolut Pay"
msgstr "Revolut Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_rupay
msgid "RuPay"
msgstr "RuPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_sepa_direct_debit
#: model:payment.provider,name:payment.payment_provider_sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "Ghi nợ trực tiếp SEPA"

#. module: payment
#: model:payment.method,name:payment.payment_method_samsung_pay
msgid "Samsung Pay"
msgstr "Samsung Pay"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Save"
msgstr "Lưu"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Saving your payment method."
msgstr "Lưu phương thức thanh toán của bạn."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select countries. Leave empty to allow any."
msgstr "Chọn quốc gia. Để trống để sử dụng ở mọi nơi."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select countries. Leave empty to make available everywhere."
msgstr "Chọn quốc gia. Để trống để sử dụng ở mọi nơi."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Select currencies. Leave empty not to restrict any."
msgstr "Chọn tiền tệ. Để trống để không giới hạn loại tiền tệ."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Select currencies. Leave empty to allow any."
msgstr "Chọn tiền tệ. Để trống để sử dụng mọi loại tiền tệ."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_res_company__payment_onboarding_payment_method
msgid "Selected onboarding payment method"
msgstr "Phương thức thanh toán đã chọn cho onboarding"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__sequence
#: model:ir.model.fields,field_description:payment.field_payment_provider__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopback
msgid "ShopBack"
msgstr "ShopBack"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopeepay
msgid "ShopeePay"
msgstr "ShopeePay"

#. module: payment
#: model:payment.method,name:payment.payment_method_shopping
msgid "Shopping Card"
msgstr "Thẻ mua sắm"

#. module: payment
#: model:payment.method,name:payment.payment_method_scb
msgid "Siam Commerical Bank"
msgstr "Siam Commerical Bank"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Skip <i class=\"oi oi-arrow-right ms-1 small\"/>"
msgstr "Bỏ qua <i class=\"oi oi-arrow-right ms-1 small\"/>"

#. module: payment
#: model:payment.method,name:payment.payment_method_sofort
msgid "Sofort"
msgstr "Sofort"

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
msgid ""
"Some of the transactions you intend to capture can only be captured in full."
" Handle the transactions individually to capture a partial amount."
msgstr ""
"Một số giao dịch bạn định thu hồi chỉ có thể được thu đầy đủ. Hãy xử lý các "
"giao dịch một cách riêng lẻ để thu tiền từng phần."

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__source_transaction_id
msgid "Source Transaction"
msgstr "Giao dịch nguồn"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__state
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_state_id
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "State"
msgstr "Trạng thái"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__state
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_search
msgid "Status"
msgstr "Trạng thái"

#. module: payment
#: model:onboarding.onboarding.step,done_text:payment.onboarding_onboarding_step_payment_provider
msgid "Step Completed!"
msgstr "Bước hoàn thành!"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__res_company__payment_onboarding_payment_method__stripe
#: model:payment.provider,name:payment.payment_provider_stripe
msgid "Stripe"
msgstr "Stripe"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__support_partial_capture
msgid "Support Partial Capture"
msgstr "Hỗ trợ thu hồi một phần"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__payment_method_ids
msgid "Supported Payment Methods"
msgstr "Phương thức thanh toán được hỗ trợ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_method_form
msgid "Supported by"
msgstr "Hỗ trợ bởi"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.availability_report_records
msgid "Supported providers:"
msgstr "Nhà cung cấp được hỗ trợ:"

#. module: payment
#: model:payment.method,name:payment.payment_method_swish
msgid "Swish"
msgstr "Swish"

#. module: payment
#: model:payment.method,name:payment.payment_method_tenpay
msgid "TENPAY"
msgstr "TENPAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_ttb
msgid "TTB"
msgstr "TTB"

#. module: payment
#: model:payment.method,name:payment.payment_method_tmb
msgid "Tamilnad Mercantile Bank Limited"
msgstr "Tamilnad Mercantile Bank Limited"

#. module: payment
#: model:payment.method,name:payment.payment_method_tarjeta_mercadopago
msgid "Tarjeta MercadoPago"
msgstr "Tarjeta MercadoPago"

#. module: payment
#: model:payment.method,name:payment.payment_method_techcom
msgid "Techcombank"
msgstr "Techcombank"

#. module: payment
#: model:payment.method,name:payment.payment_method_tendopay
msgid "TendoPay"
msgstr "TendoPay"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__state__test
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
msgid "Test Mode"
msgstr "Chế độ kiểm thử"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Thank you!"
msgstr "Cảm ơn bạn!"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "The access token is invalid."
msgstr "Token truy cập không hợp lệ."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_capture_wizard.py:0
msgid "The amount to capture must be positive and cannot be superior to %s."
msgstr "Số tiền cần thu hồi phải lớn hơn 0 và không thể vượt quá %s."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__image
#: model:ir.model.fields,help:payment.field_payment_method__image_payment_form
msgid "The base image used for this payment method; in a 64x64 px format."
msgstr ""
"Hình ảnh cơ sở được sử dụng cho phương thức thanh toán này; ở định dạng "
"64x64 px."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__brand_ids
msgid ""
"The brands of the payment methods that will be displayed on the payment "
"form."
msgstr ""
"Thương hiệu của phương thức thanh toán sẽ được hiển thị trên biểu mẫu thanh "
"toán."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__child_transaction_ids
msgid "The child transactions of the transaction."
msgstr "Giao dịch phụ của giao dịch."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__payment_details
msgid "The clear part of the payment method's payment details."
msgstr "Phần rõ ràng về thông tin thanh toán của phương thức thanh toán."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__color
msgid "The color of the card in kanban view"
msgstr "Màu của thẻ trong chế độ xem kanban"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__state_message
msgid "The complementary information message about the state"
msgstr "Thông tin bổ sung về trạng thái"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_country_ids
msgid ""
"The countries in which this payment provider is available. Leave blank to "
"make it available in all countries."
msgstr ""
"Các quốc gia nơi có thể sử dụng nhà cung cấp dịch vụ thanh toán này. Để "
"trống để sử dụng tất cả quốc gia."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__available_currency_ids
msgid ""
"The currencies available with this payment provider. Leave empty not to "
"restrict any."
msgstr ""
"Các loại tiền tệ khả dụng với nhà cung cấp dịch vụ thanh toán này. Để trống "
"không hạn loại tiền tệ."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid "The following fields must be filled: %s"
msgstr "Các trường sau phải được điền: %s"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "The following kwargs are not whitelisted: %s"
msgstr "Các kwargs sau đây không được whitelist: %s"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__reference
msgid "The internal reference of the transaction"
msgstr "Tham chiếu nội bộ của giao dịch"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_country_ids
msgid ""
"The list of countries in which this payment method can be used (if the "
"provider allows it). In other countries, this payment method is not "
"available to customers."
msgstr ""
"Danh sách các quốc gia có thể sử dụng phương thức thanh toán này (nếu nhà "
"cung cấp cho phép). Ở các quốc gia khác, phương thức thanh toán này sẽ không"
" khả dụng cho khách hàng."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__supported_currency_ids
msgid ""
"The list of currencies for that are supported by this payment method (if the"
" provider allows it). When paying with another currency, this payment method"
" is not available to customers."
msgstr ""
"Danh sách các loại tiền tệ mà phương thức thanh toán này hỗ trợ (nếu nhà "
"cung cấp cho phép). Khi thanh toán bằng loại tiền tệ khác, phương thức thanh"
" toán này không khả dụng cho khách hàng."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__provider_ids
msgid "The list of providers supporting this payment method."
msgstr "Danh sách nhà cung cấp hỗ trợ phương thức thanh toán này."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__main_currency_id
msgid "The main currency of the company, used to display monetary fields."
msgstr ""
"Loại tiền tệ chính của công ty, được sử dụng để hiển thị các trường tiền tệ."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__maximum_amount
msgid ""
"The maximum payment amount that this payment provider is available for. "
"Leave blank to make it available for any payment amount."
msgstr ""
"Số tiền thanh toán tối đa khả dụng với nhà cung cấp dịch vụ thanh toán này. "
"Để trống để không giới hạn số tiền thanh toán."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__auth_msg
msgid "The message displayed if payment is authorized"
msgstr "Thông báo hiển thị nếu thanh toán được ủy quyền"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__cancel_msg
msgid ""
"The message displayed if the order is cancelled during the payment process"
msgstr "Thông báo hiển thị nếu đơn hàng bị hủy trong quá trình thanh toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__done_msg
msgid ""
"The message displayed if the order is successfully done after the payment "
"process"
msgstr ""
"Thông báo hiển thị nếu đơn hàng được đặt thành công sau quá trình thanh toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pending_msg
msgid "The message displayed if the order pending after the payment process"
msgstr "Thông báo hiển thị nếu đơn hàng còn treo sau quá trình thanh toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__pre_msg
msgid "The message displayed to explain and help the payment process"
msgstr "Thông báo hiển thị để giải thích và hỗ trợ quá trình thanh toán"

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid ""
"The payment should either be direct, with redirection, or made by a token."
msgstr ""
"Thanh toán phải được thực hiện trực tiếp, có chuyển hướng, hoặc được thực "
"hiện bằng token."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__primary_payment_method_id
msgid ""
"The primary payment method of the current payment method, if the latter is a brand.\n"
"For example, \"Card\" is the primary payment method of the card brand \"VISA\"."
msgstr ""
"Phương thức thanh toán chính của phương thức thanh toán hiện tại, nếu phương thức thanh toán hiện tại là một thương hiệu.\n"
"Ví dụ: \"Thẻ\" là phương thức thanh toán chính của thương hiệu thẻ \"VISA\"."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_token__provider_ref
msgid "The provider reference of the token of the transaction."
msgstr "Tham chiếu nhà cung cấp của token giao dịch."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_reference
msgid "The provider reference of the transaction"
msgstr "Tham chiếu nhà cung cấp của giao dịch"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__image_payment_form
msgid "The resized image displayed on the payment form."
msgstr "Hình ảnh đã thay đổi kích thước hiển thị trên biểu mẫu thanh toán."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__landing_route
msgid "The route the user is redirected to after the transaction"
msgstr "Trang người dùng được chuyển hướng đến sau giao dịch"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "The saving of your payment method has been canceled."
msgstr "Việc lưu phương thức thanh toán của bạn đã bị huỷ."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__source_transaction_id
msgid "The source transaction of the related child transactions"
msgstr "Giao dịch nguồn của các giao dịch phụ liên quan"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__code
#: model:ir.model.fields,help:payment.field_payment_token__payment_method_code
#: model:ir.model.fields,help:payment.field_payment_transaction__payment_method_code
msgid "The technical code of this payment method."
msgstr "Mã kỹ thuật của phương thức thanh toán này."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__code
#: model:ir.model.fields,help:payment.field_payment_token__provider_code
#: model:ir.model.fields,help:payment.field_payment_transaction__provider_code
msgid "The technical code of this payment provider."
msgstr "Mã kỹ thuật của nhà cung cấp dịch vụ thanh toán này."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__redirect_form_view_id
msgid ""
"The template rendering a form submitted to redirect the user when making a "
"payment"
msgstr ""
"Mẫu kết xuất biểu mẫu đã gửi để chuyển hướng người dùng khi thực hiện thanh "
"toán"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__express_checkout_form_view_id
msgid "The template rendering the express payment methods' form."
msgstr "Mẫu kết xuất biểu mẫu của phương thức thanh toán nhanh"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a direct payment"
msgstr "Mẫu kết xuất biểu mẫu thanh toán nội tuyến khi thanh toán trực tiếp"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__token_inline_form_view_id
msgid ""
"The template rendering the inline payment form when making a payment by "
"token."
msgstr "Mẫu kết xuất biểu mẫu thanh toán nội tuyến khi thanh toán bằng token."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s encountered an error "
"(%(provider_name)s)."
msgstr ""
"Giao dịch có mã %(ref)s với số tiền %(amount)s đã gặp lỗi "
"(%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been authorized "
"(%(provider_name)s)."
msgstr ""
"Giao dịch có mã %(ref)s với số tiền %(amount)s đã được uỷ quyền "
"(%(provider_name)s)."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"The transaction with reference %(ref)s for %(amount)s has been confirmed "
"(%(provider_name)s)."
msgstr ""
"Giao dịch có mã %(ref)s với số tiền %(amount)s đã được xác nhận "
"(%(provider_name)s)."

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_transaction
msgid "There are no transactions to show"
msgstr "Không có giao dịch để hiển thị"

#. module: payment
#: model_terms:ir.actions.act_window,help:payment.action_payment_token
msgid "There is no token created yet."
msgstr "Chưa có token nào được tạo."

#. module: payment
#. odoo-python
#: code:addons/payment/wizards/payment_link_wizard.py:0
msgid "There is nothing to be paid."
msgstr "Không có gì cần thanh toán. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.pay
msgid "There is nothing to pay."
msgstr "Không có khoản thanh toán nào. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid ""
"This action will also archive %s tokens that are registered with this "
"payment method."
msgstr ""
"Tác vụ này cũng sẽ lưu trữ %s token đã được đăng ký theo phương thức thanh "
"toán này. "

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"This action will also archive %s tokens that are registered with this "
"provider. "
msgstr ""
"Tác vụ này cũng sẽ lưu trữ %s token đã được đăng ký với nhà cung cấp này."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_tokenization
msgid ""
"This controls whether customers can save their payment methods as payment tokens.\n"
"A payment token is an anonymous link to the payment method details saved in the\n"
"provider's database, allowing the customer to reuse it for a next purchase."
msgstr ""
"Điều này kiểm soát việc khách hàng có thể lưu phương thức thanh toán của họ dưới dạng token thanh toán hay không.\n"
"Token thanh toán là một liên kết ẩn danh đến thông tin phương thức thanh toán được lưu trong\n"
"cơ sở dữ liệu của nhà cung cấp, cho phép khách hàng sử dụng lại cho lần mua hàng tiếp theo."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__allow_express_checkout
msgid ""
"This controls whether customers can use express payment methods. Express "
"checkout enables customers to pay with Google Pay and Apple Pay from which "
"address information is collected at payment."
msgstr ""
"Điều này kiểm soát việc khách hàng có thể sử dụng phương thức thanh toán "
"nhanh hay không. Thanh toán nhanh cho phép khách hàng thanh toán bằng Google"
" Pay và Apple Pay từ đó thông tin địa chỉ được thu thập khi thanh toán."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_link_wizard_view_form
msgid ""
"This partner has no email, which may cause issues with some payment providers.\n"
"                     Setting an email for this partner is advised."
msgstr ""
"Đối tác không có email, việc này có thể dẫn đến trục trặc với một số nhà cung cấp dịch vụ thanh toán. \n"
"                     Bạn nên điền email cho đối tác này."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
msgid ""
"This payment method needs a partner in crime; you should enable a payment "
"provider supporting this method first."
msgstr ""
"Phương thức thanh toán này cần có nhà cung cấp; trước tiên bạn cần kích hoạt"
" nhà cung cấp dịch vụ thanh toán hỗ trợ phương thức này."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"This transaction has been confirmed following the processing of its partial "
"capture and partial void transactions (%(provider)s)."
msgstr ""
"Giao dịch này đã được xác nhận sau khi xử lý các giao dịch thu hồi một phần "
"và vô hiệu một phần (%(provider)s)."

#. module: payment
#: model:payment.method,name:payment.payment_method_tienphong
msgid "Tienphong"
msgstr "Tienphong"

#. module: payment
#: model:payment.method,name:payment.payment_method_tinka
msgid "Tinka"
msgstr "Tinka"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_provider__token_inline_form_view_id
msgid "Token Inline Form Template"
msgstr "Mẫu biểu mẫu nội tuyến token"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_method__support_tokenization
#: model:ir.model.fields,field_description:payment.field_payment_provider__support_tokenization
msgid "Tokenization"
msgstr "Token hoá"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_method__support_tokenization
msgid ""
"Tokenization is the process of saving the payment details as a token that "
"can later be reused without having to enter the payment details again."
msgstr ""
"Token hoá là quá trình lưu thông tin thanh toán dưới dạng token mà sau này "
"bạn có thể sử dụng lại mà không cần phải nhập lại thông tin thanh toán."

#. module: payment
#: model:payment.method,name:payment.payment_method_toss_pay
msgid "Toss Pay"
msgstr "Toss Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_touch_n_go
msgid "Touch'n Go"
msgstr "Touch'n Go"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__transaction_ids
msgid "Transaction"
msgstr "Giao dịch"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_transaction.py:0
msgid ""
"Transaction authorization is not supported by the following payment "
"providers: %s"
msgstr ""
"Các nhà cung cấp dịch vụ thanh toán sau không hỗ trợ ủy quyền giao dịch: %s"

#. module: payment
#: model:payment.method,name:payment.payment_method_truemoney
msgid "TrueMoney"
msgstr "TrueMoney"

#. module: payment
#: model:payment.method,name:payment.payment_method_trustly
msgid "Trustly"
msgstr "Trustly"

#. module: payment
#: model:payment.method,name:payment.payment_method_twint
msgid "Twint"
msgstr "Twint"

#. module: payment
#: model:payment.method,name:payment.payment_method_upi
msgid "UPI"
msgstr "UPI"

#. module: payment
#: model:payment.method,name:payment.payment_method_ussd
msgid "USSD"
msgstr "USSD"

#. module: payment
#: model:payment.method,name:payment.payment_method_unionpay
msgid "UnionPay"
msgstr "UnionPay"

#. module: payment
#: model:payment.method,name:payment.payment_method_uob
msgid "United Overseas Bank"
msgstr "United Overseas Bank"

#. module: payment
#: model:payment.method,name:payment.payment_method_uatp
msgid "Universal Air Travel Plan"
msgstr "Universal Air Travel Plan"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Unpublished"
msgstr "Đã huỷ hiển thị"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_method__support_refund__none
#: model:ir.model.fields.selection,name:payment.selection__payment_provider__support_refund__none
msgid "Unsupported"
msgstr "Không hỗ trợ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_form
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
msgid "Upgrade"
msgstr "Nâng cấp"

#. module: payment
#: model:payment.method,name:payment.payment_method_vpay
msgid "V PAY"
msgstr "V PAY"

#. module: payment
#: model:payment.method,name:payment.payment_method_visa
msgid "VISA"
msgstr "VISA"

#. module: payment
#: model:ir.model.fields.selection,name:payment.selection__payment_transaction__operation__validation
msgid "Validation of the payment method"
msgstr "Xác thực phương thức thanh toán"

#. module: payment
#: model:payment.method,name:payment.payment_method_venmo
msgid "Venmo"
msgstr "Venmo"

#. module: payment
#: model:payment.method,name:payment.payment_method_vietcom
msgid "Vietcombank"
msgstr "Vietcombank"

#. module: payment
#: model:payment.method,name:payment.payment_method_vipps
msgid "Vipps"
msgstr "Vipps"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_capture_wizard__void_remaining_amount
msgid "Void Remaining Amount"
msgstr "Vô hiệu số tiền còn lại"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "Void Transaction"
msgstr "Giao dịch trống"

#. module: payment
#: model:payment.method,name:payment.payment_method_wallets_india
msgid "Wallets India"
msgstr "Ví điện tử Ấn Độ"

#. module: payment
#: model:payment.method,name:payment.payment_method_walley
msgid "Walley"
msgstr "Walley"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_method.py:0
#: code:addons/payment/models/payment_provider.py:0
msgid "Warning"
msgstr "Cảnh báo"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_link_wizard__warning_message
msgid "Warning Message"
msgstr "Thông báo cảnh báo"

#. module: payment
#. odoo-javascript
#: code:addons/payment/static/src/js/payment_form.js:0
msgid "Warning!"
msgstr "Cảnh báo!"

#. module: payment
#: model:payment.method,name:payment.payment_method_wechat_pay
msgid "WeChat Pay"
msgstr "WeChat Pay"

#. module: payment
#: model:payment.method,name:payment.payment_method_welend
msgid "WeLend"
msgstr "WeLend"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_transaction__tokenize
msgid ""
"Whether a payment token should be created when post-processing the "
"transaction"
msgstr ""
"Có nên tạo token thanh toán trong giai đoạn xử lý sau giao dịch hay không"

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_capture_wizard__support_partial_capture
msgid ""
"Whether each of the transactions' provider supports the partial capture."
msgstr "Liệu mỗi nhà cung cấp giao dịch có hỗ trợ thu hồi một phần hay không."

#. module: payment
#: model:ir.model.fields,help:payment.field_payment_provider__is_published
msgid ""
"Whether the provider is visible on the website or not. Tokens remain "
"functional but are only visible on manage forms."
msgstr ""
"Liệu nhà cung cấp có hiển thị trên trang web hay không. Token vẫn hoạt động "
"nhưng chỉ hiển thị trên biểu mẫu quản lý."

#. module: payment
#: model:payment.provider,name:payment.payment_provider_transfer
msgid "Wire Transfer"
msgstr "Chuyển khoản ngân hàng"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_worldline
msgid "Worldline"
msgstr "Worldline"

#. module: payment
#: model:payment.provider,name:payment.payment_provider_xendit
msgid "Xendit"
msgstr "Xendit"

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_token.py:0
msgid ""
"You can't unarchive tokens linked to inactive payment methods or disabled "
"providers."
msgstr ""
"Bạn không thể hủy lưu trữ các token liên kết với phương thức thanh toán "
"không hoạt động hoặc nhà cung cấp bị vô hiệu hóa."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"You cannot change the company of a payment provider with existing "
"transactions."
msgstr ""
"Bạn không thể thay đổi công ty của một nhà cung cấp dịch vụ thanh toán với "
"các giao dịch hiện có."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid ""
"You cannot delete the payment provider %s; disable it or uninstall it "
"instead."
msgstr ""
"Bạn không thể xóa nhà cung cấp dịch vụ thanh toán %s; thay vào đó hãy vô "
"hiệu hoá hoặc gỡ cài đặt nhà cung cấp này."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
msgid "You cannot publish a disabled provider."
msgstr "Bạn không thể hiển thị một nhà cung cấp đã bị vô hiệu hóa."

#. module: payment
#. odoo-python
#: code:addons/payment/controllers/portal.py:0
msgid "You do not have access to this payment token."
msgstr "Bạn không có quyền truy cập token thanh toán này. "

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid ""
"You should receive an email confirming your payment within a few\n"
"                                    minutes."
msgstr ""
"Bạn sẽ nhận được email xác nhận khoản thanh toán của bạn trong vài\n"
"                                      phút nữa."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,auth_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,auth_msg:payment.payment_provider_aps
#: model_terms:payment.provider,auth_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,auth_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_demo
#: model_terms:payment.provider,auth_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,auth_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,auth_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,auth_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,auth_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,auth_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,auth_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,auth_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,auth_msg:payment.payment_provider_xendit
msgid "Your payment has been authorized."
msgstr "Thanh toán của bạn đã được chứng thực."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_aps
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_demo
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,cancel_msg:payment.payment_provider_xendit
msgid "Your payment has been cancelled."
msgstr "Thanh toán của bạn đã bị hủy."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,pending_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,pending_msg:payment.payment_provider_aps
#: model_terms:payment.provider,pending_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,pending_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_demo
#: model_terms:payment.provider,pending_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,pending_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,pending_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,pending_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,pending_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,pending_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,pending_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,pending_msg:payment.payment_provider_xendit
msgid ""
"Your payment has been successfully processed but is waiting for approval."
msgstr "Thanh toán của bạn đã được xử lý thành công nhưng đang chờ phê duyệt."

#. module: payment
#. odoo-python
#: code:addons/payment/models/payment_provider.py:0
#: model_terms:payment.provider,done_msg:payment.payment_provider_adyen
#: model_terms:payment.provider,done_msg:payment.payment_provider_aps
#: model_terms:payment.provider,done_msg:payment.payment_provider_asiapay
#: model_terms:payment.provider,done_msg:payment.payment_provider_authorize
#: model_terms:payment.provider,done_msg:payment.payment_provider_buckaroo
#: model_terms:payment.provider,done_msg:payment.payment_provider_demo
#: model_terms:payment.provider,done_msg:payment.payment_provider_flutterwave
#: model_terms:payment.provider,done_msg:payment.payment_provider_mercado_pago
#: model_terms:payment.provider,done_msg:payment.payment_provider_mollie
#: model_terms:payment.provider,done_msg:payment.payment_provider_paypal
#: model_terms:payment.provider,done_msg:payment.payment_provider_razorpay
#: model_terms:payment.provider,done_msg:payment.payment_provider_sepa_direct_debit
#: model_terms:payment.provider,done_msg:payment.payment_provider_stripe
#: model_terms:payment.provider,done_msg:payment.payment_provider_transfer
#: model_terms:payment.provider,done_msg:payment.payment_provider_worldline
#: model_terms:payment.provider,done_msg:payment.payment_provider_xendit
msgid "Your payment has been successfully processed."
msgstr "Thanh toán của bạn đã được xử lý thành công."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Your payment has not been processed yet."
msgstr "Thanh toán của bạn chưa được xử lý."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_status
msgid "Your payment is on its way!"
msgstr "Thanh toán của bạn đang được xử lý!"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.state_header
msgid "Your payment method has been saved."
msgstr "Phương thức thanh toán của bạn đã được lưu."

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.form
msgid "Your payment methods"
msgstr "Phương thức thanh toán của bạn"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_transaction_form
msgid "ZIP"
msgstr "Mã bưu chính"

#. module: payment
#: model:payment.method,name:payment.payment_method_zalopay
msgid "Zalopay"
msgstr "Zalopay"

#. module: payment
#: model:ir.model.fields,field_description:payment.field_payment_transaction__partner_zip
#: model:payment.method,name:payment.payment_method_zip
msgid "Zip"
msgstr "Mã bưu chính"

#. module: payment
#: model:payment.method,name:payment.payment_method_cofidis
msgid "cofidis"
msgstr "cofidis"

#. module: payment
#: model:payment.method,name:payment.payment_method_enets
msgid "eNETS"
msgstr "eNETS"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "express checkout not supported"
msgstr "không hỗ trợ thanh toán nhanh"

#. module: payment
#: model:payment.method,name:payment.payment_method_ideal
msgid "iDEAL"
msgstr "iDEAL"

#. module: payment
#: model:payment.method,name:payment.payment_method_in3
msgid "in3"
msgstr "in3"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible country"
msgstr "quốc gia không tương thích"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible currency"
msgstr "tiền tệ không tương thích"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "incompatible website"
msgstr "trang web không tương thích"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "manual capture not supported"
msgstr "không hỗ trợ thu hồi thủ công"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "maximum amount exceeded"
msgstr "đã vượt quá số tiền tối đa"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "no supported provider available"
msgstr "không có nhà cung cấp nào được hỗ trợ"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "payment method"
msgstr "phương thức thanh toán"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_kanban
#: model_terms:ir.ui.view,arch_db:payment.payment_provider_search
msgid "provider"
msgstr "nhà cung cấp"

#. module: payment
#: model_terms:ir.ui.view,arch_db:payment.company_mismatch_warning
msgid ""
"to make this\n"
"                    payment."
msgstr ""
"để tiến hành\n"
"                    thanh toán."

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "tokenization not supported"
msgstr "không hỗ trợ token hoá"

#. module: payment
#. odoo-python
#: code:addons/payment/const.py:0
msgid "tokenization without payment no supported"
msgstr "không hỗ trợ token hoá không có thanh toán"
