# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_accountant
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <mi<PERSON>.<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Pek<PERSON>, 2024
# Retropikzel, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <miu<PERSON><EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# a0002ef8927c0b0b9c58a7cc5f73028e_ba3d803 <aeb43a6499fcef2a5a423b3f4a45b153_32479>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# Konsta Aavaranta, 2024
# Martin Trigaux, 2024
# Joakim Weckman, 2024
# <AUTHOR> <EMAIL>, 2025
# <AUTHOR> <EMAIL>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be fully "
"reconciled by the transaction."
msgstr ""
"%(display_name_html)s, jonka avoin määrä on %(open_amount)s, täsmäytetään "
"kokonaan tapahtumalla."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"%(display_name_html)s with an open amount of %(open_amount)s will be reduced"
" by %(amount)s."
msgstr ""
"%(display_name_html)s, jonka avoin määrä on %(open_amount)s, vähennetään "
"%(amount)s."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_form
msgid "-> Reconcile"
msgstr "-> Täsmäytä"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid "<b class=\"tip_title\">Tip: Bulk update journal items</b>"
msgstr "<b class=“tip_title”>Vinkki: Massapäivitä päiväkirjarivit</b>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"<b class=\"tip_title\">Tip: Find an Accountant or register your Accounting "
"Firm</b>"
msgstr ""
"<b class=\"tip_title\">Vinkki: Etsi kirjanpitäjä tai rekisteröi "
"tilitoimisto</b>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_reconcile_model_form_inherit_account_accountant
msgid ""
"<i title=\"Run manually\" role=\"img\" aria-label=\"Run manually\" class=\"fa fa-refresh\"/>\n"
"                            Run manually"
msgstr ""
"<i title=\"Suorita käsin\" role=\"img\" aria-label=\"Suorita käsin\" class=\"fa fa-refresh\"/>\n"
"                            Suorita manuaalisesti"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<i>Lock transactions up to specific dates, inclusive</i>"
msgstr "<i>Lukitse tapahtumat tiettyihin päivämääriin asti, mukaan lukien</i>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">1 Bank Transaction</span>"
msgstr "<span class=\"o_stat_text\">1 pankkitapahtuma</span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "<span class=\"o_stat_text\">Bank Statement</span>"
msgstr "<span class=\"o_stat_text\">Pankin tiliote</span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"<span class=\"oe_inline o_form_label mx-3\" "
"invisible=\"single_currency_mode\"> in </span>"
msgstr ""
"<span class=\"oe_inline o_form_label mx-3\" "
"invisible=\"single_currency_mode\"> kohteessa </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-danger o_form_label\" invisible=\"hard_lock_date == current_hard_lock_date\">\n"
"                                        <i>This change is irreversible</i>\n"
"                                    </span>"
msgstr ""

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_fiscalyear_lock_date_exception_for_me_id                                                  or not min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_fiscalyear_lock_date_exception_for_me_id                                                  or not min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_purchase_lock_date_exception_for_me_id                                                  or not min_purchase_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_purchase_lock_date_exception_for_me_id                                                  or not min_purchase_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_sale_lock_date_exception_for_me_id                                                  or not min_sale_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_sale_lock_date_exception_for_me_id                                                  or not min_sale_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_tax_lock_date_exception_for_me_id                                                  or not min_tax_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted o_form_label\" invisible=\"not min_tax_lock_date_exception_for_me_id                                                  or not min_tax_lock_date_exception_for_everyone_id\">\n"
"                                      <span style=\"white-space: pre\">; </span>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"hard_lock_date != current_hard_lock_date\">\n"
"                                    <i>to ensure inalterability</i>\n"
"                                </span>\n"
"                                <span class=\"text-danger o_form_label\" invisible=\"hard_lock_date == current_hard_lock_date\">\n"
"                                    <i>This change is irreversible</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"hard_lock_date != current_hard_lock_date\">\n"
"                                    <i>muuttumattomuuden varmistamiseksi</i>\n"
"                                </span>\n"
"                                <span class=\"text-danger o_form_label\" invisible=\"hard_lock_date == current_hard_lock_date\">\n"
"                                    <i>Tätä muutosta ei voi peruuttaa</i>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"min_fiscalyear_lock_date_exception_for_me_id or min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                    <i>but allow exceptions</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"min_fiscalyear_lock_date_exception_for_me_id or min_fiscalyear_lock_date_exception_for_everyone_id\">\n"
"                                    <i>mutta poikkeukset ovat sallittuja</i>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"<span class=\"text-muted\" invisible=\"min_tax_lock_date_exception_for_me_id or min_tax_lock_date_exception_for_everyone_id\">\n"
"                                    <i>after a tax closing</i>\n"
"                                </span>"
msgstr ""
"<span class=\"text-muted\" invisible=\"min_tax_lock_date_exception_for_me_id or min_tax_lock_date_exception_for_everyone_id\">\n"
"                                    <i>verotuksen päättymisen jälkeen</i>\n"
"                                </span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Find an Accountant</span>"
msgstr "<span class=\"tip_button_text\">Etsi kirjanpitäjä</span>"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid "<span class=\"tip_button_text\">Register your Accounting Firm</span>"
msgstr "<span class=\"tip_button_text\">Rekisteröi tilitoimistosi</span>"

#. module: account_accountant
#: model_terms:web_tour.tour,rainbow_man_message:account_accountant.account_accountant_tour
msgid ""
"<span><strong><b>Good job!</b> You went through all steps of this tour.</strong>\n"
"            <br>See how to manage your customer invoices in the <b>Customers/Invoices</b> menu\n"
"        </span>"
msgstr ""
"<span><strong><b>Hyvää työtä!</b> Kävit läpi kaikki tämän kierroksen vaiheet.</strong>\n"
"           <br>Katso, miten voit hallita asiakaslaskuja <b>Asiakkaat/Laskut-valikossa</b> \n"
"        </span>"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "<span>Exception</span>"
msgstr "<span>Poikkeus</span>"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_account_account
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__account_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__account_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Account"
msgstr "Tili"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_chart_template
msgid "Account Chart Template"
msgstr "Tilikarttamalli"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_account_group_tree
#: model:ir.ui.menu,name:account_accountant.menu_account_group
msgid "Account Groups"
msgstr "Tiliryhmät"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.account_tag_action
#: model:ir.ui.menu,name:account_accountant.account_tag_menu
msgid "Account Tags"
msgstr "Tilin tunnisteet"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_from_account_id
msgid "Account Transfer From"
msgstr "Tilisiirto tililtä"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_auto_reconcile_wizard
msgid "Account automatic reconciliation wizard"
msgstr "Ohjattu tilien automaattinen täsmäytys"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_wizard
msgid "Account reconciliation wizard"
msgstr "Ohjattu tilien täsmäytys"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Account used for deferred expenses"
msgstr "Ostojen jaksotuksia varten käytetty tili"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Account used for deferred revenues"
msgstr "Myynnin jaksotuksia varten käytetty tili"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__account_ids
msgid "Accounts"
msgstr "Tilit"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_needaction
msgid "Action Needed"
msgstr "Vaatii toimenpiteitä"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_quick_create.xml:0
msgid "Add & Close"
msgstr "Lisää ja sulje"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_quick_create.xml:0
msgid "Add & New"
msgstr "Lisää ja uusi"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.account_tag_action
msgid "Add a new tag"
msgstr "Lisää uusi tunniste"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"After the data extraction, check and validate the bill. If no vendor has "
"been found, add one before validating."
msgstr ""
"Tarkista ja validoi lasku tietojen poimimisen jälkeen. Jos myyjää ei ole "
"löytynyt, lisää se ennen vahvistusta."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
msgid "All Transactions"
msgstr "Kaikki tapahtumat"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_autocomplete_ids
msgid "All reconciliation models"
msgstr "Kaikki täsmäytysmallit"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__allow_partials
msgid "Allow partials"
msgstr "Salli osittaiset"

#. module: account_accountant
#: model:res.groups,name:account_accountant.group_fiscal_year
msgid "Allow to define fiscal years of more or less than a year"
msgstr "Salli tilikauden pituudeksi muu kuin kalenterivuosi"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount_currency
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Amount"
msgstr "Arvo"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Amount (Company Currency)"
msgstr "Määrä (yrityksen valuutta)"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Amount (Foreign Currency)"
msgstr "Määrä (ulkomaan valuutassa)"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_currency
msgid "Amount Currency"
msgstr "Valuuttamäärä"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Amount Due"
msgstr "Erääntynyt summa"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Amount Due (in currency)"
msgstr "Erääntyvä määrä (valuuttana)"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__amount_transaction_currency
msgid "Amount in Currency"
msgstr "Valuuttamäärä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__amount
msgid "Amount in company currency"
msgstr "Määrä yrityksen valuutassa"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"An entry will transfer %(amount)s from %(from_account)s to %(to_account)s."
msgstr ""
"Merkintä siirtää %(amount)s kohteesta %(from_account)s kohteeseen "
"%(to_account)s."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
msgid "Analytic"
msgstr "Analyyttinen"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analyyttinen jakelu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__analytic_precision
msgid "Analytic Precision"
msgstr "Analyyttinen tarkkuus"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__use_anglo_saxon
msgid "Anglo-Saxon Accounting"
msgstr "Anglosaksinen kirjanpito"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__current_hard_lock_date
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__hard_lock_date
msgid ""
"Any entry up to and including that date will be postponed to a later time, "
"in accordance with its journal sequence. This lock date is irreversible and "
"does not allow any exception."
msgstr ""
"Kaikki kirjaukset kyseiseen päivämäärään asti siirretään myöhempään "
"ajankohtaan päiväkirjasekvenssin mukaisesti. Tämä lukituspäivä on "
"peruuttamaton, eikä siitä voida poiketa."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid ""
"Any entry up to and including that date will be postponed to a later time, "
"in accordance with its journal's sequence."
msgstr ""
"Kaikki kyseiseen päivämäärään asti tehdyt merkinnät siirretään myöhempään "
"ajankohtaan päiväkirjan järjestyksen mukaisesti."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__tax_lock_date
msgid ""
"Any entry with taxes up to and including that date will be postponed to a "
"later time, in accordance with its journal's sequence. The tax lock date is "
"automatically set when the tax closing entry is posted."
msgstr ""
"Kaikki kirjaukset, joissa on veroja kyseiseen päivämäärään asti, siirretään "
"myöhempään ajankohtaan päiväkirjan järjestyksen mukaisesti. Verojen "
"lukituspäivämäärä asetetaan automaattisesti, kun verojen päättämiskirjaus "
"kirjataan."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__purchase_lock_date
msgid ""
"Any purchase entry prior to and including this date will be postponed to a "
"later date, in accordance with its journal's sequence."
msgstr ""
"Kaikki ennen tätä päivämäärää tehdyt ostokirjaukset siirretään myöhempään "
"päivämäärään päiväkirjan järjestyksen mukaisesti."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_change_lock_date__sale_lock_date
msgid ""
"Any sales entry prior to and including this date will be postponed to a "
"later date, in accordance with its journal's sequence."
msgstr ""
"Kaikki tätä päivää edeltävät myyntimerkinnät siirretään myöhempään "
"ajankohtaan kirjanpitopäivän järjestyksen mukaisesti."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_attachment_count
msgid "Attachment Count"
msgstr "Liitteiden määrä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__sign_invoice
msgid "Authorized Signatory on invoice"
msgstr "Laskun valtuutettu allekirjoittaja"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.report_invoice_document
msgid "Authorized signatory"
msgstr "Valtuutettu allekirjoittaja"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list_reconcile/move_line_list_reconcile.xml:0
msgid "Auto-reconcile"
msgstr "Automaattinen uudelleentäsmäys"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_auto_reconcile_wizard.py:0
msgid "Automatically Reconciled Entries"
msgstr "Automaattisesti täsmäytetyt kirjaukset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__available_reco_model_ids
msgid "Available Reco Model"
msgstr "Saatavilla oleva Reco-malli"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/finish_buttons.xml:0
msgid "Back to"
msgstr "Takaisin"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/global_info.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__balance
msgid "Balance"
msgstr "Saldo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash
msgid "Bank & Cash Moves"
msgstr "Tili- ja käteistapahtumat"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__bank_account
msgid "Bank Account"
msgstr "Tilinumero"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_transactions
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_transactions_kanban
msgid "Bank Reconciliation"
msgstr "Pankkitapahtumien täsmäytys"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_form_bank_rec_widget
msgid "Bank Statement"
msgstr "Pankin tiliote"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid "Bank Statement %s.pdf"
msgstr "Pankkitiliote %s.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Pankkitiliotteen rivi"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid "Bank Statement.pdf"
msgstr "Pankkitiliote.pdf"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget
msgid "Bank reconciliation widget for a single statement line"
msgstr "Pankkien täsmäytyksen widget yhdelle tiliöintiriville"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Based on"
msgstr "Perustuen"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Cancel"
msgstr "Peruuta"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Vaihda lukituspäivä"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_reconcile_wizard__to_check
msgid ""
"Check if you are not certain of all the information of the counterpart."
msgstr "Tarkista, jos et ole varma vastapuolen kaikista tiedoista."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_checked
msgid "Checked"
msgstr "Valittu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "Choose a line to preview its attachments."
msgstr "Valitse rivi nähdäksesi sen liitteet."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__zero_balance
msgid "Clear Account"
msgstr "Tyhjennä tili"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.actions_account_fiscal_year
msgid "Click here to create a new fiscal year."
msgstr "Luo uusi tilikausi napsauttamalla tätä."

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_1
msgid ""
"Click here to find an accountant or if you want to list out your accounting "
"services on Odoo"
msgstr ""
"Klikkaa tästä löytääksesi kirjanpitäjän tai jos haluat listata "
"kirjanpitopalvelusi Odoossa"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"Click on a fetched bank transaction to start the reconciliation process."
msgstr "Aloita täsmäytysprosessi napsauttamalla haettua pankkitapahtumaa."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_company
msgid "Companies"
msgstr "Yritykset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__company_id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_id
msgid "Company"
msgstr "Yritys"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__company_currency_id
msgid "Company currency"
msgstr "Yrityksen valuutta"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Confirm the transaction."
msgstr "Vahvista tapahtuma."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "Congrats, you're all done!"
msgstr "Onnittelut, kaikki valmista!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Connect your bank and get your latest transactions."
msgstr "Yhdistä pankkisi ja hae viimeiset tapahtumat."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Counterpart Values"
msgstr "Vastapuolen arvot"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__country_code
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__country_code
msgid "Country Code"
msgstr "Maatunnus"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_form_bank_rec_widget
msgid "Create Statement"
msgstr "Luo tiliote"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_account_group_tree
msgid "Create a new account group"
msgstr "Luo uusi tiliryhmä"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Create a new transaction."
msgstr "Luo uusi tapahtuma."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Create model"
msgstr "Luo malli"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid ""
"Create your first vendor bill.<br/><br/><i>Tip: If you don’t have one on "
"hand, use our sample bill.</i>"
msgstr ""
"Luo ensimmäinen toimittajalasku.<br/><br/><i>Vinkki: Jos sinulla ei ole "
"laskuja käsillä, käytä esimerkkilaskua.</i>"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__create_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__create_date
msgid "Created on"
msgstr "Luotu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__credit
msgid "Credit"
msgstr "Kredit"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__cron_last_check
msgid "Cron Last Check"
msgstr "Cron-eräajon viimeinen tarkistus"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model,name:account_accountant.model_res_currency
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__currency_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Currency"
msgstr "Valuutta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_currency_id
msgid "Currency to use for reconciliation"
msgstr "Täsmäytyksessä käytettävä valuutta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__current_hard_lock_date
msgid "Current Hard Lock"
msgstr "Nykyinen Hard Lock"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Customer/Vendor"
msgstr "Asiakas/Toimittaja"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__date
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Date"
msgstr "Päivämäärä"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_end_date
msgid "Date at which the deferred expense/revenue ends"
msgstr "Päivä, jolloin jaksotettu osto/myynti päättyy"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_move_line__deferred_start_date
msgid "Date at which the deferred expense/revenue starts"
msgstr "Päivä, jolloin jaksotettu osto/myynti alkaa"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__day
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__day
msgid "Days"
msgstr "Päivää"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__debit
msgid "Debit"
msgstr "Debit"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__journal_default_account_id
msgid "Default Account"
msgstr "Oletustili"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Deferral of %s"
msgstr "Lykkäys %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_move_ids
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Deferred Entries"
msgstr "Jaksotuskirjaukset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_entry_type
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_entry_type
msgid "Deferred Entry Type"
msgstr "Jaksotuskirjauksen tyyppi"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__expense
msgid "Deferred Expense"
msgstr "Ostojen jaksotukset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_account_id
msgid "Deferred Expense Account"
msgstr "Laskennallinen kulutili"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_amount_computation_method
msgid "Deferred Expense Based on"
msgstr "Laskennallinen meno perustuu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_expense_journal_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_expense_journal_id
msgid "Deferred Expense Journal"
msgstr "Laskennalliset menot -päiväkirja"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_move__deferred_entry_type__revenue
msgid "Deferred Revenue"
msgstr "Myynnin jaksotukset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_account_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_account_id
msgid "Deferred Revenue Account"
msgstr "Tuloennakon tili"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_amount_computation_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_amount_computation_method
msgid "Deferred Revenue Based on"
msgstr "Laskennalliset tulot perustuvat"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__deferred_revenue_journal_id
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__deferred_revenue_journal_id
msgid "Deferred Revenue Journal"
msgstr "Laskennalliset tulot -päiväkirja"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred expense"
msgstr "Laskennallinen kulu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred expense entries:"
msgstr ""
"Menetelmä, jota käytetään laskennallisten kulukirjausten tuottamiseen:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred revenue"
msgstr "Tuloennakko"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Deferred revenue entries:"
msgstr ""
"Menetelmä, jota käytetään laskennallisten tulojen kirjausten tuottamiseen:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Define fiscal years of more or less than one year"
msgstr "Määritä tilikausi pienemmäksi tai suuremmaksi kuin yksi vuosi"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Deposits"
msgstr "Talletukset"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_digest_digest
msgid "Digest"
msgstr "Yhteenveto"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Discard"
msgstr "Hylkää"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Amount"
msgstr "Alennuksen summa"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Discount Date"
msgstr "Alennuspäivä"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Discuss"
msgstr "Viestintä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_allow_partials
msgid "Display Allow Partials"
msgstr "Näytä Salli osittaiset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__display_name
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__display_name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_amount_currency
msgid "Display Stroked Amount Currency"
msgstr "Näyttö yliviivattu valuutan määrä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__display_stroked_balance
msgid "Display Stroked Balance"
msgstr "Näytä yliviivattu saldo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__sign_invoice
msgid "Display signing field on invoices"
msgstr "Näytä allekirjoituskenttä laskuissa"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Jakelun analyyttinen tili"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Sinulla ei ole oikeutta tähän toimintoon. Et voi käyttää tätä tietoa "
"käyttäjien  yhteenvetopostissa"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Document"
msgstr "Dokumentti"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "Draft Entries"
msgstr "Luonnoskirjaukset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode
msgid "Edit Mode"
msgstr "Muokkaustila"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_amount
msgid "Edit Mode Amount"
msgstr "Muokkaustila Määrä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_reco_currency_id
msgid "Edit Mode Reco Currency"
msgstr "Muokkaustila Reco Valuutta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__edit_mode_amount_currency
msgid "Edit mode amount"
msgstr "Muokkaustilan määrä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_to
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_end_date
msgid "End Date"
msgstr "Päättymispäivä"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_to
msgid "Ending Date, included in the fiscal year."
msgstr "Lopetuspäivä, joka sisältyy tilivuoteen."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid ""
"Every payment and invoice before this date will receive the 'From Invoicing'"
" status, hiding all the accounting entries related to it. Use this option "
"after installing Accounting if you were using only Invoicing before, before "
"importing all your actual accounting data in to Odoo."
msgstr ""
"Jokainen tätä päivämäärää edeltävä maksu ja lasku saa 'Invoicing App Legacy'"
" -tilan piilottaen kaikki niihin liittyvät kirjanpitokirjaukset. Käytä tätä "
"vaihtoehtoa kirjanpidon asentamisen jälkeen, jos käytit aiemmin vain Odoon "
"Laskutus-moduulia, ennen kuin tuot kaikki varsinaiset kirjanpitotietosi "
"Odoohon."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_duration
msgid "Exception Duration"
msgstr "Poikkeuksen kesto"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_needed_fields
msgid "Exception Needed Fields"
msgstr "Poikkeus Tarvittavat kentät"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_reason
msgid "Exception Reason"
msgstr "Poikkeuksen syy"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_applies_to
msgid "Exception applies"
msgstr "Sovelletaan poikkeusta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__exception_needed
msgid "Exception needed"
msgstr "Tarvittava poikkeus"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Exchange Difference: %s"
msgstr "Vaihtoero: %s"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Year"
msgstr "Tilikausi"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.action_account_fiscal_year_form
msgid "Fiscal Year 2018"
msgstr "Tilikausi 2018"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.actions_account_fiscal_year
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__group_fiscal_year
#: model:ir.ui.menu,name:account_accountant.menu_account_fiscal_year
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Fiscal Years"
msgstr "Tilikaudet"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_day
msgid "Fiscalyear Last Day"
msgstr "Tilikauden viimeinen päivä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__fiscalyear_last_month
msgid "Fiscalyear Last Month"
msgstr "Tilikauden viimeinen kuukausi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__flag
msgid "Flag"
msgstr "Lippu"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_follower_ids
msgid "Followers"
msgstr "Seuraajat"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seuraajat (kumppanit)"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "For everyone:"
msgstr "Kaikille:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "For me:"
msgstr "Minulle:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__force_partials
msgid "Force Partials"
msgstr "Pakota osittaiset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__force_price_included_taxes
msgid "Force Price Included Taxes"
msgstr "Pakota hinta sisältäen verot"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__transaction_currency_id
msgid "Foreign Currency"
msgstr "Ulkomaan valuutta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__form_index
msgid "Form Index"
msgstr "Lomakkeen Indeksi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__from_date
msgid "From"
msgstr "Alkaa"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Payable accounts"
msgstr "Ostovelkojen tileiltä"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "From Trade Receivable accounts"
msgstr "Myyntisaamisista"

#. module: account_accountant
#: model_terms:digest.tip,tip_description:account_accountant.digest_tip_account_accountant_0
msgid ""
"From any list view, select multiple records and the list becomes editable. "
"If you update a cell, selected records are updated all at once. Use this "
"feature to update multiple journal entries from the General Ledger, or any "
"Journal view."
msgstr ""
"Jos valitset mistä tahansa listanäkymästä useita tietueita, listasta tulee "
"muokattava. Jos päivität solun, valitut tietueet päivitetään kerralla. Käytä"
" tätä ominaisuutta päivittääksesi useita päiväkirjakirjauksia pääkirjasta "
"tai mistä tahansa päiväkirjanäkymästä."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__full_months
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__full_months
msgid "Full Months"
msgstr "Täydet kuukaudet"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_expense_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
msgid "Generate Deferred Expense Entries"
msgstr "Luo laskennallisia kulukirjauksia -menetelmä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__generate_deferred_revenue_entries_method
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Generate Deferred Revenue Entries"
msgstr "Laskennallisen tulon kirjausten luonti -menetelmä"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Generate Entries"
msgstr "Luo viennit"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Group By"
msgstr "Ryhmittely"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__group_tax_id
msgid "Group Tax"
msgstr "Ryhmänvero"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__hard_lock_date
msgid "Hard Lock"
msgstr "Kova lukitus"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_abnormal_deferred_dates
msgid "Has Abnormal Deferred Dates"
msgstr "Sisältää epänormaaleja lykättyjä päivämääriä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__has_deferred_moves
msgid "Has Deferred Moves"
msgstr "Sisältää jaksotusvientejä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__has_message
msgid "Has Message"
msgstr "Sisältää viestin"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__id
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__id
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__id
msgid "ID"
msgstr "ID"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jos valittu, uudet viestit vaativat huomiotasi."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_error
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jos valittu, joitakin viestejä ei ole toimitettu."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__st_line_checked
msgid ""
"If this checkbox is not ticked, it means that the user was not sure of all "
"the related information at the time of the creation of the move and that the"
" move needs to be checked again."
msgstr ""
"Jos ei valittu, se tarkoittaa, että käyttäjä ei ollut varma kaikista asiaan "
"liittyvistä tiedoista siirtoa luotaessa ja että siirto on tarkistettava "
"uudelleen."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "In Currency"
msgstr "Valuutassa"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Incoming"
msgstr "Saapuva"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_config_settings.py:0
msgid ""
"Incorrect fiscal year date: day is out of range for month. Month: %(month)s;"
" Day: %(day)s"
msgstr ""
"Väärä verovuoden päivämäärä: päivä on kuukauden alueen ulkopuolella. "
"Kuukausi: %(month)s; Day: %(day)s"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__index
msgid "Index"
msgstr "Indeksi"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__invalid
msgid "Invalid"
msgstr "Virheellinen"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Invalid statements"
msgstr "Virheellinen tiliotteet"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__state
msgid ""
"Invalid: The bank transaction can't be validate since the suspense account is still involved\n"
"Valid: The bank transaction can be validated.\n"
"Reconciled: The bank transaction has already been processed. Nothing left to do."
msgstr ""
"Virheellinen: Pankkitapahtumaa ei voida validoida, koska väliaikainen tili on edelleen mukana\n"
"Voimassa: Pankkitapahtuma voidaan varmistaa.\n"
"Täsmennetty: Pankkitapahtuma on jo käsitelty. Mitään ei ole jäljellä."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Invoice Date"
msgstr "Laskun päiväys"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__invoicing_switch_threshold
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__invoicing_switch_threshold
msgid "Invoicing Switch Threshold"
msgstr "Kirjanpitodatan arkistointipäivä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_is_follower
msgid "Is Follower"
msgstr "On seuraaja"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__is_multi_currency
msgid "Is Multi Currency"
msgstr "Onko usean valuutan"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_rec_pay_account
msgid "Is Rec Pay Account"
msgstr "Onko Rec Pay-tili"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_is_reconciled
msgid "Is Reconciled"
msgstr "On täsmäytetty"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_write_off_required
msgid "Is a write-off move required to reconcile"
msgstr "Tarvitaanko poistoilmoituksen siirtoa, jotta voidaan täsmäyttää"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__is_transfer_required
msgid "Is an account transfer required"
msgstr "Tarvitaanko tilisiirto"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__transfer_warning_message
msgid "Is an account transfer required to reconcile"
msgstr "Tarvitaanko tilisiirto täsmäyttämistä varten"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__lock_date_violated_warning_message
msgid "Is the date violating the lock date of moves"
msgstr "Rikkooko päivämäärä muuttojen lukituspäivämäärää"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "It is not possible to decrease or remove the Hard Lock Date."
msgstr "Hard Lock Date -päivämäärää ei voi pienentää tai poistaa."

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_journal
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__journal_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_journal_id
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Journal"
msgstr "Päiväkirja"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__journal_currency_id
msgid "Journal Currency"
msgstr "Päiväkirjan valuutta"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__move_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Entry"
msgstr "Päiväkirjan kirjaus"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_move_line
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Journal Item"
msgstr "Päiväkirjatapahtuma"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Journal Items"
msgstr "Päiväkirjan tapahtumat"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_move_line_posted_unreconciled
msgid "Journal Items to reconcile"
msgstr "Täsmäytettävät päiväkirjakohdat"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Journal items where matching number isn't set"
msgstr "Päiväkirjat, joissa vastaava numero ei ole asetettu"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid ""
"Journal items where the account allows reconciliation no matter the residual"
" amount"
msgstr ""
"Päiväkirjan erät, joiden osalta tili sallii täsmäytyksen jäännösmäärästä "
"riippumatta"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_journal_id
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_journal_id
msgid "Journal used for deferred entries"
msgstr "Jaksotuskirjauksia varten käytettävä päiväkirja"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_digest_digest__kpi_account_bank_cash_value
msgid "Kpi Account Bank Cash Value"
msgstr "KPI-tili Pankin käteisarvo"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__label
msgid "Label"
msgstr "Otsikko"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Last Day"
msgstr "Viimeinen päivä"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Last Statement"
msgstr "Viimeinen tiliote"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_uid
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__write_date
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Latest Statement"
msgstr "Viimeisin tiliote"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Legal signatory"
msgstr "Laillinen allekirjoittaja"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Let’s go back to the dashboard."
msgstr "Mennään takaisin työpöydälle."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__line_ids
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__line_ids
msgid "Line"
msgstr "Rivi"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_bank_rec_widget_line
msgid "Line of the bank reconciliation widget"
msgstr "Pankkien täsmäytyswidgetin rivi"

#. module: account_accountant
#: model:ir.ui.menu,name:account_accountant.menu_action_change_lock_date
msgid "Lock Dates"
msgstr "Kauden lukitus"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date
msgid "Lock Everything"
msgstr "Lukitse kaikki"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date_for_everyone
msgid "Lock Everything For Everyone"
msgstr "Lukitse kaikki kaikille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__fiscalyear_lock_date_for_me
msgid "Lock Everything For Me"
msgstr "Lukitse kaikki minulle"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_view_account_change_lock_date
msgid "Lock Journal Entries"
msgstr "Lukitse päiväkirjamerkinnät"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date
msgid "Lock Purchases"
msgstr "Lukitse ostot"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date_for_everyone
msgid "Lock Purchases For Everyone"
msgstr "Lukitse ostokset kaikille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__purchase_lock_date_for_me
msgid "Lock Purchases For Me"
msgstr "Lukitse ostokset minulle"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date
msgid "Lock Sales"
msgstr "Lukitse myynnit"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date_for_everyone
msgid "Lock Sales For Everyone"
msgstr "Lukitse myynnit kaikille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__sale_lock_date_for_me
msgid "Lock Sales For Me"
msgstr "Lukitse myynnit minulle"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date
msgid "Lock Tax Return"
msgstr "Lukitse veroilmoitus"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date_for_everyone
msgid "Lock Tax Return For Everyone"
msgstr "Lukitse veroilmoitus kaikille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__tax_lock_date_for_me
msgid "Lock Tax Return For Me"
msgstr "Lukitse veroilmoitus minulle"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_main_attachment_id
msgid "Main Attachment"
msgstr "Pääliitetiedosto"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Manual Operations"
msgstr "Manuaalinen kohdistus"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__manual
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__manual
msgid "Manually & Grouped"
msgstr "Manuaalisesti & ryhmitelty"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__manually_modified
msgid "Manually Modified"
msgstr "Manuaalisesti muokattu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
msgid "Match"
msgstr "Vastaavuus"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Match Existing Entries"
msgstr "Täsmäytä avoimiin tapahtumiin"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Matched"
msgstr "Vastaavuus löytynyt"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_payment.py:0
msgid "Matched Transactions"
msgstr "Vastaavat transaktiot"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Matching"
msgstr "Vastaavat"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__matching_rules_allow_auto_reconcile
msgid "Matching Rules Allow Auto Reconcile"
msgstr "Vastaavuussäännöt salivat automaattisen täsmäytyksen"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "Memo"
msgstr "Muistio"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_ir_ui_menu
msgid "Menu"
msgstr "Valikko"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_error
msgid "Message Delivery error"
msgstr "Ongelma viestin toimituksessa"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_ids
msgid "Messages"
msgstr "Viestit"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_expense_amount_computation_method
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__deferred_revenue_amount_computation_method
msgid "Method used to compute the amount of deferred entries"
msgstr "Menetelmä, jota käytetään laskettaessa jaksotuskirjausten määrää"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_expense_entries_method
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__generate_deferred_revenue_entries_method
msgid "Method used to generate deferred entries"
msgstr "Menetelmä, jota käytetään lykättyjen kirjausten tuottamiseen"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_fiscalyear_lock_date_exception_for_everyone_id
msgid "Min Fiscalyear Lock Date Exception For Everyone"
msgstr "Aikaisin verovuoden lukituspäivän poikkeus kaikille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_fiscalyear_lock_date_exception_for_me_id
msgid "Min Fiscalyear Lock Date Exception For Me"
msgstr "Aikaisin verovuoden lukituspäivän poikkeus minulle"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_purchase_lock_date_exception_for_everyone_id
msgid "Min Purchase Lock Date Exception For Everyone"
msgstr "Aikaisin ostojen lukituspäivän poikkeus kaikille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_purchase_lock_date_exception_for_me_id
msgid "Min Purchase Lock Date Exception For Me"
msgstr "Aikaisin ostojen lukituspäivän poikkeus minulle"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_sale_lock_date_exception_for_everyone_id
msgid "Min Sale Lock Date Exception For Everyone"
msgstr "Aikaisin myyntien lukituspäivän poikkeus kaikille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_sale_lock_date_exception_for_me_id
msgid "Min Sale Lock Date Exception For Me"
msgstr "Aikaisin myyntien lukituspäivän poikkeus minulle"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_tax_lock_date_exception_for_everyone_id
msgid "Min Tax Lock Date Exception For Everyone"
msgstr "Aikaisin verojen lukituspäivän poikkeus kaikille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__min_tax_lock_date_exception_for_me_id
msgid "Min Tax Lock Date Exception For Me"
msgstr "Aikaisin verojen lukituspäivän poikkeus minulle"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Misc"
msgstr "Sekalaiset"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_expense_amount_computation_method__month
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__deferred_revenue_amount_computation_method__month
msgid "Months"
msgstr "Kuukaudet"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "More"
msgstr "Lisää"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__move_attachment_ids
msgid "Move Attachment"
msgstr "Siirrä liite"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__move_line_ids
msgid "Move lines to reconcile"
msgstr "Siirrä rivejä yhteensovittamista varten"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__name
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__name
msgid "Name"
msgstr "Nimi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__narration
msgid "Narration"
msgstr "Kuvausteksti"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Need an irreversible lock to ensure inalterability, for all users?"
msgstr ""

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "New"
msgstr "Uusi"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "New Transaction"
msgstr "Uusi transaktio"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "No attachments linked."
msgstr "Ei linkitettyjä liitteitä."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "No statement"
msgstr "Ei tiliotetta"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions_kanban
msgid "No transactions matching your filters were found."
msgstr "Suodattimiasi vastaavia tapahtumia ei löytynyt."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Not Matched"
msgstr "Ei täsmää"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Not locked"
msgstr "Ei lukittu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_tree_bank_rec_widget
msgid "Notes"
msgstr "Muistiinpanot"

#. module: account_accountant
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions
#: model_terms:ir.actions.act_window,help:account_accountant.action_bank_statement_line_transactions_kanban
msgid "Nothing to do here!"
msgstr "Ei tehtävää!"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Now, we'll create your first invoice (accountant)"
msgstr "Nyt luodaan ensimmäinen lasku (kirjanpitäjä)"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_needaction_counter
msgid "Number of Actions"
msgstr "Toimenpiteiden määrä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_error_counter
msgid "Number of errors"
msgstr "Virheiden määrä"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Toimenpiteitä vaativien viestien määrä"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Toimitusvirheellisten viestien määrä"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_expense_entries_method__on_validation
#: model:ir.model.fields.selection,name:account_accountant.selection__res_company__generate_deferred_revenue_entries_method__on_validation
msgid "On bill validation"
msgstr "Laskun validointi"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr ""
"Vain Billing Administrator -käyttöoikeuden omaavat käyttäjät voivat vaihtaa "
"kauden lukituspäiviä!"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid ""
"Only partial reconciliation is possible. Proceed in multiple steps if you "
"want to full reconcile."
msgstr ""
"Vain osittainen täsmäytys on mahdollinen. Jos haluat täyttä täsmäytystä, "
"etene useammassa vaiheessa."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/move_line_list/move_line_list.xml:0
msgid "Open attachment in pop out"
msgstr "Avaa liitetiedosto ponnahdusikkunassa"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "Open balance of %(amount)s"
msgstr "Avoin saldo %(amount)s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Original Deferred Entries"
msgstr "Alkuperäiset jaksotuskirjaukset"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,field_description:account_accountant.field_account_move__deferred_original_move_ids
msgid "Original Invoices"
msgstr "Alkuperäiset laskut"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Originator Tax"
msgstr "Alullepanijan vero"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_search_bank_rec_widget
msgid "Outgoing"
msgstr "Lähtevä"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_id
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_quick_create_form_bank_rec_widget
msgid "Partner"
msgstr "Kumppani"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_currency_id
msgid "Partner Currency"
msgstr "Kumppanin valuutta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__partner_name
msgid "Partner Name"
msgstr "Kumppanin nimi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_account_id
msgid "Partner Payable Account"
msgstr "Kumppanin maksettava tili"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_payable_amount
msgid "Partner Payable Amount"
msgstr "Kumppani Maksettava määrä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_account_id
msgid "Partner Receivable Account"
msgstr "Kumppanin saamistili"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__partner_receivable_amount
msgid "Partner Receivable Amount"
msgstr "Kumppani Saatava määrä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__partner_ids
msgid "Partners"
msgstr "Kumppanit"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Payable"
msgstr "Ostovelat"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Payable:"
msgstr "Maksettava:"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_payment_form_inherit_account_accountant
msgid "Payment Matching"
msgstr "Maksutäsmäytys"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__payment_state_before_switch
#: model:ir.model.fields,field_description:account_accountant.field_account_move__payment_state_before_switch
msgid "Payment State Before Switch"
msgstr "Maksun tila ennen vaihtoa"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_payment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Payments"
msgstr "Maksut"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "Payments Matching"
msgstr "Maksujen täsmäytys"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_auto_reconcile_wizard__search_mode__one_to_one
msgid "Perfect Match"
msgstr "Täydellinen täsmäävyys"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Please set the deferred accounts in the accounting settings."
msgstr "Aseta jaksotustilit kirjanpidon asetuksissa."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "Please set the deferred journal in the accounting settings."
msgstr "Aseta kirjanpidon asetuksissa jaksotuspäiväkirja."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__predict_bill_product
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__predict_bill_product
msgid "Predict Bill Product"
msgstr "Ennusta laskun tuote"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Predict vendor bill product"
msgstr "Ennusta myyjän laskun tuote"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model
msgid ""
"Preset to create journal entries during a invoices and payments matching"
msgstr ""
"Esiasetus, luodaksesi päiväkirjamerkintöjä laskujen ja maksujen sovittamisen"
" aikana"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__rating_ids
msgid "Ratings"
msgstr "Arviointi"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Reason..."
msgstr "Syy..."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Receivable"
msgstr "Saatavat"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Receivable:"
msgstr "Saatavat:"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__search_mode
#: model:ir.ui.menu,name:account_accountant.menu_account_reconcile
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_payment_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_tree
msgid "Reconcile"
msgstr "Täsmäytä"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_wizard
msgid "Reconcile & open"
msgstr "Täsmäytä ja avaa"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_account_id
msgid "Reconcile Account"
msgstr "Täsmäytä tili"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__reconcile_model_id
msgid "Reconcile Model"
msgstr "Täsmäytysmalli"

#. module: account_accountant
#: model:ir.actions.act_window,name:account_accountant.action_open_auto_reconcile_wizard
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_auto_reconcile_wizard
msgid "Reconcile automatically"
msgstr "Täsmäyttää automaattisesti"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_auto_reconcile_wizard__search_mode
msgid ""
"Reconcile journal items with opposite balance or clear accounts with a zero "
"balance"
msgstr ""
"Täsmäyttää päiväkirjan erät, joiden saldo on vastakkainen, tai tyhjentää "
"tilit, joiden saldo on nolla"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__reconciled
msgid "Reconciled"
msgstr "Täsmäytetty"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__reco_model_id
msgid "Reconciliation model"
msgstr "Täsmäytysmalli"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "Record cost of goods sold in your journal entries"
msgstr "Merkitse myytyjen tuotteiden kustannukset päiväkirjavienneille"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__ref
msgid "Ref"
msgstr "Viite"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Reference"
msgstr "Viite"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Purchase(s)"
msgstr "Liittyvät ostot"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_form_inherit
msgid "Related Sale(s)"
msgstr "Liittyvät myynnit"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Reset"
msgstr "Palauta"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual"
msgstr "Jäännös"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Residual in Currency"
msgstr "Jäännös valuuttana"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__return_todo_command
msgid "Return Todo Command"
msgstr "Palauta Todo-komento"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Review"
msgstr "Tarkasta"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Revoke"
msgstr "Peruuta valtuutus"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_reconcile_model_line
msgid "Rules for the reconciliation model"
msgstr "Täsmäytysmallin säännöt"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Tekstiviestin toimitusvirhe"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid "Save"
msgstr "Tallenna"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & Close"
msgstr "Tallenna ja sulje"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_form_bank_rec_widget
msgid "Save & New"
msgstr "Tallenna ja luo uusi"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Search Journal Items to Reconcile"
msgstr "Etsi päiväkirjakohteita täsmäytettäväksi"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_res_config_settings__signing_user
msgid ""
"Select a user here to override every signature on invoice by this user's "
"signature"
msgstr ""
"Valitse tässä käyttäjä, jos haluat korvata kaikki laskun allekirjoitukset "
"tämän käyttäjän allekirjoituksella"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_aml_ids
msgid "Selected Aml"
msgstr "Valittu rahanpesun vastaisuus"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__selected_reco_model_id
msgid "Selected Reco Model"
msgstr "Valittu Reco-malli"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Set an amount."
msgstr "Aseta määrä."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Set as Checked"
msgstr "Merkitse tarkistetuksi"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/js/tours/account_accountant.js:0
msgid "Set the payment reference."
msgstr "Aseta maksuviite."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_change_lock_date__show_draft_entries_warning
msgid "Show Draft Entries Warning"
msgstr "Näytä luonnosmerkintöjen varoitus"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__show_signature_area
#: model:ir.model.fields,field_description:account_accountant.field_account_move__show_signature_area
msgid "Show Signature Area"
msgstr "Näytä allekirjoitusalue"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__module_sign
msgid "Sign"
msgstr "Allekirjoita"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__signature
#: model:ir.model.fields,field_description:account_accountant.field_account_move__signature
msgid "Signature"
msgstr "Allekirjoitus"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_config_settings__signing_user
msgid "Signature used to sign all the invoice"
msgstr "Allekirjoitus, jolla kaikki laskut allekirjoitetaan"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement_line__signing_user
#: model:ir.model.fields,field_description:account_accountant.field_account_move__signing_user
msgid "Signer"
msgstr "Allekirjoittaja"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_res_company__signing_user
msgid "Signing User"
msgstr "Allekirjoittava käyttäjä"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__single_currency_mode
msgid "Single Currency Mode"
msgstr "Yhden valuutan tila"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_id
msgid "Source Aml"
msgstr "Lähteenä oleva kirjanpitovienti"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_id
msgid "Source Aml Move"
msgstr "Lähteenä oleva kirjanpitovienti"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_aml_move_name
msgid "Source Aml Move Name"
msgstr "Lähteenä olevan kirjanpitoviennin nimi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_amount_currency
msgid "Source Amount Currency"
msgstr "Lähdevaluutta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_balance
msgid "Source Balance"
msgstr "Lähdesaldo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_credit
msgid "Source Credit"
msgstr "Lähde credit"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_debit
msgid "Source Debit"
msgstr "Lähde debit"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__source_rate
msgid "Source Rate"
msgstr "Lähdekurssi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_id
msgid "St Line"
msgstr "Tiliotteen rivi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__st_line_transaction_details
msgid "St Line Transaction Details"
msgstr "Tiliotteen rivin tapahtuman tiedot"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_fiscal_year__date_from
#: model:ir.model.fields,field_description:account_accountant.field_account_move_line__deferred_start_date
msgid "Start Date"
msgstr "Alkupäivä"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_fiscal_year__date_from
msgid "Start Date, included in the fiscal year."
msgstr "Aloituspäivä, joka sisältyy tilikauteen."

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__state
msgid "State"
msgstr "Alue"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.action_bank_statement_attachment
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement"
msgstr "Tiliote"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Statement Line"
msgstr "Tilioterivi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_amount_currency
msgid "Suggestion Amount Currency"
msgstr "Ehdotus Määrä Valuutta"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_balance
msgid "Suggestion Balance"
msgstr "Ehdotus Saldo"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__suggestion_html
msgid "Suggestion Html"
msgstr "Ehdotus Html"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "Suggestions"
msgstr "Ehdotukset"

#. module: account_accountant
#: model:ir.model,name:account_accountant.model_account_tax
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__tax_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_ids
msgid "Tax"
msgstr "Vero"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_base_amount_currency
msgid "Tax Base Amount Currency"
msgstr "Veron perusteen määrä valuutassa"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Tax Grids"
msgstr "Verotunnisteet"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_repartition_line_id
msgid "Tax Repartition Line"
msgstr "Veron uudelleenjakorivi"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__tax_tag_ids
msgid "Tax Tag"
msgstr "Verotunniste"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: code:addons/account_accountant/static/src/components/bank_reconciliation/kanban.js:0
msgid "Taxes"
msgstr "Verot"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "That's on average"
msgstr "Se on keskimäärin"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget__country_code
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Kaksikirjaiminen ISO-maaatunnus. \n"
"Voit hakea tämän kentän avulla nopeasti."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__amount_transaction_currency
msgid ""
"The amount expressed in an optional other currency if it is a multi-currency"
" entry."
msgstr ""
"Summa on ilmoitettu valinnaisessa toisessa valuutassa, jos kyseessä on "
"monivaluuttainen merkintä."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The amount of the write-off of a single credit line should be strictly "
"negative."
msgstr "Yksittäisen luottolimiitin poiston määrän on oltava negatiivinen."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The amount of the write-off of a single debit line should be strictly "
"positive."
msgstr "Yksittäisen veloituslaskelman poiston määrän on oltava positiivinen."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "The amount of the write-off of a single line cannot be 0."
msgstr "Yksittäisen rivin poiston määrä ei voi olla 0."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid ""
"The date you set violates the lock date of one of your entry. It will be "
"overriden by the following date : %(replacement_date)s"
msgstr ""
"Asettamasi päivämäärä rikkoo yhden merkintöjen lukituspäivämäärää. Se "
"korvataan seuraavalla päivämäärällä : %(replacement_date)s"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_move_ids
msgid "The deferred entries created by this invoice"
msgstr "Tämän laskun luomat jaksotuskirjaukset"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid "The ending date must not be prior to the starting date."
msgstr "Lopetuspäivä ei saa olla ennen alkamispäivää."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be entirely paid by the transaction."
msgstr ""
"Lasku %(display_name_html)s, jonka avoin summa on %(open_amount)s, maksetaan"
" kokonaan tapahtumalla."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"The invoice %(display_name_html)s with an open amount of %(open_amount)s "
"will be reduced by %(amount)s."
msgstr ""
"Laskua %(display_name_html)s, jonka avoin summa on %(open_amount)s, "
"vähennetään %(amount)s."

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The invoices up to this date will not be taken into account as accounting "
"entries"
msgstr ""
"Tähän päivään mennessä tehtyjä laskuja ei oteta huomioon kirjanpitovienteinä"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_bank_rec_widget_line__transaction_currency_id
msgid "The optional other currency if it is a multi-currency entry."
msgstr "Valinnainen toinen valuutta, jos merkintä on monivaluuttainen."

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement_line__deferred_original_move_ids
#: model:ir.model.fields,help:account_accountant.field_account_move__deferred_original_move_ids
msgid "The original invoices that created the deferred entries"
msgstr "Alkuperäiset laskut, jotka loivat jaksotuskirjaukset"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid ""
"The system will try to predict the product on vendor bill lines based on the"
" label of the line"
msgstr ""
"Järjestelmä yrittää ennustaa tuotteen myyjän laskuriveillä rivin etiketin "
"perusteella"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_change_lock_date
msgid ""
"There are still draft entries in the period you want to lock.\n"
"                                You should either post or delete them."
msgstr ""
"Sillä kaudella, jonka haluat lukita, on vielä luonnosmerkintöjä.\n"
"                                Sinun pitäisi joko kirjata tai poistaa ne."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_bank_statement.py:0
msgid ""
"This bank transaction has been automatically validated using the "
"reconciliation model '%s'."
msgstr ""
"Tämä pankkitapahtuma on varmistettu automaattisesti täsmäytysmallilla "
"\"%s\"."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid ""
"This bank transaction is locked up tighter than a squirrel in a nut factory!"
" You can't hit the reset button on it. So, do you want to \"unreconcile\" it"
" instead?"
msgstr ""
"Tämä pankkitapahtuma on lukittu. Et voi painaa nollata sitä. Haluatko tämän "
"sijaan purkaa sen?"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "This can only be used on journal items"
msgstr "Tätä voidaan käyttää vain päiväkirjariveillä"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_reconcile_model_line.py:0
msgid ""
"This reconciliation model can't be used in the manual reconciliation widget "
"because its configuration is not adapted"
msgstr ""
"Tätä täsmäytysmallia ei voi käyttää manuaalisessa täsmäytys-widgetissä, "
"koska sen konfigurointia ei ole mukautettu"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_0
msgid "Tip: Bulk update journal items"
msgstr "Vihje: Massapäivitä päiväkirjarivit"

#. module: account_accountant
#: model:digest.tip,name:account_accountant.digest_tip_account_accountant_1
msgid "Tip: Find an Accountant or register your Accounting Firm"
msgstr "Vinkki: Etsi kirjanpitäjä tai rekisteröi tilitoimisto"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_auto_reconcile_wizard__to_date
msgid "To"
msgstr "Päättyy"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model:ir.model.fields,field_description:account_accountant.field_account_reconcile_wizard__to_check
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "To Check"
msgstr "Tarkistettava"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_kanban_bank_rec_widget
msgid "To check"
msgstr "Tarkistettava"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.res_config_settings_view_form
msgid "To enhance authenticity, add a signature to your invoices"
msgstr "Lisää laskuihin allekirjoitus, jotta ne olisivat virallisempia"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__todo_command
msgid "Todo Command"
msgstr "Aseta tehtäväksi -komento"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Balance"
msgstr "Saldo yhteensä"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Credit"
msgstr "Kredit yhteensä"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Debit"
msgstr "Debet yhteensä"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual"
msgstr "Kokonaisjäännös"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_move_line_reconcile_tree
msgid "Total Residual in Currency"
msgstr "Kokonaisjäännös valuutassa"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_line_search_bank_rec_widget
msgid "Transaction"
msgstr "Tapahtuma"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__transaction_currency_id
msgid "Transaction Currency"
msgstr "Tapahtuman valuutta"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "Transaction Details"
msgstr "Maksutapahtuman tiedot"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
#: model_terms:ir.ui.view,arch_db:account_accountant.view_bank_statement_tree
msgid "Transactions"
msgstr "Tapahtumat"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Transfer from %s"
msgstr "Siirrä kohteesta %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Transfer to %s"
msgstr "Siirrä kohteeseen %s"

#. module: account_accountant
#: model:ir.actions.server,name:account_accountant.auto_reconcile_bank_statement_line_ir_actions_server
msgid "Try to reconcile automatically your statement lines"
msgstr "Yritä täsmäyttää automaattisesti tiliöintirivit"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "Unreconciled"
msgstr "Täsmäyttämätön"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/res_company.py:0
msgid "Unreconciled statements lines"
msgstr "Erittelemättömät lausumarivit"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget__state__valid
msgid "Valid"
msgstr "Vahvistettu"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "Validate"
msgstr "Vahvista"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/list_view_switcher.js:0
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_list_bank_rec_widget
msgid "View"
msgstr "Näytä"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "View Reconciled Entries"
msgstr "Näytä täsmäytetyt kirjaukset"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "View models"
msgstr "Näytä mallit"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_account_bank_statement__website_message_ids
msgid "Website Messages"
msgstr "Verkkosivun ilmoitukset"

#. module: account_accountant
#: model:ir.model.fields,help:account_accountant.field_account_bank_statement__website_message_ids
msgid "Website communication history"
msgstr "Verkkosivun viestihistoria"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_move_line_reconcile_search
msgid "With residual"
msgstr "Jäännös"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__wizard_id
msgid "Wizard"
msgstr "Ohjattu toiminto"

#. module: account_accountant
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget__company_currency_id
#: model:ir.model.fields,field_description:account_accountant.field_bank_rec_widget_line__company_currency_id
msgid "Wizard Company Currency"
msgstr "Yrityksen valuutan ohjattu toiminto"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Write-Off"
msgstr "Alaskirjaus"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "Write-Off Entry"
msgstr "Poistokirjaus"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid ""
"You can not have an overlap between two fiscal years, please correct the "
"start and/or end dates of your fiscal years."
msgstr ""
"Et voi olla päällekkäinen kahden tilivuoden välillä, korjaa tilikausien "
"alkamis- ja / tai päättymispäivät."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_reconcile_wizard.py:0
msgid "You can only reconcile entries with up to two different accounts: %s"
msgstr "Voit täsmäyttää kirjauksia vain enintään kahden eri tilin kanssa: %s"

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget.py:0
msgid "You can't hit the reset button on a secured bank transaction."
msgstr "Et voi painaa nollauspainiketta suojatussa pankkitapahtumassa."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot change the account for a deferred line in %(move_name)s if it has"
" already been deferred."
msgstr ""
"Jaksotusrivin tiliä ei voi muuttaa kohdassa %(move_name)s, jos se on jo "
"jaksotettu."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid "You cannot create a deferred entry with a start date but no end date."
msgstr ""
"Et voi luoda jaksotusta, jolla on alkamispäivä mutta ei päättymispäivää."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot create a deferred entry with a start date later than the end "
"date."
msgstr ""
"Et voi luoda jaksotusta, jonka alkamispäivä on myöhäisempi kuin "
"päättymispäivä."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot generate deferred entries for a miscellaneous journal entry."
msgstr ""
"Et voi luoda jaksotuskirjauksia sekalaisia päiväkirjamerkintöjä varten."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_fiscal_year.py:0
msgid "You cannot have a fiscal year on a child company."
msgstr "Tytäryhtiöllä ei voi olla tilikautta."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/account_move.py:0
msgid ""
"You cannot reset to draft an invoice that is grouped in deferral entry. You "
"can create a credit note instead."
msgstr ""
"Et voi nollata laskun laatimista, joka on ryhmitelty lykkäysmerkinnällä. "
"Voit sen sijaan luoda hyvityslaskun."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You cannot set a Lock Date in the future."
msgstr "Et voi asettaa lukituspäivää tulevaisuuteen."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to %(btn_start)sfully reconcile%(btn_end)s the document."
msgstr ""
"Kannattaa ehkä %(btn_start)s täsmäyttää kokonaan %(btn_end)s :n asiakirja."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to make a %(btn_start)spartial reconciliation%(btn_end)s "
"instead."
msgstr ""
"Kannattaa ehkä tehdä %(btn_start)s osittainen yhteensovittaminen%(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid "You might want to record a %(btn_start)spartial payment%(btn_end)s."
msgstr "Kannattaa ehkä kirjata %(btn_start)s osittainen maksu %(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/models/bank_rec_widget_line.py:0
msgid ""
"You might want to set the invoice as %(btn_start)sfully paid%(btn_end)s."
msgstr ""
"Kannattaa ehkä asettaa laskun arvoksi %(btn_start)s täysin "
"maksettu%(btn_end)s."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You need to select a duration for the exception."
msgstr "Sinun on valittava poikkeuksen kesto."

#. module: account_accountant
#. odoo-python
#: code:addons/account_accountant/wizard/account_change_lock_date.py:0
msgid "You need to select who the exception applies to."
msgstr "Sinun on valittava, ketä poikkeus koskee."

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "You reconciled"
msgstr "Täsmäytit"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__aml
msgid "aml"
msgstr "rahanpesun vastainen"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__auto_balance
msgid "auto_balance"
msgstr "auto_balance"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.view_account_reconcile_model_widget_wizard
msgid "e.g. Bank Fees"
msgstr "esim. pankin toimitusmaksut"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__early_payment
msgid "early_payment"
msgstr "early_payment"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__exchange_diff
msgid "exchange_diff"
msgstr "exchange_diff"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__1h
msgid "for 1 hour"
msgstr "1 tunnin ajan"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__15min
msgid "for 15 minutes"
msgstr "15 minuutin ajan"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__24h
msgid "for 24 hours"
msgstr "24 tunnin ajan"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__5min
msgid "for 5 minutes"
msgstr "5 minuuttia"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_applies_to__everyone
msgid "for everyone"
msgstr "kaikille"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_applies_to__me
msgid "for me"
msgstr "minulle"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__account_change_lock_date__exception_duration__forever
msgid "forever"
msgstr "ikuisesti"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/bank_rec_form.xml:0
msgid "in"
msgstr "tuumaa"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__liquidity
msgid "liquidity"
msgstr "likviditeetti"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__manual
msgid "manual"
msgstr "manuaalinen"

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__new_aml
msgid "new_aml"
msgstr "new_aml"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "seconds per transaction."
msgstr "sekuntia transaktiota kohden."

#. module: account_accountant
#: model:ir.model.fields.selection,name:account_accountant.selection__bank_rec_widget_line__flag__tax_line
msgid "tax_line"
msgstr "vero_rivi"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to check"
msgstr "tarkistettava"

#. module: account_accountant
#: model_terms:ir.ui.view,arch_db:account_accountant.account_journal_dashboard_kanban_view
msgid "to reconcile"
msgstr "täsmäytettäväksi"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "transaction in"
msgstr "sisääntulevat siirrot"

#. module: account_accountant
#. odoo-javascript
#: code:addons/account_accountant/static/src/components/bank_reconciliation/rainbowman_content.xml:0
msgid "transactions in"
msgstr "tapahtumia"
