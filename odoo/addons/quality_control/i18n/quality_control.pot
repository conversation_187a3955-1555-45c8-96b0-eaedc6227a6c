# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* quality_control
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:25+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "% of Operations"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "% of Transfers"
msgstr ""

#. module: quality_control
#: model:ir.actions.report,print_report_name:quality_control.quality_check_report
#: model:ir.actions.report,print_report_name:quality_control.quality_check_report_internal
msgid "'Worksheet_%s' % object.name"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "15.3 cm"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "2023-08-15"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"Domain alias\"/>&amp;nbsp;"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid ""
"<span class=\"fa fa-2x\" data-icon=\"∑\" style=\"padding-left: 10px;\" "
"role=\"img\" aria-label=\"Statistics\" title=\"Statistics\"/>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-danger\">Quality Checks</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text text-success\">Quality Checks</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid ""
"<span class=\"o_stat_text\">AVG:</span>\n"
"                        <span class=\"o_stat_text\">STD:</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_product_form_view_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_template_form_view_quality_control
msgid ""
"<span class=\"o_stat_text\">Pass:</span>\n"
"                        <span class=\"o_stat_text\">Fail:</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Alert</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_production_lot_view_form
msgid "<span class=\"o_stat_text\">Quality Alerts:</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "<span class=\"o_stat_text\">Quality Check</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "<span class=\"o_stat_text\">Quality Checks</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid ""
"<span invisible=\"measure_frequency_type in ('all', 'on_demand')\">Every "
"</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>% of products</span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>Test </span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>from </span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "<span>to </span>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Lot/Serial Number: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Measure: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Notes: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Product: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Test Type: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Tested by: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Tested on: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Transfer: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "<strong>Warning: </strong>"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Accept Emails From"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_tag_action
msgid "Add a new tag"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__additional_note
msgid "Additional Note"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__additional_note
msgid "Additional remarks concerning this check."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_alert_ids
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Alerts"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__all
msgid "All"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__pass
msgid "All checks passed"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "All parameters passed."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__allowed_product_ids
msgid "Allowed Product"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__allowed_quality_point_ids
msgid "Allowed Quality Point"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Archived"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__average
msgid "Average"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Back"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__check_ids
msgid "Check"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_move_line__check_state
msgid "Check State"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Checked By"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Checked Date"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_move_line__check_ids
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__check_ids
msgid "Checks"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Checks In Progress"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__company_id
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__company_id
msgid "Company"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_configuration
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Configuration"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_on_demand_view_form
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Confirm"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Confirm Measure"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_type
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Control Frequency"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Control Person"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Control Point"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_control_points
msgid "Control Points"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure_on
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__measure_on
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__measure_on
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_on
msgid "Control per"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_point_kanban
msgid "Control per:"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Correct Measure"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Corrective Actions"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Create Alert"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_action_check
msgid "Create a new quality alert"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_stage_action
msgid "Create a new quality alert stage"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__create_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__create_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__create_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__create_uid
msgid "Created by"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__create_date
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__create_date
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__create_date
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__create_date
msgid "Created on"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__current_check_id
msgid "Current Check"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__current_revision_uuid
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__current_revision_uuid
msgid "Current Revision Uuid"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_spreadsheet_template_view_list
msgid "Data"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__day
msgid "Days"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__uom_id
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_main
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_team
msgid ""
"Define Quality Control Points in order to automatically generate\n"
"              quality checks at the right logistic operation: transfers, manufacturing orders."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__testing_percentage_within_lot
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__testing_percentage_within_lot
#: model:ir.model.fields,help:quality_control.field_quality_point__testing_percentage_within_lot
msgid "Defines the percentage within a lot that should be tested"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Describe the corrective actions you did..."
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Describe the preventive actions you did..."
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Description"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Description of the issue..."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__is_lot_tested_fractionally
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__is_lot_tested_fractionally
#: model:ir.model.fields,help:quality_control.field_quality_point__is_lot_tested_fractionally
msgid "Determines if only a fraction of the lot should be tested"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Discard"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__display_name
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__display_name
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__display_name
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__display_name
msgid "Display Name"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_spreadsheet_template_view_list
msgid "Edit"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Email Alias"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__product_tracking
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__product_tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__fail
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Fail"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Failed"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Failed Quantity"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__failure_location_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__failure_location_id
msgid "Failure Location"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__potential_failure_location_ids
#: model:ir.model.fields,field_description:quality_control.field_quality_point__failure_location_ids
msgid "Failure Locations"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__failure_message
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__failure_message
#: model:ir.model.fields,field_description:quality_control.field_quality_point__failure_message
msgid "Failure Message"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Frequency"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_unit_value
msgid "Frequency Unit Value"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Group By"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__id
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__id
msgid "ID"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__potential_failure_location_ids
#: model:ir.model.fields,help:quality_control.field_quality_point__failure_location_ids
msgid ""
"If a quality check fails, a location is chosen from this list for each "
"failed quantity."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check_on_demand__lot_id
msgid ""
"If you want to specify a lot/serial number before the transfer validation,"
"                                create a new lot here from this field with "
"the same exact lot name of the move line you want to add a check for."
"                                If you want to create a check for all move "
"lines, leave this field empty."
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "In Progress"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__move_line_id
msgid ""
"In case of Quality Check by Quantity, Move Line on which the Quality Check "
"applies"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Instructions"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__is_last_check
msgid "Is Last Check"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "John Doe"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "LT-00045"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Laptop XYZ"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__write_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__write_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__write_uid
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__write_date
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__write_date
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__write_date
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__lot_line_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__lot_line_id
msgid "Lot Line"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__is_lot_tested_fractionally
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__is_lot_tested_fractionally
#: model:ir.model.fields,field_description:quality_control.field_quality_point__is_lot_tested_fractionally
msgid "Lot Tested Fractionally"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Lot/SN"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_lot
msgid "Lot/Serial"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__lot_id
msgid "Lot/Serial Number"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__lot_name
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__lot_name
msgid "Lot/Serial Number Name"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_traceability
msgid "Lots/Serial Numbers"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__tolerance_max
#: model:ir.model.fields,field_description:quality_control.field_quality_point__tolerance_max
msgid "Max Tolerance"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__measure
#: model:quality.point.test_type,name:quality_control.test_type_measure
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Measure"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_unit
msgid "Measure Frequency Unit"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__measure_success
msgid "Measure Success"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Message If Failure"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__tolerance_min
#: model:ir.model.fields,field_description:quality_control.field_quality_point__tolerance_min
msgid "Min Tolerance"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Miscellaneous"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__month
msgid "Months"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__name
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__name
msgid "Name"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__nb_checks
msgid "Nb Checks"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
msgid "New"
msgstr ""

#. module: quality_control
#. odoo-javascript
#: code:addons/quality_control/static/src/spreadsheet_bundle/quality_spreadsheet_template_action/quality_spreadsheet_template_action.js:0
msgid "New quality spreadsheet template created"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Next"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__no_checks
msgid "No checks"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_spc
msgid "No data yet!"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__none
msgid "No measure"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_action_report
msgid "No quality alert"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_main
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_team
msgid "No quality check found"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_check_action_report
msgid "No quality checks"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_point_action
msgid "No quality control point found"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__norm
msgid "Norm"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__norm_unit
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__norm_unit
#: model:ir.model.fields,field_description:quality_control.field_quality_point__norm_unit
msgid "Norm Unit"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__note
msgid "Note"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Notes"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "OK"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_on_demand_view_form
msgid "On-Demand Quality Check"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__on_demand
msgid "On-demand"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Open spreadsheet"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__operation
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__operation
msgid "Operation"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__measure_on
#: model:ir.model.fields,help:quality_control.field_quality_check_on_demand__measure_on
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__measure_on
#: model:ir.model.fields,help:quality_control.field_quality_point__measure_on
msgid ""
"Operation = One quality check is requested at the operation level.\n"
"                  Product = A quality check is requested per product.\n"
"                 Quantity = A quality check is requested for each new product quantity registered, with partial quantity checks also possible."
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
msgid "Operation not supported"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_dashboard
msgid "Overview"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Partial Test"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Partner"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_success__pass
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Pass"
msgstr ""

#. module: quality_control
#: model:quality.point.test_type,name:quality_control.test_type_passfail
msgid "Pass - Fail"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Passed"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_check_todo
msgid "Pending checks"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__measure_frequency_value
msgid "Percentage"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__periodical
msgid "Periodically"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__picking_id
msgid "Picking"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__picture
msgid "Picture"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
msgid "Picture Uploaded"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__position_current_check
msgid "Position Current Check"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_form
msgid "Preventive Actions"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Previous"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_product_template
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__product_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__product_id
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__product
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__product
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Product"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__uom_id
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__uom_id
msgid "Product Unit of Measure"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_product_product
msgid "Product Variant"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_product_variant
msgid "Product Variants"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.quality_control_menu_product
#: model:ir.ui.menu,name:quality_control.quality_product_menu_product_template
msgid "Products"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "QC-00123"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_failed
msgid "Qty Failed"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_root
msgid "Quality"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#: model:ir.actions.server,name:quality_control.stock_picking_action_quality_alert
#: model:ir.model,name:quality_control.model_quality_alert
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_calendar
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_search_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
msgid "Quality Alert"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_graph
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_view_pivot
msgid "Quality Alert Analysis"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_alert_count
msgid "Quality Alert Count"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_lot__quality_alert_qty
msgid "Quality Alert Qty"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_stage_action
#: model:ir.ui.menu,name:quality_control.menu_quality_config_alert_stage
msgid "Quality Alert Stages"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_stage_action
msgid ""
"Quality Alert stages define the different steps a quality alert should go "
"through."
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_check
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_report
#: model:ir.actions.act_window,name:quality_control.quality_alert_action_team
#: model:ir.ui.menu,name:quality_control.menu_quality_alert
#: model:ir.ui.menu,name:quality_control.menu_quality_alert_report
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_dashboard_view_kanban
msgid "Quality Alerts"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
#: model:ir.actions.act_window,name:quality_control.action_quality_check_wizard
#: model:ir.actions.server,name:quality_control.stock_picking_action_quality_check_on_demand
#: model:ir.model,name:quality_control.model_quality_check
#: model_terms:ir.ui.view,arch_db:quality_control.view_stock_move_line_detailed_operation_tree_inherit_quality
msgid "Quality Check"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_graph
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_pivot
msgid "Quality Check Analysis"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_picking__quality_check_fail
msgid "Quality Check Fail"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Quality Check Failed"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/wizard/quality_check_wizard.py:0
msgid "Quality Check Failed for %(product_name)s"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Quality Check Picture"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__quality_point_id
msgid "Quality Check Point"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_stock_lot__quality_check_qty
msgid "Quality Check Qty"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Quality Check:"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_check_action_main
#: model:ir.actions.act_window,name:quality_control.quality_check_action_picking
#: model:ir.actions.act_window,name:quality_control.quality_check_action_production_lot
#: model:ir.actions.act_window,name:quality_control.quality_check_action_report
#: model:ir.actions.act_window,name:quality_control.quality_check_action_team
#: model:ir.ui.menu,name:quality_control.menu_quality_check_report
#: model:ir.ui.menu,name:quality_control.menu_quality_checks
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_form_inherit_quality
#: model_terms:ir.ui.view,arch_db:quality_control.stock_picking_view_search_inherit_quality
#: model_terms:ir.ui.view,arch_db:quality_control.stock_production_lot_form_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Quality Checks"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_check_action_spc
msgid "Quality Checks SPC"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_control
msgid "Quality Control"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_point
msgid "Quality Control Point"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_control_point_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_control_point_qty
msgid "Quality Control Point Qty"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_point_action
msgid "Quality Control Points"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_fail_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_fail_qty
msgid "Quality Fail Qty"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_team_action
msgid "Quality Overview"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_product_product__quality_pass_qty
#: model:ir.model.fields,field_description:quality_control.field_product_template__quality_pass_qty
msgid "Quality Pass Qty"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_product_form_view_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.product_template_form_view_quality_control
msgid "Quality Points"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_spreadsheet_template_action_config
#: model:ir.ui.menu,name:quality_control.menu_config_quality_spreadsheet_template
msgid "Quality Spreadsheet Templates"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_tag_action
#: model:ir.ui.menu,name:quality_control.menu_config_quality_tags
msgid "Quality Tags"
msgstr ""

#. module: quality_control
#: model:ir.actions.act_window,name:quality_control.quality_alert_team_action_config
#: model:ir.ui.menu,name:quality_control.menu_quality_config_alert_team
msgid "Quality Teams"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_alert_team_action
msgid ""
"Quality Teams group the different quality alerts/checks\n"
"              according to the roles (teams) that need them."
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_report_quality_control_quality_worksheet_internal
msgid "Quality Worksheet Internal Report"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_report_quality_control_quality_worksheet
msgid "Quality Worksheet Report"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_check_spreadsheet
msgid "Quality check spreadsheet"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_spreadsheet_template
msgid "Quality check template spreadsheet"
msgstr ""

#. module: quality_control
#: model_terms:ir.actions.act_window,help:quality_control.quality_point_action
msgid ""
"Quality control points define the quality checks which should be\n"
"              performed at each operation, for your different products."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_line
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_line
#: model:ir.model.fields.selection,name:quality_control.selection__quality_check__measure_on__move_line
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_on__move_line
msgid "Quantity"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_failed
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_wizard_form_failure
msgid "Quantity Failed"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_passed
msgid "Quantity Passed"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_tested
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_tested
msgid "Quantity Tested"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_tested
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__qty_tested
msgid "Quantity of product tested within the lot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_failed
msgid "Quantity of product that failed the quality check"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_passed
msgid "Quantity of product that passed the quality check"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__qty_to_test
#: model:ir.model.fields,help:quality_control.field_quality_check_wizard__qty_to_test
msgid "Quantity of product to test within the lot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__qty_to_test
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__qty_to_test
msgid "Quantity to Test"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_type__random
msgid "Randomly"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__name
msgid "Reference"
msgstr ""

#. module: quality_control
#: model:ir.ui.menu,name:quality_control.menu_quality_reporting
msgid "Reporting"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Responsible"
msgstr ""

#. module: quality_control
#. odoo-javascript
#: code:addons/quality_control/static/src/spreadsheet_bundle/quality_check_spreadsheet_action/quality_check_spreadsheet_action.xml:0
msgid "Save in"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_on_demand__show_lot_number
msgid "Show Lot Number"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__show_lot_text
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__show_lot_text
msgid "Show Lot Text"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__fail
msgid "Some checks failed"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__stock_move_line__check_state__in_progress
msgid "Some checks to be done"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__spreadsheet_id
#: model:quality.point.test_type,name:quality_control.test_type_spreadsheet
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Spreadsheet"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_data
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_data
msgid "Spreadsheet Data"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_file_name
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_file_name
msgid "Spreadsheet File Name"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_snapshot
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__spreadsheet_template_id
msgid "Spreadsheet Template"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__spreadsheet_binary_data
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_stage_view_tree
msgid "Stage Name"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_point__standard_deviation
msgid "Standard Deviation"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__quality_state
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
msgid "Status"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_move
msgid "Stock Move"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__move_line_id
msgid "Stock Move Line"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__spreadsheet_check_cell
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__check_cell
#: model:ir.model.fields,field_description:quality_control.field_quality_point__spreadsheet_check_cell
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__check_cell
msgid "Success cell"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_tag_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_tag_view_tree
msgid "Tags"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_search
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Team"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "Team Name"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_point_kanban
msgid "Team:"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_tree
msgid "Teams"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__test_type
msgid "Technical name"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_search
msgid "Test Type"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_tree
msgid "Testing % Within Lot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__testing_percentage_within_lot
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__testing_percentage_within_lot
#: model:ir.model.fields,field_description:quality_control.field_quality_point__testing_percentage_within_lot
msgid "Testing Percentage Within Lot"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,help:quality_control.field_quality_check__spreadsheet_check_cell
#: model:ir.model.fields,help:quality_control.field_quality_check_spreadsheet__check_cell
#: model:ir.model.fields,help:quality_control.field_quality_point__spreadsheet_check_cell
#: model:ir.model.fields,help:quality_control.field_quality_spreadsheet_template__check_cell
msgid ""
"The check is successful if the success cell value is TRUE. If there are "
"several sheets, specify which one you want to use (e.g. Sheet2!C4). If not "
"specified, the first sheet is selected by default."
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/wizard/on_demand_quality_check_wizard.py:0
msgid "The selected lot/serial number does not exist in the picking."
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check_spreadsheet__thumbnail
#: model:ir.model.fields,field_description:quality_control.field_quality_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_alert__title
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__title
msgid "Title"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
msgid "Tolerance"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__product_tracking
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__product_tracking
msgid "Tracking"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_stock_picking
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_tree
msgid "Transfer"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Transfer-987"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_check_view_form
msgid "Type"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_point_kanban
msgid "Type:"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_point_view_form_inherit_quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Unit of Measure"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Validate"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Value near threshold."
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.worksheet_page
msgid "Visual Check"
msgstr ""

#. module: quality_control
#: model:ir.model.fields,field_description:quality_control.field_quality_check__warning_message
#: model:ir.model.fields,field_description:quality_control.field_quality_check_wizard__warning_message
msgid "Warning Message"
msgstr ""

#. module: quality_control
#: model:ir.model.fields.selection,name:quality_control.selection__quality_point__measure_frequency_unit__week
msgid "Weeks"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_check_wizard
msgid "Wizard for Quality Check Pop Up"
msgstr ""

#. module: quality_control
#: model:ir.model,name:quality_control.model_quality_check_on_demand
msgid "Wizard to select on-demand quality check points"
msgstr ""

#. module: quality_control
#: model:ir.actions.report,name:quality_control.quality_check_report
msgid "Worksheet Report - External (PDF)"
msgstr ""

#. module: quality_control
#: model:ir.actions.report,name:quality_control.quality_check_report_internal
msgid "Worksheet Report - Internal (PDF)"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.view_quality_check_wizard
msgid "Write quality check notes..."
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
#: code:addons/quality_control/wizard/on_demand_quality_check_wizard.py:0
msgid ""
"You can not create quality check for a draft, done or cancelled transfer."
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/quality.py:0
msgid ""
"You measured %(measure).2f %(unit)s and it should be between "
"%(tolerance_min).2f and %(tolerance_max).2f %(unit)s."
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/wizard/quality_check_wizard.py:0
msgid "You must provide a picture before validating"
msgstr ""

#. module: quality_control
#. odoo-python
#: code:addons/quality_control/models/stock_picking.py:0
msgid "You still need to do the quality checks!"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "alias"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "e.g. The QA Masters"
msgstr ""

#. module: quality_control
#: model_terms:ir.ui.view,arch_db:quality_control.quality_alert_team_view_form
msgid "e.g. mycompany.com"
msgstr ""
