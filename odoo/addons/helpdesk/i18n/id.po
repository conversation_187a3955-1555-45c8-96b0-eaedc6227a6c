# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk
# 
# Translators:
# Wil Odo<PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Abe Manyo, 2025\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__answered_customer_message_count
msgid "# Exchanges"
msgstr "# Pertukaran"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__open_ticket_count
msgid "# Open Tickets"
msgstr "# Tiket Terbuka"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_count
msgid "# Ratings"
msgstr "# Rating"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_policy_count
msgid "# SLA Policy"
msgstr "# Kebijakan SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__urgent_ticket
msgid "# Urgent Ticket"
msgstr "# Tiket Mendesak"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/res_partner.py:0
msgid "%(partner_name)s's Tickets"
msgstr "Tiket %(partner_name)s"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_sla.py:0
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "%s (copy)"
msgstr "%s (salin)"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "(any of these tags)"
msgstr "(salah satu dari tag ini)"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_1
msgid "2 days to start"
msgstr "2 hari sebelum mulai"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_2
msgid "7 days to finish"
msgstr "7 hari untuk selesai"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_3
msgid "8 hours to finish"
msgstr "8 jam untuk selesai"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "<b class=\"tip_title\">Tip: Create tickets from incoming emails</b>"
msgstr "<b class=\"tip_title\">Tip: Buat tiket dari email masuk</b>"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "<b>Drag &amp; drop</b> the card to change the stage of your ticket."
msgstr "<b>Drag &amp; drop</b> kartu untuk merubah tahap tiket Anda."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"<b>Log notes</b> for internal communications (you will only notify the "
"persons you specifically tag). Use <b>@ mentions</b> to ping a colleague or "
"<b># mentions</b> to contact a group of people."
msgstr ""
"<b>Catatan log</b> untuk komunikasi internal (yang akan menotifikasi orang yang Anda tag). \n"
"    Gunakan <b>@ mentions</b> untuk ping rekan atau <b># mentions</b> untuk menghubungi kelompok orang."

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.solved_ticket_request_email_template
msgid ""
"<div>\n"
"        Dear <t t-out=\"object.sudo().partner_id.name or 'Madam/Sir'\">Madam/Sir</t>,<br/><br/>\n"
"        We would like to inform you that we have closed your ticket (reference <t t-out=\"object.id or ''\">15</t>). \n"
"        We trust that the services provided have met your expectations and that you have found a satisfactory resolution to your issue.<br/><br/>\n"
"        However, if you have any further questions or comments, please do not hesitate to reply to this email to re-open your ticket. \n"
"        Our team is always here to help you and we will be happy to assist you with any further concerns you may have.<br/><br/>\n"
"        Thank you for choosing our services and for your cooperation throughout this process. We truly value your business and appreciate the opportunity to serve you.<br/><br/>\n"
"        Kind regards,<br/><br/>\n"
"        <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team.\n"
"    </div>\n"
"        "
msgstr ""
"<div>\n"
"        Kepada <t t-out=\"object.sudo().partner_id.name or 'Madam/Sir'\">Nyonya/Tuan</t>,<br/><br/>\n"
"        Kami ingin menginformasikan Anda bahwa kami telah menutup tiket Anda (referensi <t t-out=\"object.id or ''\">15</t>). \n"
"        Kami yakin layanan yang disediakan memenuhi ekspektasi Anda dan Anda mendapati resolusi yang memuaskan untuk masalah Anda.<br/><br/>\n"
"        Namun, bila Anda memiliki pertanyaan atau komentar lebih lanjut, mohon jangan sungkan untuk membalas email ini untuk membuka ulang tiket Anda. \n"
"        Tim kami di sini selalu siap untuk membantu Anda dan akan dengan bahagia membantu Anda dengan masalah tambahan apapun yang Anda mungkin miliki.<br/><br/>\n"
"        Terima kasih telah memilih layanan kami dan untuk kooperasi Anda selama proses ini. Kami betul-betul menghargai bisnis Anda dan menghargai kesempatan ini untuk melayani Anda.<br/><br/>\n"
"        Salam hormat,<br/><br/>\n"
"        Tim <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t>.\n"
"    </div>\n"
"        "

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.rating_ticket_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the ticket \"<strong t-out=\"object.name or ''\">Table legs are unbalanced</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/><br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 14px; text-align:center;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <br/><br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your ticket has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In Progress</b>.</span>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Halo <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Halo,<br/>\n"
"            </t>\n"
"            Mohon mengambil sedikit waktu untuk menilai layanan kami terkait tiket \"<strong t-out=\"object.name or ''\">Kaki meja tidak seimbang</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                yang ditugaskan ke <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/><br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 14px; text-align:center;\">\n"
"                    <strong>Mohon beritahu kami perasaan Anda mengenai layanan kami</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(klik salah satu emoji berikut)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            Kami menghargai feedback Anda. Feedback Anda membantu kami terus menerus berkembang.\n"
"            <br/><br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">Survei pelanggan ini dikirim karena tiket Anda dipindahkan ke tahap <b t-out=\"object.stage_id.name or ''\">Sedang Berlangsung</b>.</span>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"        "

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.new_ticket_request_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.sudo().partner_id.name or object.sudo().partner_name or 'Madam/Sir'\">Madam/Sir</t>,<br/><br/>\n"
"    Your request\n"
"    <t t-if=\"hasattr(object.team_id, 'website_id') and object.get_portal_url()\">\n"
"        <a t-attf-href=\"{{ object.team_id.website_id.domain }}/my/ticket/{{ object.id }}/{{ object.access_token }}\" t-out=\"object.name or ''\">Table legs are unbalanced</a>\n"
"    </t>\n"
"    has been received and is being reviewed by our <t t-out=\"object.team_id.name or ''\">VIP Support</t> team.<br/><br/>\n"
"    The reference for your ticket is <strong><t t-out=\"object.ticket_ref or ''\">15</t></strong>.<br/><br/>\n"
"\n"
"    To provide any additional information, simply reply to this email.<br/><br/>\n"
"    <t t-if=\"object.team_id.show_knowledge_base\">\n"
"        Don't hesitate to visit our <a t-attf-href=\"{{ object.team_id.get_knowledge_base_url() }}\">Help Center</a>. You might find the answer to your question.\n"
"        <br/><br/>\n"
"    </t>\n"
"    <t t-if=\"object.team_id.allow_portal_ticket_closing\">\n"
"        Feel free to close your ticket if our help is no longer needed. Thank you for your collaboration.<br/><br/>\n"
"    </t>\n"
"\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"hasattr(object.team_id, 'website_id') and object.team_id.use_website_helpdesk_form\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\" t-att-href=\"'%s%s' % (object.team_id.website_id.domain or '', object.get_portal_url())\" target=\"_blank\">View Ticket</a>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.get_portal_url()\" target=\"_blank\">View Ticket</a>\n"
"        </t>\n"
"        <t t-if=\"hasattr(object.team_id, 'website_id') and object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'%s/my/ticket/close/%s/%s' % (object.team_id.website_id.domain or '', object.id, object.access_token)\" target=\"_blank\">Close Ticket</a>\n"
"        </t>\n"
"        <t t-elif=\"object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'/my/ticket/close/%s/%s' % (object.id, object.access_token)\" target=\"_blank\">Close Ticket</a>\n"
"        </t>\n"
"        <t t-if=\"object.team_id.use_website_helpdesk_forum or object.team_id.use_website_helpdesk_knowledge or object.team_id.use_website_helpdesk_slides\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.team_id.feature_form_url\" target=\"_blank\">Visit Help Center</a>\n"
"        </t><br/><br/>\n"
"    </div>\n"
"\n"
"    Best regards,<br/><br/>\n"
"    <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    Kepada <t t-out=\"object.sudo().partner_id.name or object.sudo().partner_name or 'Madam/Sir'\">Nyonya/Tuan</t>,<br/><br/>\n"
"    Permintaan Anda\n"
"    <t t-if=\"hasattr(object.team_id, 'website_id') and object.get_portal_url()\">\n"
"        <a t-attf-href=\"{{ object.team_id.website_id.domain }}/my/ticket/{{ object.id }}/{{ object.access_token }}\" t-out=\"object.name or ''\">Kaki meja tidak seimbang</a>\n"
"    </t>\n"
"    telah diterima dan ditinjau oleh tim <t t-out=\"object.team_id.name or ''\">Bantuan VIP</t> kami.<br/><br/>\n"
"    Referensi untuk tiket Anda adalah <strong><t t-out=\"object.ticket_ref or ''\">15</t></strong>.<br/><br/>\n"
"\n"
"    Untuk menyediakan informasi tambahan apa pun, cukup balas email ini.<br/><br/>\n"
"    <t t-if=\"object.team_id.show_knowledge_base\">\n"
"        Jangan ragu untuk mengunjungi <a t-attf-href=\"{{ object.team_id.get_knowledge_base_url() }}\">Pusat Bantuan</a> kami. Anda mungkin menemukan jawaban dari pertanyaan Anda di sana.\n"
"        <br/><br/>\n"
"    </t>\n"
"    <t t-if=\"object.team_id.allow_portal_ticket_closing\">\n"
"        Silakan tutup tiket ini bila bantuan kami tidak diperlukan lagi. Terima kasih untuk kolaborasi Anda.<br/><br/>\n"
"    </t>\n"
"\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"hasattr(object.team_id, 'website_id') and object.team_id.use_website_helpdesk_form\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\" t-att-href=\"'%s%s' % (object.team_id.website_id.domain or '', object.get_portal_url())\" target=\"_blank\">Lihat Tiket</a>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.get_portal_url()\" target=\"_blank\">Lihat Tiket</a>\n"
"        </t>\n"
"        <t t-if=\"hasattr(object.team_id, 'website_id') and object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'%s/my/ticket/close/%s/%s' % (object.team_id.website_id.domain or '', object.id, object.access_token)\" target=\"_blank\">Tutup Tiket</a>\n"
"        </t>\n"
"        <t t-elif=\"object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'/my/ticket/close/%s/%s' % (object.id, object.access_token)\" target=\"_blank\">Tutup Tiket</a>\n"
"        </t>\n"
"        <t t-if=\"object.team_id.use_website_helpdesk_forum or object.team_id.use_website_helpdesk_knowledge or object.team_id.use_website_helpdesk_slides\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.team_id.feature_form_url\" target=\"_blank\">Kunjung Pusat Bantuan</a>\n"
"        </t><br/><br/>\n"
"    </div>\n"
"\n"
"    Salam hormat,<br/><br/>\n"
"    Tim <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t>\n"
"</div>\n"
"        "

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Domain alias\" role=\"img\" aria-"
"label=\"Domain alias\"/>&amp;nbsp;"
msgstr ""
"<i class=\"fa fa-envelope-o\" title=\"Alias domain\" role=\"img\" aria-"
"label=\"Domain alias\"/>&amp;nbsp;"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg < 3.66\" title=\"Puas\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg < 2.33 or rating_avg >= 3.66\" title=\"Ok\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg >= 2.33\" title=\"Tidak Puas\"/>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<i class=\"fa fa-lg fa-clock-o me-2 mt-1\" aria-label=\"Sla Deadline\" "
"title=\"Sla Deadline\"/>"
msgstr ""
"<i class=\"fa fa-lg fa-clock-o me-2 mt-1\" aria-label=\"Sla Deadline\" "
"title=\"Deadline Sla\"/>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\" role=\"img\"/><span class=\"ms-2\">To use an "
"email alias, the first step is to configure an Alias Domain. You can achieve"
" this by navigating to the General Settings and configuring the "
"corresponding field.</span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\" role=\"img\"/><span class=\"ms-2\">Untuk "
"menggunakan alias email, langkah pertama adalah untuk mengonfigurasi Alias "
"Domain. Anda dapat meraih ini dengan menavigasi ke Pengaturan Umum dan "
"mengonfigurasi field yang sesuai.</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                    <span class=\"ms-2\">A rating request will automatically be sent by email to the customer when their ticket reaches the corresponding stage with the email template set.</span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                    <span class=\"ms-2\">Permintaan rating akan secara otomatis dikirim melalui email ke pelanggan saat tiket mereka mencapai tahap yang sesuai dengan templat email yang ditetapkan.</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                <span class=\"ms-2\">\n"
"                                    Type <b>/ticket</b> to create tickets<br/>\n"
"                                    Type <b>/search_tickets</b> to find tickets<br/>\n"
"                                </span>"
msgstr ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                <span class=\"ms-2\">\n"
"                                    Ketik <b>/ticket</b> untuk membuat tiket<br/>\n"
"                                    Ketik <b>/search_tickets</b> untuk mencari tiket<br/>\n"
"                                </span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/>&amp;nbsp;"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Set an Alias Domain"
msgstr "<i class=\"oi oi-arrow-right\"/> Tetapkan Domain Alias"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-muted\">Assigned to</small>"
msgstr "<small class=\"text-muted\">Ditugaskan ke</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">Pelanggan</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "<small>#</small>"
msgstr "<small>#</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small>Stage:</small>"
msgstr "<small>Tahap:</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not is_partner_phone_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"Dengan menyimpan perubahan ini, nomor telepon pelanggan juga akan "
"diperbarui.\" invisible=\"not is_partner_phone_update\"/>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_activity
msgid "<span class=\"m-1\"/>#"
msgstr "<span class=\"m-1\"/>#"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "<span class=\"o_field_widget o_readonly_modifier\">Working Hours</span>"
msgstr "<span class=\"o_field_widget o_readonly_modifier\">Jam Kerja</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text order-2\">Open</span>"
msgstr "<span class=\"o_stat_text order-2\">Buka</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text order-2\">Tickets</span>"
msgstr "<span class=\"o_stat_text order-2\">Tiket</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Avg. Rating\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Rating Rata-Rata\n"
"                                </span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_partner_form_inherit_helpdesk
msgid "<span class=\"o_stat_text\"> Tickets</span>"
msgstr "<span class=\"o_stat_text\"> Tiket</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text\">Rating</span>"
msgstr "<span class=\"o_stat_text\">Rating</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Failed</span>"
msgstr "<span class=\"text-muted text-nowrap\">Gagal</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Open</span>"
msgstr "<span class=\"text-muted text-nowrap\">Buka</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Unassigned</span>"
msgstr "<span class=\"text-muted text-nowrap\">Unassigned</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Urgent</span>"
msgstr "<span class=\"text-muted text-nowrap\">Urgent</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span><b>Followers </b></span>"
msgstr "<span><b>Pengikut </b></span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Average Rating</span>"
msgstr "<span>Rating Rata-Rata</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Reporting</span>"
msgstr "Laporan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>SLA Success Rate</span>"
msgstr "<span>Tingkat Kesuksesan SLA</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Tickets Closed</span>"
msgstr "<span>Tiket Ditutup</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>View</span>"
msgstr "<span>Lihat</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid ""
"<span>Your ticket has successfully been closed. Thank you for your "
"collaboration.</span>"
msgstr ""
"<span>Tiket Anda sukses ditutup. Terima kasih untuk kolaborasi Anda.</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span>days of inactivity</span>"
msgstr "<span>hari tidak aktif</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-3\">Reported on</strong>"
msgstr "<strong class=\"col-lg-3\">Dilaporkan pada</strong>"

#. module: helpdesk
#: model_terms:web_tour.tour,rainbow_man_message:helpdesk.helpdesk_tour
msgid ""
"<strong><b>Good job!</b> You walked through all steps of this tour.</strong>"
msgstr ""
"<strong><b>Kerja bagus!</b> Anda melalui semua langkah-langkah tur "
"ini.</strong>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>After</strong>"
msgstr "<strong>Setelah</strong>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>Alias </strong>"
msgstr "<strong>Alias </strong>"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Kamus Python yang akan dievaluasi untuk memberikan nilai baku ketika membuat"
" catatan baru untuk alias ini."

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_tag_name_uniq
msgid "A tag with the same name already exists."
msgstr "Tag dengan nama yang sama sudah tersedia."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__description
msgid "About Team"
msgstr "TentangTim"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Accept Emails From"
msgstr "Menerima Email Dari"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_warning
msgid "Access warning"
msgstr "Peringatan akses"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr "Tindakan Diperluka"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__active
msgid "Active"
msgstr "Aktif"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_ids
msgid "Activities"
msgstr "Aktivitas"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorasi Pengecualian Aktivitas"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_state
msgid "Activity State"
msgstr "Status Aktivitas"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon Jenis Aktifitas"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.mail_activity_type_action_config_helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config_activity_type
msgid "Activity Types"
msgstr "Jenis Aktivitas"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Adapt your <b>pipeline</b> to your workflow by adding <b>stages</b> <i>(e.g."
" Awaiting Customer Feedback, etc.).</i>"
msgstr ""
"Adaptasikan <b>pipeline</b> Anda ke workflow Anda dengan menambahkan "
"<b>tahap</b> <i>(contoh Menunggu Feedback Pelanggan, dsb.).</i>"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_team_action
msgid ""
"Adapt your pipeline to your workflow and track the progress of your tickets."
msgstr ""
"Adaptasikan pipeline Anda ke workflow Anda dan lacak kemajuan tiket Anda."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Add details about this ticket..."
msgstr "Tambahkan detail mengenai tiket ini..."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Add your stage and place it at the right step of your workflow by dragging &"
" dropping it."
msgstr ""
"Tambahkan tahap Anda dan taruh di langkah yang tepat pada workflow Anda "
"dengan drag & drop."

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_manager
msgid "Administrator"
msgstr "Administrator"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "After-Sales"
msgstr "Purna Jual"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_id
msgid "Alias"
msgstr "Alias"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_contact
msgid "Alias Contact Security"
msgstr "Alias Kontak Keamanan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain_id
msgid "Alias Domain"
msgstr "Alias Domain"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain
msgid "Alias Domain Name"
msgstr "Nama Alias Domain"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_full_name
msgid "Alias Email"
msgstr "Alias Email"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_name
msgid "Alias Name"
msgstr "Nama Alias"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_status
msgid "Alias Status"
msgstr "Status Alias"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Status alias dinilai pada pesan terakhir yang diterima."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_model_id
msgid "Aliased Model"
msgstr "Model Alias"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "All"
msgstr "Semua"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_all
msgid "All Tickets"
msgstr "Semua Tiket"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__internal
msgid "All internal users (company)"
msgstr "Semua user internal (perusahaan)."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Allow customers to help each other on a forum. Share answers from your "
"tickets directly."
msgstr ""
"Izinkan pelanggan untuk membantu satu sama lain di forum. Bagikan jawaban "
"dari tiket Anda secara langsung."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Allow your customers to close their own tickets"
msgstr "Izinkan pelanggan untuk menutup tiket mereka sendiri"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Archive Stages"
msgstr "Arsip Tahap"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Archived"
msgstr "Diarsipkan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "Apakah Anda yakin ingin melanjutkan?"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "Apakah Anda yakin ingin menghapus tahap-tahap ini?"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
msgid "Assigned"
msgstr "Ditugaskan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__user_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__user_id
msgid "Assigned To"
msgstr "Ditetapkan ke"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__user_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Assigned to"
msgstr "Ditugaskan untuk"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Assignee"
msgstr "Petugas"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__assign_method
msgid "Assignment Method"
msgstr "Metode penugasan"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid ""
"At this time, there is no customer preview available to show. The current "
"ticket cannot be accessed by the customer, as it belongs to a helpdesk team "
"that is not publicly available, or there is no customer associated with the "
"ticket."
msgstr ""
"Pada waktu ini, tidak ada pratinjau pelanggan yang tersedia untuk "
"ditunjukkan. Tiket saat ini tidak dapat diakses oleh pelanggan, karena "
"berasal dari tim meja bantuan yang tidak tersedia secara publik, atau tidak "
"ada pelanggan yang terkait tiket tersebut."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_attachment_count
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr "Hitungan Lampiran"

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_auto_assignment
msgid "Auto Assigment"
msgstr "Penugasan Otomatis"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Automate the assignment of new tickets to the right people, and make sure "
"all tickets are being handled"
msgstr ""
"Otomatiskan penugasan tiket baru ke orang yang tepat, dan pastikan semua "
"tiket ditangani"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_assignment
msgid "Automatic Assignment"
msgstr "Penugasan Otomatis"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_ticket
msgid "Automatic Closing"
msgstr "Penutupan Otomatis"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Average"
msgstr "Rata-rata"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__avg_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__avg_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__avg_response_hours
msgid "Average Hours to Respond"
msgstr "Rata-Rata Jam sebelum Menanggapi"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__rating_avg
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Average Rating"
msgstr "Rata-Rata Rating"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Rating Rata-Rata (%)"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Average Rating for the Past 7 Days"
msgstr "Rating Rata-Rata untuk 7 Hari Terakhir"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Dissatisfied"
msgstr "Rating Rata-Rata: Tidak Puas"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Okay"
msgstr "Rating Rata-Rata: Okay"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Satisfied"
msgstr "Rating Rata-Rata: Puas"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Average rating daily target"
msgstr "Target harian rating rata-rata"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Average rating for the last 7 days"
msgstr "Rating rata-rata untuk 7 hari terakhir"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg Last 7 days"
msgstr "Rata-Rata 7 Hari Terakhir"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg Open Hours"
msgstr "Rata-Rata Jam Buka"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Bad"
msgstr "Bad"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Bill the time spent on your tickets to your customers"
msgstr "Tagih waktu yang digunakan pada tiket Anda ke pelanggan Anda"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_new
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_solved
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__blocked
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Blocked"
msgstr "Diblokir"

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_crm
msgid "CRM"
msgstr "CRM"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__campaign_id
msgid "Campaign"
msgstr "Kampanye"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_cancelled
msgid "Cancelled"
msgstr "Dibatalkan"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_canned_response_menu
msgid "Canned Responses"
msgstr "Tanggapan Otomatis"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Centralize, manage, share and grow your knowledge library. Allow customers "
"to search your articles in the help center for answers to their questions."
msgstr ""
"Pusatkan, kelola, dan tumbuhkan pustaka pengetahuan Anda. Izinkan pelanggan "
"untuk mencari artikel Anda di pusat bantuan untuk jawaban pertanyaan mereka."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Channels"
msgstr "Saluran"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Click to Set Your Daily Rating Target"
msgstr "Klik untuk Menetapkan Target Rating Harian Anda"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.xml:0
msgid "Click to set"
msgstr "Klik untuk menetapkan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close"
msgstr "Tutup"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close Ticket"
msgstr "Tutup Tiket"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_date
msgid "Close date"
msgstr "Tutup tanggal"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Close inactive tickets automatically"
msgstr "Tutup tiket yang tidak aktif secara otomatis"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close ticket"
msgstr "Tutup tiket"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Closed"
msgstr "Ditutup"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Closed On"
msgstr "Ditutup Pada"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Closed Tickets"
msgstr "Tiket ditutup"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_close_analysis
msgid "Closed Tickets Analysis"
msgstr "Analisis Tiket Tertutup"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__closed_by_partner
msgid "Closed by Partner"
msgstr "Ditutup oleh mitra"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__close_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__close_date
msgid "Closing Date"
msgstr "Tanggal Penutupan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__allow_portal_ticket_closing
msgid "Closure by Customers"
msgstr "Ditutup oleh Pelanggan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__color
msgid "Color"
msgstr "Warna"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__color
msgid "Color Index"
msgstr "Indeks Warna"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Comment"
msgstr "Komentar"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__commercial_partner_id
msgid "Commercial Entity"
msgstr "Entitas Komersial"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Communication history"
msgstr "Sejarah komunikasi"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_forum
msgid "Community Forum"
msgstr "Forum Komunitas"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__company_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Company"
msgstr "Perusahaan"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config
msgid "Configuration"
msgstr "Konfigurasi"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Confirm"
msgstr "Konfirmasi"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/wizard/helpdesk_stage_delete.py:0
msgid "Confirmation"
msgstr "Konfirmasi"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "Congratulations!"
msgstr "Selamat!"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_partner
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Contact"
msgstr "Kontak"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_coupons
msgid "Coupons"
msgstr "Kupon"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Create Date"
msgstr "Tanggal dibuat"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.email_template_action_helpdesk
msgid "Create a new template"
msgstr "Buat template baru"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid ""
"Create teams to organize your tickets by expertise or geographical region, "
"and define a different workflow for each team."
msgstr ""
"Buat tim untuk mengatur tiket-tiket Anda berdasarkan keahlian atau lokasi "
"geografis, dan tetapkan workflow yang berbeda untuk setiap tim."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Create tickets by sending an email to an alias"
msgstr "Buat tiket dengan mengirimkan email ke alias"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
msgid "Create tickets to get statistics."
msgstr "Buat tiket untuk mendapatkan statistik."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Creation Date"
msgstr "Tanggal Pembuatan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Criteria"
msgstr "Kriteria"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Current stage of this ticket"
msgstr "Stage saat ini untuk tiket ini"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Pesan Kustom yang Dikembalikan"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Customer"
msgstr "Pelanggan"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/models/res_company.py:0
#: model:helpdesk.team,name:helpdesk.helpdesk_team1
msgid "Customer Care"
msgstr "Customer Care"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_email
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_email
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_email
msgid "Customer Email"
msgstr "Email Pelanggan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_name
msgid "Customer Name"
msgstr "Nama Pelanggan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_phone
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_phone
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_phone
msgid "Customer Phone"
msgstr "Telepon Pelanggan"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__access_url
msgid "Customer Portal URL"
msgstr "Customer Portal URL"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.action_open_customer_preview
msgid "Customer Preview"
msgstr "Pratinjau Pelanggan"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.rating_rating_action_helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_rating
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_ratings
msgid "Customer Ratings"
msgstr "Rating Pelanggan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__partner_ids
msgid "Customers"
msgstr "Pelanggan"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid "Customers will be added to the followers of their tickets."
msgstr "Pelanggan akan ditambahkan ke pengikut tiket mereka sendiri."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Daily Target"
msgstr "Target Harian"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Datetime at which the SLA stage was reached for the first time"
msgstr "Tanggal waktu di mana tahap SLA diraih untuk pertama kalinya"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_hours
msgid ""
"Day to reach the stage of the SLA, without taking the working calendar into "
"account"
msgstr "Hari untuk mencapai tahap SLA, tanpa memperhitungkan kalender kerja"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__deadline
msgid "Deadline"
msgstr "Batas waktu"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_graph/helpdesk_ticket_graph_model.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_kanban/helpdesk_ticket_kanban_header.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_list/helpdesk_ticket_list_renderer.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_pivot/helpdesk_ticket_pivot_model.js:0
msgid "Deadline reached"
msgstr "Deadline tercapai"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_defaults
msgid "Default Values"
msgstr "Nilai Baku"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.unlink_helpdesk_stage_action
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Delete"
msgstr "Hapus"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Delete Stage"
msgstr "Hapus Tahap"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Describe your team to your colleagues and customers..."
msgstr "Deskripsikan tim Anda ke rekan kerja dan pelanggan..."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__description
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Description"
msgstr "Deskripsi"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Description of the policy..."
msgstr "Keterangan kebijakan..."

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_digest_digest
msgid "Digest"
msgstr "Digest"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Discard"
msgstr "Buang"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Dissatisfied"
msgstr "Tidak puas"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "Tidak punya akses, lewati data ini untuk email singkat pengguna"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__balanced
msgid "Each user has an equal number of open tickets"
msgstr "Setiap user memiliki jumlah tiket terbuka yang sama"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__randomly
msgid "Each user is assigned an equal number of tickets"
msgstr "Setiap user diberikan jumlah tiket yang sama"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Edit"
msgstr "Edit"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_email
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
msgid "Email Alias"
msgstr "Email Alias"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__template_id
msgid "Email Template"
msgstr "Template Email"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__template_id
msgid ""
"Email automatically sent to the customer when the ticket reaches this stage.\n"
"By default, the email will be sent from the email alias of the helpdesk team.\n"
"Otherwise it will be sent from the company's email address, or from the catchall (as defined in the System Parameters)."
msgstr ""
"Email seara otomatis dikirim ke pelanggan saat tiket mencapai tahap.\n"
"Secara default, email akan dikirim ke alias email tim helpdesk.\n"
"Jika tidak akan dikirim dari alamat email perusahaan, atau dari catchall (seperti didefinisikan di Parameter Sistem)."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Domain email misal 'contoh.com' di '<EMAIL>'"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "Emails sent to"
msgstr "Email dikirim ke"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid ""
"Emails sent to a Helpdesk Team alias generate tickets in your pipeline."
msgstr ""
"Email yang dikirim ke alias Tim Helpdesk membuat tiket di pipeline Anda. "

#. module: helpdesk
#: model:mail.template,description:helpdesk.rating_ticket_request_email_template
msgid "Enable \"customer ratings\" feature on the helpdesk team"
msgstr "Aktifkan fitur \"rating pelanggan\" pada tim helpdesk"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Enter the <b>subject</b> of your ticket <br/><i>(e.g. Problem with my "
"installation, Wrong order, etc.).</i>"
msgstr ""
"Masukkan <b>subject</b> tiket Anda <br/><i>(contoh Masalah dengan "
"penginstalan saya, Order salah, dsb.).</i>"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__exceeded_hours
msgid "Exceeded Working Hours"
msgstr "Melampaui Jam Kerja"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid "Excluding Stages"
msgstr "Tidak Termasuk Tahap"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Extra Info"
msgstr "Info Tambahan"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__failed
msgid "Failed"
msgstr "Gagal"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_fail
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_fail
msgid "Failed SLA Policy"
msgstr "Kebijakan SLA Gagal"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_failed
msgid "Failed SLA Ticket"
msgstr "Tiket SLA Gagal"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Failed Tickets"
msgstr "Tiket Gagal"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_fsm
msgid "Field Service"
msgstr "Layanan Lapangan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "First Assignment Date"
msgstr "Tanggal Tugas Pertama"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__assign_date
msgid "First assignment date"
msgstr "Tanggal tugas pertama"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__fold
msgid "Folded in Kanban"
msgstr "Dilipat di Kanban"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Follow All Team's Tickets"
msgstr "Ikuti Semua Tiket Tim"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
msgid "Follow all your helpdesk tickets"
msgstr "Ikuti semua tiket meja bantuan Anda"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Followed"
msgstr "Diikuti"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_follower_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_partner_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Mitra)"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Ikon font awesome, misalnya fa-tasks"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Future Activities"
msgstr "Kegiatan - Kegiatan Mendatang"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Get in touch with your website visitors, and engage them with scripted "
"chatbot conversations. Create and search tickets from your conversations."
msgstr ""
"Hubungi pengunjung website Anda, dan libatkan mereka dengan percakapan "
"chatbot. Buat dan cari tiket dari percakapan-percakapan Anda."

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_dashboard_action
msgid ""
"Get statistics on your tickets and how long it takes to assign and resolve "
"them."
msgstr ""
"Dapatkan statistik mengenai tiket Anda dan berapa lama waktu yang dibutuhkan"
" untuk menugaskan dan menyelesaikannya."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get tickets through an online form"
msgstr "Dapatkan tiket melalui formulir online"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Grant discounts, free products or free shipping"
msgstr "Berikan diskon, produk gratis atau pengiriman gratis"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Grant employees access to your helpdesk team or tickets by adding them as "
"followers. Employees automatically get access to the tickets they are "
"assigned to."
msgstr ""
"Berikan karyawan akses ke tim helpdesk atau tiket Anda dengan menambahkan "
"mereka sebagai pengikut. Karyawan secara otomatis mendapatkan akses ke tiket"
" yang mereka ditugasi."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Grant portal users access to your helpdesk team or tickets by adding them as"
" followers. Customers automatically get access to their tickets in their "
"portal."
msgstr ""
"Berikan user portal akses ke tim helpdesk atau tiket Anda dengan menambahkan"
" mereka sebagai pengikut. Pelanggan secara otomatis mendapatkan akses ke "
"tiket mereka di portal mereka."

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__done
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__done
msgid "Green"
msgstr "Hijau"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_done
msgid "Green Kanban Label"
msgstr "Label Kanban Hijau"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__normal
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__normal
msgid "Grey"
msgstr "Abu-Abu"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Label Kanban Abu-Abu"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Happy"
msgstr "Senang"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Happy face"
msgstr "Muka bahagia"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__has_message
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached
msgid "Has SLA reached"
msgstr "Apakah SLA dicapai"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached_late
msgid "Has SLA reached late"
msgstr "Apakah SLA tercapai terlambat"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid "Hello"
msgstr "Halo"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Help Center"
msgstr "Pusat Bantuan"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.menu_helpdesk_root
#: model_terms:ir.ui.view,arch_db:helpdesk.digest_digest_view_form
msgid "Helpdesk"
msgstr "Helpdesk"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_dashboard_action_main
msgid "Helpdesk Overview"
msgstr "Gambaran Umum Meja Bantuan"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "Kebijakan SLA Mejabantuan"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage
msgid "Helpdesk Stage"
msgstr "Tahap Mejabantuan"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage_delete_wizard
msgid "Helpdesk Stage Delete Wizard"
msgstr "Wizard Penghapusan Tahap Mejabantuan"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_tag
msgid "Helpdesk Tags"
msgstr "Tag Mejabantuan"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model,name:helpdesk.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__team_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Helpdesk Team"
msgstr "Tim Helpdesk"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "Helpdesk Team Search"
msgstr "Pencarian Tim Meja Bantuan"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__team_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__team_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_menu
msgid "Helpdesk Teams"
msgstr "Tim-Tim Meja Bantuan"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Helpdesk Ticket"
msgstr "Tiket Helpdesk"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.ir_cron_auto_close_ticket_ir_actions_server
msgid "Helpdesk Ticket: Automatically close the tickets"
msgstr "Tiket Mejabantuan: Otomatis tutup tiket"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Helpdesk Tickets"
msgstr "Tiket Mejabantua"

#. module: helpdesk
#: model:mail.template,name:helpdesk.solved_ticket_request_email_template
msgid "Helpdesk: Ticket Closed"
msgstr "Mejabantuan: Tiket Ditutup"

#. module: helpdesk
#: model:mail.template,name:helpdesk.rating_ticket_request_email_template
msgid "Helpdesk: Ticket Rating Request"
msgstr "Mejabantuan: Permintaan Rating Tiket"

#. module: helpdesk
#: model:mail.template,name:helpdesk.new_ticket_request_email_template
msgid "Helpdesk: Ticket Received"
msgstr "Meja Bantuan: Tiket Diterima"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "High Priority"
msgstr "Prioritas Tinggi"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__2
msgid "High priority"
msgstr "Prioritas tinggi"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "History"
msgstr "Riwayat"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_open_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_open_hours
msgid "Hours Open"
msgstr "Jam Buka"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__first_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__first_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__first_response_hours
msgid "Hours to First Response"
msgstr "Jam menuju Tanggapan Pertama"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "ID"
msgstr "ID"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID dari catatan induk alias (contoh: proyek yang menahan pembuatan alias "
"tugas)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon untuk menunjukkan sebuah aktivitas pengecualian."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Jika dicentang, pesan baru memerlukan penanganan dan perhatian Anda."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Jika dicentang, beberapa pesan mempunyai kesalahan dalam pengiriman."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Jika di tetapkan, konten ini akan secara otomatis dikirimkan untuk pengguna "
"tanpa wewenang daripada pesan default."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_normal:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_normal:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_normal:helpdesk.stage_new
#: model:helpdesk.stage,legend_normal:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_normal:helpdesk.stage_solved
#: model:helpdesk.stage,name:helpdesk.stage_in_progress
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "In Progress"
msgstr "Dalam Proses"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__from_stage_ids
msgid "In Stages"
msgstr "Di Tahap"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__normal
msgid "In progress"
msgstr "Dalam proses"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_day
msgid "Inactive Period(days)"
msgstr "Periode Tidak Aktif (hari)"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__invited_internal
msgid "Invited internal users (private)"
msgstr "User internal yang diundang (privat)"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr "User portal yang diundang dan semua user internal (publik)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_is_follower
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr "Adalah Follower"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Issue credits notes"
msgstr "Terbitkan nota kredit"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON yang mempetakan ID dari field many2one ke detik yang digunakan "

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Kanban diblokir penjelasan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Kanban berkelanjutan penjelasan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__kanban_state
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__kanban_state
msgid "Kanban State"
msgstr "Status Kanban"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state_label
msgid "Kanban State Label"
msgstr "Label Status Kanban"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_done
msgid "Kanban Valid Explanation"
msgstr "Kanban berlaku penjelasan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_knowledge
msgid "Knowledge"
msgstr "Pengetahuan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 3 months"
msgstr "3 bulan terakhir"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 30 Days"
msgstr "30 Hari Terakhir"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 30 days"
msgstr "30 hari terakhir"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 365 Days"
msgstr "365 Hari Terakhir"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 7 Days"
msgstr "7 Hari Terakhir"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 7 days"
msgstr "7 hari terakhir"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__date_last_stage_update
msgid "Last Stage Update"
msgstr "Update Tahap Terakhir"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Late Activities"
msgstr "Aktifitas terakhir"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Latest Ratings"
msgstr "Rating Terkini"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's create your first <b>ticket</b>."
msgstr "Pertama mari buat <b>Anda</b>."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Let's go back to the <b>kanban view</b> to get an overview of your next "
"tickets."
msgstr ""
"Mari kembali ke <b>tampilan kanban</b> untuk mendapatkan gambaran umum tiket"
" Anda yang berikutnya."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's view your <b>team's tickets</b>."
msgstr "Mari lihat <b>tiket tim</b> Anda."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr "Live Chat"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Local-part based incoming detection"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Low Priority"
msgstr "Prioritas Rendah"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__0
msgid "Low priority"
msgstr "Prioritas rendah"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
msgid ""
"Make sure tickets are handled in a timely manner by using SLA Policies.<br>"
msgstr ""
"Pastikan tiket ditangani tepat waktu dengan menggunakan Kebijakan SLA.<br>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Make sure tickets are handled on time"
msgstr "Pastikan tiket ditangani tepat waktu"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "Make sure tickets are handled on time by using SLA Policies.<br>"
msgstr ""
"Pastikan tiket ditangani tepat waktu dengan menggunakan Kebijakan SLA.<br>"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__time
msgid ""
"Maximum number of working hours a ticket should take to reach the target "
"stage, starting from the date it was created."
msgstr ""
"Jumlah total jam kerja maksimum yang tiket harus capai untuk meraih tahap "
"target, dimulai dari tanggal tiket dibuat."

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tickets are solved."
msgstr ""
"Ukur kepuasan pelanggan Anda dengan mengirimkan permintaan rating saat tiket"
" Anda selesai."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__medium_id
msgid "Medium"
msgstr "Media"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Medium Priority"
msgstr "Prioritas Medium"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__1
msgid "Medium priority"
msgstr "Prioritas medium"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_mail_message
msgid "Message"
msgstr "Pesan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr "Kesalahan Pengiriman Pesan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr "Pesan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__priority
msgid "Minimum Priority"
msgstr "Prioritas Minimal"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__stage_id
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__sla_stage_id
msgid "Minimum stage a ticket needs to reach in order to satisfy this SLA."
msgstr "Tahap minimum yang tiket harus capai untuk memenuhi SLA ini."

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_module_module
msgid "Module"
msgstr "Modul"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__to_stage_id
msgid "Move to Stage"
msgstr "Pergi ke Tahap"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Deadline Kegiatan Saya"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "My Deadline"
msgstr "Deadline Saya"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "My Performance"
msgstr "Kinerja Saya"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_my
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_my
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "My Tickets"
msgstr "Tiket Saya"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__name
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_list_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Name"
msgstr "Nama"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Neutral face"
msgstr "Muka netral"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: model:helpdesk.stage,name:helpdesk.stage_new
msgid "New"
msgstr "Baru"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__assign_method
msgid ""
"New tickets will automatically be assigned to the team members that are "
"available, according to their working hours and their time off."
msgstr ""
"Tiket baru akan secara otomatis ditugaskan ke anggota tim yang tersedia, "
"tergantung jam kerja dan cuti mereka."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Newest"
msgstr "Terbaru"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Kalender Acara Aktivitas Berikutnya"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Batas Waktu Aktivitas Berikutnya"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_summary
msgid "Next Activity Summary"
msgstr "Ringkasan Aktivitas Berikutnya"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_id
msgid "Next Activity Type"
msgstr "Tipe Aktivitas Berikutnya"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "No Customer"
msgstr "Tidak ada Pelanggan"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "No SLA policies found. Let's create one!"
msgstr "Tidak ada kebijakan SLA yang ditemukan. Ayo buat baru!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.mail_activity_type_action_config_helpdesk
msgid "No activity types found. Let's create one!"
msgstr "Tidak ada tipe kegiatan yang ditemukan. Ayo buat baru!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_dashboard_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_dashboard_action
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid "No data yet!"
msgstr "Belum ada data!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_team_action
msgid "No stages found. Let's create one!"
msgstr "Tidak ada tahap yang ditemukan. Ayo buat baru!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "No tags found. Let's create one!"
msgstr "Tidak ada tag yang ditemukan. Ayo buat baru!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "No teams found"
msgstr "Tidak ada tim yang ditemukan"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "No teams found. Let's create one!"
msgstr "Tidak ada tim yang ditemukan. Ayo buat baru!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
msgid "No tickets found"
msgstr "Tidak ada tiket yang ditemuka"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid "No tickets found. Let's create one!"
msgstr "Tidak ada tiket yang ditemukan. Ayo buat baru!"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "None"
msgstr "Tidak Ada"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Action"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_failed
msgid "Number of SLAs Failed"
msgstr "Jumlah SLA yang gagal"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__ticket_count
msgid "Number of Tickets"
msgstr "Jumlah Tiket"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah kesalahan"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Jumlah pesan yang membutuhkan tindakan"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah pesan dengan kesalahan pengiriman"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Number of open tickets with at least one SLA failed."
msgstr "Jumlah tiket terbuka dengan setidaknya satu SLA yang gagal."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_open_ticket_count
msgid "Number of other open tickets from the same partner"
msgstr "Jumlah tiket terbuka lainnya dari mitra yang sama"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_count
msgid "Number of other tickets from the same partner"
msgstr "Jumlah tiket lainnya dari mitra yang sama"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Number of tickets closed in the past 7 days."
msgstr "Jumlah tiket yang tertutup dalam 7 hari terakhir"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Okay"
msgstr "Okay"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_on_hold
msgid "On Hold"
msgstr "Tertahan"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__ongoing
msgid "Ongoing"
msgstr "Terus-menerus"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Open"
msgstr "Terbuka"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Open Tickets"
msgstr "Tiket Terbuka"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__open_hours
msgid "Open Time (hours)"
msgstr "Jam Buka (jam)"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Open the ticket."
msgstr "Buka tiket."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"ID Opsional dari file (catatan) di mana semua pesan masuk akan dilampirkan, "
"bahkan jika mereka tidak menjawabnya. Jika diatur, hal ini akan "
"menonaktifkan pembuatan catatan baru sepenuhnya."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Our Ratings"
msgstr "Rating Kami"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_team_dashboard
msgid "Overview"
msgstr "Overview"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid "Parent Model"
msgstr "Model Induk"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Catatan Induk ID File"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Model induk dengan alias. Model yang memiliki referensi alias ini tidak "
"selalu berarti model yang diberikan oleh alias_model_id (contoh: proyek "
"(parent_model) dan tugas (model))"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_ids
msgid "Partner Tickets"
msgstr "Tiket Mitra"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__privacy_visibility
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__team_privacy_visibility
msgid ""
"People to whom this helpdesk team and its tickets will be visible.\n"
"\n"
"- Invited internal users: internal users can access the team and the tickets they are following. This access can be modified on each ticket individually by adding or removing the user as follower.\n"
"A user with the helpdesk > administrator access right level can still access this team and its tickets, even if they are not explicitely part of the followers.\n"
"\n"
"- All internal users: all internal users can access the team and all of its tickets without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the team and all of its tickets without distinction.\n"
"Portal users can only access the tickets they are following. This access can be modified on each ticket individually by adding or removing the portal user as follower."
msgstr ""
"Orang-orang ini akan dapat melihat tim meja bantuan dan tiket mereka.\n"
"\n"
"- User internal yang diundang: user internal dapat mengakses tim dan tiket yang mereka ikuti. Akses ini dapat dimodifikasi untuk setiap tiket secara individu dengan menambahkan atau menghapus user sebagai pengikut.\n"
"User dengan meja bantuan > tingkat hak akses administrator masih dapat mengakses tim ini dan tiketnya, bahkan bila mereka bukan merupakan bagian pengikut.\n"
"\n"
"- Semua user internal: semua user internal dapat mengakses tim dan semua tiketnya tanpa pembedaan khusus.\n"
"\n"
"- User portal dan internal yang diundang: semua user internal dapat mengakses tim dan semua tiketnya tanpa pembedaan.\n"
"User portal hanya dapat mengakses tiket yang mereka ikuti. Akses ini dapat dimodifikasi pada setiap tiket secara individu dengan menambahkan atau menghapus user portal sebagai pengikut."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "People to whom this team and its tickets will be visible"
msgstr "Orang yang mana tim ini dan tiketnya akan tampak"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Persentase rating bahagia"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Percentage of tickets that were closed without failing any SLAs."
msgstr "Persentase tiket yang ditutup tanpa gagal dalam SLA apapun."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid ""
"Percentage of tickets whose SLAs have successfully been reached on time over"
" the total number of tickets closed within the past 7 days."
msgstr ""
"Persentase tiket yang mana SLA-nya berhasil dicapai tepat waktu di atas "
"total jumlah tiket yang ditutup di dalam 7 hari."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Performance"
msgstr "Performa"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_graph_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_pivot_analysis
msgid "Performance Analysis"
msgstr "Analisis Performa"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__auto_close_day
msgid "Period of inactivity after which tickets will be automatically closed."
msgstr "Periode tidak aktif setelah mana tiket akan secara otomatis ditutup."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Phone"
msgstr "Telepon"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Plan onsite interventions"
msgstr "Rencanakan intervensi di tempat"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a number."
msgstr "Mohon masukkan angka."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a percentage below 100."
msgstr "Mohon masukkan persentase di bawah 100."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a positive value."
msgstr "Mohon masukkan nilai positif."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a value less than or equal to 5."
msgstr "Masukkan value kurang dari atau sama dengan 5."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Kebijakan untuk mengirimkan pesan di dokumen menggunakan mailgateway.\n"
"- semua orang: setiap orang dapat mengirim\n"
"- rekanan: hanya rekanan yang diijinkan\n"
"- pengikut: hanya pengikut dokumen terkait\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_url
msgid "Portal Access URL"
msgstr "Portal Access URL"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Portal users will be removed from the followers of the team and its tickets."
msgstr "User portal akan dihapus dari pengikut tim dan tiketnya."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__priority
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Priority"
msgstr "Prioritas"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__properties
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Properties"
msgstr "Properti"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Rating"
msgstr "Rating"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__rating_last_value
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__rating_last_value
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_pivot_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_graph_inherit_helpdesk
msgid "Rating (1-5)"
msgstr "Rating (1-5)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg_text
msgid "Rating Avg Text"
msgstr "Menilai Rata-Rata Teks"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Nilai Feedback Terakhir"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr "Nilai Gambar Terakhir"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr "Nilai Value Terakhir"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Nilai Kepuasan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_text
msgid "Rating Text"
msgstr "Nilai Teks"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr "Jumlah nilai"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_ids
msgid "Ratings"
msgstr "Rating"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
msgid "Reach Stage"
msgstr "Mencapai Tahap"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__reached
msgid "Reached"
msgstr "Mencapai"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Reached Date"
msgstr "Reached Date"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_done:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_done:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_done:helpdesk.stage_new
#: model:helpdesk.stage,legend_done:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_done:helpdesk.stage_solved
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__done
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Ready"
msgstr "Siap"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Receive notifications whenever tickets are created, rated or discussed on in"
" this team"
msgstr ""
"Terima notifikasi kapanpun tiket dibuat, dirating atau didiskusikan di tim "
"ini"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Rekam ID Thread"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__blocked
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__blocked
msgid "Red"
msgstr "Merah"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Label Kanban Merah"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Reference"
msgstr "Referensi"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_refund_status
msgid "Refund Status"
msgstr "Status Refund"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_credit_notes
msgid "Refunds"
msgstr "Pengembalian"

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_repair
msgid "Repair"
msgstr "Perbaikan"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_repair_status
msgid "Repair Status"
msgstr "Status Perbaikan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_repairs
msgid "Repairs"
msgstr "Perbaikan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Reported on"
msgstr "Dilaporkan pada"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_main
msgid "Reporting"
msgstr "Laporan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_user_id
msgid "Responsible User"
msgstr "Tanggung-jawab"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Restore"
msgstr "Pulihkan"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_return_status
msgid "Return Status"
msgstr "Status Pengembalian"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Return faulty products"
msgstr "Mengembalikkan produk cacat"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_returns
msgid "Returns"
msgstr "Pengembalian"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA"
msgstr "SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_deadline
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Deadline"
msgstr "Deadline SLA"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__failed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Failed"
msgstr "SLA Gagal"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action_main
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_users__sla_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_sla_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "SLA Policies"
msgstr "Kebijakan-kebijakan SLA"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,help:helpdesk.field_res_users__sla_ids
msgid ""
"SLA Policies that will automatically apply to the tickets submitted by this "
"customer."
msgstr ""
"Kebijakan SLA yang akan secara otomatis diterapkan ke tiket yang pelanggan "
"ini kirim."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "SLA Policy"
msgstr "Kebijakan SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__description
msgid "SLA Policy Description"
msgstr "Deskripsi Kebijakan SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_stage_id
msgid "SLA Stage"
msgstr "Tahap SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_status_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_status_ids
msgid "SLA Status"
msgstr "Status SLA"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_dashboard_action
#: model:ir.model,name:helpdesk.model_helpdesk_sla_report_analysis
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_sla_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "SLA Status Analysis"
msgstr "Analisis Status SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_fail
msgid "SLA Status Failed"
msgstr "Status SLA Gagal"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_success
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_success
msgid "SLA Status Success"
msgstr "Status SLA Sukses"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__reached
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Success"
msgstr "SLA Sukses"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "SLA Success Rate"
msgstr "Tingkat Sukses SLA"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__ongoing
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA in Progress"
msgstr "SLA Sedang Berlangsung"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "SLAs"
msgstr "SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Kesalahan Pengiriman SMS`"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Sad face"
msgstr "Muka sedih"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Sample"
msgstr "Sampel"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Satisfied"
msgstr "Puas"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Save this ticket and the modifications you've made to it."
msgstr ""
"Simpan tiket ini dan modifikasi yang Anda sudah buat ke tiket tersebut."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Schedule your <b>activity</b>."
msgstr "Jadwalkan <b>kegiatan</b> Anda."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
msgid "Search SLA Policies"
msgstr "Cari Kebijakan SLA"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Assigned to"
msgstr "Cari di Ditugaskan ke"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Customer"
msgstr "Cari di Pelanggan"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Helpdesk Team"
msgstr "Cari di Tim Helpdesk"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Stage"
msgstr "Cari di Tahap"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search%(left)s Tickets%(right)s"
msgstr "Cari %(left)s Tiket %(right)s"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_token
msgid "Security Token"
msgstr "Token Keamanan"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Select the <b>customer</b> of your ticket."
msgstr "Pilih <b>pelanggan</b> tiket Anda."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Self-Service"
msgstr "Self-Service"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.action_helpdesk_ticket_mass_mail
msgid "Send Email"
msgstr "Kirim Email"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Send broken products for repair"
msgstr "Kirim produk rusak untuk diperbaiki"

#. module: helpdesk
#: model:mail.template,description:helpdesk.new_ticket_request_email_template
msgid ""
"Send customers a confirmation email to notify them that their helpdesk "
"ticket has been received and is currently being reviewed by the helpdesk "
"team. Automatically send an email to customers when a ticket reaches a "
"specific stage in a helpdesk team by setting this template on that stage."
msgstr ""
"Kirim pelanggan email konfirmasi untuk menotifikasi mereka bahwa tiket "
"helpdesk telah diterima dan saat ini sedang ditinjau oleh tim meja bantuan. "
"Secara otomatis kirim email ke pelanggan saat tiket mencapai tahap tertentu "
"di tim meja bantuan dengan menyiapkan templat ini untuk tahap tersebut."

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_service
msgid "Service"
msgstr "Layanan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set an Email Template on Stages"
msgstr "Tetapkan Templat Email pada Tahap"

#. module: helpdesk
#: model:mail.template,description:helpdesk.solved_ticket_request_email_template
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"Tetapkan templat pada tahap project untuk mengotomatiskan email saat task "
"sampai di tahap"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Settings"
msgstr "Pengaturan"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.portal_share_action
msgid "Share Ticket"
msgstr "Bagikan Tiket"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Share presentations and videos, and organize them into courses. Allow "
"customers to search your eLearning courses in the help center for answers to"
" their questions."
msgstr ""
"Bagikan presentasi dan video, dan atur menjadi kursus. Izinkan pelanggan "
"untuk mencari kursus eLearning Anda di pusat bantuan untuk jawaban "
"pertanyaan mereka."

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_rating
msgid "Show Customer Ratings"
msgstr "Tunjukkan Rating Pelangga"

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_sla
msgid "Show SLA Policies"
msgstr "Tunjukkan Kebijakan SLA"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Show all records which has next action date is before today"
msgstr "Tampilkan semua dokumen dengan aksi berikut sebelum hari ini"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__sla_id
msgid "Sla"
msgstr "Sla"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_solved
msgid "Solved"
msgstr "Dipecahkan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__source_id
msgid "Source"
msgstr "Sumber"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__stage_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Stage"
msgstr "Tahapan"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_stage
#: model:mail.message.subtype,name:helpdesk.mt_ticket_stage
msgid "Stage Changed"
msgstr "Tahapan Diubah"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Search"
msgstr "Pencarian Tahap"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__stage_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_stage_menu
msgid "Stages"
msgstr "Tahap"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "Tahap Untuk Dihapu"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__stage_ids
msgid ""
"Stages the team will use. This team's tickets will only be able to be in "
"these stages."
msgstr ""
"Tahap-tahap yang tim akan gunakan. Tiket tim ini hanya akan berada di tahap-"
"tahap ini."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__status
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Status"
msgstr "Status"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status berdasarkan aktivitas\n"
"Terlambat: Batas waktu telah terlewati\n"
"Hari ini: Tanggal aktivitas adalah hari ini\n"
"Direncanakan: Aktivitas yang akan datang."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__duration_tracking
msgid "Status time"
msgstr "Waktu status"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__name
msgid "Subject"
msgstr "Judul"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_success
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__success_rate
msgid "Success Rate"
msgstr "Tingkat Kesuksesan"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_success
msgid "Success Rate Analysis"
msgstr "Analisis Tingkat Kesuksesa"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_success
msgid "Success SLA Policy"
msgstr "Kebijakan Kesuksesan SLA"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_tree
msgid "Tag"
msgstr "Tag"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_tag_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__tag_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_tag_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Tags"
msgstr "Label"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Tags are perfect for organizing your tickets."
msgstr "Tag merupakan cara terbaik untuk mengatur tiket-tiket Anda."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Target"
msgstr "Target"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__stage_id
msgid "Target Stage"
msgstr "Target Tahap"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
msgid "Team"
msgstr "Tim"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__member_ids
msgid "Team Members"
msgstr "Anggota tim"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_team_action
msgid "Team Stages"
msgstr "Tahap-Tahap Tim"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.email_template_action_helpdesk
msgid "Templates"
msgstr "Contoh"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Model (Jenis Dokumen Odoo) di mana alias ini dikaitkan. Email masuk yang "
"tidak menjawab catatan yang sudah ada akan menyebabkan pembuatan rekor baru "
"model ini (misalnya tugas proyek)"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Nama email alias, misalnya 'pekerjaan' jika Anda ingin menangkap email untuk"
" <<EMAIL>>"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "The team does not allow ticket closing through portal"
msgstr "Tim tidak memungkinkan penutupan tiket melalui portal"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid ""
"The time spent in these stages won't be taken into account in the "
"calculation of the SLA."
msgstr ""
"Waktu yang digunakan di tahap-tahap ini tidak akan diperhitungkan dalam "
"kalkulasi SLA."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"The visibility of the team needs to be set as \"Invited portal users and all"
" internal users\" in order to use the website form."
msgstr ""
"Visibilitas tim harus ditetapkan sebagai \"User portal yang diundang dan "
"semua user internal\" agar dapat menggunakan website form."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "There are currently no Ticket for your account."
msgstr "Saat ini tidak ada Tiket untuk akun Anda."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "This"
msgstr "Ini"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid ""
"This SLA Policy will apply to tickets matching ALL of the following "
"criteria:"
msgstr ""
"Kebijakan SLA ini akan diterapkan ke tiket yang cocok dengan SEMUA kriteria "
"berikut:"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Ini adalah nama yang membantu Anda melacak usaha kampanye yang berbeda-beda,"
" contohnya Fall_Drive, Natal_Spesial"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Ini adalah metode pengiriman, contohnya Kartu Pos, Email, atau Iklan Banner"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Ini adalah sumber link, contohnya Search Engine, domain lain, atau nama pada"
" daftar email"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "This ticket was closed %s hours after its SLA deadline."
msgstr "Tiket ini sudah ditutup %s jam setelah deadline SLA."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "This ticket was successfully closed %s hours before its SLA deadline."
msgstr "Tiket ini sudah ditutup %s jam sebelum deadlien SLA."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
msgid ""
"This will archive the stages and all of the tickets they contain from the "
"following teams:"
msgstr ""
"Ini akan mengarsip tahap-tahap dan semua tiket yang mereka miliki dari tim-"
"tim berikut:"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.mail_activity_type_action_config_helpdesk
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"Mereka mewakili kategori-kategori berbeda untuk kegiatan yang harus Anda "
"lakukan (contohnya \"Telepon\" atau \"Kirim email\")."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Three stars, maximum score"
msgstr "3 bintang, skor maksimum"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_activity
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Ticket"
msgstr "Tiket"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_dashboard_action
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_report_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_analysis
msgid "Ticket Analysis"
msgstr "Analisis Tiket"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_closed
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_closed
msgid "Ticket Closed"
msgstr "Tiket Ditutup"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.solved_ticket_request_email_template
msgid "Ticket Closed - Reference {{ object.id if object.id else 15 }}"
msgstr "Tiket Ditutup - Referensi {{ object.id if object.id else 15 }}"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__ticket_count
msgid "Ticket Count"
msgstr "Jumlah Tiket"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_new
#: model:mail.message.subtype,name:helpdesk.mt_ticket_new
msgid "Ticket Created"
msgstr "Tiket Dibuat"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__create_date
msgid "Ticket Creation Date"
msgstr "Tanggal Pembuatan Tiket"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_deadline
msgid "Ticket Deadline"
msgstr "Deadline Tiket"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_ref
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__ticket_ref
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_ref
msgid "Ticket IDs Sequence"
msgstr "Urutan ID Tiket"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_properties
msgid "Ticket Properties"
msgstr "Properti Tiket"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_rated
#: model:mail.message.subtype,name:helpdesk.mt_ticket_rated
msgid "Ticket Rated"
msgstr "Tiket Dirating"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla_status
msgid "Ticket SLA Status"
msgstr "Status SLA Tiket"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.quick_create_ticket_form
msgid "Ticket Title"
msgstr "Judul Tiket"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Ticket closed by the customer"
msgstr "Tiket ditutup oleh pelangga"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_new
msgid "Ticket created"
msgstr "Tiket dibuat"

#. module: helpdesk
#. odoo-javascript
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_my_ticket_action_no_create
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_unassigned
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__ticket_count
#: model:ir.model.fields,field_description:helpdesk.field_res_users__ticket_count
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_list_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Tickets"
msgstr "Tiket"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_analysis
msgid "Tickets Analysis"
msgstr "Analisis Tiket"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed
msgid "Tickets Closed"
msgstr "Tiket Ditutup"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Tickets Search"
msgstr "Pencarian Tiket"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__fold
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__fold
msgid "Tickets in a folded stage are considered as closed."
msgstr "Tiket yang ditahap dilipat akan dianggap sebagai ditutup."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_sale_timesheet
msgid "Time Billing"
msgstr "Billing Waktu"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_hours
msgid "Time to close (hours)"
msgstr "Waktu untuk ditutup (jam)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_hours
msgid "Time to first assignment (hours)"
msgstr "Waktu untuk tugas pertama (jam)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_timesheet
msgid "Timesheets"
msgstr "Tabel Waktu"

#. module: helpdesk
#: model:digest.tip,name:helpdesk.digest_tip_helpdesk_0
msgid "Tip: Create tickets from incoming emails"
msgstr "Tip: Buat tiket dari email masuk"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid ""
"To get things done, plan activities and use the ticket status.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"Agar kerjaan selesai, rencanakan kegiatan dan gunakan status tiket.<br>\n"
"                Kolaborasi efisien dengan mengobrol secara real-time atau melalui email."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Today"
msgstr "Hari Ini"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Today Activities"
msgstr "Aktivitas Hari ini"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Today's Average Rating"
msgstr "Rating Rata-Rata Hari Ini"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__total_response_hours
msgid "Total Exchange Time in Hours"
msgstr "Total Waktu Penukaran dalam Jam"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track &amp; Bill Time"
msgstr "Lacak &amp; Tagih Waktu"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track customer satisfaction on tickets"
msgstr "Lacak kepuasan pelanggan pada tiket"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_dashboard_action
msgid ""
"Track the performance of your teams, the success rate of your tickets, and "
"how quickly you reach your service level agreements (SLAs)."
msgstr ""
"Lacak performa tim Anda, tingkat kesuksesan tiket Anda, dan seberapa cepat "
"Anda mencapai persetujuan tingkat layanan (SLA)."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track the time spent on tickets"
msgstr "Lacak waktu yang digunakan pada tiket"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Two stars, with a maximum of three"
msgstr "2 bintang, dengan maksimum 3"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Jenis dari aktivitas pengecualian pada rekaman data."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
msgid "Unarchive Tickets"
msgstr "Batalkan Arsip Tiket"

#. module: helpdesk
#. odoo-javascript
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_graph/helpdesk_ticket_graph_model.js:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Unassigned"
msgstr "Tak diserah-tugaskan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__unassigned_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Unassigned Tickets"
msgstr "Batalkan Penugasan Tiket"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unread Messages"
msgstr "Pesan Belum Dibaca"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__3
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Urgent"
msgstr "Mendesak"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Use <b>activities</b> to organize your daily work."
msgstr "Gunakan <b>aktivitas</b> untuk mengatur tugas sehari-hari Anda."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_alias
msgid "Use Alias"
msgstr "Gunakan Alias"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_coupons
msgid "Use Coupons"
msgstr "Gunakan Kupon"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"progress of this ticket."
msgstr ""
"Gunakan chatter untuk <b>mengirim email</b> dan berkomunikasi secara efisien"
" dengan pelanggan Anda. Tambahkan orang baru ke daftar pengikut agar mereka "
"menyadari kemajuan tiket ini."

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_users
#: model:res.groups,name:helpdesk.group_helpdesk_user
msgid "User"
msgstr "Pengguna"

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team3
msgid "VIP Support"
msgstr "Bantuan VIP"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__privacy_visibility
msgid "Visibility"
msgstr "Visibilitas"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Visibility &amp; Assignment"
msgstr "Visibilitas &amp; Tugas"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Want to <b>boost your customer satisfaction</b>?<br/><i>Click Helpdesk to "
"start.</i>"
msgstr ""
"Ingin <b>meningkatkan kepuasan pelanggan Anda</b>?<br/><i>Klik Mejabantuan "
"untuk memulai.</i>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid ""
"We hope to have addressed your request satisfactorily. If you no longer need"
" our assistance, please close this ticket. Thank you for your collaboration."
msgstr ""
"Kami berharap Anda puas dengan cara kami memenuhi permintaan Anda. Bila Anda"
" tidak lagi memelurkan bantuan kami, mohon tutup tiket ini. Terima kasih "
"atas kolaborasi Anda. "

#. module: helpdesk
#: model_terms:helpdesk.team,description:helpdesk.helpdesk_team1
#: model_terms:helpdesk.team,description:helpdesk.helpdesk_team3
msgid ""
"We provide 24/7 support, Monday through Friday. Ticket responses are usually provided within 2 working days.<br>\n"
"            Support is mainly provided in English. We can also assist in Spanish, French, and Dutch."
msgstr ""
"Kami menyediakan bantuan 24/7, Senin sampai Jumat. Tanggapan tiket biasanya diberikan dalam 2 hari kerja.<br>\n"
"            Bantuan secara utama disediakan dalam bahasa Inggris. Kita juga dapat membantu dalam bahasa Spanyol, Prancis, dan Belanda."

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_website
msgid "Website"
msgstr "Website"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_form
msgid "Website Form"
msgstr "Formulir Website"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__website_message_ids
msgid "Website Messages"
msgstr "Pesan situs"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi situs"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__time
msgid "Within"
msgstr "Di dalam"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working Hours"
msgstr "Jam Kerja"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_assignation_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_assignation_hours
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Working Hours to Assign"
msgstr "Jam Kerja untuk Ditugaskan"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_close_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_close_hours
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Working Hours to Close"
msgstr "Jam Kerja untuk Ditutup"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_deadline_hours
msgid "Working Hours until SLA Deadline"
msgstr "Jam Kerja sampai Deadline SLA"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__exceeded_hours
msgid ""
"Working hours exceeded for reached SLAs compared with deadline. Positive "
"number means the SLA was reached after the deadline."
msgstr ""
"Jam Kerja melampaui SLA yang tercapai dibandingkan deadline. Angka positif "
"berarti SLA teraih setelah deadline."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working hours used to determine the deadline of SLA Policies."
msgstr "Jam kerja yang digunakan untuk menentukan deadline Kebijakan SLA."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tickets contained in these stages as "
"well?"
msgstr ""
"Apakah Anda juga ingin membatalkan arsip semua tiket yang didalam tahap-"
"tahap ini?"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid ""
"You cannot delete stages containing tickets. You can either archive them or "
"first delete all of their tickets."
msgstr ""
"Anda tidak dapat menghapus tahap yang memiliki tiket. Anda harus mengarsip "
"mereka atau menghapus semua tiket mereka terlebih dahulu."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid ""
"You cannot delete stages containing tickets. You should first delete all of "
"their tickets."
msgstr ""
"Anda tidak dapat menghapus stage yang memiliki tiket. Anda harus terlebih "
"dahulu menghapus semua tiket mereka."

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_closed_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_rating_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_success_not_zero
msgid "You cannot have negative targets"
msgstr "Anda tidak bisa memiliki target negatif"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "You completed all your tickets on time."
msgstr "Anda menyelesaikan semua tiket Anda tepat waktu."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "You have been invited to follow %s"
msgstr "Anda telah diundang untuk mengikuti %s"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid "You have been invited to follow Ticket Document :"
msgstr "Anda telah diundang untuk mengikuti Dokumen Tiket :"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "alias"
msgstr "alias"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "e.g. Close urgent tickets within 36 hours"
msgstr "contoh Tutup tiket mendesak dalam 36 jam"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. Customer Care"
msgstr "contoh Layanan Pelanggan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. My Company"
msgstr "contoh Perusahaan Saya"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.quick_create_ticket_form
msgid "e.g. Product arrived damaged"
msgstr "contoh Produk datang sudah rusak"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. mycompany.com"
msgstr "contoh. mycompany.com"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_slides
msgid "eLearning"
msgstr "eLearning"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "generate tickets in your pipeline."
msgstr "buat tiket dalam pipeline Anda."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "has been created from ticket:"
msgstr "telah dibuat dari tiket:"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.helpdesk_ratings_server_action
msgid "helpdesk view rating"
msgstr "rating tampilan mejabantuan"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "team search"
msgstr "pencarian tim"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "tickets"
msgstr "tiket"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.rating_ticket_request_email_template
msgid ""
"{{ object.company_id.name or object.user_id.company_id.name or 'Helpdesk' "
"}}: Service Rating Request"
msgstr ""
"{{ object.company_id.name or object.user_id.company_id.name or 'Helpdesk' "
"}}: Permintaan Rating Layanan"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.new_ticket_request_email_template
msgid "{{ object.name }}"
msgstr "{{ object.name }}"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "{{rating.res_name if t['is_helpdesk_user'] else ''}}"
msgstr "{{rating.res_name if t['is_helpdesk_user'] else ''}}"
