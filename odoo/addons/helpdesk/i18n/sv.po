# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk
# 
# Translators:
# <PERSON><PERSON><PERSON> <mika<PERSON>.<PERSON><PERSON>@vertel.se>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> (sile), 2024
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <mika<PERSON>.<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# Lasse L, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON> Krabbe <<EMAIL>>, 2025\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__answered_customer_message_count
msgid "# Exchanges"
msgstr "# Utbyten"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__open_ticket_count
msgid "# Open Tickets"
msgstr "# Öppna ärenden"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_count
msgid "# Ratings"
msgstr "# Betyg"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_policy_count
msgid "# SLA Policy"
msgstr "# SLA-policy"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__urgent_ticket
msgid "# Urgent Ticket"
msgstr "# Brådskande ärende"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/res_partner.py:0
msgid "%(partner_name)s's Tickets"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_sla.py:0
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "%s (copy)"
msgstr "%s (copy)"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "(any of these tags)"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_1
msgid "2 days to start"
msgstr "2 dagar till start"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_2
msgid "7 days to finish"
msgstr "7 dagar för att färdigställa"

#. module: helpdesk
#: model:helpdesk.sla,name:helpdesk.helpdesk_sla_3
msgid "8 hours to finish"
msgstr "8 timmar för att färdigställa"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "<b class=\"tip_title\">Tip: Create tickets from incoming emails</b>"
msgstr ""
"<b class=\"tip_title\">Tips: Skapa ärende från inkommande "
"e-postmeddelanden</b>"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "<b>Drag &amp; drop</b> the card to change the stage of your ticket."
msgstr "<b>Dra &amp; släpp</b> kortet för att ändra etapp på ditt ärende."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"<b>Log notes</b> for internal communications (you will only notify the "
"persons you specifically tag). Use <b>@ mentions</b> to ping a colleague or "
"<b># mentions</b> to contact a group of people."
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.solved_ticket_request_email_template
msgid ""
"<div>\n"
"        Dear <t t-out=\"object.sudo().partner_id.name or 'Madam/Sir'\">Madam/Sir</t>,<br/><br/>\n"
"        We would like to inform you that we have closed your ticket (reference <t t-out=\"object.id or ''\">15</t>). \n"
"        We trust that the services provided have met your expectations and that you have found a satisfactory resolution to your issue.<br/><br/>\n"
"        However, if you have any further questions or comments, please do not hesitate to reply to this email to re-open your ticket. \n"
"        Our team is always here to help you and we will be happy to assist you with any further concerns you may have.<br/><br/>\n"
"        Thank you for choosing our services and for your cooperation throughout this process. We truly value your business and appreciate the opportunity to serve you.<br/><br/>\n"
"        Kind regards,<br/><br/>\n"
"        <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team.\n"
"    </div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.rating_ticket_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the ticket \"<strong t-out=\"object.name or ''\">Table legs are unbalanced</strong>\"\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/><br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 14px; text-align:center;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br/>\n"
"                    <span style=\"text-color: #888888\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 14px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 14px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <br/><br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\">This customer survey has been sent because your ticket has been moved to the stage <b t-out=\"object.stage_id.name or ''\">In Progress</b>.</span>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model:mail.template,body_html:helpdesk.new_ticket_request_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.sudo().partner_id.name or object.sudo().partner_name or 'Madam/Sir'\">Madam/Sir</t>,<br/><br/>\n"
"    Your request\n"
"    <t t-if=\"hasattr(object.team_id, 'website_id') and object.get_portal_url()\">\n"
"        <a t-attf-href=\"{{ object.team_id.website_id.domain }}/my/ticket/{{ object.id }}/{{ object.access_token }}\" t-out=\"object.name or ''\">Table legs are unbalanced</a>\n"
"    </t>\n"
"    has been received and is being reviewed by our <t t-out=\"object.team_id.name or ''\">VIP Support</t> team.<br/><br/>\n"
"    The reference for your ticket is <strong><t t-out=\"object.ticket_ref or ''\">15</t></strong>.<br/><br/>\n"
"\n"
"    To provide any additional information, simply reply to this email.<br/><br/>\n"
"    <t t-if=\"object.team_id.show_knowledge_base\">\n"
"        Don't hesitate to visit our <a t-attf-href=\"{{ object.team_id.get_knowledge_base_url() }}\">Help Center</a>. You might find the answer to your question.\n"
"        <br/><br/>\n"
"    </t>\n"
"    <t t-if=\"object.team_id.allow_portal_ticket_closing\">\n"
"        Feel free to close your ticket if our help is no longer needed. Thank you for your collaboration.<br/><br/>\n"
"    </t>\n"
"\n"
"    <div style=\"text-align: center; padding: 16px 0px 16px 0px;\">\n"
"        <t t-if=\"hasattr(object.team_id, 'website_id') and object.team_id.use_website_helpdesk_form\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size: 13px;\" t-att-href=\"'%s%s' % (object.team_id.website_id.domain or '', object.get_portal_url())\" target=\"_blank\">View Ticket</a>\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.get_portal_url()\" target=\"_blank\">View Ticket</a>\n"
"        </t>\n"
"        <t t-if=\"hasattr(object.team_id, 'website_id') and object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'%s/my/ticket/close/%s/%s' % (object.team_id.website_id.domain or '', object.id, object.access_token)\" target=\"_blank\">Close Ticket</a>\n"
"        </t>\n"
"        <t t-elif=\"object.team_id.allow_portal_ticket_closing\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"'/my/ticket/close/%s/%s' % (object.id, object.access_token)\" target=\"_blank\">Close Ticket</a>\n"
"        </t>\n"
"        <t t-if=\"object.team_id.use_website_helpdesk_forum or object.team_id.use_website_helpdesk_knowledge or object.team_id.use_website_helpdesk_slides\">\n"
"            <a style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\" t-att-href=\"object.team_id.feature_form_url\" target=\"_blank\">Visit Help Center</a>\n"
"        </t><br/><br/>\n"
"    </div>\n"
"\n"
"    Best regards,<br/><br/>\n"
"    <t t-out=\"object.team_id.name or 'Helpdesk'\">Helpdesk</t> Team\n"
"</div>\n"
"        "
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Domain alias\" role=\"img\" aria-"
"label=\"Domain alias\"/>&amp;nbsp;"
msgstr ""
"<i class=\"fa fa-envelope-o\" title=\"Domänalias\" role=\"img\" aria-"
"label=\"Domänalias\"/>&amp;nbsp;"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg < 3.66\" title=\"Nöjd\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg < 2.33 or rating_avg >= 3.66\" title=\"Okej\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg >= 2.33\" title=\"Missnöjd\"/>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<i class=\"fa fa-lg fa-clock-o me-2 mt-1\" aria-label=\"Sla Deadline\" "
"title=\"Sla Deadline\"/>"
msgstr ""
"<i class=\"fa fa-lg fa-clock-o me-2 mt-1\" aria-label=\"Sla Deadline\" "
"title=\"Sla Deadline\"/>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\" role=\"img\"/><span class=\"ms-2\">To use an "
"email alias, the first step is to configure an Alias Domain. You can achieve"
" this by navigating to the General Settings and configuring the "
"corresponding field.</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                    <span class=\"ms-2\">A rating request will automatically be sent by email to the customer when their ticket reaches the corresponding stage with the email template set.</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<i class=\"fa fa-lightbulb-o\"/>\n"
"                                <span class=\"ms-2\">\n"
"                                    Type <b>/ticket</b> to create tickets<br/>\n"
"                                    Type <b>/search_tickets</b> to find tickets<br/>\n"
"                                </span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/>&amp;nbsp;"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<i class=\"oi oi-arrow-right\"/> Set an Alias Domain"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-muted\">Assigned to</small>"
msgstr "<small class=\"text-muted\">Tilldelad till</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">Kund</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "<small>#</small>"
msgstr "<small>#</small>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<small>Stage:</small>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not is_partner_phone_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not is_partner_phone_update\"/>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_activity
msgid "<span class=\"m-1\"/>#"
msgstr "<span class=\"m-1\"/>#"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "<span class=\"o_field_widget o_readonly_modifier\">Working Hours</span>"
msgstr "<span class=\"o_field_widget o_readonly_modifier\">Arbetstid</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text order-2\">Open</span>"
msgstr "<span class=\"o_stat_text order-2\">Öppen</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text order-2\">Tickets</span>"
msgstr "<span class=\"o_stat_text order-2\">Ärenden</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Avg. Rating\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    Genomsnittligt omdöme\n"
"                                </span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_partner_form_inherit_helpdesk
msgid "<span class=\"o_stat_text\"> Tickets</span>"
msgstr "<span class=\"o_stat_text\">Ärenden</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "<span class=\"o_stat_text\">Rating</span>"
msgstr "<span class=\"o_stat_text\">Betyg</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Failed</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Open</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Unassigned</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span class=\"text-muted text-nowrap\">Urgent</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span><b>Followers </b></span>"
msgstr "<span><b>Följare </b></span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Average Rating</span>"
msgstr "<span>Genomsnittligt betyg</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Rapportering</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>SLA Success Rate</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>Tickets Closed</span>"
msgstr "<span>Biljetter stängda</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "<span>View</span>"
msgstr "<span>Visa</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid ""
"<span>Your ticket has successfully been closed. Thank you for your "
"collaboration.</span>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<span>days of inactivity</span>"
msgstr "<span>dagar av inaktivitet</span>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "<strong class=\"col-lg-3\">Reported on</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:web_tour.tour,rainbow_man_message:helpdesk.helpdesk_tour
msgid ""
"<strong><b>Good job!</b> You walked through all steps of this tour.</strong>"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>After</strong>"
msgstr "<strong>Efter</strong>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "<strong>Alias </strong>"
msgstr "<strong>Alias </strong>"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Ett Pythonbibliotek som utvärderar för att tillhandahålla standardvärden när"
" nya poster skapas för detta alias."

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_helpdesk_tag_name_uniq
msgid "A tag with the same name already exists."
msgstr "En tagg med samma namn finns redan."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__description
msgid "About Team"
msgstr "Om teamet"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Accept Emails From"
msgstr "Ta emot e-post från"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_warning
msgid "Access warning"
msgstr "Åtkomstvarning"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction
msgid "Action Needed"
msgstr "Åtgärd krävs"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__active
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__active
msgid "Active"
msgstr "Aktiv"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undantaget dekoration"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_state
msgid "Activity State"
msgstr "Aktivitetsläge"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon för aktivitetstyp"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.mail_activity_type_action_config_helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config_activity_type
msgid "Activity Types"
msgstr "Aktivitetstyper"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Adapt your <b>pipeline</b> to your workflow by adding <b>stages</b> <i>(e.g."
" Awaiting Customer Feedback, etc.).</i>"
msgstr ""
"Anpassa din <b>pipeline</b> till ditt arbetsflöde genom att lägga till "
"<b>steg</b> <i>(t.ex. Inväntar kundfeedback etc.).</i>"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_team_action
msgid ""
"Adapt your pipeline to your workflow and track the progress of your tickets."
msgstr ""
"Anpassa din pipeline till ditt arbetsflöde och följ utvecklingen av dina "
"ärenden."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Add details about this ticket..."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Add your stage and place it at the right step of your workflow by dragging &"
" dropping it."
msgstr ""
"Lägg till din etapp och placera den vid rätt steg i ditt arbetsflöde genom "
"att dra & släppa det."

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_helpdesk_manager
msgid "Administrator"
msgstr "Administratör"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "After-Sales"
msgstr "Eftermarknad"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_id
msgid "Alias"
msgstr "Alias"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_contact
msgid "Alias Contact Security"
msgstr "Alias kontaktsäkerhet"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain_id
msgid "Alias Domain"
msgstr "Aliasdomän"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_domain
msgid "Alias Domain Name"
msgstr "Alias-domännamn"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_full_name
msgid "Alias Email"
msgstr "Alias e-post"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_name
msgid "Alias Name"
msgstr "Aliasnamn"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_status
msgid "Alias Status"
msgstr "Alias status"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Aliasstatus bedömd utifrån det senast mottagna meddelandet."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_model_id
msgid "Aliased Model"
msgstr "Aliasobjekt"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "All"
msgstr "Alla"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_tree
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_all
msgid "All Tickets"
msgstr "Samtliga ärenden"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__internal
msgid "All internal users (company)"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Allow customers to help each other on a forum. Share answers from your "
"tickets directly."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Allow your customers to close their own tickets"
msgstr "Tillåt dina kunder stänga sina egna ärenden"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Archive Stages"
msgstr "Arkivera Lägen"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Archived"
msgstr "Arkiverad"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "Är du säker på att du vill fortsätta?"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "Är du säker på att du vill ta bort dessa steg?"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
msgid "Assigned"
msgstr "Tilldelad"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__user_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__user_id
msgid "Assigned To"
msgstr "Tilldelad Till"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__user_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Assigned to"
msgstr "Tilldelad till"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Assignee"
msgstr "Uppdragstagare"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__assign_method
msgid "Assignment Method"
msgstr "Metod för tilldelning"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid ""
"At this time, there is no customer preview available to show. The current "
"ticket cannot be accessed by the customer, as it belongs to a helpdesk team "
"that is not publicly available, or there is no customer associated with the "
"ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_attachment_count
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_attachment_count
msgid "Attachment Count"
msgstr "Antal bilagor"

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_auto_assignment
msgid "Auto Assigment"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Automate the assignment of new tickets to the right people, and make sure "
"all tickets are being handled"
msgstr ""
"Automatisera tilldelningen av nya ärenden till rätt personer och se till att"
" alla ärenden hanteras"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_assignment
msgid "Automatic Assignment"
msgstr "Automatisk tilldelning"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_ticket
msgid "Automatic Closing"
msgstr "Automatisk stängning"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Average"
msgstr "Genomsnitt"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__avg_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__avg_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__avg_response_hours
msgid "Average Hours to Respond"
msgstr "Genomsnittlig tid för att svara"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__rating_avg
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Average Rating"
msgstr "Snittbetyg"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "Genomsnittligt Betyg (%)"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Average Rating for the Past 7 Days"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Dissatisfied"
msgstr "Genomsnittligt Betyg: Missnöjd"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Okay"
msgstr "Genomsnittligt Betyg: Okej"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Average Rating: Satisfied"
msgstr "Genomsnittligt Betyg: Nöjd"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Average rating daily target"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Average rating for the last 7 days"
msgstr "Genomsnittligt betyg för de senaste 7 dagarna"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg Last 7 days"
msgstr "Genomsnitt Senaste 7 dagarna"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Avg Open Hours"
msgstr "Genomsnittliga öppna timmar"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Bad"
msgstr "Dålig"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Bill the time spent on your tickets to your customers"
msgstr "Fakturera den tid som läggs på dina ärenden till dina kunder"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_new
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_blocked:helpdesk.stage_solved
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__blocked
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Blocked"
msgstr "Blockerad"

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_crm
msgid "CRM"
msgstr "CRM"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__campaign_id
msgid "Campaign"
msgstr "Kampanj"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_cancelled
msgid "Cancelled"
msgstr "Avbruten"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_canned_response_menu
msgid "Canned Responses"
msgstr "Standardiserade svar"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Centralize, manage, share and grow your knowledge library. Allow customers "
"to search your articles in the help center for answers to their questions."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Channels"
msgstr "Kanaler"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Click to Set Your Daily Rating Target"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.xml:0
msgid "Click to set"
msgstr "Klicka för att ställa in"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close"
msgstr "Stäng"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close Ticket"
msgstr "Avsluta ärende"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_date
msgid "Close date"
msgstr "Slutdatum"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Close inactive tickets automatically"
msgstr "Stäng inaktiva ärenden automatiskt"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Close ticket"
msgstr "Stäng ärende"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Closed"
msgstr "Stängd"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Closed On"
msgstr "Stängt på"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Closed Tickets"
msgstr "Stängda ärenden"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_close_analysis
msgid "Closed Tickets Analysis"
msgstr "Analys av stängda ärenden"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__closed_by_partner
msgid "Closed by Partner"
msgstr "Stängd av Partner"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__close_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__close_date
msgid "Closing Date"
msgstr "Slutdatum"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__allow_portal_ticket_closing
msgid "Closure by Customers"
msgstr "Stängd av Kund"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__color
msgid "Color"
msgstr "Färg"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__color
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__color
msgid "Color Index"
msgstr "Färgindex"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Comment"
msgstr "Kommentar"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__commercial_partner_id
msgid "Commercial Entity"
msgstr "Kommersiell enhet"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Communication history"
msgstr "Kommuniktationshistorik"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_forum
msgid "Community Forum"
msgstr "Forum för gemenskapen"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_company
msgid "Companies"
msgstr "Företag"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__company_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__company_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Company"
msgstr "Företag"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_config
msgid "Configuration"
msgstr "Konfiguration"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Confirm"
msgstr "Bekräfta"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/wizard/helpdesk_stage_delete.py:0
msgid "Confirmation"
msgstr "Bekräftelse"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "Congratulations!"
msgstr "Grattis!"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_partner
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Contact"
msgstr "Kontakt"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_coupons
msgid "Coupons"
msgstr "Kuponger"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Create Date"
msgstr "Skapat datum"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.email_template_action_helpdesk
msgid "Create a new template"
msgstr "Skapa en ny mall"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid ""
"Create teams to organize your tickets by expertise or geographical region, "
"and define a different workflow for each team."
msgstr ""
"Skapa team för att organisera dina ärenden efter expertis eller geografisk "
"region, och definiera ett annat arbetsflöde för varje team."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Create tickets by sending an email to an alias"
msgstr "Skapa ärenden genom att skicka ett e-post till en alias"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
msgid "Create tickets to get statistics."
msgstr "Skapa ärenden för att få statistik."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Creation Date"
msgstr "Skapad datum"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Criteria"
msgstr "Kriterier"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Current stage of this ticket"
msgstr "Nuvarande skede av detta ärende"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Anpassat studsade meddelande"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Customer"
msgstr "Kund"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/models/res_company.py:0
#: model:helpdesk.team,name:helpdesk.helpdesk_team1
msgid "Customer Care"
msgstr "Kundvård"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_email
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_email
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_email
msgid "Customer Email"
msgstr "Kund e-post"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_name
msgid "Customer Name"
msgstr "Kundnamn"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__partner_phone
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_phone
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__partner_phone
msgid "Customer Phone"
msgstr "Kundens telefon"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__access_url
msgid "Customer Portal URL"
msgstr "URL till kundportal"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.action_open_customer_preview
msgid "Customer Preview"
msgstr "Förhandsvisning kundvy"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.rating_rating_action_helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_rating
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_ratings
msgid "Customer Ratings"
msgstr "Kundbetyg"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__partner_ids
msgid "Customers"
msgstr "Kunder"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid "Customers will be added to the followers of their tickets."
msgstr "Kunder kommer att läggas till som följare av deras ärenden."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Daily Target"
msgstr "Dagligt mål"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Datetime at which the SLA stage was reached for the first time"
msgstr "Datum då SLA-fasen uppnåddes för första gången"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_hours
msgid ""
"Day to reach the stage of the SLA, without taking the working calendar into "
"account"
msgstr "Dag för att nå SLA-fasen, utan att ta hänsyn till arbetskalendern"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__deadline
msgid "Deadline"
msgstr "Tidsfrist"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_graph/helpdesk_ticket_graph_model.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_kanban/helpdesk_ticket_kanban_header.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_list/helpdesk_ticket_list_renderer.js:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_pivot/helpdesk_ticket_pivot_model.js:0
msgid "Deadline reached"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_defaults
msgid "Default Values"
msgstr "Standardvärden"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.unlink_helpdesk_stage_action
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid "Delete"
msgstr "Radera"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Delete Stage"
msgstr "Radera etapper"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Describe your team to your colleagues and customers..."
msgstr "Beskriv ditt team för dina kollegor och kunder..."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__description
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__description
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Description"
msgstr "Beskrivning"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Description of the policy..."
msgstr "Policybeskrivning..."

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_digest_digest
msgid "Digest"
msgstr "Sammanställning"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid "Discard"
msgstr "Avbryt"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__display_name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Dissatisfied"
msgstr "Missnöjd"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Saknar åtkomsträttighet, hoppa över denna data för användarens sammanställda"
" utskick"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__balanced
msgid "Each user has an equal number of open tickets"
msgstr "Varje användare har samma antal öppna ärenden"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__assign_method__randomly
msgid "Each user is assigned an equal number of tickets"
msgstr "Varje användare tilldelas ett lika stort antal biljetter"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Edit"
msgstr "Redigera"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_email
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
msgid "Email Alias"
msgstr "Alias för e-post"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__template_id
msgid "Email Template"
msgstr "E-postmall"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__template_id
msgid ""
"Email automatically sent to the customer when the ticket reaches this stage.\n"
"By default, the email will be sent from the email alias of the helpdesk team.\n"
"Otherwise it will be sent from the company's email address, or from the catchall (as defined in the System Parameters)."
msgstr ""
"E-post skickas automatiskt till kunden när ärendet når detta skede.\n"
"Som standard kommer e-postmeddelandet att skickas från helpdesk-teamets e-postalias.\n"
"Annars kommer det att skickas från företagets e-postadress, eller från catchall (som definierat i Systemparametrarna)."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__email_cc
msgid "Email cc"
msgstr "E-post CC"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "E-postdomän, t.ex. \"example.com\" i \"<EMAIL>"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "Emails sent to"
msgstr "E-post skickad till"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid ""
"Emails sent to a Helpdesk Team alias generate tickets in your pipeline."
msgstr ""
"E-post som skickas till ett Kundtjänst Team-alias genererar ärenden i din "
"kanal."

#. module: helpdesk
#: model:mail.template,description:helpdesk.rating_ticket_request_email_template
msgid "Enable \"customer ratings\" feature on the helpdesk team"
msgstr "Aktivera funktionen \"kundbetyg\" för helpdesk-teamet"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Enter the <b>subject</b> of your ticket <br/><i>(e.g. Problem with my "
"installation, Wrong order, etc.).</i>"
msgstr ""
"Ange <b>ämnet</b> för ditt ärende <br/><i>(t.ex. Problem med min "
"installation, Felaktig beställning, etc.).</i>"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__exceeded_hours
msgid "Exceeded Working Hours"
msgstr "Överskridna arbetstimmar"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid "Excluding Stages"
msgstr "Uteslutande etapper"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Extra Info"
msgstr "Tilläggsinformation"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__failed
msgid "Failed"
msgstr "Misslyckades"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_fail
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_fail
msgid "Failed SLA Policy"
msgstr "Misslyckad SLA-policy"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__sla_failed
msgid "Failed SLA Ticket"
msgstr "Misslyckat SLA-ärende"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Failed Tickets"
msgstr "Misslyckade ärenden"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_fsm
msgid "Field Service"
msgstr "Fälttjänst"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "First Assignment Date"
msgstr "Första Uppgiftsdatum"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__assign_date
msgid "First assignment date"
msgstr "Första uppgiftsdatum"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__fold
msgid "Folded in Kanban"
msgstr "Ihopfälld i kanban"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Follow All Team's Tickets"
msgstr "Följ Alla Teams Ärenden"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
msgid "Follow all your helpdesk tickets"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Followed"
msgstr "Följda"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_follower_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_follower_ids
msgid "Followers"
msgstr "Följare"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_partner_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_partner_ids
msgid "Followers (Partners)"
msgstr "Följare (kontakter)"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome-ikon t.ex. fa-tasks"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Future Activities"
msgstr "Framtida aktiviteter"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Get in touch with your website visitors, and engage them with scripted "
"chatbot conversations. Create and search tickets from your conversations."
msgstr ""

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_dashboard_action
msgid ""
"Get statistics on your tickets and how long it takes to assign and resolve "
"them."
msgstr ""
"Få statistik över dina ärenden och hur lång tid det tar att tilldela och "
"lösa dem."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Get tickets through an online form"
msgstr "Köp biljetter via ett onlineformulär"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Grant discounts, free products or free shipping"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Grant employees access to your helpdesk team or tickets by adding them as "
"followers. Employees automatically get access to the tickets they are "
"assigned to."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Grant portal users access to your helpdesk team or tickets by adding them as"
" followers. Customers automatically get access to their tickets in their "
"portal."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__done
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__done
msgid "Green"
msgstr "Grön"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_done
msgid "Green Kanban Label"
msgstr "Grön Kanban-etikett"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__normal
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__normal
msgid "Grey"
msgstr "Grå"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Grå Kanban-etikett"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Group By"
msgstr "Grupp Av"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Happy"
msgstr "Nöjd"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Happy face"
msgstr "Lyckligt ansikte"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__has_message
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__has_message
msgid "Has Message"
msgstr "Har meddelande"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached
msgid "Has SLA reached"
msgstr "Har SLA nått"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_reached_late
msgid "Has SLA reached late"
msgstr "Har SLA nått för sent"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid "Hello"
msgstr "Hej"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Help Center"
msgstr "Hjälpcenter"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.menu_helpdesk_root
#: model_terms:ir.ui.view,arch_db:helpdesk.digest_digest_view_form
msgid "Helpdesk"
msgstr "Helpdesk"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_dashboard_action_main
msgid "Helpdesk Overview"
msgstr "Kundtjänstöversikt"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla
msgid "Helpdesk SLA Policies"
msgstr "Kundtjänst SLA Policy"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage
msgid "Helpdesk Stage"
msgstr "Kundtjänst Etapp"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_stage_delete_wizard
msgid "Helpdesk Stage Delete Wizard"
msgstr "Helpdesk Guide för borttagning av steg"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_tag
msgid "Helpdesk Tags"
msgstr "Kundtjänst Taggar"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model,name:helpdesk.model_helpdesk_team
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__team_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__team_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
msgid "Helpdesk Team"
msgstr "Kundtjänstteam"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_search
msgid "Helpdesk Team Search"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_team_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__team_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__team_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_team_menu
msgid "Helpdesk Teams"
msgstr "Kundtjänst Teams"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Helpdesk Ticket"
msgstr "Kundtjänstärende"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.ir_cron_auto_close_ticket_ir_actions_server
msgid "Helpdesk Ticket: Automatically close the tickets"
msgstr "Kundtjänst Ärende: Stäng biljetterna automatiskt"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Helpdesk Tickets"
msgstr "Kundtjänst Ärenden"

#. module: helpdesk
#: model:mail.template,name:helpdesk.solved_ticket_request_email_template
msgid "Helpdesk: Ticket Closed"
msgstr "Helpdesk: Ärendet stängt"

#. module: helpdesk
#: model:mail.template,name:helpdesk.rating_ticket_request_email_template
msgid "Helpdesk: Ticket Rating Request"
msgstr "Helpdesk: Förfrågan om ärendebetyg"

#. module: helpdesk
#: model:mail.template,name:helpdesk.new_ticket_request_email_template
msgid "Helpdesk: Ticket Received"
msgstr "Kundtjänst: Ärende Mottagits"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "High Priority"
msgstr "Hög Prioritet"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__2
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__2
msgid "High priority"
msgstr "Hög prioritet"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "History"
msgstr "Historia"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_open_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_open_hours
msgid "Hours Open"
msgstr "Öppettider"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__first_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__first_response_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__first_response_hours
msgid "Hours to First Response"
msgstr "Timmar till första insats"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "ID"
msgstr "ID"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID på överliggande post med alias (exempel: projekt som har alias för skapa "
"uppgift)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon för att indikera en undantagsaktivitet."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Om markerat, nya meddelanden kräver din uppmärksamhet."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Om markerad, några meddelanden har leveransfel."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Om angivet så kommer detta innehåll att automatiskt skickas ut till "
"obehöriga användare istället för standardmeddelandet."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_normal:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_normal:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_normal:helpdesk.stage_new
#: model:helpdesk.stage,legend_normal:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_normal:helpdesk.stage_solved
#: model:helpdesk.stage,name:helpdesk.stage_in_progress
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "In Progress"
msgstr "Pågående"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__from_stage_ids
msgid "In Stages"
msgstr "I Etapper"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__normal
msgid "In progress"
msgstr "Pågående"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__auto_close_day
msgid "Inactive Period(days)"
msgstr "Inaktiv period (dagar)"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__invited_internal
msgid "Invited internal users (private)"
msgstr "Inbjudna interna användare (privat)"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_team__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr "Inbjudna portalanvändare och alla interna användare (offentliga)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_is_follower
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_is_follower
msgid "Is Follower"
msgstr "Är Följare"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Issue credits notes"
msgstr "Skapa kreditfaktura"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON som mappar id:n från ett many2one-fält till spenderade sekunder"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Kanban blockerad förklaring"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Kanban - löpande förklaring"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__kanban_state
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__kanban_state
msgid "Kanban State"
msgstr "Kanbanetapp"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__kanban_state_label
msgid "Kanban State Label"
msgstr "Kanbanetapp etikett"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__legend_done
msgid "Kanban Valid Explanation"
msgstr "Kanban - giltig förklaring"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_knowledge
msgid "Knowledge"
msgstr "Kunskap"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 3 months"
msgstr "Senaste 3 månaderna"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 30 Days"
msgstr "Senaste 30 dagarna"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 30 days"
msgstr "Senaste 30 dagarna"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 365 Days"
msgstr "De senaste 365 dagarna"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Last 7 Days"
msgstr "Senaste 7 dagarna"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Last 7 days"
msgstr "Senaste 7 dagarna"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__date_last_stage_update
msgid "Last Stage Update"
msgstr "Senaste etappuppdatering"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_uid
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__write_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Late Activities"
msgstr "Sena aktiviteter"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "Latest Ratings"
msgstr "Senaste betygen"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's create your first <b>ticket</b>."
msgstr "Låt oss skapa ditt första <b>ärende</b>."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Let's go back to the <b>kanban view</b> to get an overview of your next "
"tickets."
msgstr ""
"Låt oss gå tillbaka till <b>kanban-vyn</b> för att få en översikt över dina "
"nästa ärenden."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Let's view your <b>team's tickets</b>."
msgstr "Låt oss se ditt <b>teams ärenden</b>."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_livechat
msgid "Live Chat"
msgstr "Live chatt"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Lokal delbaserad inkommande detektering"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Low Priority"
msgstr "Låg prioritet"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__0
msgid "Low priority"
msgstr "Låg prioritet"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
msgid ""
"Make sure tickets are handled in a timely manner by using SLA Policies.<br>"
msgstr ""
"Se till att ärenden hanteras i tid genom att använda SLA-policyer.<br>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Make sure tickets are handled on time"
msgstr "Se till att biljetter hanteras i tid"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "Make sure tickets are handled on time by using SLA Policies.<br>"
msgstr ""
"Se till att ärenden hanteras i tid genom att använda SLA-policyer.<br>"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__time
msgid ""
"Maximum number of working hours a ticket should take to reach the target "
"stage, starting from the date it was created."
msgstr ""
"Maximalt antal arbetstimmar som ett ärende bör ta för att nå måletappen, med"
" början från det datum då det skapades."

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tickets are solved."
msgstr ""
"Mät kundnöjdheten genom att skicka betygsförfrågningar när dina ärenden har "
"lösts."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__medium_id
msgid "Medium"
msgstr "Medium"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Medium Priority"
msgstr "Medelhög Prioritet"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__1
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__1
msgid "Medium priority"
msgstr "Medelhög prioritet"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_ui_menu
msgid "Menu"
msgstr "Meny"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_mail_message
msgid "Message"
msgstr "Meddelande"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error
msgid "Message Delivery error"
msgstr "Meddelande leveransfel"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_ids
msgid "Messages"
msgstr "Meddelanden"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__priority
msgid "Minimum Priority"
msgstr "Lägsta prioritet"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__stage_id
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__sla_stage_id
msgid "Minimum stage a ticket needs to reach in order to satisfy this SLA."
msgstr "Minsta etapp som ett ärende måste nå för att uppfylla detta SLA."

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_ir_module_module
msgid "Module"
msgstr "Modul"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__to_stage_id
msgid "Move to Stage"
msgstr "Flytta till Etapp"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mina aktiviteters deadline"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "My Deadline"
msgstr "Min deadline"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "My Performance"
msgstr "Min prestation"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_main_my
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_my
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "My Tickets"
msgstr "Mina ärenden"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_tag__name
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_list_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Name"
msgstr "Namn"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Neutral face"
msgstr "Neutralt ansikte"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: model:helpdesk.stage,name:helpdesk.stage_new
msgid "New"
msgstr "Ny"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__assign_method
msgid ""
"New tickets will automatically be assigned to the team members that are "
"available, according to their working hours and their time off."
msgstr ""
"Nya ärenden kommer automatiskt att tilldelas de teammedlemmar som är "
"tillgängliga, enligt deras arbetstid och deras ledighet."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Newest"
msgstr "Senaste"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nästa Kalenderhändelse för aktivitet"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nästa slutdatum för aktivitet"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_summary
msgid "Next Activity Summary"
msgstr "Nästa aktivitetssammanställning"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_type_id
msgid "Next Activity Type"
msgstr "Nästa aktivitetstyp"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "No Customer"
msgstr "Ingen kund"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_action_main
msgid "No SLA policies found. Let's create one!"
msgstr "Inga SLA-policyer hittades. Låt oss skapa en!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.mail_activity_type_action_config_helpdesk
msgid "No activity types found. Let's create one!"
msgstr "Inga aktivitetstyper hittades. Låt oss skapa en!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_dashboard_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7days_tickets
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_close_analysis
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_dashboard
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_success
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_analysis_dashboard_action
#: model_terms:ir.actions.act_window,help:helpdesk.rating_rating_action_helpdesk
msgid "No data yet!"
msgstr "Ingen data ännu!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_stage_team_action
msgid "No stages found. Let's create one!"
msgstr "Inga etapper hittades. Låt oss skapa en!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "No tags found. Let's create one!"
msgstr "Inga taggar hittades. Låt oss skapa en!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_dashboard_action_main
msgid "No teams found"
msgstr "Inga team hittades"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_team_action
msgid "No teams found. Let's create one!"
msgstr "Inga team hittades. Låt oss skapa ett!"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
msgid "No tickets found"
msgstr "Inga ärenden hittades"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid "No tickets found. Let's create one!"
msgstr "Inga ärenden hittades. Låt oss skapa ett!"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "None"
msgstr "Inga"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal åtgärder"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_failed
msgid "Number of SLAs Failed"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__ticket_count
msgid "Number of Tickets"
msgstr "Antal ärenden"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fel"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_needaction_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antal meddelanden som kräver åtgärd"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__message_has_error_counter
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal meddelanden med leveransfel"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Number of open tickets with at least one SLA failed."
msgstr "Antal öppna ärenden med minst ett misslyckat SLA."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_open_ticket_count
msgid "Number of other open tickets from the same partner"
msgstr "Antal andra öppna ärenden från samma partner"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_count
msgid "Number of other tickets from the same partner"
msgstr "Antal andra ärenden från samma partner"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Number of tickets closed in the past 7 days."
msgstr "Antal ärenden som avslutats under de senaste 7 dagarna."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Okay"
msgstr "Okej"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_on_hold
msgid "On Hold"
msgstr "Pausad"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__ongoing
msgid "Ongoing"
msgstr "Pågående"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Open"
msgstr "Öppna"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
msgid "Open Tickets"
msgstr "Öppna ärenden"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__open_hours
msgid "Open Time (hours)"
msgstr "Öppen tid (timmar)"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Open the ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Valfritt ID för en tråd (post) som alla inkommande meddelanden kommer att "
"bifogas till, även om de inte svarade på den. Om inställt så kommer detta "
"att inaktivera skapandet av nya poster helt."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Our Ratings"
msgstr "Våra betyg"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_menu_team_dashboard
msgid "Overview"
msgstr "Översikt"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid "Parent Model"
msgstr "Överordnad modell"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Överordnad post tråd ID"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Överordnad modell som innehåller alias. Modellen som innehåller "
"aliasreferensen är inte nödvändigtvis den modell som ges av alias_model_id "
"(exempel: projekt (parent_model) och aktivitet (modell))"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__partner_ticket_ids
msgid "Partner Tickets"
msgstr "Partnerärenden"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__privacy_visibility
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__team_privacy_visibility
msgid ""
"People to whom this helpdesk team and its tickets will be visible.\n"
"\n"
"- Invited internal users: internal users can access the team and the tickets they are following. This access can be modified on each ticket individually by adding or removing the user as follower.\n"
"A user with the helpdesk > administrator access right level can still access this team and its tickets, even if they are not explicitely part of the followers.\n"
"\n"
"- All internal users: all internal users can access the team and all of its tickets without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the team and all of its tickets without distinction.\n"
"Portal users can only access the tickets they are following. This access can be modified on each ticket individually by adding or removing the portal user as follower."
msgstr ""
"Personer för vilka detta helpdesk-team och dess ärenden kommer att vara synliga.\n"
"\n"
"- Inbjudna interna användare: interna användare kan få åtkomst till teamet och de ärenden de följer. Denna åtkomst kan ändras individuellt på varje ärende genom att lägga till eller ta bort användaren som följare.\n"
"En användare med rättighetsnivån helpdesk > administratör kan fortfarande få åtkomst till detta team och dess ärenden, även om de inte uttryckligen är en del av följarna.\n"
"- Alla interna användare: alla interna användare kan få åtkomst till teamet och alla dess ärenden utan distinktion.\n"
"\n"
"- Inbjudna portalanvändare och alla interna användare: alla interna användare kan få åtkomst till teamet och alla dess ärenden utan distinktion.\n"
"Portalanvändare kan bara få åtkomst till de ärenden de följer. Denna åtkomst kan ändras individuellt på varje ärende genom att lägga till eller ta bort portalanvändaren som följare."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "People to whom this team and its tickets will be visible"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Procentandel positiva betyg"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Percentage of tickets that were closed without failing any SLAs."
msgstr "Procentandel ärenden som avslutades utan att några SLA gick fel."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid ""
"Percentage of tickets whose SLAs have successfully been reached on time over"
" the total number of tickets closed within the past 7 days."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Performance"
msgstr "Prestanda"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team_performance
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_graph_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_pivot_analysis
msgid "Performance Analysis"
msgstr "Analys av prestanda"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__auto_close_day
msgid "Period of inactivity after which tickets will be automatically closed."
msgstr ""
"Period av inaktivitet efter vilken ärenden automatiskt kommer att stängas."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
msgid "Phone"
msgstr "Telefon"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Plan onsite interventions"
msgstr "Planera onsite-interventioner"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a number."
msgstr "Ange ett nummer."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a percentage below 100."
msgstr "Ange en procentsats under 100."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a positive value."
msgstr "Ange ett positivt värde."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_target/helpdesk_team_target.js:0
msgid "Please enter a value less than or equal to 5."
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Policy för att publicera ett meddelande på dokumentet med hjälp av mailgateway.\n"
"- alla: alla kan posta\n"
"- partner: endast autentiserade partner\n"
"- följare: endast följare av det relaterade dokumentet eller medlemmar av följande kanaler\n"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_url
msgid "Portal Access URL"
msgstr "URL till portal"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"Portal users will be removed from the followers of the team and its tickets."
msgstr ""
"Portalanvändare kommer att tas bort från teamets följare och dess biljetter."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__priority
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__priority
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Priority"
msgstr "Prioritet"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__properties
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Properties"
msgstr "Fastigheter"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "Rating"
msgstr "Omdöme"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__rating_last_value
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__rating_last_value
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_pivot_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_graph_inherit_helpdesk
msgid "Rating (1-5)"
msgstr "Rating (1-5)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_avg_text
msgid "Rating Avg Text"
msgstr "Genomsnittsbetyg Text"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Betyg Senaste återkoppling"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_image
msgid "Rating Last Image"
msgstr "Betyg Senaste Bild"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_value
msgid "Rating Last Value"
msgstr "Betyg Senaste Värde"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Betyg nöjdhetsgrad"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_last_text
msgid "Rating Text"
msgstr "Omdömestext"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_count
msgid "Rating count"
msgstr "Betyg antal"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__rating_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__rating_ids
msgid "Ratings"
msgstr "Betyg"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
msgid "Reach Stage"
msgstr "Nå Etapp"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_status__status__reached
msgid "Reached"
msgstr "Uppnått"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__reached_datetime
msgid "Reached Date"
msgstr "Uppnådd Datum"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
#: model:helpdesk.stage,legend_done:helpdesk.stage_cancelled
#: model:helpdesk.stage,legend_done:helpdesk.stage_in_progress
#: model:helpdesk.stage,legend_done:helpdesk.stage_new
#: model:helpdesk.stage,legend_done:helpdesk.stage_on_hold
#: model:helpdesk.stage,legend_done:helpdesk.stage_solved
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__kanban_state__done
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Ready"
msgstr "Redo"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Receive notifications whenever tickets are created, rated or discussed on in"
" this team"
msgstr ""
"Få meddelanden när ärenden skapas, betygsätts eller diskuteras i detta team"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Record Thread ID"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__kanban_state__blocked
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__kanban_state__blocked
msgid "Red"
msgstr "Röd"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Röd Kanban Etikett"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Reference"
msgstr "Referens"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_refund_status
msgid "Refund Status"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_credit_notes
msgid "Refunds"
msgstr "Återbetalning"

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_repair
msgid "Repair"
msgstr "Reparera"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_repair_status
msgid "Repair Status"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_repairs
msgid "Repairs"
msgstr "Reparationer"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Reported on"
msgstr "Rapporterad den"

#. module: helpdesk
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_main
msgid "Reporting"
msgstr "Rapportering"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__activity_user_id
msgid "Responsible User"
msgstr "Ansvarig användare"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Restore"
msgstr "Återställ"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_ticket_return_status
msgid "Return Status"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Return faulty products"
msgstr "Returnera felaktiga produkter"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_product_returns
msgid "Returns"
msgstr "Avkastning"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA"
msgstr "SLA"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_deadline
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Deadline"
msgstr "SLA Deadline"

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__failed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Failed"
msgstr "SLA misslyckades"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_action_main
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_sla
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_users__sla_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_sla_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "SLA Policies"
msgstr "SLA-policyer"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_res_partner__sla_ids
#: model:ir.model.fields,help:helpdesk.field_res_users__sla_ids
msgid ""
"SLA Policies that will automatically apply to the tickets submitted by this "
"customer."
msgstr ""
"SLA-policyer som automatiskt kommer att gälla för ärenden som skickas in av "
"denna kund."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "SLA Policy"
msgstr "SLA-policy"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__description
msgid "SLA Policy Description"
msgstr "SLA-policy Beskrivning"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_stage_id
msgid "SLA Stage"
msgstr "SLA-etapp"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_status_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_status_ids
msgid "SLA Status"
msgstr "SLA-status"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_action
#: model:ir.actions.act_window,name:helpdesk.helpdesk_sla_report_analysis_dashboard_action
#: model:ir.model,name:helpdesk.model_helpdesk_sla_report_analysis
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu_sla_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_graph
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_report_analysis_view_pivot
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "SLA Status Analysis"
msgstr "Analys av SLA-status"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_fail
msgid "SLA Status Failed"
msgstr "SLA-status Misslyckad"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_success
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_success
msgid "SLA Status Success"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__reached
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA Success"
msgstr "SLA lyckade"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "SLA Success Rate"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__sla_status__ongoing
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "SLA in Progress"
msgstr "SLA pågående"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_ids
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
msgid "SLAs"
msgstr "SLA:er"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__message_has_sms_error
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveransfel"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_kanban
msgid "Sad face"
msgstr "Sorgset ansikte"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Sample"
msgstr "Prov"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_progress_data
msgid "Satisfied"
msgstr "Nöjd"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Save this ticket and the modifications you've made to it."
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Schedule your <b>activity</b>."
msgstr "Schemalägg din <b>aktivitet</b>."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_search
msgid "Search SLA Policies"
msgstr "Sök SLA-policyer"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Assigned to"
msgstr "Sök i Tilldelad till"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Customer"
msgstr "Sök i kund"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Helpdesk Team"
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search in Stage"
msgstr "Sök i etapper"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Search%(left)s Tickets%(right)s"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__access_token
msgid "Security Token"
msgstr "Säkerhetstoken"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Select the <b>customer</b> of your ticket."
msgstr "Välj <b>kund</b> för ditt ärende."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Self-Service"
msgstr "Självbetjäning"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.action_helpdesk_ticket_mass_mail
msgid "Send Email"
msgstr "Skicka e-post"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Send broken products for repair"
msgstr "Skicka trasiga produkter för reparation"

#. module: helpdesk
#: model:mail.template,description:helpdesk.new_ticket_request_email_template
msgid ""
"Send customers a confirmation email to notify them that their helpdesk "
"ticket has been received and is currently being reviewed by the helpdesk "
"team. Automatically send an email to customers when a ticket reaches a "
"specific stage in a helpdesk team by setting this template on that stage."
msgstr ""

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_service
msgid "Service"
msgstr "Tjänst"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Set an Email Template on Stages"
msgstr "Skapa en e-postmall för etapper"

#. module: helpdesk
#: model:mail.template,description:helpdesk.solved_ticket_request_email_template
msgid ""
"Set this template on a project's stage to automate email when tasks reach "
"stages"
msgstr ""
"Ställ in denna mall på ett projekts etapp för att automatisera e-post när "
"uppgifter når etapper"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
msgid "Settings"
msgstr "Inställningar"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.portal_share_action
msgid "Share Ticket"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid ""
"Share presentations and videos, and organize them into courses. Allow "
"customers to search your eLearning courses in the help center for answers to"
" their questions."
msgstr ""

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_rating
msgid "Show Customer Ratings"
msgstr "Visa kundbetyg"

#. module: helpdesk
#: model:res.groups,name:helpdesk.group_use_sla
msgid "Show SLA Policies"
msgstr "Visa SLA-policyer"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Show all records which has next action date is before today"
msgstr "Visa alla poster som har nästa händelse före idag"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__sla_id
msgid "Sla"
msgstr "Sla"

#. module: helpdesk
#: model:helpdesk.stage,name:helpdesk.stage_solved
msgid "Solved"
msgstr "Lösta"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__source_id
msgid "Source"
msgstr "Källa"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__stage_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__stage_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Stage"
msgstr "Läge"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_stage
#: model:mail.message.subtype,name:helpdesk.mt_ticket_stage
msgid "Stage Changed"
msgstr "Ändrad etapp"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
msgid "Stage Search"
msgstr "Lägessökning"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__stage_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_stage_menu
msgid "Stages"
msgstr "Stadier"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_stage_delete_wizard__stage_ids
msgid "Stages To Delete"
msgstr "Lägen att ta bort"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__stage_ids
msgid ""
"Stages the team will use. This team's tickets will only be able to be in "
"these stages."
msgstr ""
"Etapper som teamet kommer att använda. Detta teams ärenden kommer endast att"
" kunna befinna sig i dessa etapper."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_status
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__status
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Status"
msgstr "Status"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baserad på aktiviteter\n"
"Försenade: Leveranstidpunkten har passerat\n"
"Idag: Aktivitetsdatum är idag\n"
"Kommande: Framtida aktiviteter."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__duration_tracking
msgid "Status time"
msgstr "Status tid"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__name
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__name
msgid "Subject"
msgstr "Ämne"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7days_success
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__success_rate
msgid "Success Rate"
msgstr "Framgångsgrad"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_7dayssuccess
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_success
msgid "Success Rate Analysis"
msgstr "Analys av framgångsgrad"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_success
msgid "Success SLA Policy"
msgstr "Policy för framgångsrikt SLA"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_tree
msgid "Tag"
msgstr "Etikett"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_tag_action
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__tag_ids
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__tag_ids
#: model:ir.ui.menu,name:helpdesk.helpdesk_tag_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tag_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Tags"
msgstr "Etiketter"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_tag_action
msgid "Tags are perfect for organizing your tickets."
msgstr "Taggar är perfekta för att organisera dina ärenden."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "Target"
msgstr "Mål"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__stage_id
msgid "Target Stage"
msgstr "Mål Etapp"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
msgid "Team"
msgstr "Lag"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__member_ids
msgid "Team Members"
msgstr "Teammedlemmar"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_stage_team_action
msgid "Team Stages"
msgstr ""

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.email_template_action_helpdesk
msgid "Templates"
msgstr "Mallar"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Klassen (Odoo-dokumenttyp) som detta alias är knutet till. All inkommande "
"e-post som inte ingår i en pågående diskussion skapar en ny post av denna "
"klass (t ex Aktivitet)"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Namnet på alias för e-post, t.ex. 'kontakt' om du vill samla in e-post för "
"<<EMAIL>>"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "The team does not allow ticket closing through portal"
msgstr "Teamet tillåter inte stängning av ärenden via portalen"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla__exclude_stage_ids
msgid ""
"The time spent in these stages won't be taken into account in the "
"calculation of the SLA."
msgstr ""
"Den tid som spenderas i dessa etapper kommer inte att beaktas vid "
"beräkningen av SLA."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
msgid ""
"The visibility of the team needs to be set as \"Invited portal users and all"
" internal users\" in order to use the website form."
msgstr ""
"Teamets synlighet måste ställas in som \"Inbjudna portalanvändare och alla "
"internanvändare\" för att webbplatsformuläret ska kunna användas."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "There are currently no Ticket for your account."
msgstr "Det finns för närvarande inget ärende för ditt konto."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "This"
msgstr "Detta"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid ""
"This SLA Policy will apply to tickets matching ALL of the following "
"criteria:"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Det här är ett namn som hjälper dig att hålla koll på dina olika "
"kampanjinsatser, t.ex. Fall_Drive, Christmas_Special"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"Detta är leveransmetoden, t.ex. 'Postkort', 'E-post' eller 'Annonsruta'"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Detta är källan till länken, t.ex. Sökmotor, en annan domän eller namnet på "
"e-postlistan"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "This ticket was closed %s hours after its SLA deadline."
msgstr ""

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "This ticket was successfully closed %s hours before its SLA deadline."
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_confirmation_wizard
msgid ""
"This will archive the stages and all of the tickets they contain from the "
"following teams:"
msgstr ""
"Detta kommer att arkivera etapperna och alla ärenden de innehåller från "
"följande team:"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.mail_activity_type_action_config_helpdesk
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""
"Dessa representerar de olika kategorierna av saker du måste göra (t.ex. "
"\"Ringa\" eller \"Skicka e-post\")."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Three stars, maximum score"
msgstr "Tre stjärnor, högsta poäng"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_status__ticket_id
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_id
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_activity
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_form_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_tree_inherit_helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid "Ticket"
msgstr "Biljett"

#. module: helpdesk
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_dashboard
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_dashboard_action
#: model:ir.model,name:helpdesk.model_helpdesk_ticket_report_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_analysis
msgid "Ticket Analysis"
msgstr "Analys av biljetter"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_closed
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_closed
msgid "Ticket Closed"
msgstr "Ärendet stängd"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.solved_ticket_request_email_template
msgid "Ticket Closed - Reference {{ object.id if object.id else 15 }}"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__ticket_count
msgid "Ticket Count"
msgstr "Antal ärenden"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_new
#: model:mail.message.subtype,name:helpdesk.mt_ticket_new
msgid "Ticket Created"
msgstr "Ärendet skapad"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__create_date
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__create_date
msgid "Ticket Creation Date"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__sla_deadline
msgid "Ticket Deadline"
msgstr "Ärendets sista datum"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_ref
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__ticket_ref
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_ref
msgid "Ticket IDs Sequence"
msgstr "Sekvens av ärende-ID"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_properties
msgid "Ticket Properties"
msgstr "Ärendeegenskaper"

#. module: helpdesk
#: model:mail.message.subtype,name:helpdesk.mt_team_ticket_rated
#: model:mail.message.subtype,name:helpdesk.mt_ticket_rated
msgid "Ticket Rated"
msgstr "Ärende Betygsatt"

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_helpdesk_sla_status
msgid "Ticket SLA Status"
msgstr "Ärendets SLA-status"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.quick_create_ticket_form
msgid "Ticket Title"
msgstr "Ärendetitel"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
msgid "Ticket closed by the customer"
msgstr "Ärendet stängd av kunden"

#. module: helpdesk
#: model:mail.message.subtype,description:helpdesk.mt_ticket_new
msgid "Ticket created"
msgstr "Ärende skapat"

#. module: helpdesk
#. odoo-javascript
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_team.py:0
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_my_ticket_action_no_create
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_sla
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_team
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_action_unassigned
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__ticket_ids
#: model:ir.model.fields,field_description:helpdesk.field_res_partner__ticket_count
#: model:ir.model.fields,field_description:helpdesk.field_res_users__ticket_count
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_menu_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_stage_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_list_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_helpdesk_ticket
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_my_home_menu_helpdesk
msgid "Tickets"
msgstr "Biljetter"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.actions.act_window,name:helpdesk.helpdesk_ticket_analysis_action
#: model:ir.ui.menu,name:helpdesk.helpdesk_ticket_report_menu
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_kanban
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_analysis_view_tree
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_report_view_cohort
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_analysis
msgid "Tickets Analysis"
msgstr "Ärendeanalys"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_digest_digest__kpi_helpdesk_tickets_closed
msgid "Tickets Closed"
msgstr "Biljetter stängda"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Tickets Search"
msgstr "Ärendesökning"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_stage__fold
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__fold
msgid "Tickets in a folded stage are considered as closed."
msgstr "Ärenden i en ihopfälld etapp betraktas som avslutade."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_sale_timesheet
msgid "Time Billing"
msgstr "Fakturering av tid"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__close_hours
msgid "Time to close (hours)"
msgstr "Tid till stängning (timmar)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__assign_hours
msgid "Time to first assignment (hours)"
msgstr "Tid till första tilldelning (timmar)"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_helpdesk_timesheet
msgid "Timesheets"
msgstr "Tidrapporter"

#. module: helpdesk
#: model:digest.tip,name:helpdesk.digest_tip_helpdesk_0
msgid "Tip: Create tickets from incoming emails"
msgstr "Tips: Skapa ärenden från inkommande e-post"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_my_ticket_action_no_create
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_my
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_main_tree
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_team
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_unassigned
msgid ""
"To get things done, plan activities and use the ticket status.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"För att få saker gjorda, planera aktiviteter och använd ärende status.<br>\n"
"                Samarbeta effektivt genom att chatta i realtid eller via e-post."

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.rating_rating_view_search_inherit_helpdesk
msgid "Today"
msgstr "Idag"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Today Activities"
msgstr "Välj ledighetstyp"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Today's Average Rating"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__total_response_hours
msgid "Total Exchange Time in Hours"
msgstr "Total utväxlingstid i timmar"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track &amp; Bill Time"
msgstr "Spåra &amp; Fakturerings tid"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track customer satisfaction on tickets"
msgstr "Spåra kundnöjdhet i ärenden"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_action
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_sla_report_analysis_dashboard_action
msgid ""
"Track the performance of your teams, the success rate of your tickets, and "
"how quickly you reach your service level agreements (SLAs)."
msgstr ""
"Spåra teamens prestationer, hur många ärenden som lyckas och hur snabbt ni "
"når era servicenivåavtal (SLA)."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Track the time spent on tickets"
msgstr "Spåra den tid som läggs på ärenden"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
msgid "Two stars, with a maximum of three"
msgstr "Två stjärnor, dock högst tre"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ av undantagsaktivitet som har registrerats."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_stage.py:0
msgid "Unarchive Tickets"
msgstr "Återställ Arkiverade Ärenden"

#. module: helpdesk
#. odoo-javascript
#. odoo-python
#: code:addons/helpdesk/controllers/portal.py:0
#: code:addons/helpdesk/static/src/views/helpdesk_ticket_graph/helpdesk_ticket_graph_model.js:0
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
#: model_terms:ir.ui.view,arch_db:helpdesk.portal_helpdesk_ticket
msgid "Unassigned"
msgstr "Otilldelad"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__unassigned_tickets
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_search_analysis_closed
msgid "Unassigned Tickets"
msgstr "Otilldelade Ärenden"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search
msgid "Unread Messages"
msgstr "Olästa meddelanden"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/components/helpdesk_team_dashboard/helpdesk_team_dashboard.xml:0
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_sla_report_analysis__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket__priority__3
#: model:ir.model.fields.selection,name:helpdesk.selection__helpdesk_ticket_report_analysis__priority__3
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_tickets_view_search_base
msgid "Urgent"
msgstr "Brådskande"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid "Use <b>activities</b> to organize your daily work."
msgstr "Använd <b>aktiviteter</b> för att organisera ditt dagliga arbete."

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_alias
msgid "Use Alias"
msgstr "Använd Alias"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__use_coupons
msgid "Use Coupons"
msgstr "Använd kuponger"

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"progress of this ticket."
msgstr ""

#. module: helpdesk
#: model:ir.model,name:helpdesk.model_res_users
#: model:res.groups,name:helpdesk.group_helpdesk_user
msgid "User"
msgstr "Användare"

#. module: helpdesk
#: model:helpdesk.team,name:helpdesk.helpdesk_team3
msgid "VIP Support"
msgstr "VIP-support"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__privacy_visibility
msgid "Visibility"
msgstr "Synlighet"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "Visibility &amp; Assignment"
msgstr ""

#. module: helpdesk
#. odoo-javascript
#: code:addons/helpdesk/static/src/js/tours/helpdesk.js:0
msgid ""
"Want to <b>boost your customer satisfaction</b>?<br/><i>Click Helpdesk to "
"start.</i>"
msgstr ""
"Vill du <b>öka din kundnöjdhet</b>?<br/><i>Klicka på Kundtjänst för att "
"starta.</i>"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.tickets_followup
msgid ""
"We hope to have addressed your request satisfactorily. If you no longer need"
" our assistance, please close this ticket. Thank you for your collaboration."
msgstr ""

#. module: helpdesk
#: model_terms:helpdesk.team,description:helpdesk.helpdesk_team1
#: model_terms:helpdesk.team,description:helpdesk.helpdesk_team3
msgid ""
"We provide 24/7 support, Monday through Friday. Ticket responses are usually provided within 2 working days.<br>\n"
"            Support is mainly provided in English. We can also assist in Spanish, French, and Dutch."
msgstr ""
"Vi erbjuder support dygnet runt, måndag till fredag. Svar på ärenden lämnas vanligtvis inom 2 arbetsdagar.<br>\n"
"            Support ges huvudsakligen på engelska. Vi kan även hjälpa till på spanska, franska och nederländska."

#. module: helpdesk
#: model:helpdesk.tag,name:helpdesk.tag_website
msgid "Website"
msgstr "Webbplats"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_form
msgid "Website Form"
msgstr "Formulär för webbplats"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__website_message_ids
msgid "Website Messages"
msgstr "Webbplatsmeddelanden"

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__website_message_ids
#: model:ir.model.fields,help:helpdesk.field_helpdesk_ticket__website_message_ids
msgid "Website communication history"
msgstr "Webbplatsens kommunikationshistorik"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla__time
msgid "Within"
msgstr "Inom"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working Hours"
msgstr "Arbetstimmar"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_assignation_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_assignation_hours
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Working Hours to Assign"
msgstr "Arbetstid att tilldela"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__ticket_close_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_close_hours
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_graph_main
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_pivot_main
msgid "Working Hours to Close"
msgstr "Arbetstid till stängning"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_sla_report_analysis__sla_exceeded_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket__sla_deadline_hours
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_ticket_report_analysis__ticket_deadline_hours
msgid "Working Hours until SLA Deadline"
msgstr ""

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_sla_status__exceeded_hours
msgid ""
"Working hours exceeded for reached SLAs compared with deadline. Positive "
"number means the SLA was reached after the deadline."
msgstr ""
"Arbetstid överskriden för uppnådda SLA jämfört med deadline. Positivt tal "
"betyder att SLA uppnåddes efter tidsfristen."

#. module: helpdesk
#: model:ir.model.fields,help:helpdesk.field_helpdesk_team__resource_calendar_id
msgid "Working hours used to determine the deadline of SLA Policies."
msgstr ""
"Arbetstid som används för att fastställa tidsfristen för SLA-policyer."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tickets contained in these stages as "
"well?"
msgstr ""
"Skulle du vilja avarkivera alla ärenden som ingår i dessa etapper också?"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid ""
"You cannot delete stages containing tickets. You can either archive them or "
"first delete all of their tickets."
msgstr ""
"Du kan inte radera etapper som innehåller ärenden. Du kan antingen arkivera "
"dem eller först radera alla deras ärenden."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.view_helpdesk_stage_delete_wizard
msgid ""
"You cannot delete stages containing tickets. You should first delete all of "
"their tickets."
msgstr ""
"Du kan inte radera etapper som innehåller ärenden. Du bör först ta bort alla"
" deras ärenden."

#. module: helpdesk
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_closed_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_rating_not_zero
#: model:ir.model.constraint,message:helpdesk.constraint_res_users_target_success_not_zero
msgid "You cannot have negative targets"
msgstr "Du kan inte ha negativa mål"

#. module: helpdesk
#: model_terms:ir.actions.act_window,help:helpdesk.helpdesk_ticket_action_sla
msgid "You completed all your tickets on time."
msgstr "Du slutförde alla dina ärenden i tid."

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "You have been invited to follow %s"
msgstr "Du har blivit inbjuden att följa %s"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_invitation_follower
msgid "You have been invited to follow Ticket Document :"
msgstr ""

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "alias"
msgstr "alias"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_sla_view_form
msgid "e.g. Close urgent tickets within 36 hours"
msgstr "t.ex. Stäng brådskande ärenden inom 36 timmar"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. Customer Care"
msgstr "t.ex. kundtjänst"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. My Company"
msgstr "t.ex. Mitt Företag"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_ticket_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk.quick_create_ticket_form
msgid "e.g. Product arrived damaged"
msgstr "t.ex. Produkten anlände skadad"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "e.g. mycompany.com"
msgstr "t.ex. mittföretag.com"

#. module: helpdesk
#: model:ir.model.fields,field_description:helpdesk.field_helpdesk_team__use_website_helpdesk_slides
msgid "eLearning"
msgstr "eLärande"

#. module: helpdesk
#: model_terms:digest.tip,tip_description:helpdesk.digest_tip_helpdesk_0
msgid "generate tickets in your pipeline."
msgstr "generera ärenden i din pipeline."

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.ticket_creation
msgid "has been created from ticket:"
msgstr "har skapats från ärende:"

#. module: helpdesk
#: model:ir.actions.server,name:helpdesk.helpdesk_ratings_server_action
msgid "helpdesk view rating"
msgstr "helpdesk visa bedömning"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.helpdesk_team_view_form
msgid "team search"
msgstr "teamsökning"

#. module: helpdesk
#. odoo-python
#: code:addons/helpdesk/models/helpdesk_ticket.py:0
msgid "tickets"
msgstr "tickets"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.rating_ticket_request_email_template
msgid ""
"{{ object.company_id.name or object.user_id.company_id.name or 'Helpdesk' "
"}}: Service Rating Request"
msgstr ""
"{{ object.company_id.name or object.user_id.company_id.name or 'Helpdesk' "
"}}: Begäran om servicebedömning"

#. module: helpdesk
#: model:mail.template,subject:helpdesk.new_ticket_request_email_template
msgid "{{ object.name }}"
msgstr "{{ object.name }}"

#. module: helpdesk
#: model_terms:ir.ui.view,arch_db:helpdesk.team_rating_data
msgid "{{rating.res_name if t['is_helpdesk_user'] else ''}}"
msgstr "{{rating.res_name if t['is_helpdesk_user'] else ''}}"
