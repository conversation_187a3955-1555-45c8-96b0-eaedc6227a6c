# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_base
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>_nexter<PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "%(job)s on %(platform)s"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<span class=\"px-2\">-</span>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<span class=\"text-muted\" invisible=\"campaign_end_date\">No limit</span>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<strong class=\"o_kanban_label_width\">From:</strong>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "<strong class=\"o_kanban_label_width\">To:</strong>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "<strong class=\"px-2\">to</strong>"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "<strong>From</strong>"
msgstr "<strong>De la</strong>"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "<strong>to</strong>"
msgstr "<strong>către</strong>"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__api_data
msgid "API Data"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_needaction
msgid "Action Needed"
msgstr "Acțiune necesară"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_ids
msgid "Activities"
msgstr "Activități"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decorator Excepție Activitate"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_state
msgid "Activity State"
msgstr "Stare activitate"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_type_icon
msgid "Activity Type Icon"
msgstr "Pictograma tipului de activitate"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__apply_method
msgid "Apply Method"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.job_board_campaign_manager_start_ir_actions_server
msgid "Automatic Job Posting on job boards"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.job_board_campaign_manager_stop_ir_actions_server
msgid "Automatic job posting deleting on job boards"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_1920
msgid "Avatar"
msgstr "Imagine profil"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__biweekly
msgid "Bi-Week"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "Campaign Duration"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__campaign_end_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_end_date
msgid "Campaign End Date"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Campaign End Date:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__campaign_start_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "Campaign Start Date"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Campaign Start Date:"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Campaign start date can't be after campaign end date"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Campaign will start on %(start_date)s"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "Can't postpone posts that are already posted"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__company_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__company_id
msgid "Company"
msgstr "Companie"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Company:"
msgstr "Companie:"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__apply_method
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Contact Method"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Contact Method:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__apply_vector
msgid "Contact Point"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Contact Point:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__create_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__create_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__create_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__create_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__currency_id
msgid "Currency"
msgstr "Monedă"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__api_data
msgid "Data"
msgstr "Data"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__daily
msgid "Day"
msgstr "Zi"

#. module: hr_recruitment_integration_base
#. odoo-javascript
#: code:addons/hr_recruitment_integration_base/static/src/views/form/job_post_no_save_controller.js:0
msgid "Delete"
msgstr "Șterge"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__deleted
msgid "Deleted"
msgstr "Șters"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Description"
msgstr "Descriere"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__display_name
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__display_name
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: hr_recruitment_integration_base
#: model:ir.actions.server,name:hr_recruitment_integration_base.hr_job_post_double_check_action
msgid "Double Check Job Post"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-javascript
#: code:addons/hr_recruitment_integration_base/static/src/views/form/job_post_no_save_controller.js:0
msgid "Duplicate"
msgstr "Duplicare"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__job_apply_mail
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__apply_method__email
msgid "Email"
msgstr "E-mail"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Expiration date"
msgstr "Data expirării"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__expired
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Expired"
msgstr "Expirat"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__failure
msgid "Failure"
msgstr "Eșec"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_follower_ids
msgid "Followers"
msgstr "Urmăritori"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Pictogramă Font awesome, de ex. fa-sarcini"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Group By"
msgstr "Grupează după"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__hourly
msgid "Hour"
msgstr "Ora"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_exception_icon
msgid "Icon"
msgstr "Pictogramă"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Pictograma care indică o activitate de excepție."

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Dacă este bifat, mesaje noi necesită atenția dumneavoastră."

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_error
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, există mesaje cu eroare de livrare."

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_1920
msgid "Image"
msgstr "Imagine"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_1024
msgid "Image 1024"
msgstr "Imagine 1024"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_128
msgid "Image 128"
msgstr "Imagine 128"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_256
msgid "Image 256"
msgstr "Imagine 256"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__image_512
msgid "Image 512"
msgstr "Imagine 512"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Issue"
msgstr "Problema"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__job_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__job_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Job"
msgstr "Loc de munca"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__platform_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Job Board"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_kanban
msgid "Job Board Posts"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Job Boards"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.actions.act_window,name:hr_recruitment_integration_base.action_open_hr_job_post
#: model:ir.ui.menu,name:hr_recruitment_integration_base.menu_hr_job_boards
msgid "Job Boards Posts"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_job
msgid "Job Position"
msgstr "Funcție"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_job_post
msgid "Job Post"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job Post on %(platform)s has been %(mode)s"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job Post on %(platform)s has been modified"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__job_post_ids
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__post_ids
msgid "Job Posts"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Job:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__write_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__write_uid
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__write_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__write_date
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__salary_max
msgid "Maximum Salary"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_error
msgid "Message Delivery error"
msgstr "Eroare de livrare a mesajului"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__salary_min
msgid "Minimum Salary"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__monthly
msgid "Month"
msgstr "Luna"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Data limită a activității mele"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__name
msgid "Name"
msgstr "Nume"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Următoarea activitate din calendar"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Data limită pentru următoarea activitate"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_summary
msgid "Next Activity Summary"
msgstr "Sumarul următoarei activități"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_type_id
msgid "Next Activity Type"
msgstr "Următorul tip de activitate"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_recruitment_platform.py:0
msgid "No API call defined for this platform please contact the administrator"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "No Limit"
msgstr "Fără Limită"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__job_post_count
msgid "Number of Job Posts"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_error_counter
msgid "Number of errors"
msgstr "Număr de erori"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numărul de mesaje care necesită acțiune"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__pending
msgid "Pending"
msgstr "În așteptare"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__platform_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Platform"
msgstr "Platformă"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__post_html
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__post_html
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Post"
msgstr "Postează"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_recruitment_post_job_wizard
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_recruitment_post_job_wizard_view_form
msgid "Post Job"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Post Now"
msgstr "Postează"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Posts"
msgstr "Postări"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_delete
msgid "Price to Delete"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_get
msgid "Price to Get"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_publish
msgid "Price to Publish"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__price_to_update
msgid "Price to Update"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Publish on Job Board"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job.py:0
msgid "Publish on a Job Board"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Published"
msgstr "Publicat"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__rating_ids
msgid "Ratings"
msgstr "Ratings"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__recruiter_id
msgid "Recruiter"
msgstr "Recrutor"

#. module: hr_recruitment_integration_base
#: model:ir.model,name:hr_recruitment_integration_base.model_hr_recruitment_platform
msgid "Recruitment Platform"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__activity_user_id
msgid "Responsible User"
msgstr "Utilizator responsabil"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Reuse Job Post"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "Salary Range"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__payment_interval
msgid "Salary Time Unit"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_recruitment_post_job_wizard__apply_method__email
msgid "Send an Email"
msgstr "Trimiteți un e-mail"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Start Date"
msgstr "Dată început"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__status
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban_search
msgid "Status"
msgstr "Stare"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__status_message
msgid "Status Message"
msgstr "Mesaj Status "

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Status Message:"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Stare bazată pe activități\n"
"Întârziată: data activitații este deja trecută\n"
"Astăzi: data activității este astăzi\n"
"Planificate: activități viitoare."

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "Status:"
msgstr "Stare:"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Stop Campaigns"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_list
msgid "Stop Date"
msgstr "Dată de oprire"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__success
msgid "Success"
msgstr "Succes"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__recruiter_id
msgid ""
"The Recruiter will be the default value for all Applicants in this job"
"             position. The Recruiter is automatically added to all meetings "
"with the Applicant."
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__campaign_end_date
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_end_date
msgid ""
"The date when the campaign will end. If not set, the campaign will run "
"indefinitely or to the maximum allowed by a platform."
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__campaign_start_date
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "The date when the campaign will start."
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__apply_vector
msgid "The email address, phone number, url to send applications to."
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid ""
"This action will update the job post on the platform. This action will cost "
"%(price)s credits. Do you want to continue?"
msgstr ""

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipul de activitate de excepție înregistrată."

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_form
msgid "Update"
msgstr "Actualizare"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job_post__status__warning
msgid "Warning"
msgstr "Avertizare"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_recruitment_platform__website
msgid "Website"
msgstr "Site web"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job_post__website_message_ids
msgid "Website Messages"
msgstr "Mesaje de pe site-ul web"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,help:hr_recruitment_integration_base.field_hr_job_post__website_message_ids
msgid "Website communication history"
msgstr "Istoricul comunicării pe site-ul web"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__weekly
msgid "Week"
msgstr "Săptămână"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields,field_description:hr_recruitment_integration_base.field_hr_job__schedule_id
msgid "Working Schedule"
msgstr "Program de lucru"

#. module: hr_recruitment_integration_base
#: model:ir.model.fields.selection,name:hr_recruitment_integration_base.selection__hr_job__payment_interval__yearly
msgid "Year"
msgstr "An"

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_job_post.py:0
msgid "created"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/models/hr_recruitment_platform.py:0
msgid "failure"
msgstr ""

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.view_hr_job_form
msgid "per"
msgstr "per"

#. module: hr_recruitment_integration_base
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_base.hr_job_post_view_kanban
msgid "plateform icon"
msgstr ""

#. module: hr_recruitment_integration_base
#. odoo-python
#: code:addons/hr_recruitment_integration_base/wizard/hr_recruitment_post.py:0
msgid "updated"
msgstr ""
