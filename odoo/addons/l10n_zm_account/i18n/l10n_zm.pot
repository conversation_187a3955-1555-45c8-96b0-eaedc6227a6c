# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_zm_account
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-21 13:14+0000\n"
"PO-Revision-Date: 2024-02-21 13:14+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_10
msgid "10 - Disposal of capital assets"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_11
msgid "11 - Total standard Rated Outputs"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_12
msgid "12 - Export of standard rated goods and services"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_13
msgid "13 - Export of zero-rated goods and services"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_14
msgid ""
"14 - Other zero-rated outputs (e.g. supplies to donor funded projects or "
"supplies to diplomatic missions etc.)"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_15
msgid "15 - Total zero-Rated Outputs"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_16
msgid "16 - Total taxable sales"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_17
msgid "17 - Imported services (Reverse VAT)"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_18
msgid "18 - Total tax due on outputs"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_19
msgid "19 - Local exempt sales"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_20
msgid "20 - Export of exempt goods and services"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_21
msgid "21 - Total exempt sales"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_22
msgid "22 - Total sales"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_24
msgid ""
"24 - Standard rated local purchases and allowable administrative expenses "
"taxed at normal taxable value"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_25
msgid "25 - Local purchases taxed at minimum taxable value (MTV)"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_26
msgid "26 - Zero rated local purchases"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_27
msgid "27 - Standard rated imports"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_28
msgid "28 - Zero-rated imports"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_29
msgid "29 - Standard rated capital expenditure"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_30
msgid "30 - Total taxable inputs"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_31
msgid "31 - Exempt local purchases"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_32
msgid "32 - Exempt imports"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_33
msgid "33 - Non-deductible purchases"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_34
msgid "34 - Purchases from unregistered suppliers"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_35
msgid "35 - Total purchases"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_37
msgid "37 - Input Tax directly attributable to Taxable Sales"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_38
msgid "38 - Input Tax directly attributable to Exempt Sales"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line39selection
msgid ""
"39 - Apportionment of input tax (Please indicate whether or not to use "
"Apportionment of input tax.)"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_39a
msgid ""
"39a - First Method of apportionment (Please select which method of "
"apportionment to use. Yes is 39a, No is 39b.)"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_39b
msgid "39b - Second Method of apportionment"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_40
msgid "40 - Input Tax Credit Allowed"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_41
msgid "41 - Total Output Tax"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_42
msgid "42 - Input Tax Allowed"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_43
msgid "43 - Net VAT Payable/Claimable"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_44
msgid "44 - Withholding VAT Credit (From Sch.-XIII)"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_45
msgid "45 - Net VAT Payable/Claimable"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_08
msgid "8 - Standard rated local sales"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_line_09
msgid "9 - Local sales taxed at minimum taxable value (MTV)"
msgstr ""

#. module: l10n_zm_account
#: model:ir.model,name:l10n_zm_account.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_title_input_tax
msgid "Calculation of Input Tax Allowed"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_title_tax_due
msgid "Calculation of Tax Due"
msgstr ""

#. module: l10n_zm_account
#: model_terms:ir.ui.view,arch_db:l10n_zm_account.report_invoice_document
msgid "Cancelled Credit Note"
msgstr ""

#. module: l10n_zm_account
#: model_terms:ir.ui.view,arch_db:l10n_zm_account.report_invoice_document
msgid "Cancelled Tax Invoice"
msgstr ""

#. module: l10n_zm_account
#: model_terms:ir.ui.view,arch_db:l10n_zm_account.report_invoice_document
msgid "Credit Note"
msgstr ""

#. module: l10n_zm_account
#: model_terms:ir.ui.view,arch_db:l10n_zm_account.report_invoice_document
msgid "Draft Credit Note"
msgstr ""

#. module: l10n_zm_account
#: model_terms:ir.ui.view,arch_db:l10n_zm_account.report_invoice_document
msgid "Draft Tax Invoice"
msgstr ""

#. module: l10n_zm_account
#: model_terms:ir.ui.view,arch_db:l10n_zm_account.report_invoice_document
msgid "Fiscal Tax Invoice"
msgstr ""

#. module: l10n_zm_account
#: model:ir.model,name:l10n_zm_account.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_title_purchases
msgid "Purchases"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.line,name:l10n_zm_account.zm_tax_report_title_sales
msgid "Sales"
msgstr ""

#. module: l10n_zm_account
#: model:account.report,name:l10n_zm_account.zm_tax_report
msgid "VAT Return"
msgstr ""

#. module: l10n_zm_account
#: model_terms:ir.ui.view,arch_db:l10n_zm_account.report_invoice_document
msgid "Vendor Bill"
msgstr ""

#. module: l10n_zm_account
#: model_terms:ir.ui.view,arch_db:l10n_zm_account.report_invoice_document
msgid "Vendor Credit Note"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.column,name:l10n_zm_account.zm_tax_report_no_vat
msgid "a. VAT Exclusive Value"
msgstr ""

#. module: l10n_zm_account
#: model:account.report.column,name:l10n_zm_account.zm_tax_report_vat
msgid "b. VAT"
msgstr ""
