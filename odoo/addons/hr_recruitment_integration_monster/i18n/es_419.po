# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_monster
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_28
msgid "Accounting and Auditing Services"
msgstr "Servicios de contabilidad y auditoría"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_36
msgid "Administrative and Support Services"
msgstr "Servicios administrativos y de apoyo"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_29
msgid "Advertising and PR Services"
msgstr "Servicios de publicidad y relaciones públicas"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_6
msgid "Aerospace and Defense"
msgstr "Aeroespacial y defensa"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_1
msgid "Agriculture/Forestry/Fishing"
msgstr "Agricultura/Forestación/Pesca"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_0
msgid "All"
msgstr "Todos"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_30
msgid "Architectural and Design Services"
msgstr "Servicios de arquitectura y diseño"

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_recruitment_platform.py:0
msgid "Authentication error: Monster.com credentials are invalid."
msgstr ""
"Error de autenticación: las credenciales de monster.com no son válidas."

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_75
msgid "Automotive Sales and Repair Services"
msgstr "Servicios de venta y reparación de automotores"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_7
msgid "Automotive and Parts Mfg"
msgstr "Fabricación de automóviles y piezas de recambio"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_23
msgid "Banking"
msgstr "Actividades bancarias"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_8
msgid "Biotechnology/Pharmaceuticals"
msgstr "Biotecnología y farmacéutica"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_21
msgid "Broadcasting, Music, and Film"
msgstr "Radiodifusión, música y cine"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_76
msgid "Business Services - Other"
msgstr "Servicios empresariales - Otros"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_9
msgid "Chemicals/Petro-Chemicals"
msgstr "Químicos/Petroquímicos"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_14
msgid "Clothing and Textile Manufacturing"
msgstr "Fabricación de ropa y textiles "

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_32
msgid "Computer Hardware"
msgstr "Hardware de computadoras"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_33
msgid "Computer Software"
msgstr "Software"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_77
msgid "Computer/IT Services"
msgstr "Servicios de computadoras y tecnología de la información"

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_4
msgid "Construction - Industrial Facilities and Infrastructure"
msgstr "Construcción - Facilidades industriales e infraestructura"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_78
msgid "Construction - Residential & Commercial/Office"
msgstr "Construcción - residencial y comerciales/oficinas"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_10
msgid "Consumer Packaged Goods Manufacturing"
msgstr "Fabricación de bienes de consumo envasados"

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_hr_contract_type
msgid "Contract Type"
msgstr "Tipo de contrato"

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_recruitment_platform.py:0
msgid ""
"Critical error: Monster.com service has changed. Please contact customer "
"support."
msgstr ""
"Error crítico: el servicio se mosnter.com cambió. Póngase en contacto con "
"atención al cliente."

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_res_currency
msgid "Currency"
msgstr "Moneda"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_38
msgid "Education"
msgstr "Educación"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_11
msgid "Electronics, Components, and Semiconductor Mfg"
msgstr "Electrónica, componentes y semiconductores"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_3
msgid "Energy and Utilities"
msgstr "Energía y utilidades"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_79
msgid "Engineering Services"
msgstr "Cambios de ingeniería"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_80
msgid "Entertainment Venues and Theaters"
msgstr "Centros de entretenimiento y teatros"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_81
msgid "Financial Services"
msgstr "Servicios financieros"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_82
msgid "Food and Beverage Production"
msgstr "Poducción de comida y bebida"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_51
msgid "Government - Military/Defense"
msgstr "Gobierno - Ejército/defensa"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_52
msgid "Government - National"
msgstr "Gobierno - Nacional"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_50
msgid "Government and Military"
msgstr "Gobierno y ejército"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_16
msgid "Grocery/Convenience/Gas"
msgstr "Abarrotes/Autoservicio/Gas"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_39
msgid "Healthcare Services"
msgstr "Servicios para el cuidado de la salud"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_44
msgid "Hotels and Lodging"
msgstr "Hoteles y alojamiento"

#. module: hr_recruitment_integration_monster
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_company__hr_recruitment_monster_username
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_config_settings__hr_recruitment_monster_username
msgid "Identifier"
msgstr "Identificador"

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_res_partner_industry
msgid "Industry"
msgstr "Sector"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_24
msgid "Insurance"
msgstr "Seguros"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_20
msgid "Internet Services"
msgstr "Servicios de internet"

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_hr_job_post
msgid "Job Post"
msgstr "Vacante"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_34
msgid "Legal Services"
msgstr "Servicios legales"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_31
msgid "Management Consulting Services"
msgstr "Servicios de gestión y consultoría"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_35
msgid "Management and Holding Companies"
msgstr "Sociedades de gestión y de cartera"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_12
msgid "Manufacturing - Other"
msgstr "Fabricación - Otros"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_83
msgid "Marine Mfg & Services"
msgstr "Productos y servicios marinos"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_84
msgid "Medical Devices and Supplies"
msgstr "Dispositivos y suministros médicos"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_2
msgid "Metals and Minerals"
msgstr "Metales y minerales"

#. module: hr_recruitment_integration_monster
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_monster.res_config_settings_view_form
msgid "Monster Credentials"
msgstr "Credenciales de Monster"

#. module: hr_recruitment_integration_monster
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_hr_contract_type__monster_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_currency__monster_id
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_partner_industry__monster_id
msgid "Monster ID"
msgstr "ID de Monster"

#. module: hr_recruitment_integration_monster
#: model:ir.model.fields,help:hr_recruitment_integration_monster.field_hr_contract_type__monster_id
msgid "Monster ID of the contract type."
msgstr "ID de Monster del tipo de contrato"

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_recruitment_platform.py:0
msgid ""
"Monster.com credentials are not set. Please set them in the company settings"
" or ask your administrator."
msgstr ""
"Las credenciales de Monster.com no están configuradas. Configúrelas en los "
"ajustes de su empresa o pregúntele al administrador."

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_25
msgid "Mortgage"
msgstr "Hipotecas"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_47
msgid "Nonprofit Charitable Organizations"
msgstr "Organizaciones sin fines de lucro"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_40
msgid "Nursing/Residential Care Facilities"
msgstr "Centros de atención médica residencial y para ancianos"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_85
msgid "Other/Not Classified"
msgstr "Otro/No clasificado"

#. module: hr_recruitment_integration_monster
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_company__hr_recruitment_monster_password
#: model:ir.model.fields,field_description:hr_recruitment_integration_monster.field_res_config_settings__hr_recruitment_monster_password
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_monster.res_config_settings_view_form
msgid "Password"
msgstr "Contraseña"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_42
msgid "Performing and Fine Arts"
msgstr "Artes escénicas y bellas artes"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_48
msgid "Personal and Household Services"
msgstr "Servicios personales y de cuidado del hogar"

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_hr_recruitment_post_job_wizard
msgid "Post Job"
msgstr "Vacante"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_13
msgid "Printing and Publishing "
msgstr "Imprentas y editoriales"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_26
msgid "Real Estate/Property Management"
msgstr "Gestión inmobiliaria/de propiedades"

#. module: hr_recruitment_integration_monster
#: model:ir.model,name:hr_recruitment_integration_monster.model_hr_recruitment_platform
msgid "Recruitment Platform"
msgstr "Plataforma de reclutamiento"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_27
msgid "Rental Services"
msgstr "Servicios de alquiler"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_49
msgid "Repair and Maintenance Services"
msgstr "Servicios de reparación y mantenimiento"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_45
msgid "Restaurant/Food Services"
msgstr "Servicios de restaurantes y comida"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_17
msgid "Retail"
msgstr "Venta minorista"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_74
msgid "Security and Surveillance"
msgstr "Seguridad y vigilancia"

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_recruitment_platform.py:0
msgid ""
"Service not available: Monster.com service is not available. Please try "
"again later."
msgstr ""
"Servicio no disponible: el servicio de Monster.com no está disponible, "
"inténtelo de nuevo más tarde."

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_41
msgid "Social Services"
msgstr "Servicios sociales"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_43
msgid "Sports and Physical Recreation"
msgstr "Deportes y recreación"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_46
msgid "Staffing/Employment Agencies"
msgstr "Agencias de empleo"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_22
msgid "Telecommunications Services"
msgstr "Servicios de telecomunicaciones"

#. module: hr_recruitment_integration_monster
#. odoo-python
#: code:addons/hr_recruitment_integration_monster/models/hr_job_post.py:0
msgid "This Monster.com job post is not linked to an actual job post."
msgstr ""
"Esta vacante publicada en monster.com no está vinculada a ninguna vacante "
"existente."

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_5
msgid "Trade Contractors"
msgstr "Contratistas"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_19
msgid "Transport and Storage - Materials "
msgstr "Transporte y almacenamiento - Materiales"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_18
msgid "Travel, Transportation and Tourism"
msgstr "Viajes, transportes y turismo"

#. module: hr_recruitment_integration_monster
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_monster.res_config_settings_view_form
msgid "Username"
msgstr "Nombre de usuario"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_37
msgid "Waste Management"
msgstr "Empresas recolectoras de basura"

#. module: hr_recruitment_integration_monster
#: model:res.partner.industry,name:hr_recruitment_integration_monster.monster_industry_15
msgid "Wholesale Trade/Import - Export"
msgstr "Comercio al por mayor/Importación - Exportación"
