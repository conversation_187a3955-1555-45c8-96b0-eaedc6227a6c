# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock
# 
# Translators:
# Sarah Park, 2024
# Wil Odo<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:02+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"\n"
"\n"
"Transfers %(transfer_list)s: You need to supply a Lot/Serial number for products %(product_list)s."
msgstr ""
"\n"
"\n"
" 이동 %(transfer_list)s: %(product_list)s 품목의 로트/일련번호를 입력해야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"(%(serial_number)s) exists in location %(location)s"
msgstr ""
"\n"
"(%(serial_number)s)가 %(location)s에 존재합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"\n"
"The quantity done for the product %(product)s doesn't respect the rounding precision defined on the unit of measure %(unit)s.\n"
"Please change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"\n"
"품목 %(product)s의 최종 수량에는 기본 단위 %(unit)s에 정의되어 있는 소수점 자릿수가 반영되지 않습니다.\n"
"최종 수량 또는 기본 단위의 소수점을 변경해 주세요."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__state
msgid ""
" * Draft: The transfer is not confirmed yet. Reservation doesn't apply.\n"
" * Waiting another operation: This transfer is waiting for another operation before being ready.\n"
" * Waiting: The transfer is waiting for the availability of some products.\n"
"(a) The shipping policy is \"As soon as possible\": no product could be reserved.\n"
"(b) The shipping policy is \"When all products are ready\": not all the products could be reserved.\n"
" * Ready: The transfer is ready to be processed.\n"
"(a) The shipping policy is \"As soon as possible\": at least one product has been reserved.\n"
"(b) The shipping policy is \"When all products are ready\": all product have been reserved.\n"
" * Done: The transfer has been processed.\n"
" * Cancelled: The transfer has been cancelled."
msgstr ""
" * 미결: 재고 이동이 아직 확정되지 않았습니다. 예약이 적용되지 않습니다.\n"
" * 다른 작업 대기 중: 이 재고 이동이 준비되기 전에 다른 작업이 완료되기를 기다리는 중입니다.\n"
" * 대기 중: 일부 품목이 준비되기를 기다리는 중입니다.\n"
"(a) 이동 정책이 \"가능한 빨리\"인 경우: 예약 가능한 품목이 없습니다.\n"
"(b) 이동 정책이 \"모든 품목이 준비되었을 때\"인 경우 : 모든 품목을 예약할 수 없습니다.. \n"
" * 준비: 재고 이동을 처리할 준비가 완료되었습니다.\n"
"(a) 이동 정책이 \"가능한 빨리\"인 경우: 하나 이상의 품목을 예약되었습니다.\n"
"(b) 이동 정책이 \"모든 품목이 준비되었을 때\"인 경우: 모든 품목이 예약되었습니다.\n"
" * 완료: 재고 이동이 완료되었습니다.\n"
" * 취소: 재고 이동이 취소되었습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid " - Product: %(product)s, Lot/Serial Number: %(lot)s"
msgstr " - 품목: %(product)s, Lot/일련번호: %(lot)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,help:stock.field_stock_quant__cyclic_inventory_frequency
msgid ""
" When different than 0, inventory count date for products stored at this "
"location will be automatically set at the defined frequency."
msgstr "0이 아닌 경우, 이 위치에 보관되어 있는 품목에 대해서 재고 실사 날짜가 정의된 주기대로 자동 설정됩니다. "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_count
msgid "# Returns"
msgstr "# 반품"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s (copy)(%(id)s)"
msgstr "%(name)s (사본)(%(id)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence cross dock"
msgstr "%(name)s 시퀀스 크로스 도킹"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence in"
msgstr "%(name)s 시퀀스"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence internal"
msgstr "%(name)s 시퀀스 내부"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence out"
msgstr "%(name)s 시퀀스 아웃"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence packing"
msgstr "%(name)s 시퀀스 패킹"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence picking"
msgstr "%(name)s 시퀀스 피킹"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence quality control"
msgstr "%(name)s 시퀀스 품질 관리"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(name)s Sequence storage"
msgstr "%(name)s 시퀀스 보관"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"%(operations)s have default source or destination locations within warehouse"
" %(warehouse)s, therefore you cannot archive it."
msgstr "%(operations)s 의 기본 원본 또는 대상 위치가 %(warehouse)s 내에 있으므로 보관할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "%(product)s: Insufficient Quantity To Scrap"
msgstr "%(product)s: 폐기할 수량이 충분하지 않습니다"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"%(product_name)s --> Product UoM is %(product_uom)s "
"(%(product_uom_category)s) - Move UoM is %(move_uom)s "
"(%(move_uom_category)s)"
msgstr ""
"%(product_name)s --> 품목의 기본 단위는 %(product_uom)s (%(product_uom_category)s) -"
" 이동 기본 단위는 %(move_uom)s (%(move_uom_category)s)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%(warehouse)s Sequence %(code)s"
msgstr "%(warehouse)s 시퀀스 %(code)s"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid ""
"%(warehouse)s can only provide %(free_qty)s %(uom)s, while the quantity to "
"order is %(qty_to_order)s %(uom)s."
msgstr ""
"%(warehouse)s 항목은 %(free_qty)s %(uom)s 수량으로만 제공할 수 있습니다. 주문한 수량은 "
"%(qty_to_order)s %(uom)s입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%(warehouse)s: Supply Product from %(supplier)s"
msgstr "%(warehouse)s : %(supplier)s로부터 품목 공급"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_package_type.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_rule.py:0
#: code:addons/stock/models/stock_storage_category.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "%s [reverted]"
msgstr "%s [되돌림]"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "%s days"
msgstr "%s 일"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "%s: Can't split: quantities done can't be above demand"
msgstr "%s: 분할 불가: 처리 수량이 수요를 초과할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split, all demand is done. For split you need at least one "
"line not fully fulfilled"
msgstr "%s: 분할할 항목이 없으며 모든 수요가 충족되었습니다. 분할하려면 완전히 충족되지 않은 라인이 하나 이상 필요합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"%s: Nothing to split. Fill the quantities you want in a new transfer in the "
"done quantities"
msgstr "%s: 분할할 수량이 없습니다. 이미 처리된 수량으로 새 전송에 원하는 수량을 입력하세요."

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_inventory
msgid "'Count Sheet'"
msgstr "'카운트 시트'"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_delivery
msgid ""
"'Delivery Slip - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr "'배송 전표 - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_location_barcode
msgid "'Location - %s' % object.name"
msgstr "'위치 - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_lot_label
msgid "'Lot-Serial - %s' % object.name"
msgstr "'LOT-일련번호 - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_type_label
msgid "'Operation-type - %s' % object.name"
msgstr "'작업-유형 - %s' % object.name"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking_packages
msgid "'Packages - %s' % (object.name)"
msgstr "'패키지 - %s' % (object.name)"

#. module: stock
#: model:ir.actions.report,print_report_name:stock.action_report_picking
msgid ""
"'Picking Operations - %s - %s' % (object.partner_id.name or '', object.name)"
msgstr "'선별 작업 - %s - %s' % (object.partner_id.name or '', object.name)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "(copy of) %s"
msgstr "%s (사본) "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(document barcode)"
msgstr "(문서 바코드)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(package barcode)"
msgstr "(패키지 바코드)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(product barcode)"
msgstr "(품목 바코드)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "(serial barcode)"
msgstr "(일련번호 바코드)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__state
#: model:ir.model.fields,help:stock.field_stock_move_line__state
msgid ""
"* New: The stock move is created but not confirmed.\n"
"* Waiting Another Move: A linked stock move should be done before this one.\n"
"* Waiting Availability: The stock move is confirmed but the product can't be reserved.\n"
"* Available: The product of the stock move is reserved.\n"
"* Done: The product has been transferred and the transfer has been confirmed."
msgstr ""
"* 신규: 재고 이동이 생성되었으나 승인되지 않았습니다.\n"
"* 다른 이동 대기 중: 이 이동 전에 연결된 재고 이동이 먼저 완료되어야 합니다.\n"
"* 예약 대기 중: 재고 이동이 확인되었으나 품목을 예약할 수 없습니다.\n"
"* 사용 가능: 재고 이동 품목이 예약되었습니다.\n"
"* 완료: 품목 이전 후 승인 완료되었습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__usage
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move__location_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_dest_usage
#: model:ir.model.fields,help:stock.field_stock_move_line__location_usage
msgid ""
"* Vendor Location: Virtual location representing the source location for products coming from your vendors\n"
"* View: Virtual location used to create a hierarchical structures for your warehouse, aggregating its child locations ; can't directly contain products\n"
"* Internal Location: Physical locations inside your own warehouses,\n"
"* Customer Location: Virtual location representing the destination location for products sent to your customers\n"
"* Inventory Loss: Virtual location serving as counterpart for inventory operations used to correct stock levels (Physical inventories)\n"
"* Production: Virtual counterpart location for production operations: this location consumes the components and produces finished products\n"
"* Transit Location: Counterpart location that should be used in inter-company or inter-warehouses operations"
msgstr ""
"* 공급업체 위치: 공급업체에서 제공하는 제품의 기본 위치를 나타내는 가상 위치입니다.\n"
"* 화면: 창고의 구조를 생성하고 하위 위치를 집계하는 데 사용되는 가상 위치로 제품을 직접 포함하지 않습니다.\n"
"* 내부 위치: 자체 창고 내부의 실제 위치입니다.\n"
"* 고객 위치: 고객에게 보낸 제품의 목적지 위치를 나타내는 가상 위치입니다.\n"
"* 재고 손실: 물리적인 재고 수준을 수정하여 재고 작업에 사용되는 가상 위치입니다.\n"
"* 생산: 생산 작업을 위한 가상 대응 위치: 이 위치는 부품을 소비하고 완제품을 생산합니다.\n"
"* 운송 위치: 회사 간 또는 창고 간 작업에 사용하는 파트너 창고 위치입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d일"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid ", max:"
msgstr ", 최대 :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "-&gt;"
msgstr "-&gt;"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid ""
".\n"
"            Manual actions may be needed."
msgstr ""
".\n"
"            수동 작업이 필요할 수 있습니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__day
msgid "1 Day"
msgstr "1 일"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__month
msgid "1 Month"
msgstr "1 달"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__week
msgid "1 Week"
msgstr "1 주"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "12.0"
msgstr "12.0"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
msgid "1234567890"
msgstr "1234567890"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "12345678901"
msgstr "12345678901"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__2x7xprice
msgid "2 x 7 with price"
msgstr "2 x 7, 가격 표시"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "2021-9-01"
msgstr "2021-9-01"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "2023-01-01"
msgstr "2023-01-01"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "2023-09-24"
msgstr "2023-09-24"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "3.00"
msgstr "3.00"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__4x12
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12
msgid "4 x 12"
msgstr "4 x 12"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_lots
msgid "4 x 12 - One per lot/SN"
msgstr "4 x 12 - 로트 및 일련번호 당 하나"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__4x12_units
msgid "4 x 12 - One per unit"
msgstr "4 x 12 - 단위 당 하나"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x12xprice
msgid "4 x 12 with price"
msgstr "4 x 12, 가격 표시"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__4x7xprice
msgid "4 x 7 with price"
msgstr "4 x 7, 가격 표시"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "54326786758"
msgstr "54326786758"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid ""
"<br/>\n"
"                    <strong>Current Inventory: </strong>"
msgstr ""
"<br/>\n"
"                    <strong>현 재고: </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"<br/>\n"
"                Want to speed up operations?"
msgstr ""
"<br/>\n"
"                작업 속도를 높이고 싶으신가요?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>A need is created in <b>%s</b> and a rule will be triggered to fulfill "
"it."
msgstr "<br><b>%s</b>에서 필요가 제기되고 이를 이행하기 위한 규칙이 트리거됩니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>If the products are not available in <b>%s</b>, a rule will be triggered"
" to bring the missing quantity in this location."
msgstr "<br><b>%s</b>에서 제품을 사용할 수 없는 경우 이 위치에서 누락된 수량을 채우기 위한 규칙이 활성화됩니다. "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"<br>The products will be moved towards <b>%(destination)s</b>, <br/> as "
"specified from <b>%(operation)s</b> destination."
msgstr ""
"<br>품목은 <b>%(destination)s</b>까지 <br/><b>%(operation)s</b>에서 지정된 대로 이동합니다."

#. module: stock
#: model:mail.template,body_html:stock.mail_template_data_delivery_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"        We are glad to inform you that your order has been shipped.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            Your tracking reference is\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        Please find your delivery order attached for more details.<br/><br/>\n"
"        Thank you,\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        안녕하세요, <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>님.<br/><br/>\n"
"        고객님이 주문하신 상품이 발송되었습니다.\n"
"        <t t-if=\"hasattr(object, 'carrier_tracking_ref') and object.carrier_tracking_ref\">\n"
"            배송 조회 번호는 다음과 같습니다:\n"
"            <strong>\n"
"            <t t-if=\"object.carrier_tracking_url\">\n"
"                <t t-set=\"multiple_carrier_tracking\" t-value=\"object.get_multiple_carrier_tracking()\"/>\n"
"                <t t-if=\"multiple_carrier_tracking\">\n"
"                    <t t-foreach=\"multiple_carrier_tracking\" t-as=\"line\">\n"
"                        <br/><a t-att-href=\"line[1]\" target=\"_blank\" t-out=\"line[0] or ''\"/>\n"
"                    </t>\n"
"                </t>\n"
"                <t t-else=\"\">\n"
"                    <a t-attf-href=\"{{ object.carrier_tracking_url }}\" target=\"_blank\" t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"                </t>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                <t t-out=\"object.carrier_tracking_ref or ''\"/>.\n"
"            </t>\n"
"            </strong>\n"
"        </t>\n"
"        <br/><br/>\n"
"        자세한 내용은 첨부된 배송 주문서를 확인해 주세요.<br/><br/>\n"
"        감사합니다.\n"
"        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </p>\n"
"</div>\n"
"        "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                All products could not be reserved. Click on the \"Check Availability\" button to try to reserve products."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\"/>\n"
"                                모든 품목을 예약할 수 없습니다. 품목 예약을 시도하려면 \"사용 가능 확인\" 버튼을 클릭합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Back Orders</span>"
msgstr "<span class=\"col-6\">이월 주문</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Late</span>"
msgstr "<span class=\"col-6\">지연</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Operations</span>"
msgstr "<span class=\"col-6\">작업</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "<span class=\"col-6\">Waiting</span>"
msgstr "<span class=\"col-6\">대기 중</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "<span class=\"o_form_label\">Scannable Package Contents</span>"
msgstr "<span class=\"o_form_label\">스캔 가능한 패키지 구성품</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Allocation</span>"
msgstr "<span class=\"o_stat_text\">배정</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Forecasted</span>"
msgstr "<span class=\"o_stat_text\">예상 재고</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">In:</span>"
msgstr "<span class=\"o_stat_text\">입고:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Location</span>"
msgstr "<span class=\"o_stat_text\">위치</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "<span class=\"o_stat_text\">Lot/Serial Numbers</span>"
msgstr "<span class=\"o_stat_text\">Lot/일련번호</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Max:</span>"
msgstr "<span class=\"o_stat_text\">최대:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Min:</span>"
msgstr "<span class=\"o_stat_text\">최소:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Moves</span>"
msgstr "<span class=\"o_stat_text\">이동</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Next Transfer</span>"
msgstr "<span class=\"o_stat_text\">다음 이동</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">On Hand</span>"
msgstr "<span class=\"o_stat_text\">현 재고</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "<span class=\"o_stat_text\">Operations</span>"
msgstr "<span class=\"o_stat_text\">작업</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Out:</span>"
msgstr "<span class=\"o_stat_text\">출고:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "<span class=\"o_stat_text\">Product Moves</span>"
msgstr "<span class=\"o_stat_text\">품목 이동</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Putaway Rules</span>"
msgstr "<span class=\"o_stat_text\">보관 규칙</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "<span class=\"o_stat_text\">Routes</span>"
msgstr "<span class=\"o_stat_text\">경로</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "<span class=\"o_stat_text\">Storage Capacities</span>"
msgstr "<span class=\"o_stat_text\">보관 용량</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "<span class=\"o_stat_text\">Traceability</span>"
msgstr "<span class=\"o_stat_text\">추적가능성</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<span><strong>Delivery Address:</strong></span>"
msgstr "<span><strong>배송 주소:</strong></span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "<span>OBTRETU</span>"
msgstr "<span>OBTRETU</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "<span>On Hand: </span>"
msgstr "<span>보유 중: </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "<span>Package Type: </span>"
msgstr "<span>패키지 유형: </span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_delivery_no_package_section_line
msgid "<span>Products with no package assigned</span>"
msgstr "<span>패키지가 할당되지 않은 품목</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<span>Remaining quantities not yet delivered:</span>"
msgstr "<span>아직 배송 되지 않은 잔여 수량:</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "<span>days</span>"
msgstr "<span>일</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "<span>×</span>"
msgstr "<span>×</span>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid ""
"<strong>\n"
"                The done move line has been corrected.\n"
"            </strong>"
msgstr ""
"<strong>\n"
"                완료된 이동 명세가 수정되었습니다.\n"
"            </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Customer Address</strong>"
msgstr "<strong>고객 주소</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Delivery address</strong>"
msgstr "<strong>배송 주소</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"<strong>Due to some stock moves done between your initial update of the "
"quantity and now, the difference of quantity is not consistent "
"anymore.</strong>"
msgstr ""
"<strong>초기에 업데이트된 수량과 현재 수량 사이에 일부 재고 이동이 발생하여 수량 차이가 더 이상 일정하게 확인되지 "
"않습니다.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>From</strong>"
msgstr "<strong>출발</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Lot/Serial Number</strong>"
msgstr "<strong>LOT/일련번호</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Max qty:</strong>"
msgstr "<strong>최대 수량:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_warehouse_orderpoint_kanban
msgid "<strong>Min qty:</strong>"
msgstr "<strong>최소 수량:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Order</strong>"
msgstr "<strong>주문</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Pack Date:</strong>"
msgstr "<strong>패키지 날짜:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "<strong>Package Type:</strong>"
msgstr "<strong>패키지 유형 : </strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product Barcode</strong>"
msgstr "<strong>품목 바코드</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Product</strong>"
msgstr "<strong>품목</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Quantity</strong>"
msgstr "<strong>수량</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Recipient address</strong>"
msgstr "<strong>수신인 주소</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Scheduled Date</strong>"
msgstr "<strong>예정 날짜</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Shipping Date</strong>"
msgstr "<strong>배송 날짜</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "<strong>Signature</strong>"
msgstr "<strong>서명</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Status</strong>"
msgstr "<strong>상태</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_head
msgid "<strong>The initial demand has been updated.</strong>"
msgstr "<strong>초기 수량이 업데이트 되었습니다.</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>To</strong>"
msgstr "<strong>도착</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "<strong>Tracked product(s):</strong>"
msgstr "<strong>확인된 품목:</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Vendor Address</strong>"
msgstr "<strong>공급업체 주소</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse Address</strong>"
msgstr "<strong>창고 주소</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "<strong>Warehouse address</strong>"
msgstr "<strong>창고 주소</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid "<strong>Where do you want to send the products?</strong>"
msgstr "<strong>품목을 어디로 보낼까요?</strong>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "? This may lead to inconsistencies in your inventory."
msgstr "? 이로 인해 재고 현황이 일치하지 않을 수 있습니다."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_barcode_uniq
msgid "A barcode can only be assigned to one package type!"
msgstr "바코드는 하나의 패키지 유형에만 할당할 수 있습니다!"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_product_location_check
msgid "A replenishment rule already exists for this product on this location."
msgstr "이미 품목에 대한 재보충 규칙이 존재합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__is_storable
#: model:ir.model.fields,help:stock.field_product_template__is_storable
#: model:ir.model.fields,help:stock.field_stock_move__is_storable
msgid "A storable product is a product for which you manage stock."
msgstr "저장 가능 품목이란 재고로 관리되는 품목입니다."

#. module: stock
#: model:res.groups,name:stock.group_warning_stock
msgid "A warning can be set on a partner (Stock)"
msgstr "협력사(재고)에 경고를 설정할 수 있습니다"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__action
msgid "Action"
msgstr "추가 작업"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__replenish_location
msgid ""
"Activate this function to get all quantities to replenish at this particular"
" location"
msgstr "이 기능을 활성화시키면 해당 특정 위치에서 전체 수량을 보충할 수 있습니다. "

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__active
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__active
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_route__active
#: model:ir.model.fields,field_description:stock.field_stock_rule__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__active
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__active
msgid "Active"
msgstr "활성화"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_ids
msgid "Activities"
msgstr "활동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_state
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_icon
msgid "Activity Type Icon"
msgstr "활동 유형 아이콘"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_view_activity
msgid "Activity view"
msgstr "활동 보기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Add a Product"
msgstr "품목 추가"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid "Add a lot/serial number"
msgstr "LOT/일련번호 추가하기"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid "Add a new location"
msgstr "새 위치 추가"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid "Add a new route"
msgstr "새 경로 추가"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_storage_category
msgid "Add a new storage category"
msgstr "새로운 보관 카테고리 추가"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid ""
"Add an internal note that will be printed on the Picking Operations sheet"
msgstr "선별 작업표에 인쇄될 내부용 노트 추가하기"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_adv_location
msgid ""
"Add and customize route operations to process product moves in your warehouse(s): e.g. unload > quality control > stock for incoming products, pick > pack > ship for outgoing products. \n"
" You can also set putaway strategies on warehouse locations in order to send incoming products into specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"창고 내 품목 이동을 처리하기 위한 이동 경로를 추가합니다. (예: 입고 품목의 경우 하역 > 품질 관리 > 재고, 발송 품목의 경우 선별 > 포장 > 배송 등)\n"
"또한 특정 하위 위치 (예. 특정 보관함 또는 선반)로 바로 입고되는 품목을 전달하기 위해 창고 위치에 대한 보관 규칙을 설정할 수 있습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Add and customize route operations to process product moves in your "
"warehouse(s): e.g. unload > quality control > stock for incoming products, "
"pick > pack > ship for outgoing products. You can also set putaway "
"strategies on warehouse locations in order to send incoming products into "
"specific child locations straight away (e.g. specific bins, racks)."
msgstr ""
"창고 내 품목 이동을 처리하기 위한 이동 경로를 추가합니다. (예: 입고 품목의 경우 하역 > 품질 관리 > 재고, 발송 품목의 경우 선별 > 포장 > 배송 등)\n"
"또한 특정 하위 위치 (예. 특정 보관함 또는 선반)로 바로 입고되는 품목을 전달하기 위해 창고 위치에 대한 보관 규칙을 설정할 수 있습니다.."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/fields/stock_move_line_x2_many_field.js:0
msgid "Add line: %s"
msgstr "항목 추가: %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Add quality checks to your transfer operations"
msgstr "이송 작업에 품질 검사를 추가합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Additional Info"
msgstr "추가 정보"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__comment
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Additional Information"
msgstr "추가 정보"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__address
#: model:ir.model.fields,field_description:stock.field_stock_picking__warehouse_address_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__partner_id
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Address"
msgstr "주소"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__partner_address_id
msgid "Address where goods should be delivered. Optional."
msgstr "품목이 전송될 주소를 나타냅니다. 선택 입력 사항입니다."

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_adjustments
msgid "Adjustments"
msgstr "조정"

#. module: stock
#: model:res.groups,name:stock.group_stock_manager
msgid "Administrator"
msgstr "관리자"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Advanced Scheduling"
msgstr "고급 예약 실행"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_order
msgid "Advanced: Apply Procurement Rules"
msgstr "고급 : 조달 규칙 적용"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__after
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "After"
msgstr "이후"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "All"
msgstr "전체"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Categories"
msgstr "모든 카테고리"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "All Companies"
msgstr "모든 회사"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "All Products"
msgstr "모든 품목"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_action_picking_type
msgid "All Transfers"
msgstr "전체 이동"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
msgid "All Warehouses"
msgstr "전체 창고"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__one
msgid "All at once"
msgstr "한번에"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"All our contractual relations will be governed exclusively by United States "
"law."
msgstr "당사의 모든 계약 관계는 전적으로 미국법에 의거합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__returned_move_ids
msgid "All returned moves"
msgstr "전체 반품 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__allow_new_product
msgid "Allow New Product"
msgstr "새로운 품목 허용"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__mixed
msgid "Allow mixed products"
msgstr "품목 혼합 허용"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__allowed_location_ids
msgid "Allowed Location"
msgstr "허용된 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__allowed_route_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__allowed_route_ids
msgid "Allowed Route"
msgstr "이동 경로 사용 허용"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__always
msgid "Always"
msgstr "항상"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Andrwep"
msgstr "Andrwep"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Annual Inventory Day and Month"
msgstr "연간 재고 조사 날짜"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_month
msgid "Annual Inventory Month"
msgstr "연간 재고 월"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_month
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_month
msgid ""
"Annual inventory month for products not in a location with a cyclic "
"inventory date. Set to no month if no automatic annual inventory."
msgstr ""
"정기 재고 조사일이 지정된 장소에 위치하지 않는 품목에 대한 연간 재고 월입니다. 연간 재고가 자동으로 되어 있지 않은 경우에는 월 "
"없음으로 설정합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"Another parent/sub replenish location %s exists, if you wish to change it, "
"uncheck it first"
msgstr "다른 상위/하위 보충 %s 위치가 존재합니다. 변경하시려면 먼저 선택 취소하시기 바랍니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Any Category"
msgstr "모든 카테고리"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Applicability"
msgstr "적용 여부"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Applicable On"
msgstr "적용 가능"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_selectable
msgid "Applicable on Packaging"
msgstr "패키지에 적용 가능"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_selectable
msgid "Applicable on Product"
msgstr "품목에 적용 가능"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__product_categ_selectable
msgid "Applicable on Product Category"
msgstr "품목 카테고리에 적용 가능"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_selectable
msgid "Applicable on Warehouse"
msgstr "창고에 적용 가능"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Apply"
msgstr "적용"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Apply All"
msgstr "전체 적용"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__route_id
#: model:ir.model.fields,help:stock.field_stock_replenish_mixin__route_id
msgid ""
"Apply specific route for the replenishment instead of product's default "
"routes."
msgstr "품목 보충용으로 기본 경로 대신 특정 경로를 적용합니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__4
msgid "April"
msgstr "4월"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Archived"
msgstr "보관됨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Are you sure you want to cancel this transfer?"
msgstr "이 이송을 취소하시겠습니까?"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__direct
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__direct
msgid "As soon as possible"
msgstr "가능한 빨리"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__ask
msgid "Ask"
msgstr "문의"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Assign"
msgstr "할당"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Assign All"
msgstr "전체 배정"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__owner_id
msgid "Assign Owner"
msgstr "소유자 배정"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Assigned Moves"
msgstr "배정된 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__user_id
msgid "Assigned To"
msgstr "담당자"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__at_confirm
msgid "At Confirmation"
msgstr "확인 시"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "At Customer"
msgstr "고객"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_attachment_count
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_attribute_action
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Attributes"
msgstr "속성"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__8
msgid "August"
msgstr "8월"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__auto
msgid "Auto"
msgstr "자동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_delivery_slip
msgid "Auto Print Delivery Slip"
msgstr "배송 전표 자동 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_lot_labels
msgid "Auto Print Lot/SN Labels"
msgstr "로트 및 일련번호 라벨 자동 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_package_label
msgid "Auto Print Package Label"
msgstr "패키지 라벨 자동 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_packages
msgid "Auto Print Packages"
msgstr "패키지 자동 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_product_labels
msgid "Auto Print Product Labels"
msgstr "품목 라벨 자동 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report
msgid "Auto Print Reception Report"
msgstr "입고 보고서 자동 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid "Auto Print Reception Report Labels"
msgstr "입고 보고서 라벨 자동 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_print_return_slip
msgid "Auto Print Return Slip"
msgstr "반품 전표 자동 인쇄"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Automate"
msgstr "자동 주문"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__auto
msgid "Automatic Move"
msgstr "자동 이동"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__transparent
msgid "Automatic No Step Added"
msgstr "추가 단계없이 자동화"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__assigned
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Available"
msgstr "사용 가능"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Available Products"
msgstr "사용 가능 품목"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__available_quantity
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Available Quantity"
msgstr "가용수량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Available quantity should be set to zero before changing inventory tracking"
msgstr "재고 추적을 변경하기 전에 사용 가능한 수량을 0으로 설정해야 합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_id
msgid "Back Order of"
msgstr "이월 주문"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__backorder_ids
msgid "Back Orders"
msgstr "이월 주문"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation
msgid "Backorder Confirmation"
msgstr "이월 주문 확인"

#. module: stock
#: model:ir.model,name:stock.model_stock_backorder_confirmation_line
msgid "Backorder Confirmation Line"
msgstr "이월 주문 승인 내역"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__backorder_confirmation_line_ids
msgid "Backorder Confirmation Lines"
msgstr "이월 주문 승인 내역"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Backorder creation"
msgstr "이월 주문 생성"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Backorders"
msgstr "이월 주문"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__barcode
#: model:ir.model.fields,field_description:stock.field_stock_package_type__barcode
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__barcode
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Barcode"
msgstr "바코드"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Barcode Demo"
msgstr "바코드 데모"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_wms_barcode_nomenclature_all
msgid "Barcode Nomenclatures"
msgstr "바코드 명명법"

#. module: stock
#: model:ir.model,name:stock.model_barcode_rule
msgid "Barcode Rule"
msgstr "바코드 규칙"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode
msgid "Barcode Scanner"
msgstr "바코드 스캐너"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__valid_ean
msgid "Barcode is valid EAN"
msgstr "바코드가 유효한 EAN 입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_picking_batch
msgid "Batch, Wave & Cluster Transfers"
msgstr "배치, 웨이브 및 클러스터 전송"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__before
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Before"
msgstr "이전"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__by_date
msgid "Before scheduled date"
msgstr "예정일 전"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Below text serves as a suggestion and doesn’t engage Odoo S.A. "
"responsibility."
msgstr "아래의 내용은 참고용으로만 제시되는 내용이며 Odoo S.A.는 해당 내용에 대한 책임을 지지 않습니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__block
msgid "Blocking Message"
msgstr "차단 메시지"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Blocking: %s"
msgstr "차단: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__quant_ids
msgid "Bulk Content"
msgstr "대량 콘텐츠"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__weight_bulk
msgid "Bulk Weight"
msgstr "벌크 중량"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__lot
msgid "By Lots"
msgstr "LOT별"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__none
msgid "By Quantity"
msgstr "수량별"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_template__tracking__serial
msgid "By Unique Serial Number"
msgstr "고유 일련번호별"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__procure_method
msgid ""
"By default, the system will take from the stock in the source location and "
"passively wait for availability. The other possibility allows you to "
"directly create a procurement on the source location (and thus ignore its "
"current stock) to gather products. If we want to chain moves and have this "
"one to wait for the previous, this second option should be chosen."
msgstr ""
"기본적으로, 시스템은 자재 위치에 재고에서 소요되며 수동적으로 가용성을 기다립니다. 다른 가능성은 품목을 수집하기 위한 자재 위치(및 "
"현재 재고 무시에 따라서)에 구매를 직접 생성할 수 있습니다. 연결된 이동이 필요하면 이전 작업을 대기하고 있을 경우, 두 번째 옵션을 "
"선택해야 합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__active
msgid ""
"By unchecking the active field, you may hide a location without deleting it."
msgstr "활성 필드의 선택을 취소하면, 삭제하지 않고 위치를 숨길 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "COPY"
msgstr "복사하기"

#. module: stock
#: model:product.template,name:stock.product_cable_management_box_product_template
msgid "Cable Management Box"
msgstr "케이블 보관함"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_calendar
msgid "Calendar View"
msgstr "일정표 화면"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any customer or supplier location."
msgstr "고객 또는 공급업체 창고를 찾을 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Can't find any generic route %s."
msgstr "일반 경로 %s를 찾을 수 없습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Cancel"
msgstr "취소"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_cancel
msgid "Cancel Next Move"
msgstr "다음 이동 취소"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__cancel
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__cancel
msgid "Cancelled"
msgstr "취소됨"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot move an empty package"
msgstr "빈 패키지를 이동할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Cannot remove the location of a non empty package"
msgstr "비어 있지 않은 패키지의 위치를 제거할 수 없습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__capacity_ids
msgid "Capacity"
msgstr "생산 능력"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Package"
msgstr "패키지별 용량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Capacity by Product"
msgstr "품목별 용량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_category_id
#: model_terms:ir.ui.view,arch_db:stock.product_search_form_view_stock_report
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Category"
msgstr "카테고리"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__route_from_categ_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_from_categ_ids
msgid "Category Routes"
msgstr "범주 경로"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Certain countries apply withholding at source on the amount of invoices, in "
"accordance with their internal legislation. Any withholding at source will "
"be paid by the client to the tax authorities. Under no circumstances can My "
"Company (Chicago) become involved in costs related to a country's "
"legislation. The amount of the invoice will therefore be due to My Company "
"(Chicago) in its entirety and does not include any costs relating to the "
"legislation of the country in which the client is located."
msgstr ""
"일부 국가에서는 국내 법령에 따라 청구서 금액에 대해 원천징수를 적용하고 있습니다. 원천징수액은 고객이 세무 당국에 납부합니다. 어떠한 "
"경우에도 국가 법령과 관련한 비용에 My Company (Chicago)사는 관여할 수 없습니다. 따라서 청구서 상의 금액 전액을 My "
"Company (Chicago) 사에 지불하여야 하며, 고객이 위치하고 있는 국가의 법령과 관련된 비용은 이에 포함되지 않습니다. "

#. module: stock
#: model:ir.actions.act_window,name:stock.action_change_product_quantity
#: model:ir.model,name:stock.model_stock_change_product_qty
msgid "Change Product Quantity"
msgstr "품목 수량 변경"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Changing the Lot/Serial number for move lines with different products is not"
" allowed."
msgstr "다른 제품이 포함된 이동 라인의 로트/일련 번호 변경은 허용되지 않습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_orderpoint.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Changing the company of this record is forbidden at this point, you should "
"rather archive it and create a new one."
msgstr "이 시점에서는 이 레코드의 회사를 변경하는 것이 금지되므로 보관하고 새 레코드를 만드는 것이 좋습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Changing the operation type of this record is forbidden at this point."
msgstr "이 시점에서는 이 레코드의 작업 유형을 변경할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Changing the product is only allowed in 'Draft' state."
msgstr "품목 변경은 '초안' 상태에서만 허용됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__barcode_separator
msgid ""
"Character(s) used to separate data contained within an aggregate barcode "
"(i.e. a barcode containing multiple barcode encodings)"
msgstr "집계 바코드(즉, 여러 바코드 인코딩이 포함된 바코드) 내의 데이터를 구분하는 데 사용되는 문자입니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Check Availability"
msgstr "사용 가능 확인"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_packages
msgid "Check the existence of destination packages on move lines"
msgstr "이동 내역에 대상 꾸러미 상품가 있는지 확인하십시오"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_line_exist
msgid "Check the existence of pack operation on the picking"
msgstr "선별에 포장 작업이 있는지 확인하십시오"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__print_label
msgid ""
"Check this box if you want to generate shipping label in this operation."
msgstr "이 작업에 대한 배송 라벨을 생성하려면 확인란을 선택합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__scrap_location
#: model:ir.model.fields,help:stock.field_stock_move__scrapped
msgid ""
"Check this box to allow using this location to put scrapped/damaged goods."
msgstr "이 위치를 사용하여 폐기/손상된 상품을 넣을 수 있게 하려면 이 상자를 선택하십시오."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/wizard/stock_label_type.py:0
msgid "Choose Labels Layout"
msgstr "라벨 인쇄용 레이아웃 선택"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose Type of Labels To Print"
msgstr "인쇄할 라벨 유형 선택"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quantity_history__inventory_datetime
#: model:ir.model.fields,help:stock.field_stock_request_count__inventory_date
msgid "Choose a date to get the inventory at that date"
msgstr "해당 날짜에 재고를 확보할 날짜를 선택하십시오"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Choose destination location"
msgstr "목적지 위치 선택"

#. module: stock
#: model:ir.model,name:stock.model_lot_label_layout
msgid "Choose the sheet layout to print lot labels"
msgstr "로트 라벨 인쇄용 시트 레이아웃 선택"

#. module: stock
#: model:ir.model,name:stock.model_product_label_layout
msgid "Choose the sheet layout to print the labels"
msgstr "라벨 인쇄용 시트 레이아웃 선택"

#. module: stock
#: model:ir.model,name:stock.model_picking_label_type
msgid "Choose whether to print product or lot/sn labels"
msgstr "품목이나 LOT/일련번호 라벨 인쇄 여부 선택"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Choose your date"
msgstr "날짜 선택"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Clear"
msgstr "제거"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Close"
msgstr "닫기"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__closest_location
#: model:product.removal,name:stock.removal_closest
msgid "Closest Location"
msgstr "가까운 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__color
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__color
msgid "Color"
msgstr "색상"

#. module: stock
#: model:ir.model,name:stock.model_res_company
msgid "Companies"
msgstr "회사"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__company_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_location__company_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move__company_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__company_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__company_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__company_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__company_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__company_id
#: model:ir.model.fields,field_description:stock.field_stock_route__company_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__company_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__company_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__company_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__company_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Company"
msgstr "회사"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs"
msgstr "배송 커넥터를 사용하여 운송비를 계산합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr "DHL로 배송비를 계산 및 배송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr "Easypost로 배송비를 계산 및 배송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr "FedEx로 배송비를 계산 및 배송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr "Sendcloud로 배송비를 계산 및 배송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Shiprocket"
msgstr "Shiprocket으로 배송비를 계산 및 배송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with Starshipit"
msgstr "Starshipit로 배송비 계산 및 배송"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr "UPS로 배송비를 계산 및 배송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr "USPS로 배송비를 계산 및 배송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr "bpost로 배송비를 계산 및 배송합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__reservation_date
msgid "Computes when a move should be reserved"
msgstr "이동 예약을 해야 하는 경우를 계산합니다"

#. module: stock
#: model:ir.model,name:stock.model_res_config_settings
msgid "Config Settings"
msgstr "환경설정"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_config_settings
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Configuration"
msgstr "설정"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.lot_label_layout_form_picking
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.picking_label_type_form
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quantity_history
msgid "Confirm"
msgstr "승인"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__confirmed
msgid "Confirmed"
msgstr "확인 완료"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_conflict
msgid "Conflict in Inventory"
msgstr "재고 충돌"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Conflict in Inventory Adjustment"
msgstr "재고 조정 충돌"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_to_fix_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Conflicts"
msgstr "충돌"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__visibility_days
msgid ""
"Consider product forecast these many days in the future upon product replenishment, set to 0 for just-in-time.\n"
"The value depends on the type of the route (Buy or Manufacture)"
msgstr ""
"향후 며칠 동안의 품목 예측을 고려하여 재보충이 실행되며, 0으로 설정 시 적시 공급이 이루어집니다..\n"
"해당 값은 경로 유형 (매입 또는 제조)에 따라 달라질 수 있습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_owner
msgid "Consignment"
msgstr "위탁매매"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__consume_line_ids
msgid "Consume Line"
msgstr "소비 내역"

#. module: stock
#: model:ir.model,name:stock.model_res_partner
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_partner_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Contact"
msgstr "연락처"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_ids
msgid "Contains"
msgstr "포함"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Content"
msgstr "내용"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Contents"
msgstr "콘텐츠"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Continue"
msgstr "계속"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
msgid "Control panel buttons"
msgstr "제어판 버튼 "

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_move_line__product_uom_category_id
#: model:ir.model.fields,help:stock.field_stock_scrap__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr "단위 사이의 변환은 동일 카테고리에서만 가능합니다. 변환은 비율에 따라 이루어집니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posx
msgid "Corridor (X)"
msgstr "열 (X)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__set_count
msgid "Count"
msgstr "수량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_move_ready
msgid "Count Move Ready"
msgstr "이동 준비 계산"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking
msgid "Count Picking"
msgstr "선별 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_backorders
msgid "Count Picking Backorders"
msgstr "이월 주문 선별 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_draft
msgid "Count Picking Draft"
msgstr "초안 선별 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_late
msgid "Count Picking Late"
msgstr "지연 선별 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_ready
msgid "Count Picking Ready"
msgstr "진행 선별 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__count_picking_waiting
msgid "Count Picking Waiting"
msgstr "대기 선별 수"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_inventory
msgid "Count Sheet"
msgstr "시트 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Counted Quantity"
msgstr "계산된 수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Counterpart Locations"
msgstr "상대방 창고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_backorder
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "Create Backorder"
msgstr "이월 주문 생성"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Create Backorder?"
msgstr "이월 주문 생성"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Create New"
msgstr "새로 만들기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_create_lots
msgid "Create New Lots/Serial Numbers"
msgstr "새 LOT/일련번호 만들기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid "Create Stock"
msgstr "재고 생성"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid ""
"Create a backorder if you expect to process the remaining\n"
"                            products later. Do not create a backorder if you will not\n"
"                            process the remaining products."
msgstr ""
"나머지 품목을 나중에 처리하려면 이월주문서를 생성하십시오.\n"
"                       나머지 품목을 처리하지 않는 경우에는\n"
"                       이월주문서를 생성하지 마십시오."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid "Create a new operation type"
msgstr "새 작업 유형 만들기"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid "Create a new package"
msgstr "신규 패키지 생성"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create customizable worksheets for your quality checks"
msgstr "품질 검사를 위한 맞춤형 워크시트를 생성합니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid ""
"Create new putaway rules to dispatch automatically specific products to "
"their appropriate destination location upon receptions."
msgstr "제품 입고 시 특정 품목을 정확한 목적지로 자동 발송할 수 있도록 새로운 보관규칙을 생성합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Create products easily by scanning using"
msgstr "다음으로 스캔하여 쉽게 품목을 생성합니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "Create some storable products to see their stock info in this view."
msgstr "이 화면에서 재고 정보를 보려면 저장 가능 품목을 생성하십시오."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__create_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_uid
msgid "Created by"
msgstr "작성자"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__create_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__create_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__create_date
#: model:ir.model.fields,field_description:stock.field_product_removal__create_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__create_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__create_date
#: model:ir.model.fields,field_description:stock.field_stock_location__create_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move__create_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__create_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__create_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__create_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__create_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__create_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__create_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_route__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__create_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__create_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__create_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__create_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__create_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__create_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__create_date
msgid "Created on"
msgstr "작성일자"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"Creating a new warehouse will automatically activate the Storage Locations "
"setting"
msgstr "새로운 창고를 생성하면 보관 위치 설정이 자동으로 활성화됩니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Creation Date"
msgstr "작성일자"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date
msgid "Creation Date, usually the time of the order"
msgstr "작성일, 일반적으로 주문 시간"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Creation date"
msgstr "작성일"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__date
msgid ""
"Creation date of this move line until updated due to: quantity being "
"increased, 'picked' status has updated, or move line is done."
msgstr "이 이동 항목의 생성 날짜는 수량 증가, '피킹됨' 상태 업데이트 또는 이동 항목이 완료된 경우로 인해 업데이트됩니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross Dock"
msgstr "크로스도킹"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__xdock_type_id
msgid "Cross Dock Type"
msgstr "크로스도킹 유형"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Cross-Dock"
msgstr "크로스도킹(입고 즉시 출고)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__crossdock_route_id
msgid "Crossdock Route"
msgstr "크로스도킹(입고 즉시 출고) 경로"

#. module: stock
#: model:ir.actions.act_window,name:stock.location_open_quants
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Current Stock"
msgstr "현재 재고"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__qty_available
msgid ""
"Current quantity of products.\n"
"In a context with a single Stock Location, this includes goods stored at this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"stored in the Stock Location of the Warehouse of this Shop, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"품목의 현재 수량입니다.\n"
"재고 공간이 하나인 경우에는 이 공간 또는 하위 공간에 저장된 상품을 포함합니다.\n"
"창고가 하나인 경우에는 이 창고의 재고 공간 또는 그 하위 재고 공간에 저장된 품목을 포함합니다.\n"
"이 상점의 창고 또는 그 하위의 재고 공간에 보관합니다.\n"
"그렇지 않으면 '내부' 유형의 모든 저장 공간에 저장된 품목이 포함됩니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__custom
#: model:ir.model.fields.selection,name:stock.selection__stock_orderpoint_snooze__predefined_date__custom
msgid "Custom"
msgstr "사용자 정의"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer"
msgstr "고객"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__sale_delay
#: model:ir.model.fields,field_description:stock.field_product_template__sale_delay
msgid "Customer Lead Time"
msgstr "고객 리드타임"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_customer
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__customer
msgid "Customer Location"
msgstr "고객 위치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Customer Locations"
msgstr "고객 위치"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot_report
msgid "Customer Lot Report"
msgstr "고객 로트 보고서"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lot_report
msgid "Customer lots"
msgstr "고객 로트"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Customizable Desk"
msgstr "맞춤형 책상"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Cyclic Counting"
msgstr "순환 계산"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_DATE"
msgstr "DEMO_DATE"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_ORIGIN_DISPLAY_NAME"
msgstr "DEMO_ORIGIN_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PARTNER_NAME"
msgstr "DEMO_PARTNER_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_PRODUCT_DISPLAY_NAME"
msgstr "DEMO_PRODUCT_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_QUANTITY"
msgstr "DEMO_QUANTITY"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_SOURCE_DISPLAY_NAME"
msgstr "DEMO_SOURCE_DISPLAY_NAME"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "DEMO_UOM"
msgstr "DEMO_UOM"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr "DHL 익스프레스 배송"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__date_done
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Date"
msgstr "날짜"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__search_date_category
msgid "Date Category"
msgstr "날짜 카테고리"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Processing"
msgstr "날짜 처리"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Date Scheduled"
msgstr "계획일자"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__date_planned
msgid "Date at which the replenishment should take place."
msgstr "보충이 완료되어야 하는 날짜입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__date_done
msgid "Date at which the transfer has been processed or cancelled."
msgstr "이송이 처리되거나 취소된 날짜."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__next_inventory_date
msgid "Date for next planned inventory based on cyclic schedule."
msgstr "주기적인 일정을 기준으로 한 다음 재고 계획 일자"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_done
msgid "Date of Transfer"
msgstr "이송 날짜"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__last_inventory_date
msgid "Date of the last inventory at this location."
msgstr "이 위치에서의 최근 재고 일자입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__reservation_date
msgid "Date to Reserve"
msgstr "예약 날짜"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Day and month that annual inventory counts should occur."
msgstr "연간 재고 조사를 실시하는 날짜를 정의합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,field_description:stock.field_res_config_settings__annual_inventory_day
msgid "Day of the month"
msgstr "해당 월의 날짜"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__annual_inventory_day
#: model:ir.model.fields,help:stock.field_res_config_settings__annual_inventory_day
msgid ""
"Day of the month when the annual inventory should occur. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"연간 재고 조사를 실시해야 하는 날짜입니다. 0이나 음수인 경우에는 대신 해당 월의 1일로 선택됩니다.\n"
"숫자가 해당 월의 말일보다 큰 경우에는, 해당 월의 말일이 대신 선택됩니다."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Day(s)"
msgstr "일"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before
msgid "Days"
msgstr "일"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Days To Order"
msgstr "주문까지의 날짜"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_days_before_priority
msgid "Days when starred"
msgstr "별표 표시된 날짜"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__date_deadline
msgid "Deadline"
msgstr "마감 시한"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deadline exceed or/and by the scheduled"
msgstr "마감일 초과 또는/및 예정일"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Deadline updated due to delay on %s"
msgstr "%s 지연으로 인해 업데이트된 기한"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__12
msgid "December"
msgstr "12월"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Barcode Name"
msgstr "바코드 이름 기본값"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Name"
msgstr "이름 기본값"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default OBTRETU Barcode"
msgstr "OBTRETU 바코드 기본값"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Default Return Name"
msgstr "반품명 기본값"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__reception_steps
msgid "Default incoming route to follow"
msgstr "팔로우할 기본 입고 경로"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__delivery_steps
msgid "Default outgoing route to follow"
msgstr "팔로우 할 기본 출고 경로"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,help:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_uom
msgid "Default unit of measure used for all stock operations."
msgstr "모든 재고 운용에 사용되는 기본 단위입니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__procure_method__make_to_stock
msgid "Default: Take From Stock"
msgstr "기본값 : 재고에서 가져 오기"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__route_ids
msgid "Defaults routes through the warehouse"
msgstr "창고를 통한 기본 경로"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid ""
"Define a minimum stock rule so that Odoo automatically creates requests for "
"quotations or confirmed manufacturing orders to resupply your stock."
msgstr "최소 재고 수량을 설정하여 Odoo가 자동으로 견적을 요청하거나 제조주문서를 승인하여 재고를 재보충할 수 있도록 합니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_warehouse_form
msgid "Define a new warehouse"
msgstr "새 창고 정의"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Define your locations to reflect your warehouse structure and\n"
"            organization. Odoo is able to manage physical locations\n"
"            (warehouses, shelves, bin, etc), partner locations (customers,\n"
"            vendors) and virtual locations which are the counterpart of\n"
"            the stock operations like the manufacturing orders\n"
"            consumptions, inventories, etc."
msgstr ""
"창고 구조 및 조직을 반영하도록 공간을\n"
"            정의하십시오. Odoo는 생산계획 소요,\n"
"            재고 자산 등과 같은 재고 작업의,\n"
"            상대적인 물리적 공간(위치)(창고, 선반, 박스 등) 파트너 창고(고객, 공급업체) 및 가상 공간을 관리 할 수 있습니다.\n"
"            파트너 창고(고객, 공급업체) 및 가상 공간을\n"
"            관리 할 수 있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__removal_strategy_id
msgid ""
"Defines the default method used for suggesting the exact location (shelf) where to take the products from, which lot etc. for this location. This method can be enforced at the product category level, and a fallback is made on the parent locations if none is set here.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closet location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting)."
msgstr ""
"품목을 가져올 정확한 위치 (선반)나 어떤 로트로 가져올지 등 기본 방법을 정의합니다. 이 방법은 품목 카테고리 수준에서 적용될 수 있으며 설정한 내용이 없는 경우에는 상위 위치에서 대안책으로 설정된 내용이 실행됩니다.\n"
"\n"
"FIFO: 먼저 입고된 품목이나 로트가 먼저 출고됩니다.\n"
"LIFO: 나중에 입고된 품목이나 로트가 먼저 출고됩니다.\n"
"Closet: 대상 장소에 가장 가까이에 위치한 품목이나 로트가 먼저 출고됩니다.\n"
"FEFO: 유효기간에 가장 근접한 품목이나 로트가 먼저 출고됩니다. (\"유효기간 만료\" 설정에 따라서 이 방법은 달라질 수 있습니다)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__delay_alert_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__delay_alert_date
msgid "Delay Alert Date"
msgstr "지연 알림일"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Delay on %s"
msgstr "%s 지연"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__ship_only
msgid "Deliver (1 step)"
msgstr "배송 (1단계)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 1 step (ship)"
msgstr "1단계 배송 (선적)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 2 steps (pick + ship)"
msgstr "2단계 배송 (선별 + 선적)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Deliver in 3 steps (pick + pack + ship)"
msgstr "3단계 배송 (선별 + 포장 + 선적)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Delivered"
msgstr "배송완료"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Delivered Qty"
msgstr "배송된 수량"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_outgoing
#: model:ir.ui.menu,name:stock.out_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Deliveries"
msgstr "배송"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__outgoing
#: model:ir.ui.menu,name:stock.menu_delivery
msgid "Delivery"
msgstr "배송"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Delivery Address"
msgstr "배송 주소"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__delivery_date
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery Date"
msgstr "배송일"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr "배송 방법"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.picking.type,name:stock.picking_type_out
msgid "Delivery Orders"
msgstr "출고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_route_id
msgid "Delivery Route"
msgstr "배송 경로"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_delivery
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Delivery Slip"
msgstr "거래명세서"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__move_type
msgid "Delivery Type"
msgstr "배송 유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Delivery date"
msgstr "배송일"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__sale_delay
#: model:ir.model.fields,help:stock.field_product_template__sale_delay
msgid ""
"Delivery lead time, in days. It's the number of days, promised to the "
"customer, between the confirmation of the sales order and the delivery."
msgstr "일 단위의 배송 리드타임입니다. 판매 주문 확인에서 배송까지 고객에게 약속된 날짜수입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_count
msgid "Delivery order count"
msgstr "배송주문서 수"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Delivery orders of %s"
msgstr "%s의 배송주문서"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom_qty
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Demand"
msgstr "수요"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "Demo Address and Name"
msgstr "데모용 주소 및 이름"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Demo Display Name"
msgstr "데모용 표시명"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Lot/SN"
msgstr "데모 로트/일련번호"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_lot_label
msgid "Demo Product"
msgstr "데모용 품목"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_packaging__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product in this packaging: whether it will be bought, manufactured, "
"replenished on order, etc."
msgstr ""
"설치되어 있는 모듈에 따라, 이 패키지에서 품목 경로를 설정할 수 있습니다. 구매를 거친 것인지, 제조한 것인지, 주문에 따라 보충된 "
"것인지 등을 설정합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__route_ids
#: model:ir.model.fields,help:stock.field_product_template__route_ids
msgid ""
"Depending on the modules installed, this will allow you to define the route "
"of the product: whether it will be bought, manufactured, replenished on "
"order, etc."
msgstr "설치된 모듈에 따라 품목의 경로, 즉 구매, 제조, 주문시 보충 여부 등을 정의할 수 있습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__note
#: model:ir.model.fields,field_description:stock.field_stock_move__name
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Description"
msgstr "설명"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Delivery Orders"
msgstr "배송 주문에 대한 설명"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Internal Transfers"
msgstr "내부 이송에 대한 설명"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Description for Receipts"
msgstr "입고 내역"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__description_picking
msgid "Description of Picking"
msgstr "선별 설명"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingout
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingout
msgid "Description on Delivery Orders"
msgstr "배송 주문시 설명"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_picking
#: model:ir.model.fields,field_description:stock.field_product_template__description_picking
msgid "Description on Picking"
msgstr "선별시 설명"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__description_pickingin
#: model:ir.model.fields,field_description:stock.field_product_template__description_pickingin
msgid "Description on Receptions"
msgstr "인수시 설명"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Description on transfer"
msgstr "이전에 관한 설명"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__description_picking
msgid "Description picking"
msgstr "피킹 설명"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_location_id
msgid "Dest Location"
msgstr "도착지"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id
msgid "Dest Package"
msgstr "도착 패키지"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__dest_package_id_domain
msgid "Dest Package Id Domain"
msgstr "도착 패키지 아이디 도메인"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__partner_id
msgid "Destination Address "
msgstr "목적지 주소 "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Destination Location"
msgstr "목적지 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_usage
msgid "Destination Location Type"
msgstr "입고할 위치 유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Location:"
msgstr "목적지 위치 :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_dest_ids
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Destination Moves"
msgstr "이동 목적지"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__result_package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Destination Package"
msgstr "목적지 패키지"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Destination Package:"
msgstr "목적지 패키지:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__location_dest_id
msgid "Destination location"
msgstr "목적지 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_dest_from_rule
msgid "Destination location origin from rule"
msgstr "규칙에서 이동 위치 출처"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__route_ids
msgid "Destination route"
msgstr "목적지 경로"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Detailed Operations"
msgstr "세부 작업"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_details_visible
msgid "Details Visible"
msgstr "세부 정보 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_diff_quantity
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Difference"
msgstr "차액"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_edit_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_request_count_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Discard"
msgstr "취소"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Discard and manually resolve the conflict"
msgstr "삭제 후 수동으로 해결"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_fleet
msgid "Dispatch Management System"
msgstr "배차 관리 시스템"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_assign_serial
msgid "Display Assign Serial"
msgstr "배정 일련번호 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_complete
msgid "Display Complete"
msgstr "완료"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__display_import_lot
msgid "Display Import Lot"
msgstr "로트 가져오기 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_lot_on_delivery_slip
msgid "Display Lots & Serial Numbers on Delivery Slips"
msgstr "납품 전표에 로트 번호 및 일련번호 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__display_name
#: model:ir.model.fields,field_description:stock.field_picking_label_type__display_name
#: model:ir.model.fields,field_description:stock.field_procurement_group__display_name
#: model:ir.model.fields,field_description:stock.field_product_removal__display_name
#: model:ir.model.fields,field_description:stock.field_product_replenish__display_name
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__display_name
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__display_name
#: model:ir.model.fields,field_description:stock.field_stock_location__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot__display_name
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move__display_name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_level__display_name
#: model:ir.model.fields,field_description:stock.field_stock_package_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__display_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__display_name
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__display_name
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__display_name
#: model:ir.model.fields,field_description:stock.field_stock_request_count__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__display_name
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_route__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rule__display_name
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__display_name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__display_name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__display_name
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__display_name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__display_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__display_name
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Display Name"
msgstr "표시명"

#. module: stock
#: model:res.groups,name:stock.group_lot_on_delivery_slip
msgid "Display Serial & Lot Number in Delivery Slips"
msgstr "납품 전표에 일련번호 및 로트 번호 표시"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.package_level_tree_view_picking
msgid "Display package content"
msgstr "패키지 내용 표시"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__disposable
msgid "Disposable Box"
msgstr "일회용 상자"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "Do you confirm you want to scrap"
msgstr "폐기를 승인하시겠습니까?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Documentation"
msgstr "문서"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_done
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__done
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__done
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Done"
msgstr "완료"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
msgid "Done By"
msgstr "작업자"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_quantity
msgid "Done Packaging Quantity"
msgstr "완료 패키지 수량"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_scrap__state__draft
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft"
msgstr "미결"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Draft Moves"
msgstr "임시 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_dropshipping
msgid "Dropshipping"
msgstr "직배송"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Due to receipts scheduled in the future, you might end up with "
"excessive stock . Check the Forecasted Report  before reordering"
msgstr "향후 입고가 예정되어 있으므로 재고 수량이 초과될 수 있습니다. 재주문 전 예측 보고서를 확인해 주세요."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "Duplicated SN Warning"
msgstr "일련번호 중복 경고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__sn_duplicated
msgid "Duplicated Serial Number"
msgstr "일련번호 중복 경고"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__dymo
msgid "Dymo"
msgstr "Dymo 라벨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr "Easypost 배송"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Edit Product"
msgstr "품목 수정"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Editing quantities in an Inventory Adjustment location is forbidden,those "
"locations are used as counterpart when correcting the quantities."
msgstr "재고 조정 위치에서 수량을 편집하는 것은 금지되어 있으며, 수량을 수정할 때 해당 위치가 대안으로 사용됩니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Effective Date"
msgstr "유효 날짜"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Email Confirmation"
msgstr "이메일 확인"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_move_email_validation
#: model:ir.model.fields,field_description:stock.field_res_config_settings__stock_move_email_validation
msgid "Email Confirmation picking"
msgstr "이메일 피킹 확인"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email Template confirmation picking"
msgstr "이메일 피킹 확인 서식"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_company__stock_mail_confirmation_template_id
msgid "Email sent to the customer once the order is done."
msgstr "주문이 완료되면 고객에게 이메일이 발송됩니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Empty Locations"
msgstr "비어 있는 위치"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid ""
"Enjoy a quick-paced experience with the Odoo barcode app. It is blazing fast"
" and works even without a stable internet connection. It supports all flows:"
" inventory adjustments, batch picking, moving lots or pallets, low inventory"
" checks, etc. Go to the \"Apps\" menu to activate the barcode interface."
msgstr ""
"Odoo 바코드 앱으로 업무를 빠르게 처리해 보세요. 인터넷 연결이 안정적이지 않더라도 문제 없이 작동합니다. 재고 조정, 일괄 피킹, "
"로트 및 팔레트 이동, 재고 부족 알림 등 업무 전반에서 걸쳐 활용할 수 있습니다. 앱 메뉴로 이동하여 바코드 인터페이스를 활성화하세요."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__tracking
#: model:ir.model.fields,help:stock.field_product_template__tracking
#: model:ir.model.fields,help:stock.field_stock_move__has_tracking
#: model:ir.model.fields,help:stock.field_stock_move_line__tracking
#: model:ir.model.fields,help:stock.field_stock_quant__tracking
#: model:ir.model.fields,help:stock.field_stock_scrap__tracking
#: model:ir.model.fields,help:stock.field_stock_track_line__tracking
msgid "Ensure the traceability of a storable product in your warehouse."
msgstr "창고에 저장 가능한 품목의 추적성을 확인하십시오."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_location_form
msgid ""
"Every stock operation in Odoo moves the products from one\n"
"            location to another one.  For instance, if you receive products\n"
"            from a vendor, Odoo will move products from the Vendor\n"
"            location to the Stock location. Each report can be performed on\n"
"            physical, partner or virtual locations."
msgstr ""
"Odoo의 모든 재고 작업은 품목을 한 공간에서\n"
"            다른 공간으로 옮깁니다.  예를 들어, 공급 업체로부터\n"
"            품목을 받은 경우, Odoo는 품목을 공급 업체 창고에서\n"
"            재고 공간로 옮깁니다. 각 보고서는 실제,\n"
"            파트너, 또는 가상 공간에서 수행 할 수 있습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s) occurred on the picking"
msgstr "피킹에서 예외가 발생했습니다"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Exception(s):"
msgstr "예외 :"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Exp"
msgstr "예상"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Exp %s"
msgstr "예상 %s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__expected
msgid "Expected"
msgstr "예상됨"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Expected Delivery:"
msgstr "예상 배송일:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_product_expiry
msgid "Expiration Dates"
msgstr "유통기한"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "External note..."
msgstr "외부 노트..."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_removal__method
msgid "FIFO, LIFO..."
msgstr "FIFO, LIFO..."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_favorite
msgid "Favorite"
msgstr "즐겨찾기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__favorite_user_ids
msgid "Favorite User"
msgstr "즐겨찾는 사용자"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Favorites"
msgstr "즐겨찾기"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__2
msgid "February"
msgstr "2월"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr "FedEx 배송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__filtered_location
msgid "Filtered Location"
msgstr "필터링된 위치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Filters"
msgstr "필터"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_final_id
msgid "Final Location"
msgstr "최종 위치"

#. module: stock
#: model:product.removal,name:stock.removal_fifo
msgid "First In First Out (FIFO)"
msgstr "선입선출법 (FIFO)"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Lot Number"
msgstr "첫 번째 로트 번호"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial
msgid "First SN/Lot"
msgstr "첫 번째 일련번호/로트"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "First Serial Number"
msgstr "첫 번째 일련번호"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__fixed
msgid "Fixed"
msgstr "고정"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_id
msgid "Fixed Procurement Group"
msgstr "고정 조달 그룹"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_follower_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_partner_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_type_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예: fa-tasks"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__removal_strategy_id
msgid "Force Removal Strategy"
msgstr "품목 출고 방식"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_forecast
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Forecast"
msgstr "예측"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_availability
msgid "Forecast Availability"
msgstr "예상 수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Forecast Description"
msgstr "예상 설명"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Forecast Report"
msgstr "예측 보고서"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__virtual_available
msgid ""
"Forecast quantity (computed as Quantity On Hand - Outgoing + Incoming)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"예상수량(보관중재고 - 출고 + 입고)\n"
"단일 재고 공간의 경우, 이 장소 또는 하위 공간에 보관된 품목을 포함합니다.\n"
"단일 창고의 경우, 창고의 재고 공간 또는 하위 공간의 품목을 포함합니다.\n"
"기타의 경우, '내부 공간'형태의 모든 재고 공간을 포함합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__free_qty
msgid ""
"Forecast quantity (computed as Quantity On Hand - reserved quantity)\n"
"In a context with a single Stock Location, this includes goods stored in this location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods stored in the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods stored in any Stock Location with 'internal' type."
msgstr ""
"예측 수량 (보유 수량-예약 수량으로 계산됨)\n"
"단일 재고 위치와 관련하여 여기에는 이 위치 또는 그 하위에 저장된 품목이 포함됩니다.\n"
"단일 창고와 관련하여 여기에는 이 창고 또는 그 하위의 재고 위치에 저장된 품목이 포함됩니다.\n"
"그렇지 않으면 여기에는 '내부' 유형의 재고 위치에 저장된 품목이 포함됩니다."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
msgid "Forecasted"
msgstr "예측 수량"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date"
msgstr "예상일"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Forecasted Date + Visibility Days"
msgstr "예상 날짜 + 가시성 일수"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__out
msgid "Forecasted Deliveries"
msgstr "예측 배송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__forecast_expected_date
msgid "Forecasted Expected date"
msgstr "예측되는 예상일"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted Inventory"
msgstr "예측 재고량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.model.fields,field_description:stock.field_product_product__virtual_available
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecasted_quantity
#: model:ir.model.fields,field_description:stock.field_product_template__virtual_available
#: model:ir.model.fields,field_description:stock.field_stock_move__availability
msgid "Forecasted Quantity"
msgstr "예상 수량"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__in
msgid "Forecasted Receipts"
msgstr "예상 입고"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
#: model:ir.actions.client,name:stock.stock_forecasted_product_product_action
#: model:ir.actions.client,name:stock.stock_forecasted_product_template_action
msgid "Forecasted Report"
msgstr "예측 보고서"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__report_stock_quantity__state__forecast
msgid "Forecasted Stock"
msgstr "예상 재고량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__forecast_weight
msgid "Forecasted Weight"
msgstr "예상 무게"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Forecasted with Pending"
msgstr "보류 중인 예측 수량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__print_format
#: model:ir.model.fields,field_description:stock.field_product_label_layout__print_format
msgid "Format"
msgstr "형식"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__free_qty
msgid "Free Qty"
msgstr "무료 수량"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock"
msgstr "여유 재고"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Free Stock in Transit"
msgstr "운송 중인 일반 재고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__free_qty
msgid "Free To Use Quantity "
msgstr "수량 사용 가능"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Free to Use"
msgstr "사용 가능"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "From"
msgstr "기존 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__owner_id
msgid "From Owner"
msgstr "소유자에서"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__complete_name
msgid "Full Location Name"
msgstr "전체 위치 이름"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Future Activities"
msgstr "향후 활동"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Deliveries"
msgstr "향후 배송"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future P&L"
msgstr "향후 P&L"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Productions"
msgstr "생산 예정"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Future Receipts"
msgstr "향후 수령"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "General"
msgstr "일반"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate"
msgstr "생성"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Lot numbers"
msgstr "로트 번호 생성"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Generate Serial numbers"
msgstr "일련번호 생성"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Generate Serials/Lots"
msgstr "일련번호/로트 생성"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__print_label
msgid "Generate Shipping Labels"
msgstr "배송 라벨 생성"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get a full traceability from vendors to customers"
msgstr "공급업체에서 고객에 이르기까지 완벽한 추적 기능을 제공합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Get informative or blocking warnings on partners"
msgstr "협력사와 관련된 정보 또는 경고 내용을 차단합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_putaway_rule__sequence
msgid ""
"Give to the more specialized category, a higher priority to have them in top"
" of the list."
msgstr "보다 전문화 된 범주에 우선 순위를 부여하여 목록 맨 위에 올리십시오."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__sequence
msgid "Gives the sequence of this line when displaying the warehouses."
msgstr "창고를 표시할 때 이 내역의 순서를 제공합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Go to Warehouses"
msgstr "창고로 이동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Group By"
msgstr "그룹별"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Group by..."
msgstr "그룹별..."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "HTML reports cannot be auto-printed, skipping report: %s"
msgstr "HTML 보고서를 자동 인쇄할 수 없습니다. 보고서를 건너뜁니다: %s"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Hardware"
msgstr "하드웨어"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__has_message
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_message
#: model:ir.model.fields,field_description:stock.field_stock_scrap__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_exist
msgid "Has Pack Operations"
msgstr "포장 작업 있음"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_packages
msgid "Has Packages"
msgstr "패키지"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__has_return
msgid "Has Return"
msgstr "반품 여부"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_scrap_move
msgid "Has Scrap Moves"
msgstr "폐기 이동 있음"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_tracking
msgid "Has Tracking"
msgstr "추적이 가능함"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_has_variants
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_has_variants
msgid "Has variants"
msgstr "세부선택항목 있음"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Having Category"
msgstr "카테고리가 있습니다"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__height
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Height"
msgstr "높이"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posz
msgid "Height (Z)"
msgstr "높이(Z)"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_height
msgid "Height must be positive"
msgstr "높이는 양수여야 합니다"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Hidden until next scheduler."
msgstr "다음 스케줄까지 숨깁니다"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__hide_reservation_method
msgid "Hide Reservation Method"
msgstr "예약 방법 숨기기"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "History"
msgstr "기록"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
msgid "Horizon"
msgstr "지평선"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_method
msgid "How products in transfers of this operation type should be reserved."
msgstr "이 작업 유형에서 운송 중에 있는 품목을 예약하는 방법입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__id
#: model:ir.model.fields,field_description:stock.field_picking_label_type__id
#: model:ir.model.fields,field_description:stock.field_procurement_group__id
#: model:ir.model.fields,field_description:stock.field_product_removal__id
#: model:ir.model.fields,field_description:stock.field_product_replenish__id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__id
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__id
#: model:ir.model.fields,field_description:stock.field_stock_location__id
#: model:ir.model.fields,field_description:stock.field_stock_lot__id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__id
#: model:ir.model.fields,field_description:stock.field_stock_move__id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__id
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__id
#: model:ir.model.fields,field_description:stock.field_stock_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_quant__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__id
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__id
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__id
#: model:ir.model.fields,field_description:stock.field_stock_request_count__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__id
#: model:ir.model.fields,field_description:stock.field_stock_route__id
#: model:ir.model.fields,field_description:stock.field_stock_rule__id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__id
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__id
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__id
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__id
msgid "ID"
msgstr "ID"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_icon
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"If a payment is still outstanding more than sixty (60) days after the due "
"payment date, My Company (Chicago) reserves the right to call on the "
"services of a debt recovery company. All legal expenses will be payable by "
"the client."
msgstr ""
"지급기일로부터 미결제 상태로 육십(60)일이 경과한 경우 당사 (시카고)는 해당 건에 대해 추심전문업체에 요청할 수 있는 권리를 "
"가집니다. 관련된 모든 법적인 비용은 고객이 부담합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"If a separator is defined, a QR code containing all serial numbers contained"
" in package will be generated, using the defined character(s) to separate "
"each numbers"
msgstr ""
"구분 기호가 정의된 경우, 각 번호를 구분하기 위해 정의된 문자를 사용하여 패키지에 포함된 모든 일련 번호가 포함된 QR 코드가 "
"생성됩니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__same
msgid "If all products are same"
msgstr "모든 품목이 동일한 경우"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__propagate_cancel
msgid "If checked, when this move is cancelled, cancel the linked move too"
msgstr "선택하면, 이 이동이 취소되면 연결된 이동도 취소됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__result_package_id
msgid "If set, the operations are packed into this package"
msgstr "설정된 경우 작업이 이 꾸러미 상품을 채웁니다"

#. module: stock
#: model:ir.model.fields,help:stock.field_lot_label_layout__label_quantity
msgid ""
"If the UoM of a lot is not 'units', the lot will be considered as a unit and"
" only one label will be printed for this lot."
msgstr "로트의 기본 단위가 '단위'가 아닌 경우 로트 단위로 취급되며 해당 로트에는 하나의 라벨만 인쇄됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"orderpoint without removing it."
msgstr "활성 필드를 False로 설정하면 주문 orderpoint를 제거하지 않고 숨길 수 있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__active
msgid ""
"If the active field is set to False, it will allow you to hide the route "
"without removing it."
msgstr "사용중인 필드를 아니오로 설정하면, 제거하지 않고 경로를 숨길 수 있습니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_storage_category__allow_new_product__empty
msgid "If the location is empty"
msgstr "위치 정보가 비어 있는 경우"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__sn_duplicated
msgid "If the same SN is in another Quant"
msgstr "동일한 일련번호가 다른 퀀트에 있는 경우"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_delivery_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the delivery slip "
"of a picking when it is validated."
msgstr "확인란을 선택하면, 픽업이 승인될 경우 Odoo에서 자동으로 배송 전표를 인쇄합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_lot_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the lot/SN labels "
"of a picking when it is validated."
msgstr "확인란을 선택하면, 픽업이 승인될 경우 Odoo에서 자동으로 로트 또는 일련번호 라벨을 인쇄합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_package_label
msgid ""
"If this checkbox is ticked, Odoo will automatically print the package label "
"when \"Put in Pack\" button is used."
msgstr "확인란을 선택하면, '포장에 넣기' 버튼을 사용할 경우 Odoo에서 자동으로 패키지 라벨을 인쇄합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_packages
msgid ""
"If this checkbox is ticked, Odoo will automatically print the packages and "
"their contents of a picking when it is validated."
msgstr "확인란을 선택하면, 픽업이 승인될 경우 Odoo에서 자동으로 패키지 및 해당 내용물을 인쇄합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_product_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the product labels"
" of a picking when it is validated."
msgstr "확인란을 선택하면, 픽업이 승인될 경우 Odoo에서 자동으로 품목 라벨을 인쇄합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report_labels
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report labels of a picking when it is validated."
msgstr "확인란을 선택하면, 픽업이 승인될 경우 Odoo에서 자동으로 입고 보고서 라벨을 인쇄합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically print the reception "
"report of a picking when it is validated and has assigned moves."
msgstr "확인란을 선택하면, 작업 확인 및 배정이 완료되면 Odoo에서 자동으로 입고 픽업 입고 보고서를 인쇄합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_print_return_slip
msgid ""
"If this checkbox is ticked, Odoo will automatically print the return slip of"
" a picking when it is validated."
msgstr "확인란을 선택하면, 픽업이 승인될 경우 Odoo에서 자동으로 반품 전표를 인쇄합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__auto_show_reception_report
msgid ""
"If this checkbox is ticked, Odoo will automatically show the reception "
"report (if there are moves to allocate to) when validating."
msgstr "이 체크박스를 선택하면, Odoo에서 유효성 검사 시 수신된 보고서 (할당된 이동 내역이 있는 경우)를 자동으로 표시합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking__show_operations
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_operations
msgid ""
"If this checkbox is ticked, the pickings lines will represent detailed stock"
" operations. If not, the picking lines will represent an aggregate of "
"detailed stock operations."
msgstr ""
"이 확인란을 선택하면, 선별 내역이 상세한 재고 작업을 나타냅니다. 그렇지 않은 경우, 선별 내역은 상세한 재고 작업을 나타냅니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_create_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_create_lots
msgid ""
"If this is checked only, it will suppose you want to create new Lots/Serial "
"Numbers, so you can provide them in a text field. "
msgstr "이 항목을 선택하면 새 LOT/일련번호를 생성하려는 것으로 간주하여 텍스트 필드에 입력할 수 있게됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,help:stock.field_stock_picking_type__use_existing_lots
msgid ""
"If this is checked, you will be able to choose the Lots/Serial Numbers. You "
"can also decide to not put lots in this operation type.  This means it will "
"create stock with no lot or not put a restriction on the lot taken. "
msgstr ""
"이 항목을 선택하면 LOT/일련번호를 선택할 수 있으며, 또한 이 작업 유형에 LOT를 넣지 않기로 결정할 수도 있습니다. 즉, LOT가"
" 없는 재고를 만들거나 가져간 LOT에 제한을 두지 않습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__return_id
msgid ""
"If this picking was created as a return of another picking, this field links"
" to the original picking."
msgstr "이 피킹이 다른 피킹의 반환으로 생성된 경우 이 필드는 기존의 피킹과 연결됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__backorder_id
msgid ""
"If this shipment was split, then this field links to the shipment which "
"contains the already processed part."
msgstr "이 발송물이 분리된 경우 이 필드는 이미 처리된 부품이 들어있는 발송물로 연결됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,help:stock.field_stock_picking_type__show_entire_packs
msgid "If ticked, you will be able to select entire packages to move"
msgstr "체크 시 이동시킬 전체 패키지 품목을 선택할 수 있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__active
msgid "If unchecked, it will allow you to hide the rule without removing it."
msgstr "선택을 해제하면 제거하지 않고 품목을 숨길 수 있습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__backorder_confirmation_id
msgid "Immediate Transfer"
msgstr "즉시 이동"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Import"
msgstr "가져오기"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Lots"
msgstr "로트 번호 가져오기"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/generate_serial.js:0
msgid "Import Serials"
msgstr "일련번호 가져오기"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Import Serials/Lots"
msgstr "일련번호 및 로트번호 가져오기"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Import Template for Inventory Adjustments"
msgstr "재고 조정용 서식 가져오기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "In Stock"
msgstr "재고 있음"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__in_type_id
msgid "In Type"
msgstr "유형 있음"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date_deadline
#: model:ir.model.fields,help:stock.field_stock_picking__date_deadline
msgid ""
"In case of outgoing flow, validate the transfer before this date to allow to deliver at promised date to the customer.\n"
"        In case of incoming flow, validate the transfer before this date in order to have these products in stock at the date promised by the supplier"
msgstr ""
"출고 작업의 경우 고객에게 약속한 날짜에 배송하려면 이 날짜 이전에 전송에 대한 승인을 확인해야 합니다.\n"
"        입고 작업의 경우 이 날짜 이전에 전송에 대한 승인을 확인하여 공급업체가 약속한 날짜까지 해당 제품의 재고가 있는지 확인합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "In internal locations"
msgstr "내부 위치"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"In order for it to be admissible, My Company (Chicago) must be notified of "
"any claim by means of a letter sent by recorded delivery to its registered "
"office within 8 days of the delivery of the goods or the provision of the "
"services."
msgstr ""
"승인을 받기 위해서는 상품 배송 또는 서비스 제공 후 8일 이내에 등록된 사무실로 등기 우편을 통해 서신으로 My Company "
"(Chicago) 사에 청구할 내용을 통지해야만 합니다."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__incoming_qty
#: model:ir.model.fields,field_description:stock.field_product_template__incoming_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Incoming"
msgstr "입고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__in_date
msgid "Incoming Date"
msgstr "입고 날짜"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Incoming Draft Transfer"
msgstr "입고 재고 이동 초안"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__incoming_move_line_ids
msgid "Incoming Move Line"
msgstr "입고 이동 내역"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_steps
msgid "Incoming Shipments"
msgstr "입고 배송"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Incorrect type of action submitted as a report, skipping action"
msgstr "보고서로 제출된 작업의 유형이 잘못되어 이 작업을 건너뛰는 경우"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_diff_quantity
msgid ""
"Indicates the gap between the product's theoretical quantity and its counted"
" quantity."
msgstr "품목의 서류상의 수량과 실측 수량 간의 차이를 나타냅니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
msgid "Initial Demand"
msgstr "초기 수요"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Input"
msgstr "입력"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_input_stock_loc_id
msgid "Input Location"
msgstr "공간(위치) 입력"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Install"
msgstr "설치하기"

#. module: stock
#: model:ir.actions.server,name:stock.action_install_barcode
msgid "Install Barcode"
msgstr "바코드 설치"

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_company.py:0
msgid "Inter-warehouse transit"
msgstr "창고간 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_dest_id
msgid "Intermediate Location"
msgstr "중간 위치"

#. module: stock
#: model:ir.ui.menu,name:stock.int_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Internal"
msgstr "내부 이동"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__internal
msgid "Internal Location"
msgstr "내부 위치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Internal Locations"
msgstr "내부 공간(위치)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__ref
msgid "Internal Reference"
msgstr "품목 코드"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__internal
msgid "Internal Transfer"
msgstr "내부 전송용"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_internal
#: model:stock.picking.type,name:stock.picking_type_internal
msgid "Internal Transfers"
msgstr "내부 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_company__internal_transit_location_id
msgid "Internal Transit Location"
msgstr "내부 이송 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__int_type_id
msgid "Internal Type"
msgstr "내부 유형"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__child_internal_location_ids
msgid "Internal locations among descendants"
msgstr "하위 항목 간의 내부 위치"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "Internal locations having stock can't be converted"
msgstr "재고가 있는 내부 위치는 전환할 수 없습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__ref
msgid ""
"Internal reference number in case it differs from the manufacturer's "
"lot/serial number"
msgstr "제조자의 LOT/일련번호와 다른 경우 내부 참조 번호를 사용합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Invalid domain left operand %s"
msgstr "잘못된 도메인 왼쪽 피연산자 %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain operator %s"
msgstr "잘못된 도메인 피연산자 %s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/models/stock_lot.py:0
msgid "Invalid domain right operand '%s'. It must be of type Integer/Float"
msgstr "도메인의 우측 피연산자 값 '%s' 항목이 잘못되었습니다. 값은 반드시 정수 또는 부동 소수점 형식이어야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"Invalid rule's configuration, the following rule causes an endless loop: %s"
msgstr "잘못된 규칙 구성입니다. 다음 규칙으로 인해 무한 반복되고 있습니다: %s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_auto_apply
msgid "Inventoried Quantity"
msgstr "재고 수량"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_inventory_tree
#: model:ir.actions.server,name:stock.action_view_quants
#: model:ir.model.fields,field_description:stock.field_stock_move__is_inventory
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_inventory
#: model:ir.ui.menu,name:stock.menu_stock_root
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_pivot
msgid "Inventory"
msgstr "재고 관리"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_inventory_adjustement_name
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory Adjustment"
msgstr "재고 조정"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.model,name:stock.model_stock_inventory_adjustment_name
msgid "Inventory Adjustment Reference / Reason"
msgstr "재고 조정 참조 / 이유"

#. module: stock
#: model:ir.model,name:stock.model_stock_inventory_warning
msgid "Inventory Adjustment Warning"
msgstr "재고 조정 경고"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Inventory Adjustments"
msgstr "재고 조정"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Inventory Count Sheet"
msgstr "재고 실사 시트"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_request_count__inventory_date
msgid "Inventory Date"
msgstr "재고 날짜"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__cyclic_inventory_frequency
#: model:ir.model.fields,field_description:stock.field_stock_quant__cyclic_inventory_frequency
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Inventory Frequency"
msgstr "재고 주기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_inventory
msgid "Inventory Location"
msgstr "재고 공간"

#. module: stock
#: model:ir.model,name:stock.model_stock_location
msgid "Inventory Locations"
msgstr "재고 공간"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__inventory
msgid "Inventory Loss"
msgstr "재고 손실"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_view_inherit_stock
msgid "Inventory Management"
msgstr "재고 관리"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Inventory On Hand"
msgstr "현재고"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_picking_type_action
msgid "Inventory Overview"
msgstr "재고 관리 현황"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_quantity_set
msgid "Inventory Quantity Set"
msgstr "재고 수량 설정"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__inventory_adjustment_name
msgid "Inventory Reason"
msgstr "재고 사유"

#. module: stock
#: model:ir.model,name:stock.model_stock_route
msgid "Inventory Routes"
msgstr "재고 경로"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Inventory Valuation"
msgstr "재고 자산 평가"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_inventory_at_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__inventory_datetime
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Inventory at Date"
msgstr "날짜별 재고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__is_empty
msgid "Is Empty"
msgstr "비어 있음"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_is_follower
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__is_fresh_package
msgid "Is Fresh Package"
msgstr "신선한 꾸러미 상품 여부"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__is_locked
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_locked
msgid "Is Locked"
msgstr "잠김 여부"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_multi_location
msgid "Is Multi Location"
msgstr "다중 위치입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__is_partial_package
msgid "Is Partial Package"
msgstr "부분 포장입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__is_signed
msgid "Is Signed"
msgstr "서명됨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__scrap_location
msgid "Is a Scrap Location?"
msgstr "폐기 공간 여부"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_initial_demand_editable
msgid "Is initial demand editable"
msgstr "초기 수요 편집 여부"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__has_deadline_issue
msgid "Is late"
msgstr "지연됨"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__has_deadline_issue
msgid "Is late or will be late depending on the deadline and scheduled date"
msgstr "마감일 및 예정일에 따라서 지연되었거나 지연될 예정입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__is_quantity_done_editable
msgid "Is quantity done editable"
msgstr "완료된 수량 편집 가능 여부"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"It is not possible to unreserve more products of %s than you have in stock."
msgstr "%s의 더 많은 품목을 재고보다 확보할 수 없습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__move_type
msgid "It specifies goods to be deliver partially or all at once"
msgstr "품목의 일부 또는 전부를 즉시 배달하도록 지정합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__move_type
msgid "It specifies goods to be transferred partially or all at once"
msgstr "일부 혹은 전부 이동할 품목을 지정합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__json_popover
msgid "JSON data for the popover widget"
msgstr "팝오버 위젯용 JSON 데이터"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__1
msgid "January"
msgstr "1월"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "John Doe"
msgstr "John Doe"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_lead_days
msgid "Json Lead Days"
msgstr "Json 리드 일자"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.js:0
msgid "Json Popup"
msgstr "Json 팝업"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__json_replenishment_history
msgid "Json Replenishment History"
msgstr "Json 보충 내역"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__7
msgid "July"
msgstr "7월"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__6
msgid "June"
msgstr "6월"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__kanban_dashboard_graph
msgid "Kanban Dashboard Graph"
msgstr "칸반 현황판 그래프"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Counted Quantity"
msgstr "계산된 수량 유지"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "Keep Difference"
msgstr "차이 유지"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Keep current lines"
msgstr "현재 라인 유지"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Counted Quantity</strong> (the Difference will be updated)"
msgstr "<strong>계산된 수량</strong> 유지 (차이 업데이트 예정)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid ""
"Keep the <strong>Difference</strong> (the Counted Quantity will be updated "
"to reflect the same difference as when you counted)"
msgstr "<strong>차이</strong> 유지 (실사할 경우와 동일한 차이가 반영되도록 계산된 수량을 업데이트 예정)"

#. module: stock
#: model:ir.actions.server,name:stock.action_print_labels
msgid "Labels"
msgstr "라벨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__label_type
msgid "Labels to print"
msgstr "인쇄할 라벨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Laptop"
msgstr "노트북"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 12 Months"
msgstr "최근 12개월"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 3 Months"
msgstr "최근 3개월"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Last 30 Days"
msgstr "최근 30일"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__last_count_date
msgid "Last Count Date"
msgstr "최종 집계일"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__last_delivery_partner_id
msgid "Last Delivery Partner"
msgstr "최근 배송 협력사"

#. module: stock
#: model:product.removal,name:stock.removal_lifo
msgid "Last In First Out (LIFO)"
msgstr "후입선출 (LIFO)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__last_inventory_date
msgid "Last Inventory"
msgstr "최종 재고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_uid
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_uid
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_uid
#: model:ir.model.fields,field_description:stock.field_product_removal__write_uid
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_location__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_route__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_uid
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__write_date
#: model:ir.model.fields,field_description:stock.field_picking_label_type__write_date
#: model:ir.model.fields,field_description:stock.field_procurement_group__write_date
#: model:ir.model.fields,field_description:stock.field_product_removal__write_date
#: model:ir.model.fields,field_description:stock.field_product_replenish__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__write_date
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__write_date
#: model:ir.model.fields,field_description:stock.field_stock_location__write_date
#: model:ir.model.fields,field_description:stock.field_stock_lot__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move__write_date
#: model:ir.model.fields,field_description:stock.field_stock_move_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_level__write_date
#: model:ir.model.fields,field_description:stock.field_stock_package_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__write_date
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__write_date
#: model:ir.model.fields,field_description:stock.field_stock_quantity_history__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__write_date
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__write_date
#: model:ir.model.fields,field_description:stock.field_stock_request_count__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__write_date
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_route__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rule__write_date
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap__write_date
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__write_date
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__write_date
#: model:ir.model.fields,field_description:stock.field_stock_traceability_report__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__write_date
#: model:ir.model.fields,field_description:stock.field_stock_track_line__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__write_date
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__last_used
msgid "Last Used"
msgstr "최근 사용"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__last_count_date
msgid "Last time the Quantity was Updated"
msgstr "최근 수량 업데이트 시간"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__products_availability_state__late
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late"
msgstr "지연"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Activities"
msgstr "지연된 활동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Late Availability"
msgstr "지연 가용성"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_late
msgid "Late Transfers"
msgstr "지연된 이송 작업"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__products_availability
msgid "Latest product availability status of the picking"
msgstr "피킹 품목의 최근 가용성 상태를 나타냅니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__lead_days_date
msgid "Lead Days Date"
msgstr "리드일 날짜"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__lead_time
#: model:ir.model.fields,field_description:stock.field_stock_rule__delay
msgid "Lead Time"
msgstr "리드타임"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Lead Times"
msgstr "리드타임"

#. module: stock
#: model:product.removal,name:stock.removal_least_packages
msgid "Least Packages"
msgstr "최소 패키지"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__empty
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__none
msgid "Leave Empty"
msgstr "비워두기"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__company_id
#: model:ir.model.fields,help:stock.field_stock_rule__route_company_id
msgid "Leave this field empty if this route is shared between all companies"
msgstr "이 경로가 모든 회사간에 공유되는 경우 이 필드는 공백으로 비워둡니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Legend"
msgstr "범례"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__packaging_length
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Length"
msgstr "길이"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_length
msgid "Length must be positive"
msgstr "길이는 양수여야 합니다"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__length_uom_name
msgid "Length unit of measure label"
msgstr "길이 측정 단위 라벨"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__company_id
#: model:ir.model.fields,help:stock.field_stock_quant__company_id
#: model:ir.model.fields,help:stock.field_stock_quant_relocate__company_id
msgid "Let this field empty if this location is shared between companies"
msgstr "이 위치가 회사간에 공유되는 경우 이 필드를 공백으로 비워둡니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Linked Moves"
msgstr "연결된 이동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of detailed operations"
msgstr "세부 작업 목록 보기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of next transfers"
msgstr "다음 전송 목록 보기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "List view of operations"
msgstr "작업 보기 목록"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__location_id
#: model:ir.model.fields,field_description:stock.field_product_template__location_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__location_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__location_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__location_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__location_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__location
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Location"
msgstr "위치"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_location_barcode
msgid "Location Barcode"
msgstr "위치 바코드"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__name
msgid "Location Name"
msgstr "위치 이름"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__location_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__lot_stock_id
msgid "Location Stock"
msgstr "재고 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__usage
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Location Type"
msgstr "위치 유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: Store to"
msgstr "공간 : 다음에 저장"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Location: When arrives to"
msgstr "공간 : 다음에 도착할 때"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
#: model:ir.actions.act_window,name:stock.action_location_form
#: model:ir.actions.act_window,name:stock.action_prod_inv_location_form
#: model:ir.actions.act_window,name:stock.action_storage_category_locations
#: model:ir.ui.menu,name:stock.menu_action_location_form
#: model:ir.ui.menu,name:stock.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.report_location_barcode
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Locations"
msgstr "위치"

#. module: stock
#: model:ir.actions.server,name:stock.action_toggle_is_locked
msgid "Lock/Unlock"
msgstr "잠금/잠금 해제"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Logistics"
msgstr "물류"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__lot
msgid "Lot"
msgstr "LOT"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_lot_customer_report_view_list
msgid "Lot / Serial Number"
msgstr "로트번호 / 일련번호"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__lot_label_format
msgid "Lot Label Format to auto-print"
msgstr "자동 인쇄할 Lot 라벨 형식"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_lot_template_view
msgid "Lot Label Report"
msgstr "로트 라벨 보고서"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__lot_properties_definition
msgid "Lot Properties"
msgstr "Lot 속성"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Lot numbers"
msgstr "로트 번호"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__lots
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Lot/SN Labels"
msgstr "LOT/일련번호 라벨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
msgid "Lot/SN:"
msgstr "LOT/일련번호:"

#. module: stock
#: model:ir.model,name:stock.model_stock_lot
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__lot_id
msgid "Lot/Serial"
msgstr "LOT/일련번호"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Lot/Serial #"
msgstr "LOT/일련번호 #"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__name
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__lot_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Lot/Serial Number"
msgstr "LOT/일련번호"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_lot_label
msgid "Lot/Serial Number (PDF)"
msgstr "LOT/일련번호 (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_lot_template
msgid "Lot/Serial Number (ZPL)"
msgstr "LOT/일련번호 (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lot_name
msgid "Lot/Serial Number Name"
msgstr "LOT/일련번호 이름"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid "Lot/Serial Number Relocated"
msgstr "Lot/일련 번호 재배치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Lot/Serial:"
msgstr "LOT/일련번호:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_production_lot
msgid "Lots & Serial Numbers"
msgstr "LOT 및 일련번호"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the delivery slip"
msgstr "LOT 및 일련번호가 배송 전표에 표시됩니다."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_production_lot_form
#: model:ir.ui.menu,name:stock.menu_action_production_lot_form
msgid "Lots / Serial Numbers"
msgstr "로트번호 / 일련번호"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__lots_visible
msgid "Lots Visible"
msgstr "로트 표시가능"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid "Lots or serial numbers were not provided for tracked products"
msgstr "상품 배송 추적용으로 로트나 일련번호가 제공되지 않았습니다."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Lots/Serial Numbers"
msgstr "LOT/일련번호"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_production_lot_form
#: model_terms:ir.actions.act_window,help:stock.action_production_lot_form
msgid ""
"Lots/Serial numbers help you tracking the path followed by your products.\n"
"            From their traceability report you will see the full history of their use, as well as their composition."
msgstr ""
"Lots/Serial 번호는 품목의 경로를 추적하도록 도와줍니다.\n"
"          추적보고서에서 사용에 대한 전체 기록과 구성을 확인할 수 있습니다. "

#. module: stock
#: model:ir.actions.act_window,name:stock.action_product_replenish
msgid "Low on stock? Let's replenish."
msgstr "재고가 부족하신가요? 재보충을 시도합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__mto_pull_id
msgid "MTO rule"
msgstr "주문생산 규칙"

#. module: stock
#: model:res.groups,name:stock.group_tracking_owner
msgid "Manage Different Stock Owners"
msgstr "다른 재고 소유주 관리"

#. module: stock
#: model:res.groups,name:stock.group_production_lot
msgid "Manage Lots / Serial Numbers"
msgstr "LOT / 일련번호 관리"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_locations
msgid "Manage Multiple Stock Locations"
msgstr "다수의 재고 위치 관리"

#. module: stock
#: model:res.groups,name:stock.group_stock_multi_warehouses
msgid "Manage Multiple Warehouses"
msgstr "다수의 창고 관리"

#. module: stock
#: model:res.groups,name:stock.group_tracking_lot
msgid "Manage Packages"
msgstr "패키지 품목 관리"

#. module: stock
#: model:res.groups,name:stock.group_adv_location
msgid "Manage Push and Pull inventory flows"
msgstr "Push and Pull 재고 흐름 관리"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Manage product packagings (e.g. pack of 6 bottles, box of 10 pieces)"
msgstr "품목 패키지를 관리합니다. (예. 6병 포장, 10개들이 상자 포장)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse_orderpoint__trigger__manual
msgid "Manual"
msgstr "수동"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__auto__manual
msgid "Manual Operation"
msgstr "수동 작업"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "Manual Replenishment"
msgstr "수동 보충"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__reservation_method__manual
msgid "Manually"
msgstr "수동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
msgid "Manufacturing"
msgstr "제조 관리"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__3
msgid "March"
msgstr "3월"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Mark as Todo"
msgstr "할 일로 표시"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Max"
msgstr "최대"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid "Max Quantity"
msgstr "최대 수량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__max_weight
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__max_weight
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Max Weight"
msgstr "최대 중량"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_max_weight
msgid "Max Weight must be positive"
msgstr "중량은 양수여야 합니다"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_positive_max_weight
msgid "Max weight should be a positive number."
msgstr "최대 중량은 양수이어야 합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before_priority
msgid ""
"Maximum number of days before scheduled date that priority picking products "
"should be reserved."
msgstr "빠른 픽업 상품을 예약해야 하는 예약일 이전의 최대 일수입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__reservation_days_before
msgid ""
"Maximum number of days before scheduled date that products should be "
"reserved."
msgstr "상품을 예약해야 하는 예약일 이전의 최대 일수입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__max_weight
msgid "Maximum weight shippable in this packaging"
msgstr "이 상품 포장으로 선적할 수 있는 최대 중량"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__5
msgid "May"
msgstr "5월"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn_msg
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn_msg
msgid "Message for Stock Picking"
msgstr "재고 선별을 위한 메세지"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_ids
msgid "Messages"
msgstr "메시지"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__method
msgid "Method"
msgstr "방법"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Min"
msgstr "최소"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid "Min Quantity"
msgstr "최소 수량"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse_orderpoint
msgid "Minimum Inventory Rule"
msgstr "최소 재고 규칙"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__orderpoint_ids
msgid "Minimum Stock Rules"
msgstr "최소 재고 규칙"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_ids
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__move_ids
msgid "Move"
msgstr "이동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_operations
msgid "Move Detail"
msgstr "이동 세부 내용"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_entire_packs
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_entire_packs
msgid "Move Entire Packages"
msgstr "전체 패키지 품목 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_move__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__move_line_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_level__move_line_ids
msgid "Move Line"
msgstr "이동 명세"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
msgid "Move Lines"
msgstr "이동 명세"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_lines_count
msgid "Move Lines Count"
msgstr "이동 항목 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__move_quantity
msgid "Move Quantity"
msgstr "이동 수량"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__origin_returned_move_id
msgid "Move that created the return move"
msgstr "반품 이동을 만든 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__product_return_moves
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Moves"
msgstr "이동"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_action
#: model:ir.ui.menu,name:stock.stock_move_menu
msgid "Moves Analysis"
msgstr "이동 분석"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_move_line_action
#: model:ir.ui.menu,name:stock.stock_move_line_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_pivot
msgid "Moves History"
msgstr "이동 내역"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__group_id
msgid ""
"Moves created through this orderpoint will be put in this procurement group."
" If none is given, the moves generated by stock rules will be grouped into "
"one big picking."
msgstr ""
"이 주문 지점을 통해 생성된 이동이 이 조달 그룹에 배치됩니다. 아무것도 주어지지 않으면, 재고 규칙에 의해 생성된 움직임은 하나의 큰 "
"피킹으로 그룹화됩니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_adv_location
msgid "Multi-Step Routes"
msgstr "다중-단계 경로"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid "Multiple Quantity"
msgstr "여러 수량"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_package_type
msgid "Multiple capacity rules for one package type."
msgstr "여러 용량 규칙을 하나의 패키지 유형에 적용"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_unique_product
msgid "Multiple capacity rules for one product."
msgstr "여러 용량 규칙을 하나의 품목에 적용"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__my_activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"My Company (Chicago) undertakes to do its best to supply performant services"
" in due time in accordance with the agreed timeframes. However, none of its "
"obligations can be considered as being an obligation to achieve results. My "
"Company (Chicago) cannot under any circumstances, be required by the client "
"to appear as a third party in the context of any claim for damages filed "
"against the client by an end consumer."
msgstr ""
"My Company (Chicago)는 합의된 기간에 따라 적시에 성능 기준에 맞는 서비스를 제공하기 위해 최선을 다할 것을 약속합니다."
" 그러나 이러한 의무 중 어떤 것도 결과를 달성하기 위한 의무 사항으로 간주될 수 없습니다. My Company (Chicago)는 어떤"
" 상황에서도 최종 소비자가 고객에게 제기한 손해 배상 청구와 관련하여 고객이 당사를 제3자 업체로 표시되도록 할 수 없습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "My Counts"
msgstr "나의 수치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "My Transfers"
msgstr "나의 이송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_removal__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__name
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_display_name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__name
msgid "Name"
msgstr "이름"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Name Demo"
msgstr "데모용 이름"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_in
msgid "Nbr Moves In"
msgstr "입고 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_moves_out
msgid "Nbr Moves Out"
msgstr "출고 수"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_search_form_view_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_product_search_form_view
msgid "Negative Forecasted Quantity"
msgstr "음수 예상 수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Negative Stock"
msgstr "음수 재고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__net_weight
msgid "Net Weight"
msgstr "순 중량"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__create_backorder__never
msgid "Never"
msgstr "하지않음"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__never_product_template_attribute_value_ids
msgid "Never attribute Values"
msgstr "값 속성 없음"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__draft
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__new
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "New"
msgstr "신규"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "New Move: %(product)s"
msgstr "새 이동: %(product)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__new_quantity
msgid "New Quantity on Hand"
msgstr "새 보유 수량"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_form
msgid "New Transfer"
msgstr "신규 이송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_calendar_event_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_date_deadline
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_summary
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_type_id
msgid "Next Activity Type"
msgstr "다음 활동 유형"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__next_inventory_date
msgid "Next Expected"
msgstr "다음 예상"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Next Transfers"
msgstr "다음 이동"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_date
msgid "Next date the On Hand Quantity should be counted."
msgstr "보유 중인 수량을 계산해야 하는 다음 날짜입니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "Next transfer(s) impacted:"
msgstr "다음 배송에 영향 있음 :"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_putaway_rule__sublocation__no
msgid "No"
msgstr "아니오"

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "No %s selected or a delivery order selected"
msgstr "%s 선택된 내용이 없거나 배송주문서를 선택하였습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "No Backorder"
msgstr "이월 주문 없음"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__no-message
msgid "No Message"
msgstr "메시지 없음"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "No Stock On Hand"
msgstr "보유 재고 없음"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "No allocation need found."
msgstr "배정할 내용 없음"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "No data yet!"
msgstr "데이터가 아직 없습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No delivery to do!"
msgstr "배송할 품목이 없습니다!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "No negative quantities allowed"
msgstr "음수 수량 허용되지 않음"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "No operation made on this lot."
msgstr "이 로트에서는 아무런 작업도 하지 않았습니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
msgid "No operations found. Let's create a transfer!"
msgstr "작업을 찾을 수 없습니다. 전송 내역을 생성합니다!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "No product found to generate Serials/Lots for."
msgstr "일련 번호/로트를 생성할 제품을 찾을 수 없습니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "No product found. Let's create one!"
msgstr "품목이 없습니다. 새로 작성해 주세요."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid ""
"No products to return (only lines in Done state and not fully returned yet "
"can be returned)."
msgstr "반품할 품목이 없습니다(완료 상태의 내역과 아직 완전히 반품되지 않은 건만 반품이 가능합니다)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_putaway_tree
msgid "No putaway rule found. Let's create one!"
msgstr "보관규칙이 없습니다. 새로 작성해 주세요."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No receipt yet! Create a new one."
msgstr "아직 입고 내역이 없습니다! 새로 생성하세요."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint
msgid "No reordering rule found"
msgstr "재주문 규칙이 없습니다"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"No rule has been found to replenish \"%(product)s\" in \"%(location)s\".\n"
"Verify the routes configuration on the product."
msgstr ""
"\"%(product)s\"를 보충하는 규칙이 \"%(location)s\"에서 발견되지 않았습니다.\n"
"품목의 경로 환경 설정을 확인합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "No source location defined on stock rule: %s!"
msgstr "재고 규칙에 정의된 원본 위치가 없습니다 : %s!"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid "No stock move found"
msgstr "재고이동을 찾을수 없습니다"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_product_stock_view
msgid "No stock to show"
msgstr "표시할 재고 없음"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "No transfer found. Let's create one!"
msgstr "재고 이동이 없습니다. 새로 작성해 주세요."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__0
msgid "Normal"
msgstr "일반"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: code:addons/stock/static/src/widgets/forecast_widget.xml:0
msgid "Not Available"
msgstr "사용불가"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Not Snoozed"
msgstr "일시 중지되지 않음"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Note"
msgstr "노트"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__note
msgid "Notes"
msgstr "메모"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Nothing to check the availability for."
msgstr "사용 가능한 재고가 없습니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__11
msgid "November"
msgstr "11월"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Number of SN"
msgstr "SN 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__next_serial_count
msgid "Number of SN/Lots"
msgstr "일련번호/로트 수"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_in
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_in
msgid "Number of incoming stock moves in the past 12 months"
msgstr "지난 12개월 동안의 재고 입고 이동 수"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_needaction_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_picking__message_has_error_counter
#: model:ir.model.fields,help:stock.field_stock_scrap__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__nbr_moves_out
#: model:ir.model.fields,help:stock.field_product_template__nbr_moves_out
msgid "Number of outgoing stock moves in the past 12 months"
msgstr "지난 12개월 동안의 출고 재고 이동 수"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__days_to_order
msgid "Numbers of days  in advance that replenishments demands are created."
msgstr "보충 요청이 생성되기 전까지의 일수입니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__10
msgid "October"
msgstr "10월"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Odoo opens a PDF preview by default. If you (Enterprise users only) want to print instantly,\n"
"                                        install the IoT App on a computer that is on the same local network as the\n"
"                                        barcode operator and configure the routing of the reports.\n"
"                                        <br/>"
msgstr ""
"Odoo는 기본적으로 PDF 미리 보기를 엽니다. 유료 버전을 구독하고 있는 경우, 즉시 인쇄하려면\n"
"                                        바코드 운영자와 동일한 로컬 네트워크에 있는 컴퓨터에 IoT 앱을 설치하고\n"
"                                        보고서의 라우팅을 설정하세요.\n"
"                                        <br/>"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Office Chair"
msgstr "사무용 의자"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_quant__on_hand
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_on_hand
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_template_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_product_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "On Hand"
msgstr "보유량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_qty
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "On Hand Quantity"
msgstr "보유 수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "On hand Quantity"
msgstr "보유 수량"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__available_quantity
msgid ""
"On hand quantity which hasn't been reserved on a transfer, in the default "
"unit of measure of the product"
msgstr "재고이동에 예약되지 않은 현재고, 품목의 기본단위기준"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_kanban_stock_view
msgid "On hand:"
msgstr "보유 :"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__lots
msgid "One per lot/SN"
msgstr "LOT/일련번호 당 하나"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__label_quantity__units
msgid "One per unit"
msgstr "단위 당 하나"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Open"
msgstr "열기"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__move_quantity__move
msgid "Operation Quantities"
msgstr "작업 수량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Operation Type"
msgstr "작업 유형"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__return_picking_type_id
msgid "Operation Type for Returns"
msgstr "반품 작업 유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking_type_label
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Operation Types"
msgstr "작업 유형"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/models/stock_quant.py:0
msgid "Operation not supported"
msgstr "지원되지 않는 작업"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_id
msgid "Operation type"
msgstr "작업 유형"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking_type_label
msgid "Operation type (PDF)"
msgstr "작업 유형 (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking_type
msgid "Operation type (ZPL)"
msgstr "작업 유형 (ZPL)"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids
#: model:ir.ui.menu,name:stock.menu_stock_warehouse_mgmt
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Operations"
msgstr "작업"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_type_list
#: model:ir.ui.menu,name:stock.menu_pickingtype
msgid "Operations Types"
msgstr "작업 유형"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_line_ids_without_package
msgid "Operations without package"
msgstr "꾸러미 상품이 없는 작업"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Optimize your transfers by grouping operations together and assigning jobs "
"to workers"
msgstr "작업을 그룹화하고 작업자에게 작업을 할당하여 전송을 최적화하세요."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__partner_id
msgid ""
"Optional address where goods are to be delivered, specifically used for "
"allotment"
msgstr "상품을 배송할 선택적 주소, 특히 할당에 사용"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__posx
#: model:ir.model.fields,help:stock.field_stock_location__posy
#: model:ir.model.fields,help:stock.field_stock_location__posz
msgid "Optional localization details, for information purpose only"
msgstr "정보용으로만 제공되는 선택적 현지화 정보"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__returned_move_ids
msgid "Optional: all returned moves created from this move"
msgstr "선택 사항 : 이 이동에서 작성된 모든 반송된 이동"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_dest_ids
msgid "Optional: next stock move when chaining them"
msgstr "선택 사항 : 연결시 다음 재고 이동"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__move_orig_ids
msgid "Optional: previous stock move when chaining them"
msgstr "선택 사항 : 연결할 때 이전 재고 이동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Options"
msgstr "옵션"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_warning_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Order"
msgstr "주문"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
msgid "Order To Max"
msgstr "최대 주문량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed"
msgstr "완료된 주문"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Order signed by %s"
msgstr "%s 를 통해 승인된 주문"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "Ordered"
msgstr "주문함"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__orderpoint_ids
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__orderpoint_id
msgid "Orderpoint"
msgstr "지시점"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin"
msgstr "출발지"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
msgid "Origin Moves"
msgstr "원점 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin_returned_move_id
msgid "Origin return move"
msgstr "원점 반송 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__move_orig_ids
msgid "Original Move"
msgstr "원점 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__orderpoint_id
msgid "Original Reordering Rule"
msgstr "원래의 재주문 규칙"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Other Information"
msgstr "기타 정보"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"Our invoices are payable within 21 working days, unless another payment "
"timeframe is indicated on either the invoice or the order. In the event of "
"non-payment by the due date, My Company (Chicago) reserves the right to "
"request a fixed interest payment amounting to 10% of the sum remaining due. "
"My Company (Chicago) will be authorized to suspend any provision of services"
" without prior warning in the event of late payment."
msgstr ""
"청구서나 주문서에 다른 지불 기간이 명시되어 있지 않는 한, 당사의 청구서는 영업일 기준 21일 이내에 지불하도록 되어 있습니다. "
"기한일까지 결제가 완료되지 않은 경우 My Company (Chicago)는 미납금의 10%에 해당하는 고정 이자를 요청할 권한을 "
"가집니다. 또한 My Company (Chicago)는 연체 시 사전 경고 없이 서비스 제공을 중단할 권한이 있습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__out_type_id
msgid "Out Type"
msgstr "출력 유형"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_header.xml:0
#: model:ir.model.fields,field_description:stock.field_product_product__outgoing_qty
#: model:ir.model.fields,field_description:stock.field_product_template__outgoing_qty
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Outgoing"
msgstr "출고"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Outgoing Draft Transfer"
msgstr "출고 초안 전송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__outgoing_move_line_ids
msgid "Outgoing Move Line"
msgstr "출고 이동 내역"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__delivery_steps
msgid "Outgoing Shipments"
msgstr "출하 상차"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Output"
msgstr "산출"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_output_stock_loc_id
msgid "Output Location"
msgstr "출고 공간(위치)"

#. module: stock
#: model:ir.ui.menu,name:stock.stock_picking_type_menu
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Overview"
msgstr "전체보기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__owner_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__owner_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Owner"
msgstr "소유자"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__restrict_partner_id
msgid "Owner "
msgstr "담당자(파트너) "

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Owner:"
msgstr "소유자:"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "P&L Qty"
msgstr "P&L 수량"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__pdf
msgid "PDF"
msgstr "PDF"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Pack"
msgstr "묶음"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__pack_date
msgid "Pack Date"
msgstr "포장 일자"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date Demo"
msgstr "데모용 포장 일자"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Pack Date:"
msgstr "포장 일자:"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pack_type_id
msgid "Pack Type"
msgstr "묶음 유형"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__package_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__package_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__package_id
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__package
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_tree
msgid "Package"
msgstr "패키지"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package A"
msgstr "패키지 A"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Package B"
msgstr "패키지 B"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode_small
msgid "Package Barcode (PDF)"
msgstr "패키지 바코드 (PDF)"

#. module: stock
#: model:ir.actions.report,name:stock.label_package_template
msgid "Package Barcode (ZPL)"
msgstr "패키지 바코드 (ZPL)"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_quant_package_barcode
msgid "Package Barcode with Contents"
msgstr "내용물이 있는 패키지 바코드"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__package_capacity_ids
msgid "Package Capacity"
msgstr "패키지 용량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_package_level.py:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Content"
msgstr "패키지 내용물"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Package Label"
msgstr "패키지 라벨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__package_label_to_print
msgid "Package Label to Print"
msgstr "인쇄할 패키지 라벨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_level_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids
msgid "Package Level"
msgstr "패키지 단계"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__package_level_ids_details
msgid "Package Level Ids Details"
msgstr "패키지 단계 ids 세부사항"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
msgid "Package Name"
msgstr "패키지 이름"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__name
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Reference"
msgstr "패키지 참조"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Package Transfers"
msgstr "패키지 이송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_packaging__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_package_type__name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__package_type_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_type_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__package_type_id
#: model_terms:ir.ui.view,arch_db:stock.quant_package_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Package Type"
msgstr "패키지 유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type Demo"
msgstr "데모용 패지키 유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "Package Type:"
msgstr "패키지 유형:"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_type_view
#: model:ir.ui.menu,name:stock.menu_packaging_types
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_tree
msgid "Package Types"
msgstr "패키지 유형"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__package_use
msgid "Package Use"
msgstr "포장 사용"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Package manually relocated"
msgstr "수동으로 재배치된 패키지"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__valid_sscc
msgid "Package name is valid SSCC"
msgstr "패키지 이름은 유효한 SSCC 입니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Package type"
msgstr "패키지 유형"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_package_view
#: model:ir.actions.report,name:stock.action_report_picking_packages
#: model:ir.model,name:stock.model_stock_quant_package
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_tracking_lot
#: model:ir.ui.menu,name:stock.menu_package
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Packages"
msgstr "패키지"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_package_view
msgid ""
"Packages are usually created via transfers (during pack operation) and can contain different products.\n"
"                Once created, the whole package can be moved at once, or products can be unpacked and moved as single units again."
msgstr ""
"패키지는 일반적으로 (포장 작업 중에) 재고이동을 통해 생성되며  여러 품목을 넣을 수 있습니다.\n"
"            일단 생성되면 전체 패키지를 한 번에 이동하거나 포장을 풀고 품목을 다시 각각의 단위로 이동할 수 있습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_id
msgid "Packaging"
msgstr "패키징"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__height
msgid "Packaging Height"
msgstr "패키지 높이"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__packaging_length
msgid "Packaging Length"
msgstr "패키지 길이"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__width
msgid "Packaging Width"
msgstr "패키지 폭"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__packaging_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Packagings"
msgstr "패키지"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_pack_stock_loc_id
msgid "Packing Location"
msgstr "포장 공간(위치)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Packing Zone"
msgstr "포장 구역"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Pallet"
msgstr "팔레트"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Parent Location"
msgstr "상위 공간(위치)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__parent_path
msgid "Parent Path"
msgstr "상위 경로"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__parent_route_ids
msgid "Parent Routes"
msgstr "상위 경로"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__procurement_group__move_type__direct
msgid "Partial"
msgstr "부분"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__partial_package_names
msgid "Partial Package Names"
msgstr "부분 패키지 이름"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__partially_available
msgid "Partially Available"
msgstr "부분적으로 이용 가능"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__partner_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__partner_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Partner"
msgstr "협력사"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__partner_address_id
msgid "Partner Address"
msgstr "협력사 주소"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_action_inventory_tree
msgid "Physical Inventory"
msgstr "재고 실사"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__pick_ids
msgid "Pick"
msgstr "선별"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quant_id
msgid "Pick From"
msgstr "픽업 장소"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__pick_type_id
msgid "Pick Type"
msgstr "선별 유형"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_ship
msgid "Pick then Deliver (2 steps)"
msgstr "픽업 후 배송 (2단계)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__delivery_steps__pick_pack_ship
msgid "Pick, Pack, then Deliver (3 steps)"
msgstr "픽업, 포장, 배송 (3단계)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picked
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picked
msgid "Picked"
msgstr "선별"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_picking_label_type__picking_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_destination__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__picking_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Picking"
msgstr "선별"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Picking Lists"
msgstr "선별 목록"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_picking
msgid "Picking Operations"
msgstr "선별 작업"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__picking_properties_definition
msgid "Picking Properties"
msgstr "피킹 속성"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking_type
msgid "Picking Type"
msgstr "선별 유형"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__picking_type_code_domain
msgid "Picking Type Code Domain"
msgstr "픽업 유형 코드 도메인"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Picking list"
msgstr "선별 목록"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Planning Issue"
msgstr "계획 문제"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Planning Issues"
msgstr "계획 문제"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Please create a warehouse for company %s."
msgstr "회사 %s에 대한 창고를 만들어 주세요."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid ""
"Please put this document inside your return parcel.<br/>\n"
"                                Your parcel must be sent to this address:"
msgstr ""
"이 문서를 반품 택배 안에 동봉하여 주십시오.<br/>\n"
"                                반품 주소:"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Please specify at least one non-zero quantity."
msgstr "0이 아닌 수량을 하나 이상 지정하십시오."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "Preceding operations"
msgstr "선행 작업"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__route_id
#: model:ir.model.fields,field_description:stock.field_stock_replenish_mixin__route_id
msgid "Preferred Route"
msgstr "선호 경로"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__route_ids
msgid "Preferred route"
msgstr "선호 경로"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "Presence depends on the type of operation."
msgstr "참석은 작업 유형에 따라 다릅니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Press the CREATE button to define quantity for each product in your stock or"
" import them from a spreadsheet throughout Favorites"
msgstr "신규 버튼을 눌러 각 품목에 대한 재고 수량을 정의하거나 즐겨찾기에 있는 스프레드 시트에서 가져오기할 수 있습니다."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Print"
msgstr "인쇄"

#. module: stock
#: model:res.groups,name:stock.group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lot & Serial Numbers"
msgstr "LOT 및 일련번호용 GS1 바코드 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_lot_print_gs1
msgid "Print GS1 Barcodes for Lots & Serial Numbers"
msgstr "LOT 및 일련번호용 GS1 바코드 인쇄"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Print Label"
msgstr "라벨 인쇄"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_main/stock_reception_report_main.xml:0
#: code:addons/stock/static/src/components/reception_report_table/stock_reception_report_table.xml:0
#: code:addons/stock/static/src/xml/report_stock_reception.xml:0
msgid "Print Labels"
msgstr "라벨 인쇄"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print label as:"
msgstr "다른 이름으로 라벨 인쇄:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on \"Put in Pack\""
msgstr "\"포장에 넣기\" 인쇄"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Print on Validation"
msgstr "유효성 검사 시 인쇄"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__printed
msgid "Printed"
msgstr "인쇄 완료"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__priority
#: model:ir.model.fields,field_description:stock.field_stock_picking__priority
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sequence
msgid "Priority"
msgstr "우선 순위"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__delay_alert_date
msgid "Process at this date to be on time"
msgstr "해당 날짜에 정시 처리"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Process operations faster with barcodes"
msgstr "바코드를 사용하여 작업 처리 속도를 향상할 수 있습니다."

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_procurement
msgid "Procurement"
msgstr "조달"

#. module: stock
#: model:ir.model,name:stock.model_procurement_group
#: model:ir.model.fields,field_description:stock.field_stock_move__group_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__group_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__group_id
msgid "Procurement Group"
msgstr "조달 그룹"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
msgid "Procurement group"
msgstr "조달 그룹"

#. module: stock
#: model:ir.actions.server,name:stock.ir_cron_scheduler_action_ir_actions_server
#: model:ir.ui.menu,name:stock.menu_procurement_compute
msgid "Procurement: run scheduler"
msgstr "조달 : 스케줄러 실행"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__produce_line_ids
msgid "Produce Line"
msgstr "생산 내역"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Produced Qty"
msgstr "생산된 수량"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model,name:stock.model_product_template
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_id
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__product_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__product_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__product_id
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__product_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_id
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_id
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Product"
msgstr "품목"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability
msgid "Product Availability"
msgstr "품목 가용성"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__product_capacity_ids
msgid "Product Capacity"
msgstr "품목 용량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__categ_ids
#: model:ir.ui.menu,name:stock.menu_product_category_config_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Product Categories"
msgstr "품목 카테고리"

#. module: stock
#: model:ir.model,name:stock.model_product_category
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_category_name
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_categ_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Product Category"
msgstr "품목 카테고리"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Product Display Name"
msgstr "품목 표시명"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_product
msgid "Product Label (ZPL)"
msgstr "품목 라벨 (ZPL)"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__product_label_format
msgid "Product Label Format to auto-print"
msgstr "자동 인쇄할 품목 라벨 형식"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_label_product_product_view
msgid "Product Label Report"
msgstr "품목 라벨 보고서"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__picking_label_type__label_type__products
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Product Labels"
msgstr "품목 라벨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_product_lot_filter
msgid "Product Lots Filter"
msgstr "품목 LOT 필터"

#. module: stock
#: model:ir.model,name:stock.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "품목 이동 (재고 이동 상세)"

#. module: stock
#: model:ir.model,name:stock.model_product_packaging
msgid "Product Packaging"
msgstr "품목 포장법"

#. module: stock
#: model:ir.actions.report,name:stock.label_product_packaging
msgid "Product Packaging (ZPL)"
msgstr "품목 패키지(ZPL)"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_product_packagings
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Product Packagings"
msgstr "패키지 품목"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Confirmed"
msgstr "품목 수량 확인"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Product Quantity Updated"
msgstr "품목 수량이 업데이트되었습니다"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Product Relocated"
msgstr "품목 이전"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.js:0
#: model:ir.model,name:stock.model_product_replenish
msgid "Product Replenish"
msgstr "품목 보충"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenish_mixin
msgid "Product Replenish Mixin"
msgstr "제품 보충 믹스인"

#. module: stock
#: model:ir.actions.report,name:stock.action_report_stock_rule
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rules_report
msgid "Product Routes Report"
msgstr "품목 이동 경로 보고서"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_move__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__product_tmpl_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_tmpl_id
msgid "Product Template"
msgstr "품목 양식"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_tmpl_id
msgid "Product Tmpl"
msgstr "품목 서식"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__tracking
msgid "Product Tracking"
msgstr "품목 추적"

#. module: stock
#: model:ir.model,name:stock.model_uom_uom
msgid "Product Unit of Measure"
msgstr "품목 단위"

#. module: stock
#: model:ir.model,name:stock.model_product_product
msgid "Product Variant"
msgstr "품목 세부선택"

#. module: stock
#: model:ir.actions.act_window,name:stock.stock_product_normal_action
#: model:ir.ui.menu,name:stock.product_product_menu
msgid "Product Variants"
msgstr "품목 세부사항"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Product barcode"
msgstr "품목 바코드"

#. module: stock
#. odoo-python
#: code:addons/stock/report/product_label_report.py:0
msgid "Product model not defined, Please contact your administrator."
msgstr "품목 모델이 정의되지 않았습니다. 관리자에게 문의하십시오."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid ""
"Product this lot/serial number contains. You cannot change it anymore if it "
"has already been moved."
msgstr "이 LOT/Serial 번호가 포함된 품목. 이미 이동된 경우 더이상 변경할 수 없습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom_name
msgid "Product unit of measure label"
msgstr "품목 단위 라벨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__has_tracking
msgid "Product with Tracking"
msgstr "품목 추적"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__production
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production"
msgstr "생산"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__property_stock_production
#: model:ir.model.fields,field_description:stock.field_product_template__property_stock_production
msgid "Production Location"
msgstr "생산 공간"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Production Locations"
msgstr "생산 공간"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_quantity_history.py:0
#: model:ir.actions.act_window,name:stock.act_product_location_open
#: model:ir.actions.act_window,name:stock.product_template_action_product
#: model:ir.model.fields,field_description:stock.field_stock_route__product_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__product_ids
#: model:ir.ui.menu,name:stock.menu_product_in_config_stock
#: model:ir.ui.menu,name:stock.menu_product_variant_config_stock
#: model:ir.ui.menu,name:stock.menu_stock_inventory_control
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "Products"
msgstr "품목"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__products_availability_state
msgid "Products Availability State"
msgstr "품목 사용가능 단계"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__priority
msgid ""
"Products will be reserved first for the transfers with the highest "
"priorities."
msgstr "재고이동시 우선 순위가 가장 높은  품목이 먼저 예약됩니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Products: %(location)s"
msgstr "품목: %(location)s"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__group_propagation_option__propagate
msgid "Propagate"
msgstr "전달"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__propagate_cancel
msgid "Propagate cancel and split"
msgstr "취소 및 분할 전달"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Propagation"
msgstr "전달하기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__group_propagation_option
msgid "Propagation of Procurement Group"
msgstr "조달 그룹의 전달하기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_carrier
msgid "Propagation of carrier"
msgstr "배송업체 전달"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__lot_properties
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_properties
#: model:ir.model.fields,field_description:stock.field_stock_quant__lot_properties
msgid "Properties"
msgstr "속성"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull_push
msgid "Pull & Push"
msgstr "푸쉬-풀 방식"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__pull
msgid "Pull From"
msgstr "풀 방식"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Pull Rule"
msgstr "풀 전략"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__push_domain
msgid "Push Applicability"
msgstr "푸시 적용"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Push Rule"
msgstr "푸시 전략"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__action__push
msgid "Push To"
msgstr "푸시 방식"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_detailed_operation_tree
msgid "Put in Pack"
msgstr "포장에 넣기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Put your products in packs (e.g. parcels, boxes) and track them"
msgstr "제품을 패키지(예: 소포, 박스)에 포장하고 추적합니다."

#. module: stock
#: model:ir.model,name:stock.model_stock_putaway_rule
msgid "Putaway Rule"
msgstr "보관 규칙"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
#: model:ir.actions.act_window,name:stock.category_open_putaway
#: model:ir.actions.act_window,name:stock.location_open_putaway
#: model:ir.model.fields,field_description:stock.field_product_category__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_product_product__putaway_rule_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__putaway_rule_ids
#: model:ir.ui.menu,name:stock.menu_putaway
#: model_terms:ir.ui.view,arch_db:stock.product_category_form_view_inherit
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Putaway Rules"
msgstr "보관 규칙"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Putaway:"
msgstr "반입 :"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_putaway_tree
msgid "Putaways Rules"
msgstr "보관 규칙"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_orderpoint_qty_multiple_check
msgid "Qty Multiple must be greater than or equal to zero."
msgstr "다수 수량은 0보다 크거나 같아야합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control
msgid "Quality"
msgstr "품질 관리"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Quality Control"
msgstr "품질 관리"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__wh_qc_stock_loc_id
msgid "Quality Control Location"
msgstr "품질 관리 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__qc_type_id
msgid "Quality Control Type"
msgstr "품질 관리 유형"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_quality_control_worksheet
msgid "Quality Worksheet"
msgstr "품질 워크시트"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_inventory_adjustment_name__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_inventory_warning__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_location__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_request_count__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quant_ids
msgid "Quant"
msgstr "Quant"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's creation is restricted, you can't do this operation."
msgstr "Quant의 생성이 제한되어 있으므로 이 작업을 수행할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quant's editing is restricted, you can't do this operation."
msgstr "퀀트 편집이 제한되어 있는 관계로 이 작업을 수행할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities Already Set"
msgstr "이미 설정된 수량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities To Reset"
msgstr "수량 재설정"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantities unpacked"
msgstr "포장 개봉 수량"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_product_replenish__quantity
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__product_qty
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move__quantity
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_quant__quantity
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__quantity
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_qty
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__quantity
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__quantity
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_kandan
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Quantity"
msgstr "수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
msgid "Quantity Multiple"
msgstr "다수의 수량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__qty_available
#: model:ir.model.fields,field_description:stock.field_product_template__qty_available
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity On Hand"
msgstr "보유 수량"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity Received"
msgstr "받은 수량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity Relocated"
msgstr "이전된 수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form_editable
msgid "Quantity Reserved"
msgstr "예약된 수량"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_replenishment_info.py:0
msgid "Quantity available too low"
msgstr "사용 가능한 수량이 너무 적음"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_change_product_qty.py:0
msgid "Quantity cannot be negative."
msgstr "수량은 마이너스 값을 가질 수 없습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__is_outdated
msgid "Quantity has been moved since last count"
msgstr "최근 조사한 이래 수량이 이동됨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__quantity_product_uom
msgid "Quantity in Product UoM"
msgstr "품목 단위 수량"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__availability
msgid "Quantity in stock that can still be reserved for this move"
msgstr "이 이동을 위해 여전히 예약 할 수있는 재고 수량"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_qty
msgid "Quantity in the default UoM of the product"
msgstr "이 양은 품목의 기본 단위로 수량이 표시됩니다"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__incoming_qty
msgid ""
"Quantity of planned incoming products.\n"
"In a context with a single Stock Location, this includes goods arriving to this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods arriving to the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods arriving to any Stock Location with 'internal' type."
msgstr ""
"계획된 입고 품목의 수량.\n"
"단일 재고 공간이 있는 상황에서, 이 공간나 그 하위 공간에 도착한 품목이 여기에 포함됩니다.\n"
"단일 창고의 경우, 이 창고의 재고 공간 또는 그 하위 공간에 도착한 품목이 여기에 포함됩니다.\n"
"그렇지 않으면 '내부' 유형의 재고 공간에 도착한 품목이 포함됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__outgoing_qty
msgid ""
"Quantity of planned outgoing products.\n"
"In a context with a single Stock Location, this includes goods leaving this Location, or any of its children.\n"
"In a context with a single Warehouse, this includes goods leaving the Stock Location of this Warehouse, or any of its children.\n"
"Otherwise, this includes goods leaving any Stock Location with 'internal' type."
msgstr ""
"계획 출고되는 체품의 수량.\n"
"단일 재고 공간이 있는 상황에서 이 공간를 떠나는 품목 또는 그 하위 공간의 품목을 포함합니다.\n"
"단일 창고가 있는 환경에서 이 창고의 재고 공간을 떠나는 품목 또는 그 하위 공간을 포함합니다.\n"
"그렇지 않은 경우, 이 것은 '내부' 유형의 모든 재고 공간를 벗어나는 품목을 포함합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__quantity
msgid ""
"Quantity of products in this quant, in the default unit of measure of the "
"product"
msgstr "이 퀀트의 품목 수량, 품목의 기본단위 기준"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__reserved_quantity
msgid ""
"Quantity of reserved products in this quant, in the default unit of measure "
"of the product"
msgstr "이 퀀트의 예약 품목 수량, 품목의 기본단위 기준"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quantity or Reserved Quantity should be set."
msgstr "수량 또는 예약 수량이 설정되어 있어야 합니다."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Quantity per Lot"
msgstr "로트별 수량"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_storage_category_capacity_positive_quantity
msgid "Quantity should be a positive number."
msgstr "수량은 양수여야 합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_lot_label_layout__label_quantity
#: model:ir.model.fields,field_description:stock.field_product_label_layout__move_quantity
msgid "Quantity to print"
msgstr "인쇄할 수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Quantity:"
msgstr "수량:"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant
#: model:ir.model.fields,field_description:stock.field_stock_inventory_conflict__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_lot__quant_ids
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__quant_ids
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Quants"
msgstr "수량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Quants are auto-deleted when appropriate. If you must manually delete them, "
"please ask a stock manager to do it."
msgstr "수량은 필요할 경우 자동 삭제됩니다. 수기로 삭제해야 하는 경우에는 재고 관리자에게 연락하시기 바랍니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Quants cannot be created for consumables or services."
msgstr "소비재 또는 서비스에 대해 퀀트를 생성할 수 없습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_return_slip
msgid "RETURN OF"
msgstr "반품"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__rating_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__rating_ids
msgid "Ratings"
msgstr "평점"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__assigned
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Ready"
msgstr "준비"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_get_picking_type_ready_moves
msgid "Ready Moves"
msgstr "준비된 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_qty
msgid "Real Quantity"
msgstr "실수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_body
msgid "Reason"
msgstr "사유"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_relocate__message
msgid "Reason for relocation"
msgstr "이전 사유"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__code__incoming
msgid "Receipt"
msgstr "입고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__reception_route_id
msgid "Receipt Route"
msgstr "입고 경로"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_incoming
#: model:ir.ui.menu,name:stock.in_picking
#: model:stock.picking.type,name:stock.picking_type_in
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Receipts"
msgstr "입고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Receive From"
msgstr "출고 업체"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__one_step
msgid "Receive and Store (1 step)"
msgstr "입고 및 보관 (1단계)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 1 step (stock)"
msgstr "1단계로 입고 (재고)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 2 steps (input + stock)"
msgstr "2단계 입고 (입고 + 재고)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Receive in 3 steps (input + quality + stock)"
msgstr "3단계 입고 (입고 + 품질 검사 + 재고)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__two_steps
msgid "Receive then Store (2 steps)"
msgstr "입고 후 보관 (2단계)"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_warehouse__reception_steps__three_steps
msgid "Receive, Quality Control, then Store (3 steps)"
msgstr "입고, 품질 관리, 보관 (3단계)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Received Qty"
msgstr "입고 수량"

#. module: stock
#: model:ir.actions.client,name:stock.stock_reception_action
#: model:ir.actions.report,name:stock.stock_reception_report_action
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_reception_report
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report"
msgstr "수취 보고서"

#. module: stock
#: model:ir.actions.report,name:stock.label_picking
msgid "Reception Report Label"
msgstr "수취 보고서 라벨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Reception Report Labels"
msgstr "수취 보고서 라벨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid ""
"Reduce stockouts with alerts, barcode app, replenishment propositions,\n"
"                        locations management traceability, quality control, etc."
msgstr ""
"알림, 바코드 앱, 보충 제안, 위치 관리 추적, 품질 관리 등을 사용하여\n"
"                        재고 부족을 최소화하세요."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_procurement_group__name
#: model:ir.model.fields,field_description:stock.field_stock_move__reference
#: model:ir.model.fields,field_description:stock.field_stock_move_line__reference
#: model:ir.model.fields,field_description:stock.field_stock_picking__name
#: model:ir.model.fields,field_description:stock.field_stock_scrap__name
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
msgid "Reference"
msgstr "참조"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_id
msgid "Reference Sequence"
msgstr "참조 순서"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_picking_name_uniq
msgid "Reference must be unique per company!"
msgstr "회사마다 기준은 유일합니다!"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__origin
msgid "Reference of the document"
msgstr "문서의 참조 번호입니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Reference:"
msgstr "참조 :"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_procurement_group__stock_move_ids
msgid "Related Stock Moves"
msgstr "관련 재고이동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Relocate"
msgstr "이전"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "Relocate your stock"
msgstr "재고 이전하기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Remaining parts of picking partially processed"
msgstr "부분적으로 처리된 선별의 나머지 부분"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_removal
msgid "Removal"
msgstr "재고 삭제"

#. module: stock
#: model:ir.model,name:stock.model_product_removal
#: model:ir.model.fields,field_description:stock.field_stock_location__removal_strategy_id
msgid "Removal Strategy"
msgstr "재고 삭제 방법"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Removal strategy %s not implemented."
msgstr "제거 전략 %s가 구현되지 않았습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid ""
"Remove manually entered value and replace by the quantity to order based on "
"the forecasted quantities"
msgstr "수동으로 입력한 값을 제거하고 예상 수량을 기준으로 주문할 수량으로 대체합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_max_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_max_qty
msgid "Reordering Max Qty"
msgstr "재주문 최대 수량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__reordering_min_qty
#: model:ir.model.fields,field_description:stock.field_product_template__reordering_min_qty
msgid "Reordering Min Qty"
msgstr "재주문 최소 수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rule"
msgstr "재주문 규칙"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_orderpoint
#: model:ir.model.fields,field_description:stock.field_product_product__nbr_reordering_rules
#: model:ir.model.fields,field_description:stock.field_product_template__nbr_reordering_rules
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_form
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Reordering Rules"
msgstr "재주문 규칙"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Reordering Rules Search"
msgstr "재주문 규칙 검색"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Replenish"
msgstr "보충하기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__replenish_location
msgid "Replenish Location"
msgstr "보충 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__should_replenish
msgid "Replenish Quantities"
msgstr "수량 보충"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:stock.route,name:stock.route_warehouse0_mto
msgid "Replenish on Order (MTO)"
msgstr "주문 보충 (MTO)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "Replenish wizard"
msgstr "보충 마법사"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_replenish
#: model:ir.actions.server,name:stock.action_replenishment
#: model:ir.ui.menu,name:stock.menu_reordering_rules_replenish
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Replenishment"
msgstr "재보충"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__replenishment_info_id
msgid "Replenishment Info"
msgstr "재보충 정보"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_replenishment_info
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Replenishment Information"
msgstr "재보충 정보"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Information for %(product)s in %(warehouse)s"
msgstr "%(product)s 중 %(warehouse)s에 위치한 품목에 대한 보충 정보 "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "Replenishment Report"
msgstr "재보충 보고서"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Replenishment Report Search"
msgstr "재보충 보고서 검색"

#. module: stock
#: model:ir.model,name:stock.model_ir_actions_report
msgid "Report Action"
msgstr "보고서 작업"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/multi_print.js:0
msgid "Report Printing Error"
msgstr "인쇄 오류 신고"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_report
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "Reporting"
msgstr "보고"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_request_count
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Request a Count"
msgstr "실사 요청"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Request your vendors to deliver to your customers"
msgstr "품목을 공급업체로부터 고객에게 직접 배달하도록 요청합니다."

#. module: stock
#: model:res.groups,name:stock.group_stock_sign_delivery
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Require a signature on your delivery orders"
msgstr "배송주문서에 서명 확인을 추가합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__reservation_method
msgid "Reservation Method"
msgstr "예약 방법"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Reservations"
msgstr "예약"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Reserve"
msgstr "예약"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__full
msgid "Reserve Only Full Packagings"
msgstr "전체 패키지 예약"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__packaging_reserve_method
msgid ""
"Reserve Only Full Packagings: will not reserve partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then only 1000 will be reserved\n"
"Reserve Partial Packagings: allow reserving partial packagings. If customer orders 2 pallets of 1000 units each and you only have 1600 in stock, then 1600 will be reserved"
msgstr ""
"전체 패키지 예약: 패키지는 부분적으로 예약되지 않습니다. 고객이 각 1000개 단위인 팔레트 2개를 주문했으나 재고가 1600개만 있는 경우 1000개만 예약됩니다. \n"
"부분 패키지 예약: 패키지를 부분적으로 예약할 수 있습니다. 고객이 각 1000개 단위인 팔레트 2개를 주문했으나 재고가 1600개만 있는 경우 1600개만 예약됩니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__packaging_reserve_method
msgid "Reserve Packagings"
msgstr "포장 예약"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_category__packaging_reserve_method__partial
msgid "Reserve Partial Packagings"
msgstr "부분 패키지 예약"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_package_level__state__assigned
msgid "Reserved"
msgstr "예약됨"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_packaging_qty
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_packaging_qty
msgid "Reserved Packaging Quantity"
msgstr "예약된 포장 수량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant__reserved_quantity
msgid "Reserved Quantity"
msgstr "예약된 수량"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "Reserving a negative quantity is not allowed."
msgstr "수량을 음수로 예약하는 것은 허용되지 않습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__responsible_id
#: model:ir.model.fields,field_description:stock.field_product_template__responsible_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__user_id
msgid "Responsible"
msgstr "담당자"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__activity_user_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Resupply"
msgstr "재공급"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_wh_ids
msgid "Resupply From"
msgstr "재공급처"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__resupply_route_ids
msgid "Resupply Routes"
msgstr "재공급 경로"

#. module: stock
#: model:ir.actions.act_window,name:stock.act_stock_return_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return"
msgstr "반납"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return All"
msgstr ""

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking
msgid "Return Picking"
msgstr "반품 선별"

#. module: stock
#: model:ir.model,name:stock.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "반품 선별 내역"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Return Slip"
msgstr "반품 명세서"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_return_picking_form
msgid "Return for Exchange"
msgstr "교환 반품"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_id
msgid "Return of"
msgstr "반품"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Return of %(picking_name)s"
msgstr "%(picking_name)s 반품"

#. module: stock
#: model:ir.actions.report,name:stock.return_label_report
msgid "Return slip"
msgstr "반품 명세서"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "Returned Picking"
msgstr "반품된 선별"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields,field_description:stock.field_stock_picking__return_ids
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Returns"
msgstr "반품"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Returns Type"
msgstr "반품 유형"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_quant_package__package_use__reusable
msgid "Reusable Box"
msgstr "재사용 박스"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__package_use
msgid ""
"Reusable boxes are used for batch picking and emptied afterwards to be reused. In the barcode application, scanning a reusable box will add the products in this box.\n"
"        Disposable boxes aren't reused, when scanning a disposable box in the barcode application, the contained products are added to the transfer."
msgstr ""
"재사용 가능한 상자는 일괄 피킹에 사용되며 나중에 비워 재사용할 수 있습니다. 바코드 앱에서 재사용 가능한 상자를 스캔하면 이 상자에 제품이 추가됩니다.\n"
"        일회용 상자는 재사용되지 않습니다. 바코드 앱에서 일회용 상자를 스캔하면 포함된 제품이 이동에 추가됩니다."

#. module: stock
#: model:ir.actions.server,name:stock.action_revert_inventory_adjustment
msgid "Revert Inventory Adjustment"
msgstr "재고 조정 되돌리기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__route_id
#: model:ir.model.fields,field_description:stock.field_stock_route__name
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__route_id
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Route"
msgstr "경로"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_company_id
msgid "Route Company"
msgstr "라우팅 회사"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__route_sequence
msgid "Route Sequence"
msgstr "경로 순서"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_routes_form
#: model:ir.actions.server,name:stock.action_open_routes
#: model:ir.model.fields,field_description:stock.field_product_category__route_ids
#: model:ir.model.fields,field_description:stock.field_product_packaging__route_ids
#: model:ir.model.fields,field_description:stock.field_product_product__route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__route_ids
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__route_ids
#: model:ir.ui.menu,name:stock.menu_routes_config
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_tree
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Routes"
msgstr "경로"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__has_available_route_ids
#: model:ir.model.fields,field_description:stock.field_product_template__has_available_route_ids
msgid "Routes can be selected on this product"
msgstr "이 품목에 대한 이동경로를 선택할 수 있습니다"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_wh_ids
msgid ""
"Routes will be created automatically to resupply this warehouse from the "
"warehouses ticked"
msgstr "체크된 창고에서 이 창고로 재공급하기 위한 경로가 자동으로 생성됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_replenishment_info__warehouseinfo_ids
#: model:ir.model.fields,help:stock.field_stock_warehouse__resupply_route_ids
msgid ""
"Routes will be created for these resupply warehouses and you can select them"
" on products and product categories"
msgstr "이러한 재공급 창고에 대한 경로가 생성되며 품목 및 품목 카테고리를 선택할 수 있습니다"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"Rule %(rule)s belongs to %(rule_company)s while the route belongs to "
"%(route_company)s."
msgstr "%(rule)s 규칙은 %(rule_company)s에 속하고 경로는 %(route_company)s에 포함됩니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__rule_message
msgid "Rule Message"
msgstr "규칙 메시지"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_rules_form
#: model:ir.model.fields,field_description:stock.field_stock_route__rule_ids
#: model:ir.ui.menu,name:stock.menu_action_rules_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_tree
msgid "Rules"
msgstr "규칙"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Categories"
msgstr "범주 규칙"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_putaway_search
msgid "Rules on Products"
msgstr "품목 규칙"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__rule_ids
msgid "Rules used"
msgstr "사용된 규칙"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "S0001"
msgstr "S0001"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_sms
msgid "SMS Confirmation"
msgstr "SMS 확인"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_picking__message_has_sms_error
#: model:ir.model.fields,field_description:stock.field_stock_scrap__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC Demo"
msgstr "SSCC 데모"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode_small
msgid "SSCC:"
msgstr "SSCC:"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "STANDARD TERMS AND CONDITIONS OF SALE"
msgstr "표준 판매 조건"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Sales History"
msgstr "판매 이력"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sample data"
msgstr "샘플 데이터"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__date_planned
#: model:ir.model.fields,field_description:stock.field_stock_move_line__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_picking__scheduled_date
#: model:ir.model.fields,field_description:stock.field_stock_quant__inventory_date
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Scheduled Date"
msgstr "예정일"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__date
#: model:ir.model.fields,help:stock.field_stock_move_line__scheduled_date
msgid "Scheduled date until move is done, then date of actual move processing"
msgstr "이동이 완료될 때까지의 예정일 후, 실제 이동 처리일"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Scheduled or processing date"
msgstr "계획되거나 또는 처리중인 날짜"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__scheduled_date
msgid ""
"Scheduled time for the first part of the shipment to be processed. Setting "
"manually a value here would set it as expected date for all the stock moves."
msgstr "첫 입고 품목이 처리되도록 예정된 날짜입니다. 여기에 수동으로 값을 설정하면 모든 재고 이동에 대한 예상 날짜로 설정됩니다."

#. module: stock
#: model:ir.actions.server,name:stock.action_scrap
#: model:ir.model,name:stock.model_stock_scrap
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__scrap_id
#: model:ir.ui.menu,name:stock.menu_stock_scrap
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap"
msgstr "폐기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_location_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Scrap Location"
msgstr "폐기 공간"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_scrap
msgid "Scrap Orders"
msgstr "폐기 지시서"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view2
msgid "Scrap Products"
msgstr "품목 폐기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_scrap__scrap_reason_tag_ids
msgid "Scrap Reason"
msgstr "폐기 사유"

#. module: stock
#: model:ir.model,name:stock.model_stock_scrap_reason_tag
msgid "Scrap Reason Tag"
msgstr "폐기 사유 태그"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrap_id
msgid "Scrap operation"
msgstr "폐기 작업"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid "Scrap products"
msgstr "품목 폐기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__scrapped
msgid "Scrapped"
msgstr "폐기됨"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_stock_scrap
msgid ""
"Scrapping a product will remove it from your stock. The product will\n"
"                end up in a scrap location that can be used for reporting purpose."
msgstr ""
"품목을 폐기하면 재고에서 해당 품목이 제거됩니다. 폐기된 품목은 보고 목적으로 \n"
"사용하기 위해 폐기 공간으로 이동됩니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Scraps"
msgstr "폐기물"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
msgid "Search Procurement"
msgstr "조달 검색"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
msgid "Search Scrap"
msgstr "폐기 검색"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_picking.py:0
msgid "Search not supported without a value."
msgstr "값이 없으면 검색이 지원되지 않습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Select Route"
msgstr "경로 선택"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "Select the places where this route can be selected"
msgstr "이 경로를 적용할 수 있는 항목 선택"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
msgid ""
"Selected storage category does not exist in the 'store to' location or any "
"of its sublocations"
msgstr "선택한 저장소 카테고리가 '저장 위치' 또는 하위 위치에 존재하지 않습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__picking_warn
#: model:ir.model.fields,help:stock.field_res_users__picking_warn
msgid ""
"Selecting the \"Warning\" option will notify user with the message, "
"Selecting \"Blocking Message\" will throw an exception with the message and "
"block the flow. The Message has to be written in the next field."
msgstr ""
"\"경고\" 옵션을 선택하면 사용자들이 메시지를 받을 수 있습니다. \"차단 메시지\"을 선택하면, 예외가 발생하고 메시지 흐름이 "
"차단됩니다. 메시지는 다음 필드에 작성되어야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Selection not supported."
msgstr "지원되지 않는 선택"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr "품목의 판매 단위와 구매 단위를 다르게 설정합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Send an automatic confirmation SMS Text Message when Delivery Orders are "
"done"
msgstr "배송 주문이 완료되면 자동으로 확인 SMS 문자 메시지를 전송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Send an automatic confirmation email when Delivery Orders are done"
msgstr "배송 주문이 완료되면 자동으로 확인 이메일을 전송합니다."

#. module: stock
#: model:ir.actions.act_window,name:stock.action_lead_mass_mail
msgid "Send email"
msgstr "이메일 전송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr "Sendcloud 배송"

#. module: stock
#: model:mail.template,description:stock.mail_template_data_delivery_confirmation
msgid ""
"Sent to the customers when orders are delivered, if the setting is enabled"
msgstr "설정하실 경우 주문이 배송될 경우 고객에게 전송합니다"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__barcode_separator
msgid "Separator"
msgstr "구분선"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__res_company__annual_inventory_month__9
msgid "September"
msgstr "9월"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__sequence
#: model:ir.model.fields,field_description:stock.field_stock_package_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence
#: model:ir.model.fields,field_description:stock.field_stock_route__sequence
#: model:ir.model.fields,field_description:stock.field_stock_rule__sequence
#: model:ir.model.fields,field_description:stock.field_stock_scrap_reason_tag__sequence
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__sequence
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
msgid "Sequence"
msgstr "순서"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Sequence %(code)s"
msgstr "시퀀스 %(code)s"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__sequence_code
msgid "Sequence Prefix"
msgstr "순서 접두사"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__lot_ids
msgid "Serial Numbers"
msgstr "일련번호"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"Serial number (%(serial_number)s) already exists in location(s): "
"%(location_list)s. Please correct the serial number encoded."
msgstr ""
"일련번호 (%(serial_number)s)가 이미 다음 위치에 존재합니다: %(location_list)s. 인코딩된 일련번호를 "
"수정하세요."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Please correct this to prevent inconsistent data."
msgstr ""
"일련번호 (%(serial_number)s)가 %(source_location)s 위치가 아닌 다음 위치에 존재합니다: %(other_locations)s.\n"
"\n"
"정보 오류를 방지하기 위해 이 문제를 수정하세요."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"Serial number (%(serial_number)s) is not located in %(source_location)s, but is located in location(s): %(other_locations)s.\n"
"\n"
"Source location for this move will be changed to %(recommended_location)s"
msgstr ""
"일련번호 (%(serial_number)s)가 %(source_location)s 위치가 아닌 다음 위치에 존재합니다: %(other_locations)s.\n"
"\n"
"이동 내역의 원 위치가 다음으로 변경됩니다: %(recommended_location)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Serial numbers"
msgstr "일련번호"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "Set"
msgstr "배치"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_request_count__set_count__set
msgid "Set Current Value"
msgstr "현재값 설정"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set Warehouse Routes"
msgstr "창고 경로 설정"

#. module: stock
#: model:ir.model.fields,help:stock.field_product_category__removal_strategy_id
msgid ""
"Set a specific removal strategy that will be used regardless of the source location for this product category.\n"
"\n"
"FIFO: products/lots that were stocked first will be moved out first.\n"
"LIFO: products/lots that were stocked last will be moved out first.\n"
"Closest location: products/lots closest to the target location will be moved out first.\n"
"FEFO: products/lots with the closest removal date will be moved out first (the availability of this method depends on the \"Expiration Dates\" setting).\n"
"Least Packages: FIFO but with the least number of packages possible when there are several packages containing the same product."
msgstr ""
"모든 품목 카테고리에 적용할 수 있는 표준화된 제거 전략을 수립합니다:\n"
"\n"
"FIFO: 가장 먼저 입고된 제품/로트부터 우선적으로 출고됩니다.\n"
"LIFO: 가장 최근에 입고된 제품/로트가 우선적으로 출고됩니다.\n"
"가장 가까운 위치: 대상 위치에서 가장 가까운 곳에 위치한 제품/로트가 우선적으로 출고됩니다.\n"
"FEFO: 만료일이 가장 가까운 제품/로트가 우선적으로 출고되며, '만료일' 설정이 활성화된 경우에 한하여 적용됩니다.\n"
"최소 패키지: FIFO를 활용하되 여러 패키지에 동일한 제품이 포함된 경우 단위 수가 가장 적은 패키지의 우선순위를 지정합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set expiration dates on lots & serial numbers"
msgstr "LOT 및 일련번호에 유통기한을 설정합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set owner on stored products"
msgstr "보관 중인 품목의 소유자를 설정합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Set product attributes (e.g. color, size) to manage variants"
msgstr "세부선택사항을 위해 품목 속성 (예. 색상, 크기)을 설정합니다."

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_to_zero_quants_tree
msgid "Set to 0"
msgstr "0으로 설정"

#. module: stock
#: model:ir.actions.server,name:stock.action_view_set_quants_tree
msgid "Set to quantity on hand"
msgstr "보유 수량을 설정"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_config_settings
#: model:ir.ui.menu,name:stock.menu_stock_general_settings
msgid "Settings"
msgstr "설정"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
msgid "Shelf A"
msgstr "선반 A"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__posy
msgid "Shelves (Y)"
msgstr "행(Y)"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_lot_report
msgid "Ship a lot to a customer."
msgstr "고객에게 로트를 배송합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Shipments"
msgstr "선적"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping"
msgstr "배송"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Shipping Connectors"
msgstr "배송업체"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_type
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__move_type
msgid "Shipping Policy"
msgstr "배송 정책"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_quant_package__shipping_weight
msgid "Shipping Weight"
msgstr "선적 중량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Shipping connectors allow to compute accurate shipping costs, print shipping"
" labels and request carrier picking at your warehouse to ship to the "
"customer. Apply shipping connector from delivery methods."
msgstr ""
"배송 커넥터를 사용하면 정확한 운송 비용을 계산하고 배송 라벨를 인쇄하여 창고에서 운송 업체 선택을 요청한 뒤 고객에게 배송 할 수 "
"있습니다. 배송 방법에서 배송 커넥터를 적용하십시오."

#. module: stock
#: model:mail.template,name:stock.mail_template_data_delivery_confirmation
msgid "Shipping: Send by Email"
msgstr "배송: 이메일로 전송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_shiprocket
msgid "Shiprocket Connector"
msgstr "Shiprocket 배송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__code
msgid "Short Name"
msgstr "약식 이름"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__code
msgid "Short name used to identify your warehouse"
msgstr "창고를 식별하는데 사용되는 약식 이름"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_allocation
msgid "Show Allocation"
msgstr "할당 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_check_availability
msgid "Show Check Availability"
msgstr "사용 가능 여부 확인"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_operations
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_operations
msgid "Show Detailed Operations"
msgstr "세부 작업 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_forecasted_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_forecasted_qty_status_button
msgid "Show Forecasted Qty Status Button"
msgstr "예상 수량 상태 버튼 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_m2o
msgid "Show Lots M2O"
msgstr "LOT M2O 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_level__show_lots_text
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_lots_text
msgid "Show Lots Text"
msgstr "LOT 텍스트 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__show_next_pickings
msgid "Show Next Pickings"
msgstr "다음 피킹 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__show_on_hand_qty_status_button
#: model:ir.model.fields,field_description:stock.field_product_template__show_on_hand_qty_status_button
msgid "Show On Hand Qty Status Button"
msgstr "보유 수량 상태 버튼 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__is_favorite
msgid "Show Operation in Overview"
msgstr "현황에서 작업 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__show_picking_type
msgid "Show Picking Type"
msgstr "픽업 유형 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_quant
msgid "Show Quant"
msgstr "수량 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__auto_show_reception_report
msgid "Show Reception Report at Validation"
msgstr "승인 시 수취 보고서 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation__show_transfers
msgid "Show Transfers"
msgstr "전송 표시"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Show all records which has next action date is before today"
msgstr "다음 행동 날짜가 오늘 이전 인 모든 기록보기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_m2o
msgid "Show lot_id"
msgstr "lot_id 표시"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__show_lots_text
msgid "Show lot_name"
msgstr "lot_name 표시"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rules_report__warehouse_ids
msgid "Show the routes that apply on selected warehouses."
msgstr "선택한 창고에 적용되는 경로를 보여줍니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Sign"
msgstr "서명"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_sign_delivery
#: model:ir.model.fields,field_description:stock.field_stock_picking__signature
#: model:ir.model.fields,help:stock.field_stock_picking__signature
msgid "Signature"
msgstr "서명"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Signed"
msgstr "서명됨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size"
msgstr "사이즈"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Size: Length × Width × Height"
msgstr "사이즈: 길이 × 너비 × 높이"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/stock_orderpoint_list_view.xml:0
#: model:ir.actions.act_window,name:stock.action_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_stock_orderpoint_snooze
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "Snooze"
msgstr "다시 알림"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__snoozed_until
msgid "Snooze Date"
msgstr "다시 알림 날짜"

#. module: stock
#: model:ir.model,name:stock.model_stock_orderpoint_snooze
msgid "Snooze Orderpoint"
msgstr "주문 시점 다시 알림"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_orderpoint_snooze__predefined_date
msgid "Snooze for"
msgstr "다음 후 다시 알림"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__snoozed_until
msgid "Snoozed"
msgstr "다시 알림"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_set_view
msgid "Some selected lines already have quantities set, they will be ignored."
msgstr "일부 선택 항목에 이미 설정된 수량이 있으므로, 해당 내용을 적용할 수 없습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__origin
msgid "Source"
msgstr "원본"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__origin
#: model:ir.model.fields,field_description:stock.field_stock_picking__origin
#: model:ir.model.fields,field_description:stock.field_stock_scrap__origin
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Source Document"
msgstr "원본 문서"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
#: model:ir.model.fields,field_description:stock.field_stock_move__location_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking__location_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__default_location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__location_src_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__location_id
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Source Location"
msgstr "기존 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__location_usage
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_usage
msgid "Source Location Type"
msgstr "기존 위치 유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Location:"
msgstr "기존 위치 :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_reception_report_label
msgid "Source Name"
msgstr "출처명"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__package_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
msgid "Source Package"
msgstr "원본 패키지"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.message_body
msgid "Source Package:"
msgstr "원 패키지:"

#. module: stock
#: model:ir.actions.server,name:stock.stock_split_picking
msgid "Split"
msgstr "분할"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Starred"
msgstr "별표"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Starred Products"
msgstr "별표 표시된 품목"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_starshipit
msgid "Starshipit Connector"
msgstr "Starshipit 커넥터"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__state
#: model:ir.model.fields,field_description:stock.field_stock_package_level__state
msgid "State"
msgstr "시/도"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__state
#: model:ir.model.fields,field_description:stock.field_stock_move_line__state
#: model:ir.model.fields,field_description:stock.field_stock_picking__state
#: model:ir.model.fields,field_description:stock.field_stock_scrap__state
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Status"
msgstr "상태"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_state
#: model:ir.model.fields,help:stock.field_stock_picking__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동 기준 상태\n"
"기한 초과: 기한이 이미 지났습니다.\n"
"오늘: 활동 날짜가 오늘입니다.\n"
"예정: 향후 계획된 활동입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.actions.act_window,name:stock.action_product_stock_view
#: model:ir.ui.menu,name:stock.menu_product_stock
msgid "Stock"
msgstr "재고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_stock_barcode_barcodelookup
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Stock Barcode Database"
msgstr "재고 바코드 데이터베이스"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Stock In Transit"
msgstr "운송 중인 재고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
#: model_terms:ir.ui.view,arch_db:stock.view_location_tree2
msgid "Stock Location"
msgstr "재고 공간"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Stock Locations"
msgstr "재고 공간"

#. module: stock
#: model:ir.model,name:stock.model_stock_move
#: model:ir.model.fields,field_description:stock.field_product_product__stock_move_ids
msgid "Stock Move"
msgstr "재고 이동"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
msgid "Stock Moves"
msgstr "재고 이동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_graph
#: model_terms:ir.ui.view,arch_db:stock.view_move_pivot
msgid "Stock Moves Analysis"
msgstr "재고 이동 분석"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__move_id
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
msgid "Stock Operation"
msgstr "재고 작업"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_destination
msgid "Stock Package Destination"
msgstr "재고 꾸러미 상품 목적지"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_level
msgid "Stock Package Level"
msgstr "재고 꾸러미 상품 단계"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__picking_warn
#: model:ir.model.fields,field_description:stock.field_res_users__picking_warn
msgid "Stock Picking"
msgstr "재고 선별"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__stock_quant_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_view_graph
msgid "Stock Quant"
msgstr "재고량"

#. module: stock
#: model:ir.model,name:stock.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "재고 수량 이력"

#. module: stock
#: model:ir.model,name:stock.model_stock_quant_relocate
msgid "Stock Quantity Relocation"
msgstr "재고 수량 이전"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_quantity
msgid "Stock Quantity Report"
msgstr "재고 수량 보고서"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_reception
msgid "Stock Reception Report"
msgstr "재고 수취 보고서"

#. module: stock
#: model:ir.model,name:stock.model_stock_forecasted_product_product
#: model:ir.model,name:stock.model_stock_forecasted_product_template
msgid "Stock Replenishment Report"
msgstr "재고 재보충 보고서"

#. module: stock
#: model:ir.model,name:stock.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "재고 실사 요청 재고"

#. module: stock
#: model:ir.model,name:stock.model_stock_rule
#: model:ir.model.fields,field_description:stock.field_stock_move__rule_id
msgid "Stock Rule"
msgstr "재고 규칙"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_stock_rules_report
msgid "Stock Rules Report"
msgstr "재고 규칙 보고서"

#. module: stock
#: model:ir.model,name:stock.model_stock_rules_report
msgid "Stock Rules report"
msgstr "재고 규칙 보고서"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_confirmation
msgid "Stock Track Confirmation"
msgstr "재고 추적 확인"

#. module: stock
#: model:ir.model,name:stock.model_stock_track_line
msgid "Stock Track Line"
msgstr "재고 추적 내역"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__move_ids_without_package
msgid "Stock move"
msgstr "재고 이동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Available (Ready to process)"
msgstr "사용 가능한 재고 이동 (처리 준비)"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that are Confirmed, Available or Waiting"
msgstr "확인 됨, 사용 가능 또는 대기 중 재고 이동"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
msgid "Stock moves that have been processed"
msgstr "처리된 재고 이동"

#. module: stock
#: model:ir.model,name:stock.model_stock_package_type
msgid "Stock package type"
msgstr "재고 포장 유형"

#. module: stock
#: model:ir.model,name:stock.model_report_stock_report_stock_rule
msgid "Stock rule report"
msgstr "재고 규칙 보고서"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_info
msgid "Stock supplier replenishment information"
msgstr "재고 공급업체 보충 정보"

#. module: stock
#: model:ir.model,name:stock.model_stock_replenishment_option
msgid "Stock warehouse replenishment option"
msgstr "재고 창고 보충 옵션"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Storage"
msgstr "저장 공간"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
msgid "Storage Capacities"
msgstr "보관 용량"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category
#: model:ir.ui.menu,name:stock.menu_storage_categoty_config
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_tree
msgid "Storage Categories"
msgstr "보관 카테고리"

#. module: stock
#: model:ir.model,name:stock.model_stock_storage_category
#: model:ir.model.fields,field_description:stock.field_stock_location__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__storage_category_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__name
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__storage_category_id
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_form
msgid "Storage Category"
msgstr "보관 카테고리"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_storage_category_capacity
#: model:ir.model,name:stock.model_stock_storage_category_capacity
#: model:ir.model.fields,field_description:stock.field_product_product__storage_category_capacity_ids
#: model:ir.model.fields,field_description:stock.field_stock_package_type__storage_category_capacity_ids
#: model_terms:ir.ui.view,arch_db:stock.stock_storage_category_capacity_tree
msgid "Storage Category Capacity"
msgstr "보관 카테고리 용량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_stock_multi_locations
msgid "Storage Locations"
msgstr "저장 공간"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__store_type_id
msgid "Storage Type"
msgstr "저장 유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Store To"
msgstr "저장 위치"

#. module: stock
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid ""
"Store and retrieve information regarding every Lot/Serial Number (condition,"
" product info, ...)."
msgstr "모든 로트번호/일련번호 (상태, 품목 정보 등)에 대한 정보를 저장하고 검색합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__group_stock_multi_locations
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Store products in specific locations of your warehouse (e.g. bins, racks) "
"and to track inventory accordingly."
msgstr "창고의 특정 위치 (예. 창고 보관함, 선반)에 품목을 보관하고 재고의 보관 위치를 추적할 수 있습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "Store to"
msgstr "저장 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_out_id
msgid "Store to sublocation"
msgstr "하위 위치에 저장"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__sublocation
msgid "Sublocation"
msgstr "하위 위치"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__supplied_wh_id
msgid "Supplied Warehouse"
msgstr "공급된 창고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__procure_method
#: model:ir.model.fields,field_description:stock.field_stock_rule__procure_method
msgid "Supply Method"
msgstr "공급 방법"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_route__supplier_wh_id
msgid "Supplying Warehouse"
msgstr "공급 창고"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_scrap_reason_tag_name_uniq
msgid "Tag name already exists!"
msgstr "태그명이 이미 존재합니다!"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_stock
msgid "Take From Stock"
msgstr "재고에서 가져오기"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__mts_else_mto
msgid "Take From Stock, if unavailable, Trigger Another Rule"
msgstr "재고에서 가져오기가 불가능할 경우 다른 규칙 실행"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__procure_method
msgid ""
"Take From Stock: the products will be taken from the available stock of the source location.\n"
"Trigger Another Rule: the system will try to find a stock rule to bring the products in the source location. The available stock will be ignored.\n"
"Take From Stock, if Unavailable, Trigger Another Rule: the products will be taken from the available stock of the source location.If there is no stock available, the system will try to find a  rule to bring the products in the source location."
msgstr ""
"재고에서 가져오기: 기존 위치에 있는 사용 가능한 재고에서 품목을 이동시켜 재고를 확보합니다.\n"
"다른 규칙 실행: 시스템이 기존 위치로 품목을 가져올 수 있는 재고 규칙을 찾으려고 시도합니다. 가용 재고는 무시됩니다.\n"
"재고에서 가져오기가 불가능할 경우 다른 규칙 실행: 기존 위치에 있는 가용 재고에서 품목을 가져오며, 사용 가능한 재고가 없는 경우에는 시스템에서 제품을 기존 위치에 가져오는 규칙을 찾으려고 합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_allocation
msgid ""
"Technical Field used to decide whether the button \"Allocation\" should be "
"displayed."
msgstr "\"배정\" 버튼을 표시할지 결정하는데 사용되는 기술 필드입니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Technical Information"
msgstr "기술적인 정보"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__show_check_availability
msgid ""
"Technical field used to compute whether the button \"Check Availability\" "
"should be displayed."
msgstr "\"가용성 확인\" 버튼을 표시해야 하는지 여부를 계산하는 데 사용되는 기술 필드입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_tmpl_id
msgid "Template"
msgstr "서식"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The %s location is required by the Inventory app and cannot be deleted, but "
"you can archive it."
msgstr "%s 위치는 재고 앱에 꼭 필요한 위치로 삭제할 수 없습니다. 대신 보관할 수 있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__auto
msgid ""
"The 'Manual Operation' value will create a stock move after the current one."
" With 'Automatic No Step Added', the location is replaced in the original "
"move."
msgstr ""
"'수동작업'의 값은 현재의 작업 뒤에 새로운 재고이동을 생성합니다. 'Automatic No Step Added'을 사용하게 되면, "
"공간는 처음 이동에서 대체됩니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "The Lot/Serial number (%s) is linked to another product."
msgstr "로트/일련 번호(%s)가 다른 제품에 연결되어 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The Serial Number (%(serial_number)s) is already used in location(s): %(location_list)s.\n"
"\n"
"Is this expected? For example, this can occur if a delivery operation is validated before its corresponding receipt operation is validated. In this case the issue will be solved automatically once all steps are completed. Otherwise, the serial number should be corrected to prevent inconsistent data."
msgstr ""
"다음 위치에서 일련번호 (%(serial_number)s)를 이미 사용하고 있습니다: %(location_list)s.\n"
"\n"
"확인하시기 바랍니다. 예를 들어, 관련한 수취 작업이 승인되기 전에 배송 작업이 승인되는 경우에 이런 상황이 발생할 수 있습니다. 이 경우 해당 문제는 전체 단계가 종료되면 자동으로 해결됩니다. 그렇지 않으면 일련번호를 수정하여 데이터를 동일하게 유지할 수 있도록 해야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "The backorder %s has been created."
msgstr "%s 이월 주문이 생성되었습니다."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_barcode_company_uniq
msgid "The barcode for a location must be unique per company!"
msgstr "위치용 바코드는 회사별로 다르게 지정해야 합니다!"

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid ""
"The client explicitly waives its own standard terms and conditions, even if "
"these were drawn up after these standard terms and conditions of sale. In "
"order to be valid, any derogation must be expressly agreed to in advance in "
"writing."
msgstr ""
"본 표준 판매 이용약관 이전에 작성되었다 하더라도, 고객의 자체 표준 약관은 명시적으로 철회하기로 합니다. 내용이 효력을 가지려면, 모든"
" 수정 사항은 사전에 명확히 서면 동의가 있어야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"The combination of lot/serial number and product must be unique within a company including when no company is defined.\n"
"The following combinations contain duplicates:\n"
"%(error_lines)s"
msgstr ""
"로트/일련 번호와 제품의 조합은 회사가 정의되지 않은 경우에도 회사 내에서 고유해야 합니다.\n"
"다음 조합에는 중복이 포함되어 있습니다:\n"
"%(error_lines)s"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse__company_id
msgid "The company is automatically set from your user preferences."
msgstr "회사는 사용자 기본 설정에서 자동으로 설정됩니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_2
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "The day after tomorrow"
msgstr "모레"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The deadline has been automatically updated due to a delay on %s."
msgstr "마감일이 %s 지연으로 인하여 자동으로 변경되었습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__delay
msgid ""
"The expected date of the created transfer will be computed based on this "
"lead time."
msgstr "새로 생성된 이동의 예상 배송일은 이 리드타임을 기준으로 계산됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__sequence
msgid "The first in the sequence is the default one."
msgstr "순서의 첫 번째가 기본입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "The following replenishment order have been generated"
msgstr "다음과 같이 보충 주문서가 생성되었습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "The forecasted quantity of"
msgstr "예측 수량"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "The forecasted stock on the"
msgstr "다음"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid "The inter-warehouse transfers have been generated"
msgstr "창고 간 전송 항목이 생성되었습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "The inventory adjustments have been reverted."
msgstr "재고 조정이 이전으로 복귀되었습니다."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_location_inventory_freq_nonneg
msgid "The inventory frequency (days) for a location must be non-negative"
msgstr "해당 위치의 재고 주기 (일)는 음수가 아니어야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"The minimum quantity must be less than or equal to the maximum quantity."
msgstr "최소 수량은 최대 수량보다 적거나 같아야 합니다."

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_name_uniq
msgid "The name of the warehouse must be unique per company!"
msgstr "창고의 이름은 회사마다 고유해야 합니다!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The number of Serial Numbers to generate must be greater than zero."
msgstr "생성할 일련번호는 0보다 큰 숫자여야 합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_final_id
msgid ""
"The operation brings the products to the intermediate location.But this "
"operation is part of a chain of operations targeting the final location."
msgstr "이 작업은 제품을 중간 위치로 이동합니다. 그러나 이 작업은 최종 위치에 도달하기 위한 일련의 작업 중 일부입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_id
msgid "The operation takes and suggests products from this location."
msgstr "이 작업에는 이 위치에서 품목을 가져와 제안하는 작업이 포함됩니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_picking_type_action
msgid ""
"The operation type system allows you to assign each stock\n"
"            operation a specific type which will alter its views accordingly.\n"
"            On the operation type you could e.g. specify if packing is needed by default,\n"
"            if it should show the customer."
msgstr ""
"작업 유형 시스템을 사용하면 각 재고 작업에 특정 유형을 지정하여\n"
"                그에 따라서 보기를 변경할 수 있습니다.\n"
"                작업 유형에서 예를 들어 고객에게 포장이 필요한 경우,\n"
"                포장이 기본적으로 필요한지 여부를 지정하십시오."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__location_dest_id
msgid "The operations brings product to this location"
msgstr "이 작업은 품목을 이 위치로 가져옵니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__package_id
msgid "The package containing this quant"
msgstr "이 퀀트가 포함된 꾸러미 상품"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__location_id
msgid ""
"The parent location that includes this location. Example : The 'Dispatch "
"Zone' is the 'Gate 1' parent location."
msgstr "이 공간(위치)을 포함하는 상위 공간(위치)입니다. 예: '입고 공간(위치)'은 '게이트1'의 상위 공간(위치)입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__qty_multiple
msgid ""
"The procurement quantity will be rounded up to a multiple of this field "
"quantity. If it is 0, it is not rounded."
msgstr "조달 수량은 이 필드 수량의 가장 가까운 배수로 반올림됩니다. 필드 수량이 0이면 반올림되지 않습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "The product is not available in sufficient quantity"
msgstr "품목의 충분한 수량이 없습니다"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__inventory_quantity
msgid "The product's counted quantity."
msgstr "품목의 계산 수량입니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"The quantities selected do not all belong to the same location.\n"
"                    You may not assign them a package without moving them to a common location."
msgstr ""
"선택된 수량이 같은 위치에 속해 있지 않습니다.\n"
"                    같은 위치로 이동시키지 않으면 패키지를 할당할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"The quantity done for the product \"%(product)s\" doesn't respect the "
"rounding precision defined on the unit of measure \"%(unit)s\". Please "
"change the quantity done or the rounding precision of your unit of measure."
msgstr ""
"품목 \"%(product)s\"의 최종 수량에는 기본 단위 \"%(unit)s\"에 정의되어 있는 소수점 자릿수가 반영되지 않습니다. "
"최종 수량 또는 기본 단위의 소수점을 변경해 주세요."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "The quantity per lot should always be a positive value."
msgstr "로트당 수량은 항상 양수여야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"The requested operation cannot be processed because of a programming error "
"setting the `product_qty` field instead of the `product_uom_qty`."
msgstr ""
"'product_uom_qty'대신 'product_qty` 필드를 설정하는 프로그래밍 오류 때문에 요청한 작업을 처리 할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The search does not support the %(operator)s operator or %(value)s value."
msgstr "검색에서는 %(operator)s 연산자나 %(value)s 값이 지원되지 않습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"The selected Inventory Frequency (Days) creates a date too far into the "
"future."
msgstr "선택한 재고 주기 (일)의 생성 날짜가 너무 나중의 날짜입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"The serial number has already been assigned: \n"
" Product: %(product)s, Serial Number: %(serial_number)s"
msgstr ""
"일련번호가 이미 할당되었습니다: \n"
" 품목: %(product)s, 일련번호: %(serial_number)s"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_warehouse_warehouse_code_uniq
msgid "The short name of the warehouse must be unique per company!"
msgstr "창고의 약식 이름은 회사별로 다르게 지정해야 합니다!"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_customer
#: model:ir.model.fields,help:stock.field_res_users__property_stock_customer
msgid ""
"The stock location used as destination when sending goods to this contact."
msgstr "이 담당자에게 상품을 보낼 때 대상으로 사용되는 재고 위치입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,help:stock.field_res_users__property_stock_supplier
msgid ""
"The stock location used as source when receiving goods from this contact."
msgstr "이 담당자로부터 상품을 받을 때 소스로 사용된 재고 위치입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__picking_id
msgid "The stock operation where the packing has been made"
msgstr "포장이 이루어진 재고 작업"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__rule_id
msgid "The stock rule that created this stock move"
msgstr "이 재고 이동을 생성한 재고 규칙"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_warehouse_id
msgid ""
"The warehouse to propagate on the created move/procurement, which can be "
"different of the warehouse this rule is for (e.g for resupplying rules from "
"another warehouse)"
msgstr ""
"생성 된 이동 / 조달에 전파 할 창고.이 규칙은 다른 창고의 규칙 재 공급을위한 것입니다 (예를 들어, 다른 창고로부터의 재 공급 "
"규칙)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "There are no inventory adjustments to revert."
msgstr "되돌리기를 할 제고 조정 항목이 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"There is nothing eligible to put in a pack. Either there are no quantities "
"to put in a pack or all products are already in a pack."
msgstr "포장에 넣을 수 있는 상품이 없습니다. 수량이 부족하거나 모든 품목이 이미 포장되어 있습니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid "There's no product move yet"
msgstr "품목 이동내역이 아직 없습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.duplicated_sn_warning
msgid "This SN is already in another location."
msgstr "이 일련번호는 이미 다른 위치에 있습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"This adds a dropshipping route to apply on products in order to request your"
" vendors to deliver to your customers. A product to dropship will generate a"
" purchase request for quotation once the sales order confirmed. This is a "
"on-demand flow. The requested delivery address will be the customer delivery"
" address and not your warehouse."
msgstr ""
"이는 공급 업체가 고객에게 제공하도록 요청하기 위해 제품에 적용 할 수있는 운송 방법을 추가합니다. 직배송할 상품은 판매 주문이 확인되면"
" 견적 요청 구매를 생성합니다. 이것은 주문형 흐름입니다. 요청된 배송 주소는 창고가 아닌 고객 배송 주소입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"This analysis gives you an overview of the current stock level of your "
"products."
msgstr "이 분석을 통해 품목의 현재 재고 수준에 대한 전반적인 내용을 확인할 수 있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__picked
msgid ""
"This checkbox is just indicative, it doesn't validate or generate any "
"product moves."
msgstr "이 확인란은 단순 표시용으로, 제품 이동의 유효성을 검사하거나 생성하지 않습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__name
msgid "This field will fill the packing origin and the name of its moves"
msgstr "이 필드는 이동 이름과 포장 출처를 채웁니다"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_dest_id
msgid ""
"This is the default destination location when this operation is manually "
"created. However, it is possible to change it afterwards or that the routes "
"use another one by default."
msgstr ""
"이 작업을 수동으로 생성할 때 설정되는 기본 목적지 위치입니다. 나중에 변경하거나 경로가 기본적으로 다른 위치를 사용하도록 수정할 수 "
"있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__default_location_src_id
msgid ""
"This is the default source location when this operation is manually created."
" However, it is possible to change it afterwards or that the routes use "
"another one by default."
msgstr ""
"이 작업을 수동으로 생성할 때 설정되는 기본 원 위치입니다. 나중에 변경하거나 경로가 기본적으로 다른 위치를 사용하도록 수정할 수 "
"있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__owner_id
msgid "This is the owner of the quant"
msgstr "재고 명세의 소유자입니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__product_uom_qty
msgid ""
"This is the quantity of product that is planned to be moved.Lowering this "
"quantity does not generate a backorder.Changing this quantity on assigned "
"moves affects the product reservation, and should be done with care."
msgstr ""
"이동 예정인 품목의 수량으로, 이 수량을 낮춰도 이월 주문은 생성되지 않습니다. 지정된 이동에서 이 수량을 변경하면 품목 예약에도 영향을"
" 미칠 수 있으므로 신중하게 수행해야 합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_location__child_internal_location_ids
msgid ""
"This location (if it's internal) and all its descendants filtered by "
"type=Internal."
msgstr "이 위치 (내부 위치인 경우) 및 필터가 type=Internal로 설정된 전체 하위 위치입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"This location's usage cannot be changed to view as it contains products."
msgstr "이 공간의 용도는 품목이 포함되어 있으므로 변경할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"This lot %(lot_name)s is incompatible with this product %(product_name)s"
msgstr "이 %(lot_name)s Lot은 이 %(product_name)s품목과 호환되지 않습니다"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "This lot/serial number is already in another location"
msgstr "이 LOT/일련번호는 이미 다른 위치에 있습니다"

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_action
msgid ""
"This menu gives you the full traceability of inventory\n"
"                operations on a specific product. You can filter on the product\n"
"                to see all the past or future movements for the product."
msgstr ""
"이 메뉴는 재고의 전체 추적 가능성을 제공합니다.\n"
"                특정 품목에 대한 작업. 품목에 대한 과거 또는 미래의\n"
"                움직임을 모두 보려면 품목을 필터링 할 수 있습니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.stock_move_line_action
msgid ""
"This menu gives you the full traceability of inventory operations on a specific product.\n"
"                    You can filter on the product to see all the past movements for the product."
msgstr ""
"이 메뉴는 특정 품목에 대한 재고 작업의 전체 추적성을 제공합니다. \n"
"                      품목을 필터링하면 해당 품목의 과거 이동 내역을 모두 확인할 수 있습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "This note is added to delivery orders."
msgstr "이 노트는 배송 주문에 추가됩니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to internal transfer orders (e.g. where to pick the "
"product in the warehouse)."
msgstr "이 노트는 내부 재고 이동 주문 (예: 창고에서 품목을 픽업할 위치)에 추가됩니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid ""
"This note is added to receipt orders (e.g. where to store the product in the"
" warehouse)."
msgstr "이 노트는 입고 주문에 추가됩니다. (예: 창고에 품목을 보관하는 위치)"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product has been used in at least one inventory movement. It is not "
"advised to change the Product Type since it can lead to inconsistencies. A "
"better solution could be to archive the product and create a new one "
"instead."
msgstr ""
"이 품목은 하나 이상의 재고 이동에 사용되었습니다. 불일치가 발생할 수 있으므로 품목 유형을 변경하지 않는 것이 좋습니다. 더 나은 "
"방법은 품목을 보관하고 대신 신규 품목을 만드는 것입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are quantities of "
"it belonging to another company."
msgstr "다른 회사에서 사용 중인 수량이 있는 경우, 이 품목의 회사는 변경할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"This product's company cannot be changed as long as there are stock moves of"
" it belonging to another company."
msgstr "다른 회사에서 재고 이동이 있는 경우, 이 품목의 회사는 변경할 수 없습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_change_product_qty__new_quantity
msgid ""
"This quantity is expressed in the Default Unit of Measure of the product."
msgstr "이 수량은 상품 기본 단위 기준으로 표시됩니다."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid "This record already exists."
msgstr "이 레코드는 이미 존재합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/report/report_stock_reception.py:0
msgid "This report cannot be used for done and not done %s at the same time"
msgstr "이 보고서는 %s 항목을 동시에 완료 및 미완료로 사용할 수 없습니다. "

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"This sequence prefix is already being used by another operation type. It is "
"recommended that you select a unique prefix to avoid issues and/or repeated "
"reference values or assign the existing reference sequence to this operation"
" type."
msgstr ""
"해당 순서 접두어는 이미 다른 작업 유형에서 사용 중입니다. 문제 발생 및 순환 참조 오류을 방지하려면 고유한 접두어를 선택하거나 작업 "
"유형에 기존 참조된 순서를 할당하는 것이 좋습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_production
#: model:ir.model.fields,help:stock.field_product_template__property_stock_production
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated by manufacturing orders."
msgstr "이 재고 위치는 제조 주문서에 의해 생성된 재고 이동의 출고 위치로 기본 재고 장소 대신 사용됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__property_stock_inventory
#: model:ir.model.fields,help:stock.field_product_template__property_stock_inventory
msgid ""
"This stock location will be used, instead of the default one, as the source "
"location for stock moves generated when you do an inventory."
msgstr "이 재고 공간는 제조 주문에 의해 생선 된 재고 이동의 출고 할 공간/이동 전 공간로 기본 재고 공간대신 사용됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_product_product__responsible_id
#: model:ir.model.fields,help:stock.field_product_template__responsible_id
msgid ""
"This user will be responsible of the next activities related to logistic "
"operations for this product."
msgstr "이 사용자는 이 품목의 물류 작업과 관련된 활동을 담당합니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.inventory_warning_reset_view
msgid "This will discard all unapplied counts, do you want to proceed?"
msgstr "이렇게 할 경우, 적용하지 않은 실사 내용은 모두 삭제됩니다. 계속하시겠습니까?"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_track_confirmation
msgid ""
"Those products you added are tracked but lots/serials were not defined. Once applied those can't be changed.<br/>\n"
"                    Apply anyway?"
msgstr ""
"추가한 품목은 추적이 되지만 LOT/일련번호는 설정되지 않았습니다. 일단 적용이 완료되면 변경할 수 없습니다.<br/>\n"
"                    적용하시겠습니까?"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid "Time Horizon"
msgstr "투자 기간"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_1
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_1
msgid "Tip: Monitor Lot details"
msgstr "팁: 로트 세부 정보 모니터링"

#. module: stock
#: model:digest.tip,name:stock.digest_tip_stock_0
#: model_terms:digest.tip,tip_description:stock.digest_tip_stock_0
msgid "Tip: Speed up inventory operations with barcodes"
msgstr "팁: 바코드를 활용하면 재고 작업 속도가 빨라집니다."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_move_line__location_dest_id
#: model:ir.model.fields,field_description:stock.field_stock_package_level__location_dest_id
#: model_terms:ir.ui.view,arch_db:stock.report_picking
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "To"
msgstr "목적지 위치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Apply"
msgstr "적용하기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__to_backorder
msgid "To Backorder"
msgstr "이월 주문으로"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "To Count"
msgstr "계산하기"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Deliver"
msgstr "배송"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_graph
#: model:ir.actions.act_window,name:stock.action_picking_tree_ready
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_move_search
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "To Do"
msgstr "To Do"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Location"
msgstr "위치로"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__qty_to_order
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order
msgid "To Order"
msgstr "주문 대기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_computed
msgid "To Order Computed"
msgstr "계산된 주문"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__qty_to_order_manual
msgid "To Order Manual"
msgstr "직접 주문"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "To Order with Visibility Days"
msgstr "가시성 일수로 주문"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid "To Package"
msgstr "패키지로"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Process"
msgstr "진행 대기"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "To Receive"
msgstr "입고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "To Reorder"
msgstr "재주문 대기"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__today
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today"
msgstr "오늘"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Today Activities"
msgstr "오늘 활동"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__day_1
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Tomorrow"
msgstr "내일"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Available"
msgstr "사용 가능한 총계"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Demand"
msgstr "총 수요"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Forecasted"
msgstr "총 예측치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Free to Use"
msgstr "총 일반 사용 가능"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Incoming"
msgstr "총 입고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total On Hand"
msgstr "총 보유"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
msgid "Total Outgoing"
msgstr "총 출고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
msgid "Total Quantity"
msgstr "전체 수량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
msgid "Total Reserved"
msgstr "총 예약"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__total_route_ids
msgid "Total routes"
msgstr "전체 경로"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__shipping_weight
msgid ""
"Total weight of packages and products not in a package. Packages with no "
"shipping weight specified will default to their products' total weight. This"
" is the weight used to compute the cost of the shipping."
msgstr ""
"패키지 및 패키지 외 품목의 총 중량입니다. 패키지에 선적 중량이 지정되지 않은 경우에는 품목의 총 중량이 기본값으로 설정됩니다. 해당 "
"중량은 배송 비용을 계산하는데 사용합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__weight_bulk
msgid "Total weight of products which are not in a package."
msgstr "패키지에 포함되어 있지 않은 품목의 총 중량"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant_package__shipping_weight
msgid "Total weight of the package."
msgstr "패키지의 총 중량"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "Traceability"
msgstr "추적"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.js:0
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
#: model:ir.actions.client,name:stock.action_stock_report
#: model:ir.model,name:stock.model_stock_traceability_report
#: model_terms:ir.ui.view,arch_db:stock.report_stock_body_print
msgid "Traceability Report"
msgstr "추적 보고서"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__is_storable
#: model:ir.model.fields,field_description:stock.field_product_template__is_storable
#: model:ir.model.fields,field_description:stock.field_stock_move__is_storable
msgid "Track Inventory"
msgstr "재고 추적"

#. module: stock
#: model:ir.model.fields,help:stock.field_res_config_settings__module_product_expiry
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of life, alert. \n"
" Such dates are set automatically at lot/serial number creation based on values set on the product (in days)."
msgstr ""
"LOT & 일련번호에서 다음 날짜를 추적합니다: 유통기한, 제거, 생산중단단계, 경고.\n"
" 이러한 날짜는 품목에 설정된 값 (일)을 기준으로 LOT/일련번호 생성시 자동으로 설정됩니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Track following dates on lots & serial numbers: best before, removal, end of"
" life, alert. Such dates are set automatically at lot/serial number creation"
" based on values set on the product (in days)."
msgstr ""
"LOT & 일련번호에서 다음 날짜를 추적합니다: 유통기한, 제거, 생산중단단계, 경고. 이러한 날짜는 품목에 설정된 값 (일)을 기준으로"
" LOT/일련번호 생성시 자동으로 설정됩니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Track product location in your warehouse"
msgstr "창고 내에서의 품목 보관 위치를 추적합니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.product_template_action_product
msgid "Track your stock quantities by creating storable products."
msgstr "저장 가능 품목을 생성하여 재고 수량을 추적합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Tracked Products in Inventory Adjustment"
msgstr "재고 조사에서 추적된 품목"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_product__tracking
#: model:ir.model.fields,field_description:stock.field_product_template__tracking
#: model:ir.model.fields,field_description:stock.field_stock_move_line__tracking
#: model:ir.model.fields,field_description:stock.field_stock_quant__tracking
#: model:ir.model.fields,field_description:stock.field_stock_track_line__tracking
msgid "Tracking"
msgstr "추적"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_track_confirmation__tracking_line_ids
msgid "Tracking Line"
msgstr "추적 명세"

#. module: stock
#: model:ir.model,name:stock.model_stock_picking
#: model:ir.model.fields,field_description:stock.field_stock_backorder_confirmation_line__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_id
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_search_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Transfer"
msgstr "전송"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_tree
msgid "Transfer to"
msgstr "다음으로 이동"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock.action_picking_tree_all
#: model:ir.model.fields,field_description:stock.field_stock_lot__delivery_ids
#: model:ir.ui.menu,name:stock.menu_stock_transfers
#: model_terms:ir.ui.view,arch_db:stock.procurement_group_form_view
#: model_terms:ir.ui.view,arch_db:stock.stock_move_line_view_search
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "Transfers"
msgstr "이동"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Transfers %s: Please add some items to move."
msgstr "재고 이동 %s : 이동을 위해 품목을 추가하세요."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_get_picking_type_operations
#: model_terms:ir.actions.act_window,help:stock.action_picking_type_list
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "Transfers allow you to move products from one location to another."
msgstr "재고 이동을 통해 품목의 위치를 이동시킬 수 있습니다."

#. module: stock
#: model:ir.actions.act_window,name:stock.do_view_pickings
msgid "Transfers for Groups"
msgstr "그룹으로 이송"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid ""
"Transfers that are late on scheduled time or one of pickings will be late"
msgstr "전송이 예정 시간보다 지연되거나 픽업 중에 지연되는 항목이 있습니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__transit
msgid "Transit Location"
msgstr "이송 위치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.quant_search_view
msgid "Transit Locations"
msgstr "이송 위치"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Transport management: organize packs in your fleet, or carriers."
msgstr "운송 관리: 차량 또는 화물 운송업체를 통해 소포를 정리하세요."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__trigger
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger"
msgstr "자동 실행"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_rule__procure_method__make_to_order
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule"
msgstr "다른 규칙 실행"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "Trigger Another Rule If No Stock"
msgstr "재고가 없을 경우 다른 규칙을 실행"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
msgid "Trigger Manual"
msgstr "수동 작동"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_scrap__should_replenish
msgid "Trigger replenishment for scrapped products"
msgstr "폐기 제품에 대한 보충 트리거"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/stock_forecasted.js:0
msgid "Try to add some incoming or outgoing transfers."
msgstr "입고 또는 출고 이동을 추가할 수 있습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_barcode_rule__type
msgid "Type"
msgstr "유형"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Type a message..."
msgstr "메시지를 입력하세요."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_code
#: model:ir.model.fields,field_description:stock.field_stock_package_level__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking__picking_type_code
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__code
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
msgid "Type of Operation"
msgstr "운영 유형"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__activity_exception_decoration
#: model:ir.model.fields,help:stock.field_stock_picking__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr "UPS 배송"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr "USPS 배송"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/components/reception_report_line/stock_reception_report_line.xml:0
msgid "Unassign"
msgstr "할당 취소"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"Unavailable Serial numbers. Please correct the serial numbers encoded: "
"%(serial_numbers_to_locations)s"
msgstr "사용할 수 없는 일련번호. 인코딩된 일련번호를 수정하세요: %(serial_numbers_to_locations)s"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/client_actions/stock_traceability_report_backend.xml:0
msgid "Unfold"
msgstr "펼치기"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__name
msgid "Unique Lot/Serial Number"
msgstr "고유한 LOT/일련번호"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_product_stock_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_editable
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_simple
msgid "Unit"
msgstr "단위"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__price_unit
msgid "Unit Price"
msgstr "단가"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__forecast_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_lot__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_move_line__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__uom_id
#: model:ir.model.fields,field_description:stock.field_stock_scrap__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_storage_category_capacity__product_uom_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__product_uom
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty__product_uom_name
#: model:ir.model.fields,field_description:stock.field_stock_warn_insufficient_qty_scrap__product_uom_name
#: model_terms:ir.ui.view,arch_db:stock.package_level_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_form
#: model_terms:ir.ui.view,arch_db:stock.view_move_line_tree_detailed
#: model_terms:ir.ui.view,arch_db:stock.view_move_tree_receipt_picking
#: model_terms:ir.ui.view,arch_db:stock.view_picking_move_tree
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "Unit of Measure"
msgstr "단위"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__uom
msgid "Unit of Measure Name"
msgstr "단위명"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_inventory
#: model_terms:ir.ui.view,arch_db:stock.report_package_barcode
msgid "Units"
msgstr "단위"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Units Of Measure"
msgstr "단위"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_unit_measure_stock
msgid "Units of Measure"
msgstr "단위"

#. module: stock
#: model:ir.ui.menu,name:stock.product_uom_menu
msgid "Units of Measures"
msgstr "단위"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_replenish__product_uom_id
msgid "Unity of measure"
msgstr "측정 통일"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Unknown Pack"
msgstr "알 수 없는 포장"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid ""
"Unless previously specified by the source document, this will be the default"
" picking policy for this operation type."
msgstr "이전에 원본 문서에서 지정하지 않은 경우 이 작업 유형에 대한 기본 피킹 정책이 됩니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "Unpack"
msgstr "포장 해체"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
#: model:ir.actions.server,name:stock.action_unreserve_picking
#: model_terms:ir.ui.view,arch_db:stock.vpicktree
msgid "Unreserve"
msgstr "예약 해제"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.search_customer_lot_filter
msgid "Unreturned"
msgstr "미반송"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "Unsafe unit of measure"
msgstr "안전하지 않은 측정 단위"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__unwanted_replenish
msgid "Unwanted Replenish"
msgstr "요청하지 않은 보충"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__product_uom
#: model:ir.ui.menu,name:stock.menu_stock_uom_form_action
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_orderpoint_tree_editable
msgid "UoM"
msgstr "단위"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_stock_uom_categ_form_action
msgid "UoM Categories"
msgstr "단위 범주"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot_report__uom_id
msgid "Uom"
msgstr "측정 단위"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_change_product_quantity
msgid "Update Product Quantity"
msgstr "품목 수량 업데이트"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_adjustment_name_form_view
msgid "Update Quantities"
msgstr "수량 업데이트"

#. module: stock
#. odoo-javascript
#. odoo-python
#: code:addons/stock/models/product.py:0
#: code:addons/stock/static/src/stock_forecasted/forecasted_buttons.xml:0
#: model:ir.actions.act_window,name:stock.dashboard_open_quants
#: model_terms:ir.ui.view,arch_db:stock.product_form_view_procurement_button
#: model_terms:ir.ui.view,arch_db:stock.product_product_view_form_easy_inherit_stock
#: model_terms:ir.ui.view,arch_db:stock.product_template_form_view_procurement_button
msgid "Update Quantity"
msgstr "수량 갱신"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"Updating the location of this transfer will result in unreservation of the currently assigned items. An attempt to reserve items at the new location will be made and the link with preceding transfers will be discarded.\n"
"\n"
"To avoid this, please discard the source location change before saving."
msgstr ""
"이 전송 위치를 업데이트하면 현재 할당된 품목의 예약이 취소됩니다. 그러면 시스템에서 새 위치에서 항목 예약을 시도하고 이전 전송과의 링크가 제거됩니다.\n"
"\n"
"이 문제를 방지하려면 저장하기 전에 원본 위치 변경을 취소하세요."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__priority__1
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__priority__1
msgid "Urgent"
msgstr "긴급"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move_line__picking_type_use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking__use_existing_lots
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__use_existing_lots
msgid "Use Existing Lots/Serial Numbers"
msgstr "기존 LOT/일련번호 사용"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "Use Existing ones"
msgstr "기존 항목 사용"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid ""
"Use GS1 nomenclature datamatrix whenever barcodes are printed for lots and "
"serial numbers."
msgstr "GS1 데이터 매트릭스를 사용하여 로트 및 일련번호를 바코드로 인쇄합니다."

#. module: stock
#: model:res.groups,name:stock.group_reception_report
msgid "Use Reception Report"
msgstr "수취 보고서 사용"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "Use your own routes"
msgstr "나의 경로를 설정합니다."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "Used by"
msgstr "사용자"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__sequence
msgid "Used to order the 'All Operations' kanban view"
msgstr "'모든 작업'의 칸반뷰를 정리하는 데 사용됩니다"

#. module: stock
#: model:ir.model,name:stock.model_res_users
#: model:ir.model.fields,field_description:stock.field_stock_request_count__user_id
#: model:res.groups,name:stock.group_stock_user
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_tree_inventory_editable
msgid "User"
msgstr "사용자"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_quant__user_id
msgid "User assigned to do product count."
msgstr "품목 실사에 배정된 사용자입니다."

#. module: stock
#: model:ir.actions.server,name:stock.action_validate_picking
#: model_terms:ir.ui.view,arch_db:stock.stock_scrap_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "Validate"
msgstr "승인"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/xml/inventory_lines.xml:0
msgid "Validate Inventory"
msgstr "재고조사 승인"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_change_product_qty__product_variant_count
msgid "Variant Count"
msgstr "세부선택항목 수"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor"
msgstr "공급업체"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_partner__property_stock_supplier
#: model:ir.model.fields,field_description:stock.field_res_users__property_stock_supplier
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__supplier
msgid "Vendor Location"
msgstr "공급업체 창고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
msgid "Vendor Locations"
msgstr "공급업체 창고"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_location__usage__view
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
msgid "View"
msgstr "화면"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.product_view_kanban_catalog
msgid "View Availability"
msgstr "사용 가능 항목 보기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "View Diagram"
msgstr "도표 보기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__view_location_id
msgid "View Location"
msgstr "공간(위치) 보기"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "View and allocate received quantities."
msgstr "입고 수량을 확인 및 할당합니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__visibility_days
msgid "Visibility Days"
msgstr "가시성 일자"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "Visibility days"
msgstr "표시 일자"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_volume
msgid "Volume for Shipping"
msgstr "선적 부피"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/OUT/00001"
msgstr "WH/OUT/00001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
msgid "WH/OUT/0001"
msgstr "WH/OUT/0001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Outgoing"
msgstr "WH/출고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_generic_barcode
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "WH/Stock"
msgstr "WH/재고"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__confirmed
#: model_terms:ir.ui.view,arch_db:stock.stock_picking_type_kanban
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting"
msgstr "대기 중"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__waiting
msgid "Waiting Another Move"
msgstr "다른 이동 대기 중"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__state__waiting
msgid "Waiting Another Operation"
msgstr "다른 작업 대기 중"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_move__state__confirmed
msgid "Waiting Availability"
msgstr "사용 가능 확인중"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Waiting Moves"
msgstr "대기 이동"

#. module: stock
#: model:ir.actions.act_window,name:stock.action_picking_tree_waiting
msgid "Waiting Transfers"
msgstr "대기 이동"

#. module: stock
#: model:ir.model,name:stock.model_stock_warehouse
#: model:ir.model.fields,field_description:stock.field_product_product__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_replenish__warehouse_id
#: model:ir.model.fields,field_description:stock.field_product_template__warehouse_id
#: model:ir.model.fields,field_description:stock.field_report_stock_quantity__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_move__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_picking_type__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_quant__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_rule__warehouse_id
#: model:ir.model.fields,field_description:stock.field_stock_warehouse__name
#: model:ir.model.fields,field_description:stock.field_stock_warehouse_orderpoint__warehouse_id
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.stock_reorder_report_search
#: model_terms:ir.ui.view,arch_db:stock.stock_warehouse_view_search
#: model_terms:ir.ui.view,arch_db:stock.view_location_search
#: model_terms:ir.ui.view,arch_db:stock.view_pickingtype_filter
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_filter
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse_tree
#: model_terms:ir.ui.view,arch_db:stock.warehouse_orderpoint_search
msgid "Warehouse"
msgstr "창고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse Configuration"
msgstr "창고 환경 설정"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_domain_ids
msgid "Warehouse Domain"
msgstr "창고 도메인"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.replenishment_option_tree_view
msgid "Warehouse Location"
msgstr "창고 위치"

#. module: stock
#: model:ir.ui.menu,name:stock.menu_warehouse_config
msgid "Warehouse Management"
msgstr "창고 관리"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_location__warehouse_view_ids
msgid "Warehouse View"
msgstr "창고 보기"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_rule__propagate_warehouse_id
msgid "Warehouse to Propagate"
msgstr "보급할 창고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "Warehouse view location"
msgstr "창고 보기 위치"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid "Warehouse's Routes"
msgstr "창고의 경로"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_warehouse_filter.xml:0
msgid "Warehouse:"
msgstr "창고:"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_report_search_panel.xml:0
#: model:ir.actions.act_window,name:stock.action_warehouse_form
#: model:ir.model.fields,field_description:stock.field_stock_route__warehouse_ids
#: model:ir.model.fields,field_description:stock.field_stock_rules_report__warehouse_ids
#: model:ir.ui.menu,name:stock.menu_action_warehouse_form
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_stock_replenishment_info
msgid "Warehouses"
msgstr "창고"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty
msgid "Warn Insufficient Quantity"
msgstr "수량 부족 경고"

#. module: stock
#: model:ir.model,name:stock.model_stock_warn_insufficient_qty_scrap
msgid "Warn Insufficient Scrap Quantity"
msgstr "폐기 수량 부족 경고"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product_strategy.py:0
#: code:addons/stock/models/stock_move.py:0
#: code:addons/stock/models/stock_move_line.py:0
#: code:addons/stock/models/stock_quant.py:0
#: code:addons/stock/models/stock_scrap.py:0
#: code:addons/stock/models/stock_warehouse.py:0
#: model:ir.model.fields.selection,name:stock.selection__res_partner__picking_warn__warning
msgid "Warning"
msgstr "경고"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Warning Duplicated SN"
msgstr "일련번호 중복 경고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_option__warning_message
msgid "Warning Message"
msgstr "경고 메시지"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_partner_stock_warnings_form
msgid "Warning on the Picking"
msgstr "선별에 대한 경고"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid "Warning!"
msgstr "경고!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "Warning: change source location"
msgstr "경고: 원본 위치 변경"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Warnings"
msgstr "경고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__group_warning_stock
msgid "Warnings for Stock"
msgstr "재고에 대한 경고"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,field_description:stock.field_stock_scrap__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_lot__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_picking__website_message_ids
#: model:ir.model.fields,help:stock.field_stock_scrap__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__base_weight
msgid "Weight"
msgstr "무게"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_picking__shipping_weight
msgid "Weight for Shipping"
msgstr "선적 중량"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_package_type__base_weight
msgid "Weight of the package type"
msgstr "패키지 유형 중량"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_storage_category__weight_uom_name
msgid "Weight unit"
msgstr "중량 단위"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__weight_uom_name
msgid "Weight unit of measure label"
msgstr "무게 단위 라벨"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__barcode_rule__type__weight
msgid "Weighted Product"
msgstr "중량이 있는 품목"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_replenishment_info__wh_replenishment_option_ids
msgid "Wh Replenishment Option"
msgstr "보충 시기 옵션"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__warehouse_selectable
msgid ""
"When a warehouse is selected for this route, this route should be seen as "
"the default route when products pass through this warehouse."
msgstr "이 이동 경로에서 창고를 선택하면 이 이동 경로는 품목이 이 창고를 통과할 때 기본 경로로 간주되어야 합니다."

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__move_type__one
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__move_type__one
msgid "When all products are ready"
msgstr "모든 품목이 준비될 때"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_selectable
msgid ""
"When checked, the route will be selectable in the Inventory tab of the "
"Product form."
msgstr "선택 시, 품목 서식의 재고 탭에서 이동 경로를 설정할 수 있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__product_categ_selectable
msgid "When checked, the route will be selectable on the Product Category."
msgstr "선택 시 품목 카테고리에서 이동 경로를 선택할 수 있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_route__packaging_selectable
msgid "When checked, the route will be selectable on the Product Packaging."
msgstr "선택하면 품목 패키지에서 경로를 선택할 수 있습니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_putaway_rule__location_in_id
#: model_terms:ir.ui.view,arch_db:stock.stock_putaway_list
msgid "When product arrives in"
msgstr "품목 도착 위치"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products are needed in <b>%(destination)s</b>, <br> "
"<b>%(operation)s</b> are created from <b>%(source_location)s</b> to fulfill "
"the need."
msgstr ""
"품목이 <b>%(destination)s</b> 장소에서 필요한 경우, <br> <b>%(operation)s</b> 항목이 "
"<b>%(source_location)s</b> 장소에서 필요한 내용이 생성됩니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_rule.py:0
msgid ""
"When products arrive in <b>%(source_location)s</b>, <br> "
"<b>%(operation)s</b> are created to send them to <b>%(destination)s</b>."
msgstr ""
"품목이 <b>%(source_location)s</b> 장소에 도착하면 <br/> <b>%(operation)s</b> 항목이 생성되어 "
"<b>%(destination)s</b> 장소로 이동합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__location_dest_from_rule
msgid ""
"When set to True the destination location of the stock.move will be the "
"rule.Otherwise, it takes it from the picking type."
msgstr "True로 설정하면 stock.move의 대상 위치가 규칙이 됩니다. 그렇지 않으면 피킹 유형에서 가져옵니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__is_locked
msgid ""
"When the picking is not done this allows changing the initial demand. When "
"the picking is done this allows changing the done quantities."
msgstr "선별이 종료되지 않은 경우에는 기초 수요를 변경할 수 있습니다. 선별이 완료되면 완료 수량을 변경할 수 있습니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_min_qty
msgid ""
"When the virtual stock goes below the Min Quantity specified for this field,"
" Odoo generates a procurement to bring the forecasted quantity above of this"
" Min Quantity."
msgstr ""
"가상 재고가 이 필드에 지정된 최소 수량보다 낮아지면 Odoo는 이 최소 수량 이상으로 예측 수량을 늘리기 위해 조달을 생성합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_warehouse_orderpoint__product_max_qty
msgid ""
"When the virtual stock goes below the Min Quantity, Odoo generates a "
"procurement to bring the forecasted quantity up to (or near to) the Max "
"Quantity specified for this field (or to Min Quantity, whichever is bigger)."
msgstr ""
"가상 재고가 최소 수량 아래로 떨어지면 Odoo는 이 필드에 지정된 최대 수량 또는 더 큰 경우 최소 수량까지(또는 그 근처까지) 예측 "
"수량을 늘리기 위한 조달을 생성합니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_carrier
msgid "When ticked, carrier of shipment will be propagated."
msgstr "체크하면 운송업체에게 전달됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_rule__propagate_cancel
msgid ""
"When ticked, if the move created by this rule is cancelled, the next move "
"will be cancelled too."
msgstr "이 규칙에 의해 생성된 이동이 취소되면 다음 이동도 취소됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking_type__create_backorder
msgid ""
"When validating a transfer:\n"
" * Ask: users are asked to choose if they want to make a backorder for remaining products\n"
" * Always: a backorder is automatically created for the remaining products\n"
" * Never: remaining products are cancelled"
msgstr ""
"이동을 승인할 경우:\n"
" * 확인하기: 나머지 품목에 대해서 이월 주문을 할 것인지 사용자에게 선택하도록 확인\n"
" * 항상 실행: 나머지 품목에 대해 이월 주문을 자동으로 생성함 \n"
" * 실행하지 않음: 나머지 품목이 취소됨"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_picking__owner_id
msgid ""
"When validating the transfer, the products will be assigned to this owner."
msgstr "재고 이동을 승인하면 품목이 이 소유자에게 할당됩니다."

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move_line__owner_id
msgid ""
"When validating the transfer, the products will be taken from this owner."
msgstr "재고이동을 승인할 때 이 소유자로부터 품목을 가져옵니다."

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_move__additional
msgid "Whether the move was added after the picking's confirmation"
msgstr "선별 확인 후 이동이 추가되었는지 여부"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_package_type__width
#: model_terms:ir.ui.view,arch_db:stock.stock_package_type_form
msgid "Width"
msgstr "폭"

#. module: stock
#: model:ir.model.constraint,message:stock.constraint_stock_package_type_positive_width
msgid "Width must be positive"
msgstr "폭은 양수여야 합니다"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_stock_return_picking_line__wizard_id
#: model:ir.model.fields,field_description:stock.field_stock_track_line__wizard_id
msgid "Wizard"
msgstr "마법사"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "Write one lot/serial name per line, followed by the quantity."
msgstr "Lot/일련번호를 한 줄에 하나씩 적고, 그 뒤에 수량을 적습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
#: model:ir.model.fields.selection,name:stock.selection__stock_picking__search_date_category__yesterday
#: model_terms:ir.ui.view,arch_db:stock.view_picking_internal_search
msgid "Yesterday"
msgstr "어제"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_quant_relocate_view_form
msgid ""
"You are about to move quantities in a package without moving the full package.\n"
"                    Those quantities will be removed from the following package(s):"
msgstr ""
"전체 패키지를 이동하지 않고 패키지의 수량만을 이동하려고 합니다.\n"
"                    해당 수량은 다음 패키지에서 제거됩니다:"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_quant_form
msgid ""
"You are going to pick products that are not referenced\n"
"in this location. That leads to a negative stock."
msgstr ""
"참조가 이 위치로 되어 있지 않은 품목을 선택합니다.\n"
"재고 수량이 음수로 나타나게 됩니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid "You are good, no replenishment to perform!"
msgstr "아직 재보충해야 할 주문이 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to change the product linked to a serial or lot number "
"if some stock moves have already been created with that number. This would "
"lead to inconsistencies in your stock."
msgstr ""
"해당 번호로 일부 재고 이동이 이미 생성된 경우 Serial 번호 또는 Lot 번호에 연결된 품목을 변경할 수 없습니다. 이로 인해 "
"재고가 일치하지 않을 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You are not allowed to create a lot or serial number with this operation "
"type. To change this, go on the operation type and tick the box \"Create New"
" Lots/Serial Numbers\"."
msgstr ""
"이 작업 유형으로 로트 또는 일련번호를 만들 수 없습니다. 이를 변경하려면 작업 유형으로 이동하여 \"새 LOT/일련번호 만들기\" "
"상자를 선택하십시오."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_package_destination_form_view
msgid ""
"You are trying to put products going to different locations into the same "
"package"
msgstr "서로 다른 공간으로 이동하는 품목을 동일한 패키지에 넣을 수 없습니다"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You are using a unit of measure smaller than the one you are using in order "
"to stock your product. This can lead to rounding problem on reserved "
"quantity. You should use the smaller unit of measure possible in order to "
"valuate your stock or change its rounding precision to a smaller value "
"(example: 0.00001)."
msgstr ""
"품목 재고를 확보하기 위해 사용하는 것보다 작은 단위를 사용하고 있습니다. 이로 인해 예약 수량에 반올림 문제가 발생할 수 "
"있습니다. 재고를 평가하거나 반올림 정밀도를 더 작은 값으로 변경하려면 가능한 작은 측정 단위를 사용해야합니다 (예: 0.00001)."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_routes_form
msgid ""
"You can define here the main routes that run through\n"
"                your warehouses and that define the flows of your products. These\n"
"                routes can be assigned to a product, a product category or be fixed\n"
"                on procurement or sales order."
msgstr ""
"여기에서 창고를 통해 실행되고 제품의 흐름을 정의하는 \n"
"                  기본 이동 경로를 정의할 수 있습니다.\n"
"                  이 이동 경로는 품목, 품목 카테고리에 할당되거나 \n"
"                  조달 또는 판매 주문에서 고정될 수 있습니다."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_inventory_conflict_form_view
msgid "You can either :"
msgstr "다음 중에서 진행하실 수 있습니다:"

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that is currently "
"reserved on a stock move. If you need to change the inventory tracking, you "
"should first unreserve the stock move."
msgstr ""
"현재 재고 이동으로 예약된 품목의 재고 추적은 변경할 수 없습니다. 재고 추적을 수정하려면 먼저 재고 이동 예약을 취소해야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You can not change the inventory tracking of a product that was already "
"used."
msgstr "이미 품목 유형을 사용한 경우에는 재고 추적을 변경할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can not create a snoozed orderpoint that is not manually triggered."
msgstr "수동으로 실행시키지 않으면 일시 중지된 주문 지점을 생성할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You can not delete moves linked to another operation"
msgstr "다른 작업에 이동이 연결되어 있는 경우에는 삭제할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You can not delete product moves if the picking is done. You can only "
"correct the done quantities."
msgstr "선별이 완료되면 품목 이동을 삭제할 수 없습니다. 완료 수량만 수정할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can not enter negative quantities."
msgstr "음수를 입력할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You can only enter positive quantities."
msgstr "수량은 양수만 입력할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You can only move a lot/serial to a new location if it exists in a single "
"location."
msgstr "lot/일련번호가 단일 위치에 있는 경우에만 새로운 위치로 이동할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You can only move positive quantities stored in locations used by a single "
"company per relocation."
msgstr "단일 회사에서 사용하는 위치에 저장된 양의 수량만큼만 이동할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid "You can only process 1.0 %s of products with unique serial number."
msgstr "고유 Serial 번호가 있는 품목의 1.0 %s만 처리할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You can only snooze manual orderpoints. You should rather archive 'auto-"
"trigger' orderpoints if you do not want them to be triggered."
msgstr ""
"주문 포인트는 수동 주문 포인트만 일시 중지할 수 있습니다. 자동 체결 주문 포인트를 활성화하지 않으려면 보관처리해야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You can't deactivate the multi-location if you have more than once warehouse"
" by company"
msgstr "회사별 창고가 2개 이상인 경우에는 다중 위치를 비활성화시킬 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid "You can't disable locations %s because they still contain products."
msgstr "%s 위치에는 여전히 품목이 포함되어 있으므로 비활성화할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot archive location %(location)s because it is used by warehouse "
"%(warehouse)s"
msgstr "%(location)s 위치는 %(warehouse)s 창고에서 사용 중이므로 보관할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot cancel a stock move that has been set to 'Done'. Create a return "
"in order to reverse the moves which took place."
msgstr "이미 '완료'된 재고이동을 취소할 수 없습니다. 반품을 통해 재고를 복구할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot change a cancelled stock move, create a new line instead."
msgstr "재고 이동을 취소한 경우에는 변경할 수 없으며, 대신 새로운 항목을 생성합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You cannot change the Scheduled Date on a done or cancelled transfer."
msgstr "완료 또는 취소된 이송에서 예약 날짜를 변경할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot change the UoM for a stock move that has been set to 'Done'."
msgstr "재고 이동 단위가 '완료'로 설정된 경우에는 변경할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_lot.py:0
msgid ""
"You cannot change the company of a lot/serial number currently in a location"
" belonging to another company."
msgstr "현재 다른 회사에 속해 있는 로트/일련번호의 회사는 변경할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the ratio of this unit of measure as some products with "
"this UoM have already been moved or are currently reserved."
msgstr "특정 품목이 이 단위를 이미 사용하고 있거나 현재 예약중인 재고가 있다면 이 단위의 비율을 변경할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You cannot change the unit of measure as there are already stock moves for "
"this product. If you want to change the unit of measure, you should rather "
"archive this product and create a new one."
msgstr ""
"이 품목에 대한 재고 이동이 이미 있으므로 단위를 변경할 수 없습니다. 단위를 변경하려면, 이 품목을 보관하고 신규 품목을 만들어야 "
"합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_scrap.py:0
msgid "You cannot delete a scrap which is done."
msgstr "완료된 폐기건은 삭제할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot directly pack quantities from different transfers into the same "
"package through this view. Try adding them to a batch picking and pack it "
"there."
msgstr ""
"이 보기를 사용하여 서로 다른 전송의 수량을 동일한 패키지로 통합할 수 없습니다. 대신 일괄 피킹에 수량을 추가하고 그곳에서 포장할 수 "
"있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot duplicate stock quants."
msgstr "재고량은 중복으로 사용할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "You cannot modify inventory loss quantity"
msgstr "재고 손실 수량을 수정할 수 없습니다"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot move the same package content more than once in the same transfer"
" or split the same package into two location."
msgstr "동일한 이송에서 동일한 패키지 내용을 두 번 이상 이동하거나 동일한 패키지를 두 위치로 분할할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot pack products into the same package when they are from different "
"transfers with different operation types."
msgstr "서로 다른 작업 유형의 서로 다른 이송으로 출발한 제품은 함께 패키징할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid ""
"You cannot perform moves because their unit of measure has a different "
"category from their product unit of measure."
msgstr "측정 단위가 제품 측정 단위 카테고리와 다르기 때문에 이동을 수행할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_location.py:0
msgid ""
"You cannot set a location as a scrap location when it is assigned as a "
"destination location for a manufacturing type operation."
msgstr "위치가 제조 유형 작업의 대상 위치로 지정된 경우에는 해당 위치를 폐기 위치로 지정할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot set a scrap location as the destination location for a "
"manufacturing type operation."
msgstr "제조 유형 작업용으로 대상 위치가 되어 있는 경우에는 스크랩 위치로 설정할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a draft move. It needs to be confirmed first."
msgstr "임시 이동을 분할할 수 없습니다. 먼저 확인해야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot split a stock move that has been set to 'Done' or 'Cancel'."
msgstr "'완료' 또는 '취소'로 설정된 재고 이동 항목은 분할할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid ""
"You cannot take products from or deliver products to a location of type "
"\"view\" (%s)."
msgstr "품목을 \"보기\" 유형의 (%s) 공간으로 입출고할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move.py:0
msgid "You cannot unreserve a stock move that has been set to 'Done'."
msgstr "'완료'로 설정된 재고 이동을 예약 해제할 수 없습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You cannot use the same serial number twice. Please correct the serial "
"numbers encoded."
msgstr "동일한 일련번호를 두 번 사용할 수 없습니다. 인코딩 된 일련번호를 수정하십시오."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You cannot validate a transfer if no quantities are reserved. To force the "
"transfer, encode quantities."
msgstr "예약된 수량이 없을 경우 재고 이동을 승인할 수 없습니다. 재고 이동을 강제로 처리하려면 수정 모드에서 수량을 입력하세요."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You can’t validate an empty transfer. Please add some products to move "
"before proceeding."
msgstr "재고 이동이 비어있을 경우 승인 할 수 없습니다. 계속 진행하려면 재고 이동 품목을 추가해 주세요."

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_backorder_confirmation
msgid "You have processed less products than the initial demand."
msgstr "요청 수량보다 적은 수의 품목을 처리했습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/res_config_settings.py:0
msgid ""
"You have product(s) in stock that have lot/serial number tracking enabled. \n"
"Switch off tracking on all the products before switching off this setting."
msgstr ""
"재고 추적이 활성화된 Lot/일련번호 품목이 존재합니다.\n"
"이 설정을 해제하기 전에 모든 품목에서 추적 기능을 해제해 주십시오."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You have product(s) in stock that have no lot/serial number. You can assign "
"lot/serial numbers by doing an inventory adjustment."
msgstr ""
"Lot/Serial 번호가 없는 품목(들) 재고가 있습니다. 재고 조사을 수행하여 Lot/Serial 번호를 지정할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_orderpoint.py:0
msgid ""
"You have to select a product unit of measure that is in the same category as"
" the default unit of measure of the product"
msgstr "품목의 기본 단위와 동일 카테고리에 있는 단위를 선택해야 합니다"

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return Done pickings."
msgstr "선별 완료만 반환할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/stock_picking_return.py:0
msgid "You may only return one picking at a time."
msgstr "한 번에 하나의 선별만 반환할 수 있습니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid ""
"You need to activate storage locations to be able to do internal operation "
"types."
msgstr "내부 작업 유형을 수행하려면 저장 위치를 ​​활성화해야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/wizard/product_replenish.py:0
msgid "You need to select a route to replenish your products"
msgstr "제품을 보충할 경로를 선택해야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_move_line.py:0
msgid ""
"You need to supply a Lot/Serial Number for product:\n"
"%(products)s"
msgstr ""
"품목에 대한 로트번호/일련번호를 입력해야 합니다:\n"
"%(products)s"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_picking.py:0
msgid "You need to supply a Lot/Serial number for products %s."
msgstr "품목 %s의 Lot/Serial 번호를 지정해야 합니다."

#. module: stock
#: model_terms:res.company,invoice_terms_html:stock.res_company_1
msgid "You should update this document to reflect your T&amp;C."
msgstr "귀사의 이용약관을 반영하여 이 문서를 업데이트해야 합니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_warehouse.py:0
msgid ""
"You still have ongoing operations for operation types %(operations)s in "
"warehouse %(warehouse)s"
msgstr "작업 유형 %(operations)s에 대한 작업이 아직 창고 %(warehouse)s에서 진행 중입니다."

#. module: stock
#. odoo-python
#: code:addons/stock/models/product.py:0
msgid ""
"You still have some active reordering rules on this product. Please archive "
"or delete them first."
msgstr "이 품목에 대한 재주문 규칙이 여전히 존재합니다. 먼저 보관함으로 이동거나 삭제하십시오."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/list/inventory_report_list_model.js:0
msgid ""
"You tried to create a record that already exists. The existing record was "
"modified instead."
msgstr "생성하려는 레코드가 이미 존재합니다. 대신 최근 내용이 수정되었습니다."

#. module: stock
#: model_terms:ir.actions.act_window,help:stock.action_orderpoint_replenish
msgid ""
"You'll find here smart replenishment propositions based on inventory forecasts.\n"
"            Choose the quantity to buy or manufacture and launch orders in a click.\n"
"            To save time in the future, set the rules as \"automated\"."
msgstr ""
"여기에서 재고 예측을 기반으로 한 스마트 보충 안내를 확인할 수 있습니다.\n"
"            클릭 한 번으로 구매 또는 제조 수량을 선택하여 재보충 주문을 시작하세요. \n"
"            규칙을 \"자동\"으로 설정해놓으면 주문에 소요되는 시간을 줄일 수 있습니다. "

#. module: stock
#: model_terms:res.company,lunch_notify_message:stock.res_company_1
msgid ""
"Your lunch has been delivered.\n"
"Enjoy your meal!"
msgstr ""
"점심 식사가 배송되었습니다.\n"
"맛있게 드세요!"

#. module: stock
#. odoo-python
#: code:addons/stock/models/stock_quant.py:0
msgid "Your stock is currently empty"
msgstr "현재 재고가 없습니다"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__package_label_to_print__zpl
msgid "ZPL"
msgstr "ZPL"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__lot_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zpl
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zpl
msgid "ZPL Labels"
msgstr "ZPL 라벨"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_lots
msgid "ZPL Labels - One per lot/SN"
msgstr "ZPL 라벨 - lot/일련번호당 하나"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__lot_label_format__zpl_units
msgid "ZPL Labels - One per unit"
msgstr "ZPL 라벨s - 단위당 하나"

#. module: stock
#: model:ir.model.fields.selection,name:stock.selection__product_label_layout__print_format__zplxprice
#: model:ir.model.fields.selection,name:stock.selection__stock_picking_type__product_label_format__zplxprice
msgid "ZPL Labels with price"
msgstr "ZPL 라벨, 가격 표시"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_stock_rule
msgid "]<br/>min:"
msgstr "]<br/>분 :"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "below the inventory"
msgstr "재고 이하"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr "bpost 배송"

#. module: stock
#: model:product.removal,method:stock.removal_closest
msgid "closest"
msgstr "가장 가까운"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/views/search/stock_orderpoint_search_panel.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_stock_rule_form
#: model_terms:ir.ui.view,arch_db:stock.view_template_property_form
msgid "days"
msgstr "일"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before when starred"
msgstr "별표 표시된 날짜 이전"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "days before/"
msgstr "다음 날짜 이전"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. CW"
msgstr "예: CW"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
#: model_terms:ir.ui.view,arch_db:stock.view_warehouse
msgid "e.g. Central Warehouse"
msgstr "예: 메인 창고"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/lots_dialog.xml:0
msgid "e.g. LOT-PR-00012"
msgstr "예: LOT-PR-00012"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_production_lot_form
msgid "e.g. LOT/0001/20121"
msgstr "예. LOT/0001/20121"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. Lumber Inc"
msgstr "예: Lumber Inc"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_quant_package_form
msgid "e.g. PACK0000007"
msgstr "예: PACK0000007"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_form
msgid "e.g. PO0032"
msgstr "예. PO0032"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Physical Locations"
msgstr "예: 실제 위치하고 있는 장소"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_picking_type_form
msgid "e.g. Receptions"
msgstr "예: 수취"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_stock_move_line_operation_tree
msgid "e.g. SN000001"
msgstr "예: SN000001"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.view_location_form
msgid "e.g. Spare Stock"
msgstr "예: 예비 재고"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_location_route_form_view
msgid "e.g. Two-steps reception"
msgstr "예: 2단계 수취"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "예: d7vctmiv2rwgenebha8bxq7irooudn"

#. module: stock
#: model:product.removal,method:stock.removal_fifo
msgid "fifo"
msgstr "선입선출"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_scrap_form_view
msgid "from location"
msgstr "출발공간"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_warn_insufficient_qty_form_view
msgid "in"
msgstr "분류"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.res_config_settings_view_form
msgid "in barcode."
msgstr "바코드에서"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
#: model_terms:ir.ui.view,arch_db:stock.view_product_replenish
msgid "is"
msgstr "기준 예상 재고"

#. module: stock
#: model:product.removal,method:stock.removal_least_packages
msgid "least_packages"
msgstr "least_packages"

#. module: stock
#: model:product.removal,method:stock.removal_lifo
msgid "lifo"
msgstr "후입선출"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "minimum of"
msgstr "최소"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "of"
msgstr "of"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/stock_rescheduling_popover.xml:0
msgid "planned on"
msgstr "예정일"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.exception_on_picking
msgid "processed instead of"
msgstr "다음 대신 처리됨"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.stock_report_view_graph
msgid "report_stock_quantity_graph"
msgstr "report_stock_quantity_graph"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/stock_forecasted/forecasted_details.xml:0
msgid "reserved"
msgstr "예약"

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "should be replenished"
msgstr "보충 필요"

#. module: stock
#: model:ir.actions.server,name:stock.click_dashboard_graph
msgid "stock.click_dashboard_graph"
msgstr "stock.click_dashboard_graph"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_incoming
msgid "stock.method_action_picking_tree_incoming"
msgstr "stock.method_action_picking_tree_incoming"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_internal
msgid "stock.method_action_picking_tree_internal"
msgstr "stock.method_action_picking_tree_internal"

#. module: stock
#: model:ir.actions.server,name:stock.method_action_picking_tree_outgoing
msgid "stock.method_action_picking_tree_outgoing"
msgstr "stock.method_action_picking_tree_outgoing"

#. module: stock
#: model:ir.model.fields,field_description:stock.field_product_category__filter_for_stock_putaway_rule
msgid "stock.putaway.rule"
msgstr "stock.putaway.rule"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.help_message_template
msgid "the barcode app"
msgstr "바코드 앱"

#. module: stock
#: model:ir.model.fields,help:stock.field_stock_move__warehouse_id
msgid ""
"the warehouse to consider for the route selection on the next procurement "
"(if any)."
msgstr "다음 조달 시 경로 선택을 고려할 창고 (있는 경우)."

#. module: stock
#. odoo-javascript
#: code:addons/stock/static/src/widgets/json_widget.xml:0
msgid "to reach the maximum of"
msgstr "다음 최대치에 도달"

#. module: stock
#: model_terms:ir.ui.view,arch_db:stock.report_delivery_document
#: model_terms:ir.ui.view,arch_db:stock.report_picking
msgid "units"
msgstr "단위"

#. module: stock
#: model:mail.template,subject:stock.mail_template_data_delivery_confirmation
msgid ""
"{{ object.company_id.name }} Delivery Order (Ref {{ object.name or 'n/a' }})"
msgstr "{{ object.company_id.name }} 배송 주문서 (Ref {{ object.name or 'n/a' }})"
