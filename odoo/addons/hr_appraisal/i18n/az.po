# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "%(user)s decided, as %(role)s, to publish the employee's feedback"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_template.py:0
msgid "%s (copy)"
msgstr "%s (surət)"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "%s's Goals"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "1 Meeting"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__100
msgid "100%"
msgstr "100%"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__025
msgid "25%"
msgstr "25%"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__module_hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "360 Feedback"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__050
msgid "50%"
msgstr "50%"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__075
msgid "75%"
msgstr "75%"

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_confirm
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        An appraisal of <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t> has been confirmed.\n"
"                        <br/><br/>\n"
"                        Please schedule an appraisal date together.\n"
"                        <br/><br/>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t>,\n"
"                        <br/>\n"
"                        <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t> (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>) wishes an appraisal.\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t>,\n"
"                        <br/>\n"
"                        An appraisal has been requested by <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t>\n"
"                        <br/>\n"
"                        (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>).\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <br/>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Describe something that made you proud, a piece of work positive for\n"
"                        the company.\n"
"                    </em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Did you face new difficulties? Did you confront yourself to new\n"
"                        obstacles?\n"
"                    </em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Every job has strong points, what are, in your opinion, the tasks that\n"
"                        you enjoy the most/the least?\n"
"                    </em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        From a manager point of view, how could you help the employee to\n"
"                        overcome their weaknesses?\n"
"                    </em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        How can the company help you with your need and objectives in order\n"
"                        for you to reach your goals and look for the best collaboration.\n"
"                    </em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        How do you see the employee in the future, do your vision follow the\n"
"                        employee's desire?\n"
"                    </em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Some achievements comforting you in their strengths to face job's\n"
"                        issues.\n"
"                    </em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Autonomy</em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Culture/Behavior:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-1\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Do you need rapid answer to the current situation?</em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Give an example of long-term objective (&gt; 6 months)</em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Give an example of short-term objective (&lt; 6 months)</em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Internal Communication:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-2\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Job's content:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-3\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Pro-activity</em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Remuneration:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-5\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Stress Resistance</em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Teamwork</em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Time Management</em>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Work organization:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-4\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee_base.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Schedule an appraisal\n"
"                        </p><p>\n"
"                            Plan appraisals with your colleagues, collect and discuss feedback.\n"
"                        </p>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<small role=\"img\" class=\"fa fa-circle text-success\" invisible=\"state !="
" 'pending' or waiting_feedback\" aria-label=\"Ready\" title=\"Ready\"/>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"fw-bold\">Meeting: </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"last_appraisal_state == 'done'\">\n"
"                            Ongoing\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\" invisible=\"last_appraisal_state != 'done'\">\n"
"                            Latest\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Appraisal\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Appraisal\n"
"                        </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_text\">Appraisals</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_text\">Goals</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-end\" invisible=\"employee_feedback_published\">Not Visible to Manager</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state == 'new'\">Visible to Manager</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state != 'new'\">Visible &amp; Editable by Manager</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-end\" invisible=\"manager_feedback_published or not can_see_manager_publish\">Not Visible to Employee</span>\n"
"                                            <span class=\"text-end\" invisible=\"not manager_feedback_published or not can_see_manager_publish\">Visible to Employee</span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
msgid ""
"<span invisible=\"not is_manager or (next_appraisal_date or not ongoing_appraisal_count)\">\n"
"                    Ongoing\n"
"                </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Evaluation\n"
"                    </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Feedback\n"
"                    </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Improvements\n"
"                    </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My feelings\n"
"                    </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My future\n"
"                    </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My work\n"
"                    </span>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong class=\"text-o-color-4\">\n"
"                                    LEAST\n"
"                                    <br>\n"
"                                </strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong class=\"text-o-color-4\">\n"
"                                    MOST\n"
"                                    <br>\n"
"                                </strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>360 feedback :</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Any particular remark on the training ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Are you happy with your current role ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>Can the employee accurately recall and explain the key concepts from"
" the training ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Career objective</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Career opportunities</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Company Feedback</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Could you provide examples ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Create your SWOT</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_gantt
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_gantt
msgid "<strong>Date — </strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Do you seek any particular career path ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Feedback on management</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>GOALS :</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Handover to manage inside the team</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>Has the employee effectively integrated the new skills or knowledge "
"into their daily work tasks ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How actively did the employee participate during the training "
"sessions (e.g., asking questions, contributing to discussions) ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How confident do you feel in using the skills and knowledge gained "
"from the training in your work ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>How do you feel about the support and resources provided to you "
"during this period ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How helpful were the provided materials ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How would you asses your level before the training ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How would you assess your level post training ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How would you rate the employee's proficiency in the skill on a "
"scale from 1 to 10 ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Management feedback and assessment</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Motivations</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>On a scale of 1 to 10, how would you rate the employee’s overall "
"improvement since the training ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Opportunity</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Overall experience feedback</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Overall personal experience</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Position succession and candidate suggestion</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>SWOT results</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>SWOT</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Skills at stakes :</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Strength</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to keep doing ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to start doing ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to stop doing ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Threats</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Time to assess the leadership team:</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Training assessment</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>We wish you the best of success and sincerely thank you for your "
"invaluable contribution to the company's achievements.</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Weakness</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What advice do you have for improving management's leadership and "
"support for future employees ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>What advice would you give to the successor ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What are your thoughts on the company culture and work environment "
"?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>What are your thoughts on the recent changes in the company ? Were "
"you able to adapt ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>What are your thoughts on working with your team ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What are your thoughts on your overall experience during the "
"probationary period ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What aspects of management and leadership at the company were most "
"effective in helping you succeed ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What aspects of your role have you enjoyed the most/least ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>What change would you see in the company ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>What further actions (e.g., additional training, mentoring) do you "
"recommend to support the employee’s continued development ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>What is your overall feeling about the previous year ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What key skills should the management team focus on for a smooth "
"transition ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What motivates you to continue working with us, and what are your "
"career aspirations here ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>What were the key challenges in your position ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Which emotions were prevalent ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>Which skills do you possess that you feel the company is not "
"utilizing ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>Which skills would you like to prioritize for training, and why "
"?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Who would you see as your successor ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Would you need extra help from the management team ?</strong>"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_hr_appraisal_goal_tag_name_uniq
msgid "A tag with the same name already exists."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__accessible_employee_feedback
msgid "Accessible Employee Feedback"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__accessible_manager_feedback
msgid "Accessible Manager Feedback"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "Action Needed"
msgstr "Gərəkli Əməliyyat"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__active
msgid "Active"
msgstr "Aktiv"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_ids
msgid "Activities"
msgstr "Fəaliyyətlər"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Faəliyyət istisnaetmə İşarəsi"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid "Activity State"
msgstr "Fəaliyyət Statusu"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Activity Type Icon"
msgstr "Fəaliyyət Növü ikonu"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_config_templates_action
msgid "Add a new template"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Add existing contacts..."
msgstr "Mövcud əlaqələri əlavə et..."

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Another appraisal with the same people is already ongoing."
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree2
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__appraisal_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_all
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_department
#: model:ir.ui.menu,name:hr_appraisal.menu_appraisal_analysis_report
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_graph
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Analysis"
msgstr "Qiymətləndirmə Təhlili"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_note
msgid "Appraisal Assessment Note"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_confirm_mail_template
msgid "Appraisal Confirm Mail Template"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_count
msgid "Appraisal Count"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_close
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Appraisal Date"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__employee_id
msgid "Appraisal Employee"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Form to Fill"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal
msgid "Appraisal Goal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal_tag
msgid "Appraisal Goal Tags"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Officer"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_plan_posted
msgid "Appraisal Plan Posted"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisal_properties_definition
msgid "Appraisal Properties"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Request"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request
msgid "Appraisal Requested"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__pending
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Sent"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_report
msgid "Appraisal Statistics"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_templates_action
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_template_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_template_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_template_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_template_view_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Appraisal Template"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__custom_appraisal_template_id
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_template_menu
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisal Templates"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
msgid ""
"Appraisal for %(appraisal_title)s should be using template "
"\"%(template_name)s\" instead of \"%(wrong_template_name)s\""
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal for %(employee)s on %(date)s"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal for %s to fill"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal of %s"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal to fill"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_from_department
msgid "Appraisal to start"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.ir_cron_scheduler_appraisal_ir_actions_server
msgid "Appraisal: Run employee appraisal"
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.js:0
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_root
#: model:ir.ui.menu,name:hr_appraisal.menu_open_view_hr_appraisal_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_departure_wizard_view_form
msgid "Appraisals"
msgstr "Qiymətləndirmələr"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisals Automation"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisals Plans"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisals_to_process_count
msgid "Appraisals to Process"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Archived"
msgstr "Arxivləndi"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Ask to fill a survey to other employees"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__assessment_note_ids
msgid "Assessment Note"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.action_hr_appraisal_goal
msgid ""
"Assign Goals to motivate your Employees and keep track of their objectives "
"between Appraisals."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_attachment_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_attachment_count
msgid "Attachment Count"
msgstr "Qoşma Sayı"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__author_id
msgid "Author"
msgstr "Müəllif"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_plan
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_plan
msgid "Automatically Generate Appraisals"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"Automatically creates the confirmed appraisals when Next Appraisal Date is "
"reached"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__avatar_1920
msgid "Avatar"
msgstr "avatar"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__avatar_128
msgid "Avatar 128"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_base
msgid "Basic Employee"
msgstr "Əsas İşçi"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body_has_template_value
msgid "Body content is the same as the template"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_calendar_event
msgid "Calendar Event"
msgstr "Təqvim Tədbiri"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__can_edit_body
msgid "Can Edit Body"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__can_request_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__can_request_appraisal
msgid "Can Request Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_employee_publish
msgid "Can See Employee Publish"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_manager_publish
msgid "Can See Manager Publish"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Cancel"
msgstr "Ləğv edin"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel Future Appraisals"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel all appraisal after contract end date."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__cancel
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__cancel
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Cancelled"
msgstr "Ləğv olundu"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__date_close
msgid "Closing date of the current appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__previous_appraisal_date
msgid "Closing date of the previous appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__color
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__color
msgid "Color"
msgstr "Rəng"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_company
msgid "Companies"
msgstr "Şirkətlər"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__company_id
msgid "Company"
msgstr "Şirkət"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Compose Email"
msgstr "Email tərtib et"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_configuration
msgid "Configuration"
msgstr "Konfiqurasiya"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Confirm"
msgstr "Təsdiq edin"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__pending
msgid "Confirmed"
msgstr "Təsdiq olundu"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body
msgid "Contents"
msgstr "Məzmun"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__create_date
msgid "Create Date"
msgstr "Tarix Yaradın"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_first_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_first_appraisal
msgid "Create a first Appraisal after"
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Create a new employee"
msgstr "Yeni işçi yarat"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Create a new tag"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_next_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_next_appraisal
msgid "Create a second Appraisal after"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_after_recruitment
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_after_recruitment
msgid "Create an Appraisal after recruitment"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.action_hr_appraisal_goal
msgid "Create new goals"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Create new tags to use on Employee's goals"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Created By"
msgstr "Tərəfindən yaradılıb"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Created On"
msgstr "Tarixdə yaradıldı"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Creation Date"
msgstr "Yaradılma Tarixi"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__next_appraisal_date
msgid "Date where the new appraisal will be automatically created"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__deadline
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Deadline"
msgstr "Son Tarix"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Deadline Date"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "Deadline:"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_goal.py:0
msgid "Deadline: %s"
msgstr "Son tarix: %s"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Default Template"
msgstr "Defolt Şablon"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"Define the next appraisal date automatically based on Appraisal's History"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "Delete"
msgstr "Silin"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_department
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__department_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Department"
msgstr "Şöbə"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Getmə Sehrbazı"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__description
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__description
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Description"
msgstr "Təsvir"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__display_name
msgid "Display Name"
msgstr "Göstəriləcək Ad"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Do you want to"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__done
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Done"
msgstr "Hazırdır"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_mail_template
msgid "Email Templates"
msgstr "Email Şablonları"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Employee"
msgstr "İşçi"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal
msgid "Employee Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_template
msgid "Employee Appraisal Template"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_autocomplete_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_autocomplete_ids
msgid "Employee Autocomplete"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__appraisal_employee_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Employee Feedback"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_published
msgid "Employee Feedback Published"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_template
msgid "Employee Feedback Template"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__name
msgid "Employee Name"
msgstr "İşçinin Adı"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_user_id
msgid "Employee User"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee's Feedback"
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.js:0
msgid "Employees"
msgstr "İşçilər"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_note
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__assessment_note_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_note
msgid "Evaluation Scale"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Exceeds expectations"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Extended Filters..."
msgstr "Genişləndirilmiş Filtrlər..."

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_external
msgid "External"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Fill appraisal for %s"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_final_interview
msgid "Final Interview"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Final Interview Date"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__assessment_note
msgid "Final Rating"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_follower_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_follower_ids
msgid "Followers"
msgstr "İzləyicilər"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_partner_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_partner_ids
msgid "Followers (Partners)"
msgstr "İzləyicilər (Tərəfdaşlar)"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Gözəl şriftli ikon, məsələn fa-tapşırıqlar"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Future Activities"
msgstr "Gələcək Fəaliyyətlər"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"Give one positive achievement that convinced you of the employee's\n"
"                    value."
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Goal"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Goal Tags"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_goal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_goal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_graph
msgid "Goals"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__goals_count
msgid "Goals Count"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Group By"
msgstr "Görə Qrupla"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Group by..."
msgstr "Qruplaşdır..."

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_confirm
msgid "HR: Appraisal Confirmation"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_request_from_employee
msgid "HR: Employee Appraisal Request"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_request
msgid "HR: Manager appraisal request"
msgstr ""

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_hardskills
msgid "Hard Skills"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__has_message
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__has_message
msgid "Has Message"
msgstr "Mesajı Var"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How could the employee improve?"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How do I feel about my own..."
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How do I feel about the company..."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__id
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__id
msgid "ID"
msgstr "ID"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon"
msgstr "Simvol"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisna fəaliyyəti göstərən simvol."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşarələnibsə, yeni mesajlara baxmalısınız."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşarələnibsə, bəzi mesajların çatdırılmasında xəta var."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_1920
msgid "Image"
msgstr "Şəkil"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_128
msgid "Image 128"
msgstr "Şəkil 128"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "In Progress"
msgstr "Həyata Keçirilməkdə"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "In progress Evaluations"
msgstr ""

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_internal
msgid "Internal"
msgstr "Daxili"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__final_interview
msgid "Interview"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_is_follower
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_is_follower
msgid "Is Follower"
msgstr "İzləyicidir"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__is_manager
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__is_manager
msgid "Is Manager"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__job_id
msgid "Job Position"
msgstr "İş Mövqeyi"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__lang
msgid "Language"
msgstr "Dil"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_id
msgid "Last Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_date
msgid "Last Appraisal Date"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Last Meeting"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late"
msgstr "Son"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late Activities"
msgstr "Ən son Əməliyyatlar"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_load_appraisal_demo_data
msgid "Load appraisal scenario"
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Load sample data"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"Long term (&gt; 6 months) career discussion, where does the employee\n"
"                    wants to go, how to help them reach this path?"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__template_id
msgid "Mail Template"
msgstr "Məktub Şablonu"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Manager"
msgstr "Menecer"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager Assessment will show here"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__appraisal_manager_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Manager Feedback"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_published
msgid "Manager Feedback Published"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_template
msgid "Manager Feedback Template"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_user_ids
msgid "Manager Users"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager's Feedback"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_mark_as_done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Mark as Done"
msgstr "Hazır kimi işarələ"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_count_display
msgid "Meeting Count"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_ids
msgid "Meetings"
msgstr "Görüşlər"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Meets expectations"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error
msgid "Message Delivery error"
msgstr "Mesajın Çatdırılmasında xəta"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mənim Fəaliyyətlərimin Son Tarixi "

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_my
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "My Appraisals"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "My Goals"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__name
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Name"
msgstr "Ad"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Needs improvement"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee_base.py:0
msgid "New and Pending Appraisals"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Növbəti Fəaliyyət Təqvimi Tədbiri"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Növbəti Fəaliyyətin Son Tarixi"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_summary
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_summary
msgid "Next Activity Summary"
msgstr "Növbəti Fəaliyyət Xülasəsi"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_id
msgid "Next Activity Type"
msgstr "Yeni Fəaliyyət Növü"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_search
msgid "Next Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__next_appraisal_date
msgid "Next Appraisal Date"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Next Meeting"
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "No Appraisals yet ..."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "No Meeting"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Note"
msgstr "Qeyd"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of Actions"
msgstr "Hərəkətlərin sayı"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of errors"
msgstr "Xətaların sayı"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Əməliyyat tələb edən mesajların sayı"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Çatdırılma xətası olan mesajların sayı"

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_user
msgid "Officer: Access all appraisals"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Ongoing"
msgstr "Davamlı"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__ongoing_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__ongoing_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__ongoing_appraisal_count
msgid "Ongoing Appraisal Count"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_goal.py:0
msgid "Operation not supported"
msgstr "Əməliyyat dəstəklənmir"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Opportunities"
msgstr "Fürsətlər"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "Overall experience feedback"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__parent_user_id
msgid "Parent User"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "People I Manage"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__previous_appraisal_date
msgid "Previous Appraisal Date"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Previous Appraisals"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__note
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Private Note"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Private note (only accessible to people set as managers)"
msgstr ""

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_programming
msgid "Programming"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__progression
msgid "Progress"
msgstr "İrəliləyiş vaxtı "

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_properties
msgid "Properties"
msgstr "Xüsusiyyətlər"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_public
msgid "Public Employee"
msgstr "Dövlət Qulluqçusu"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__rating_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__rating_ids
msgid "Ratings"
msgstr "Qiymətləndirmələr"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Ready"
msgstr "Hazır"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__recipient_ids
msgid "Recipients"
msgstr "Qəbuledicilər"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__related_partner_id
msgid "Related Partner"
msgstr "Əlaqədar Tərəfdaş"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/fields/appraisal_remaining_days.js:0
msgid "Remaining Days"
msgstr "Qalan Günlər"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__render_model
msgid "Rendering Model"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Reopen"
msgstr ""

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_report
msgid "Reporting"
msgstr "Hesabatlıq"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid "Request Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_create_multi_appraisals
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_employee_tree
msgid "Request Appraisals"
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_request_appraisal
msgid "Request an Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_user_id
msgid "Responsible User"
msgstr "Məsul İstifadəçi"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-in Çatdırılmasında xəta"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Search Appraisal"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Self Assessment will show here"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Send"
msgstr "Göndərin"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Send by email"
msgstr "Emaillə Göndər"

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_confirm
msgid ""
"Sent automatically to both employee and manager when appraisal is confirmed"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"Sent manually to the employee by the manager who wants to do an appraisal"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_request
msgid "Sent manually to the manager by the employee who wants an appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__sequence
msgid "Sequence"
msgstr "Ardıcıllıq"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Set default appraisal template"
msgstr ""

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_settings_action
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_configuration
msgid "Settings"
msgstr "Parametrlər"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "Short term (6-months) actions / decisions / objectives"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__show_employee_feedback_full
msgid "Show Employee Feedback Full"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__show_manager_feedback_full
msgid "Show Manager Feedback Full"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Show all records which has next action date is before today"
msgstr "Növbəti fəaliyyət tarixi bu günə qədər olan bütün qeydləri göstərin"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_softskills
msgid "Soft Skills"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_state
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Status"
msgstr "Status"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Fəaliyyətlərə əsaslanan status\n"
"Gecikmiş: Gözlənilən tarixdən keçib\n"
"Bu gün: Fəaliyyət tarixi bu gündür\n"
"Planlaşdırılıb: Gələcək fəaliyyətlər."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Strengths"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Strongly Exceed Expectations"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__subject
msgid "Subject"
msgstr "Mövzu"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Subject..."
msgstr "Mövzu..."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__tag_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_config_goal_tags
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_tag_view_tree
msgid "Tags"
msgstr "Etiketlər"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/mail_template.py:0
msgid ""
"Template %(template_name)s is necessary for appraisal requests and may not "
"be removed."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"Thanks to your Appraisal Plan, without any new manual Appraisal, the new "
"Appraisal will be automatically created on %s."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The \"Manager Feedback Published\" cannot be changed by an employee."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The appraisal's status has been set to Done by %s"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__last_appraisal_date
msgid "The date of the last appraisal"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__next_appraisal_date
msgid ""
"The date of the next appraisal is computed by the appraisal plan's dates "
"(first appraisal + periodicity)."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_res_company_positif_number_months
msgid "The duration time must be bigger or equal to 1 month."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"The employee %(employee)s arrived %(months)s months ago. The appraisal is "
"created and you can fill it here."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The employee feedback cannot be changed by managers."
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/fields/boolean_confirm.js:0
msgid ""
"The employee's feedback will be published without their consent. Do you "
"really want to publish it? This action will be logged in the chatter."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"The last appraisal of %(employee)s was %(months)s months ago. The appraisal "
"is created and you can fill it here."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The manager feedback cannot be changed by an employee."
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__assessment_note
msgid "This field is not visible to the Employee."
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Threats"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__new
msgid "To Confirm"
msgstr "Təsdiq edilməli"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "To Do"
msgstr "Görülməli iş"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__new
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "To Start"
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "To create an appraisal, you need at least 2 employees"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Today Activities"
msgstr "Bugünkü Fəaliyyətlər"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_training
msgid "Training"
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Try the backend and reporting:"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Qeyddəki istisna fəaliyyət növü."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__uncomplete_goals_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__uncomplete_goals_count
msgid "Uncomplete Goals Count"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Unpublished"
msgstr "Dərc edilməyib"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_search
msgid "Upcoming Appraisals"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid ""
"Use this area to write the content that will be displayed for the Employee."
"                                         You can use a lot of options with "
"the html editor, accessible by pressing / on the keyboard."
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid ""
"Use this area to write the content that will be displayed for the Manager."
"                                         You can use a lot of options with "
"the html editor, accessible by pressing / on the keyboard."
msgstr ""

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_users
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_user_id
msgid "User"
msgstr "İstifadəçi"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__user_body
msgid "User Contents"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__waiting_feedback
msgid "Waiting Feedback from Employee/Managers"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Weaknesses"
msgstr ""

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website Messages"
msgstr "Veb sayt Mesajları"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website communication history"
msgstr "Veb saytın kommunikasiya tarixçəsi"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "What are my best achievement(s) since my last appraisal?"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"What are my short and long-term goals with the company, and for my\n"
"                    career?"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"What has been the most challenging aspect of my work this past year and\n"
"                    why?"
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "What would I need to improve my work?"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"When the Appraisals plan is saved, it will overwrite all Next Appraisal "
"Dates for employees without ongoing appraisals."
msgstr ""

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "Which parts of my job do I most / least enjoy?"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"You arrived %s months ago. Your appraisal is created and you can fill it "
"here."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee.py:0
msgid ""
"You cannot delete an employee who is a goal's manager, archive it instead."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "You cannot delete appraisal which is not in draft or cancelled state"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee.py:0
msgid "You cannot set 'Next Appraisal Date' in the past."
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Your Appraisal has been completed"
msgstr ""

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"Your last appraisal was %s months ago. Your appraisal is created and you can"
" fill it here."
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "create a new one"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "e.g. Annual Appraisal"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
msgid "e.g. Improve your English level"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "e.g. John Doe"
msgstr "məs. Con Dou"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_tag_view_tree
msgid "e.g. Remediation, Team, Improvement plan, Career change, ..."
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months after recruitment, then after"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months, then every"
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months."
msgstr ""

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "once published"
msgstr ""

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "or"
msgstr "və yaxud"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"{{ hasattr(object, 'name') and object.name or '' }} requests an Appraisal"
msgstr ""

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_confirm
msgid "{{ object.employee_id.name }}: Appraisal Confirmed"
msgstr ""
