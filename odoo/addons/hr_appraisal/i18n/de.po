# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal
# 
# Translators:
# Wil Odoo, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "%(user)s decided, as %(role)s, to publish the employee's feedback"
msgstr ""
"%(user)s hat als %(role)s entschieden, das Mitarbeiterfeedback zu "
"veröffentlichen"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_template.py:0
msgid "%s (copy)"
msgstr "%s (Kopie)"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "%s's Goals"
msgstr "Ziele von %s"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "1 Meeting"
msgstr "1 Meeting"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__100
msgid "100%"
msgstr "100 %"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__025
msgid "25%"
msgstr "25 %"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__module_hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "360 Feedback"
msgstr "360°-Feedback"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__050
msgid "50%"
msgstr "50 %"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__075
msgid "75%"
msgstr "75 %"

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_confirm
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        An appraisal of <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t> has been confirmed.\n"
"                        <br/><br/>\n"
"                        Please schedule an appraisal date together.\n"
"                        <br/><br/>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Die Beurteilung von <t t-out=\"ctx.get('employee_to_name', 'employee')\">Mitarbeiter</t> wurde bestätigt.\n"
"                        <br/><br/>\n"
"                        Bitte vereinbaren Sie gemeinsam einen Termin für das Mitarbeitergespräch.\n"
"                        <br/><br/>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    Beurteilung ansehen\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t>,\n"
"                        <br/>\n"
"                        <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t> (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>) wishes an appraisal.\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hallo <t t-out=\"ctx.get('employee_to_name', 'employee')\">Mitarbeiter</t>,\n"
"                        <br/>\n"
"                        <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t> (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>) wünscht eine Beurteilung.\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Jährliche Beurteilung.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    Beurteilung ansehen\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t>,\n"
"                        <br/>\n"
"                        An appraisal has been requested by <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t>\n"
"                        <br/>\n"
"                        (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>).\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <br/>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hallo <t t-out=\"ctx.get('employee_to_name', 'employee')\">Mitarbeiter</t>,\n"
"                        <br/>\n"
"                        <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t> hat um eine Mitarbeiterbeurteilung gebeten\n"
"                        <br/>\n"
"                        (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>).\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Jährliche Beurteilung.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    Beurteilung ansehen\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <br/>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Describe something that made you proud, a piece of work positive for\n"
"                        the company.\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Beschreiben Sie etwas, worauf Sie stolz sind, eine positive Arbeit für\n"
"                        Ihr Unternehmen.\n"
"                    </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Did you face new difficulties? Did you confront yourself to new\n"
"                        obstacles?\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Sind Sie mit neuen Schwierigkeiten konfrontiert worden? Haben Sie sich neuen\n"
"                        Hindernissen gestellt?\n"
"                    </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Every job has strong points, what are, in your opinion, the tasks that\n"
"                        you enjoy the most/the least?\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Jeder Job hat seine Vorzüge. Welche Aufgaben haben Sie\n"
"                        am meisten/am wenigsten genossen?\n"
"                    </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        From a manager point of view, how could you help the employee to\n"
"                        overcome their weaknesses?\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Aus der Perspektive eines Managers, wie würden Sie Ihren Mitarbeitern dabei helfen,\n"
"                         ihre Schwächen zu überwinden?\n"
"                    </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        How can the company help you with your need and objectives in order\n"
"                        for you to reach your goals and look for the best collaboration.\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Wie kann das Unternehmen Ihnen bei Ihren Bedürfnissen und Zielen helfen,\n"
"                        damit Sie Ihre Ziele erreichen und die beste Zusammenarbeit anstreben können.\n"
"                    </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        How do you see the employee in the future, do your vision follow the\n"
"                        employee's desire?\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Wo sehen Sie den Mitarbeiter in der Zukunft, stimmt Ihre Vision mit der Einstellung\n"
"                        des Mitarbeiters überein?\n"
"                    </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Some achievements comforting you in their strengths to face job's\n"
"                        issues.\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Einige persönliche Erfolge, die Sie dabei stärken, Schwierigkeiten\n"
"                        im Job zu bewältigen.\n"
"                    </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Autonomy</em>"
msgstr "<em>Autonomie</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Culture/Behavior:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-1\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Kultur/Verhalten:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-1\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Do you need rapid answer to the current situation?</em>"
msgstr ""
"<em>Brauchen Sie eine schnelle Antwort auf die aktuelle Situation?</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Give an example of long-term objective (&gt; 6 months)</em>"
msgstr ""
"<em>Nennen Sie ein Beispiel für eine langfristige Zielvorgabe (&gt; 6 "
"Monate)</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Give an example of short-term objective (&lt; 6 months)</em>"
msgstr ""
"<em>Nennen Sie ein Beispiel für eine kurzfristige Zielvorgabe (&lt; 6 "
"Monate)</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Internal Communication:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-2\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Interne Kommunikation:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-2\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Job's content:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-3\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Jobinhalt:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-3\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Pro-activity</em>"
msgstr "<em>Proaktivität</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Remuneration:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-5\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Entlohnung:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-5\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Stress Resistance</em>"
msgstr "<em>Stressresistenz</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Teamwork</em>"
msgstr "<em>Teamwork</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Time Management</em>"
msgstr "<em>Zeitmanagement</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Work organization:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-4\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Arbeitsorganisation:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-4\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee_base.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Schedule an appraisal\n"
"                        </p><p>\n"
"                            Plan appraisals with your colleagues, collect and discuss feedback.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Planen Sie eine Mitarbeiterbeurteilung\n"
"                        </p><p>\n"
"                            Planen Sie Mitarbeiterbeurteilungen mit Ihren Kollegen, sammeln Sie Feedback und tauschen Sie sich aus.\n"
"                        </p>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<small role=\"img\" class=\"fa fa-circle text-success\" invisible=\"state !="
" 'pending' or waiting_feedback\" aria-label=\"Ready\" title=\"Ready\"/>"
msgstr ""
"<small role=\"img\" class=\"fa fa-circle text-success\" invisible=\"state !="
" 'pending' or waiting_feedback\" aria-label=\"Bereit\" title=\"Bereit\"/>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"fw-bold\">Meeting: </span>"
msgstr "<span class=\"fw-bold\">Meeting: </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"last_appraisal_state == 'done'\">\n"
"                            Ongoing\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\" invisible=\"last_appraisal_state != 'done'\">\n"
"                            Latest\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Appraisal\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"last_appraisal_state == 'done'\">\n"
"                            Laufende\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\" invisible=\"last_appraisal_state != 'done'\">\n"
"                            Neueste\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Beurteilung\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Appraisal\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Letzte Beurteilung\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_text\">Appraisals</span>"
msgstr "<span class=\"o_stat_text\">Beurteilungen</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_text\">Goals</span>"
msgstr "<span class=\"o_stat_text\">Ziele</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-end\" invisible=\"employee_feedback_published\">Not Visible to Manager</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state == 'new'\">Visible to Manager</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state != 'new'\">Visible &amp; Editable by Manager</span>"
msgstr ""
"<span class=\"text-end\" invisible=\"employee_feedback_published\">Nicht für Manager sichtbar</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state == 'new'\">Für Manager sichtbar</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state != 'new'\">Für Manager sichtbar &amp; bearbeitbar</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-end\" invisible=\"manager_feedback_published or not can_see_manager_publish\">Not Visible to Employee</span>\n"
"                                            <span class=\"text-end\" invisible=\"not manager_feedback_published or not can_see_manager_publish\">Visible to Employee</span>"
msgstr ""
"<span class=\"text-end\" invisible=\"manager_feedback_published or not can_see_manager_publish\">Nicht sichtbar für Mitarbeiter</span>\n"
"                                            <span class=\"text-end\" invisible=\"not manager_feedback_published or not can_see_manager_publish\">Sichtbar für Mitarbeiter</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
msgid ""
"<span invisible=\"not is_manager or (next_appraisal_date or not ongoing_appraisal_count)\">\n"
"                    Ongoing\n"
"                </span>"
msgstr ""
"<span invisible=\"not is_manager or (next_appraisal_date or not ongoing_appraisal_count)\">\n"
"                    Laufend\n"
"                </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Evaluation\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Beurteilung\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Feedback\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Feedback\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Improvements\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Verbesserungen\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My feelings\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Meine Gefühle\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My future\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Meine Zukunft\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My work\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Meine Arbeit\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong class=\"text-o-color-4\">\n"
"                                    LEAST\n"
"                                    <br>\n"
"                                </strong>"
msgstr ""
"<strong class=\"text-o-color-4\">\n"
"                                    AM WENIGSTEN\n"
"                                    <br>\n"
"                                </strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong class=\"text-o-color-4\">\n"
"                                    MOST\n"
"                                    <br>\n"
"                                </strong>"
msgstr ""
"<strong class=\"text-o-color-4\">\n"
"                                    AM MEISTEN\n"
"                                    <br>\n"
"                                </strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>360 feedback :</strong>"
msgstr "<strong>360-Feedback :</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Any particular remark on the training ?</strong>"
msgstr "<strong>Irgendeine Anmerkung zur Schulung?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Are you happy with your current role ?</strong>"
msgstr "<strong>Sind Sie zufrieden mit Ihrer aktuellen Rolle?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>Can the employee accurately recall and explain the key concepts from"
" the training ?</strong>"
msgstr ""
"<strong>Kann sich der Mitarbeiter genau an die Schlüsselkonzepte der "
"Schulung erinnern und diese erklären?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Career objective</strong>"
msgstr "<strong>Karriereziele</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Career opportunities</strong>"
msgstr "<strong>Karrierechancen</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Company Feedback</strong>"
msgstr "<strong>Unternehmensfeedback</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Could you provide examples ?</strong>"
msgstr "<strong>Können Sie Beispiele nennen?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Create your SWOT</strong>"
msgstr "<strong>Erstellen Sie Ihre Analyse</strong>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_gantt
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_gantt
msgid "<strong>Date — </strong>"
msgstr "<strong>Datum — </strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Do you seek any particular career path ?</strong>"
msgstr "<strong>Folgen Sie einem bestimmten Karriereweg?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Feedback on management</strong>"
msgstr "<strong>Feedback zur Geschäftsführung</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>GOALS :</strong>"
msgstr "<strong>ZIELE:</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Handover to manage inside the team</strong>"
msgstr "<strong>Aufgabenübertragung im Team</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>Has the employee effectively integrated the new skills or knowledge "
"into their daily work tasks ?</strong>"
msgstr ""
"<strong>Hat der Mitarbeiter die neuen Kompetenzen oder Kenntnisse effektiv "
"in seine täglichen Arbeitsaufgaben integriert?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How actively did the employee participate during the training "
"sessions (e.g., asking questions, contributing to discussions) ?</strong>"
msgstr ""
"<strong>Wie aktiv hat sich der Mitarbeiter an den Schulungen beteiligt (z. "
"B. Fragen gestellt, sich an Diskussionen beteiligt)?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How confident do you feel in using the skills and knowledge gained "
"from the training in your work ?</strong>"
msgstr ""
"<strong>Wie sicher fühlen Sie sich bei der Anwendung der in der Schulung "
"erworbenen Fähigkeiten und Kenntnisse bei Ihrer Arbeit?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>How do you feel about the support and resources provided to you "
"during this period ?</strong>"
msgstr ""
"<strong>Was halten Sie von der Unterstützung und den Ressourcen, die Ihnen "
"in dieser Zeit zur Verfügung gestellt wurden?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How helpful were the provided materials ?</strong>"
msgstr ""
"<strong>Wie nützlich waren die zur Verfügung gestellten "
"Materialien?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How would you asses your level before the training ?</strong>"
msgstr ""
"<strong>Wie würden Sie Ihr Niveau vor dem Training einschätzen?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How would you assess your level post training ?</strong>"
msgstr ""
"<strong>Wie würden Sie Ihr Niveau nach dem Training einschätzen?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How would you rate the employee's proficiency in the skill on a "
"scale from 1 to 10 ?</strong>"
msgstr ""
"<strong>Wie würden Sie die Kompetenzen des Mitarbeiters auf einer Skala von "
"1 bis 10 bewerten?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Management feedback and assessment</strong>"
msgstr "<strong>Feedback zum Management und Bewertung</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Motivations</strong>"
msgstr "<strong>Motivation</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>On a scale of 1 to 10, how would you rate the employee’s overall "
"improvement since the training ?</strong>"
msgstr ""
"<strong>Wie würden Sie auf einer Skala von 1 bis 10 die allgemeine "
"Verbesserung des Mitarbeiters seit der Schulung bewerten?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Opportunity</strong>"
msgstr "<strong>Chancen</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Overall experience feedback</strong>"
msgstr "<strong>Allgemeines Feedback zur Erfahrung</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Overall personal experience</strong>"
msgstr "<strong>Allgemeine persönliche Erfahrung</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Position succession and candidate suggestion</strong>"
msgstr "<strong>Stellennachfolge und Kandidatenvorschlag</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>SWOT results</strong>"
msgstr "<strong>Ergebnisse der Analyse</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>SWOT</strong>"
msgstr "<strong>Allgemeine Analyse</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Skills at stakes :</strong>"
msgstr "<strong>Betroffene Kompetenzen:</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Strength</strong>"
msgstr "<strong>Stärken</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to keep doing ?</strong>"
msgstr "<strong>Dinge, die man weiterhin tun sollte?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to start doing ?</strong>"
msgstr "<strong>Dinge, die man einführen sollte?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to stop doing ?</strong>"
msgstr "<strong>Dinge, die man abschaffen sollte?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Threats</strong>"
msgstr "<strong>Risiken</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Time to assess the leadership team:</strong>"
msgstr "<strong>Zeit zur Bewertung des Führungsteams:</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Training assessment</strong>"
msgstr "<strong>Schulungsbewertung</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>We wish you the best of success and sincerely thank you for your "
"invaluable contribution to the company's achievements.</strong>"
msgstr ""
"<strong>Wir wünschen Ihnen viel Erfolg und danken Ihnen herzlich für Ihren "
"unschätzbaren Beitrag zum Erfolg des Unternehmens.</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Weakness</strong>"
msgstr "<strong>Schwächen</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What advice do you have for improving management's leadership and "
"support for future employees ?</strong>"
msgstr ""
"<strong>Welche Ratschläge haben Sie, um die Verwaltung und Unterstützung "
"zukünftiger Mitarbeiter durch das Management zu verbessern?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>What advice would you give to the successor ?</strong>"
msgstr "<strong>Welchen Rat würden Sie dem Nachfolger geben?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What are your thoughts on the company culture and work environment "
"?</strong>"
msgstr ""
"<strong>Was denken Sie über die Unternehmenskultur und das "
"Arbeitsumfeld?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>What are your thoughts on the recent changes in the company ? Were "
"you able to adapt ?</strong>"
msgstr ""
"<strong>Was denken Sie über die jüngsten Veränderungen im Unternehmen? "
"Konnten Sie sich anpassen?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>What are your thoughts on working with your team ?</strong>"
msgstr ""
"<strong>Was halten Sie von der Zusammenarbeit mit Ihrem Team?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What are your thoughts on your overall experience during the "
"probationary period ?</strong>"
msgstr ""
"<strong>Wie war Ihre allgemeine Erfahrung während der Probezeit?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What aspects of management and leadership at the company were most "
"effective in helping you succeed ?</strong>"
msgstr ""
"<strong>Welche Aspekte des Managements und der Führung im Unternehmen haben "
"Ihnen am meisten zum Erfolg verholfen?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What aspects of your role have you enjoyed the most/least ?</strong>"
msgstr ""
"<strong>Welche Aspekte Ihrer Aufgabe haben Ihnen am meisten/am wenigsten "
"Spaß gemacht?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>What change would you see in the company ?</strong>"
msgstr ""
"<strong>Welche Veränderungen würden Sie im Unternehmen sehen?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>What further actions (e.g., additional training, mentoring) do you "
"recommend to support the employee’s continued development ?</strong>"
msgstr ""
"<strong>Welche weiteren Maßnahmen (z. B. zusätzliche Schulungen, "
"Praxisanleitung) empfehlen Sie, um die weitere Entwicklung des Mitarbeiters "
"zu unterstützen?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>What is your overall feeling about the previous year ?</strong>"
msgstr "<strong>Wie beurteilen Sie das vergangene Jahr insgesamt?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What key skills should the management team focus on for a smooth "
"transition ?</strong>"
msgstr ""
"<strong>Auf welche Schlüsselkompetenzen sollte sich das Managementteam "
"konzentrieren, um einen reibungslosen Übergang zu gewährleisten?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What motivates you to continue working with us, and what are your "
"career aspirations here ?</strong>"
msgstr ""
"<strong>Was motiviert Sie, weiterhin mit uns zusammenzuarbeiten, und wie "
"sehen Ihre beruflichen Ziele aus?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>What were the key challenges in your position ?</strong>"
msgstr ""
"<strong>Welche waren die größten Herausforderungen in Ihrem Job?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Which emotions were prevalent ?</strong>"
msgstr "<strong>Welche Gefühle waren vorherrschend?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>Which skills do you possess that you feel the company is not "
"utilizing ?</strong>"
msgstr ""
"<strong>Welche Kompetenzen besitzen Sie, die Ihrer Meinung nach vom "
"Unternehmen nicht genutzt werden?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>Which skills would you like to prioritize for training, and why "
"?</strong>"
msgstr ""
"<strong>Welche Kompetenzen möchten Sie vorrangig schulen und warum?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Who would you see as your successor ?</strong>"
msgstr "<strong>Wen würden Sie als Nachfolger sehen?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Would you need extra help from the management team ?</strong>"
msgstr ""
"<strong>Benötigen Sie zusätzliche Unterstützung durch das "
"Managementteam?</strong>"

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_hr_appraisal_goal_tag_name_uniq
msgid "A tag with the same name already exists."
msgstr "Es besteht bereits ein Stichwort mit demselben Namen."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__accessible_employee_feedback
msgid "Accessible Employee Feedback"
msgstr "Zugängliches Mitarbeiter-Feedback"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__accessible_manager_feedback
msgid "Accessible Manager Feedback"
msgstr "Zugängliches Manager-Feedback"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__active
msgid "Active"
msgstr "Aktiv"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_ids
msgid "Activities"
msgstr "Aktivitäten"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitätsausnahme-Dekoration"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid "Activity State"
msgstr "Status der Aktivität"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Activity Type Icon"
msgstr "Symbol des Aktivitätstyps"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_config_templates_action
msgid "Add a new template"
msgstr "Fügen Sie eine neue Vorlage hinzu"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Add existing contacts..."
msgstr "Bestehende Kontakte hinzufügen …"

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Another appraisal with the same people is already ongoing."
msgstr "Eine andere Beurteilung mit derselben Person läuft bereits."

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree2
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__appraisal_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Appraisal"
msgstr "Mitarbeiterbeurteilung"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_all
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_department
#: model:ir.ui.menu,name:hr_appraisal.menu_appraisal_analysis_report
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_graph
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Analysis"
msgstr "Beurteilungsanalyse"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_note
msgid "Appraisal Assessment Note"
msgstr "Bewertungsnotiz für Beurteilung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_confirm_mail_template
msgid "Appraisal Confirm Mail Template"
msgstr "E-Mail-Vorlage zur Bestätigung der Beurteilung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_count
msgid "Appraisal Count"
msgstr "Anzahl Beurteilungen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_close
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Appraisal Date"
msgstr "Datum der Beurteilung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__employee_id
msgid "Appraisal Employee"
msgstr "Zu beurteilender Mitarbeiter"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Form to Fill"
msgstr "Auszufüllendes Beurteilungsformular"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal
msgid "Appraisal Goal"
msgstr "Ziel der Beurteilung"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal_tag
msgid "Appraisal Goal Tags"
msgstr "Stichwörter für Beurteilungsziele"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Officer"
msgstr "Beurteilungsbeauftragter"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_plan_posted
msgid "Appraisal Plan Posted"
msgstr "Beurteilungsplan Veröffentlicht"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisal_properties_definition
msgid "Appraisal Properties"
msgstr "Beurteilungseigenschaften"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Request"
msgstr "Beurteilungsanfrage"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request
msgid "Appraisal Requested"
msgstr "Angefragte Mitarbeiterbeurteilung"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__pending
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Sent"
msgstr "Beurteilung versendet"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_report
msgid "Appraisal Statistics"
msgstr "Beurteilungsstatistiken"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_templates_action
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_template_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_template_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_template_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_template_view_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Appraisal Template"
msgstr "Beurteilungsvorlage"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__custom_appraisal_template_id
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_template_menu
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisal Templates"
msgstr "Beurteilungsvorlagen"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
msgid ""
"Appraisal for %(appraisal_title)s should be using template "
"\"%(template_name)s\" instead of \"%(wrong_template_name)s\""
msgstr ""
"Die Beurteilung für %(appraisal_title)s sollte die Vorlage "
"„%(template_name)s“ statt „%(wrong_template_name)s“ verwenden."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal for %(employee)s on %(date)s"
msgstr "Beurteilung für %(employee)s am %(date)s"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal for %s to fill"
msgstr "Auszufüllende Beurteilung für %s"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal of %s"
msgstr "Beurteilung von %s"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal to fill"
msgstr "Auszufüllende Beurteilung"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_from_department
msgid "Appraisal to start"
msgstr "Anstehende Beurteilungen"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.ir_cron_scheduler_appraisal_ir_actions_server
msgid "Appraisal: Run employee appraisal"
msgstr "Beurteilung: Mitarbeiterbeurteilung durchführen"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.js:0
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_root
#: model:ir.ui.menu,name:hr_appraisal.menu_open_view_hr_appraisal_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_departure_wizard_view_form
msgid "Appraisals"
msgstr "Mitarbeiterbeurteilung"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisals Automation"
msgstr "Beurteilungsautomatisierung"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisals Plans"
msgstr "Beurteilungspläne"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisals_to_process_count
msgid "Appraisals to Process"
msgstr "Durchzuführende Beurteilungen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Archived"
msgstr "Archiviert"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Ask to fill a survey to other employees"
msgstr "Bitten Sie andere Mitarbeiter an einer Umfrage teilzunehmen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__assessment_note_ids
msgid "Assessment Note"
msgstr "Bewertungsnotiz"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.action_hr_appraisal_goal
msgid ""
"Assign Goals to motivate your Employees and keep track of their objectives "
"between Appraisals."
msgstr ""
"Weisen Sie Ihren Mitarbeitern Ziele zu, um sie zu motivieren und ihre Ziele "
"zwischen den Beurteilungen zu verfolgen."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_attachment_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__author_id
msgid "Author"
msgstr "Autor"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_plan
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_plan
msgid "Automatically Generate Appraisals"
msgstr "Beurteilungen automatisch generieren"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"Automatically creates the confirmed appraisals when Next Appraisal Date is "
"reached"
msgstr ""
"Erstellt automatisch die bestätigten Beurteilungen, wenn das nächste "
"Beurteilungsdatum erreicht ist."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__avatar_1920
msgid "Avatar"
msgstr "Avatar"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_base
msgid "Basic Employee"
msgstr "Basismitarbeiter"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body_has_template_value
msgid "Body content is the same as the template"
msgstr "Textinhalt ist derselbe wie die Vorlage"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_calendar_event
msgid "Calendar Event"
msgstr "Kalender-Ereignis"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__can_edit_body
msgid "Can Edit Body"
msgstr "Darf Inhalt bearbeiten"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__can_request_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__can_request_appraisal
msgid "Can Request Appraisal"
msgstr "Kann um Beurteilung bitten"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_employee_publish
msgid "Can See Employee Publish"
msgstr "Kann sehen, wenn Mitarbeiter veröffentlicht"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_manager_publish
msgid "Can See Manager Publish"
msgstr "Kann sehen, wenn Manager veröffentlicht"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel Future Appraisals"
msgstr "Zukünftige Beurteilungen abbrechen"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel all appraisal after contract end date."
msgstr "Alle geplanten Beurteilungen nach Vertragsauflösung abbrechen."

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__cancel
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__cancel
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Cancelled"
msgstr "Abgebrochen"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__date_close
msgid "Closing date of the current appraisal"
msgstr "Abschlussdatum der aktuellen Beurteilung"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__previous_appraisal_date
msgid "Closing date of the previous appraisal"
msgstr "Abschlussdatum der vorherigen Beurteilung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__color
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__color
msgid "Color"
msgstr "Farbe"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Compose Email"
msgstr "E-Mail verfassen"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen "

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_configuration
msgid "Configuration"
msgstr "Konfiguration"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Confirm"
msgstr "Bestätigen"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__pending
msgid "Confirmed"
msgstr "Bestätigt"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body
msgid "Contents"
msgstr "Inhalte"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__create_date
msgid "Create Date"
msgstr "Erstellt am"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_first_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_first_appraisal
msgid "Create a first Appraisal after"
msgstr "Eine erste Beurteilung erstellen nach"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Create a new employee"
msgstr "Einen neuen Mitarbeiter anlegen"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Create a new tag"
msgstr "Neues Stichwort erstellen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_next_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_next_appraisal
msgid "Create a second Appraisal after"
msgstr "Eine zweite Beurteilung erstellen nach"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_after_recruitment
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_after_recruitment
msgid "Create an Appraisal after recruitment"
msgstr "Eine Beurteilung nach der Einstellung erstellen"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.action_hr_appraisal_goal
msgid "Create new goals"
msgstr "Neue Ziele erstellen"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Create new tags to use on Employee's goals"
msgstr "Erstellen Sie neue Stichwörter für die Ziele der Mitarbeiter"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Created By"
msgstr "Erstellt von"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Created On"
msgstr "Erstellt am"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Creation Date"
msgstr "Erstellungsdatum"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__next_appraisal_date
msgid "Date where the new appraisal will be automatically created"
msgstr "Datum, an dem die neue Beurteilung automatisch erstellt wird"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__deadline
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Deadline"
msgstr "Frist"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Deadline Date"
msgstr "Frist"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "Deadline:"
msgstr "Frist:"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_goal.py:0
msgid "Deadline: %s"
msgstr "Frist: %s"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Default Template"
msgstr "Standardvorlage"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"Define the next appraisal date automatically based on Appraisal's History"
msgstr ""
"Definieren Sie das nächste Beurteilungsdatum automatisch anhand der "
"Beurteilungshistorie"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "Delete"
msgstr "Löschen"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_department
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__department_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Department"
msgstr "Abteilung"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Austrittassistent"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__description
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__description
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Description"
msgstr "Beschreibung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Do you want to"
msgstr "Möchten Sie"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__done
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Done"
msgstr "Erledigt"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_mail_template
msgid "Email Templates"
msgstr "E-Mail-Vorlagen"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Employee"
msgstr "Mitarbeiter"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal
msgid "Employee Appraisal"
msgstr "Mitarbeiterbeurteilung"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_template
msgid "Employee Appraisal Template"
msgstr "Mitarbeiterbeurteilungsvorlage"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_autocomplete_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_autocomplete_ids
msgid "Employee Autocomplete"
msgstr "Automatische Vervollständigung des Mitarbeiters"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__appraisal_employee_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Employee Feedback"
msgstr "Mitarbeiter-Feedback"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_published
msgid "Employee Feedback Published"
msgstr "Mitarbeiter-Feedback veröffentlicht"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_template
msgid "Employee Feedback Template"
msgstr "Vorlage für Mitarbeiter-Feedback"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__name
msgid "Employee Name"
msgstr "Name des Mitarbeiters"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_user_id
msgid "Employee User"
msgstr "Mitarbeiter-Benutzer"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee's Feedback"
msgstr "Feedback des Mitarbeiters"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.js:0
msgid "Employees"
msgstr "Mitarbeiter"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_note
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__assessment_note_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_note
msgid "Evaluation Scale"
msgstr "Bewertungsskala"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Exceeds expectations"
msgstr "Übertrifft Erwartungen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Extended Filters..."
msgstr "Erweiterte Filter ..."

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_external
msgid "External"
msgstr "Extern"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Fill appraisal for %s"
msgstr "Beurteilung für %s ausfüllen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_final_interview
msgid "Final Interview"
msgstr "Schlussgespräch"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Final Interview Date"
msgstr "Datum des Schlussgesprächs"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__assessment_note
msgid "Final Rating"
msgstr "Finale Bewertung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_follower_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_partner_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "FontAwesome-Icon, z. B. fa-tasks"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Future Activities"
msgstr "Anstehende Aktivitäten"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"Give one positive achievement that convinced you of the employee's\n"
"                    value."
msgstr ""
"Nennen Sie einen Erfolg, der Sie vom Wert des Mitarbeiters überzeugt\n"
"                    hat."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Goal"
msgstr "Ziel"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Goal Tags"
msgstr "Zielstichwörter"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_goal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_goal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_graph
msgid "Goals"
msgstr "Ziele"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__goals_count
msgid "Goals Count"
msgstr "Anzahl Ziele"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Group By"
msgstr "Gruppieren nach"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Group by..."
msgstr "Gruppieren nach ..."

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_confirm
msgid "HR: Appraisal Confirmation"
msgstr "Personalwesen: Bestätigung der Mitarbeiterbeurteilung"

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_request_from_employee
msgid "HR: Employee Appraisal Request"
msgstr "Personalwesen: Beurteilungsanfrage an Mitarbeiter"

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_request
msgid "HR: Manager appraisal request"
msgstr "Personalwesen: Beurteilungsanfrage an Manager"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_hardskills
msgid "Hard Skills"
msgstr "Hard Skills"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "Hat Abteilungsleiterzugriff"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__has_message
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How could the employee improve?"
msgstr "Wie könnte der Mitarbeiter sich verbessern?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How do I feel about my own..."
msgstr "Was ich von mir selbst denke ..."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How do I feel about the company..."
msgstr "Was ich über das Unternehmen denke ..."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__id
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__id
msgid "ID"
msgstr "ID"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon, um eine Ausnahmeaktivität anzuzeigen."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_1920
msgid "Image"
msgstr "Bild"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_128
msgid "Image 128"
msgstr "Bild 128"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "In Progress"
msgstr "Laufend"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "In progress Evaluations"
msgstr "Beurteilungen in Bearbeitung"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_internal
msgid "Internal"
msgstr "Intern"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__final_interview
msgid "Interview"
msgstr "Bewerbungsgespräch"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__is_mail_template_editor
msgid "Is Editor"
msgstr "Ist Editor"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_is_follower
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__is_manager
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__is_manager
msgid "Is Manager"
msgstr "Ist Manager"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__job_id
msgid "Job Position"
msgstr "Stelle"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__lang
msgid "Language"
msgstr "Sprache"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_id
msgid "Last Appraisal"
msgstr "Letzte Beurteilung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_date
msgid "Last Appraisal Date"
msgstr "Datum der letzten Beurteilung"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Last Meeting"
msgstr "Letztes Meeting"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late"
msgstr "Verspätet"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late Activities"
msgstr "Verspätete Aktivitäten"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_load_appraisal_demo_data
msgid "Load appraisal scenario"
msgstr "Beurteilungsszenario laden"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Load sample data"
msgstr "Beispieldaten laden"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"Long term (&gt; 6 months) career discussion, where does the employee\n"
"                    wants to go, how to help them reach this path?"
msgstr ""
"Langfristige (&gt;6 Monate) Laufbahnbesprechung, wohin\n"
"                    möchte der Mitarbeiter, wie kann man ihm auf seinem Weg helfen?"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__template_id
msgid "Mail Template"
msgstr "E-Mail-Vorlage"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Manager"
msgstr "Manager"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager Assessment will show here"
msgstr "Managerbewertung wird hier angezeigt"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__appraisal_manager_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Manager Feedback"
msgstr "Manager-Feedback"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_published
msgid "Manager Feedback Published"
msgstr "Manager-Feedback veröffentlicht"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_template
msgid "Manager Feedback Template"
msgstr "Manager-Feedback-Vorlage"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_user_ids
msgid "Manager Users"
msgstr "Manager-Benutzer"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager's Feedback"
msgstr "Feedback des Managers"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_mark_as_done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Mark as Done"
msgstr "Als erledigt markieren"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_count_display
msgid "Meeting Count"
msgstr "Anzahl Besprechungen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_ids
msgid "Meetings"
msgstr "Meetings"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Meets expectations"
msgstr "Erfüllt Erwartungen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Frist für meine Aktivitäten"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_my
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "My Appraisals"
msgstr "Meine Beurteilungen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "My Goals"
msgstr "Meine Ziele"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__name
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Name"
msgstr "Name"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Needs improvement"
msgstr "Verbesserung erforderlich"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee_base.py:0
msgid "New and Pending Appraisals"
msgstr "Neue und ausstehende Beurteilungen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nächstes Aktivitätskalenderereignis"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nächste Aktivitätsfrist"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_summary
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_search
msgid "Next Appraisal"
msgstr "Nächste Beurteilung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__next_appraisal_date
msgid "Next Appraisal Date"
msgstr "Nächstes Beurteilungsdatum"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Next Meeting"
msgstr "Nächstes Meeting"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "No Appraisals yet ..."
msgstr "Noch keine Mitarbeiterbeurteilungen ..."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "No Meeting"
msgstr "Keine Besprechung"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Note"
msgstr "Notiz"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_user
msgid "Officer: Access all appraisals"
msgstr "Sachbearbeiter: Auf alle Mitarbeiterbeurteilungen zugreifen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Ongoing"
msgstr "Laufend"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__ongoing_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__ongoing_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__ongoing_appraisal_count
msgid "Ongoing Appraisal Count"
msgstr "Anzahl laufender Beurteilungen"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_goal.py:0
msgid "Operation not supported"
msgstr "Vorgang nicht unterstützt"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Opportunities"
msgstr "Chancen"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Optionale Übersetzung (ISO-Code) zur Auswahl beim E-Mail-Versand. Falls es "
"keinen Eintrag gibt, wird die englische Version verwendet. Es sollte sich "
"normalerweise um einen Platzhalterausdruck handeln, der die passende Sprache"
" enthält, z. B. {{ object.partner_id.lang }}."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "Overall experience feedback"
msgstr "Allgemeines Feedback zur Erfahrung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__parent_user_id
msgid "Parent User"
msgstr "Übergeordneter Benutzer"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "People I Manage"
msgstr "Meine Mitarbeiter"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__previous_appraisal_date
msgid "Previous Appraisal Date"
msgstr "Datum der vorherigen Beurteilung"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Previous Appraisals"
msgstr "Vorherige Mitarbeiterbeurteilungen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__note
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Private Note"
msgstr "Private Notiz"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Private note (only accessible to people set as managers)"
msgstr ""
"Private Notiz (nur zugänglich für Personen, die als Manager eingestellt "
"sind)"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_programming
msgid "Programming"
msgstr "Programmierung"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__progression
msgid "Progress"
msgstr "Fortschritt"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_properties
msgid "Properties"
msgstr "Eigenschaften"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_public
msgid "Public Employee"
msgstr "Öffentlicher Mitarbeiter"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__rating_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Ready"
msgstr "Bereit"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__recipient_ids
msgid "Recipients"
msgstr "Empfänger"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__related_partner_id
msgid "Related Partner"
msgstr "Zugehöriger Partner"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/fields/appraisal_remaining_days.js:0
msgid "Remaining Days"
msgstr "Verbleibende Tage"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__render_model
msgid "Rendering Model"
msgstr "Rendering-Modell"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Reopen"
msgstr "Erneut öffnen"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_report
msgid "Reporting"
msgstr "Berichtswesen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid "Request Appraisal"
msgstr "Um Beurteilung bitten"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_create_multi_appraisals
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_employee_tree
msgid "Request Appraisals"
msgstr "Um Beurteilungen bitten"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_request_appraisal
msgid "Request an Appraisal"
msgstr "Um Beurteilung bitten"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Search Appraisal"
msgstr "Beurteilung suchen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Self Assessment will show here"
msgstr "Selbsteinschätzung wird hier angezeigt"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Send"
msgstr "Senden"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Send by email"
msgstr "Per E-Mail versenden"

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_confirm
msgid ""
"Sent automatically to both employee and manager when appraisal is confirmed"
msgstr ""
"Automatischer Versand an den Mitarbeiter und Manager, wenn eine Beurteilung "
"bestätigt wird"

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"Sent manually to the employee by the manager who wants to do an appraisal"
msgstr ""
"Manueller Versand an den Mitarbeiter durch den Manager, der eine Beurteilung"
" durchführen möchte"

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_request
msgid "Sent manually to the manager by the employee who wants an appraisal"
msgstr ""
"Manueller Versand an den Manager durch den Mitarbeiter, der eine Beurteilung"
" möchte"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__sequence
msgid "Sequence"
msgstr "Reihenfolge"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Set default appraisal template"
msgstr "Legen Sie eine Standardbeurteilungsvorlage fest"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_settings_action
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_configuration
msgid "Settings"
msgstr "Einstellungen"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "Short term (6-months) actions / decisions / objectives"
msgstr "Kurzfristige (6-monatige) Maßnahmen/Entscheidungen/Zielvorgaben"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__show_employee_feedback_full
msgid "Show Employee Feedback Full"
msgstr "Komplettes Feedback des Mitarbeiters anzeigen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__show_manager_feedback_full
msgid "Show Manager Feedback Full"
msgstr "Komplettes Feedback des Managers anzeigen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Show all records which has next action date is before today"
msgstr "Alle Datensätze mit vor heute geplanten Aktionen anzeigen"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_softskills
msgid "Soft Skills"
msgstr "Soziale Kompetenzen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_state
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Status"
msgstr "Status"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum bereits überschritten\n"
"Heute: Aktivitätsdatum ist heute\n"
"Geplant: anstehende Aktivitäten."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Strengths"
msgstr "Stärken"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Strongly Exceed Expectations"
msgstr "Übertrifft Erwartungen bei weitem"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__subject
msgid "Subject"
msgstr "Betreff"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Subject..."
msgstr "Betreff ..."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__tag_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_config_goal_tags
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_tag_view_tree
msgid "Tags"
msgstr "Stichwörter"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/mail_template.py:0
msgid ""
"Template %(template_name)s is necessary for appraisal requests and may not "
"be removed."
msgstr ""
"Die Vorlage %(template_name)s ist für Beurteilungsanfragen notwendig und "
"kann nicht entfernt werden."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"Thanks to your Appraisal Plan, without any new manual Appraisal, the new "
"Appraisal will be automatically created on %s."
msgstr ""
"Dank Ihres Beurteilungsplans wird die neue Beurteilung ohne eine neue "
"manuelle Beurteilung automatisch erstellt am %s."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The \"Manager Feedback Published\" cannot be changed by an employee."
msgstr ""
"Das „veröffentlichte Manager-Feedback“ kann von Mitarbeitern nicht "
"bearbeitet werden."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The appraisal's status has been set to Done by %s"
msgstr "Der Status der Beurteilung wurde durch %s auf Erledigt gesetzt."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__last_appraisal_date
msgid "The date of the last appraisal"
msgstr "Das Datum der letzten Beurteilung"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__next_appraisal_date
msgid ""
"The date of the next appraisal is computed by the appraisal plan's dates "
"(first appraisal + periodicity)."
msgstr ""
"Das Datum der nächsten Beurteilung wird automatisch errechnet (Erste "
"Beurteilung + Intervall)"

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_res_company_positif_number_months
msgid "The duration time must be bigger or equal to 1 month."
msgstr "Die Dauer muss größer oder gleich 1 Monat sein."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"The employee %(employee)s arrived %(months)s months ago. The appraisal is "
"created and you can fill it here."
msgstr ""
"Der Mitarbeiter %(employee)s ist schon vor %(months)s Monaten angekommen. "
"Die Beurteilung ist erstellt und Sie können sie hier ausfüllen."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The employee feedback cannot be changed by managers."
msgstr "Das Mitarbeiter-Feedback kann von Managern nicht bearbeitet werden."

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/fields/boolean_confirm.js:0
msgid ""
"The employee's feedback will be published without their consent. Do you "
"really want to publish it? This action will be logged in the chatter."
msgstr ""
"Das Feedback des Mitarbeiters wird ohne seine Zustimmung veröffentlicht. "
"Wollen Sie es wirklich veröffentlichen? Diese Aktion wird im Chatter "
"protokolliert."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"The last appraisal of %(employee)s was %(months)s months ago. The appraisal "
"is created and you can fill it here."
msgstr ""
"Die letzte Beurteilung von %(employee)s liegt %(months)s Monate zurück. Die "
"Beurteilung wurde erstellt und Sie können sie hier ausfüllen."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The manager feedback cannot be changed by an employee."
msgstr "Das Manager-Feedback kann von Mitarbeitern nicht bearbeitet werden."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__assessment_note
msgid "This field is not visible to the Employee."
msgstr "Dieses Feld ist für den Mitarbeiter nicht sichtbar."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Threats"
msgstr "Risiken"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__new
msgid "To Confirm"
msgstr "Zu bestätigen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "To Do"
msgstr "To-do"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__new
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "To Start"
msgstr "Anstehend"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "To create an appraisal, you need at least 2 employees"
msgstr ""
"Um eine Mitarbeiterbeurteilung zu erstellen, benötigen Sie mindestens 2 "
"Mitarbeiter"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Today Activities"
msgstr "Heutige Aktivitäten"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_training
msgid "Training"
msgstr "Schulung"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Try the backend and reporting:"
msgstr "Das Backend und Berichtswesen ausprobieren:"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der Ausnahmeaktivität im Datensatz."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__uncomplete_goals_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__uncomplete_goals_count
msgid "Uncomplete Goals Count"
msgstr "Anzahl unerreichter Ziele"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Unpublished"
msgstr "Unveröffentlicht"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_search
msgid "Upcoming Appraisals"
msgstr "Zukünftige Beurteilungen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid ""
"Use this area to write the content that will be displayed for the Employee."
"                                         You can use a lot of options with "
"the html editor, accessible by pressing / on the keyboard."
msgstr ""
"Verwenden Sie diesen Bereich, um den Inhalt zu schreiben, der für den "
"Mitarbeiter angezeigt wird.                                         Mit dem "
"HTML-Editor, der über die Tastenkombination „/“ aufgerufen wird, können Sie "
"viele Optionen nutzen."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid ""
"Use this area to write the content that will be displayed for the Manager."
"                                         You can use a lot of options with "
"the html editor, accessible by pressing / on the keyboard."
msgstr ""
"Verwenden Sie diesen Bereich, um den Inhalt zu schreiben, der für den "
"Manager angezeigt wird.                                         Mit dem "
"HTML-Editor, der über die Tastenkombination „/“ aufgerufen wird, können Sie "
"viele Optionen nutzen."

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_users
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_user_id
msgid "User"
msgstr "Benutzer"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__user_body
msgid "User Contents"
msgstr "Benutzerinhalte"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__waiting_feedback
msgid "Waiting Feedback from Employee/Managers"
msgstr "Warten auf Feedback von Mitarbeitern/Managern"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Weaknesses"
msgstr "Schwächen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "What are my best achievement(s) since my last appraisal?"
msgstr "Was sind meine beste(n) Leistung(en) seit meiner letzten Beurteilung?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"What are my short and long-term goals with the company, and for my\n"
"                    career?"
msgstr ""
"Was sind meine kurz- und langfristigen Ziele im Unternehmen und für meine\n"
"                   berufliche Laufbahn?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"What has been the most challenging aspect of my work this past year and\n"
"                    why?"
msgstr ""
"Was war die größte Herausforderung bei meiner Arbeit im vergangenen Jahr\n"
"                   und warum?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "What would I need to improve my work?"
msgstr "Was würde mir dabei helfen, meine Arbeit zu verbessern?"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"When the Appraisals plan is saved, it will overwrite all Next Appraisal "
"Dates for employees without ongoing appraisals."
msgstr ""
"Wenn der Beurteilungsplan gespeichert wird, überschreibt er alle nächsten "
"Beurteilungsdaten für Mitarbeiter ohne laufende Beurteilungen."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "Which parts of my job do I most / least enjoy?"
msgstr "Was macht mir an meiner Arbeit am meisten / am wenigsten Spaß?"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"You arrived %s months ago. Your appraisal is created and you can fill it "
"here."
msgstr ""
"Sie sind schon vor %s Monaten angekommen. Ihre Beurteilung ist erstellt und "
"Sie können sie hier ausfüllen."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee.py:0
msgid ""
"You cannot delete an employee who is a goal's manager, archive it instead."
msgstr ""
"Sie können einen Mitarbeiter, der Manager eines Ziels ist, nicht löschen, "
"sondern müssen ihn archivieren."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "You cannot delete appraisal which is not in draft or cancelled state"
msgstr ""
"Sie können keine Beurteilungen löschen, die sich nicht im Entwurf befinden "
"oder abgebrochen wurden."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee.py:0
msgid "You cannot set 'Next Appraisal Date' in the past."
msgstr ""
"Sie können kein „Nächstes Beurteilungsdatum“ in der Vergangenheit festlegen."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Your Appraisal has been completed"
msgstr "Ihre Beurteilung ist abgeschlossen"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"Your last appraisal was %s months ago. Your appraisal is created and you can"
" fill it here."
msgstr ""
"Ihre letzte Beurteilung liegt %s Monate zurück. Ihre Beurteilung wurde "
"erstellt und Sie können sie hier ausfüllen."

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "create a new one"
msgstr "eine neue erstellen"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "e.g. Annual Appraisal"
msgstr "z. B. Jährliche Beurteilung"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
msgid "e.g. Improve your English level"
msgstr "z. B. Verbessern Sie Ihr Englisch"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "e.g. John Doe"
msgstr "z. B. Lieschen Müller"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_tag_view_tree
msgid "e.g. Remediation, Team, Improvement plan, Career change, ..."
msgstr "z. B. Förderung, Team, Verbesserungsplan, Berufswechsel ..."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months after recruitment, then after"
msgstr "Monate nach Einstellung, dann nach"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months, then every"
msgstr "Monate, dann alle"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months."
msgstr "Monate."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "once published"
msgstr "sobald veröffentlicht"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "or"
msgstr "oder"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"{{ hasattr(object, 'name') and object.name or '' }} requests an Appraisal"
msgstr ""
"{{ hasattr(object, 'name') and object.name or '' }} hat um eine Beurteilung "
"gebeten"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_confirm
msgid "{{ object.employee_id.name }}: Appraisal Confirmed"
msgstr "{{ object.employee_id.name }}: Beurteilung bestätigt"
