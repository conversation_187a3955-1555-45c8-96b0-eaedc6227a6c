# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_appraisal
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "%(user)s decided, as %(role)s, to publish the employee's feedback"
msgstr ""
"%(user)s decidió, como %(role)s, publicar la retroalimentación del empleado"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_template.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "%s's Goals"
msgstr "Metas de %s"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "1 Meeting"
msgstr "1 reunión"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__100
msgid "100%"
msgstr "100 %"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__025
msgid "25%"
msgstr "25 %"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__module_hr_appraisal_survey
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "360 Feedback"
msgstr "Retroalimentación de 360 grados"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__050
msgid "50%"
msgstr "50 %"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_goal__progression__075
msgid "75%"
msgstr "75 %"

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_confirm
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        An appraisal of <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t> has been confirmed.\n"
"                        <br/><br/>\n"
"                        Please schedule an appraisal date together.\n"
"                        <br/><br/>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Se confirmó una valoración para <t t-out=\"ctx.get('employee_to_name', 'employee')\">empleado</t>.\n"
"                        <br/><br/>\n"
"                        Programen una fecha de valoración juntos.\n"
"                        <br/><br/>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\"background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    Ver valoración\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t>,\n"
"                        <br/>\n"
"                        <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t> (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>) wishes an appraisal.\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Estimado/a <t t-out=\"ctx.get('employee_to_name', 'employee')\">empleado</t>,\n"
"                        <br/>\n"
"                        <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t> (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>) desea una evaluación.\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Evaluación anual.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    Ver evaluación\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model:mail.template,body_html:hr_appraisal.mail_template_appraisal_request
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Dear <t t-out=\"ctx.get('employee_to_name', 'employee')\">employee</t>,\n"
"                        <br/>\n"
"                        An appraisal has been requested by <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t>\n"
"                        <br/>\n"
"                        (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>).\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Annual appraisal.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    View Appraisal\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <br/>\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Estimado/a <t t-out=\"ctx.get('employee_to_name', 'employee')\">empleado</t>,\n"
"                        <br/>\n"
"                        <t t-out=\"ctx.get('author_name', '')\">Addison Olson</t>\n"
"                        <br/>\n"
"                        (<t t-out=\"ctx.get('author_mail', '')\"><EMAIL></t>) ha solicitado una evaluación.\n"
"                        <t t-if=\"ctx.get('user_body')\">\n"
"                            <div style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; background-color: #F1F1F1;\">\n"
"                                <t t-out=\"ctx.get('user_body')\">Evaluación anual.</t>\n"
"                            </div>\n"
"                        </t>\n"
"                        <t t-if=\"ctx.get('recipient_users')\">\n"
"                            <p style=\"margin: 16px 0px 16px 0px; padding: 8px 16px 8px 16px; text-align: center;\">\n"
"                                <a t-att-href=\"ctx.get('url')\" style=\" margin: auto; background-color:#875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                    Ver evaluación\n"
"                                </a>\n"
"                            </p>\n"
"                        </t>\n"
"                        <br/>\n"
"                    </p>\n"
"                </div>\n"
"            "

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Describe something that made you proud, a piece of work positive for\n"
"                        the company.\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Describa algo que le haya hecho sentir orgulloso, algún trabajo positivo para\n"
"                        la empresa.\n"
"            </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Did you face new difficulties? Did you confront yourself to new\n"
"                        obstacles?\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        ¿Enfrentó nuevas dificultades? ¿Combatió nuevos\n"
"                        obstáculos?\n"
"            </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Every job has strong points, what are, in your opinion, the tasks that\n"
"                        you enjoy the most/the least?\n"
"                    </em>"
msgstr ""
"<em>\n"
"                       Cada trabajo tiene puntos fuertes. ¿Cuáles considera que son las tareas que\n"
"                       disfruta más y cuáles las que disfruta menos?\n"
"            </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        From a manager point of view, how could you help the employee to\n"
"                        overcome their weaknesses?\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Desde el punto de vista de un gerente, ¿cómo podría ayudar al empleado a\n"
"                        enfrentar sus debilidades?\n"
"            </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        How can the company help you with your need and objectives in order\n"
"                        for you to reach your goals and look for the best collaboration.\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        ¿De qué forma la empresa puede ayudarle con sus necesidades y objetivos con la\n"
"                        finalidad de que alcance sus metas y puedan colaborar de la manera más óptima?\n"
"            </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        How do you see the employee in the future, do your vision follow the\n"
"                        employee's desire?\n"
"                    </em>"
msgstr ""
"<em>\n"
"                      ¿Cuál es su visión sobre el empleado en el futuro, acaso esta es compatible con\n"
"                      sus deseos?\n"
"            </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>\n"
"                        Some achievements comforting you in their strengths to face job's\n"
"                        issues.\n"
"                    </em>"
msgstr ""
"<em>\n"
"                        Algunos logros que le ayudarán a superar las \n"
"                        dificultades en el trabajo.\n"
"            </em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Autonomy</em>"
msgstr "<em>Autonomía</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Culture/Behavior:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-1\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Cultura y funcionamiento:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-1\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Do you need rapid answer to the current situation?</em>"
msgstr "<em>¿Necesita una respuesta rápida a la situación actual?</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Give an example of long-term objective (&gt; 6 months)</em>"
msgstr "<em>Dé un ejemplo de objetivo a largo plazo (&gt; 6 meses)</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Give an example of short-term objective (&lt; 6 months)</em>"
msgstr "<em>Dé un ejemplo de objetivo a corto plazo (&gt; 6 meses)</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Internal Communication:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-2\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Comunicación interna:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-2\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Job's content:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-3\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Contenido del trabajo:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-3\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Pro-activity</em>"
msgstr "<em>Proactividad</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Remuneration:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-5\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Remuneración:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-5\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Stress Resistance</em>"
msgstr "<em>Tolerancia al estrés</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Teamwork</em>"
msgstr "<em>Trabajo en equipo</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "<em>Time Management</em>"
msgstr "<em>Gestión del tiempo</em>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<em>Work organization:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-4\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"
msgstr ""
"<em>Organización laboral:</em>\n"
"                        <span class=\"o_stars o_five_stars\" id=\"checkId-4\">\n"
"                            <i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i>\n"
"                        </span>"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee_base.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Schedule an appraisal\n"
"                        </p><p>\n"
"                            Plan appraisals with your colleagues, collect and discuss feedback.\n"
"                        </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                            Programar una evaluación\n"
"                        </p><p>\n"
"                            Planifique evaluaciones con sus colegas, recolecte y discuta retroalimentación.\n"
"                        </p>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<small role=\"img\" class=\"fa fa-circle text-success\" invisible=\"state !="
" 'pending' or waiting_feedback\" aria-label=\"Ready\" title=\"Ready\"/>"
msgstr ""
"<small role=\"img\" class=\"fa fa-circle text-success\" invisible=\"state !="
" 'pending' or waiting_feedback\" aria-label=\"Listo\" title=\"Listo\"/>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "<span class=\"fw-bold\">Meeting: </span>"
msgstr "<span class=\"fw-bold\">Reunión: </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"last_appraisal_state == 'done'\">\n"
"                            Ongoing\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\" invisible=\"last_appraisal_state != 'done'\">\n"
"                            Latest\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Appraisal\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"last_appraisal_state == 'done'\">\n"
"                            En curso\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\" invisible=\"last_appraisal_state != 'done'\">\n"
"                            Más reciente\n"
"                        </span>\n"
"                        <span class=\"o_stat_text\">\n"
"                            Evaluación\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Last Appraisal\n"
"                        </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Última evaluación\n"
"                        </span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_text\">Appraisals</span>"
msgstr "<span class=\"o_stat_text\">Evaluaciones</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "<span class=\"o_stat_text\">Goals</span>"
msgstr "<span class=\"o_stat_text\">Metas</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-end\" invisible=\"employee_feedback_published\">Not Visible to Manager</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state == 'new'\">Visible to Manager</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state != 'new'\">Visible &amp; Editable by Manager</span>"
msgstr ""
"<span class=\"text-end\" invisible=\"employee_feedback_published\">No es visible para el gerente</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state == 'new'\">Es visible para el gerente</span>\n"
"                                            <span class=\"text-end\" invisible=\"not employee_feedback_published or state != 'new'\">Es visible y editable para el gerente</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid ""
"<span class=\"text-end\" invisible=\"manager_feedback_published or not can_see_manager_publish\">Not Visible to Employee</span>\n"
"                                            <span class=\"text-end\" invisible=\"not manager_feedback_published or not can_see_manager_publish\">Visible to Employee</span>"
msgstr ""
"<span class=\"text-end\" invisible=\"manager_feedback_published or not can_see_manager_publish\">No es visible para el empleado</span>\n"
"                                            <span class=\"text-end\" invisible=\"not manager_feedback_published or not can_see_manager_publish\">Es visible para el empleado</span>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
msgid ""
"<span invisible=\"not is_manager or (next_appraisal_date or not ongoing_appraisal_count)\">\n"
"                    Ongoing\n"
"                </span>"
msgstr ""
"<span invisible=\"not is_manager or (next_appraisal_date or not ongoing_appraisal_count)\">\n"
"                    En curso\n"
"                </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Evaluation\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Evaluación\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Feedback\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Retroalimentación\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        Improvements\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Mejoras\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My feelings\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Mis sentimientos\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My future\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Mi futuro\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                        My work\n"
"                    </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                        Mi trabajo\n"
"                    </span>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong class=\"text-o-color-4\">\n"
"                                    LEAST\n"
"                                    <br>\n"
"                                </strong>"
msgstr ""
"<strong class=\"text-o-color-4\">\n"
"                                    MENOS\n"
"                                    <br>\n"
"                                </strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong class=\"text-o-color-4\">\n"
"                                    MOST\n"
"                                    <br>\n"
"                                </strong>"
msgstr ""
"<strong class=\"text-o-color-4\">\n"
"                                    MÁS\n"
"                                    <br>\n"
"                                </strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>360 feedback :</strong>"
msgstr "<strong>Retroalimentación de 360 grados:</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Any particular remark on the training ?</strong>"
msgstr "<strong>¿Algún comentario en particular sobre la formación?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Are you happy with your current role ?</strong>"
msgstr "<strong>¿Está feliz con su puesto actual?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>Can the employee accurately recall and explain the key concepts from"
" the training ?</strong>"
msgstr ""
"<strong>¿El empleado puede recordar y explicar con precisión los conceptos "
"clave de la formación?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Career objective</strong>"
msgstr "<strong>Objetivo profesional</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Career opportunities</strong>"
msgstr "<strong>Oportunidades profesionales</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Company Feedback</strong>"
msgstr "<strong>Retroalimentación de la empresa</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Could you provide examples ?</strong>"
msgstr "<strong>¿Podría proporcionar ejemplos?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Create your SWOT</strong>"
msgstr "<strong>Cree su análisis</strong>"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_gantt
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_gantt
msgid "<strong>Date — </strong>"
msgstr "<strong>Fecha — </strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Do you seek any particular career path ?</strong>"
msgstr ""
"<strong>¿Está en búsqueda de algún camino profesional en "
"particular?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Feedback on management</strong>"
msgstr "<strong>Retroalimentación sobre la gestión</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>GOALS :</strong>"
msgstr "<strong>OBJETIVOS:</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Handover to manage inside the team</strong>"
msgstr "<strong>Transferencia del trabajo dentro del equipo</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>Has the employee effectively integrated the new skills or knowledge "
"into their daily work tasks ?</strong>"
msgstr ""
"<strong>¿El empleado ha integrado de manera eficaz sus nuevas habilidades o "
"conocimientos a sus tareas diarias?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How actively did the employee participate during the training "
"sessions (e.g., asking questions, contributing to discussions) ?</strong>"
msgstr ""
"<strong>¿El empleado participó de manera activa durante las sesiones de "
"formación? (por ejemplo, haciendo preguntas, contribuyendo a la "
"conversación)</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How confident do you feel in using the skills and knowledge gained "
"from the training in your work ?</strong>"
msgstr ""
"<strong>¿Qué grado de confianza tiene al usar las habilidades y "
"conocimientos que adquirió durante la formación en su trabajo?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>How do you feel about the support and resources provided to you "
"during this period ?</strong>"
msgstr ""
"<strong>¿Qué piensa del apoyo y los recursos que le han proporcionado "
"durante este periodo?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How helpful were the provided materials ?</strong>"
msgstr "<strong>¿Le resultaron útiles los materiales proporcionados?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How would you asses your level before the training ?</strong>"
msgstr "<strong>¿Cómo evaluaría su nivel antes de la formación?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>How would you assess your level post training ?</strong>"
msgstr "<strong>¿Cómo evaluaría su nivel tras la formación?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>How would you rate the employee's proficiency in the skill on a "
"scale from 1 to 10 ?</strong>"
msgstr ""
"<strong>En una escala del 1 al 10, ¿cómo calificaría el dominio del empleado"
" en la habilidad?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Management feedback and assessment</strong>"
msgstr "<strong>Retroalimentación y evaluación de la dirección</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Motivations</strong>"
msgstr "<strong>Motivación</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>On a scale of 1 to 10, how would you rate the employee’s overall "
"improvement since the training ?</strong>"
msgstr ""
"<strong>En una escala del 1 a 10, ¿cómo calificaría la mejora general del "
"empleado luego de la formación?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Opportunity</strong>"
msgstr "<strong>Oportunidad</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Overall experience feedback</strong>"
msgstr "<strong>Retroalimentación sobre la experiencia general</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Overall personal experience</strong>"
msgstr "<strong>Experiencia general personal</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Position succession and candidate suggestion</strong>"
msgstr "<strong>Sucesión del puesto y propuesta de candidatos</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>SWOT results</strong>"
msgstr "<strong>Resultados del análisis</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>SWOT</strong>"
msgstr "<strong>Análisis</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Skills at stakes :</strong>"
msgstr "<strong>Habilidades afectadas:</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Strength</strong>"
msgstr "<strong>Fortalezas</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to keep doing ?</strong>"
msgstr "<strong>¿Cuáles son las cosas que debe seguir haciendo?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to start doing ?</strong>"
msgstr "<strong>¿Cuáles son las cosas que debe empezar a hacer?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Things to stop doing ?</strong>"
msgstr "<strong>¿Cuáles son las cosas que debe dejar de hacer?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Threats</strong>"
msgstr "<strong>Amenazas</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Time to assess the leadership team:</strong>"
msgstr "<strong>Es momento de evaluar al equipo directivo:</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Training assessment</strong>"
msgstr "<strong>Evaluación de la fromación</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>We wish you the best of success and sincerely thank you for your "
"invaluable contribution to the company's achievements.</strong>"
msgstr ""
"<strong>Le deseamos el mayor de los éxitos y le agradecemos por su "
"invaluable contribución a los logros de la empresa.</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>Weakness</strong>"
msgstr "<strong>Debilidades</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What advice do you have for improving management's leadership and "
"support for future employees ?</strong>"
msgstr ""
"<strong>¿Cuál sería su consejo para mejorar el liderazgo de la dirección y "
"el apoyo a los futuros empleados?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>What advice would you give to the successor ?</strong>"
msgstr "<strong>¿Qué consejo le daría al sucesor?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What are your thoughts on the company culture and work environment "
"?</strong>"
msgstr ""
"<strong>¿Qué piensa acerca de la cultura y el entorno laboral de la "
"empresa?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>What are your thoughts on the recent changes in the company ? Were "
"you able to adapt ?</strong>"
msgstr ""
"<strong>¿Qué piensa acerca de los cambios recientes en la empresa? ¿Ha "
"podido adaptarse a ellos?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>What are your thoughts on working with your team ?</strong>"
msgstr ""
"<strong>¿Qué piensa sobre la manera en la que colabora con su "
"equipo?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What are your thoughts on your overall experience during the "
"probationary period ?</strong>"
msgstr ""
"<strong>¿Qué piensa de su experiencia general durante el periodo de "
"prueba?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What aspects of management and leadership at the company were most "
"effective in helping you succeed ?</strong>"
msgstr ""
"<strong>¿Qué aspectos de la gestión y el liderazgo de la empresa le ayudaron"
" más a tener éxito?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What aspects of your role have you enjoyed the most/least ?</strong>"
msgstr ""
"<strong>¿Cuáles son los aspectos de su rol que ha disfrutado más y cuáles "
"menos?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid "<strong>What change would you see in the company ?</strong>"
msgstr "<strong>¿Qué cambios le gustaría ver en la empresa?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid ""
"<strong>What further actions (e.g., additional training, mentoring) do you "
"recommend to support the employee’s continued development ?</strong>"
msgstr ""
"<strong> ¿Qué otras acciones (p. ej. formaciones, asesoramiento) recomienda "
"para que los empleados puedan seguir creciendo?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>What is your overall feeling about the previous year ?</strong>"
msgstr "<strong>¿Cuál es su opinión general sobre el año anterior?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid ""
"<strong>What key skills should the management team focus on for a smooth "
"transition ?</strong>"
msgstr ""
"<strong>¿Cuáles son las habilidades clave en las que el equipo de gestión "
"debería centrarse para que no hayan problemas durante el cambio?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_probationary_period_template
msgid ""
"<strong>What motivates you to continue working with us, and what are your "
"career aspirations here ?</strong>"
msgstr ""
"<strong>¿Qué le motiva a seguir trabajando con nosotros? ¿Cuáles son sus "
"objetivos profesionales aquí?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>What were the key challenges in your position ?</strong>"
msgstr ""
"<strong>¿Cuáles fueron los principales desafíos de su puesto?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "<strong>Which emotions were prevalent ?</strong>"
msgstr "<strong>¿Qué emociones fueron predominantes?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>Which skills do you possess that you feel the company is not "
"utilizing ?</strong>"
msgstr ""
"<strong>¿Cuáles son las habilidades que posee y que cree que la empresa no "
"está utilizando?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid ""
"<strong>Which skills would you like to prioritize for training, and why "
"?</strong>"
msgstr ""
"<strong>¿Qué habilidades le gustaría priorizar durante la formación y por "
"qué?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "<strong>Who would you see as your successor ?</strong>"
msgstr "<strong>¿A quién vería como su sucesor?</strong>"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_post_training_template
msgid "<strong>Would you need extra help from the management team ?</strong>"
msgstr "<strong>¿Necesitaría más ayuda del equipo de gestión?</strong>"

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_hr_appraisal_goal_tag_name_uniq
msgid "A tag with the same name already exists."
msgstr "Ya existe una etiqueta con el mismo nombre."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__accessible_employee_feedback
msgid "Accessible Employee Feedback"
msgstr "Retroalimentación del empleado accesible"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__accessible_manager_feedback
msgid "Accessible Manager Feedback"
msgstr "Retroalimentación del gerente accesible"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__active
msgid "Active"
msgstr "Activo"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actvidad"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_config_templates_action
msgid "Add a new template"
msgstr "Añadir una nueva plantilla"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Add existing contacts..."
msgstr "Añadir contactos existentes..."

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Another appraisal with the same people is already ongoing."
msgstr "Ya se está llevando acabo una evaluación con las mismas personas."

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree2
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__appraisal_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Appraisal"
msgstr "Evaluación"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_all
#: model:ir.actions.act_window,name:hr_appraisal.action_appraisal_report_department
#: model:ir.ui.menu,name:hr_appraisal.menu_appraisal_analysis_report
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_graph
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_pivot
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Analysis"
msgstr "Análisis de evaluaciones"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_note
msgid "Appraisal Assessment Note"
msgstr "Nota de valoración de la evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_confirm_mail_template
msgid "Appraisal Confirm Mail Template"
msgstr "Plantilla de correo de confirmación de evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__appraisal_count
msgid "Appraisal Count"
msgstr "Número de evaluaciones"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_close
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Appraisal Date"
msgstr "Fecha de la evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__employee_id
msgid "Appraisal Employee"
msgstr "Evaluación de empleado"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Form to Fill"
msgstr "Formulario de evaluación a completar"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal
msgid "Appraisal Goal"
msgstr "Meta de la evaluación"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_goal_tag
msgid "Appraisal Goal Tags"
msgstr "Etiquetas de la meta de la evaluación"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Officer"
msgstr "Encargado de evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_plan_posted
msgid "Appraisal Plan Posted"
msgstr "Plan de evaluación publicado"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisal_properties_definition
msgid "Appraisal Properties"
msgstr "Propiedades de la evaluación"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal Request"
msgstr "Solicitud de evaluación"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request
msgid "Appraisal Requested"
msgstr "Evaluación solicitada"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__pending
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Appraisal Sent"
msgstr "Evaluación enviada"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_report
msgid "Appraisal Statistics"
msgstr "Estadísticas de evaluación"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_templates_action
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_template_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_template_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_template_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_template_view_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Appraisal Template"
msgstr "Plantilla de evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__custom_appraisal_template_id
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_template_menu
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisal Templates"
msgstr "Plantillas de evaluación"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/wizard/request_appraisal.py:0
msgid ""
"Appraisal for %(appraisal_title)s should be using template "
"\"%(template_name)s\" instead of \"%(wrong_template_name)s\""
msgstr ""
"La evaluación de %(appraisal_title)s debería usar la plantilla "
"“%(template_name)s” en lugar de “%(wrong_template_name)s”"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal for %(employee)s on %(date)s"
msgstr "Evaluación para %(employee)s el %(date)s"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal for %s to fill"
msgstr "Evaluación de %s a completar"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal of %s"
msgstr "Evaluación de %s"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Appraisal to fill"
msgstr "Evaluación a completar"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_from_department
msgid "Appraisal to start"
msgstr "Evaluación a empezar"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.ir_cron_scheduler_appraisal_ir_actions_server
msgid "Appraisal: Run employee appraisal"
msgstr "Evaluación: ejecutar la evaluación de empleado"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.js:0
#: model:ir.actions.act_window,name:hr_appraisal.open_view_hr_appraisal_tree
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_root
#: model:ir.ui.menu,name:hr_appraisal.menu_open_view_hr_appraisal_tree
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_departure_wizard_view_form
msgid "Appraisals"
msgstr "Evaluación"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisals Automation"
msgstr "Automatización de evaluaciones"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Appraisals Plans"
msgstr "Planes de evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_department__appraisals_to_process_count
msgid "Appraisals to Process"
msgstr "Evaluaciones a procesar"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Archived"
msgstr "Archivado"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Ask to fill a survey to other employees"
msgstr "Solicite a otros empleados completar una encuesta"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__assessment_note_ids
msgid "Assessment Note"
msgstr "Nota de evaluación"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.action_hr_appraisal_goal
msgid ""
"Assign Goals to motivate your Employees and keep track of their objectives "
"between Appraisals."
msgstr ""
"Asigne metas que motiven a sus empleados y lleve el seguimiento de sus "
"objetivos entre evaluaciones."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_attachment_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__author_id
msgid "Author"
msgstr "Autor"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__appraisal_plan
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__appraisal_plan
msgid "Automatically Generate Appraisals"
msgstr "Generar automáticamente evaluaciones"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"Automatically creates the confirmed appraisals when Next Appraisal Date is "
"reached"
msgstr ""
"Crea automáticamente las evaluaciones confirmadas cuando se alcanza la fecha"
" de la siguiente evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__avatar_1920
msgid "Avatar"
msgstr "Avatar"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_base
msgid "Basic Employee"
msgstr "Empleado básico"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body_has_template_value
msgid "Body content is the same as the template"
msgstr "El contenido del cuerpo es el mismo que el de la plantilla"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_calendar_event
msgid "Calendar Event"
msgstr "Evento de calendario"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__can_edit_body
msgid "Can Edit Body"
msgstr "Puede editar el cuerpo"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__can_request_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__can_request_appraisal
msgid "Can Request Appraisal"
msgstr "Puede solicitar una evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_employee_publish
msgid "Can See Employee Publish"
msgstr "Puede ver el empleado publicar"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__can_see_manager_publish
msgid "Can See Manager Publish"
msgstr "Puede ver el gerente publicar"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Cancel"
msgstr "Cancelar"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel Future Appraisals"
msgstr "Cancelar evaluaciones futuras"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_departure_wizard__cancel_appraisal
msgid "Cancel all appraisal after contract end date."
msgstr ""
"Cancele todas las evaluaciones después de la fecha de finalización de "
"contrato."

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__cancel
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__cancel
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
msgid "Cancelled"
msgstr "Cancelado"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__date_close
msgid "Closing date of the current appraisal"
msgstr "Última fecha de la evaluación actual"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__previous_appraisal_date
msgid "Closing date of the previous appraisal"
msgstr "Última fecha de la evaluación previa"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__color
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__color
msgid "Color"
msgstr "Color"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__company_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__company_id
msgid "Company"
msgstr "Compañía"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Compose Email"
msgstr "Redactar correo electrónico"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_configuration
msgid "Configuration"
msgstr "Configuración"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Confirm"
msgstr "Confirmar"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__pending
msgid "Confirmed"
msgstr "Confirmado"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__body
msgid "Contents"
msgstr "Contenidos"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__create_date
msgid "Create Date"
msgstr "Fecha de creación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_first_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_first_appraisal
msgid "Create a first Appraisal after"
msgstr "Crear una primera evaluación después"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Create a new employee"
msgstr "Crear un nuevo empleado"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Create a new tag"
msgstr "Crear una nueva etiqueta"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_next_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_next_appraisal
msgid "Create a second Appraisal after"
msgstr "Crear una segunda evaluación después"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_res_company__duration_after_recruitment
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__duration_after_recruitment
msgid "Create an Appraisal after recruitment"
msgstr "Crear una evaluación después del reclutamiento"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.action_hr_appraisal_goal
msgid "Create new goals"
msgstr "Crear nuevas metas"

#. module: hr_appraisal
#: model_terms:ir.actions.act_window,help:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Create new tags to use on Employee's goals"
msgstr "Crear etiquetas nuevas para usar en las metas de los empleados"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Created By"
msgstr "Creado por"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Created On"
msgstr "Creado el"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__create_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__create_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Creation Date"
msgstr "Fecha de creación"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__next_appraisal_date
msgid "Date where the new appraisal will be automatically created"
msgstr "Fecha en la que la nueva evaluación se creará automáticamente"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__deadline
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Deadline"
msgstr "Fecha límite"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Deadline Date"
msgstr "Fecha límite"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_view_activity
msgid "Deadline:"
msgstr "Fecha límite:"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_goal.py:0
msgid "Deadline: %s"
msgstr "Fecha límite: %s"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Default Template"
msgstr "Plantilla por defecto"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"Define the next appraisal date automatically based on Appraisal's History"
msgstr ""
"Defina la fecha de la siguiente evaluación automáticamente según el "
"historial de la evaluación"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
msgid "Delete"
msgstr "Eliminar"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_department
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__department_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Department"
msgstr "Departamento"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Asistente de salida"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__description
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__description
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Description"
msgstr "Descripción"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__display_name
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Do you want to"
msgstr "Quiere"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__done
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Done"
msgstr "Hecho"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_mail_template
msgid "Email Templates"
msgstr "Plantillas de correo electrónico"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Employee"
msgstr "Empleado"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal
msgid "Employee Appraisal"
msgstr "Evaluación del empleado"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_appraisal_template
msgid "Employee Appraisal Template"
msgstr "Plantilla para la evaluación del empleado"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_autocomplete_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__employee_autocomplete_ids
msgid "Employee Autocomplete"
msgstr "Autocompletar empleado"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__appraisal_employee_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Employee Feedback"
msgstr "Retroalimentación del empleado"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_published
msgid "Employee Feedback Published"
msgstr "Retroalimentación del empleado publicada"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_feedback_template
msgid "Employee Feedback Template"
msgstr "Plantilla de retroalimentación del empleado"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__name
msgid "Employee Name"
msgstr "Nombre del empleado"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__employee_user_id
msgid "Employee User"
msgstr "Usuario del empleado"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Employee's Feedback"
msgstr "Retroalimentación del empleado"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.js:0
msgid "Employees"
msgstr "Empleados"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_note
#: model:ir.model.fields,field_description:hr_appraisal.field_res_config_settings__assessment_note_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_note
msgid "Evaluation Scale"
msgstr "Escala de evaluación"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Exceeds expectations"
msgstr "Supera las expectativas"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Extended Filters..."
msgstr "Filtros avanzados..."

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_external
msgid "External"
msgstr "Externo"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Fill appraisal for %s"
msgstr "Complete la evaluación para %s"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__date_final_interview
msgid "Final Interview"
msgstr "Entrevista final"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Final Interview Date"
msgstr "Fecha de entrevista final"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__assessment_note
msgid "Final Rating"
msgstr "Calificación final"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_follower_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_partner_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_type_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome p. ej. fa-tasks"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Future Activities"
msgstr "Actividades futuras"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"Give one positive achievement that convinced you of the employee's\n"
"                    value."
msgstr ""
"Mencione un logro positivo que le haya convencido del valor del\n"
"            empleado."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Goal"
msgstr "Meta"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_goal_tag_action
msgid "Goal Tags"
msgstr "Etiquetas de la meta"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.action_hr_appraisal_goal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_goal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_graph
msgid "Goals"
msgstr "Metas"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__goals_count
msgid "Goals Count"
msgstr "Número de metas"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Group By"
msgstr "Agrupar por"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Group by..."
msgstr "Agrupar por..."

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_confirm
msgid "HR: Appraisal Confirmation"
msgstr "RR. HH.: confirmación de evaluación"

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_request_from_employee
msgid "HR: Employee Appraisal Request"
msgstr "RR. HH.: solicitud de evaluacón del empleado"

#. module: hr_appraisal
#: model:mail.template,name:hr_appraisal.mail_template_appraisal_request
msgid "HR: Manager appraisal request"
msgstr "RR. HH.: solicitud de evaluación del gerente"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_hardskills
msgid "Hard Skills"
msgstr "Habilidades técnicas"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr "Tiene acceso de gerente al departamento"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__has_message
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How could the employee improve?"
msgstr "¿Cómo podría mejorar el empleado?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How do I feel about my own..."
msgstr "Cómo me siento respecto a mi..."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "How do I feel about the company..."
msgstr "Cómo me siento respecto a la empresa..."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__id
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__id
msgid "ID"
msgstr "ID"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_icon
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_1920
msgid "Image"
msgstr "Imagen"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "In Progress"
msgstr "En proceso"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "In progress Evaluations"
msgstr "Evaluaciones en proceso"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_internal
msgid "Internal"
msgstr "Interno"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__final_interview
msgid "Interview"
msgstr "Entrevista"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__is_mail_template_editor
msgid "Is Editor"
msgstr "Es un editor"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_is_follower
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__is_manager
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__is_manager
msgid "Is Manager"
msgstr "Es un gerente"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__job_id
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__lang
msgid "Language"
msgstr "Idioma"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_id
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_id
msgid "Last Appraisal"
msgstr "Última evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__last_appraisal_date
msgid "Last Appraisal Date"
msgstr "Fecha de la última evaluación"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Last Meeting"
msgstr "Última reunión"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__write_uid
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__write_date
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late"
msgstr "Retrasado"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Late Activities"
msgstr "Actividades retrasadas"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_load_appraisal_demo_data
msgid "Load appraisal scenario"
msgstr "Cargar escenario de evaluación"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Load sample data"
msgstr "Cargar datos de muestra"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"Long term (&gt; 6 months) career discussion, where does the employee\n"
"                    wants to go, how to help them reach this path?"
msgstr ""
"Conversación sobre su trayectoria profesional a largo plazo (más de 6 meses), ¿hacia dónde se quiere dirigir el empleado\n"
"                    y cómo podría ayudarle a llegar allí?"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__template_id
msgid "Mail Template"
msgstr "Plantilla de correo electrónico"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_id
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "Manager"
msgstr "Gerente"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager Assessment will show here"
msgstr "La valoración del gerente aparecerá aquí"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_template__appraisal_manager_feedback_template
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "Manager Feedback"
msgstr "Retroalimentación del gerente"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_published
msgid "Manager Feedback Published"
msgstr "Retroalimentación del gerente publicada"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_feedback_template
msgid "Manager Feedback Template"
msgstr "Plantilla de retroalimentación del gerente"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__manager_user_ids
msgid "Manager Users"
msgstr "Usuarios gerentes"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Manager's Feedback"
msgstr "Retroalimentación del gerente"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_mark_as_done
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Mark as Done"
msgstr "Marcar como hecho"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_count_display
msgid "Meeting Count"
msgstr "Número de reuniones"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__meeting_ids
msgid "Meetings"
msgstr "Reuniones"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Meets expectations"
msgstr "Cumple las expectativas"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_action_my
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "My Appraisals"
msgstr "Mis evaluaciones"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
msgid "My Goals"
msgstr "Mis metas"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal_tag__name
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__name
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_tree
msgid "Name"
msgstr "Nombre"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Needs improvement"
msgstr "Necesita mejorar"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee_base.py:0
msgid "New and Pending Appraisals"
msgstr "Evaluaciones nuevas y pendientes"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_date_deadline
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_summary
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_type_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_search
msgid "Next Appraisal"
msgstr "Siguiente evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__next_appraisal_date
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__next_appraisal_date
msgid "Next Appraisal Date"
msgstr "Fecha de la siguiente evaluación"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Next Meeting"
msgstr "Próxima reunión"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "No Appraisals yet ..."
msgstr "Todavía no hay evaluaciones..."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "No Meeting"
msgstr "Sin reuniones"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Note"
msgstr "Nota"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_needaction_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__message_has_error_counter
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: hr_appraisal
#: model:res.groups,name:hr_appraisal.group_hr_appraisal_user
msgid "Officer: Access all appraisals"
msgstr "Encargado: acceder a todas las evaluaciones"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Ongoing"
msgstr "En curso"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__ongoing_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__ongoing_appraisal_count
#: model:ir.model.fields,field_description:hr_appraisal.field_res_users__ongoing_appraisal_count
msgid "Ongoing Appraisal Count"
msgstr "Número de evaluaciones en curso"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal_goal.py:0
msgid "Operation not supported"
msgstr "Operación no admitida"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Opportunities"
msgstr "Oportunidades"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_request_appraisal__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Idioma de traducción opcional (código ISO) a seleccionar para el envío de "
"correos electrónicos. Si no se selecciona esta opción, se utilizará la "
"versión en inglés. Por lo general, se usa una expresión de marcador de "
"posición para indicar el idioma adecuado, por ejemplo, {{ "
"object.partner_id.lang }}."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_exit_interview_template
msgid "Overall experience feedback"
msgstr "Comentarios sobre la experiencia general"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__parent_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__parent_user_id
msgid "Parent User"
msgstr "Usuario padre"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_search
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "People I Manage"
msgstr "Gente que gestiono"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__previous_appraisal_date
msgid "Previous Appraisal Date"
msgstr "Fecha de valoración anterior"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Previous Appraisals"
msgstr "Evaluaciones anteriores"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__note
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Private Note"
msgstr "Nota privada"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Private note (only accessible to people set as managers)"
msgstr ""
"Nota privada (solo es accesible para personas establecidas como gerentes)"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_programming
msgid "Programming"
msgstr "Programación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__progression
msgid "Progress"
msgstr "Progreso"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__appraisal_properties
msgid "Properties"
msgstr "Propiedades"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_hr_employee_public
msgid "Public Employee"
msgstr "Empleado público"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__rating_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Ready"
msgstr "Listo"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__recipient_ids
msgid "Recipients"
msgstr "Destinatarios"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__related_partner_id
msgid "Related Partner"
msgstr "Contacto relacionado"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/fields/appraisal_remaining_days.js:0
msgid "Remaining Days"
msgstr "Días restantes"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__render_model
msgid "Rendering Model"
msgstr "Modelo de visualización"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Reopen"
msgstr "Reabrir"

#. module: hr_appraisal
#: model:ir.ui.menu,name:hr_appraisal.menu_hr_appraisal_report
msgid "Reporting"
msgstr "Informes"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_public_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_users_view_form
msgid "Request Appraisal"
msgstr "Solicitar evaluación"

#. module: hr_appraisal
#: model:ir.actions.server,name:hr_appraisal.action_create_multi_appraisals
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_employee_tree
msgid "Request Appraisals"
msgstr "Solicitar evaluaciones"

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_request_appraisal
msgid "Request an Appraisal"
msgstr "Solicitar una evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__activity_user_id
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__message_has_sms_error
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Search Appraisal"
msgstr "Buscar evaluación"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Self Assessment will show here"
msgstr "La autoevaluación aparecerá aquí"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Send"
msgstr "Enviar"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "Send by email"
msgstr "Enviar por correo electrónico"

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_confirm
msgid ""
"Sent automatically to both employee and manager when appraisal is confirmed"
msgstr ""
"Enviado automáticamente al empleado y al gerente al confirmar la evaluación"

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"Sent manually to the employee by the manager who wants to do an appraisal"
msgstr ""
"Enviado manualmente al empleado por el gerente que desea una evaluación"

#. module: hr_appraisal
#: model:mail.template,description:hr_appraisal.mail_template_appraisal_request
msgid "Sent manually to the manager by the employee who wants an appraisal"
msgstr ""
"Enviado manualmente al gerente por el empleado que desea una evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_note__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "Set default appraisal template"
msgstr "Configure plantilla de evaluación por defecto"

#. module: hr_appraisal
#: model:ir.actions.act_window,name:hr_appraisal.hr_appraisal_config_settings_action
#: model:ir.ui.menu,name:hr_appraisal.hr_appraisal_menu_configuration
msgid "Settings"
msgstr "Ajustes"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "Short term (6-months) actions / decisions / objectives"
msgstr "Acciones, decisiones y objetivos a corto plazo (6 meses) "

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__show_employee_feedback_full
msgid "Show Employee Feedback Full"
msgstr "Mostrar la retroalimentación del empleado completa"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__show_manager_feedback_full
msgid "Show Manager Feedback Full"
msgstr "Mostrar la retroalimentación del gerente completa"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Show all records which has next action date is before today"
msgstr ""
"Mostrar todos los registros que tienen la próxima fecha de acción antes de "
"hoy"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_softskills
msgid "Soft Skills"
msgstr "Habilidades blandas"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_report__state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__last_appraisal_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_base__last_appraisal_state
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee_public__last_appraisal_state
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "Status"
msgstr "Estado"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_state
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya ha pasado\n"
"Hoy: la fecha límite es hoy\n"
"Planificada: actividades futuras."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Strengths"
msgstr "Fortalezas"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/res_company.py:0
msgid "Strongly Exceed Expectations"
msgstr "Supera las expectativas con creces "

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__subject
msgid "Subject"
msgstr "Asunto"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.request_appraisal_view_form
msgid "Subject..."
msgstr "Asunto..."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__tag_ids
#: model:ir.ui.menu,name:hr_appraisal.menu_config_goal_tags
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_tag_view_tree
msgid "Tags"
msgstr "Etiquetas"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/mail_template.py:0
msgid ""
"Template %(template_name)s is necessary for appraisal requests and may not "
"be removed."
msgstr ""
"La plantilla %(template_name)s es necesaria para las solicitudes de "
"evaluación y no puede eliminarla."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"Thanks to your Appraisal Plan, without any new manual Appraisal, the new "
"Appraisal will be automatically created on %s."
msgstr ""
"Gracias a su plan de evaluación, sin ninguna nueva evaluación manual, la "
"nueva evaluación se creará automáticamente en %s."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The \"Manager Feedback Published\" cannot be changed by an employee."
msgstr ""
"Un empleado no puede modificar la \"retroalimentación del gerente "
"publicada\"."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The appraisal's status has been set to Done by %s"
msgstr "%s ha establecido el estado de la evaluación como Hecho"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__last_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__last_appraisal_date
msgid "The date of the last appraisal"
msgstr "La fecha de la última evaluación"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_employee__next_appraisal_date
#: model:ir.model.fields,help:hr_appraisal.field_res_users__next_appraisal_date
msgid ""
"The date of the next appraisal is computed by the appraisal plan's dates "
"(first appraisal + periodicity)."
msgstr ""
"La fecha de la seguiente evaluación se calcula conforme a las fechas del "
"plan de evaluación (primera evaluación + periodicidad)."

#. module: hr_appraisal
#: model:ir.model.constraint,message:hr_appraisal.constraint_res_company_positif_number_months
msgid "The duration time must be bigger or equal to 1 month."
msgstr "El tiempo de duración debe ser mayor o igual a 1 mes."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"The employee %(employee)s arrived %(months)s months ago. The appraisal is "
"created and you can fill it here."
msgstr ""
"El empleado %(employee)s llegó hace %(months)s meses. Puede ver y realizar "
"su evaluación aquí."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The employee feedback cannot be changed by managers."
msgstr "Los gerentes no pueden cambiar la retroalimentación del empleado."

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/fields/boolean_confirm.js:0
msgid ""
"The employee's feedback will be published without their consent. Do you "
"really want to publish it? This action will be logged in the chatter."
msgstr ""
"La retroalimentación del empleado se publicará sin su consentimiento. ¿En "
"verdad desea publicarla? Esta acción se registrará en el chatter."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"The last appraisal of %(employee)s was %(months)s months ago. The appraisal "
"is created and you can fill it here."
msgstr ""
"La última evaluación de %(employee)s fue hace %(months)s meses. Puede ver y "
"realizar su evaluación aquí."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "The manager feedback cannot be changed by an employee."
msgstr "Un empleado no puede cambiar la retroalimentación de un gerente."

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__assessment_note
msgid "This field is not visible to the Employee."
msgstr "Este campo no es visible para el empleado."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Threats"
msgstr "Amenazas"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal__state__new
msgid "To Confirm"
msgstr "A confirmar"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "To Do"
msgstr "Pendiente"

#. module: hr_appraisal
#: model:ir.model.fields.selection,name:hr_appraisal.selection__hr_appraisal_report__state__new
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_appraisal_report_search
msgid "To Start"
msgstr "A empezar"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "To create an appraisal, you need at least 2 employees"
msgstr "Necesita al menos 2 empleados para crear una evaluación"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_search
msgid "Today Activities"
msgstr "Actividades de hoy"

#. module: hr_appraisal
#: model:hr.appraisal.goal.tag,name:hr_appraisal.hr_appraisal_goal_tag_training
msgid "Training"
msgstr "Formación"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "Try the backend and reporting:"
msgstr "Pruebe con el backend y los informes:"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__activity_exception_decoration
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción en el registro."

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__uncomplete_goals_count
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_employee__uncomplete_goals_count
msgid "Uncomplete Goals Count"
msgstr "Número de metas incompletas"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Unpublished"
msgstr "No publicado"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_employee_view_search
msgid "Upcoming Appraisals"
msgstr "Próximas evaluaciones"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid ""
"Use this area to write the content that will be displayed for the Employee."
"                                         You can use a lot of options with "
"the html editor, accessible by pressing / on the keyboard."
msgstr ""
"Use esta área para escribir el contenido que se mostrará al empleado."
"                                         Puede usar muchas opciones con el "
"editor HTML, al cual puede acceder si presiona / en el teclado."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid ""
"Use this area to write the content that will be displayed for the Manager."
"                                         You can use a lot of options with "
"the html editor, accessible by pressing / on the keyboard."
msgstr ""
"Use esta área para escribir el contenido que se mostrará al gerente."
"                                         Puede usar muchas opciones con el "
"editor HTML, al cual puede acceder si presiona / en el teclado."

#. module: hr_appraisal
#: model:ir.model,name:hr_appraisal.model_res_users
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__manager_user_id
msgid "User"
msgstr "Usuario"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_request_appraisal__user_body
msgid "User Contents"
msgstr "Contenidos del usuario"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__waiting_feedback
msgid "Waiting Feedback from Employee/Managers"
msgstr "Esperando la retroalimentación del empleado/gerente"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_yearly_template
#: model_terms:hr.appraisal.template,appraisal_manager_feedback_template:hr_appraisal.hr_appraisal_yearly_template
msgid "Weaknesses"
msgstr "Debilidades"

#. module: hr_appraisal
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,field_description:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: hr_appraisal
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal__website_message_ids
#: model:ir.model.fields,help:hr_appraisal.field_hr_appraisal_goal__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "What are my best achievement(s) since my last appraisal?"
msgstr "¿Cuáles han sido mis mejores logros desde la última evaluación?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"What are my short and long-term goals with the company, and for my\n"
"                    career?"
msgstr ""
"¿Cuáles son mis metas a corto y largo plazo en la empresa y para mi\n"
"                    trayectoria profesional?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid ""
"What has been the most challenging aspect of my work this past year and\n"
"                    why?"
msgstr ""
"¿Cuál ha sido el aspecto más desafiante de mi trabajo este último año y\n"
"                   por qué?"

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "What would I need to improve my work?"
msgstr "¿Qué necesitaría para mejorar mi trabajo?"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid ""
"When the Appraisals plan is saved, it will overwrite all Next Appraisal "
"Dates for employees without ongoing appraisals."
msgstr ""
"Cuando se guarde el plan de evaluaciones se sobreescribirán todas las fechas"
" de seguientes evaluaciones para los empleados que no tengan evaluaciones en"
" curso."

#. module: hr_appraisal
#: model_terms:hr.appraisal.template,appraisal_employee_feedback_template:hr_appraisal.hr_appraisal_default_template
msgid "Which parts of my job do I most / least enjoy?"
msgstr ""
"¿Cuáles son las partes de mi trabajo que disfruto más y cuáles son aquellas "
"que disfruto menos?"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"You arrived %s months ago. Your appraisal is created and you can fill it "
"here."
msgstr ""
"Lleva %s meses en este trabajo. Puede ver y realizar su evaluación aquí."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee.py:0
msgid ""
"You cannot delete an employee who is a goal's manager, archive it instead."
msgstr ""
"No puede eliminar un empleado que es el gerente de un objetivo, mejor "
"archívelo."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "You cannot delete appraisal which is not in draft or cancelled state"
msgstr ""
"No puede borrar una evaluación que no esté en el estado de borrador o "
"cancelada."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_employee.py:0
msgid "You cannot set 'Next Appraisal Date' in the past."
msgstr "La \"fecha de la siguiente evaluación\" no puede ser en el pasado."

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid "Your Appraisal has been completed"
msgstr "Su evaluación ha sido completada"

#. module: hr_appraisal
#. odoo-python
#: code:addons/hr_appraisal/models/hr_appraisal.py:0
msgid ""
"Your last appraisal was %s months ago. Your appraisal is created and you can"
" fill it here."
msgstr ""
"Su última evaluación fue hace %s meses. Puede ver y realizar su evaluación "
"aquí."

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "create a new one"
msgstr "cree una nueva"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_template_form
msgid "e.g. Annual Appraisal"
msgstr "p. ej. Evaluación anual"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_view_form
msgid "e.g. Improve your English level"
msgstr "p. ej. mejore su nivel de inglés"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "e.g. John Doe"
msgstr "p. ej. John Doe"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.hr_appraisal_goal_tag_view_tree
msgid "e.g. Remediation, Team, Improvement plan, Career change, ..."
msgstr "p. ej. remedio, equipo, plan de mejora, cambio de carrera, etc."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months after recruitment, then after"
msgstr "meses después del reclutamiento, y después"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months, then every"
msgstr "meses, después cada"

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.res_config_settings_view_employee_form
msgid "months."
msgstr "meses."

#. module: hr_appraisal
#: model_terms:ir.ui.view,arch_db:hr_appraisal.view_hr_appraisal_form
msgid "once published"
msgstr "una vez publicada"

#. module: hr_appraisal
#. odoo-javascript
#: code:addons/hr_appraisal/static/src/views/appraisal_helper_view.xml:0
msgid "or"
msgstr "o/u"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_request_from_employee
msgid ""
"{{ hasattr(object, 'name') and object.name or '' }} requests an Appraisal"
msgstr ""
"{{ hasattr(object, 'name') and object.name or '' }} solicita una evaluación"

#. module: hr_appraisal
#: model:mail.template,subject:hr_appraisal.mail_template_appraisal_confirm
msgid "{{ object.employee_id.name }}: Appraisal Confirmed"
msgstr "{{ object.employee_id.name }}: Evaluación confirmada"
