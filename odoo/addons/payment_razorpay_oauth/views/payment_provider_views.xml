<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_provider_form_razorpay_oauth" model="ir.ui.view">
        <field name="name">Razorpay Provider Oauth Form</field>
        <field name="model">payment.provider</field>
        <field name="inherit_id" ref="payment_razorpay.payment_provider_form_razorpay"/>
        <field name="arch" type="xml">
            <group name="provider_credentials" position="before">
                <div class="text-muted mb-2" invisible="not razorpay_account_id">
                    This provider is linked with your Razorpay account.
                </div>
            </group>
            <field name="razorpay_key_id" position="before">
                <label string="Account ID" for="razorpay_account_id" groups="base.group_no_one"/>
                <div class="o_row" groups="base.group_no_one">
                    <field name="razorpay_account_id" readonly="True"/>
                    <button
                        string="Reset Your Razorpay Account"
                        type="object"
                        name="action_razorpay_reset_oauth_account"
                        class="btn-secondary ms-2"
                        confirm="Are you sure you want to disconnect?"
                        invisible="not razorpay_account_id"
                        readonly="True"
                    />
                </div>
            </field>
            <field name="razorpay_key_id" position="attributes">
                <attribute name="required">False</attribute>
                <attribute name="decoration-muted">razorpay_account_id</attribute>
                <attribute name="groups">base.group_no_one</attribute>
            </field>
            <field name="razorpay_key_secret" position="attributes">
                <attribute name="required">razorpay_key_id</attribute>
                <attribute name="decoration-muted">razorpay_account_id</attribute>
                <attribute name="groups">base.group_no_one</attribute>
            </field>
            <field name="razorpay_webhook_secret" position="replace">
                <label for="razorpay_webhook_secret" groups="base.group_no_one"/>
                <div class="o_row" groups="base.group_no_one">
                    <field
                        string="Webhook Secret"
                        name="razorpay_webhook_secret"
                        password="True"
                    />
                    <button
                        string="Generate your webhook"
                        type="object"
                        name="action_razorpay_create_webhook"
                        class="btn-primary ms-2"
                        invisible="not razorpay_account_id or razorpay_webhook_secret"
                    />
                </div>
            </field>
            <group name="provider_credentials" position="after">
                <div
                    class="alert alert-warning"
                    role="alert"
                    invisible="code != 'razorpay' or not razorpay_key_id or razorpay_account_id"
                >
                    You are currently connected to Razorpay through the credentials method, which is
                    deprecated. Click the "Connect" button below to use the recommended OAuth
                    method.
                </div>
                <div invisible="code != 'razorpay' or razorpay_account_id">
                    <button
                        string="Connect"
                        type="object"
                        name="action_razorpay_redirect_to_oauth_url"
                        class="btn-primary"
                    />
                </div>
            </group>
        </field>
    </record>

</odoo>
