<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_mn" model="res.partner" forcecreate="1">
        <field name="name">MN Company</field>
        <field name="vat"/>
        <field name="street">Ард Аюушийн өргөн чөлөө</field>
        <field name="city">Улаанбаатар</field>
        <field name="country_id" ref="base.mn"/>
        <field name="state_id" ref="base.state_mn_35"/>
        <field name="zip">16092</field>
        <field name="phone">+976 8812 3456</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.mnexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_mn" model="res.company" forcecreate="1">
        <field name="name">MN Company</field>
        <field name="partner_id" ref="base.partner_demo_company_mn"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_mn')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_mn'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>mn</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_mn')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
