<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <record id="payment_provider_form" model="ir.ui.view">
            <field name="name">provider.form.inherit.website</field>
            <field name="model">payment.provider</field>
            <field name="inherit_id" ref="payment.payment_provider_form"/>
            <field name="arch" type="xml">
                <group name="payment_state" position='inside'>
                    <field name="website_id" options="{'no_open': True, 'no_create_edit': True}" groups="website.group_multi_website"/>
                </group>
            </field>
        </record>

    </data>
</odoo>
