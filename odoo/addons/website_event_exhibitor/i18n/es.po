# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_event_exhibitor
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:05+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
")\n"
"                    to meet them!"
msgstr ""
")\n"
"                    para conocerlos!"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "<i class=\"fa fa-ban me-2\"/>Unpublished"
msgstr "<i class=\"fa fa-ban me-2\"/>No publicado"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right mx-2\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<small class=\"badge text-bg-danger\">Unpublished</small>"
msgstr "<small class=\"badge text-bg-danger\">No publicado</small>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "<span class=\"fa fa-plus me-1\"/> Add Exhibitors"
msgstr "<span class=\"fa fa-plus me-1\"/> Añadir expositores"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_aside
msgid "<span class=\"h5 m-3\">Other exhibitors</span>"
msgstr "<span class=\"h5 m-3\">Otros expositores</span>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is currently closed</span><br/>\n"
"                    Come back between"
msgstr ""
"<span>¡Uy! Esta sala se encuentra cerrada</span><br/>\n"
"                    Vuelva más tarde entre"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"<span>Oops! This room is full</span><br/>Come back later to have a chat with"
" us!"
msgstr ""
"<span>¡Uy! Esta sala está llena</span><br/>¡Vuelva más tarde para charlar "
"con nosotros!"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"A non-profit international educational and scientific\n"
"                    organisation, hosting three departments (aeronautics and\n"
"                    aerospace, environmental and applied fluid dynamics, and\n"
"                    turbomachinery and propulsion)."
msgstr ""
"Una organización educativa y científica internacional sin fines de lucro\n"
"                    con tres departamentos (aeronáutica y\n"
"                    aeroespacial, dinámica medioambiental y de fluidos, y\n"
"                    turbomaquinaria y propulsión)."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "About"
msgstr "Sobre"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__active
msgid "Active"
msgstr "Activo"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actvidad"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid "All Countries"
msgstr "Todos los países"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_exhibitor_main
msgid "All Exhibitors"
msgstr "Todos los expositores"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid "All Levels"
msgstr "Todos los niveles"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Archived"
msgstr "Archivado"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "As a team, we are happy to contribute to this event."
msgstr "Como equipo, estamos contentos de contribuir a este evento."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Attendees will be able to join to meet"
msgstr "Los asistentes podrán unirse"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Available from"
msgstr "Disponible desde"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_exhibitor_main
msgid "Back to all Exhibitors"
msgstr "Regresar a todos los expositores"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type1
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__bronze
msgid "Bronze"
msgstr "Bronce"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_country
msgid "By Country"
msgstr "Por país"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar_sponsorship
msgid "By Level"
msgstr "Por nivel"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__can_publish
msgid "Can Publish"
msgstr "Puede publicar"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__chat_room_id
msgid "Chat Room"
msgstr "Sala de chat"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar
msgid "Close"
msgstr "Cerrar"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
msgid "Come back between"
msgstr "Vuelva entre"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Come see us live, we hope to meet you!"
msgstr "Venga a vernos en vivo, ¡esperamos conocerle!"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Connect"
msgstr "Conectar"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Countries"
msgstr "Países"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_id
msgid "Country"
msgstr "País"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__country_flag_url
msgid "Country Flag"
msgstr "Bandera del país"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid "Create a Sponsor / Exhibitor"
msgstr "Crear patrocinador/expositor"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid "Create a Sponsor Level"
msgstr "Crear un nivel de patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__create_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Customer Relationship Management"
msgstr "Gestión de relaciones con el cliente"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Deco Addict designs, develops, integrates and supports HR and Supply\n"
"                Chain processes in order to make our customers more productive,\n"
"                responsive and profitable."
msgstr ""
"Deco Addict diseña, desarrolla, integra y da soporte a los procesos de RR. HH. y de cadena\n"
"                de suministro para que nuestros clientes sean más productivos,\n"
"                receptivos y rentables."

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Deco Addict integrates ERP for Global Companies and supports PME\n"
"                with Open Sources software to manage their businesses. Our\n"
"                consultants are experts in the following areas:"
msgstr ""
"Deco Addict integra el ERP para empresas globales y da soporte a las PYMES\n"
"                con software de código abierto para gestionar sus empresas. Nuestros\n"
"                asesores son expertos en las siguientes áreas:"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_description
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Description"
msgstr "Descripción"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Discover more"
msgstr "Más información"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__display_name
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_type__exhibitor_menu
msgid ""
"Display exhibitors on website, in the footer of every page of the event."
msgstr ""
"Mostrar expositores en el sitio web, en el pie de cada página del evento."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Display in footer"
msgstr "Mostrar en el pie de página"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_email
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Email"
msgstr "Correo electrónico"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_to
msgid "End hour"
msgstr "Hora de finalización"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model:ir.model,name:website_event_exhibitor.model_event_event
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Event"
msgstr "Evento"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Event Page"
msgstr "Página del evento"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor
msgid "Event Sponsor"
msgstr "Patrocinador de evento"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_sponsor_type
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_tree
msgid "Event Sponsor Level"
msgstr "Nivel de patrocinador de evento"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_type_view_form
msgid "Event Sponsor Levels"
msgstr "Niveles de patrocinador de eventos"

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_action_from_event
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Event Sponsors"
msgstr "Patrocinadores de evento"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_event_type
msgid "Event Template"
msgstr "Plantilla de evento"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__exhibitor
msgid "Exhibitor"
msgstr "Expositor"

#. module: website_event_exhibitor
#. odoo-python
#: code:addons/website_event_exhibitor/models/event_event.py:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar
msgid "Exhibitors"
msgstr "Expositores"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_type_view_form
msgid "Exhibitors Menu Item"
msgstr "Elemento de menú de expositores"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu_ids
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__website_event_menu__menu_type__exhibitor
msgid "Exhibitors Menus"
msgstr "Menú de expositores"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar
msgid "Filters"
msgstr "Filtros"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Contactos)"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome p. ej. fa-tasks"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__sponsor
msgid "Footer Logo Only"
msgstr "Logotipo solo en el pie de página"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type3
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__gold
msgid "Gold"
msgstr "Oro"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Group By"
msgstr "Agrupar por"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_4
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_5
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_6
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_7
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_8
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_9
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_1
msgid "Happy to be Sponsor"
msgstr "Feliz de ser patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__id
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__id
msgid "ID"
msgstr "ID"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si está marcada, hay nuevos mensajes que requieren su atención."

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si está marcada, algunos mensajes tienen error de envío."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_256
msgid "Image 256"
msgstr "Imagen 256"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_image_url
msgid "Image URL"
msgstr "URL de la imagen"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__event_date_tz
msgid ""
"Indicates the timezone in which the event dates/times will be displayed on "
"the website."
msgstr ""
"Indica la zona horaria en la que se mostrarán las fechas y horas del evento "
"en el sitio web."

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Inventory and Warehouse management"
msgstr "Gestión de inventario y almacén"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_published
msgid "Is Published"
msgstr "Está publicado"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"It provides post-graduate education in fluid dynamics\n"
"                    (research master in fluid dynamics, former \"Diploma\n"
"                    Course\", doctoral program, stagiaire program and lecture\n"
"                    series) and encourages \"training in research through\n"
"                    research\"."
msgstr ""
"Ofrece formación de posgrado en dinámica de fluidos\n"
"                    (maestría de investigación en dinámica de fluidos, conocido anteriormente como \"Diplomado\"\n"
"                    programa de doctorado, pasantía y conferencias) y fomenta la \"formación en investigación a través\n"
"                    de la investigación\"."

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_2
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_2
msgid ""
"It undertakes and promotes research in the field of fluid\n"
"                    dynamics. It possesses about fifty different wind tunnels,\n"
"                    turbomachinery and other specialized test facilities, some\n"
"                    of which are unique or the largest in the world. Extensive\n"
"                    research on experimental, computational and theoretical\n"
"                    aspects of gas and liquid flows is carried out under the\n"
"                    direction of the faculty and research engineers, sponsored\n"
"                    mainly by governmental and international agencies as well\n"
"                    as industries."
msgstr ""
"Realiza y promueve la investigación en el campo de la dinámica\n"
"                    de fluidos. Cuenta con cerca de cincuenta túneles de viento,\n"
"                    turbomaquinária y otras instalaciones de experimentación especializadas, algunas\n"
"                    de las cuales son únicas o las más grandes en el mundo. Se realiza\n"
"                    una amplia investigación sobre los aspectos experimentales, computacionales y teóricos\n"
"                    del flujo de gases y líquidos bajo la dirección\n"
"                    de la facultad y los ingenieros de investigación, con el patrocinio\n"
"                    principal del gobierno y de organismos internacionales, así como\n"
"                    de los sectores industriales."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Jitsi Name"
msgstr "Nombre de Jitsi"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Join us next time to meet"
msgstr "Visítenos la próxima vez para conocer"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "Join us there to meet"
msgstr "Visítenos para conocer"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_lang_id
msgid "Language"
msgstr "Idioma"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_uid
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__write_date
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_last_activity
msgid "Last activity"
msgstr "Última actividad"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Level"
msgstr "Nivel"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Level:"
msgstr "Nivel:"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "Live"
msgstr "En vivo"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__image_512
msgid "Logo"
msgstr "Logotipo"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Materials Management"
msgstr "Gestión de materiales"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_capacity
msgid "Max capacity"
msgstr "Máxima capacidad"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Maximum number of participant reached in the room at the same time"
msgstr "Número máximo de participantes alcanzado en la sala al mismo tiempo "

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_website_event_menu__menu_type
msgid "Menu Type"
msgstr "Tipo de menú"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error
msgid "Message Delivery error"
msgstr "Error de envío de mensaje"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_mobile
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Mobile"
msgstr "Móvil"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_card
msgid "More info"
msgstr "Más información"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_name
msgid "Name"
msgstr "Nombre"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de la siguiente actividad"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_type_id
msgid "Next Activity Type"
msgstr "Tipo de la siguiente actividad"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__no_ribbon
msgid "No Ribbon"
msgstr "Sin cinta"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "No exhibitor found."
msgstr "No se encontró ningún expositor."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Online"
msgstr "En línea"

#. module: website_event_exhibitor
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor__exhibitor_type__online
msgid "Online Exhibitor"
msgstr "Expositor en línea"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Opening Hours"
msgstr "Horario de apertura"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__hour_from
msgid "Opening hour"
msgstr "Horario de apertura"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid ""
"Our experts invent, imagine and develop solutions which meet\n"
"                your business requirements. They build a new technical\n"
"                environment for your company, but they always take the already\n"
"                installed IT software into account. That is why Idealis\n"
"                Consulting delivers excellence in HR and SC Management."
msgstr ""
"Nuestros expertos inventan, imaginan y desarrollan soluciones que satisfacen\n"
"                las necesidades de su empresa. Crean un nuevo entorno\n"
"                técnico para su empresa, pero siempre consideran\n"
"                el software de TI ya instalado. Es por eso que Idealis\n"
"                Consulting entrega excelencia para la gestión de RR. HH. y de cadena de suministro."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_participant_count
msgid "Participant count"
msgstr "Número de participantes"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_id
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Partner"
msgstr "Contacto"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_max_participant_reached
msgid "Peak participants"
msgstr "Asistencia máxima"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Personnel Administration"
msgstr "Administración de personal"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__partner_phone
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Phone"
msgstr "Teléfono"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_search
msgid "Published"
msgstr "Publicado"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_type_action
msgid ""
"Rank your sponsors based on your own grading system (e.g. \"Gold, Silver, "
"Bronze\")."
msgstr ""
"Clasifique a sus patrocinadores según su propio sistema de clasificación "
"(por ejemplo, \"Oro, Plata, Bronce\")."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Reporting"
msgstr "Informes"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__display_ribbon_style
msgid "Ribbon Style"
msgstr "Estilo de la cinta"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_is_full
msgid "Room Is Full"
msgstr "La sala está llena"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__room_name
msgid "Room Name"
msgstr "Nombre de la sala"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de envío del SMS"

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Sales and Distribution"
msgstr "Ventas y distribución"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_topbar
msgid "Search an exhibitor ..."
msgstr "Busque un expositor..."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sequence
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__exhibitor_menu
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_type__exhibitor_menu
msgid "Showcase Exhibitors"
msgstr "Lista de expositores"

#. module: website_event_exhibitor
#: model:event.sponsor.type,name:website_event_exhibitor.event_sponsor_type2
#: model:ir.model.fields.selection,name:website_event_exhibitor.selection__event_sponsor_type__display_ribbon_style__silver
msgid "Silver"
msgstr "Plata"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__subtitle
msgid "Slogan"
msgstr "Eslogan"

#. module: website_event_exhibitor
#. odoo-python
#: code:addons/website_event_exhibitor/models/event_sponsor.py:0
msgid "Sponsor"
msgstr "Patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_count
msgid "Sponsor Count"
msgstr "Número de patrocinadores"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__email
msgid "Sponsor Email"
msgstr "Correo electrónico del patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor_type__name
msgid "Sponsor Level"
msgstr "Nivel del patrocinador"

#. module: website_event_exhibitor
#: model:ir.actions.act_window,name:website_event_exhibitor.event_sponsor_type_action
#: model:ir.ui.menu,name:website_event_exhibitor.menu_event_sponsor_type
msgid "Sponsor Levels"
msgstr "Niveles del patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__mobile
msgid "Sponsor Mobile"
msgstr "Móvil del patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__name
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "Sponsor Name"
msgstr "Nombre del patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__phone
msgid "Sponsor Phone"
msgstr "Teléfono del patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__exhibitor_type
msgid "Sponsor Type"
msgstr "Tipo de patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__url
msgid "Sponsor Website"
msgstr "Sitio web del patrocinador"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_kanban
msgid "Sponsor image"
msgstr "Imagen del patrocinador"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_event__sponsor_ids
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_event_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Sponsors"
msgstr "Patrocinadores"

#. module: website_event_exhibitor
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action
#: model_terms:ir.actions.act_window,help:website_event_exhibitor.event_sponsor_action_from_event
msgid ""
"Sponsors are advertised on your event pages.<br>\n"
"    Exhibitors have a dedicated page a with chat room for people to connect with them."
msgstr ""
"Los patrocinadores se anuncian en las páginas de sus eventos.<br>\n"
"    Los expositores tienen una página dedicada con una sala de chat para que la gente conecte con ellos."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Sponsorship"
msgstr "Patrocinio"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__sponsor_type_id
msgid "Sponsorship Level"
msgstr "Nivel de patrocinio"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha límite ya ha pasado\n"
"Hoy: la fecha límite es hoy\n"
"Planificada: actividades futuras."

#. module: website_event_exhibitor
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_1
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_7_sponsor_3
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_0
#: model_terms:event.sponsor,website_description:website_event_exhibitor.event_sponsor_3
msgid "Talent Management"
msgstr "Gestión de talentos"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_url
msgid "The full URL to access the document through the website."
msgstr "La URL completa para acceder al documento a través del sitio web."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "The sponsor website description is missing."
msgstr "Falta la descripción de sitio web del patrocinador."

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__event_date_tz
msgid "Timezone"
msgstr "Zona horaria"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.snippet_options
msgid "Top Bar Filter"
msgstr "Filtro de barra superior"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción en el registro."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_thumb_details
msgid "Unpublished"
msgstr "No publicado"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_published
msgid "Visible on current website"
msgstr "Visible en el sitio web actual"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "We could not find any exhibitor at this moment."
msgstr "No pudimos encontrar ningún expositor en este momento."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitors_main
msgid "We could not find any exhibitor matching your search for:"
msgstr "No encontramos ningún expositor que coincida con su búsqueda:"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_tree
msgid "Website"
msgstr "Sitio web"

#. module: website_event_exhibitor
#: model:ir.model,name:website_event_exhibitor.model_website_event_menu
msgid "Website Event Menu"
msgstr "Menú de eventos del sitio web"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__website_url
msgid "Website URL"
msgstr "URL del sitio web"

#. module: website_event_exhibitor
#: model:ir.model.fields,help:website_event_exhibitor.field_event_sponsor__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: website_event_exhibitor
#: model:ir.model.fields,field_description:website_event_exhibitor.field_event_sponsor__is_in_opening_hours
msgid "Within opening hours"
msgstr "Dentro del horario de apertura"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "a few seconds"
msgstr "unos pocos segundos"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "e.g. \"Openwood specializes in home decoration...\""
msgstr "p. ej., \"Openwood se especializa en decoración para el hogar...\""

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : OpenWood Decoration"
msgstr "p. ej. decoración de OpenWood"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : https://www.odoo.com"
msgstr "p. ej.: https://www.odoo.com"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. : <EMAIL>"
msgstr "p. ej.: <EMAIL>"

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.event_sponsor_view_form
msgid "e.g. Your best choice for your home"
msgstr "p. ej. \"La mejor opción para su hogar\""

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
msgid "is not available right now."
msgstr "no está disponible en este momento."

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
msgid "is over."
msgstr "terminó."

#. module: website_event_exhibitor
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid ""
"is over.\n"
"                <br/>"
msgstr ""
"terminó.\n"
"                <br/>"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "starts in"
msgstr "comienza en"

#. module: website_event_exhibitor
#. odoo-javascript
#: code:addons/website_event_exhibitor/static/src/components/exhibitor_connect_closed_dialog/exhibitor_connect_closed_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_event_exhibitor.exhibitor_main
msgid "starts on"
msgstr "comienza el"
