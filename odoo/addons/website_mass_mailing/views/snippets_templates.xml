<?xml version="1.0" encoding="utf-8"?>
<odoo>
<template id="remove_external_snippets" inherit_id="website.external_snippets">
    <xpath expr="//t[@id='newsletter_snippet']" position="replace"/>
</template>

<template id="snippets" inherit_id="website.snippets">
    <xpath expr="//t[@id='mass_mailing_newsletter_block_hook']" position="replace">
        <t t-snippet="website_mass_mailing.s_newsletter_block" string="Newsletter Block" t-forbid-sanitize="form" group="contact_and_forms">
            <keywords>form, updates, digest, bulletin, announcements, notifications, communication, email, modal, alert, dialog, prompt, pop-up, subscription</keywords>
        </t>
    </xpath>
    <xpath expr="//t[@id='mass_mailing_newsletter_popup_hook']" position="replace">
        <t t-snippet="website_mass_mailing.s_newsletter_subscribe_popup" string="Newsletter Popup" t-forbid-sanitize="form" group="contact_and_forms"/>
    </xpath>
    <xpath expr="//t[@id='mass_mailing_newsletter_hook']" position="replace">
        <t t-snippet="website_mass_mailing.s_newsletter_subscribe_form" string="Newsletter" t-thumbnail="/website/static/src/img/snippets_thumbs/s_newsletter_subscribe_form.svg" t-forbid-sanitize="form"/>
    </xpath>
</template>

<!--
Users upgraded from a version lower than 16.0 may have those blocks in their
database, without the s_newsletter_list class. See fixNewsletterListClass.
-->
<template id="s_newsletter_subscribe_form" name="Newsletter">
    <div class="s_newsletter_subscribe_form s_newsletter_list js_subscribe" data-vxml="001" data-list-id="0" data-name="Newsletter Form">
        <div class="js_subscribed_wrap d-none">
            <p class="h4-fs text-center text-success"><i class="fa fa-check-circle-o" role="img"/> Thanks for registering!</p>
        </div>
        <div class="js_subscribe_wrap">
            <div class="input-group">
                <input type="email" name="email" class="js_subscribe_value form-control" placeholder="<EMAIL>"/>
                <a role="button" href="#" class="btn btn-primary js_subscribe_btn o_submit">Subscribe</a>
            </div>
        </div>
    </div>
</template>

<!--
Users upgraded from a version lower than 16.0 may have those blocks in their
database, without the s_newsletter_list class. See fixNewsletterListClass.
-->
<template id="s_newsletter_block" name="Newsletter Block">
    <section class="s_newsletter_block s_newsletter_list o_cc o_cc2 pt64 pb64" data-list-id="0">
        <div class="container">
            <t t-call="website_mass_mailing.s_newsletter_block_default_template"/>
        </div>
    </section>
</template>

<template id="s_newsletter_block_default_template" groups="base.group_user">
    <div class="row">
        <div class="col-lg-6">
            <h2 class="h4-fs">Subscribe to our newsletter</h2>
            <p class="text-muted">Get all the latest news, blog posts and product updates from our company, delivered directly to your inbox.</p>
        </div>
        <div class="col-lg-5 offset-lg-1">
            <t t-snippet-call="website_mass_mailing.s_newsletter_subscribe_form"/>
        </div>
    </div>
</template>

<template id="s_newsletter_block_form_template" groups="base.group_user">
    <section class="s_website_form" data-vcss="001" data-snippet="s_website_form" data-name="Form">
        <div class="o_container_small">
            <form id="newsletter_form" action="/website/form/" method="post" enctype="multipart/form-data" class="o_mark_required"
                  data-mark="*" data-model_name="mailing.contact" data-success-mode="message" hide-change-model="true">
                <div class="s_website_form_rows row s_col_no_bgcolor">
                    <div class="mb-0 py-2 col-12 s_website_form_field s_website_form_model_required" data-type="char" data-name="Field">
                        <div class="row s_col_no_resize s_col_no_bgcolor">
                            <label class="col-form-label col-sm-auto s_website_form_label" style="width: 250px" for="mailing_sub1">
                                <span class="s_website_form_label_content">Name</span>
                                <span class="s_website_form_mark"> *</span>
                            </label>
                            <div t-if="request.env['mailing.contact']._is_name_split_activated()" class="col-sm row">
                                <div class="col-sm-6">
                                    <input id="mailing_sub1" type="text" class="form-control s_website_form_input"
                                           name="first_name" placeholder="First Name" required="1"/>
                                </div>
                                <div class="col-sm-6">
                                    <input type="text" class="form-control s_website_form_input"
                                           name="last_name" placeholder="Last Name" required="1"/>
                                </div>
                            </div>
                            <div t-else="" class="col-sm">
                                <input id="mailing_sub1" type="text" class="form-control s_website_form_input" name="name" required="1" placeholder="John Smith"/>
                            </div>
                        </div>
                    </div>
                    <div class="mb-0 py-2 col-12 s_website_form_field s_website_form_model_required" data-type="email" data-name="Field">
                        <div class="row s_col_no_resize s_col_no_bgcolor">
                            <label class="col-form-label col-sm-auto s_website_form_label" style="width: 250px" for="mailing_sub2">
                                <span class="s_website_form_label_content">Email</span>
                                <span class="s_website_form_mark"> *</span>
                            </label>
                            <div class="col-sm">
                                <input id="mailing_sub2" type='email' class='form-control s_website_form_input' name="email" placeholder="<EMAIL>"/>
                            </div>
                        </div>
                    </div>
                    <div class="mb-0 py-2 col-12 s_website_form_field s_website_form_model_required" data-type="many2many" data-name="Field">
                        <div class="row s_col_no_resize s_col_no_bgcolor">
                            <label class="col-sm-auto s_website_form_label" style="width: 250px" for="mailing_list_">
                                <span class="s_website_form_label_content">Subscribe to</span>
                                <span class="s_website_form_mark"> *</span>
                            </label>
                            <div class="col-sm">
                                <div class="row s_col_no_resize s_col_no_bgcolor s_website_form_multiple" data-name="list_ids" data-display="horizontal">
                                    <t t-foreach="request.env['mailing.list'].search([('is_public', '=', True)])" t-as="record">
                                        <div class="checkbox col-12 col-lg-4 col-md-6">
                                            <div class="form-check">
                                                <input type="checkbox" class="s_website_form_input form-check-input" name="list_ids"
                                                    t-attf-id="mailing_list_#{record.id}" t-att-value="record.id" required="1"/>
                                                <label class="form-check-label s_website_form_check_label" t-attf-for="mailing_list_#{record.id}">
                                                    <t t-esc="record.name"/>
                                                </label>
                                            </div>
                                        </div>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-0 py-2 col-12 s_website_form_field s_website_form_custom s_website_form_required" data-type="boolean" data-name="Field">
                        <div class="row s_col_no_resize s_col_no_bgcolor">
                            <label class="col-sm-auto s_website_form_label" style="width: 250px;" for="mailing_sub3">
                                <span class="s_website_form_label_content">I agree to receive updates</span>
                                <span class="s_website_form_mark"> *</span>
                            </label>
                            <div class="col-sm">
                                <div class="form-check">
                                    <input type="checkbox" value="Yes" class="s_website_form_input form-check-input" name="I want to be kept updated"
                                           id="mailing_sub3" required="1"/>
                                </div>
                                <div class="s_website_form_field_description small form-text text-muted">
                                    We send one weekly newsletter per list and always try to keep it interesting. You can unsubscribe at any time.
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="s_website_form_submit s_website_form_no_submit_label col-12 mb-0 py-2 text-end" data-name="Submit Button">
                        <div style="width: 250px;" class="s_website_form_label"/>
                        <span id="s_website_form_result"></span>
                        <a href="#" role="button" class="btn btn-primary ms-auto ms-lg-0 s_website_form_send">Subscribe</a>
                    </div>
                </div>
            </form>
            <div class="s_website_form_end_message d-none">
                <div class="oe_structure">
                    <section class="s_text_block pt64 pb64 o_colored_level o_cc o_cc2 text-center" data-snippet="s_text_block">
                        <div class="container">
                            <i class="fa fa-paper-plane fa-2x mb-3 rounded-circle text-bg-success" role="presentation"/>
                            <h1 class="fw-bolder">Thank you for subscribing!</h1>
                            <p class="lead">You will now be informed about the latest news.</p>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>
</template>

<template id="s_newsletter_subscribe_popup" name="Newsletter Popup">
    <div class="s_popup o_newsletter_popup o_snippet_invisible" data-name="Newsletter Popup" data-vcss="001" data-invisible="1">
        <div class="modal fade s_popup_middle o_newsletter_modal"
             style="background-color: var(--black-50) !important;"
             data-show-after="5000" data-display="afterDelay" data-consents-duration="7"
             data-bs-focus="false" data-bs-backdrop="false" tabindex="-1" role="dialog">
            <div class="modal-dialog d-flex">
                <div class="modal-content oe_structure">
                    <div class="s_popup_close js_close_popup o_we_no_overlay o_not_editable" aria-label="Close">&#215;</div>
                    <section class="s_text_block oe_img_bg o_bg_img_center pt88 pb64" data-snippet="s_text_block" data-name="Text"
                             style="background-image: url('/web/image/website.s_cover_default_image'); background-position: 0 100%;">
                        <div class="container s_allow_columns">
                            <div class="row">
                                <div class="col-lg-12 bg-black-50">
                                    <h2 style="text-align: center;">Always <b>First</b>.</h2>
                                    <p style="text-align: center;">Be the first to find out all the latest news,<br/> products, and trends.</p>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section class="s_text_block" data-snippet="s_text_block" data-name="Text">
                        <div class="container">
                            <div class="row s_nb_column_fixed g-0">
                                <div class="col-lg-8 offset-lg-2 pt32 pb32">
                                    <t t-snippet-call="website_mass_mailing.s_newsletter_subscribe_form"/>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</template>

<template id="newsletter_subscribe_options" name="Newsletter Subscribe Options" inherit_id="website.snippet_options">
    <xpath expr="//*[@t-set='so_snippet_addition_selector']" position="inside" t-translation="off">, .o_newsletter_popup</xpath>
    <xpath expr="//div[1]" position="before">
        <div data-js="NewsletterLayout" data-selector=".s_newsletter_block">
            <we-select string="Template"
                       data-name="newsletter_template_opt"
                       data-attribute-name="newsletterTemplate"
                       data-attribute-default-value="email">
                <we-button title="Email Subscription" string="Email Subscription"
                           data-select-template="website_mass_mailing.s_newsletter_block_default_template"
                           data-select-data-attribute="email" data-name="email_opt"/>
                <we-button title="Form Subscription" string="Form Subscription"
                           data-select-template="website_mass_mailing.s_newsletter_block_form_template"
                           data-select-data-attribute="form" data-name="form_opt"/>
            </we-select>
        </div>
        <t t-call="website_mass_mailing.newsletter_subscribe_options_common">
            <t t-set="_selector">.o_newsletter_popup</t>
            <t t-set="_target">.s_newsletter_list</t>
        </t>
        <t t-call="website_mass_mailing.newsletter_subscribe_options_common">
            <t t-set="_selector">.s_newsletter_list</t>
            <t t-set="_exclude">.s_newsletter_block .s_newsletter_list, .o_newsletter_popup .s_newsletter_list</t>
        </t>
        <div data-selector=".js_subscribe" data-drop-near="p, h1, h2, h3, blockquote, .card"/>
    </xpath>
</template>

<template id="newsletter_subscribe_options_common">
    <div data-js="mailing_list_subscribe"
         t-att-data-selector="_selector"
         t-att-data-exclude="_exclude"
         t-att-data-target="_target">
        <we-select string="Newsletter" data-attribute-name="listId" data-dependencies="!form_opt"></we-select>
    </div>
    <div data-js="recaptchaSubscribe"
         t-att-data-selector="_selector"
         t-att-data-exclude="_exclude"
         t-att-data-target="_target">
        <t t-set="recaptcha_public_key" t-value="request.env['ir.config_parameter'].sudo().get_param('recaptcha_public_key')"/>
        <we-checkbox t-if="recaptcha_public_key" string="Show reCaptcha Policy" data-toggle-recaptcha-legal="" data-no-preview="true"/>
    </div>
</template>

<!-- Extend default mass_mailing snippets with website feature -->

<template id="s_mail_block_footer_social" inherit_id="mass_mailing.s_mail_block_footer_social">
    <xpath expr="//div[hasclass('o_mail_footer_links')]" position="inside">
        <t> | <a role="button" href="/contactus" class="btn btn-link">Contact</a></t>
    </xpath>
</template>

<template id="s_mail_block_footer_social_left" inherit_id="mass_mailing.s_mail_block_footer_social_left">
    <xpath expr="//div[hasclass('o_mail_footer_links')]" position="inside">
        <t> | <a role="button" href="/contactus" class="btn btn-link">Contact</a></t>
    </xpath>
</template>

</odoo>
