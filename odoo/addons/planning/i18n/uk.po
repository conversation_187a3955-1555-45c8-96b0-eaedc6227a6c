# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* planning
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: planning
#. odoo-python
#: code:addons/planning/models/resource_resource.py:0
msgid "%(resource_name)s (%(role)s)"
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "%s (copy)"
msgstr "%s (копія)"

#. module: planning
#: model:ir.actions.report,print_report_name:planning.report_planning_slot
msgid "'Planning'"
msgstr "'Планування'"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid "(%s days span)"
msgstr "(%s діапазон днів)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid ".&amp;nbsp;"
msgstr ".&amp;nbsp;"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "01/01/2023 5:00 PM"
msgstr "01/01/2023 5:00 PM"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "01/01/2023 9:00 AM"
msgstr "01/01/2023 9:00 AM"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "04:00"
msgstr "04:00"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "08:00"
msgstr "08:00"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "1 Onsite Interview"
msgstr "1 Співбесіда офлайн"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "1 Phone Call"
msgstr "1 Телефонний дзвінок"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "100"
msgstr "100"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "12:00"
msgstr "12:00"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "13"
msgstr "13"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "14"
msgstr "14"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "15"
msgstr "15"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "16:00"
msgstr "16:00"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "2 open days"
msgstr "2 відкриті дні"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "4 Days after Interview"
msgstr "4 дні після співбесіди"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "8 hours"
msgstr "8 годин"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<b class=\"o_gantt_title d-flex align-items-center justify-content-center "
"bg-100 position-sticky start-0 p-2 border-end\"> Schedule</b>"
msgstr ""

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid "<b class=\"tip_title\">Tip: Record your planning faster</b>"
msgstr "<b class=\"tip_title\">Порада: Записуйте ваше планування швидше</b>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Allocated Time:</b>"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"<b>Drag & drop</b> your shift to reschedule it. <i>Tip: hit CTRL (or Cmd) to"
" duplicate it instead.</i> <b>Adjust the size</b> of the shift to modify its"
" period."
msgstr ""
"<b>Перетягніть</b> вашу зміну, щоб запланувати її знову. <i>Порада: "
"натомість натисніть CTRL (або Cmd), щоби продублювати зміну.</i> "
"<b>Відрегулюйте розмір</b> зміни, щоб змінити її період."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Note:</b>"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "<b>Publish & send</b> your employee's planning."
msgstr "<b>Опублікуйте та надішліть</b> планування вашого співробітника."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Role:</b>"
msgstr ""

#. module: planning
#: model:mail.template,body_html:planning.email_template_shift_switch_email
msgid ""
"<div>\n"
"                    <p t-if=\"ctx.get('old_assignee_name')\">Dear <t t-out=\"ctx['old_assignee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p>\n"
"                        The following shift that you requested to switch has been re-assigned\n"
"                        <t t-if=\"ctx.get('new_assignee_name')\"> to <t t-out=\"ctx['new_assignee_name']\">Marc Demo</t></t>.\n"
"                    </p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Date</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    05/31/2021, 8:00 AM ⟶ 05/31/2021, 4:00 PM\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_hours']\">4:00</t>h)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_percentage']\">50</t>%)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Role</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Gathering</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Sales Order Item</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Coffee</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Note</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <br/>\n"
"                </div>\n"
"            "
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/>\n"
"                        You have successfully requested to switch your shift. You will be notified when another employee takes over your shift."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/> Request to switch shift cancelled "
"successfully."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid "<i class=\"fa fa-check-circle\"/> This shift is no longer assigned to you."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/> You were successfully assigned this open "
"shift."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Hours\" title=\"Hours\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Hours\" title=\"Hours\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_assignee_contact_popover
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Mail\" role=\"img\" style=\"padding-"
"right: 2px\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o\" title=\"Mail\" role=\"img\" style=\"padding-"
"right: 2px\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> This shift is already assigned to "
"another employee."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> You can no longer unassign yourself "
"from this shift."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right my-1 mx-1\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right my-1 mx-1\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid "<i class=\"fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_assignee_contact_popover
msgid "<i class=\"fa fa-phone\" title=\"Phone\" role=\"img\" style=\"padding-right: 2px\"/>"
msgstr "<i class=\"fa fa-phone\" title=\"Phone\" role=\"img\" style=\"padding-right: 2px\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "<i class=\"mt-1 fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Bartender</span>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Waiter</span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "<span class=\"ms-0 me-4\">Edit</span>"
msgstr "<span class=\"ms-0 me-4\">Редагувати</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">04:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">04:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">12:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">12:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">16:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">16:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">04:00</span>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">08:00</span>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">12:00</span>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">1:00 PM - 5:00 "
"PM</span>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">8:00 AM - 12:00 "
"PM</span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid ""
"<span class=\"o_start_date\"/>\n"
"                                    <i class=\"mx-1 fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                    <span class=\"o_end_date\"/>"
msgstr ""
"<span class=\"o_start_date\"/>\n"
"                                    <i class=\"mx-1 fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                    <span class=\"o_end_date\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
msgid "<span class=\"o_stat_text\">Planning</span>"
msgstr "<span class=\"o_stat_text\">Планування</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr "<span class=\"text-muted small\">Днів для отримання пропозиції</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Обробити</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Час на відповідь</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1 text-start\">Open Shifts</span>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Doris Cole </span> "
"<span class=\"text-muted text-truncate\">(Bartender)</span> </span>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Eli Lambert </span> "
"<span class=\"text-muted text-truncate\">(Waiter)</span> </span>"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Jeffrey Kelly </span> "
"<span class=\"text-muted text-truncate\">(Bartender)</span> </span>"
msgstr ""

#. module: planning
#: model_terms:web_tour.tour,rainbow_man_message:planning.planning_tour
msgid ""
"<span><b>Congratulations!</b> You are now a master of planning.\n"
"        </span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>Allow employees to:</span>"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid ""
"<span>The employee assigned would like to switch shifts with someone "
"else.</span>"
msgstr ""
"<span>Призначений працівник хотів би помінятися змінами з кимось "
"іншим.</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>months ahead</span>"
msgstr "<span>місяців вперед</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Allocated Time — </strong>"
msgstr "<strong>Розподілений час — </strong>"

#. module: planning
#: model:mail.template,body_html:planning.email_template_slot_single
msgid ""
"<style>\n"
"                    .planning_mail_template_button {\n"
"                        padding: 5px 10px;\n"
"                        text-decoration: none;\n"
"                        border: 1px;\n"
"                        border-radius: 3px;\n"
"                        text-align: center;\n"
"                        color: #374151;\n"
"                        background-color: #E7E9ED;\n"
"                        border: solid #E7E9ED;\n"
"                        flex: 1 1 40%;\n"
"                    }\n"
"                    .top_button {\n"
"                        color: #FFFFFF;\n"
"                        background-color: #714B67;\n"
"                        border: solid #714B67;\n"
"                        flex: 1 1 60%;\n"
"                    }\n"
"                    .button_box {\n"
"                        display: flex;\n"
"                        flex-wrap: wrap;\n"
"                        gap: 10px;\n"
"                        margin: 20px 15px 0px 15px;\n"
"                    }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee_name')\">Dear <t t-out=\"ctx['employee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">We have a new shift opening:</p>\n"
"                    <p t-else=\"\">You have been assigned the following shift:</p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Date</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    05/31/2021, 8:00 AM ⟶ 05/31/2021, 4:00 PM\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_hours']\">4:00</t>h)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_percentage']\">50</t>%)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Role</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Gathering</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Sales Order Item</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Coffee</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Note</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <div class=\"button_box\">\n"
"                        <a t-if=\"ctx.get('available_link')\" t-att-href=\"ctx['available_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">Assign me this shift</a>\n"
"                        <a t-if=\"ctx.get('unavailable_link')\" t-att-href=\"ctx['unavailable_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">I am unavailable</a>\n"
"                        <a t-if=\"ctx.get('google_url')\" t-att-href=\"ctx['google_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Add to Google Calendar</a>\n"
"                        <a t-if=\"ctx.get('iCal_url')\" t-att-href=\"ctx['iCal_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Add to iCal/Outlook</a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">If you are interested and available, please assign yourself this open shift.</p>\n"
"                    <p t-else=\"\">In case the current shift doesn't suit you, we encourage you to reach out to your colleagues and request to switch shifts. They might be interested in exchanging shifts with you.</p>\n"
"                </div>\n"
"            </body>"
msgstr ""

#. module: planning
#: model:mail.template,body_html:planning.email_template_planning_planning
msgid ""
"<style>\n"
"                .planning_mail_template_button {\n"
"                    padding: 5px 10px;\n"
"                    text-decoration: none;\n"
"                    border: 1px;\n"
"                    border-radius: 3px;\n"
"                    display: inline-block; \n"
"                    width: 190px;\n"
"                    text-align: center;\n"
"                }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee')\">Dear <t t-out=\"ctx['employee'].name or ''\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\">\n"
"                        Your upcoming shifts from <t t-out=\"format_date(ctx['start_datetime'])\">05-05-2021</t>\n"
"                        to <t t-out=\"format_date(ctx['end_datetime'])\">05-11-2021</t> have been published.\n"
"                    </p>\n"
"                    <div style=\"display: flex; margin: 15px;\">\n"
"                        <div t-if=\"ctx.get('planning_url')\" style=\"margin-right: 15px;\">\n"
"                            <a t-att-href=\"ctx['planning_url']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #FFFFFF; background-color: #875A7B; border: solid #875A7B;\">View Your Planning</a>\n"
"                        </div>\n"
"                        <div t-if=\"ctx.get('planning_url_ics')\">\n"
"                            <a t-att-href=\"ctx['planning_url_ics']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #374151; background-color: #E7E9ED; border: solid #E7E9ED;\">Add to Calendar</a>\n"
"                        </div>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('slot_unassigned') or (object.allow_self_unassign and object.self_unassign_days_before)\">\n"
"                        <t t-if=\"ctx.get('slot_unassigned')\">\n"
"                            We would also like to remind you that there are some open shifts available, and if you are interested and available, please assign yourself to those shifts.\n"
"                        </t>\n"
"                        <t t-if=\"object.allow_self_unassign and object.self_unassign_days_before\">\n"
"                            If you are unable to work a shift that has been assigned to you, please unassign yourself within <t t-out=\"object.self_unassign_days_before or ''\">5</t> day(s) before the start of the shift.\n"
"                        </t>\n"
"                    </p>\n"
"                    <p t-if=\"ctx.get('message')\" t-out=\"ctx['message']\"/>\n"
"                    <p t-if=\"object.allow_self_unassign\">In case your current schedule doesn't suit you, we encourage you to reach out to your colleagues and request to switch shifts. They might be interested in exchanging shifts with you.</p>\n"
"                </div>\n"
"            </body>"
msgstr ""

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_until_limit
msgid ""
"A recurrence repeating itself until a certain date must have its limit set"
msgstr "Повторення до певної дати має встановити межу"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "A shift must be in the same company as its recurrence."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__active
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__active
msgid "Active"
msgstr "Активно"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.js:0
msgid "Add Shift"
msgstr "Додати зміну"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__note
msgid "Additional message displayed in the email sent to employees"
msgstr ""
"В електронному листі, надісланому співробітникам, відображається додаткове "
"повідомлення"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Additional message included in the email sent to your employees"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Additional note sent to the employee"
msgstr "Додаткова примітка надіслана співробітнику"

#. module: planning
#: model:res.groups,name:planning.group_planning_manager
msgid "Administrator"
msgstr "Адміністратор"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid ""
"All open shifts have already been assigned, or there are no resources "
"available to take them at this time."
msgstr ""
"Усі відкриті зміни вже призначено, або наразі немає ресурсів для їх "
"виконання."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__all
msgid "All shifts"
msgstr "Усі зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid ""
"All subsequent shifts will be deleted. Are you sure you want to continue?"
msgstr "Усі наступні зміни буде видалено. Ви впевнені, що хочете продовжити?"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Allocated Hours"
msgstr "Розподілені години"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Allocated Percentage"
msgstr "Відсоток розподілення"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__allocated_hours
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_hours
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Allocated Time"
msgstr "Розподілений час"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_percentage
msgid "Allocated Time %"
msgstr "Призначений час %"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__allocated_percentage
msgid "Allocated Time (%)"
msgstr "Розподілений час (%)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Allocated Time:"
msgstr "Розподілений час:"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_allocated_hours_positive
msgid "Allocated hours and allocated time percentage cannot be negative."
msgstr ""
"Розподілені години та відсоток виділеного часу не можуть бути від’ємними."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocation_type
msgid "Allocation Type"
msgstr "Тип розподілу"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid ""
"Analyze the allocation of your resources across roles, projects, and sales "
"orders, and estimate future needs."
msgstr ""
"Проаналізуйте розподіл своїх ресурсів між ролями, проектами та замовленнями "
"на продаж і оцініть майбутні потреби."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
msgid "Archived"
msgstr "Заархівовано"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/resource_form/resource_form_controller.js:0
#: code:addons/planning/static/src/views/resource_list/resource_list_controller.js:0
msgid ""
"Archiving this resource will transform all of its future shifts into open "
"shifts. Are you sure you want to continue?"
msgstr ""
"Архівування цього ресурсу перетворить усі його майбутні зміни на відкриті "
"зміни. Ви впевнені, що бажаєте продовжити?"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer.js:0
msgid "Are you sure you want to delete this shift?"
msgstr "Ви впевнені, що хочете видалити цю зміну?"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Arrow"
msgstr "Стрілка"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Arrow icon"
msgstr "Іконка стрілки"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Ask To Switch"
msgstr "Надіслати запит на заміну"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Ask to Switch"
msgstr "Надіслати запит на заміну"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Assign a <b>resource</b>, or leave it open for the moment. <i>Tip: Create "
"open shifts for the roles you will be needing to complete a mission. Then, "
"assign those open shifts to the resources that are available.</i>"
msgstr ""
"Призначайте <b>ресурс</b>, або залиште відкритим на момент. <i>Порада: "
"Створіть відкриті зміни для ролей, які вам знадобляться для виконання місії."
" Потім призначте ці відкриті зміни до доступних ресурсів.</i>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "Assignee"
msgstr "Призначений"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Auto Plan"
msgstr "Автоматичне планування"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Automatically plan open shifts and sales orders"
msgstr "Автоматично планувати відкриті зміни і замовлення на продаж"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_filter_panel/planning_calendar_filter_panel.xml:0
msgid "Avatar"
msgstr "Аватар"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Bartender"
msgstr "Бармен"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee_base
msgid "Basic Employee"
msgstr "Звичайний користувач"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_resource
msgid "By Resource"
msgstr "За ресурсом"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_role
msgid "By Role"
msgstr "За роллю"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "CANCEL SWITCH"
msgstr "СКАСУВАТИ ЗАМІНУ"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Cancel Switch"
msgstr "Скасувати заміну"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Chat"
msgstr "Чат"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__color
#: model:ir.model.fields,field_description:planning.field_planning_slot__color
msgid "Color"
msgstr "Колір"

#. module: planning
#: model:planning.role,name:planning.planning_role_cm
msgid "Community Manager"
msgstr "Менеджер спільноти"

#. module: planning
#: model:ir.model,name:planning.model_res_company
msgid "Companies"
msgstr "Компанії"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__company_id
#: model:ir.model.fields,field_description:planning.field_planning_planning__company_id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__company_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__company_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Company"
msgstr "Компанія"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_planning__company_id
msgid ""
"Company linked to the material resource. Leave empty for the resource to be "
"available in every company."
msgstr ""
"Компанія пов'язана з матеріальними ресурсами. Залиште порожнім, щоб ресурс "
"був доступний у кожній компанії."

#. module: planning
#: model:ir.model,name:planning.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_settings
msgid "Configuration"
msgstr "Налаштування"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.xml:0
msgid "Confirm"
msgstr "Підтвердити"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
msgid "Copy previous"
msgstr "Копіювати попередні"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Copy previous week"
msgstr "Скопіювати попередній тиждень"

#. module: planning
#: model:planning.role,name:planning.planning_role_crane
msgid "Crane"
msgstr "Журавель"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_uid
msgid "Created by"
msgstr "Створив"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_date
#: model:ir.model.fields,field_description:planning.field_planning_role__create_date
#: model:ir.model.fields,field_description:planning.field_planning_send__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_date
msgid "Created on"
msgstr "Створено"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Date"
msgstr "Дата"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__date_end
msgid "Date End"
msgstr "Дата кінця"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__date_start
msgid "Date Start"
msgstr "Дата початку"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__day
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__day
msgid "Days"
msgstr "Дні"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_res_company__planning_self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_self_unassign_days_before
msgid "Days before shift for unassignment"
msgstr "Днів до відмови від призначення зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Deadline"
msgstr "Кінцевий термін"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_planning__self_unassign_days_before
#: model:ir.model.fields,help:planning.field_planning_slot__self_unassign_days_before
#: model:ir.model.fields,help:planning.field_res_company__planning_self_unassign_days_before
#: model:ir.model.fields,help:planning.field_res_config_settings__planning_self_unassign_days_before
msgid "Deadline in days for shift unassignment"
msgstr "Дедлайн у днях для відмови від призначення зіни"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__default_planning_role_id
#: model:ir.model.fields,field_description:planning.field_resource_resource__default_role_id
msgid "Default Role"
msgstr "Типова роль"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_search_view_inherit
msgid "Default Roles"
msgstr "Типові ролі"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid ""
"Define a work email address for the following employees so they will receive"
" the planning."
msgstr ""
"Визначте робочу адресу електронної пошти для наступних працівників, щоб вони"
" отримували планування."

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__role_id
msgid ""
"Define the roles your resources perform (e.g. Chef, Bartender, Waiter...). "
"Create open shifts for the roles you need to complete a mission. Then, "
"assign those open shifts to the resources that are available."
msgstr ""
"Визначте ролі, які виконують ваші ресурси (наприклад, шеф-кухар, бармен, "
"офіціант...). Створюйте відкриті зміни для ролей, які вам потрібні для "
"виконання місії. Потім призначте ці відкриті зміни до доступних ресурсів."

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"Define the roles your resources perform (e.g. Chef, Bartender, Waiter...). "
"Create open shifts for the roles you will be needing to complete a mission. "
"Then, assign those open shifts to the resources that are available."
msgstr ""
"Визначте ролі, які виконують ваші ресурси (наприклад, шеф-кухар, бармен, "
"офіціант...). Створіть відкриті зміни для ролей, які вам знадобляться для "
"виконання місії. Потім призначте ці відкриті зміни до доступних ресурсів."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_kanban
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Delete"
msgstr "Видалити"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.js:0
msgid "Delete Recurring Shift"
msgstr "Видалити повторювану зміну"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__department_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__department_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Department"
msgstr "Відділ"

#. module: planning
#: model:ir.model,name:planning.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Помічник звільнення"

#. module: planning
#: model:planning.role,name:planning.planning_role_developer
msgid "Developer"
msgstr "Розробник"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Discard"
msgstr "Відмінити"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__display_name
#: model:ir.model.fields,field_description:planning.field_planning_planning__display_name
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__display_name
#: model:ir.model.fields,field_description:planning.field_planning_role__display_name
#: model:ir.model.fields,field_description:planning.field_planning_send__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_resources
msgid "Distribute your material resources across projects and sales orders."
msgstr ""
"Розподіліть свої матеріальні ресурси між проектами та замовленнями на "
"продаж."

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Doris Cole (Bartender)"
msgstr "Doris Cole (Бармен)"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__state__draft
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__state__draft
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Draft"
msgstr "Чернетка"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid ""
"Drag a shift to another day to reschedule it, or to another row to reassign "
"the shift. Press CTRL (or Cmd on Mac) while dragging a shift to duplicate "
"it."
msgstr ""
"Перетягніть зміну на інший день, щоб перепланувати її, або в інший рядок, "
"щоб перепризначити зміну. Натисніть CTRL (або Cmd на Mac), перетягуючи зсув,"
" щоб скопіювати його."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__duration
msgid "Duration"
msgstr "Тривалість"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__duration_days
msgid "Duration Days"
msgstr "Тривалість днів"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_kanban
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Edit"
msgstr "Редагувати"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.xml:0
msgid "Edit Recurrent Shift"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Eli Lambert (Waiter)"
msgstr "Eli Lambert (Офіціант)"

#. module: planning
#: model:mail.template,description:planning.email_template_shift_switch_email
msgid ""
"Email sent automatically when an employee self-assigns to the unwanted shift"
" of another employee, notifying the person who requested to switch that the "
"shift has been taken"
msgstr ""

#. module: planning
#: model:mail.template,description:planning.email_template_slot_single
msgid "Email sent automatically when publishing a shift"
msgstr "При публікації зміни автоматично відправляється лист"

#. module: planning
#: model:mail.template,description:planning.email_template_planning_planning
msgid ""
"Email sent automatically when publishing the schedule of your employees"
msgstr ""
"Електронна пошта надсилається автоматично під час публікації розкладу ваших "
"співробітників"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__employee_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__employee_id
msgid "Employee"
msgstr "Співробітник"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Employee Name"
msgstr "ПІБ співробітника"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_employee_unavailabilities
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_employee_unavailabilities
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Employee Unavailabilities"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__employee_ids
#: model:ir.ui.menu,name:planning.planning_menu_settings_employee
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Employees"
msgstr "Співробітники"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid "Employees with no Work Email"
msgstr "Співробітники без робочого Email"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "End"
msgstr "Кінець"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "End Date"
msgstr "Кінцева дата"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__end_time
msgid "End Hour"
msgstr "Години звершення"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_hr_employee_employee_token_unique
msgid "Error: each employee token must be unique"
msgstr "Помилка: кожен токен співробітника повинен бути унікальним"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "Every %(repeat_interval)s week(s) until %(repeat_until)s"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__note
msgid "Extra Message"
msgstr "Додаткове повідомлення"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
msgid ""
"Find here your planning. Assign yourself open shifts that match your roles, "
"or indicate your unavailability."
msgstr ""
"Знайдіть тут своє планування. Призначте собі відкриті зміни, які "
"відповідають вашим ролям, або вкажіть свою неготовність."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "First you have to specify the date of the invitation."
msgstr "Спочатку потрібно вказати дату запрошення."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Fixed Hours"
msgstr "Фіксовані години"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Flexible Hours"
msgstr "Гнучкі години"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__forecast
msgid "Forecast"
msgstr "Прогноз"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__forever
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__forever
msgid "Forever"
msgstr "Назавжди"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "Forever, every %s week(s)"
msgstr "Завжди, кожен %s тиждень(і)"

#. module: planning
#: model:planning.role,name:planning.planning_furniture_assembler
msgid "Furniture Assembler"
msgstr ""

#. module: planning
#: model:planning.role,name:planning.planning_furniture_tool
msgid "Furniture Tools"
msgstr "Меблеві інструменти"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Generate shifts"
msgstr "Створити зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Give depth to your"
msgstr "Надайте інформацію вашій"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Group By"
msgstr "Групувати за"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__has_slots
#: model:ir.model.fields,field_description:planning.field_hr_employee_base__has_slots
#: model:ir.model.fields,field_description:planning.field_hr_employee_public__has_slots
msgid "Has Slots"
msgstr "Є слоти"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__request_to_switch
msgid "Has there been a request to switch on this shift slot?"
msgstr "Чи був запит замінити цей слот зміни?"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__name
msgid "Hours"
msgstr "Години"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__resource_type__user
msgid "Human"
msgstr "Людський"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "I Am Unavailable"
msgstr "Я недоступний"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "I Take It"
msgstr "Я візьму це"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "I am unavailable"
msgstr "Я недоступний"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__id
#: model:ir.model.fields,field_description:planning.field_planning_planning__id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__id
#: model:ir.model.fields,field_description:planning.field_planning_role__id
#: model:ir.model.fields,field_description:planning.field_planning_send__id
#: model:ir.model.fields,field_description:planning.field_planning_slot__id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__id
msgid "ID"
msgstr "ID"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_analysis_report__publication_warning
#: model:ir.model.fields,help:planning.field_planning_slot__publication_warning
msgid ""
"If checked, it means that the shift contains has changed since its last "
"publish."
msgstr ""
"Якщо позначено, це означає, що зміст зміни було змінено з часу його "
"останньої публікації."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"If you are happy with your planning, you can now <b>send</b> it to your "
"employees."
msgstr ""
"Якщо ви задоволені плануванням, ви можете тепер <b>відправити</b> його вашим"
" співробітникам."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__include_unassigned
msgid "Include Open Shifts"
msgstr "Включаючи відкриті зміни"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__include_unassigned
msgid "Includes Open Shifts"
msgstr "Включає відкриті зміни"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Jeffrey Kelly (Bartender)"
msgstr "Jeffrey Kelly (Бармен)"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__job_title
#: model:ir.model.fields,field_description:planning.field_planning_slot__job_title
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Job Title"
msgstr "Назва посади"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_date
#: model:ir.model.fields,field_description:planning.field_planning_role__write_date
#: model:ir.model.fields,field_description:planning.field_planning_send__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
msgid "Legend"
msgstr "Історія"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__allow_self_unassign
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_self_unassign
msgid "Let Employee Unassign Themselves"
msgstr "Дозволити співробітникам самостійно знімати із себе зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"Let employees switch shifts with colleagues or unassign themselves when "
"unavailable"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Let's check out the Gantt view for cool features. Get ready to <b>share your"
" schedule</b> and easily plan your shifts with just one click by <em>copying"
" the previous week's schedule</em>."
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Let's create your first <b>shift</b>. <i>Tip: use the (+) shortcut available"
" on each cell of the Gantt view to save time.</i>"
msgstr ""
"Створіть вашу першу <b>зміну</b>. <i>Порада: ля економії часу використовуйте"
" швидкий набір (+) доступний на кожній клітинці в прегеляді Ганта.</i>"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Let's schedule a <b>shift</b> for this time range."
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Let's start managing your employees' schedule!"
msgstr "Давайте почнемо керування розкладом ваших співробітників!"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "List"
msgstr "Список"

#. module: planning
#: model:hr.job,name:planning.job_maintenance_technician
#: model:planning.role,name:planning.planning_maintenance_technician
msgid "Maintenance Technician"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "Make encoding shifts easy with templates."
msgstr "Кодуйте зміни легко за допомогою шаблонів."

#. module: planning
#: model:planning.role,name:planning.planning_role_management
msgid "Management"
msgstr "Управління"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__manager_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__manager_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Manager"
msgstr "Керівник співробітника"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__resource_type__material
msgid "Material"
msgstr "Матеріал"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_resources
#: model:ir.ui.menu,name:planning.planning_menu_settings_resource
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Materials"
msgstr "Матеріали"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "May 2024"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__publication_warning
#: model:ir.model.fields,field_description:planning.field_planning_slot__publication_warning
msgid "Modified Since Last Publication"
msgstr "Змінено після останньої публікації"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "Month"
msgstr "Місяць"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__month
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__month
msgid "Months"
msgstr "Місяці"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Department"
msgstr "Мій відділ"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_my_calendar
#: model:ir.actions.act_window,name:planning.planning_action_open_shift
#: model:ir.ui.menu,name:planning.planning_menu_my_planning
msgid "My Planning"
msgstr "Моє планування"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Roles"
msgstr "Мої ролі"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Shifts"
msgstr "Мої зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Team"
msgstr "Моя команда"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__name
msgid "Name"
msgstr "Назва"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "New Event"
msgstr "Нова подія"

#. module: planning
#. odoo-javascript
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "New Shift"
msgstr "Нова зміна"

#. module: planning
#. odoo-python
#: code:addons/planning/controllers/main.py:0
msgid "New_Shift"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Next Week"
msgstr "Наступного тижня"

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "No Email Address for Some Employees"
msgstr ""

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_number
msgid "No Of Repetitions of the plannings"
msgstr "Немає повторення планувань"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid "No data yet!"
msgstr "Ще немає даних!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_resources
msgid "No material resources found. Let's create one!"
msgstr "Не знайдено матеріальних ресурів. Створіть його!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "No roles found. Let's create one!"
msgstr "Не знайдено жодної ролі. Створіть її!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "No shift templates found. Let's create one!"
msgstr "Не знайдено шаблону змін. Створіть його!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_resource
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid "No shifts found. Let's create one!"
msgstr "Не знайдено змін. Створіть їх!"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__name
#: model:ir.model.fields,field_description:planning.field_planning_slot__name
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Note"
msgstr "Примітка"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Note:"
msgstr "Примітка:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Now that this week is ready, let's get started on <b>next week's "
"schedule</b>."
msgstr ""
"Тепер, оскільки цей тиждень вже готовий, давайте розпочнемо з <b>розкладу на"
" наступний тиждень</b>."

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__x_times
msgid "Number of Occurrences"
msgstr "Кількість випадків"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__x_times
msgid "Number of Repetitions"
msgstr "Кількість повторень"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Open Shift"
msgstr "Відкрита зміна"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_model.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_model.js:0
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Open Shifts"
msgstr "Відкриті зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid "Open Shifts Available"
msgstr "Доступні відкриті зміни"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid "Open shifts assigned"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid "Open shifts unscheduled"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "Open: %s"
msgstr "Відкрити: %s"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid ""
"Operation not supported, you should always compare overlap_slot_count to 0 "
"value with = or > operator."
msgstr ""
"Операція не підтримується, завжди потрібно порівнювати overlap_slot_count до"
" 0 значення з = або > оператора."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Period"
msgstr "Період"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"Plan resource allocation across projects and estimate deadlines more "
"accurately"
msgstr "Плануйте розподіл ресурсів між проектами та точніше оцінюйте терміни"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Plan your shifts in one click by <b>copying the schedule from the previous "
"week</b>."
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Plan your shifts in one click by <b>copying the schedule from the previous "
"week</b>. Open the menu to access this option."
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__start_time
msgid "Planned Hours"
msgstr "Заплановані години"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: model:ir.actions.report,name:planning.report_planning_slot
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__planning
#: model:ir.ui.menu,name:planning.planning_menu_root
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_view_my_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_pivot
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Planning"
msgstr "Планування"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_report_action_analysis
#: model:ir.ui.menu,name:planning.planning_menu_planning_analysis
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_pivot
msgid "Planning Analysis"
msgstr "Аналіз планування"

#. module: planning
#: model:ir.model,name:planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "Звіт аналізу планування"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Planning Meeting"
msgstr "Планування зустрічі"

#. module: planning
#: model:ir.model,name:planning.model_planning_recurrency
msgid "Planning Recurrence"
msgstr "Повторення планування"

#. module: planning
#: model:ir.model,name:planning.model_planning_role
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
msgid "Planning Role"
msgstr "Роль планування"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "Planning Role List"
msgstr "Список ролей планування"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot
msgid "Planning Shift"
msgstr "Планування зміни"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__slot_id
msgid "Planning Slot"
msgstr "Слот планування"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__slot_properties_definition
msgid "Planning Slot Properties"
msgstr "Планування властивостей слота"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Planning:"
msgstr "Планування:"

#. module: planning
#: model:mail.template,name:planning.email_template_planning_planning
msgid "Planning: New Schedule"
msgstr "Планування: Новий розклад"

#. module: planning
#: model:mail.template,name:planning.email_template_slot_single
msgid "Planning: New Shift"
msgstr "Планування: Нова зміна"

#. module: planning
#: model:mail.template,name:planning.email_template_shift_switch_email
msgid "Planning: Shift Re-assigned"
msgstr "Планування: Зміна перепризначена"

#. module: planning
#: model:ir.actions.server,name:planning.ir_cron_forecast_schedule_ir_actions_server
msgid "Planning: generate next recurring shifts"
msgstr "Планування: створити наступні повторювані зміни"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Planning: new open shift available on"
msgstr "Планування: нові відкриті зміни доступні на"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Planning: new shift on"
msgstr "Планування: нова зміна на"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Plans"
msgstr "Плани"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_email
msgid ""
"Please add a work email for the following employee so that they can receive "
"their planning:"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Prepare for the ultimate multi-tasking challenge:"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer_controls.xml:0
msgid "Press Ctrl to duplicate the shift"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_config_settings__module_project_forecast
msgid "Project Planning"
msgstr "Планування проекту"

#. module: planning
#: model:planning.role,name:planning.planning_role_projector
msgid "Projector"
msgstr "Проектор"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__slot_properties
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Properties"
msgstr "Властивості"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Publish"
msgstr "Опублікувати"

#. module: planning
#: model:ir.actions.server,name:planning.model_planning_slot_action_publish_and_send
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Publish & Send"
msgstr "Опублікувати та Надіслати"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_send_action
msgid "Publish & Send the Schedule by Email"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__state__published
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__state__published
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Published"
msgstr "Опубліковано"

#. module: planning
#: model:hr.job,name:planning.job_quality_control
#: model:planning.role,name:planning.planning_quality_control
msgid "Quality Control Inspector"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_generation_interval
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_generation_interval
msgid "Rate Of Shift Generation"
msgstr "Створення ставки зміни"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__recurrence_update
msgid "Recurrence Update"
msgstr "Оновлення повторення"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__recurrency_id
msgid "Recurrency"
msgstr "Повторення"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Recurring Shifts"
msgstr "Повторювані зміни"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "Recurring shifts created"
msgstr ""

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__slot_ids
msgid "Related Planning Entries"
msgstr "Пов'язані записи розкладу"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__user_id
msgid "Related user name for the resource to manage its access."
msgstr "Пов'язане ім'я користувача ресурсу для управління доступом."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Remove"
msgstr "Вилучити"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat
msgid "Repeat"
msgstr "Повторити"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_interval
msgid "Repeat Every"
msgstr "Повторювати кожні"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_type
msgid "Repeat Type"
msgstr "Тип повтору"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_unit
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_unit
msgid "Repeat Unit"
msgstr "Повторювати одиницю"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_until
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_until
msgid "Repeat Until"
msgstr "Повторювати до"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_interval
msgid "Repeat every"
msgstr "Повторювати кожен"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_number
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_number
msgid "Repetitions"
msgstr "Повторення"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_reporting
msgid "Reporting"
msgstr "Звітність"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Requests to Switch"
msgstr "Запити на заміну"

#. module: planning
#: model:ir.actions.server,name:planning.model_planning_slot_action_reset_to_draft
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Reset to Draft"
msgstr "Зробити чернеткою"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__resource_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Resource"
msgstr "Ресурс"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_color
msgid "Resource color"
msgstr "Колір ресурсу"

#. module: planning
#: model:ir.model,name:planning.model_resource_resource
#: model:ir.model.fields,field_description:planning.field_planning_role__resource_ids
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
msgid "Resources"
msgstr "Кадри"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__role_id
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Role"
msgstr "Роль"

#. module: planning
#: model:ir.model.fields,help:planning.field_hr_employee__default_planning_role_id
msgid ""
"Role that will be selected by default when creating a shift for this employee.\n"
"This role will also have precedence over the other roles of the employee when planning orders."
msgstr ""
"Роль, яка буде обрана за замовчуванням при створенні зміни для цього співробітника.\n"
"Ця роль також матиме пріоритет над іншими ролями працівника під час планування замовлень."

#. module: planning
#: model:ir.model.fields,help:planning.field_resource_resource__default_role_id
msgid ""
"Role that will be selected by default when creating a shift for this resource.\n"
"This role will also have precedence over the other roles of the resource when planning shifts."
msgstr ""
"Роль, яка буде вибрана за замовчуванням під час створення зміни для цього ресурсу.\n"
"Ця роль також матиме пріоритет над іншими ролями ресурсу під час планування змін."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Role:"
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.actions.act_window,name:planning.planning_action_roles
#: model:ir.model.fields,field_description:planning.field_hr_employee__planning_role_ids
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_roles
#: model:ir.model.fields,field_description:planning.field_resource_resource__role_ids
#: model:ir.ui.menu,name:planning.planning_menu_settings_role
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_search_view_inherit
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Roles"
msgstr "Ролі"

#. module: planning
#: model:ir.model.fields,help:planning.field_hr_employee__planning_role_ids
msgid ""
"Roles that the employee can fill in. When creating a shift for this employee, only the shift templates for these roles will be displayed.\n"
"Similarly, only the open shifts available for these roles will be sent to the employee when the schedule is published.\n"
"Additionally, the employee will only be assigned orders for these roles (with the default planning role having precedence over the other ones).\n"
"Leave empty for the employee to be assigned shifts regardless of the role."
msgstr ""
"Ролі, які може виконувати працівник. Під час створення зміни для цього працівника відображатимуться лише шаблони змін для цих ролей.\n"
"Подібним чином, під час публікації розкладу працівнику буде надіслано лише відкриті зміни, доступні для цих ролей.\n"
"Крім того, працівнику будуть призначені накази лише для цих ролей (причому роль планування за замовчуванням має пріоритет над іншими).\n"
"Залиште поле порожнім, щоб працівнику призначалися зміни незалежно від ролі."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save"
msgstr "Зберегти"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid "Save & Send Schedule"
msgstr "Зберегти та надіслати розклад"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save Template"
msgstr "Зберегти шаблон"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_creation
msgid "Save as Template"
msgstr "Зберегти як Шаблон"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Save this shift once it is ready."
msgstr "Збережть зміну після того, як вона буде готова."

#. module: planning
#: model:planning.role,name:planning.planning_role_scanner
msgid "Scanner"
msgstr "Сканер"

#. module: planning
#: model:ir.model,name:planning.model_planning_planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Schedule"
msgstr "Запланувати"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_resource
msgid "Schedule by Resource"
msgstr "Розклад за співробітником"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_role
msgid "Schedule by Role"
msgstr "Запланувати за роллю"

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "Schedule sent to your employees"
msgstr ""

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_resource
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""
"Заплануйте свої людські та матеріальні ресурси за ролями, проектами та "
"замовленнями на продаж."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Search operation not supported"
msgstr "Операція пошуку не підтримується"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__access_token
msgid "Security Token"
msgstr "Токен безпеки"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "See conflicting shifts"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Send"
msgstr "Надіслати"

#. module: planning
#: model:ir.model,name:planning.model_planning_send
msgid "Send Planning"
msgstr "Надіслати планування"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Send schedule"
msgstr "Надіслати розклад"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_settings
#: model:ir.ui.menu,name:planning.planning_menu_settings_config
msgid "Settings"
msgstr "Налаштування"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Share the schedule with your team by publishing and sending it. Open the "
"menu to access this option."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Shift"
msgstr "Зміна"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Shift List"
msgstr "Список зміни"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot_template
msgid "Shift Template"
msgstr "Шаблон зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Shift Template Form"
msgstr "Форма шаблону зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_tree
msgid "Shift Template List"
msgstr "Список шаблонів змін"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_shift_template
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_id
#: model:ir.ui.menu,name:planning.planning_menu_settings_shift_template
msgid "Shift Templates"
msgstr "Шаблони змін"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "Shift saved as template"
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Shift sent"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts Planned"
msgstr "Заплановані зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Shifts from"
msgstr "Зміни від"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts in Conflict"
msgstr "Зміни конфліктують"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts of Your Department Member"
msgstr "Зміни вашого відділу"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts of Your Team Member"
msgstr "Зміни вашого члена команди"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Shifts reset to draft"
msgstr ""

#. module: planning
#: model:planning.role,name:planning.planning_shipping_associate
msgid "Shipping Associate"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Some changes were made since this shift was published."
msgstr ""
"Були зроблені деякі зміни з того часу, коли ця зміна була опублікована."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Span"
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Start"
msgstr "Початок"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Start & End Hours"
msgstr "Години початку та закінчення"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_planning__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Start Date"
msgstr "Початкова дата"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__state
#: model:ir.model.fields,field_description:planning.field_planning_slot__state
msgid "Status"
msgstr "Статус"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_send__end_datetime
msgid "Stop Date"
msgstr "Дата зупинки"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__res_company__planning_employee_unavailabilities__switch
msgid "Switch shifts with other employees"
msgstr ""

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_res_company_planning_self_unassign_days_before_positive
msgid ""
"The amount of days before unassignment must be positive or equal to zero."
msgstr ""
"Кількість днів до скасування призначення має бути додатною або дорівнювати "
"нулю."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The company does not allow you to unassign yourself from shifts."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The deadline for unassignment has passed."
msgstr "Термін скасування призначення минув."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_start_date_lower_end_date
msgid "The end date of a shift should be after its start date."
msgstr "Дата закінчення зміни має бути після дати її початку."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "The number of repetitions cannot be negative."
msgstr "Кількість повторень не може бути негативною."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_repeat_interval_positive
msgid "The recurrence should be greater than 0."
msgstr "Повторення має бути більше 0."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The recurrence's end date should fall after the shift's start date."
msgstr "Кінцева дата повторення має бути після дати початку зміни."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "The shift has successfully been created."
msgstr ""

#. module: planning
#. odoo-javascript
#. odoo-python
#: code:addons/planning/static/src/views/planning_hooks.js:0
#: code:addons/planning/wizard/planning_send.py:0
msgid ""
"The shifts have already been published, or there are no shifts to publish."
msgstr "Зміни вже опубліковані, або немає змін для публікації."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The shifts have successfully been published and sent."
msgstr "Зміни успішно опубліковано і надіслано."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_duration_days_positive
msgid "The span must be at least 1 working day."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid ""
"The start and end hours must be greater or equal to 0 and lower than 24."
msgstr ""

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_lower_than_24
msgid "The start hour cannot be greater than 24."
msgstr "Час початку не можу бути більше 24."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_positive
msgid "The start hour cannot be negative."
msgstr "Час початку не може бути негативним."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid ""
"The start hour cannot be before the end hour for a one-day shift template."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "The work email is missing for the following employees:"
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no resources available for this open shift."
msgstr "Для цієї відкритої зміни немає ресурсів."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid ""
"There are no shifts planned for the previous week, or they have already been"
" copied."
msgstr ""
"На попередній тиждень немає запланованих змін, або вони вже скопійовані."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no shifts to publish and send."
msgstr "Немає змін для публікації і відправки."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no shifts to reset to draft."
msgstr "Немає змін для скидання у чернетку."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "This Progress Bar is not implemented."
msgstr "Цей індикатор виконання не реалізовано."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__was_copied
msgid "This Shift Was Copied From Previous Week"
msgstr "Ця зміна скопійована з попереднього тижня"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "This Week"
msgstr "Цього тижня"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__subsequent
msgid "This and following shifts"
msgstr "Ця і наступні зміни"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid ""
"This employee is not expected to work during this period, either because "
"they do not have a current contract or because they are on leave."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "This method must take two slots in argument."
msgstr ""

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid ""
"This open shift is no longer available, or the planning has been updated in "
"the meantime. Please contact your manager for further information."
msgstr ""
"Ця відкрита зміна більше не доступна, або планування тим часом оновлено. Для"
" отримання додаткової інформації зв’яжіться з вашим менеджером."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__this
msgid "This shift"
msgstr "Ця зміна"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "This shift is recurrent. Delete:"
msgstr "Ця зміна повторюється. Видалити:"

#. module: planning
#: model:digest.tip,name:planning.digest_tip_planning_0
msgid "Tip: Record your planning faster"
msgstr "Порада: Записуйте ваш розклад швидше"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__repeat
msgid ""
"To avoid polluting your database and performance issues, shifts are only "
"created for the next 6 months. They are then gradually created as time "
"passes by in order to always get shifts 6 months ahead. This value can be "
"modified from the settings of Planning, in debug mode."
msgstr ""
"Щоб уникнути забруднення бази даних і проблем із продуктивністю, зміни "
"створюються лише на наступні 6 місяців. Потім вони поступово створюються з "
"плином часу, щоб завжди отримувати зміни на 6 місяців вперед. Це значення "
"можна змінити в налаштуваннях планування в режимі розробника."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Today"
msgstr "Сьогодні"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Total"
msgstr "Разом"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__resource_type
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_type
msgid "Type"
msgstr "Тип"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__res_company__planning_employee_unavailabilities__unassign
msgid "Unassign themselves from shifts"
msgstr ""

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__until
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__until
msgid "Until"
msgstr "До"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "Unwanted Shifts Available"
msgstr "Доступні небажані зміни"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_until
msgid "Up to which date should the plannings be repeated"
msgstr "До якої дати планування слід повторити"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__user_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__user_id
#: model:res.groups,name:planning.group_planning_user
msgid "User"
msgstr "Користувач"

#. module: planning
#. odoo-python
#: code:addons/planning/models/hr.py:0
msgid "View Planning"
msgstr "Переглянути розклад"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Waiter"
msgstr "Офіціант"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "Week"
msgstr "Тиждень"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_type
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__week
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__week
msgid "Weeks"
msgstr "Тижні"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_email
msgid "Work Email"
msgstr "Робоча ел. пошта"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_location_id
msgid "Work Location"
msgstr "Робоче розташування"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Working Days"
msgstr "Робочі дні"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Write the <b>role</b> your employee will perform (<i>e.g. Chef, Bartender, "
"Waiter, etc.</i>). <i>Tip: Create open shifts for the roles you will be "
"needing to complete a mission. Then, assign those open shifts to the "
"resources that are available.</i>"
msgstr ""
"Напишіть <b>роль</b> яку буде виконувати ваш співробітник (<i>напр. Шеф-"
"повар, Бармен, Офіціант тощо.</i>). <i>Порада: створіть відкриті зміни для "
"ролей, які вам будуть необхідні для завершення місії. Потім призначте ці "
"відкриті зміни до доступних ресурсів.</i>"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__year
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__year
msgid "Years"
msgstr "Роки"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You are not allowed to reset shifts to draft."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You can not assign yourself to an already assigned shift."
msgstr "Ви не можете призначити себе на зміну, яка вже призначена."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You can not unassign another employee than yourself."
msgstr ""
"Ви не можете скасувати призначення з іншого співробітника, лише із себе."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot assign yourself to a shift in the past."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot cancel a request to switch made by another user."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot cancel a request to switch that is in the past."
msgstr "Ви не можете скасувати запит на заміну, який є в минулому."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot request to switch a shift that is assigned to another user."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot switch a shift that is in the past."
msgstr "Ви не можете замінити зміну, яка є в минулому."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot unassign yourself from a shift in the past."
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "You don't have any shifts planned yet."
msgstr ""

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid ""
"You don't have any shifts planned yet. You can assign yourself some of the "
"available open shifts."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to assign yourself to shifts."
msgstr ""

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to cancel a request to switch."
msgstr "Ви не маєте права скасувати заявку на заміну."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to switch shifts."
msgstr "Ви не маєте права замінювати зміни."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "You have been successfully unassigned from this shift"
msgstr "Вас успішно зняли з призначення на цю зміну"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "Your Planning"
msgstr "Ваш розклад"

#. module: planning
#: model:mail.template,subject:planning.email_template_planning_planning
msgid ""
"Your planning from {{ format_date(ctx.get('start_datetime')) }} to {{ "
"format_date(ctx.get('end_datetime')) }}"
msgstr ""
"Ваше планування з {{ format_date(ctx.get('start_datetime')) }} to {{ "
"format_date(ctx.get('end_datetime')) }}"

#. module: planning
#: model:mail.template,subject:planning.email_template_shift_switch_email
msgid ""
"Your shift on {{ ctx.get('start_datetime') }} was re-assigned to {{ "
"ctx.get('new_assignee_name', 'Marc Demo') }}"
msgstr ""
"Ваша зміна на {{ ctx.get('start_datetime') }} була перепризначена на {{ "
"ctx.get('new_assignee_name', 'Marc Demo') }}"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "all"
msgstr "всі"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "all shifts"
msgstr "усі зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "close"
msgstr "закрити"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "days before the beginning of the shift"
msgstr "днів до початку зміни"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "e.g. Bartender"
msgstr "напр. Бармен"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "e.g. Cleaner"
msgstr "напр. Прибиральник"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_form_view_inherit
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_tree_view_inherit
msgid "e.g. Crane"
msgstr "напр. Журавель"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "e.g. John Doe"
msgstr "наприклад, Степан Бандера"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "other shift(s) in conflict."
msgstr "інші зміни у конфлікті."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "subsequent"
msgstr "наступний"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "this"
msgstr "цей"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "this and following shifts"
msgstr "ця і наступні зміни"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "this shift"
msgstr "зміна"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "to"
msgstr "до"

#. module: planning
#: model:mail.template,subject:planning.email_template_slot_single
msgid "{{ ctx.get('mail_subject', '') }} {{ ctx.get('start_datetime' , '') }}"
msgstr ""
"{{ ctx.get('mail_subject', '') }} {{ ctx.get('start_datetime' , '') }}"
