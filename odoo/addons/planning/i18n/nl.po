# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* planning
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2025
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: planning
#. odoo-python
#: code:addons/planning/models/resource_resource.py:0
msgid "%(resource_name)s (%(role)s)"
msgstr "%(resource_name)s (%(role)s)"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "%s (copy)"
msgstr "%s (kopie)"

#. module: planning
#: model:ir.actions.report,print_report_name:planning.report_planning_slot
msgid "'Planning'"
msgstr "'Planning'"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid "(%s days span)"
msgstr "(%s dagen tijd)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid ".&amp;nbsp;"
msgstr ".&amp;nbsp;"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "01/01/2023 5:00 PM"
msgstr "01/01/2023 5:00 PM"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "01/01/2023 9:00 AM"
msgstr "01/01/2023 9:00 AM"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "04:00"
msgstr "04:00"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "08:00"
msgstr "08:00"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "1 Onsite Interview"
msgstr "1 sollicitatiegesprek op locatie"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "1 Phone Call"
msgstr "1 telefoongesprek"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "100"
msgstr "100"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "12:00"
msgstr "12:00 uur"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "13"
msgstr "13"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "14"
msgstr "14"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "15"
msgstr "15"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "16:00"
msgstr "16:00 uur"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "2 open days"
msgstr "2 opendeurdagen"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "4 Days after Interview"
msgstr "4 dagen na sollicitatiegesprek"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "8 hours"
msgstr "8 uren"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<b class=\"o_gantt_title d-flex align-items-center justify-content-center "
"bg-100 position-sticky start-0 p-2 border-end\"> Schedule</b>"
msgstr ""
"<b class=\"o_gantt_title d-flex align-items-center justify-content-center "
"bg-100 position-sticky start-0 p-2 border-end\"> Planning</b>"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid "<b class=\"tip_title\">Tip: Record your planning faster</b>"
msgstr "<b class=\"tip_title\">Tip: leg je planning sneller vast.</b>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Allocated Time:</b>"
msgstr "<b>Toegewezen tijd:</b>"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"<b>Drag & drop</b> your shift to reschedule it. <i>Tip: hit CTRL (or Cmd) to"
" duplicate it instead.</i> <b>Adjust the size</b> of the shift to modify its"
" period."
msgstr ""
"<b>Sleep & zet</b> je dienst neer om deze opnieuw in te plannen. <i>Tip: "
"druk op CTRL (of Cmd) om het in plaats daarvan te dupliceren.</i> <b>Pas de "
"grootte aan</b> van de dienst om de periode te wijzigen."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Note:</b>"
msgstr "<b>Notitie:</b>"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "<b>Publish & send</b> your employee's planning."
msgstr "<b>Publiceer en verstuur</b> de planning van je werknemer."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "<b>Role:</b>"
msgstr "<b>Functie:</b>"

#. module: planning
#: model:mail.template,body_html:planning.email_template_shift_switch_email
msgid ""
"<div>\n"
"                    <p t-if=\"ctx.get('old_assignee_name')\">Dear <t t-out=\"ctx['old_assignee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p>\n"
"                        The following shift that you requested to switch has been re-assigned\n"
"                        <t t-if=\"ctx.get('new_assignee_name')\"> to <t t-out=\"ctx['new_assignee_name']\">Marc Demo</t></t>.\n"
"                    </p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Date</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    05/31/2021, 8:00 AM ⟶ 05/31/2021, 4:00 PM\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_hours']\">4:00</t>h)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_percentage']\">50</t>%)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Role</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Gathering</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Sales Order Item</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Coffee</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Note</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <br/>\n"
"                </div>\n"
"            "
msgstr ""
"<div>\n"
"                    <p t-if=\"ctx.get('old_assignee_name')\">Beste <t t-out=\"ctx['old_assignee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hallo,</p>\n"
"                    <br/>\n"
"                    <p>\n"
"                        De volgende dienst die je hebt aangevraagd is opnieuw toegewezen\n"
"                        <t t-if=\"ctx.get('new_assignee_name')\"> aan <t t-out=\"ctx['new_assignee_name']\">Marc Demo</t></t>.\n"
"                    </p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Datum</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    05/31/2021, 8:00 AM ⟶ 05/31/2021, 4:00 PM\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                   <t t-out=\"ctx['allocated_hours']\">(4:00h</t>)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                   <t t-out=\"ctx['allocated_percentage']\">(50%</t>)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Rol</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Barman</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Verzamelen</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Item Verkooporder</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Koffie</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Opmerking</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <br/>\n"
"                </div>\n"
"            "

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/>\n"
"                        You have successfully requested to switch your shift. You will be notified when another employee takes over your shift."
msgstr ""
"<i class=\"fa fa-check-circle\"/>\n"
"                        Je hebt met succes een verzoek ingediend om je dienst te ruilen. Je krijgt bericht wanneer een andere medewerker je dienst overneemt."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/> Request to switch shift cancelled "
"successfully."
msgstr ""
"<i class=\"fa fa-check-circle\"/> Verzoek om verschuiving succesvol "
"geannuleerd."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid "<i class=\"fa fa-check-circle\"/> This shift is no longer assigned to you."
msgstr ""
"<i class=\"fa fa-check-circle\"/> Deze dienst is niet langer aan jou "
"toegewezen."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-check-circle\"/> You were successfully assigned this open "
"shift."
msgstr ""
"<i class=\"fa fa-check-circle\"/> Je hebt met succes deze open dienst "
"toegewezen gekregen."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_kanban
msgid "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Hours\" title=\"Hours\"/>"
msgstr "<i class=\"fa fa-clock-o\" role=\"img\" aria-label=\"Uren\" title=\"Uren\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_assignee_contact_popover
msgid ""
"<i class=\"fa fa-envelope-o\" title=\"Mail\" role=\"img\" style=\"padding-"
"right: 2px\"/>"
msgstr ""
"<i class=\"fa fa-envelope-o\" title=\"Mail\" role=\"img\" style=\"padding-"
"right: 2px\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> This shift is already assigned to "
"another employee."
msgstr ""
"<i class=\"fa fa-exclamation-circle\"/> Deze dienst is al toegewezen aan een"
" andere medewerker."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_notification
msgid ""
"<i class=\"fa fa-exclamation-circle\"/> You can no longer unassign yourself "
"from this shift."
msgstr ""
"<i class=\"fa fa-exclamation-circle\"/> Je kunt jezelf niet langer losmaken "
"van deze verschuiving."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid ""
"<i class=\"fa fa-long-arrow-right my-1 mx-1\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"
msgstr ""
"<i class=\"fa fa-long-arrow-right my-1 mx-1\" aria-label=\"Arrow icon\" "
"title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid "<i class=\"fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<i class=\"fa fa-long-arrow-right\" title=\"Arrow\"/>"
msgstr "<i class=\"fa fa-long-arrow-right\" title=\"Pijl\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_assignee_contact_popover
msgid "<i class=\"fa fa-phone\" title=\"Phone\" role=\"img\" style=\"padding-right: 2px\"/>"
msgstr ""
"<i class=\"fa fa-phone\" title=\"Telefoon\" role=\"img\" style=\"padding-"
"right: 2px\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "<i class=\"mt-1 fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"
msgstr "<i class=\"mt-1 fa fa-clock-o\" role=\"img\" aria-label=\"Date\" title=\"Date\"/>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Bartender</span>"
msgstr ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Barman</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Waiter</span>"
msgstr ""
"<i class=\"o_group_caret fa fa-fw me-1 fa-caret-down\"></i> <span "
"class=\"text-truncate w-0 flex-grow-1 text-start\">Ober</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "<span class=\"ms-0 me-4\">Edit</span>"
msgstr "<span class=\"ms-0 me-4\">Bewerken</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">04:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">04:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">12:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">12:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">16:00</span>"
msgstr ""
"<span class=\"o_gantt_consolidated_pill_title bg-view text-truncate px-1 "
"mb-1\">16:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">04:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">04:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">08:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">08:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">12:00</span>"
msgstr ""
"<span class=\"o_gantt_pill_title bg-view text-truncate px-1 "
"z-1\">12:00</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">1:00 PM - 5:00 "
"PM</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">1:00 PM - 5:00 "
"PM</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">8:00 AM - 12:00 "
"PM</span>"
msgstr ""
"<span class=\"o_gantt_pill_title text-truncate mx-1\">8:00 AM - 12:00 "
"PM</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid ""
"<span class=\"o_start_date\"/>\n"
"                                    <i class=\"mx-1 fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                    <span class=\"o_end_date\"/>"
msgstr ""
"<span class=\"o_start_date\"/>\n"
"                                    <i class=\"mx-1 fa fa-long-arrow-right\" aria-label=\"Arrow icon\" title=\"Arrow\"/>\n"
"                                    <span class=\"o_end_date\"/>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
msgid "<span class=\"o_stat_text\">Planning</span>"
msgstr "<span class=\"o_stat_text\">Planning</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Days to get an Offer</span>"
msgstr ""
"<span class=\"text-muted small\">Dagen om een aanbieding te krijgen</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Process</span>"
msgstr "<span class=\"text-muted small\">Proces</span>"

#. module: planning
#: model_terms:hr.job,job_details:planning.job_maintenance_technician
#: model_terms:hr.job,job_details:planning.job_quality_control
msgid "<span class=\"text-muted small\">Time to Answer</span>"
msgstr "<span class=\"text-muted small\">Tijd om te antwoorden</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1 text-start\">Open Shifts</span>"
msgstr ""
"<span class=\"text-truncate w-0 flex-grow-1 text-start\">Open "
"diensten</span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Doris Cole </span> "
"<span class=\"text-muted text-truncate\">(Bartender)</span> </span>"
msgstr ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Doris Cole </span> "
"<span class=\"text-muted text-truncate\">(Barmaid)</span> </span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Eli Lambert </span> "
"<span class=\"text-muted text-truncate\">(Waiter)</span> </span>"
msgstr ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Eli Lambert </span> "
"<span class=\"text-muted text-truncate\">(Ober)</span> </span>"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Jeffrey Kelly </span> "
"<span class=\"text-muted text-truncate\">(Bartender)</span> </span>"
msgstr ""
"<span class=\"text-truncate w-0 flex-grow-1\"> <span>Jeffrey Kelly </span> "
"<span class=\"text-muted text-truncate\">(Barman)</span> </span>"

#. module: planning
#: model_terms:web_tour.tour,rainbow_man_message:planning.planning_tour
msgid ""
"<span><b>Congratulations!</b> You are now a master of planning.\n"
"        </span>"
msgstr ""
"<span><b>Gefeliciteerd!</b> Je bent nu een meester in plannen.\n"
"        </span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>Allow employees to:</span>"
msgstr "<span>Werknemers toestaan om:</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid ""
"<span>The employee assigned would like to switch shifts with someone "
"else.</span>"
msgstr ""
"<span>De toegewezen werknemer wil graag van dienst wisselen met iemand "
"anders.</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "<span>months ahead</span>"
msgstr "<span>maanden vooruit</span>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "<strong>Allocated Time — </strong>"
msgstr "<strong>Toegewezen tijd — </strong>"

#. module: planning
#: model:mail.template,body_html:planning.email_template_slot_single
msgid ""
"<style>\n"
"                    .planning_mail_template_button {\n"
"                        padding: 5px 10px;\n"
"                        text-decoration: none;\n"
"                        border: 1px;\n"
"                        border-radius: 3px;\n"
"                        text-align: center;\n"
"                        color: #374151;\n"
"                        background-color: #E7E9ED;\n"
"                        border: solid #E7E9ED;\n"
"                        flex: 1 1 40%;\n"
"                    }\n"
"                    .top_button {\n"
"                        color: #FFFFFF;\n"
"                        background-color: #714B67;\n"
"                        border: solid #714B67;\n"
"                        flex: 1 1 60%;\n"
"                    }\n"
"                    .button_box {\n"
"                        display: flex;\n"
"                        flex-wrap: wrap;\n"
"                        gap: 10px;\n"
"                        margin: 20px 15px 0px 15px;\n"
"                    }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee_name')\">Dear <t t-out=\"ctx['employee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">We have a new shift opening:</p>\n"
"                    <p t-else=\"\">You have been assigned the following shift:</p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Date</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    05/31/2021, 8:00 AM ⟶ 05/31/2021, 4:00 PM\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_hours']\">4:00</t>h)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                    (<t t-out=\"ctx['allocated_percentage']\">50</t>%)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Role</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Bartender</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Gathering</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Sales Order Item</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Coffee</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Note</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <div class=\"button_box\">\n"
"                        <a t-if=\"ctx.get('available_link')\" t-att-href=\"ctx['available_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">Assign me this shift</a>\n"
"                        <a t-if=\"ctx.get('unavailable_link')\" t-att-href=\"ctx['unavailable_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">I am unavailable</a>\n"
"                        <a t-if=\"ctx.get('google_url')\" t-att-href=\"ctx['google_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Add to Google Calendar</a>\n"
"                        <a t-if=\"ctx.get('iCal_url')\" t-att-href=\"ctx['iCal_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Add to iCal/Outlook</a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">If you are interested and available, please assign yourself this open shift.</p>\n"
"                    <p t-else=\"\">In case the current shift doesn't suit you, we encourage you to reach out to your colleagues and request to switch shifts. They might be interested in exchanging shifts with you.</p>\n"
"                </div>\n"
"            </body>"
msgstr ""
"<style>\n"
"                    .planning_mail_template_button {\n"
"                        padding: 5px 10px;\n"
"                        tekstdecoratie: geen;\n"
"                        rand: 1px;\n"
"                        randradius: 3px;\n"
"                        tekst-uitlijning: centreren;\n"
"                        kleur: #374151;\n"
"                        achtergrondkleur: #E7E9ED;\n"
"                        rand: effen #E7E9ED;\n"
"                        flex: 1 1 40%;\n"
"                    }\n"
"                    .top_button {\n"
"                        kleur: #FFFFFF;\n"
"                        achtergrondkleur: #714B67;\n"
"                        border: solid #714B67;\n"
"                        flex: 1 1 60%;\n"
"                    }\n"
"                    .knop_vak {\n"
"                        weergave: flex;\n"
"                        flex-wrap: wrap;\n"
"                        tussenruimte: 10px;\n"
"                        margin: 20px 15px 0px 15px;\n"
"                    }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee_name')\">Beste <t t-out=\"ctx['employee_name']\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hallo,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">We hebben een nieuwe dienst:</p>\n"
"                    <p t-else=\"\">Je hebt de volgende dienst toegewezen gekregen:</p>\n"
"                    <table style=\"margin-left: 30px;\">\n"
"                        <tr t-if=\"(ctx.get('start_datetime') and ctx.get('end_datetime'))                                   or ctx.get('allocated_hours')                                   or ctx.get('allocated_percentage')\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Datum</th>\n"
"                            <td style=\"padding: 5px;\">\n"
"                                <span t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\" t-out=\"'%s ⟶ %s' % (                                     ctx['start_datetime'],                                     ctx['end_datetime'],                                 )\">\n"
"                                    05/31/2021, 8:00 AM ⟶ 05/31/2021, 4:00 PM\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_hours')\" style=\"opacity: 0.5;\">\n"
"                                   <t t-out=\"ctx['allocated_hours']\">(4:00h</t>)\n"
"                                </span>\n"
"                                <span t-if=\"ctx.get('allocated_percentage') and ctx['allocated_percentage'] != '100'\" style=\"opacity: 0.5;\">\n"
"                                   <t t-out=\"ctx['allocated_percentage']\">(50%</t>)\n"
"                                </span>\n"
"                            </td>\n"
"                        </tr>\n"
"                        <tr t-if=\"object.role_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Rol</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.role_id.name or ''\">Barman</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'project_id') and object.project_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Project</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().project_id.name or ''\">Verzamelen</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'sale_line_id') and object.sale_line_id\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Item Verkooporder</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.sudo().sale_line_id.name or ''\">Koffie</td>\n"
"                        </tr>\n"
"                        <tr t-if=\"hasattr(object, 'name') and object.name\">\n"
"                            <th style=\"padding: 5px; text-align: left;\">Opmerking</th>\n"
"                            <td style=\"padding: 5px;\" t-out=\"object.name or ''\">/</td>\n"
"                        </tr>\n"
"                    </table>\n"
"                    <div class=\"button_box\">\n"
"                        <a t-if=\"ctx.get('available_link')\" t-att-href=\"ctx['available_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">Wijs mij deze dienst toe</a>\n"
"                        <a t-if=\"ctx.get('unavailable_link')\" t-att-href=\"ctx['unavailable_link']\" target=\"_blank\" class=\"planning_mail_template_button top_button\">Ik ben niet beschikbaar</a>\n"
"                        <a t-if=\"ctx.get('google_url')\" t-att-href=\"ctx['google_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Toevoegen aan Google Agenda</a>\n"
"                        <a t-if=\"ctx.get('iCal_url')\" t-att-href=\"ctx['iCal_url']\" target=\"_blank\" class=\"planning_mail_template_button\">Toevoegen aan iCal/Outlook</a>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('open_shift_available')\">Als je geïnteresseerd en beschikbaar bent, wijs jezelf dan deze open dienst toe.</p>\n"
"                    <p t-else=\"\">Als de huidige dienst je niet schikt, raden we je aan om contact op te nemen met je collega's en te vragen om van dienst te ruilen. Misschien zijn ze geïnteresseerd om van dienst te ruilen.</p>\n"
"                </div>\n"
"            </body>"

#. module: planning
#: model:mail.template,body_html:planning.email_template_planning_planning
msgid ""
"<style>\n"
"                .planning_mail_template_button {\n"
"                    padding: 5px 10px;\n"
"                    text-decoration: none;\n"
"                    border: 1px;\n"
"                    border-radius: 3px;\n"
"                    display: inline-block; \n"
"                    width: 190px;\n"
"                    text-align: center;\n"
"                }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee')\">Dear <t t-out=\"ctx['employee'].name or ''\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hello,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\">\n"
"                        Your upcoming shifts from <t t-out=\"format_date(ctx['start_datetime'])\">05-05-2021</t>\n"
"                        to <t t-out=\"format_date(ctx['end_datetime'])\">05-11-2021</t> have been published.\n"
"                    </p>\n"
"                    <div style=\"display: flex; margin: 15px;\">\n"
"                        <div t-if=\"ctx.get('planning_url')\" style=\"margin-right: 15px;\">\n"
"                            <a t-att-href=\"ctx['planning_url']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #FFFFFF; background-color: #875A7B; border: solid #875A7B;\">View Your Planning</a>\n"
"                        </div>\n"
"                        <div t-if=\"ctx.get('planning_url_ics')\">\n"
"                            <a t-att-href=\"ctx['planning_url_ics']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #374151; background-color: #E7E9ED; border: solid #E7E9ED;\">Add to Calendar</a>\n"
"                        </div>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('slot_unassigned') or (object.allow_self_unassign and object.self_unassign_days_before)\">\n"
"                        <t t-if=\"ctx.get('slot_unassigned')\">\n"
"                            We would also like to remind you that there are some open shifts available, and if you are interested and available, please assign yourself to those shifts.\n"
"                        </t>\n"
"                        <t t-if=\"object.allow_self_unassign and object.self_unassign_days_before\">\n"
"                            If you are unable to work a shift that has been assigned to you, please unassign yourself within <t t-out=\"object.self_unassign_days_before or ''\">5</t> day(s) before the start of the shift.\n"
"                        </t>\n"
"                    </p>\n"
"                    <p t-if=\"ctx.get('message')\" t-out=\"ctx['message']\"/>\n"
"                    <p t-if=\"object.allow_self_unassign\">In case your current schedule doesn't suit you, we encourage you to reach out to your colleagues and request to switch shifts. They might be interested in exchanging shifts with you.</p>\n"
"                </div>\n"
"            </body>"
msgstr ""
"<style>\n"
"                .planning_mail_template_button {\n"
"                    padding: 5px 10px;\n"
"                    tekstdecoratie: geen;\n"
"                    rand: 1px;\n"
"                    randradius: 3px;\n"
"                    weergave: inline-block; \n"
"                    breedte: 190px;\n"
"                    tekst-uitlijning: centreren;\n"
"                }\n"
"                </style>\n"
"                <body><div>\n"
"                    <p t-if=\"ctx.get('employee')\">Beste <t t-out=\"ctx['employee'].name or ''\">Anita Oliver</t>,</p>\n"
"                    <p t-else=\"\">Hallo,</p>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('start_datetime') and ctx.get('end_datetime')\">\n"
"                        Je aankomende verschuivingen van <t t-out=\"format_date(ctx['start_datetime'])\">05-05-2021</t>\n"
"                        tot <t t-out=\"format_date(ctx['end_datetime'])\">05-11-2021</t> zijn gepubliceerd.\n"
"                    </p>\n"
"                    <div style=\"display: flex; margin: 15px;\">\n"
"                        <div t-if=\"ctx.get('planning_url')\" style=\"margin-right: 15px;\">\n"
"                            <a t-att-href=\"ctx['planning_url']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #FFFFFF; background-color: #875A7B; border: solid #875A7B;\">Bekijk je planning</a>\n"
"                        </div>\n"
"                        <div t-if=\"ctx.get('planning_url_ics')\">\n"
"                            <a t-att-href=\"ctx['planning_url_ics']\" target=\"_blank\" class=\"planning_mail_template_button\" style=\"color: #374151; background-color: #E7E9ED; border: solid #E7E9ED;\">Toevoegen aan kalender</a>\n"
"                        </div>\n"
"                    </div>\n"
"                    <br/>\n"
"                    <p t-if=\"ctx.get('slot_unassigned') or (object.allow_self_unassign and object.self_unassign_days_before)\">\n"
"                        <t t-if=\"ctx.get('slot_unassigned')\">\n"
"                            We willen je er ook graag aan herinneren dat er nog wat open diensten beschikbaar zijn, en als je geïnteresseerd en beschikbaar bent, wijs jezelf dan alsjeblieft aan voor die diensten.\n"
"                        </t>\n"
"                        <t t-if=\"object.allow_self_unassign and object.self_unassign_days_before\">\n"
"                            Als je niet in staat bent om een dienst te draaien die aan jou is toegewezen, meld je dan af binnen <t t-out=\"object.self_unassign_days_before or ''\">5</t> dag(en) voor het begin van de dienst.\n"
"                        </t>\n"
"                    </p>\n"
"                    <p t-if=\"ctx.get('message')\" t-out=\"ctx['message']\"/>\n"
"                    <p t-if=\"object.allow_self_unassign\">Als je huidige rooster niet bij je past, moedigen we je aan om contact op te nemen met je collega's en te vragen om van dienst te ruilen. Misschien zijn ze geïnteresseerd om diensten met je te ruilen.</p>\n"
"                </div>\n"
"            </body>"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_until_limit
msgid ""
"A recurrence repeating itself until a certain date must have its limit set"
msgstr ""
"Een herhaling die zich herhaalt t/m een bepaalde datum moet zijn limiet "
"hebben"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "A shift must be in the same company as its recurrence."
msgstr "Een dienst moet in hetzelfde bedrijf zijn als de herhaling."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__active
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__active
msgid "Active"
msgstr "Actief"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.js:0
msgid "Add Shift"
msgstr "Dienst toevoegen"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_send__note
msgid "Additional message displayed in the email sent to employees"
msgstr ""
"Extra bericht dat getoond wordt in de e-mail die verzonden wordt naar "
"werknemers"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Additional message included in the email sent to your employees"
msgstr "Extra bericht in de e-mail die naar je werknemers verzonden wordt"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Additional note sent to the employee"
msgstr "Extra notitie om te versturen naar de werknemer"

#. module: planning
#: model:res.groups,name:planning.group_planning_manager
msgid "Administrator"
msgstr "Beheerder"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid ""
"All open shifts have already been assigned, or there are no resources "
"available to take them at this time."
msgstr ""
"Alle openstaande diensten zijn reeds toegewezen of er zijn geen resources "
"beschikbaar om ze op dit moment op te nemen."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__all
msgid "All shifts"
msgstr "Alle diensten"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid ""
"All subsequent shifts will be deleted. Are you sure you want to continue?"
msgstr ""
"Alle volgende diensten worden verwijderd. Weet je zeker dat je door wilt "
"gaan?"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Allocated Hours"
msgstr "Toegewezen tijd"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Allocated Percentage"
msgstr "Toegewezen percentage"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__allocated_hours
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_hours
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Allocated Time"
msgstr "Toegewezen tijd"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocated_percentage
msgid "Allocated Time %"
msgstr "Toegewezen tijd %"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__allocated_percentage
msgid "Allocated Time (%)"
msgstr "Toegewezen tijd (%)"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Allocated Time:"
msgstr "Toegewezen tijd:"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_allocated_hours_positive
msgid "Allocated hours and allocated time percentage cannot be negative."
msgstr ""
"Toegekende uren en toegerekende tijdpercentage kunnen niet negatief zijn."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__allocation_type
msgid "Allocation Type"
msgstr "Soort toewijzing"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid ""
"Analyze the allocation of your resources across roles, projects, and sales "
"orders, and estimate future needs."
msgstr ""
"Analyseer de toewijzing van je resources over functies, projecten en "
"verkooporders en schat toekomstige behoeften in."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
msgid "Archived"
msgstr "Gearchiveerd"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/resource_form/resource_form_controller.js:0
#: code:addons/planning/static/src/views/resource_list/resource_list_controller.js:0
msgid ""
"Archiving this resource will transform all of its future shifts into open "
"shifts. Are you sure you want to continue?"
msgstr ""
"Het archiveren van deze resource zal alle toekomstige diensten in "
"openstaande diensten veranderen. Weet je zeker dat je wilt doorgaan?"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer.js:0
msgid "Are you sure you want to delete this shift?"
msgstr "Weet je zeker dat je deze dienst wilt verwijderen?"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Arrow"
msgstr "Pijl"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Arrow icon"
msgstr "Pijl icon"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Ask To Switch"
msgstr "Vraag om te wisselen"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Ask to Switch"
msgstr "Vraag om te wisselen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Assign a <b>resource</b>, or leave it open for the moment. <i>Tip: Create "
"open shifts for the roles you will be needing to complete a mission. Then, "
"assign those open shifts to the resources that are available.</i>"
msgstr ""
"Wijs een <b>resource</b> toe, of laat deze voorlopig open. <i>Tip: maak "
"openstaande diensten voor de functies die je nodig hebt om een missie te "
"voltooien. Wijs die openstaande diensten vervolgens toe aan de beschikbare "
"resources.</i>"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "Assignee"
msgstr "Toegewezen persoon"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Auto Plan"
msgstr "Automatisch plannen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Automatically plan open shifts and sales orders"
msgstr "Automatisch plannen van openstaande diensten en verkooporders"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_filter_panel/planning_calendar_filter_panel.xml:0
msgid "Avatar"
msgstr "Avatar"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Bartender"
msgstr "Barman"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee_base
msgid "Basic Employee"
msgstr "Basis werknemer"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_resource
msgid "By Resource"
msgstr "Per resource"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule_by_role
msgid "By Role"
msgstr "Per functie"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "CANCEL SWITCH"
msgstr "ANNULEER WISSELING"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Cancel Switch"
msgstr "Annuleer wisseling"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Chat"
msgstr "Chat"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__color
#: model:ir.model.fields,field_description:planning.field_planning_slot__color
msgid "Color"
msgstr "Kleur"

#. module: planning
#: model:planning.role,name:planning.planning_role_cm
msgid "Community Manager"
msgstr "Community manager"

#. module: planning
#: model:ir.model,name:planning.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__company_id
#: model:ir.model.fields,field_description:planning.field_planning_planning__company_id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__company_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__company_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Company"
msgstr "Bedrijf"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_planning__company_id
msgid ""
"Company linked to the material resource. Leave empty for the resource to be "
"available in every company."
msgstr ""
"Bedrijf gekoppeld aan het materiële resource. Laat dit open zodat de "
"resource beschikbaar is in elk bedrijf."

#. module: planning
#: model:ir.model,name:planning.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie-instellingen"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_settings
msgid "Configuration"
msgstr "Configuratie"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.xml:0
msgid "Confirm"
msgstr "Bevestigen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
msgid "Copy previous"
msgstr "Kopieer vorige"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Copy previous week"
msgstr "Kopieer vorige week"

#. module: planning
#: model:planning.role,name:planning.planning_role_crane
msgid "Crane"
msgstr "Kraan"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__create_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__create_date
#: model:ir.model.fields,field_description:planning.field_planning_role__create_date
#: model:ir.model.fields,field_description:planning.field_planning_send__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__create_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Date"
msgstr "Datum"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__date_end
msgid "Date End"
msgstr "Einddatum"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__date_start
msgid "Date Start"
msgstr "Startdatum"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__day
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__day
msgid "Days"
msgstr "Dagen"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_res_company__planning_self_unassign_days_before
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_self_unassign_days_before
msgid "Days before shift for unassignment"
msgstr "Dagen voor dienst wegens uitdiensttreding"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Deadline"
msgstr "Deadline"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_planning__self_unassign_days_before
#: model:ir.model.fields,help:planning.field_planning_slot__self_unassign_days_before
#: model:ir.model.fields,help:planning.field_res_company__planning_self_unassign_days_before
#: model:ir.model.fields,help:planning.field_res_config_settings__planning_self_unassign_days_before
msgid "Deadline in days for shift unassignment"
msgstr "Deadline in dagen voor uitdiensttreding van de dienst"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__default_planning_role_id
#: model:ir.model.fields,field_description:planning.field_resource_resource__default_role_id
msgid "Default Role"
msgstr "Standaard functie"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_search_view_inherit
msgid "Default Roles"
msgstr "Standaard functies"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid ""
"Define a work email address for the following employees so they will receive"
" the planning."
msgstr ""
"Definieer een zakelijk e-mailadres voor de volgende werknemers zodat zij de "
"planning ontvangen."

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__role_id
msgid ""
"Define the roles your resources perform (e.g. Chef, Bartender, Waiter...). "
"Create open shifts for the roles you need to complete a mission. Then, "
"assign those open shifts to the resources that are available."
msgstr ""
"Definieer de functies die je werknemers vervullen (bijv. chef-kok, barman, "
"ober...). Creëer openstaande diensten voor de functies die je nodig hebt om "
"een missie te voltooien. Wijs die openstaande diensten vervolgens toe aan de"
" beschikbare werknemers."

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid ""
"Define the roles your resources perform (e.g. Chef, Bartender, Waiter...). "
"Create open shifts for the roles you will be needing to complete a mission. "
"Then, assign those open shifts to the resources that are available."
msgstr ""
"Definieer de functies die je resources vervullen (bijvoorbeeld chef-kok, "
"barman, ober...). Maak openstaande diensten voor de functies die je nodig "
"hebt om een missie te voltooien. Wijs die openstaande diensten vervolgens "
"toe aan de beschikbare resources."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_kanban
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Delete"
msgstr "Verwijderen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.js:0
msgid "Delete Recurring Shift"
msgstr "Herhalende dienst verwijderen"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__department_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__department_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Department"
msgstr "Afdeling"

#. module: planning
#: model:ir.model,name:planning.model_hr_departure_wizard
msgid "Departure Wizard"
msgstr "Vertrek wizard"

#. module: planning
#: model:planning.role,name:planning.planning_role_developer
msgid "Developer"
msgstr "Developer"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Discard"
msgstr "Negeren"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__display_name
#: model:ir.model.fields,field_description:planning.field_planning_planning__display_name
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__display_name
#: model:ir.model.fields,field_description:planning.field_planning_role__display_name
#: model:ir.model.fields,field_description:planning.field_planning_send__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot__display_name
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_resources
msgid "Distribute your material resources across projects and sales orders."
msgstr "Verdeel je materiële resources over projecten en verkooporders."

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Doris Cole (Bartender)"
msgstr "Doris Cole (barman)"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__state__draft
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__state__draft
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Draft"
msgstr "Concept"

#. module: planning
#: model_terms:digest.tip,tip_description:planning.digest_tip_planning_0
msgid ""
"Drag a shift to another day to reschedule it, or to another row to reassign "
"the shift. Press CTRL (or Cmd on Mac) while dragging a shift to duplicate "
"it."
msgstr ""
"Sleep een dienst naar een andere dag om het opnieuw in te plannen of naar "
"een andere tij om de dienst opnieuw toe te wijzen. Druk op CTRL (of Cmd op "
"Mac) terwijl je een dienst versleept om het te dupliceren."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__duration
msgid "Duration"
msgstr "Duur"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__duration_days
msgid "Duration Days"
msgstr "Duur in dagen"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_kanban
#: model_terms:ir.ui.view,arch_db:planning.planning_view_kanban
msgid "Edit"
msgstr "Bewerken"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.xml:0
msgid "Edit Recurrent Shift"
msgstr "Terugkerende verschuiving bewerken"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Eli Lambert (Waiter)"
msgstr "Eli Lambert (Ober)"

#. module: planning
#: model:mail.template,description:planning.email_template_shift_switch_email
msgid ""
"Email sent automatically when an employee self-assigns to the unwanted shift"
" of another employee, notifying the person who requested to switch that the "
"shift has been taken"
msgstr ""
"E-mail die automatisch wordt verzonden wanneer een werknemer zichzelf "
"toewijst aan de ongewenste dienst van een andere werknemer, met de melding "
"aan degene die om de dienst heeft gevraagd dat de dienst is overgenomen"

#. module: planning
#: model:mail.template,description:planning.email_template_slot_single
msgid "Email sent automatically when publishing a shift"
msgstr "Automatisch verzonden e-mail bij het publiceren van een dienst"

#. module: planning
#: model:mail.template,description:planning.email_template_planning_planning
msgid ""
"Email sent automatically when publishing the schedule of your employees"
msgstr ""
"Automatisch verzonden e-mail bij het publiceren van het uurrooster van je "
"werknemers"

#. module: planning
#: model:ir.model,name:planning.model_hr_employee
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__employee_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__employee_id
msgid "Employee"
msgstr "Werknemer"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Employee Name"
msgstr "Naam werknemer"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_employee_unavailabilities
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_employee_unavailabilities
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Employee Unavailabilities"
msgstr "Onbeschikbaarheden van werknemers"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__employee_ids
#: model:ir.ui.menu,name:planning.planning_menu_settings_employee
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Employees"
msgstr "Werknemers"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid "Employees with no Work Email"
msgstr "Werknemers zonder zakelijk e-mailadres"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "End"
msgstr "Einde"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__end_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "End Date"
msgstr "Einddatum"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__end_time
msgid "End Hour"
msgstr "Eindtijd"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_hr_employee_employee_token_unique
msgid "Error: each employee token must be unique"
msgstr "Foutmelding: de token van elke werknemer moet uniek zijn"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "Every %(repeat_interval)s week(s) until %(repeat_until)s"
msgstr "Om de %(repeat_interval)s weken tot %(repeat_until)s"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__note
msgid "Extra Message"
msgstr "Extra bericht"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
msgid ""
"Find here your planning. Assign yourself open shifts that match your roles, "
"or indicate your unavailability."
msgstr ""
"Vind hier je planning. Wijs jezelf openstaande diensten toe die passen bij "
"je functies, of geef aan dat je niet beschikbaar bent."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "First you have to specify the date of the invitation."
msgstr "Eerst dien je de datum van de uitnodiging in te geven."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Fixed Hours"
msgstr "Vaste uren"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Flexible Hours"
msgstr "Flexibele uren"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__forecast
msgid "Forecast"
msgstr "Virtueel"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__forever
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__forever
msgid "Forever"
msgstr "Voor altijd"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "Forever, every %s week(s)"
msgstr "Voor altijd, elke %s week (weken)"

#. module: planning
#: model:planning.role,name:planning.planning_furniture_assembler
msgid "Furniture Assembler"
msgstr "Meubelmonteur"

#. module: planning
#: model:planning.role,name:planning.planning_furniture_tool
msgid "Furniture Tools"
msgstr "Meubelgereedschap"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Generate shifts"
msgstr "Diensten genereren"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Give depth to your"
msgstr "Geef diepte aan je"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Group By"
msgstr "Groeperen op"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_hr_employee__has_slots
#: model:ir.model.fields,field_description:planning.field_hr_employee_base__has_slots
#: model:ir.model.fields,field_description:planning.field_hr_employee_public__has_slots
msgid "Has Slots"
msgstr "Heeft slots"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__request_to_switch
msgid "Has there been a request to switch on this shift slot?"
msgstr "Is er een verzoek ingediend om deze dienst te wisselen?"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__name
msgid "Hours"
msgstr "Uren"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__resource_type__user
msgid "Human"
msgstr "Persoon"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "I Am Unavailable"
msgstr "Ik ben niet beschikbaar"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "I Take It"
msgstr "Ik neem deze"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "I am unavailable"
msgstr "Ik ben niet beschikbaar"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__id
#: model:ir.model.fields,field_description:planning.field_planning_planning__id
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__id
#: model:ir.model.fields,field_description:planning.field_planning_role__id
#: model:ir.model.fields,field_description:planning.field_planning_send__id
#: model:ir.model.fields,field_description:planning.field_planning_slot__id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__id
msgid "ID"
msgstr "ID"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_analysis_report__publication_warning
#: model:ir.model.fields,help:planning.field_planning_slot__publication_warning
msgid ""
"If checked, it means that the shift contains has changed since its last "
"publish."
msgstr ""
"Indien aangevinkt, betekent dit dat de dienst is gewijzigd sinds de laatste "
"publicatie."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"If you are happy with your planning, you can now <b>send</b> it to your "
"employees."
msgstr ""
"Als je tevreden bent met je planning, kunt je deze nu naar je werknemers "
"<b>verzenden</b>."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__include_unassigned
msgid "Include Open Shifts"
msgstr "Inclusief openstaande diensten"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__include_unassigned
msgid "Includes Open Shifts"
msgstr "Inclusief openstaande diensten"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Jeffrey Kelly (Bartender)"
msgstr "Jeffrey Kelly (barman)"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__job_title
#: model:ir.model.fields,field_description:planning.field_planning_slot__job_title
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
msgid "Job Title"
msgstr "Functietitel"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_role__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_send__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_uid
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__write_date
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__write_date
#: model:ir.model.fields,field_description:planning.field_planning_role__write_date
#: model:ir.model.fields,field_description:planning.field_planning_send__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot__write_date
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
msgid "Legend"
msgstr "Legenda"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__allow_self_unassign
#: model:ir.model.fields,field_description:planning.field_planning_slot__allow_self_unassign
msgid "Let Employee Unassign Themselves"
msgstr "Laat de werknemer zichzelf uitschrijven"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"Let employees switch shifts with colleagues or unassign themselves when "
"unavailable"
msgstr ""
"Geef werknemers de mogelijkheid om van dienst te wisselen met collega's of "
"zichzelf niet toe te wijzen als ze niet beschikbaar zijn"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Let's check out the Gantt view for cool features. Get ready to <b>share your"
" schedule</b> and easily plan your shifts with just one click by <em>copying"
" the previous week's schedule</em>."
msgstr ""
"Laten we de Gantt-weergave bekijken voor coole functies. <b>Deel je "
"rooster</b> en plan je diensten in een handomdraai door <em>de planning van "
"vorige week te kopiëren</em>."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Let's create your first <b>shift</b>. <i>Tip: use the (+) shortcut available"
" on each cell of the Gantt view to save time.</i>"
msgstr ""
"Laten we je eerste <b>dienst</b> maken. <i>Tip: gebruik de sneltoets (+) die"
" beschikbaar is in elke cel van de Gantt-weergave om tijd te besparen.</i>"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Let's schedule a <b>shift</b> for this time range."
msgstr "Laten we een <b>dienst</b> inplannen voor dit tijdbereik."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Let's start managing your employees' schedule!"
msgstr ""
"Laten we starten met het beheren van het werkschema van je werknemers!"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "List"
msgstr "Lijst"

#. module: planning
#: model:hr.job,name:planning.job_maintenance_technician
#: model:planning.role,name:planning.planning_maintenance_technician
msgid "Maintenance Technician"
msgstr "Onderhoudstechnieker"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "Make encoding shifts easy with templates."
msgstr "Maak het coderen van diensten eenvoudig met sjablonen."

#. module: planning
#: model:planning.role,name:planning.planning_role_management
msgid "Management"
msgstr "Management"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__manager_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__manager_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Manager"
msgstr "Manager"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Marc Demo"
msgstr "Marc Demo"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/fields/many2many_avatar_resource/many2many_avatar_resource_field.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__resource_type__material
msgid "Material"
msgstr "Materiaal"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_resources
#: model:ir.ui.menu,name:planning.planning_menu_settings_resource
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Materials"
msgstr "Materialen"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "May 2024"
msgstr "Mei 2024"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__publication_warning
#: model:ir.model.fields,field_description:planning.field_planning_slot__publication_warning
msgid "Modified Since Last Publication"
msgstr "Gewijzigd sinds laatste publicatie"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "Month"
msgstr "Maand"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__month
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__month
msgid "Months"
msgstr "Maanden"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Department"
msgstr "Mijn afdeling"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_my_calendar
#: model:ir.actions.act_window,name:planning.planning_action_open_shift
#: model:ir.ui.menu,name:planning.planning_menu_my_planning
msgid "My Planning"
msgstr "Mijn planning"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Roles"
msgstr "Mijn functies"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Shifts"
msgstr "Mijn diensten"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "My Team"
msgstr "Mijn team"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__name
msgid "Name"
msgstr "Naam"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "New Event"
msgstr "Nieuw evenement"

#. module: planning
#. odoo-javascript
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "New Shift"
msgstr "Nieuwe dienst"

#. module: planning
#. odoo-python
#: code:addons/planning/controllers/main.py:0
msgid "New_Shift"
msgstr "Nieuwe_Shift"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Next Week"
msgstr "Volgende week"

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "No Email Address for Some Employees"
msgstr "Geen e-mailadres voor sommige werknemers"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_number
msgid "No Of Repetitions of the plannings"
msgstr "Geen herhalingen van de planningen"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_report_action_analysis
msgid "No data yet!"
msgstr "Nog geen gegevens!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_resources
msgid "No material resources found. Let's create one!"
msgstr "Geen materiële resources gevonden. Laten we er een maken!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "No roles found. Let's create one!"
msgstr "Geen functies gevonden. Laten we er één maken!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_shift_template
msgid "No shift templates found. Let's create one!"
msgstr "Geen dienstsjabloon gevonden. Laten we er één maken!"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_my_calendar
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_resource
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid "No shifts found. Let's create one!"
msgstr "Geen diensten gevonden. Laten we er één maken!"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__name
#: model:ir.model.fields,field_description:planning.field_planning_slot__name
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Note"
msgstr "Notitie"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Note:"
msgstr "Notitie:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Now that this week is ready, let's get started on <b>next week's "
"schedule</b>."
msgstr ""
"Nu deze week klaar is, gaan we aan de slag met <b>het schema van volgende "
"week</b>."

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__x_times
msgid "Number of Occurrences"
msgstr "Aantal voorvallen"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__x_times
msgid "Number of Repetitions"
msgstr "Aantal herhalingen"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Open Shift"
msgstr "Openstaande dienst"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_model.js:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_model.js:0
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Open Shifts"
msgstr "Openstaande diensten"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
msgid "Open Shifts Available"
msgstr "Beschikbare openstaande diensten"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid "Open shifts assigned"
msgstr "Open diensten toegewezen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid "Open shifts unscheduled"
msgstr "Open diensten ongepland"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.js:0
msgid "Open: %s"
msgstr "Openen: %s"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid ""
"Operation not supported, you should always compare overlap_slot_count to 0 "
"value with = or > operator."
msgstr ""
"Bewerking niet ondersteund, je moet overlap_slot_count altijd vergelijken "
"met de waarde 0 met = of > operator."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_send__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
msgid "Period"
msgstr "Periode"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid ""
"Plan resource allocation across projects and estimate deadlines more "
"accurately"
msgstr ""
"Plan de toewijzing van middelen over projecten en schat deadlines "
"nauwkeuriger in"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Plan your shifts in one click by <b>copying the schedule from the previous "
"week</b>."
msgstr ""
"Plan je diensten met één klik door <b>de planning van de vorige week te "
"kopiëren.</b>."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Plan your shifts in one click by <b>copying the schedule from the previous "
"week</b>. Open the menu to access this option."
msgstr ""
"Plan je diensten met één klik door <b>de planning van de vorige week te "
"kopiëren</b>. Open het menu om naar deze optie te gaan."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__start_time
msgid "Planned Hours"
msgstr "Geplande uren"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: model:ir.actions.report,name:planning.report_planning_slot
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__allocation_type__planning
#: model:ir.ui.menu,name:planning.planning_menu_root
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_view_my_calendar
#: model_terms:ir.ui.view,arch_db:planning.planning_view_pivot
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:planning.slot_report
msgid "Planning"
msgstr "Planning"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_report_action_analysis
#: model:ir.ui.menu,name:planning.planning_menu_planning_analysis
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_graph
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_report_view_pivot
msgid "Planning Analysis"
msgstr "Planningsanalyse"

#. module: planning
#: model:ir.model,name:planning.model_planning_analysis_report
msgid "Planning Analysis Report"
msgstr "Planningsanalyserapport"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Planning Meeting"
msgstr "Planning vergadering"

#. module: planning
#: model:ir.model,name:planning.model_planning_recurrency
msgid "Planning Recurrence"
msgstr "Herhaling van planning"

#. module: planning
#: model:ir.model,name:planning.model_planning_role
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_form
msgid "Planning Role"
msgstr "Planningsfunctie"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "Planning Role List"
msgstr "Lijst planningsfunctie"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot
msgid "Planning Shift"
msgstr "Planning dienst"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__slot_id
msgid "Planning Slot"
msgstr "Planningsslot"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_role__slot_properties_definition
msgid "Planning Slot Properties"
msgstr "Eigenschappen planningsslot"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Planning:"
msgstr "Planning:"

#. module: planning
#: model:mail.template,name:planning.email_template_planning_planning
msgid "Planning: New Schedule"
msgstr "Planning: Nieuw rooster"

#. module: planning
#: model:mail.template,name:planning.email_template_slot_single
msgid "Planning: New Shift"
msgstr "Planning: Nieuwe dienst"

#. module: planning
#: model:mail.template,name:planning.email_template_shift_switch_email
msgid "Planning: Shift Re-assigned"
msgstr "Planning: opnieuw toegewezen dienst"

#. module: planning
#: model:ir.actions.server,name:planning.ir_cron_forecast_schedule_ir_actions_server
msgid "Planning: generate next recurring shifts"
msgstr "Planning: genereer volgende herhalende diensten"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Planning: new open shift available on"
msgstr "Planning: nieuwe openstaande dienst beschikbaar op"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Planning: new shift on"
msgstr "Planning: nieuwe dienst op"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.brand_promotion
msgid "Plans"
msgstr "Plannen"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_email
msgid ""
"Please add a work email for the following employee so that they can receive "
"their planning:"
msgstr ""
"Voeg een werkmail toe voor de volgende medewerker zodat hij of zij de "
"planning kan ontvangen:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "Prepare for the ultimate multi-tasking challenge:"
msgstr "Bereid je voor op de ultieme multitasking-uitdaging:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_renderer_controls.xml:0
msgid "Press Ctrl to duplicate the shift"
msgstr "Druk op Ctrl om de dienst te dupliceren"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_config_settings__module_project_forecast
msgid "Project Planning"
msgstr "Project planning"

#. module: planning
#: model:planning.role,name:planning.planning_role_projector
msgid "Projector"
msgstr "Projector"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__slot_properties
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search
msgid "Properties"
msgstr "Eigenschappen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Publish"
msgstr "Publiceer"

#. module: planning
#: model:ir.actions.server,name:planning.model_planning_slot_action_publish_and_send
#: model_terms:ir.ui.view,arch_db:planning.planning_send_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Publish & Send"
msgstr "Publiceren & verzenden"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_send_action
msgid "Publish & Send the Schedule by Email"
msgstr "Publiceer en verzend de planning via e-mail"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: model:ir.model.fields.selection,name:planning.selection__planning_analysis_report__state__published
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__state__published
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Published"
msgstr "Gepubliceerd"

#. module: planning
#: model:hr.job,name:planning.job_quality_control
#: model:planning.role,name:planning.planning_quality_control
msgid "Quality Control Inspector"
msgstr "Kwaliteitscontroleur"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_res_company__planning_generation_interval
#: model:ir.model.fields,field_description:planning.field_res_config_settings__planning_generation_interval
msgid "Rate Of Shift Generation"
msgstr "Snelheid van diensten aanmaken"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__recurrence_update
msgid "Recurrence Update"
msgstr "Terugkeerpatroon bijwerken"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__recurrency_id
msgid "Recurrency"
msgstr "Herhaling"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "Recurring Shifts"
msgstr "Herhalende diensten"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "Recurring shifts created"
msgstr "Herhalende diensten aangemaakt"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__slot_ids
msgid "Related Planning Entries"
msgstr "Gerelateerde planningsgegevens"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__user_id
msgid "Related user name for the resource to manage its access."
msgstr ""
"Gekoppelde gebruikersnaam voor de resource om zijn toegang te beheren."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Remove"
msgstr "Verwijderen"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat
msgid "Repeat"
msgstr "Herhaal"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_interval
msgid "Repeat Every"
msgstr "Herhaal iedere"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_type
msgid "Repeat Type"
msgstr "Soort herhaling"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_unit
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_unit
msgid "Repeat Unit"
msgstr "Herhaal eenheid"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_until
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_until
msgid "Repeat Until"
msgstr "Herhaal tot"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_interval
msgid "Repeat every"
msgstr "Herhaal elke"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_number
#: model:ir.model.fields,field_description:planning.field_planning_slot__repeat_number
msgid "Repetitions"
msgstr "Herhalingen"

#. module: planning
#: model:ir.ui.menu,name:planning.planning_menu_reporting
msgid "Reporting"
msgstr "Rapportages"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Requests to Switch"
msgstr "Verzoeken om te wisselen"

#. module: planning
#: model:ir.actions.server,name:planning.model_planning_slot_action_reset_to_draft
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Reset to Draft"
msgstr "Terugzetten naar concept"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__resource_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_id
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Resource"
msgstr "Resource"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_color
msgid "Resource color"
msgstr "Kleur resource"

#. module: planning
#: model:ir.model,name:planning.model_resource_resource
#: model:ir.model.fields,field_description:planning.field_planning_role__resource_ids
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_search
msgid "Resources"
msgstr "Resources"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__role_id
#: model:ir.model.fields,field_description:planning.field_planning_slot_template__role_id
#: model_terms:ir.ui.view,arch_db:planning.open_slots_list_template
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_search
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Role"
msgstr "Functie"

#. module: planning
#: model:ir.model.fields,help:planning.field_hr_employee__default_planning_role_id
msgid ""
"Role that will be selected by default when creating a shift for this employee.\n"
"This role will also have precedence over the other roles of the employee when planning orders."
msgstr ""
"Functie die standaard worden gekozen bij het creëren van een dienst voor deze werknemer.\n"
"Deze functie heeft ook voorrang op de andere functies van de werknemer bij het plannen van orders."

#. module: planning
#: model:ir.model.fields,help:planning.field_resource_resource__default_role_id
msgid ""
"Role that will be selected by default when creating a shift for this resource.\n"
"This role will also have precedence over the other roles of the resource when planning shifts."
msgstr ""
"Functie die standaard worden gekozen bij het aanmaken van een dienst voor deze resource. \n"
"Deze functie heeft ook voorrang op de andere functies van de resource bij het plannen van diensten."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_shift_ics_description
msgid "Role:"
msgstr "Functie:"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: model:ir.actions.act_window,name:planning.planning_action_roles
#: model:ir.model.fields,field_description:planning.field_hr_employee__planning_role_ids
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_roles
#: model:ir.model.fields,field_description:planning.field_resource_resource__role_ids
#: model:ir.ui.menu,name:planning.planning_menu_settings_role
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_search_view_inherit
#: model_terms:ir.ui.view,arch_db:planning.view_employee_filter_inherit_hr
msgid "Roles"
msgstr "Functies"

#. module: planning
#: model:ir.model.fields,help:planning.field_hr_employee__planning_role_ids
msgid ""
"Roles that the employee can fill in. When creating a shift for this employee, only the shift templates for these roles will be displayed.\n"
"Similarly, only the open shifts available for these roles will be sent to the employee when the schedule is published.\n"
"Additionally, the employee will only be assigned orders for these roles (with the default planning role having precedence over the other ones).\n"
"Leave empty for the employee to be assigned shifts regardless of the role."
msgstr ""
"Functies die de werknemer kan invullen. Bij het aanmaken van een dienst voor deze werknemer worden alleen de dienstsjablonen voor deze functies getoond.\n"
"Evenzo worden alleen de openstaande diensten die beschikbaar zijn voor deze functies naar de werknemer verzonden wanneer het uurrooster wordt gepubliceerd.\n"
"Bovendien krijgt de werknemer alleen orders voor deze functies toegewezen (waarbij de standaard planning functie voorrang heeft op de andere).\n"
"Laat leeg zodat de werknemer diensten krijgt toegewezen, ongeacht de functie."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save"
msgstr "Opslaan"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.employee_no_email_list_wizard
msgid "Save & Send Schedule"
msgstr "Rooster opslaan en verzenden"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Save Template"
msgstr "Template bewaren"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_creation
msgid "Save as Template"
msgstr "Opslaan als sjabloon"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid "Save this shift once it is ready."
msgstr "Sla deze dienst op als deze klaar is."

#. module: planning
#: model:planning.role,name:planning.planning_role_scanner
msgid "Scanner"
msgstr "Scanner"

#. module: planning
#: model:ir.model,name:planning.model_planning_planning
#: model:ir.ui.menu,name:planning.planning_menu_schedule
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Schedule"
msgstr "Inplannen"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_resource
msgid "Schedule by Resource"
msgstr "Planning per resource"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_schedule_by_role
msgid "Schedule by Role"
msgstr "Planning per functie"

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "Schedule sent to your employees"
msgstr "Planning verzonden naar je werknemers"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_resource
#: model_terms:ir.actions.act_window,help:planning.planning_action_schedule_by_role
msgid ""
"Schedule your human and material resources across roles, projects and sales "
"orders."
msgstr ""
"Plan je menselijke en materiële middelen over functies, projecten en "
"verkooporders."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Search operation not supported"
msgstr "Zoekbewerking niet ondersteund"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__access_token
msgid "Security Token"
msgstr "Veiligheidstoken"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/conflicting_slot_ids_field/conflicting_slot_ids_field.xml:0
msgid "See conflicting shifts"
msgstr "Bekijk conflicterende diensten"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form_in_gantt
msgid "Send"
msgstr "Verzenden"

#. module: planning
#: model:ir.model,name:planning.model_planning_send
msgid "Send Planning"
msgstr "Verzend planning"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_calendar_controller.xml:0
#: code:addons/planning/static/src/views/planning_gantt/planning_gantt_controller.xml:0
msgid "Send schedule"
msgstr "Verzend planning"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_settings
#: model:ir.ui.menu,name:planning.planning_menu_settings_config
msgid "Settings"
msgstr "Instellingen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Share the schedule with your team by publishing and sending it. Open the "
"menu to access this option."
msgstr ""
"Publiceer en verstuur de planning om deze met je team te delen. Selecteer "
"deze optie via het menu."

#. module: planning
#. odoo-python
#: code:addons/planning/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
msgid "Shift"
msgstr "Dienst"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "Shift List"
msgstr "Dienstlijst"

#. module: planning
#: model:ir.model,name:planning.model_planning_slot_template
msgid "Shift Template"
msgstr "Dienstsjabloon"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Shift Template Form"
msgstr "Dienstsjabloon formulier"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_tree
msgid "Shift Template List"
msgstr "Dienstsjabloonlijst"

#. module: planning
#: model:ir.actions.act_window,name:planning.planning_action_shift_template
#: model:ir.model.fields,field_description:planning.field_planning_slot__template_id
#: model:ir.ui.menu,name:planning.planning_menu_settings_shift_template
msgid "Shift Templates"
msgstr "Dienstsjablonen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "Shift saved as template"
msgstr "Verschuiving opgeslagen als sjabloon"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Shift sent"
msgstr "Shift verzonden"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts Planned"
msgstr "Geplande diensten"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "Shifts from"
msgstr "Diensten van"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts in Conflict"
msgstr "Diensten in conflict"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts of Your Department Member"
msgstr "Diensten van je afdelingslid"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Shifts of Your Team Member"
msgstr "Diensten van je teamlid"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "Shifts reset to draft"
msgstr "Verschuivingen teruggezet naar concept"

#. module: planning
#: model:planning.role,name:planning.planning_shipping_associate
msgid "Shipping Associate"
msgstr "Medewerker verzending"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "Some changes were made since this shift was published."
msgstr ""
"Er zijn enkele wijzigingen aangebracht sinds deze dienst werd gepubliceerd."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Span"
msgstr "Duur"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_planning
msgid "Start"
msgstr "Start"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Start & End Hours"
msgstr "Start- & Einduren"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_planning__start_datetime
#: model:ir.model.fields,field_description:planning.field_planning_slot__start_datetime
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Start Date"
msgstr "Begindatum"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__state
#: model:ir.model.fields,field_description:planning.field_planning_slot__state
msgid "Status"
msgstr "Status"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_planning__end_datetime
#: model:ir.model.fields,field_description:planning.field_planning_send__end_datetime
msgid "Stop Date"
msgstr "Einddatum"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__res_company__planning_employee_unavailabilities__switch
msgid "Switch shifts with other employees"
msgstr "Diensten ruilen met andere werknemers"

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_res_company_planning_self_unassign_days_before_positive
msgid ""
"The amount of days before unassignment must be positive or equal to zero."
msgstr ""
"Het aantal dagen voor uitdiensttreding moet positief of gelijk aan nul zijn."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The company does not allow you to unassign yourself from shifts."
msgstr ""
"Het bedrijf laat je niet toe om je eigen toewijzing ongedaan te maken."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The deadline for unassignment has passed."
msgstr "De deadline voor uitdiensttreding is verstreken."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_check_start_date_lower_end_date
msgid "The end date of a shift should be after its start date."
msgstr "De einddatum van een dienst moet na de begindatum liggen."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_recurrency.py:0
msgid "The number of repetitions cannot be negative."
msgstr "Het aantal herhalingen kan niet negatief zijn."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_recurrency_check_repeat_interval_positive
msgid "The recurrence should be greater than 0."
msgstr "De herhaling moet groter zijn dan 0."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The recurrence's end date should fall after the shift's start date."
msgstr ""
"De einddatum van het herhalingspatroon moet na de begindatum van de dienst "
"vallen."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_form/planning_form_view.js:0
msgid "The shift has successfully been created."
msgstr "De dienst is succesvol gemaakt."

#. module: planning
#. odoo-javascript
#. odoo-python
#: code:addons/planning/static/src/views/planning_hooks.js:0
#: code:addons/planning/wizard/planning_send.py:0
msgid ""
"The shifts have already been published, or there are no shifts to publish."
msgstr ""
"De diensten zijn al gepubliceerd, of er zijn geen diensten om te publiceren."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "The shifts have successfully been published and sent."
msgstr "De diensten zijn succesvol gepubliceerd en verzonden."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_duration_days_positive
msgid "The span must be at least 1 working day."
msgstr "De tijdsduur moet minstens 1 werkdag zijn."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid ""
"The start and end hours must be greater or equal to 0 and lower than 24."
msgstr ""
"De begin- en einduren moeten groter of gelijk aan 0 zijn, en kleiner dan 24 "
"zijn."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_lower_than_24
msgid "The start hour cannot be greater than 24."
msgstr "Het beginuur kan niet groter zijn dan 24."

#. module: planning
#: model:ir.model.constraint,message:planning.constraint_planning_slot_template_check_start_time_positive
msgid "The start hour cannot be negative."
msgstr "Het beginuur kan niet negatief zijn."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning_template.py:0
msgid ""
"The start hour cannot be before the end hour for a one-day shift template."
msgstr ""
"Het beginuur kan niet voor het einduur vallen voor het sjabloon van een "
"eendagse dienst."

#. module: planning
#. odoo-python
#: code:addons/planning/wizard/planning_send.py:0
msgid "The work email is missing for the following employees:"
msgstr "De werkmail ontbreekt voor de volgende medewerkers:"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no resources available for this open shift."
msgstr "Er zijn geen beschikbare resources voor deze openstaande dienst."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_hooks.js:0
msgid ""
"There are no shifts planned for the previous week, or they have already been"
" copied."
msgstr ""
"Er zijn geen diensten gepland voor de vorige week, of ze zijn al gekopieerd."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no shifts to publish and send."
msgstr "Er zijn geen diensten om te publiceren en te verzenden."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "There are no shifts to reset to draft."
msgstr "Er zijn geen diensten om te herzetten naar concept."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "This Progress Bar is not implemented."
msgstr "Deze voorgangsbalk is niet geïmplementeerd."

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__was_copied
msgid "This Shift Was Copied From Previous Week"
msgstr "Deze dienst is gekopieerd van vorige week"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "This Week"
msgstr "Deze week"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__subsequent
msgid "This and following shifts"
msgstr "Deze dienst en de volgende"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid ""
"This employee is not expected to work during this period, either because "
"they do not have a current contract or because they are on leave."
msgstr ""
"Deze werknemer moet tijdens deze periode niet werken, hetzij omdat hij geen "
"lopend contract heeft of omdat hij met verlof is."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "This method must take two slots in argument."
msgstr "Deze methode moet twee slots als argument hebben."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid ""
"This open shift is no longer available, or the planning has been updated in "
"the meantime. Please contact your manager for further information."
msgstr ""
"Deze openstaande dienst is niet meer beschikbaar, of de planning is "
"ondertussen aangepast. Neem contact op met je manager voor meer informatie."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/views/planning_calendar/planning_ask_recurrence_update/planning_ask_recurrence_update_dialog.js:0
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__recurrence_update__this
msgid "This shift"
msgstr "Deze dienst"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "This shift is recurrent. Delete:"
msgstr "Deze dienst is terugkerend. Verwijder:"

#. module: planning
#: model:digest.tip,name:planning.digest_tip_planning_0
msgid "Tip: Record your planning faster"
msgstr "Tip: leg je planning sneller vast"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_slot__repeat
msgid ""
"To avoid polluting your database and performance issues, shifts are only "
"created for the next 6 months. They are then gradually created as time "
"passes by in order to always get shifts 6 months ahead. This value can be "
"modified from the settings of Planning, in debug mode."
msgstr ""
"Om de vervuiling van je database tegen te gaan en prestatieproblemen te "
"vermijden worden de diensten alleen aangemaakt voor de komende 6 maanden. Ze"
" worden geleidelijk aangemaakt na verloop van tijd om de diensten altijd 6 "
"maanden op voorhand beschikbaar te stellen. Deze waarde kan worden "
"bijgewerkt in de instellingen van de Planning-app, in debugmodus."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
#: model_terms:ir.ui.view,arch_db:planning.planning_view_search_base
msgid "Today"
msgstr "Vandaag"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Total"
msgstr "Totaal"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__resource_type
#: model:ir.model.fields,field_description:planning.field_planning_slot__resource_type
msgid "Type"
msgstr "Soort"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__res_company__planning_employee_unavailabilities__unassign
msgid "Unassign themselves from shifts"
msgstr "Zichzelf uit diensten verwijderen"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_type__until
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_type__until
msgid "Until"
msgstr "T/m"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.unwanted_slots_list_template
msgid "Unwanted Shifts Available"
msgstr "Ongewenste diensten beschikbaar"

#. module: planning
#: model:ir.model.fields,help:planning.field_planning_recurrency__repeat_until
msgid "Up to which date should the plannings be repeated"
msgstr "T/m welke datum moet de planning worden herhaald"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_analysis_report__user_id
#: model:ir.model.fields,field_description:planning.field_planning_slot__user_id
#: model:res.groups,name:planning.group_planning_user
msgid "User"
msgstr "Gebruiker"

#. module: planning
#. odoo-python
#: code:addons/planning/models/hr.py:0
msgid "View Planning"
msgstr "Bekijk planning"

#. module: planning
#: model_terms:ir.actions.act_window,help:planning.planning_action_roles
msgid "Waiter"
msgstr "Bediende"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "Week"
msgstr "Week"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_recurrency__repeat_type
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__week
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__week
msgid "Weeks"
msgstr "Weken"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_email
msgid "Work Email"
msgstr "Zakelijk e-mailadres"

#. module: planning
#: model:ir.model.fields,field_description:planning.field_planning_slot__work_location_id
msgid "Work Location"
msgstr "Werklocatie"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_slot_template_view_form
msgid "Working Days"
msgstr "Werkdagen"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/tours/planning.js:0
msgid ""
"Write the <b>role</b> your employee will perform (<i>e.g. Chef, Bartender, "
"Waiter, etc.</i>). <i>Tip: Create open shifts for the roles you will be "
"needing to complete a mission. Then, assign those open shifts to the "
"resources that are available.</i>"
msgstr ""
"Schrijf de <b>functie</b> op die je werknemer zal vervullen (<i>bijv. Chef, "
"barman, ober, enz.</i>). <i>Tip: maak openstaande diensten voor de functies "
"die je nodig hebt om een missie te voltooien. Wijs die openstaande diensten "
"vervolgens toe aan de beschikbare werknemers.</i>"

#. module: planning
#: model:ir.model.fields.selection,name:planning.selection__planning_recurrency__repeat_unit__year
#: model:ir.model.fields.selection,name:planning.selection__planning_slot__repeat_unit__year
msgid "Years"
msgstr "Jaar"

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You are not allowed to reset shifts to draft."
msgstr "Het is niet toegestaan om diensten terug te zetten naar concept."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You can not assign yourself to an already assigned shift."
msgstr "Je kunt jezelf niet toewijzen aan een reeds toegewezen dienst."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You can not unassign another employee than yourself."
msgstr "Je kunt geen andere werknemer dan jezelf uitschrijven."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot assign yourself to a shift in the past."
msgstr "Je kunt jezelf niet toewijzen aan een dienst in het verleden."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot cancel a request to switch made by another user."
msgstr ""
"Je kunt een verzoek an een andere gebruiker om van dienst te wisselen niet "
"annuleren."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot cancel a request to switch that is in the past."
msgstr ""
"Je kunt een verzoek tot wisseling dat in het verleden is gedaan niet "
"annuleren."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot request to switch a shift that is assigned to another user."
msgstr ""
"Je kunt niet vragen om met een dienst te wisselen die aan een andere "
"gebruiker is toegewezen."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot switch a shift that is in the past."
msgstr "Je kunt een dienst die in het verleden ligt niet wisselen."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You cannot unassign yourself from a shift in the past."
msgstr "Je kunt jezelf niet terugtrekken uit een dienst in het verleden."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid "You don't have any shifts planned yet."
msgstr "Je hebt nog geen geplande diensten."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/js/planning_calendar_front.js:0
msgid ""
"You don't have any shifts planned yet. You can assign yourself some of the "
"available open shifts."
msgstr ""
"Je hebt nog geen geplande diensten. Wijs jezelf beschikbare open diensten "
"toe."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to assign yourself to shifts."
msgstr "Je hebt het recht niet om jezelf diensten toe te wijzen."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to cancel a request to switch."
msgstr "Je hebt het recht niet om een verzoek tot wisseling te annuleren."

#. module: planning
#. odoo-python
#: code:addons/planning/models/planning.py:0
msgid "You don't have the right to switch shifts."
msgstr "Je hebt het recht niet om van diensten te wisselen."

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "You have been successfully unassigned from this shift"
msgstr "Je bent met succes uit deze dienst verwijderd"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.slot_unassign
msgid "Your Planning"
msgstr "Je planning"

#. module: planning
#: model:mail.template,subject:planning.email_template_planning_planning
msgid ""
"Your planning from {{ format_date(ctx.get('start_datetime')) }} to {{ "
"format_date(ctx.get('end_datetime')) }}"
msgstr ""
"Je planning van {{ format_date(ctx.get('start_datetime')) }} t/m {{ "
"format_date(ctx.get('end_datetime')) }}"

#. module: planning
#: model:mail.template,subject:planning.email_template_shift_switch_email
msgid ""
"Your shift on {{ ctx.get('start_datetime') }} was re-assigned to {{ "
"ctx.get('new_assignee_name', 'Marc Demo') }}"
msgstr ""
"Je dienst op {{ ctx.get('start_datetime') }} is opnieuw toegewezen aan {{ "
"ctx.get('new_assignee_name', 'Marc Demo') }}"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "all"
msgstr "alle"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "all shifts"
msgstr "alle diensten"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "close"
msgstr "sluiten"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.res_config_settings_view_form
msgid "days before the beginning of the shift"
msgstr "dagen voor het begin van de dienst"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:planning.planning_view_form
#: model_terms:ir.ui.view,arch_db:planning.planning_view_tree
msgid "e.g. Bartender"
msgstr "bijv. Barman"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "e.g. Cleaner"
msgstr "bijv. Schoonmaker"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_form_view_inherit
#: model_terms:ir.ui.view,arch_db:planning.resource_resource_tree_view_inherit
msgid "e.g. Crane"
msgstr "bijv. Kraan"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.hr_employee_view_form_simplified
#: model_terms:ir.ui.view,arch_db:planning.planning_role_view_tree
msgid "e.g. John Doe"
msgstr "b.v. John Doe"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.planning_view_gantt
msgid "other shift(s) in conflict."
msgstr "andere dienst(en) in conflict."

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "subsequent"
msgstr "volgende"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "this"
msgstr "deze"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "this and following shifts"
msgstr "deze dienst en de volgende"

#. module: planning
#. odoo-javascript
#: code:addons/planning/static/src/components/address_recurrency_confirmation_dialog/address_recurrency_confirmation_dialog.xml:0
msgid "this shift"
msgstr "deze dienst"

#. module: planning
#: model_terms:ir.ui.view,arch_db:planning.period_report_template
msgid "to"
msgstr "t/m"

#. module: planning
#: model:mail.template,subject:planning.email_template_slot_single
msgid "{{ ctx.get('mail_subject', '') }} {{ ctx.get('start_datetime' , '') }}"
msgstr ""
"{{ ctx.get('mail_subject', '') }} {{ ctx.get('start_datetime' , '') }}"
