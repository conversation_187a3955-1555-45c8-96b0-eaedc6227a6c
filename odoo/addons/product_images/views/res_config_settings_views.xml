<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.product.images</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//div[@id='msg_module_product_images']" position="replace">
                <div class="content-group"
                     invisible="not module_product_images"
                     id="msg_module_product_images">
                    <div class="content-group mt16 row">
                        <label string="API Key" for="google_custom_search_key"
                               class="col-4 col-lg-4 o_light_label"/>
                        <field name="google_custom_search_key" nolabel="1"/>
                        <label string="Search Engine ID" for="google_pse_id"
                               class="col-4 col-lg-4 o_light_label"/>
                        <field name="google_pse_id" nolabel="1"/>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
