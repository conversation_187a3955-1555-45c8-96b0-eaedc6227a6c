# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_kz
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-11-23 11:24+0000\n"
"PO-Revision-Date: 2022-11-23 11:24+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_001_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_001_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_001
msgid "300.00.001. Sales turnover subject to VAT, including"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_002_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_002
msgid "300.00.002. Sales turnover subject to zero VAT"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_003_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_003_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_003
msgid "300.00.003. Adjustment of taxable turnover"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_004_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_004
msgid ""
"300.00.004. Turnover on sale of goods, works, services, the place of sale of"
" which is not the Republic of Kazakhstan"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_005_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_005
msgid "300.00.005. Turnover exempt from VAT"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_006_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_006
msgid "300.00.006. Total turnover"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_007_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_007
msgid "300.00.007. Share of taxable turnover in total turnover"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_008_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_008
msgid ""
"300.00.008. Share of taxable turnover at zero rate in the total taxable "
"turnover"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_009_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_009
msgid ""
"300.00.009. Share of taxable turnover in total turnover, calculated without "
"taking into account turnovers, for which the proportional with the right to "
"separate accounting for individual turnovers is applied"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_010_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_010
msgid ""
"300.00.010. VAT accrued on import of goods by offset method in accordance "
"with the terms of the subsoil use contract"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_011_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_011
msgid ""
"300.00.011. VAT accrued on import of goods by offset method, except for line"
" 300.00.010 (300.04.001 B)"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_012_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_012
msgid "300.00.012. Total VAT accrued"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_013_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_013_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_013
msgid ""
"300.00.013. Goods, works, services purchased with VAT in the Republic of "
"Kazakhstan, including:"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_014_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_014_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_014
msgid "300.00.014. Works, services purchased from a non-resident"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_015_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_015
msgid ""
"300.00.015. Goods, works, services purchased without VAT and for which set-"
"off is not allowed"
msgstr ""

#. module: l10n_kz
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_016
msgid ""
"300.00.016. Imports with payment of VAT (on the basis of the declaration of "
"goods, on the basis of the declaration form 320.00), including:"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_017_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_017
msgid "300.00.017. Exempted imports of goods"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_018_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_018
msgid ""
"300.00.018. Import of goods for which the deadline for payment of VAT has "
"been changed (on the basis of the declaration of goods)"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_019_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_019
msgid ""
"300.00.019. VAT paid on imported goods, for which the deadline for VAT "
"payment was changed"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_020_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_020_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_020
msgid ""
"300.00.020. Imports of goods for which VAT was paid by the offset method in "
"accordance with the terms of the subsoil use contract"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_021_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_021
msgid "300.00.021. Total purchases"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_022_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_022
msgid "300.00.022. Adjustment of the amount of VAT to be credited"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_023_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_023
msgid "300.00.023. Total amount of VAT to be credited"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_024_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_024
msgid ""
"300.00.024. The total amount of VAT to be credited when applying the "
"proportional method with the right to separate accounting for certain "
"turnovers, including:"
msgstr ""

#. module: l10n_kz
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_025
msgid "300.00.025. The amount of VAT allowed to be deducted:"
msgstr ""

#. module: l10n_kz
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_026
msgid "300.00.026. The amount of VAT not allowed to be set off:"
msgstr ""

#. module: l10n_kz
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_027
msgid ""
"300.00.027. The amount of excess VAT, attributable to offset, over the "
"amount of accrued tax"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_028_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_028
msgid ""
"300.00.028. Amount of VAT on goods, works, services used for the purposes of"
" turnovers taxed at zero rate"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_029_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_029_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_029
msgid "300.00.029. Import of goods for which VAT is paid by offset method"
msgstr ""

#. module: l10n_kz
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_030
msgid "300.00.030. Calculated amount of VAT for the tax period:"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_031_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_031
msgid ""
"300.00.031. Reducing the amount of excess VAT generated after meeting the "
"requirements specified in subparagraph 3) of paragraph 1 of Article 369 of "
"the Tax Code"
msgstr ""

#. module: l10n_kz
#: model:ir.model,name:l10n_kz.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_kz
#. odoo-python
#: code:addons/l10n_kz/models/chart_template.py:0
msgid "Bank"
msgstr ""

#. module: l10n_kz
#. odoo-python
#: code:addons/l10n_kz/models/chart_template.py:0
msgid "Cash"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_027_1_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_027_1
msgid ""
"I. Accrued at the beginning of the reporting tax period on an accrual basis "
"for the activities provided for by Article 411 of the Tax Code"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_030_1_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_030_1
msgid "I. Amount of VAT payable to the budget"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_006_1_net
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_006_1
msgid "I. Amount of taxable turnover"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_024_1_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_024_1
msgid ""
"I. For goods, works, services for which the proportional method of "
"offsetting is applied"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_016_1_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_016_1_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_016_1
msgid ""
"I. Imports from states that are not members of the Eurasian Economic Union"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_013_1_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_013_1_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_013_1
msgid "I. On invoices"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_001_1_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_001_1_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_001_1
msgid "I. With the issuance of invoices"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_026_1_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_025_1
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_026_1
msgid "I. With the proportional method"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_027_2_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_027_2
msgid ""
"II. Cumulative total on the declaration at the end of the reporting tax "
"period"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_030_2_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_030_2
msgid ""
"II. Excess of the amount of VAT credited over the amount of accrued tax"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_024_2_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_024_2
msgid ""
"II. For goods, works, services, for which the method of separate accounting "
"is applied"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_016_2_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_016_2_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_016_2
msgid "II. Imports from member states of the Eurasian Economic Union"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_013_2_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_013_2_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_013_2
msgid "II. On other documents"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_026_2_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_026_2
msgid "II. Through separate accounting"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_025_1_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_025_2
msgid "II. With the split method"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_001_2_net
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_001_2_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_001_2
msgid "II. Without the issuance of invoices"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_026_3_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_026_3
msgid ""
"III. At proportional and separate method with separate accounting on "
"separate turnovers"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_025_3_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_025_3
msgid ""
"III. At the proportional method with separate accounting on separate "
"turnovers"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_024_3_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_024_3
msgid ""
"III. For goods, works, services used simultaneously for the purposes of "
"taxable and non-taxable turnover"
msgstr ""

#. module: l10n_kz
#: model:account.report.expression,report_line_name:l10n_kz.l10n_kz_tr_expression_300_00_025_4_tax
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_line_300_00_025_4
msgid ""
"IV. When applying the provisions of Article 411 of the Tax Code (additional "
"VAT credit)"
msgstr ""

#. module: l10n_kz
#: model:account.report.column,name:l10n_kz.l10n_kz_tr_column_tax
msgid "Sum of VAT"
msgstr ""

#. module: l10n_kz
#: model:account.report.column,name:l10n_kz.l10n_kz_tr_column_net
msgid "Sum of sales turnover without VAT"
msgstr ""

#. module: l10n_kz
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_title_vat_sum
msgid "The amount of VAT to be credited"
msgstr ""

#. module: l10n_kz
#: model:account.report,name:l10n_kz.l10n_kz_tr_form_300_00
msgid "VAT Report - Form 300.00"
msgstr ""

#. module: l10n_kz
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_title_vat_calculation
msgid "VAT accrual"
msgstr ""

#. module: l10n_kz
#: model:account.report.line,name:l10n_kz.l10n_kz_tr_title_vat_tax_period
msgid "VAT calculations for the tax period"
msgstr ""
