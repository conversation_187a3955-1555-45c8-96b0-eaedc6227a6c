<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_ec" model="res.partner" forcecreate="1">
        <field name="name">EC Company</field>
        <field name="l10n_latam_identification_type_id" ref="l10n_ec.ec_ruc"/>
        <field name="vat">2222222222001</field>
        <field name="street">Av. de las Americas 505</field>
        <field name="city">Quito</field>
        <field name="country_id" ref="base.ec"/>

        <field name="zip">170112</field>
        <field name="phone">+593 99 123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.ecexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_ec" model="res.company" forcecreate="1">
        <field name="name">EC Company</field>
        <field name="partner_id" ref="base.partner_demo_company_ec"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_ec')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_ec'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>ec</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_ec')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
