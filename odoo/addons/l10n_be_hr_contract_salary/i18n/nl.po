# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_contract_salary
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-11-20 08:49+0000\n"
"PO-Revision-Date: 2025-02-07 17:07+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/models/hr_contract.py:0
msgid "%(co2_fee)s € (CO2 Fee) + %(rent)s € (Rent)"
msgstr "%(co2_fee)s € (CO2-vergoeding) + %(rent)s € (Huur)"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/models/hr_contract.py:0
msgid "%s km"
msgstr "%s km"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/models/hr_contract.py:0
msgid "%s € (Rent)"
msgstr "%s € (Huur)"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_thirteen_month
msgid "13th Month"
msgstr "13de maand"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_representation_fees_value_2
msgid "150 €"
msgstr "€ 150"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_mobile_value_1
msgid "30.0 €"
msgstr "€ 30"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,helper:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_disabled_juniors_dependant
#: model:hr.contract.salary.personal.info,helper:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_juniors_dependent
msgid "< 65 years old"
msgstr "< 65 jaar"

#. module: l10n_be_hr_contract_salary
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_contract_salary.res_config_settings_view_form
msgid "<span>days / year</span>"
msgstr "<span>dagen / jaar</span>"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,helper:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_disabled_senior_dependent
#: model:hr.contract.salary.personal.info,helper:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_senior_dependent
msgid ">= 65 years old"
msgstr ">= 65 jaar"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__additional_car_ids
msgid "Additional cars"
msgstr "Extra wagens"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_ambulatory_insured_spouse
msgid "Ambulatory (spouse)"
msgstr "Ambulante (echtgeno(o)t(e))"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_ambulatory_insured_children
msgid "Ambulatory Children (<19 y/o)"
msgstr "Ambulante Kinderen (<19 jaar)"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_ambulatory_insured_adults
msgid "Ambulatory Children (>= 19 y/o)"
msgstr "Ambulante Kinderen (>= 19 jaar)"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_ambulatory_insurance
msgid "Ambulatory Insurance"
msgstr "Ambulante verzekering"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_ambulatory_insurance_note
msgid "Ambulatory Insurance (info)"
msgstr "Ambulante verzekering (info)"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__assigned_car_warning
msgid "Assigned Car Warning"
msgstr "Waarschuwing toegewezen voertuig"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_transport_private_bike
msgid "Bicyle Cost"
msgstr "Fietskosten"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__l10n_be_canteen_cost
msgid "Canteen Cost"
msgstr "Kantinekosten"

#. module: l10n_be_hr_contract_salary
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_contract_salary.hr_contract_salary_offer_view_form
msgid "Car"
msgstr "Voertuig"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_job__l10n_be_custom_representation_fees_car_management
msgid "Car Management"
msgstr "Voertuigbeheer"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_certificate_civil_engineer
msgid "Civil Engineer"
msgstr "Burgerlijk ingenieur"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_resume__code
msgid "Code"
msgstr "Code"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_yearly_commission
msgid "Commissions"
msgstr "Commissies"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_transport_company_bike
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_transport_company_bike
msgid "Company Bike"
msgstr "Bedrijfsfiets"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_transport_company_car
msgid "Company Car"
msgstr "Bedrijfswagen"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_transport_new_car
msgid "Company Car (To order)"
msgstr "Bedrijfswagen (Te bestellen)"

#. module: l10n_be_hr_contract_salary
#: model:ir.model,name:l10n_be_hr_contract_salary.model_res_config_settings
msgid "Config Settings"
msgstr "Configuratie instellingen"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__contract_type_id
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__contract_type_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_contract_salary.hr_contract_salary_offer_view_search
msgid "Contract Type"
msgstr "Contracttype"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__country_code
msgid "Country Code"
msgstr "Landcode"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_job__l10n_be_custom_representation_fees
msgid "Customized Representation Fees"
msgstr "Aangepaste representatiekosten"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__car_id
msgid "Default Vehicle"
msgstr "Standaard voertuig"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,help:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__car_id
msgid ""
"Default employee's company car. If left empty, the default value will be the"
" employee's current car."
msgstr ""
"Standaard bedrijfswagen van de werknemer. Indien leeg gelaten, is de "
"standaardwaarde de huidige auto van de werknemer."

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_disabled
msgid "Disabled"
msgstr "Gehandicapt"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_disabled_children_bool
msgid "Disabled Children"
msgstr "Gehandicapte kinderen"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_disabled_spouse_bool
msgid "Disabled Spouse"
msgstr "Gehandicapte echtgeno(o)t(e)"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_disabled_juniors_dependant
msgid "Disabled people"
msgstr "Gehandicapte mensen"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_disabled_senior_dependent
msgid "Disabled seniors"
msgstr "Gehandicapte seniors"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_marital_divorced
msgid "Divorced"
msgstr "Gescheiden"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_double_holiday
msgid "Double Holiday Pay"
msgstr "Dubbel vakantiegeld"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__double_holiday_wage
msgid "Double Holiday Wage"
msgstr "Dubbel vakantiegeld"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_driving_license
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__driving_license
msgid "Driving License"
msgstr "Rijbewijs"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__driving_license_filename
msgid "Driving License Filename"
msgstr "Bestandsnaam rijbewijs"

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_meal_vouchers
msgid "Each month you'll receive 8€ per day worked on a meal voucher card."
msgstr ""
"Je ontvangt elke maand 8€ per gewerkte dag op een maaltijdchequekaart."

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_eco_voucher
msgid "Eco Vouchers"
msgstr "Ecocheques"

#. module: l10n_be_hr_contract_salary
#: model:ir.model,name:l10n_be_hr_contract_salary.model_hr_employee
msgid "Employee"
msgstr "Werknemer"

#. module: l10n_be_hr_contract_salary
#: model:ir.model,name:l10n_be_hr_contract_salary.model_hr_contract
msgid "Employee Contract"
msgstr "Arbeidsovereenkomst"

#. module: l10n_be_hr_contract_salary
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_contract_salary.hr_payroll_dashboard_warning_ip_eligible_contract
msgid "Employees without Intellectual Property"
msgstr "Werknemers zonder intellectuele eigendom"

#. module: l10n_be_hr_contract_salary
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_contract_salary.hr_payroll_dashboard_warning_employee_without_exemption
msgid "Employees without Withholding Taxes Exemption"
msgstr "Werknemers zonder vrijstelling van bronbelasting"

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_internet
msgid ""
"Enter the amount of your internet subscription invoice. If you have a pack "
"only the internet part of your operator subscription will be paid by the "
"employer."
msgstr ""
"Geef het bedrag van de factuur van je internetabonnement in. Als je een "
"pakket hebt, wordt enkel het internetgedeelte van je operatorabonnement door"
" de werkgever betaald."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_transport_public
msgid ""
"Enter the monthly amount you spend on public transportation to go to work. "
"The approximative amount reimbursed by the employer is calculated "
"accordingly."
msgstr ""
"Geef het maandelijkse bedrag in dat je uitgeeft aan openbaar vervoer om naar"
" het werk te gaan. Het bedrag dat de werkgever ongeveer vergoed, wordt "
"dienovereenkomstig berekend."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_transport_train
msgid ""
"Enter the monthly amount you spend on train transportation to go to work. "
"The approximative amount reimbursed by the employer is calculated "
"accordingly."
msgstr ""
"Geef het maandelijkse bedrag in dat je uitgeeft aan treinvervoer om naar je "
"werk te gaan. Het bedrag dat de werkgever je ongeveer terugbetaald, wordt "
"dienovereenkomstig berekend."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_transport_private_car
msgid ""
"Enter the number of kilometers between your home and your work place. The "
"approximative amount reimbursed by the employer is calculated accordingly."
msgstr ""
"Geef het aantal kilometers in tussen je woonplaats en je werkplek. Het "
"bedrag dat de werkgever ongeveer vergoed, wordt dienovereenkomstig berekend."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_transport_private_bike
msgid ""
"Enter the number of kilometers between your home and your work place. The "
"reimbursed amount is of 0.20 cents per km with a maximum amount of 8€, "
"computed on a one-day-per-week basis."
msgstr ""
"Geef het aantal kilometers in tussen je woonplaats en je werkplek. Het "
"vergoede bedrag is 0,20 cent per km met een maximum van € 8 berekend op "
"basis van één dag per week."

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_extra_time_off
msgid "Extra Time Off"
msgstr "Extra verlof"

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_ambulatory_insurance_note
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_hospital_insurance_note
msgid ""
"For all insured persons, indicate the last name / first name / date of birth"
" / relationship / Indicate if disabled"
msgstr ""
"Vermeld voor alle verzekerde personen de achternaam / voornaam / "
"geboortedatum / verwantschap / Vermeld indien gehandicapt"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_fuel_card
msgid "Fuel Card"
msgstr "Brandstofkaart"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
msgid "Gross (Part Time)"
msgstr "Bruto (deeltijds)"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_group_insurance
msgid "Group Insurance"
msgstr "Groepsverzekering"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_fleet_vehicle_state__hide_in_offer
msgid "Hide In Offer"
msgstr "Verbergen in aanbod"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_job__l10n_be_custom_representation_fees_homeworking
msgid "Homeworking"
msgstr "Telewerken"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_hospital_insurance
msgid "Hospital Insurance"
msgstr "Hospitalisatieverzekering"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_hospital_insurance_note
msgid "Hospital insurance (info)"
msgstr "Hospitalisatieverzekering (info)"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__id_card
msgid "ID Card Copy"
msgstr "Kopie identiteitskaart"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_id_card
msgid "ID card copy (Both Sides)"
msgstr "Kopie identiteitskaart (beide kanten)"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__id_card_filename
msgid "Id Card Filename"
msgstr "Bestandsnaam identiteitskaart"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,help:l10n_be_hr_contract_salary.field_hr_job__l10n_be_contract_ip
msgid "If checked, the job position is eligible to Intellectual Property"
msgstr ""
"Indien aangevinkt, komt de functie in aanmerking voor Intellectuele Eigendom"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,help:l10n_be_hr_contract_salary.field_hr_job__l10n_be_contract_withholding_taxes_exemption
msgid ""
"If checked, the job position will grant a withholding taxes exemption to "
"eligible employees"
msgstr ""
"Indien aangevinkt, verleent de functie een vrijstelling van bronbelasting "
"aan werknemers die hiervoor in aanmerking komen."

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,help:l10n_be_hr_contract_salary.field_hr_job__l10n_be_custom_representation_fees
msgid ""
"If you check this field, you can select various advantages to set the threshold in the representation fees.\n"
" Amount above the representation fee threshold is called \"Representation fees without serious standard\" and can be prorated according to the employee working rate"
msgstr ""
"Als je dit veld aanvinkt, kan je verschillende voordelen selecteren om de "
"drempel voor de representatiekosten in te stellen. Bedragen boven de drempel"
" voor representatiekosten worden \"Representatiekosten zonder serieuze "
"norm\" genoemd en kunnen worden aangepast aan het werktarief van de "
"werknemer."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_thirteen_month
msgid ""
"If you meet conditions to benefit from 13th month, you will receive this "
"amount around December. This amount may be different based on your working "
"schedule and the number of worked days during the year."
msgstr ""
"Als je aan de voorwaarden voldoet om de 13de maand te ontvangen, ontvang je "
"dit bedrag rond december. Dit bedrag kan verschillen op basis van je "
"werkschema en het aantal gewerkte dagen gedurende het jaar."

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__image_1920_filename
msgid "Image 1920 Filename"
msgstr "Bestandsnaam afbeelding 1920"

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_extra_time_off
msgid ""
"In addition to your legal leaves, you can choose to have extra days off (up "
"to +15 days). The amount of annual time off (legal leaves) allocated to you "
"depends on your working hours from the previous year. For example, if you "
"worked full-time for 12 months under a Belgian contract, you are entitled to"
" 20 days off."
msgstr ""
"Naast je wettelijk verlof, kan je kiezen voor extra verlof (tot  +15 dagen)."
" Het aantal dagen jaarlijks verlof (wettelijk verlof) dat je wordt "
"toegekend, hangt af van je werkuren van het vorige jaar. Als je bijvoorbeeld"
" 12 maanden voltijds hebt gewerkt in het kader van een Belgisch "
"arbeidsovereenkomst, heb je recht op 20 verlofdagen."

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_insured_children
msgid "Insured Children (< 19 y/o)"
msgstr "Verzekerde kinderen (< 19 jaar)"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_insured_adults
msgid "Insured Children (>= 19 y/o)"
msgstr "Verzekerde kinderen (>= 19 jaar)"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_ambulatory_insured_spouse
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_insured_spouse
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_insured_spouse
msgid "Insured Spouse"
msgstr "Verzekerde echtgeno(o)t(e)"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_intellectual_property
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_job__l10n_be_contract_ip
msgid "Intellectual Property"
msgstr "Intellectuele eigendom"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_job__l10n_be_custom_representation_fees_internet
msgid "Internet"
msgstr "Internet"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__internet_invoice_filename
msgid "Internet Invoice Filename"
msgstr "Bestandsnaam internetfactuur"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_internet
msgid "Internet Subscription"
msgstr "Internetabonnement"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_internet_invoice
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__internet_invoice
msgid "Internet Subscription Invoice"
msgstr "Factuur internetabonnement"

#. module: l10n_be_hr_contract_salary
#: model:ir.actions.server,name:l10n_be_hr_contract_salary.action_hr_job_payroll_configuration
msgid "Job Configuration"
msgstr "Functieconfiguratie"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/models/hr_job.py:0
#: model:ir.model,name:l10n_be_hr_contract_salary.model_hr_job
msgid "Job Position"
msgstr "Functie"

#. module: l10n_be_hr_contract_salary
#: model:ir.ui.menu,name:l10n_be_hr_contract_salary.menu_hr_job_positions
msgid "Job Positions"
msgstr "Functies"

#. module: l10n_be_hr_contract_salary
#: model:ir.ui.menu,name:l10n_be_hr_contract_salary.menu_hr_job_configuration
msgid "Jobs"
msgstr "Functies"

#. module: l10n_be_hr_contract_salary
#: model:hr.job,name:l10n_be_hr_contract_salary.job_developer_belgium
msgid "Junior Developer BE"
msgstr "Junior Ontwikkelaar BE"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_km_home_work
msgid "Km Home/Work"
msgstr "Km Woonplaats/Werkplek"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__l10n_be_bicyle_cost
msgid "L10N Be Bicyle Cost"
msgstr "L10N Be Fietsvergoeding"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_marital_cohabitant
msgid "Legal Cohabitant"
msgstr "Wettelijk samenwonend"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_marital
msgid "Marital Status"
msgstr "Burgerlijke staat"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_marital_married
msgid "Married"
msgstr "Gehuwd"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_meal_vouchers
msgid "Meal Vouchers"
msgstr "Maaltijdcheques"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__mobile_invoice_filename
msgid "Mobile Invoice Filename"
msgstr "Bestandsnaam telefoonfactuur"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_mobile_invoice
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__mobile_invoice
msgid "Mobile Subscription Invoice"
msgstr "Factuur GSM abonnement"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/models/hr_contract.py:0
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_ambulatory_insurance_value_0
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_group_insurance_value_0
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_hospital_insurance_value_0
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_intellectual_property_value_0
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_mobile_value_0
msgid "No"
msgstr "Nee"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
msgid "No Category"
msgstr "Geen categorie"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_children
msgid "Number of Dependent Children"
msgstr "Aantal kinderen ten laste"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_disabled_children_number
msgid "Number of Disabled Children"
msgstr "Aantal gehandicapte kinderen"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
msgid "Oops"
msgstr "Oeps"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_dependent_people
msgid "Other Dependent People"
msgstr "Andere personen ten laste"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_res_config_settings__default_holidays
msgid "Paid Time Off"
msgstr "Betaald verlof"

#. module: l10n_be_hr_contract_salary
#: model:ir.model,name:l10n_be_hr_contract_salary.model_hr_payslip
msgid "Pay Slip"
msgstr "Loonstrook"

#. module: l10n_be_hr_contract_salary
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_contract_salary.hr_job_view_form
msgid "Payroll"
msgstr "Loonadministratie"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_lang
msgid "Payroll Language"
msgstr "Taal loonadministratie"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_juniors_dependent
msgid "People"
msgstr "Personen"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_job__l10n_be_custom_representation_fees_phone
msgid "Phone"
msgstr "Telefoon"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_mobile
msgid "Phone Subscription"
msgstr "Telefoonabonnement"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_transport_private_bike
msgid "Private Bike"
msgstr "Eigen fiets"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_transport_private_car
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_transport_private_car
msgid "Private Car"
msgstr "Eigen auto"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_transport_public
msgid "Public Transport"
msgstr "Openbaar vervoer"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_transport_public
msgid "Public Transportation"
msgstr "Openbaar vervoer"

#. module: l10n_be_hr_contract_salary
#: model:hr.department,name:l10n_be_hr_contract_salary.hr_department_rdbe
msgid "RD BE"
msgstr "RD BE"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_representation_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_contract_salary.hr_job_view_form
msgid "Representation Fees"
msgstr "Representatiekosten"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__sim_card
msgid "SIM Card Copy"
msgstr "SIM kaart kopie"

#. module: l10n_be_hr_contract_salary
#: model:ir.model,name:l10n_be_hr_contract_salary.model_hr_contract_salary_offer
msgid "Salary Package Offer"
msgstr "Aanbod salarispakket"

#. module: l10n_be_hr_contract_salary
#: model:ir.model,name:l10n_be_hr_contract_salary.model_hr_contract_salary_resume
msgid "Salary Package Resume"
msgstr "Salarispakket samenvatting"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,placeholder:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_spouse_fiscal_status
msgid "Select a Situation"
msgstr "Kies een situatie"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,placeholder:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_marital
msgid "Select a status"
msgstr "Kies een status"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_seniority
msgid "Seniority at Hiring"
msgstr "Anciënniteit bij aanwerving"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_other_senior_dependent
msgid "Seniors"
msgstr "Senioren"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__new_car
msgid "Show \"Company Car (To Order)\""
msgstr "Bekijk \"Bedrijfswagen (te bestellen)\""

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__show_new_car
msgid "Show New Car"
msgstr "Toon nieuwe voertuigen"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_sim_card
msgid "Sim Card Copy"
msgstr "SIM kaart kopie"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract__sim_card_filename
msgid "Sim Card Filename"
msgstr "Bestandsnaam SIM-kaart"

#. module: l10n_be_hr_contract_salary
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_contract_salary.salary_package_wishlist_sidebar
msgid "Simulation with Car to Order"
msgstr "Simulatie met te bestellen wagen"

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_intellectual_property
msgid ""
"Since Odoo invests massively in developing and writing intellectual property"
" content, legally we are able to pay part of our employees' salaries as "
"intellectual property income. This amount will be paid in cash every month "
"and is taxed less."
msgstr ""
"Omdat Odoo veel investeert in het ontwikkelen en schrijven van inhoud met "
"intellectuele eigendom, kunnen we wettelijk gezien een deel van het loon van"
" onze werknemers uitkeren als inkomsten uit intellectuele eigendom. Dit "
"bedrag wordt elke maand contact uitbetaald en is minder belast."

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_marital_single
msgid "Single"
msgstr "Ongehuwd"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
msgid ""
"Sorry, the selected car has been selected by someone else. Please refresh "
"and try again."
msgstr ""
"Sorry, de geselecteerde auto is geselecteerd door iemand anders. Vernieuw de"
" pagina en probeer het opnieuw."

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_spouse_birthdate
msgid "Spouse Birthdate"
msgstr "Geboortedatum partner"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_spouse_complete_name
msgid "Spouse Name and First Name"
msgstr "Naam en voornaam partner"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_spouse_fiscal_status
msgid "Spouse Professional Situation"
msgstr "Professionele situatie partner"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,help:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"Tweeletterige ISO-landcode. \n"
"Gebruik dit veld om snel te zoeken."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_group_insurance
msgid ""
"The chosen percentage of the salary will be sacrified into a group "
"insurance. Regarding the employer's budget, in addition to the amount "
"invested in group insurance, 8.86% of this amount is paid to the ONSS, and "
"an additional 4.4% is used in the management costs of the insurance product."
msgstr ""
"Het gekozen percentag van het loon wordt geïnvesteerd in een "
"groepsverzekering. Wat het werkgeversbudget betreft, wordt naast het bedrag "
"dat in de groepsverzekering wordt geïnvesteerd, 8,86% van dit bedrag aan de "
"RSZ betaald en nog eens 4,4% wordt gebruikt voor de beheerskosten van het "
"verzekeringsproduct."

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,help:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__new_car
msgid ""
"The employee will be able to choose a new car even if the maximum number of "
"used cars available is reached."
msgstr ""
"De werknemer zal een nieuwe auto kunnen kiezen, zelfs als het maximum aantal"
" beschikbare tweedehands auto's is bereikt."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_mobile
msgid ""
"This BASE subscription include unlimited Internet, calls and SMS in Belgium "
"+ Roaming trough Europe (International Communications are excluded)."
msgstr ""
"Dit BASE-abonnement omvat onbeperkt internet, bellen en sms'en in België + "
"roaming door Europa (internationale gesprekken zijn uitgesloten)."

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
msgid ""
"This contract is a full time credit time... No simulation can be done for "
"this type of contract as its wage is equal to 0."
msgstr ""
"Dit contract is een voltijds tijdskrediet... Er kan geen simulatie gedaan "
"worden voor dit type contract aangezien het loon gelijk is aan 0."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_representation_fees
msgid "This is a monthly net amount, taken into account in your payslip."
msgstr ""
"Dit is een maandelijks nettobedrag, dat in rekening wordt genomen in je "
"loonstrookje."

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_transport_train
msgid "Train Transport"
msgstr "Treinvervoer"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,fold_label:l10n_be_hr_contract_salary.l10n_be_transport_train
msgid "Train Transportation"
msgstr "Treinvervoer"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_transport_company_car
msgid "Transportation"
msgstr "Vervoer"

#. module: l10n_be_hr_contract_salary
#: model:ir.model,name:l10n_be_hr_contract_salary.model_fleet_vehicle_state
msgid "Vehicle Status"
msgstr "Voertuigstatus"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/models/hr_contract_salary_offer.py:0
msgid "We already have %s car(s) without driver(s) available"
msgstr "Er zijn reeds %s wagen(s) zonder bestuurder(s) beschikbaar"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_marital_widower
msgid "Widower"
msgstr "Weduwe/Weduwnaar"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.benefit,name:l10n_be_hr_contract_salary.l10n_be_transport_new_car
msgid "Wishlist Car"
msgstr "Autowenslijst"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__wishlist_car_warning
msgid "Wishlist Car Warning"
msgstr "Waarschuwing Verlanglijst Wagen"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_high_income
msgid "With High Income"
msgstr "Met hoog inkomen"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_high_pension
msgid "With High Pension"
msgstr "Met hoog pensioen"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_low_income
msgid "With Low Income"
msgstr "Met een laag inkomen"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_low_pension
msgid "With Low Pension"
msgstr "Met laag pensioen"

#. module: l10n_be_hr_contract_salary
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_contract_salary.hr_contract_salary_offer_view_search
msgid "With a Vehicle"
msgstr "Met een voertuig"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,field_description:l10n_be_hr_contract_salary.field_hr_job__l10n_be_contract_withholding_taxes_exemption
msgid "Withholding Taxes Exemption"
msgstr "Vrijstelling van bronbelasting"

#. module: l10n_be_hr_contract_salary
#: model:hr.contract.salary.personal.info.value,name:l10n_be_hr_contract_salary.hr_contract_salary_personal_info_without_income
msgid "Without Income"
msgstr "Zonder inkomen"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/models/hr_contract.py:0
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_ambulatory_insurance_value_1
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_hospital_insurance_value_1
#: model:hr.contract.salary.benefit.value,name:l10n_be_hr_contract_salary.l10n_be_intellectual_property_value_1
msgid "Yes"
msgstr "Ja"

#. module: l10n_be_hr_contract_salary
#: model:ir.model.fields,help:l10n_be_hr_contract_salary.field_hr_contract_salary_offer__additional_car_ids
msgid ""
"You can add used cars to this field, they'll be added to the list for "
"simulation purposes."
msgstr ""
"Voeg gebruikte auto's toe aan dit veld, ze worden vervolgens toegevoegd aan "
"de lijst voor simulatiedoeleinden."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_double_holiday
msgid ""
"You'll receive this amount around June. This amount will depend on the "
"number of worked days during the previous year."
msgstr ""
"Je ontvangt dit bedrag rond juni. Dit bedrag is afhankelijk van het aantal "
"gewerkte dagen in het voorgaande jaar."

#. module: l10n_be_hr_contract_salary
#: model_terms:hr.contract.salary.benefit,description:l10n_be_hr_contract_salary.l10n_be_eco_voucher
msgid ""
"You'll receive this amount in the form of Eco Vouchers around June. The "
"exact amount is based on real worked day during the period between the first"
" of June of last year and the 31th of May of this year."
msgstr ""
"Je ontvangt dit bedrag rond juni in de vorm van ecocheques. Het exacte "
"bedrag is gebaseerd op de werkelijk gewerkte dag in de periode tussen 1 juni"
" vorig jaar en 31 mei dit jaar."

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
msgid ""
"Your monthly gross wage is below the minimum legal amount %(min_gross)s €"
msgstr ""
"Je maandelijkse brutoloon is lager dan het wettelijk minimumbedrag van € "
"%(min_gross)s "

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
msgid ""
"Your monthly gross wage will be below the minimum legal amount %(min_gross)s"
" €"
msgstr ""
"Je maandelijkse brutoloon zal lager zijn dan het wettelijk minimumbedrag van"
" € %(min_gross)s "

#. module: l10n_be_hr_contract_salary
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_contract_salary.salary_package_wishlist_sidebar
msgid "close"
msgstr "sluiten"

#. module: l10n_be_hr_contract_salary
#. odoo-python
#: code:addons/l10n_be_hr_contract_salary/controllers/main.py:0
msgid "• Available in %s"
msgstr "• Beschikbaar in %s"
