# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON> <j<PERSON><PERSON><EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Will Sensors, 2025
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Latvian (https://app.transifex.com/odoo/teams/41243/lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : 2);\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid ""
"\n"
");\n"
"\n"
"export class ProjectTaskFormController extends FormControllerWithHTMLExpander {\n"
"    setup() {\n"
"        super.setup();\n"
"        this.notifications = useService(\"notification\");\n"
"    }\n"
"\n"
"    /**\n"
"     * @override\n"
"     */\n"
"    getStaticActionMenuItems() {\n"
"        return {\n"
"            ...super.getStaticActionMenuItems(),\n"
"            openHistoryDialog: {\n"
"                sequence: 50,\n"
"                icon: \"fa fa-history\",\n"
"                description: _t(\"Version History\"),\n"
"                callback: () => this.openHistoryDialog(),\n"
"            },\n"
"        };\n"
"    }\n"
"\n"
"    get deleteConfirmationDialogProps() {\n"
"        const deleteConfirmationDialogProps = super.deleteConfirmationDialogProps;\n"
"        if (!this.model.root.data.subtask_count) {\n"
"            return deleteConfirmationDialogProps;\n"
"        }\n"
"        return {\n"
"            ...deleteConfirmationDialogProps,\n"
"            body: subTaskDeleteConfirmationMessage,\n"
"        }\n"
"    }\n"
"\n"
"    async openHistoryDialog() {\n"
"        const record = this.model.root;\n"
"        const versionedFieldName = 'description';\n"
"        const historyMetadata = record.data[\"html_field_history_metadata\"]?.[versionedFieldName];\n"
"        if (!historyMetadata) {\n"
"            this.notifications.add(\n"
"                escape(_t(\n"
"                    \"The task description lacks any past content that could be restored at the moment.\"\n"
"                ))\n"
"            );\n"
"            return;\n"
"        }\n"
"\n"
"        this.dialogService.add(\n"
"            HistoryDialog,\n"
"            {\n"
"                title: _t(\"Task Description History\"),\n"
"                noContentHelper: markup(\n"
"                    "
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
msgid "# of Tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "#{task.stage_id.name}"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/widget/subtask_counter.js:0
msgid "%(closedCount)s sub-tasks closed out of %(totalCount)s"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(closed_task_count)s / %(task_count)s"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/widget/subtask_counter.js:0
msgid "%(count1)s/%(count2)s"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s Dashboard"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Burndown Chart"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Milestones"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Rating"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Tasks Analysis"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid "%(partner_name)s's Tasks"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project_stage.py:0
#: code:addons/project/models/project_task.py:0
#: code:addons/project/models/project_task_type.py:0
msgid "%s (copy)"
msgstr "%s (kopija)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "%s closed tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/depend_on_ids_one2many/depend_on_ids_list_renderer.xml:0
msgid "(other) tasks to which you do not have access."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid ""
",\n"
"    <br/><br/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this "
"task won't be notified of the note you are logging unless you specifically "
"tag them)</i>. Use @ <b>mentions</b> to ping a colleague or # "
"<b>mentions</b> to reach an entire team."
msgstr ""

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/><br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the <strong t-out=\"object.name or ''\">Planning and budget</strong> task\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 13px;text-align:center;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br/>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey has been sent because your task has been moved to the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br/>Best regards,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/><br/>\n"
"    Thank you for contacting us. We appreciate your interest in our products/services.<br/>\n"
"    Our team is currently reviewing your inquiry and will respond to your email as soon as possible.<br/>\n"
"    If you have any further questions or concerns in the meantime, please do not hesitate to let us know. We are here to help.<br/><br/>\n"
"    Thank you for your patience.<br/>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/> Atpakaļ pie rediģēšanas režīma"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "<i title=\"Private Task\" class=\"fa fa-lock\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Assignees</small>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Customer</small>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Done</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Tasks</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<span class=\"o_stat_text\">Blocked Tasks</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Parent Task</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Sub-tasks</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"text-muted o_row ps-1 pb-3\">Send a rating request:</span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">A rating request will be sent as soon as the task reaches a stage on which a Rating Email Template is defined.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Rating requests will be sent as long as the task remains in a stage on which a Rating Email Template is defined.</span>\n"
"                                                </span>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Atskaites</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>Skatīt</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>Termiņš:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>Projekts:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python vārdnīca, kas tiks novērtēta, lai nodrošinātu noklusējuma vērtības, "
"izveidojot jaunus ierakstus šiem aizstājvārdiem."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "A new task has been created and is not part of any project."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "A new task has been created in the \"%(project_name)s\" project."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr ""

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_private_task_has_no_parent
msgid "A private task cannot have a parent."
msgstr ""

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_recurring_task_has_no_parent
msgid "A subtask cannot be recurrent."
msgstr ""

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr ""

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "Pieņemt e-pastus no"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__access_mode
msgid "Access Mode"
msgstr "Piekļuves režīms"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "Piekļuves brīdinājums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "Nepieciešama darbība"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "Aktīvs"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "Aktivitātes"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitātes izņēmuma noformējums"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_project_task_plan
#: model:ir.ui.menu,name:project.mail_activity_plan_menu_config_project
msgid "Activity Plans"
msgstr "Aktivitāšu plāni"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "Aktivitātes stāvoklis"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitātes tipa ikona"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "Aktivitāšu tipi"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Progress Report\", \"Stand-up Meeting\", ...)"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Add Milestone"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.js:0
msgid "Add Sub-tasks"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Add a sub-task"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Add details about this task..."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "Pievienot papildus saturu, ko attēlot e-pastā"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_3
msgid ""
"Add project-specific property fields on tasks to customize your project "
"management process."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Add your task once it is ready."
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "Administrators"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Agile Scrum"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "Alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "Alias Contact Security"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain_id
msgid "Alias Domain"
msgstr "Aizstājvārda domēns"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias Domain Name"
msgstr "Aizstājvārda domēna nosaukums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_full_name
msgid "Alias Email"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "Alias Name"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_status
msgid "Alias Status"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "Aliased Model"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "All"
msgstr "Visi"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management_all_tasks
msgid "All Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allocated_hours
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__allocated_hours
msgid "Allocated Time"
msgstr "Piešķirtais laiks"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_allocated_hours_template
msgid "Allocated Time:"
msgstr "Piešķirtais laiks:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Analytic"
msgstr "Analītika"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__auto_account_id
msgid "Analytic Account"
msgstr "Analītiskais Konts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "Analītika"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__share_link
msgid "Anyone with this link can access the project in read mode."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__03_approved
msgid "Approved"
msgstr "Apstiprināts"

#. module: project
#: model:project.tags,name:project.project_tags_07
msgid "Architecture"
msgstr "Arhitektūra"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
msgid "Archive Account"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
msgid "Archive Accounts"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Archived"
msgstr "Arhivēts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: code:addons/project/static/src/components/subtask_one2many_field/subtask_list_renderer.js:0
msgid "Are you sure you want to delete this record?"
msgstr "Vai tiešām vēlaties dzēst šo ierakstu?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "Are you sure you want to restore this version ?"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Assembling"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Assign a responsible to your task"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Assigned"
msgstr "Piešķirts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "Piešķirts"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.open_view_blocked_by_list_view
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "Assignees"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignement Date"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "Uzticēšanas datums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "Piešķiršanas datums"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Attach all documents or links to the task directly, to have all research "
"information centralized."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "Pielikumu skaits"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Attachments"
msgstr "Pielikumi"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "Autors"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_state
msgid "Automatic Kanban Status"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_state
msgid ""
"Automatically modify the state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the state to 'Approved' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'Changes Requested' (orange bullet).\n"
msgstr ""

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Automatically send an email to customers when a task reaches a specific "
"stage in a project by setting this template on that stage"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Avatar"
msgstr "Ikona"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
msgid "Average Rating"
msgstr "Vidējais vērtējums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
msgid "Average Rating (1-5)"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Backlog"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "Bilance"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Billed"
msgstr "Piestādīts rēķins"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__dependent_ids
msgid "Block"
msgstr "Bloķēt"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocked"
msgstr "Bloķēts"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked Tasks"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocking"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Brainstorm"
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Burndown Chart"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_canceled
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
msgid "Cancelled"
msgstr "Atcelts"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__02_changes_requested
#: model:mail.message.subtype,description:project.mt_task_changes_requested
#: model:mail.message.subtype,name:project.mt_project_task_changes_requested
#: model:mail.message.subtype,name:project.mt_task_changes_requested
msgid "Changes Requested"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the "
"name of a customer, of a product, of a team, of a construction site, "
"etc.</i>"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Choose one of the following access modes for your collaborators:"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Client Review"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Close the sub-tasks list"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__closed_depend_on_count
msgid "Closed Depending on Tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed On"
msgstr "Slēgts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_report_project_task_user__is_closed
msgid "Closed state"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__is_closed__closed
msgid "Closed tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__is_closed
msgid "Closing Stage"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__partner_id
msgid "Collaborator"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__collaborator_ids
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Collaborators"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
msgid "Color"
msgstr "Krāsa"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Communication history"
msgstr "Komunikācijas vēsture"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_project_stage__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Company"
msgstr "Uzņēmums"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurācijas uzstādījumi"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "Konfigurācija"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "Apstiprināt"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Confirmation"
msgstr "Apstiprināšana"

#. module: project
#: model_terms:web_tour.tour,rainbow_man_message:project.project_tour
msgid "Congratulations, you are now a master of project management."
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_06
msgid "Construction"
msgstr "Construction"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Consulting"
msgstr "Consulting"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "Kontakts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid "Convert Task"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.actions.server,name:project.action_server_convert_to_subtask
msgid "Convert to Task/Sub-Task"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Copywriting"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Costs"
msgstr "Izmaksas"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "Attēls"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr ""
"Izveidojiet <b>aktivitātess</b>, lai iestatīt Jūsu uzdevumus vai ieplānot "
"tikšanās."

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "Izveidošanas datums"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
msgid "Create a Task Activity Plan"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Create a new sub-task"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr ""

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "Izveidots"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "Izveidoja"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "Izveidots"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Creation Date"
msgstr "Izveidošanas datums"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "Uzdevuma pašreizējais posms"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr ""
"Pašreiz pieejams visiem, kas apskata šo dokumentu, piemiedziet, lai "
"ierobežot tikai darbiniekiem."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Pielāgots atlēkušais ziņojums"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Customer"
msgstr "Klients"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Customer Email"
msgstr "Klienta e-pasts"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Customer Feedback"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "Klienta portāla URL"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Customers will be added to the followers of their project and tasks."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "Ikdienas"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model:ir.embedded.actions,name:project.project_embedded_action_project_updates
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Dashboard"
msgstr "Kopskats"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Date"
msgstr "Datums"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the state of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage/state to another."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "Dienas"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Deadline"
msgstr "Termiņš"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "Cienījamais(-ā)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "Noklusējuma vērtības"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_project_stage_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Delete"
msgstr "Izdzēst"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Delete Milestone"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid "Delete Project Stage"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Delete Stage"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Delivered"
msgstr "Piegādāts"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Dependent Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_count
msgid "Depending on Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model:ir.model.fields,field_description:project.field_report_project_task_user__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "Apraksts"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.tags,name:project.project_tags_08
msgid "Design"
msgstr "Design"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Development"
msgstr "Attīstība"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "Apkopojums"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Digital Marketing"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "Atmest"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "Nosaukums"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Display the sub-task in your pipeline"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/digest_digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "Nav piekļuves, izlaidiet šos datus lietotāja kopsavilkuma e-pastam"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__done
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_update__status__done
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_done
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
msgid "Done"
msgstr "Gatavs"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Draft"
msgstr "Melnraksts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Duplicate"
msgstr "Dublēt"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid ""
"Each user should have at least one personal stage. Create a new stage to "
"which the tasks can be transferred after the selected ones are deleted."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__edit
msgid "Edit"
msgstr "Labot"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__edit_limited
msgid "Edit with limited access"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"Edit with limited access: collaborators can view and edit tasks they follow "
"in the Kanban view."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"Edit: collaborators can view and edit all tasks in the Kanban view. "
"Additionally, they can choose which tasks they want to follow."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Editing"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_email
msgid "Email Alias"
msgstr "Email Alias"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "E-pasta veidne"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "E-pasta CC"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "E-pasta domēns piem., 'piemers.lv' e-pastā '<EMAIL>'"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Employees Only"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "Beigu datums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "Beigu Datums"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Expected"
msgstr "Sagaidāms"

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "Derīguma beigu datums"

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "Papildus informācija"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "Favorīts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Favorite Projects"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Final Document"
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_11
msgid "Finance"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/chatter/portal_chatter_patch.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.xml:0
msgid "Follow"
msgstr "Sekot"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow and comments tasks of your projects"
msgstr "Sekojiet un komentējiet uzdevumus Jūsu projektiem"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow the evolution of your projects"
msgstr "Sekojiet Jūsu projektu attīstībai"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Followed"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "Sekotāji"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "Sekotāji (Partneri)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Fonts awesome ikona, piem., fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "Uz visiem laikiem"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Frequency"
msgstr "Biežums"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Future"
msgstr "Nākotnē"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "Nākotnes aktivitātes"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback and evaluate the performance of your employees"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Give the sub-task a <b>name</b>"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Grant Portal Access"
msgstr "Piešķirt portāla piekļuvi"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
" Employees automatically get access to the tasks they are assigned to."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Grant portal users access to your project by adding them as followers (the "
"tasks of the project are not included). To grant access to tasks to a portal"
" user, add them as followers for these tasks."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Group By"
msgstr "Grupēt pēc"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Handoff"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "Ir ziņojums"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid "Hello"
msgstr "Sveicināti"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "Hide closed tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hide the sub-task in your pipeline"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "Augsts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "Vēsture"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__html_field_history
msgid "History data"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__html_field_history_metadata
msgid "History metadata"
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_13
msgid "Home"
msgstr "Sākums"

#. module: project
#: model:account.analytic.account,name:project.analytic_construction
#: model:project.project,name:project.project_home_construction
msgid "Home Construction"
msgstr "Mājas būvniecība"

#. module: project
#: model:project.project,name:project.project_project_4
msgid "Home Make Over"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "Stundas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Sākotnējā ieraksta ID, kurā ir aizstājvārds (piemērs: projekts, kurā ir "
"uzdevuma izveides aizstājvārds)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona izņēmuma aktivitātes identificēšanai."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Ideas"
msgstr "Idejas"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ja atzīmēts, jums jāpievērš uzmanība jauniem ziņojumiem."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Ja atzīmēts, daži ziņojumi satur piegādes kļūdu."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__01_in_progress
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,name:project.project_stage_1
msgid "In Progress"
msgstr "Tiek izpildīts"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "In development"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
msgid "Inbox"
msgstr "Iesūtne"

#. module: project
#: model:project.tags,name:project.project_tags_09
msgid "Interior"
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "Iekšējais"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Internal Note"
msgstr "Iekšējā piezīme"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Internal notes are only displayed to internal users."
msgstr "Iekšējās piezīmes ir redzamas tikai iekšējiem lietotājiem."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Invalid operator: %s"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Invalid value: %s"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users (private)"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Invoiced"
msgstr "Rēķins izrakstīts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Is Closed (Burn-up Chart)"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
#: model:ir.model.fields,field_description:project.field_report_project_task_user__message_is_follower
msgid "Is Follower"
msgstr "Ir sekotājs"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
msgid "Is toggle mode"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "Pieteikuma versija"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__duration_tracking
#: model:ir.model.fields,help:project.field_project_task__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_recurring_tasks_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last 30 Days"
msgstr "Pēdējās 30 dienas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last 365 Days"
msgstr "Pēdējās 365 dienas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Last 7 Days"
msgstr "Pēdējās 7 dienas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "Pēdējais mēnesis"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
msgid "Last Rating (1-5)"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last Stage Update"
msgstr "Last Stage Update"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "Pēdējoreiz atjaunots"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "Pēdējo reizi atjaunoja"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "Pēdējās izmaiņas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "Pēdējās aktivitātes"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Late Milestones"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
msgid "Later"
msgstr "Vēlāk"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Leave a comment"
msgstr "Atstājiet komentāru"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>project</b>."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>stage</b>."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>task</b>."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your second <b>stage</b>."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's start working on your task."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Live"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Logo Design"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Look for the"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "Zems"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Manufacturing"
msgstr "Ražošana"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
msgid "Mark as done"
msgstr "Atzīmēt kā pabreigtu"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Mark as incomplete"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Mark as reached"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Mark the task as <b>Cancelled</b>"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Material Sourcing"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_15
msgid "Meeting"
msgstr "Tikšanās"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "Izvēlne"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "Ziņojums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "Ziņojuma piegādes kļūda"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "Ziņojumi"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Milestone"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Milestones"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Mixing"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "Mēneši"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Manas aktivitātes izpildes termiņš"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "My Deadline"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "Mani favorīti"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Projects"
msgstr "Mani Projekti"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_my_task
#: model:ir.ui.menu,name:project.menu_project_management_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "My Tasks"
msgstr "Mani uzdevumi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Name"
msgstr "Nosaukums"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr ""

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_list.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
msgid "New"
msgstr "Jauns"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
msgid "New Milestone"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Orders"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_project_calendar/project_project_calendar_controller.js:0
msgid "New Project"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Projects"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Request"
msgstr "Jauns pieprasījums"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
msgid "New Task"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Newest"
msgstr "Jaunākais"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
msgid "Next"
msgstr "Nākamais"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Next Activity"
msgstr "Nākošā aktivitāte"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nākamās darbības kalendāra pasākums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nākamās aktivitātes beigu termiņš"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "Nākamās darbības kopsavilkums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "Nākamās darbības veids"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Project"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "No Subject"
msgstr "Nav temata"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "Vēl nav datu!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_recurring_tasks_action
#: model_terms:ir.actions.act_window,help:project.project_task_action_from_partner
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "None"
msgstr "Nav"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Not Implemented."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "Piezīme"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "Darbību skaits"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "Kļūdu skaits"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Ziņojumu, kuriem nepieciešama darbība, skaits"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Ziņojumu, kas satur piegādes kļūdu, skaits"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_10
msgid "Office"
msgstr ""

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Old Completed Sprint"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Only jpeg, png, bmp and tiff images are allowed as attachments."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr ""
"Oops! Kaut kas nogāja greizi. Mēģiniet pārlādēt lappusi un pierakstīties."

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Open Tasks"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Open sub-tasks notebook section"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__is_closed__open
msgid "Open tasks"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Operation not supported"
msgstr "Operācija netiek atbalstīta"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Organize priorities amongst orders using the"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_from_partner
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Others"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Overdue"
msgstr "Nokavēts"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "Nokavētie uzdevumi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Page Ideas"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "Virsmodelis"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Virsieraksta sarakstes ID"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
msgid "Parent Task"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid ""
"Partner company cannot be different from its assigned projects' company"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid "Partner company cannot be different from its assigned tasks' company"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
msgid "Partner's Tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"People invited to collaborate on the project will have portal access rights."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks they are following, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "Apmierinātu novērtējumu procents"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Personal Stage"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Planned Date"
msgstr "Plānotais datums"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Podcast and Video Production"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Veids, kādā notiek ziņojumu ievietošana dokumentā:\n"
"- visi: ikviens var izlikt\n"
"- partneri: tikai autentificēti partneri\n"
"- sekotāji: tikai saistītā dokumenta sekotāji vai sekojošo kanālu dalībnieki\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "Portāla pieejas URL"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
msgid "Previous"
msgstr "Iepriekšējais"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Prioritize your tasks by marking important ones using the"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#: model:project.tags,name:project.project_tags_16
msgid "Priority"
msgstr "Prioritāte"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.js:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_model.js:0
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Private"
msgstr "Privāts"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Private Tasks"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"Private tasks cannot be converted into sub-tasks. Please set a project on "
"the task to gain access to this feature."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Profitability"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "Progress"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "Project"
msgstr "Projekti"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__account_id
msgid "Project Account"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "Projektu Vadītājs"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "Projekta Nosaukums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_share_collaborator_wizard
msgid "Project Sharing Collaborator Wizard"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_recurring_tasks_action
msgid "Project Sharing Recurrence"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_project_stage_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Task Stage Delete Wizard"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "Projekta uzdevumi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "Project Title"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "Projekta apraksts"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
msgid "Project shared with your collaborators."
msgstr ""

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "Projekta uzdevumi"

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "Projekts: Projekts pabeigts"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr ""

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
msgid "Project: Send rating"
msgstr ""

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Projects"
msgstr "Projekti"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Properties"
msgstr "Īpašības"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Public Link"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Published"
msgstr "Publicēts"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Published on"
msgstr "Publicēts"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Publishing"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "Reizi ceturksnī"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Quickly check the status of tasks for approvals or change requests and "
"identify those on hold until dependencies are resolved with the hourglass "
"icon."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "Vērtējums"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_pivot
msgid "Rating (1-5)"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "Vērtējuma Vidējais teksts"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "Vērtējuma Pēdējās atsauksmes"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "Vērtējuma Pēdējais attēls"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "Vērtējuma Pēdējā vērtība"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "Apmierinātības vērtējums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "Vērtējuma Teksts"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "Reitingu skaits"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_milestone__rating_ids
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model:ir.model.fields,field_description:project.field_project_update__rating_ids
msgid "Ratings"
msgstr "Reitingi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__read
msgid "Read"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Read: collaborators can view tasks but cannot edit them."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_share_collaborator_wizard__access_mode
msgid ""
"Read: collaborators can view tasks but cannot edit them.\n"
"Edit with limited access: collaborators can view and edit tasks they follow in the Kanban view.\n"
"Edit: collaborators can view and edit all tasks in the Kanban view. Additionally, they can choose which tasks they want to follow."
msgstr ""

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "Saņēmēji"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Ierakstīt sarakstes ID"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Recording"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "Atkārtošana"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "Atkārtojas"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
msgid "Recurrent tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurring Tasks"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Refused"
msgstr "Atteikts"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "Saistītais dokuments"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "Saistītā dokumenta ID"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "Saistītā dokumenta modelis"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
msgid "Remove Collaborator"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.xml:0
msgid "Rename"
msgstr "Pārdēvēt"

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "Atkārtot katru"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
msgid "Repeat Unit"
msgstr ""

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "Atskaites"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Research"
msgstr ""

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Research Project"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Researching"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Resources Allocation"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "Atbildīgie lietotāji"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "Restore"
msgstr "Atjaunot"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid ""
"Restoring will replace the current content with the selected version. Any "
"unsaved changes will be lost."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Revenues"
msgstr "Ieņēmumi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.xml:0
msgid "SAVE"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Īsziņas piegādes kļūda"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
msgid "Save the task to be able to drag images in description"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
msgid "Save the task to be able to paste images in description"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Schedule your activity once it is ready."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Script"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "Meklēt projektu"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Assignees"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Customer"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Milestone"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Priority"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Project"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Stages"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Status"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search%(left)s Tasks%(right)s"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "Drošības atslēgkods"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Select an assignee from the menu"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Send"
msgstr "Nosūtīt"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "Nosūtīt e-pastu"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__send_invitation
msgid "Send Invitation"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
msgid "Set Status"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr ""

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_priority_switch_field/project_task_priority_switch_field.js:0
msgid "Set priority as %s"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
msgid "Set state as..."
msgstr ""

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "Uzstādījumi"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Share Project"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share Task"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__show_display_in_project
msgid "Show Display In Project"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr ""
"Rādīt visus ierakstus, kuriem nākamais darbības datums ir pirms šodienas"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "Show closed tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_12
msgid "Social"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Software Development"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
msgid ""
"Some of the selected analytic accounts are associated with a project:\n"
"%(accountList)s\n"
"\n"
"Archiving these accounts will remove the option to log timesheets for their respective projects.\n"
"\n"
"Are you sure you want to proceed?"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Sorry. You can't set a task as its parent task."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Sort your tasks by sprint using milestones, tags, or a dedicated property. "
"At the end of each sprint, just pick the remaining tasks in your list and "
"move them all at once to the next sprint by editing the milestone, tag, or "
"property."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Specifications"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint Backlog"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint Complete"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint in Progress"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Stage"
msgstr "Stadija"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Stage (Burndown Chart)"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "Stāvoklis mainīts"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "Stāvoklis mainīts"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Stage: %s"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Starred Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "Sākuma datums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__state
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__state
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
msgid "State"
msgstr "Stāvoklis"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_stage_state_selection/project_task_stage_with_state_selection.js:0
msgid "State readonly"
msgstr ""

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_status_with_color_selection/project_status_with_color_selection_field.xml:0
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Status"
msgstr "Statuss"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Status Update - %(date)s"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statuss balstīts uz darbībām\n"
"Kavēts: termiņš jauy ir pagājis\n"
"Šodien: darbības datums ir šodien\n"
"Plānots: nākotnes aktivitātes."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__duration_tracking
#: model:ir.model.fields,field_description:project.field_project_task__duration_tracking
msgid "Status time"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_allocated_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "Kopsavilkums"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "T-shirt Printing"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tags"
msgstr "Iezīmes"

#. module: project
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task"
msgstr "Uzdevums"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_approved
#: model:mail.message.subtype,name:project.mt_task_approved
msgid "Task Approved"
msgstr "Uzdevums apstiprināts"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_canceled
msgid "Task Canceled"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_task_canceled
msgid "Task Cancelled"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Task Converted from To-Do"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_done
#: model:mail.message.subtype,name:project.mt_task_done
msgid "Task Done"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_in_progress
#: model:mail.message.subtype,name:project.mt_project_task_in_progress
#: model:mail.message.subtype,name:project.mt_task_in_progress
msgid "Task In Progress"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr ""

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr ""

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "Uzdevuma posms"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr ""

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"Task Transferred from Project %(source_project)s to %(destination_project)s"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_waiting
#: model:mail.message.subtype,name:project.mt_project_task_waiting
#: model:mail.message.subtype,name:project.mt_task_waiting
msgid "Task Waiting"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_approved
msgid "Task approved"
msgstr "Uzdevums apstiprināts"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_canceled
msgid "Task cancelled"
msgstr ""

#. module: project
#: model:mail.message.subtype,description:project.mt_task_done
msgid "Task done"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task: Rating Request"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.embedded.actions,name:project.project_embedded_action_all_tasks_dashboard
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.ui.menu,name:project.menu_project_management
#: model:project.project,label_tasks:project.project_home_construction
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model:project.project,label_tasks:project.project_project_4
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tasks"
msgstr "Uzdevumi"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "Uzdevumu analīze"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "Uzdevumu posmi"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
msgid "Tasks in Recurrence"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Tests"
msgstr "Tests"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
msgid "The Burndown Chart must be grouped by Date"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
msgid "The end date should be in the future"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
msgid "The interval should be greater than 0"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"The project and the associated partner must be linked to the same company."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"The project's company cannot be changed if its analytic account has analytic"
" lines or if more than one project is linked to it."
msgstr ""

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
msgid ""
"The report should be grouped either by \"Stage\" to represent a Burndown "
"Chart or by \"Is Closed\" to represent a Burn-up chart. Without one of these"
" groupings applied, the report will not provide relevant information."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "The search does not support operator %(operator)s or value %(value)s."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"The task and the associated partner must be linked to the same company."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to "
"'%(visibility)s' in order to make it accessible by the recipient(s)."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "The task description was empty at the time."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
msgid ""
"The view must be grouped by date and by Stage - Burndown chart or Is Closed "
"- Burnup chart"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
msgid "There are no comments for now."
msgstr "Pašreiz šeit nav komentāru."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "Šeit nav projektu."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "Šeit nav uzdevumu."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "There is nothing to report."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"They can edit shared project tasks and view specific documents in read mode "
"on your website. This includes leads/opportunities, quotations/sales orders,"
" purchase orders, invoices and bills, timesheets, and tickets."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
msgid "This Month"
msgstr "Šis mēnesis"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "This Week"
msgstr "Šī nedēļa"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
msgid ""
"This analytic account is associated with the following projects:\n"
"%(projectList)s\n"
"\n"
"Archiving the account will remove the option to log timesheets for these projects.\n"
"\n"
"Are you sure you want to proceed?"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
msgid ""
"This follower is currently a project collaborator. Removing them will revoke"
" their portal access to the project. Are you sure you want to proceed?"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid ""
"This is a preview of how the project will look when it's shared with "
"customers and they have editing access."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is associated with %(project_company)s, whereas the selected "
"stage belongs to %(stage_company)s. There are a couple of options to "
"consider: either remove the company designation from the project or from the"
" stage. Alternatively, you can update the company information for these "
"records to align them under the same company."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is currently restricted to \"Invited internal users\". The "
"project's visibility will be changed to \"invited portal users and all "
"internal users (public)\" in order to make it accessible to the recipients."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is not associated with any company, while the stage is "
"associated with %s. There are a couple of options to consider: either change"
" the project's company to align with the stage's company or remove the "
"company designation from the stage"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "This task has sub-tasks, so it can't be private."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
msgid "This task is blocked by another unfinished task"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/depend_on_ids_one2many/depend_on_ids_list_renderer.xml:0
msgid "This task is currently blocked by"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr ""

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr ""

#. module: project
#: model:digest.tip,name:project.digest_tip_project_3
#: model_terms:digest.tip,tip_description:project.digest_tip_project_3
msgid "Tip: Project-Specific Fields"
msgstr ""

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Use task states to keep track of your tasks' progression"
msgstr ""

#. module: project
#: model:digest.tip,name:project.digest_tip_project_2
#: model_terms:digest.tip,tip_description:project.digest_tip_project_2
msgid "Tip: Your Own Personal Kanban"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
msgid "Title"
msgstr "Nosaukums"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "To Bill"
msgstr ""

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "Veicamais darbs"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "To Invoice"
msgstr "Rēķini izrakstīšanai"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "To Print"
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid ""
"To transform a task into a sub-task, select a parent task. Alternatively, "
"leave the parent task field blank to convert a sub-task into a standalone "
"task."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Today"
msgstr "Šodien"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "Šodienas aktivitātes"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total"
msgstr "Kopā"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total Costs"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total Revenues"
msgstr "Kopējie ieņēmumi"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Track major progress points that must be reached to achieve success."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid ""
"Track project costs, revenues, and margin by setting the analytic account "
"associated with the project on relevant documents."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Two tasks cannot depend on each other."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Reģistrētās izņēmuma aktivitātes tips."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
msgid "Unarchive Projects"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid "Unarchive Tasks"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Unassigned"
msgstr "Nepiešķirts"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/chatter/portal_chatter_patch.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.xml:0
msgid "Unfollow"
msgstr "Atsekot"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Unknown Analytic Account"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "Neizlasīti ziņojumi"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
msgid "Until"
msgstr "Līdz"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr ""

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "Izmantot vērtējumu projektā"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "Izmanto uzdevumus kā"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Use This For My Project"
msgstr ""

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_2
msgid ""
"Use personal stages to organize your tasks and create your own workflow."
msgstr ""

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Use the"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"main changes about this task."
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""

#. module: project
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "Lietotājs"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "Skatīt"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "View Task"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "View Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "Redzamība"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Visible"
msgstr "Redzams"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__04_waiting_normal
msgid "Waiting"
msgstr "Gaida"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "Tīmekļa lapas ziņojumi"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Website Redesign"
msgstr ""

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "Tīmekļa lapas komunikācijas vēsture"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "Reizi nedēļā"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "Nedēļas"

#. module: project
#: model:project.tags,name:project.project_tags_14
msgid "Work"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr ""

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the projects contained in these stages as"
" well?"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Write a message..."
msgstr "Rakstīt ziņojumu..."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Writing"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "Ik gadu"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "Gadi"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
msgid ""
"You are not able to switch the company of this stage to %(company_name)s "
"since it currently includes projects associated with "
"%(project_company_name)s. Please ensure that this stage exclusively consists"
" of projects linked to %(company_name)s."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "You can change the sub-task state here!"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You can only set a personal stage on a private task."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "You can open sub-tasks from the kanban card!"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You can either archive them or"
" first delete all of their projects."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You should first delete all of"
" their projects."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You cannot read the following fields on tasks: %(field_list)s"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You cannot write on the following fields on tasks: %(field_list)s"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You have been assigned to %s"
msgstr "Jums ir piešķirts %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "Jums ir piešķirts"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You have been invited to follow %s"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid "You have been invited to follow Task Document :"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"You have full control and can revoke portal access anytime. Are you ready to"
" proceed?"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"You have unsaved changes - no worries! Odoo will automatically save it as "
"you navigate.<br/> You can discard these changes from here or manually save "
"your task.<br/>Let's save it manually."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "You must be"
msgstr "Jums ir jābūt"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Your managers decide which feedback is accepted"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "alias"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "and"
msgstr "and"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "avatar"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "button."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
msgid "comments"
msgstr "komentāri"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "e.g. Office Party"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "e.g. Tasks"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_tree
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "e.g. To Do"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. mycompany.com"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "icon to organize your daily activities."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"icon to see tasks waiting on other ones. Once a task is marked as complete "
"or cancelled, all of its dependencies will be unblocked."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "icon."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "logged in"
msgstr "pieteicies"

#. module: project
#: model:ir.actions.server,name:project.action_server_view_my_task
msgid "menu view My Tasks"
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "on a periodic basis"
msgstr ""

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"state to indicate a request for changes or a need for discussion on a task."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"state to inform your colleagues that a task is approved for the next stage."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "state to mark the task as cancelled."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "state to mark the task as complete."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "state.message"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_name_with_subtask_count_char_field/project_task_name_with_subtask_count_char_field.xml:0
msgid "sub-tasks)"
msgstr ""

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "task"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr ""

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "to post a comment."
msgstr "publicēt komentāru."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "to signalize what is the current status of your Idea."
msgstr ""

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "when reaching a given stage"
msgstr ""

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr ""

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid ""
"{{ object.project_id.company_id.name or user.env.company.name }}: "
"Satisfaction Survey"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
msgid "👤 Unassigned"
msgstr ""

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_graph/project_task_graph_model.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
msgid "🔒 Private"
msgstr ""
