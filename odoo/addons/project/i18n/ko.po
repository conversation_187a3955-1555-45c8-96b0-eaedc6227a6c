# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* project
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    No milestones found. Let's create one!\n"
"                </p><p>\n"
"                    Track major progress points that must be reached to achieve success.\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    마일스톤을 찾을 수 없습니다. 지금 생성해 주세요!\n"
"                </p><p>\n"
"                    성공적으로 완료하기 위해 달성해야 하는 주요 진행 포인트를 추적하십시오.\n"
"                </p>\n"
"            "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid ""
"\n"
");\n"
"\n"
"export class ProjectTaskFormController extends FormControllerWithHTMLExpander {\n"
"    setup() {\n"
"        super.setup();\n"
"        this.notifications = useService(\"notification\");\n"
"    }\n"
"\n"
"    /**\n"
"     * @override\n"
"     */\n"
"    getStaticActionMenuItems() {\n"
"        return {\n"
"            ...super.getStaticActionMenuItems(),\n"
"            openHistoryDialog: {\n"
"                sequence: 50,\n"
"                icon: \"fa fa-history\",\n"
"                description: _t(\"Version History\"),\n"
"                callback: () => this.openHistoryDialog(),\n"
"            },\n"
"        };\n"
"    }\n"
"\n"
"    get deleteConfirmationDialogProps() {\n"
"        const deleteConfirmationDialogProps = super.deleteConfirmationDialogProps;\n"
"        if (!this.model.root.data.subtask_count) {\n"
"            return deleteConfirmationDialogProps;\n"
"        }\n"
"        return {\n"
"            ...deleteConfirmationDialogProps,\n"
"            body: subTaskDeleteConfirmationMessage,\n"
"        }\n"
"    }\n"
"\n"
"    async openHistoryDialog() {\n"
"        const record = this.model.root;\n"
"        const versionedFieldName = 'description';\n"
"        const historyMetadata = record.data[\"html_field_history_metadata\"]?.[versionedFieldName];\n"
"        if (!historyMetadata) {\n"
"            this.notifications.add(\n"
"                escape(_t(\n"
"                    \"The task description lacks any past content that could be restored at the moment.\"\n"
"                ))\n"
"            );\n"
"            return;\n"
"        }\n"
"\n"
"        this.dialogService.add(\n"
"            HistoryDialog,\n"
"            {\n"
"                title: _t(\"Task Description History\"),\n"
"                noContentHelper: markup(\n"
"                    "
msgstr ""
"\n"
");\n"
"\n"
"export class ProjectTaskFormController extends FormControllerWithHTMLExpander {\n"
"    setup() {\n"
"        super.setup();\n"
"        this.notifications = useService(\"notification\");\n"
"    }\n"
"\n"
"    /**\n"
"     * @override\n"
"     */\n"
"    getStaticActionMenuItems() {\n"
"        return {\n"
"            ...super.getStaticActionMenuItems(),\n"
"            openHistoryDialog: {\n"
"                sequence: 50,\n"
"                icon: \"fa fa-history\",\n"
"                description: _t(\"Version History\"),\n"
"                callback: () => this.openHistoryDialog(),\n"
"            },\n"
"        };\n"
"    }\n"
"\n"
"    get deleteConfirmationDialogProps() {\n"
"        const deleteConfirmationDialogProps = super.deleteConfirmationDialogProps;\n"
"        if (!this.model.root.data.subtask_count) {\n"
"            return deleteConfirmationDialogProps;\n"
"        }\n"
"        return {\n"
"            ...deleteConfirmationDialogProps,\n"
"            body: subTaskDeleteConfirmationMessage,\n"
"        }\n"
"    }\n"
"\n"
"    async openHistoryDialog() {\n"
"        const record = this.model.root;\n"
"        const versionedFieldName = 'description';\n"
"        const historyMetadata = record.data[\"html_field_history_metadata\"]?.[versionedFieldName];\n"
"        if (!historyMetadata) {\n"
"            this.notifications.add(\n"
"                escape(_t(\n"
"                    \"The task description lacks any past content that could be restored at the moment.\"\n"
"                ))\n"
"            );\n"
"            return;\n"
"        }\n"
"\n"
"        this.dialogService.add(\n"
"            HistoryDialog,\n"
"            {\n"
"                title: _t(\"Task Description History\"),\n"
"                noContentHelper: markup(\n"
"                    "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_count
msgid "# Ratings"
msgstr "# 평점"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_model.js:0
#: model:ir.model.fields,field_description:project.field_project_milestone__task_count
#: model:ir.model.fields,field_description:project.field_report_project_task_user__nbr
msgid "# of Tasks"
msgstr "# 작업 수"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"#{record.milestone_count_reached.value} Milestones reached out of "
"#{record.milestone_count.value}"
msgstr ""
"#{record.milestone_count.value}에 도달한 #{record.milestone_count_reached.value}"
" 마일스톤"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "#{task.stage_id.name}"
msgstr "#{task.stage_id.name}"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/widget/subtask_counter.js:0
msgid "%(closedCount)s sub-tasks closed out of %(totalCount)s"
msgstr "%(closedCount)s의 하위 작업 중 %(totalCount)s의 작업이 종료되었습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(closed_task_count)s / %(task_count)s"
msgstr "%(closed_task_count)s / %(task_count)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"
msgstr "%(closed_task_count)s / %(task_count)s (%(closed_rate)s%%)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/widget/subtask_counter.js:0
msgid "%(count1)s/%(count2)s"
msgstr "%(count1)s/%(count2)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s Dashboard"
msgstr "%(name)s 대시보드"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Burndown Chart"
msgstr "%(name)s 번다운 차트"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Milestones"
msgstr "%(name)s 마일스톤"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Rating"
msgstr "%(name)s 평가"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "%(name)s's Tasks Analysis"
msgstr "%(name)s 작업 분석"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid "%(partner_name)s's Tasks"
msgstr "%(partner_name)s의 작업"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/models/project_project_stage.py:0
#: code:addons/project/models/project_task.py:0
#: code:addons/project/models/project_task_type.py:0
msgid "%s (copy)"
msgstr "%s (사본)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "%s closed tasks"
msgstr "%s 마감된 작업"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "(due"
msgstr "(다음의 기한"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "(last project update),"
msgstr "(최근 프로젝트 업데이트),"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/depend_on_ids_one2many/depend_on_ids_list_renderer.xml:0
msgid "(other) tasks to which you do not have access."
msgstr "(기타) 작업에 액세스할 수 없습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "- reached on"
msgstr "- 다음에 도달 완료"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "<b>Drag &amp; drop</b> the card to change your task from stage."
msgstr "카드를 <b>끌어서</b> 작업의 단계를 수정합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"<b>Log notes</b> for internal communications <i>(the people following this "
"task won't be notified of the note you are logging unless you specifically "
"tag them)</i>. Use @ <b>mentions</b> to ping a colleague or # "
"<b>mentions</b> to reach an entire team."
msgstr ""
"<b>로그 메모</b>를 내부 커뮤니케이션용으로 기록합니다 <i>(특별히 태그를 지정하지 않는 한 작업 팔로워에게는 로그 메모에 대한 "
"알림을 보내지 않습니다)</i>. @ 기호로 동료에게 <b>멘션</b>하거나 # 기호로 팀 전체에 <b>멘션</b>으로 전달할 수도 "
"있습니다."

#. module: project
#: model:mail.template,body_html:project.rating_project_request_email_template
msgid ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                Hello <t t-out=\"partner.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                Hello,<br/><br/>\n"
"            </t>\n"
"            Please take a moment to rate our services related to the <strong t-out=\"object.name or ''\">Planning and budget</strong> task\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                assigned to <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 13px;text-align:center;\">\n"
"                    <strong>Tell us how you feel about our services</strong><br/>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(click on one of these smileys)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            We appreciate your feedback. It helps us improve continuously.\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey has been sent because your task has been moved to the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">This satisfaction survey is sent <b t-out=\"object.project_id.rating_status_period or ''\">weekly</b> as long as the task is in the <b t-out=\"object.stage_id.name or ''\">In progress</b> stage.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br/>Best regards,</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "
msgstr ""
"<div>\n"
"    <t t-set=\"access_token\" t-value=\"object._rating_get_access_token()\"/>\n"
"    <t t-set=\"partner\" t-value=\"object._rating_get_partner()\"/>\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin:0px auto;\">\n"
"    <tbody>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            <t t-if=\"partner.name\">\n"
"                안녕하세요, <t t-out=\"partner.name or ''\">Brandon Freeman</t>님.<br/><br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                안녕하세요,<br/><br/>\n"
"            </t>\n"
"            잠시만 시간을 내어 <strong t-out=\"object.name or ''\">계획 및 예산</strong> 작업과 관련한 하기 직업의 서비스를 평가해 주시면 감사하겠습니다.\n"
"            <t t-if=\"object._rating_get_operator().name\">\n"
"                담당 직원: <strong t-out=\"object._rating_get_operator().name or ''\">Mitchell Admin</strong>.<br/>\n"
"            </t>\n"
"            <t t-else=\"\">\n"
"                .<br/>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td style=\"text-align: center;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"width:100%; margin: 32px 0px 32px 0px; display: inline-table;\">\n"
"                <tr><td style=\"font-size: 13px;text-align:center;\">\n"
"                    <strong>서비스에 대해 느끼신 점을 말씀해 주세요.</strong><br/>\n"
"                    <span style=\"font-size: 12px; opacity: 0.5; color: #454748;\">(스마일 이모티콘 중 하나를 선택)</span>\n"
"                </td></tr>\n"
"                <tr><td style=\"font-size: 13px;\">\n"
"                    <table style=\"width:100%;text-align:center;margin-top:2rem;\">\n"
"                        <tr>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/5\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Satisfied\" src=\"/rating/static/src/img/rating_5.png\" title=\"Satisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/3\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Okay\" src=\"/rating/static/src/img/rating_3.png\" title=\"Okay\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                            <td>\n"
"                                <a t-attf-href=\"/rate/{{ access_token }}/1\" t-att-class=\"'pe-none' if object._rating_get_operator() else ''\">\n"
"                                    <img alt=\"Dissatisfied\" src=\"/rating/static/src/img/rating_1.png\" title=\"Dissatisfied\"/>\n"
"                                </a>\n"
"                            </td>\n"
"                        </tr>\n"
"                    </table>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td></tr>\n"
"        <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"            고객님의 의견에 깊은 감사의 말씀을 드립니다. 해당 내용을 반영하여 서비스를 지속적으로 개선할 수 있도록 노력하겠습니다..\n"
"            <t t-if=\"object.project_id.rating_status == 'stage'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">본 고객용 설문은 작업이 <b t-out=\"object.stage_id.name or ''\">진행 중</b> 단계로 넘어감에 따라 자동으로 발송되었습니다.</span>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.rating_status == 'periodic'\">\n"
"                <br/><span style=\"margin: 0; font-size: 12px; opacity: 0.5; color: #454748;\">본 고객 만족 설문 조사는 <b t-out=\"object.project_id.rating_status_period or ''\">매주</b>마다, 작업이 <b t-out=\"object.stage_id.name or ''\">진행 중</b> 단계에 있는 동안 전송됩니다.</span>\n"
"            </t>\n"
"        </td></tr>\n"
"        <tr><td><br/>감사합니다.</td></tr>\n"
"        <tr><td>\n"
"           <t t-out=\"object.project_id.company_id.name or ''\">YourCompany</t>\n"
"        </td></tr>\n"
"        <tr><td style=\"opacity: 0.5;\">\n"
"            <t t-out=\"object.project_id.company_id.phone or ''\">**************</t>\n"
"            <t t-if=\"object.project_id.company_id.email\">\n"
"                | <a t-attf-href=\"mailto:{{ object.project_id.company_id.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.email or ''\"><EMAIL></a>\n"
"            </t>\n"
"            <t t-if=\"object.project_id.company_id.website\">\n"
"                | <a t-attf-href=\"{{ object.project_id.company_id.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"object.project_id.company_id.website or ''\">http://www.example.com</a>\n"
"            </t>\n"
"        </td></tr>\n"
"    </tbody>\n"
"    </table>\n"
"</div>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.project_done_email_template
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/>\n"
"    It is my pleasure to let you know that we have successfully completed the project \"<strong t-out=\"object.name or ''\">Renovations</strong>\".\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">You are receiving this email because your project has been moved to the stage <b t-out=\"object.stage_id.name or ''\">Done</b></span>\n"
"            "
msgstr ""
"<div>\n"
"    안녕하세요, <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>님.<br/>\n"
"    \"<strong t-out=\"object.name or ''\">레노베이션</strong>\" 프로젝트를 성공적으로 완료했음을 알려드립니다.\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"<br/><span style=\"margin: 0px 0px 0px 0px; font-size: 12px; opacity: 0.5; color: #454748;\" groups=\"project.group_project_stages\">본 이메일은 귀하의 프로젝트가 <b t-out=\"object.stage_id.name or ''\">완료</b> 단계로 이동하여 자동으로 발송되었습니다.</span>\n"
"            "

#. module: project
#: model:mail.template,body_html:project.mail_template_data_project_task
msgid ""
"<div>\n"
"    Dear <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>,<br/><br/>\n"
"    Thank you for contacting us. We appreciate your interest in our products/services.<br/>\n"
"    Our team is currently reviewing your inquiry and will respond to your email as soon as possible.<br/>\n"
"    If you have any further questions or concerns in the meantime, please do not hesitate to let us know. We are here to help.<br/><br/>\n"
"    Thank you for your patience.<br/>\n"
"    Best regards,\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "
msgstr ""
"<div>\n"
"    안녕하세요, <t t-out=\"object.partner_id.name or 'customer'\">Brandon Freeman</t>님.<br/><br/>\n"
"    먼저 저희 제품 및 서비스에 관심 가져주셔서 감사합니다.<br/>\n"
"    현재 문의하신 내용을 검토 중이며, 최대한 빠른 시일 내에 답변드리겠습니다.<br/>\n"
"    추가 질문이나 우려사항이 있으실 경우 언제든지 알려주시면 도움드릴 수 있도록 최선을 다하겠습니다.<br/><br/>\n"
"    기다려 주셔서 감사합니다.<br/>\n"
"    감사합니다.\n"
"    <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"        <br/>\n"
"        <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"    </t>\n"
"</div>\n"
"        "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg &lt; 3.66\" title=\"Satisfied\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg &lt; 2.33 or rating_avg &gt;= 3.66\" title=\"Okay\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg &gt;= 2.33\" title=\"Dissatisfied\"/>"
msgstr ""
"<i class=\"fa fa-fw o_button_icon fa-smile-o text-success\" invisible=\"rating_avg < 3.66\" title=\"만족\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-meh-o text-warning\" invisible=\"rating_avg < 2.33 or rating_avg >= 3.66\" title=\"보통\"/>\n"
"                            <i class=\"fa fa-fw o_button_icon fa-frown-o text-danger\" invisible=\"rating_avg >= 2.33\" title=\"불만족\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-lightbulb-o\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-lightbulb-o\"/> 및"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "<i class=\"fa fa-lock\"/> Private"
msgstr "<i class=\"fa fa-lock\"/> 개인"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> "
"Customer Ratings</b> are disabled on the following project(s) : <br/>"
msgstr ""
"<i class=\"fa fa-warning\" title=\"Customer disabled on projects\"/><b> 고객 "
"평가</b> 가 이 프로젝트(들)에서 비활성화 되었습니다 : <br/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<i class=\"fa fa-warning\"/>&amp;nbsp;"
msgstr "<i class=\"fa fa-warning\"/> 및"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<i class=\"oi oi-arrow-right me-1\"/>Back to edit mode"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>편집 모드로 돌아가기"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "<i title=\"Private Task\" class=\"fa fa-lock\"/>"
msgstr "<i title=\"Private Task\" class=\"fa fa-lock\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-end\">Stage:</small>"
msgstr "<small class=\"text-end\">단계:</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Assignees</small>"
msgstr "<small class=\"text-muted\">배정 대상자</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<small class=\"text-muted\">Customer</small>"
msgstr "<small class=\"text-muted\">고객</small>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"
msgstr "<span class=\"fa fa-clock-o me-2\" title=\"Dates\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid ""
"<span class=\"fa fa-envelope-o me-2\" aria-label=\"Domain Alias\" "
"title=\"Domain Alias\"/>"
msgstr "<span class=\"fa fa-envelope-o me-2\" aria-label=\"도메인 별칭\" title=\"도메인 별칭\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span class=\"fa fa-user me-2\" aria-label=\"Partner\" title=\"Partner\"/>"
msgstr "<span class=\"fa fa-user me-2\" aria-label=\"협렧\" title=\"협력사\"/>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Done</span>"
msgstr "<span class=\"fw-normal\"> 완료</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "<span class=\"fw-normal\"> Tasks</span>"
msgstr "<span class=\"fw-normal\"> 작업</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "<span class=\"o_stat_text\">Blocked Tasks</span>"
msgstr "<span class=\"o_stat_text\">차단 작업</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Last Rating</span>"
msgstr "<span class=\"o_stat_text\">최근 평가</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Parent Task</span>"
msgstr "<span class=\"o_stat_text\">상위 작업</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "<span class=\"o_stat_text\">Sub-tasks</span>"
msgstr "<span class=\"o_stat_text\">하위 작업</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"o_stat_text\">Tasks</span>"
msgstr "<span class=\"o_stat_text\">작업</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "<span class=\"text-muted o_row ps-1 pb-3\">Send a rating request:</span>"
msgstr "<span class=\"text-muted o_row ps-1 pb-3\">평가 요청 보내기:</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">A rating request will be sent as soon as the task reaches a stage on which a Rating Email Template is defined.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">Rating requests will be sent as long as the task remains in a stage on which a Rating Email Template is defined.</span>\n"
"                                                </span>"
msgstr ""
"<span colspan=\"2\" class=\"text-muted o_row ps-1\">\n"
"                                                    <i class=\"fa fa-lightbulb-o pe-2\"/>\n"
"                                                    <span invisible=\"rating_status == 'periodic'\">평가 요청은 작업이 환경 설정 내용에 따라 정의된 단계에 도달하는 즉시 이메일을 통해 전송됩니다.</span>\n"
"                                                    <span invisible=\"rating_status == 'stage'\">평가 요청은 작업의 환경 설정 내용에 따라 정의된 단계에 있는 동안 이메일을 통해 전송됩니다.</span>\n"
"                                                </span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>Reporting</span>"
msgstr "<span>보고</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "<span>View</span>"
msgstr "<span>보기</span>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Deadline:</strong>"
msgstr "<strong>마감일 :</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Milestone:</strong>"
msgstr "<strong>마일스톤:</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "<strong>Project:</strong>"
msgstr "<strong>프로젝트 :</strong>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "<u>Milestones</u>"
msgstr "<u>마일스톤</u>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "=&gt;"
msgstr "=&gt;"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "이 별칭에 대한 새 레코드를 만들 때 기본값을 제공하도록 평가되는 Python 사전입니다."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_collaborator_unique_collaborator
msgid ""
"A collaborator cannot be selected more than once in the project sharing "
"access. Please remove duplicate(s) and try again."
msgstr "프로젝트 공유 액세스에서 공동 작업자를 두 번 이상 선택할 수 없습니다. 중복된 선택 항목 삭제 후 다시 시도하십시오."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "A new task has been created and is not part of any project."
msgstr "새 작업이 생성되었으나 아직 프로젝트에 속하지 않습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "A new task has been created in the \"%(project_name)s\" project."
msgstr "\"%(project_name)s\" 프로젝트에 새 작업이 만들어졌습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid ""
"A personal stage cannot be linked to a project because it is only visible to"
" its corresponding user."
msgstr "개인 단계는 해당 사용자에게만 표시되기 때문에 프로젝트 단계에 연결시킬 수 없습니다."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_private_task_has_no_parent
msgid "A private task cannot have a parent."
msgstr "비공개 작업에는 상위 항목을 설정할 수 없습니다."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_recurring_task_has_no_parent
msgid "A subtask cannot be recurrent."
msgstr "하위 작업은 반복될 수 없습니다."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_tags_name_uniq
msgid "A tag with the same name already exists."
msgstr "같은 이름의 태그가 이미 존재합니다."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_task_user_rel_project_personal_stage_unique
msgid "A task can only have a single personal stage per user."
msgstr "개인 단계는 사용자 당 하나만 작업에 위치시킬 수 있습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Accept Emails From"
msgstr "수신 이메일 허용"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__access_mode
msgid "Access Mode"
msgstr "액세스 모드"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_warning
#: model:ir.model.fields,field_description:project.field_project_share_wizard__access_warning
#: model:ir.model.fields,field_description:project.field_project_task__access_warning
msgid "Access warning"
msgstr "사용 권한 경고"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__active
msgid "Active"
msgstr "활성화"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_ids
#: model:ir.model.fields,field_description:project.field_project_task__activity_ids
#: model:ir.model.fields,field_description:project.field_project_update__activity_ids
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Activities"
msgstr "활동"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "활동 예외 장식"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_plan_action_config_project_task_plan
#: model:ir.ui.menu,name:project.mail_activity_plan_menu_config_project
msgid "Activity Plans"
msgstr "활동 계획"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_state
#: model:ir.model.fields,field_description:project.field_project_task__activity_state
#: model:ir.model.fields,field_description:project.field_project_update__activity_state
msgid "Activity State"
msgstr "활동 상태"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_icon
msgid "Activity Type Icon"
msgstr "활동 유형 아이콘"

#. module: project
#: model:ir.actions.act_window,name:project.mail_activity_type_action_config_project_types
#: model:ir.ui.menu,name:project.project_menu_config_activity_type
msgid "Activity Types"
msgstr "활동 유형"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Progress Report\", \"Stand-up Meeting\", ...)"
msgstr ""
"작업 계획은 몇 번의 클릭으로 작업 목록을 배정하는 데 사용됩니다.\n"
"                    (예: \"진행 보고서\", \"짧은 업무 회의\", ...)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Add Milestone"
msgstr "마일스톤 추가"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.js:0
msgid "Add Sub-tasks"
msgstr "하위 작업 추가"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Add a sub-task"
msgstr "하위 작업 추가"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Add columns to organize your tasks into <b>stages</b> <i>e.g. New - In "
"Progress - Done</i>."
msgstr "열을 추가하여 작업을 관리할<b>단계</b>구성합니다 <i>예. 신규 - 진행중 - 완료</i>."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Add details about this task..."
msgstr "이 작업에 대한 세부 정보 추가"

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__note
msgid "Add extra content to display in the email"
msgstr "이메일에 표시할 추가 콘텐츠 만들기"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_3
msgid ""
"Add project-specific property fields on tasks to customize your project "
"management process."
msgstr "프로젝트 관리를 사용자 지정하려면 작업에 프로젝트별 속성 필드를 추가하세요."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Add your task once it is ready."
msgstr "준비되면 작업을 추가합니다."

#. module: project
#: model:res.groups,name:project.group_project_manager
msgid "Administrator"
msgstr "관리자"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Agile Scrum"
msgstr "애자일 스크럼"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_id
msgid "Alias"
msgstr "별칭"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_contact
msgid "Alias Contact Security"
msgstr "별칭 연락처 보안"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain_id
msgid "Alias Domain"
msgstr "별칭 도메인"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_domain
msgid "Alias Domain Name"
msgstr "별칭 도메인 이름"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_full_name
msgid "Alias Email"
msgstr "이메일 별칭"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_name
msgid "Alias Name"
msgstr "별칭 이름"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_status
msgid "Alias Status"
msgstr "별칭 상태"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_status
msgid "Alias status assessed on the last message received."
msgstr "최근 받은메일에서 확인한 별칭 상태입니다."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_model_id
msgid "Aliased Model"
msgstr "별칭 모델"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "All"
msgstr "전체"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_all_task
#: model:ir.ui.menu,name:project.menu_project_management_all_tasks
msgid "All Tasks"
msgstr "전체 작업"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__employees
msgid "All internal users"
msgstr "전체 내부 사용자"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__allocated_hours
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__allocated_hours
msgid "Allocated Time"
msgstr "할당된 시간"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.portal_my_task_allocated_hours_template
msgid "Allocated Time:"
msgstr "할당 시간:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Analytic"
msgstr "분석"

#. module: project
#: model:ir.model,name:project.model_account_analytic_account
#: model:ir.model.fields,field_description:project.field_project_project__auto_account_id
msgid "Analytic Account"
msgstr "분석 계정"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Analytics"
msgstr "분석"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
msgid ""
"Analyze how quickly your team is completing your project's tasks and check "
"if everything is progressing according to plan."
msgstr "팀에서 프로젝트의 작업을 얼마나 빨리 완수할 수 있는지와 모든 것이 계획대로 진행되고 있는지를 분석합니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid ""
"Analyze the progress of your projects and the performance of your employees."
msgstr "프로젝트의 진행 상황과 직원들의 실적을 분석합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_wizard__share_link
msgid "Anyone with this link can access the project in read mode."
msgstr "이 링크를 가진 사람은 누구나 읽기 전용 모드로 프로젝트에 액세스할 수 있습니다."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__03_approved
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__03_approved
msgid "Approved"
msgstr "결재 완료"

#. module: project
#: model:project.tags,name:project.project_tags_07
msgid "Architecture"
msgstr "구조"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
msgid "Archive Account"
msgstr "보관 계정"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
msgid "Archive Accounts"
msgstr "보관 계정"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Archive Stages"
msgstr "단계 보관"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Archived"
msgstr "보관됨"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid "Are you sure you want to continue?"
msgstr "계속 하시겠습니까?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Are you sure you want to delete these stages?"
msgstr "이 단계를 삭제하시겠습니까?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: code:addons/project/static/src/components/subtask_one2many_field/subtask_list_renderer.js:0
msgid "Are you sure you want to delete this record?"
msgstr "이 내용을 삭제하시겠습니까?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "Are you sure you want to restore this version ?"
msgstr "이 버전을 복원하시겠습니까?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow"
msgstr "화살표"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Arrow icon"
msgstr "화살표 아이콘"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Assembling"
msgstr "조립"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Assign a responsible to your task"
msgstr "작업에 책임자 지정"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Assigned"
msgstr "배정됨"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
msgid "Assigned to"
msgstr "담당자"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__user_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__user_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__user_ids
#: model_terms:ir.ui.view,arch_db:project.open_view_blocked_by_list_view
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "Assignees"
msgstr "담당자"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Assignement Date"
msgstr "배정일"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_assign
msgid "Assigning Date"
msgstr "지정일"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_assign
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_assign
msgid "Assignment Date"
msgstr "배정 일자"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__at_risk
#: model:ir.model.fields.selection,name:project.selection__project_update__status__at_risk
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "At Risk"
msgstr "리스크"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Attach all documents or links to the task directly, to have all research "
"information centralized."
msgstr "문서나 링크를 작업에 직접 첨부하여 모든 조사 자료를 중앙 집중화하십시오."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_project__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_task__message_attachment_count
#: model:ir.model.fields,field_description:project.field_project_update__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Attachments"
msgstr "첨부 파일"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__attachment_ids
msgid "Attachments that don't come from a message"
msgstr "메시지에서 가져오지 않은 첨부 파일입니다."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__user_id
msgid "Author"
msgstr "작성자"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Auto-generate tasks for regular activities"
msgstr "일상적인 활동을 위해 자동생성된 작업"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__auto_validation_state
msgid "Automatic Kanban Status"
msgstr "자동 칸반 상태"

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__auto_validation_state
msgid ""
"Automatically modify the state when the customer replies to the feedback for this stage.\n"
" * Good feedback from the customer will update the state to 'Approved' (green bullet).\n"
" * Neutral or bad feedback will set the kanban state to 'Changes Requested' (orange bullet).\n"
msgstr ""
"고객이 이 단계에 대한 피드백에 회신할 경우 자동으로 상태를 변경합니다 .\n"
" * 고객으로부터 좋은 피드백을 받은 경우 상태는 '승인 완료' (초록색)로 변경됩니다.\n"
" * 보통 또는 나쁜 피드백을 받은 경우 칸반 상태는 '변경 요청' (주황색)으로 변경됩니다.\n"

#. module: project
#: model:mail.template,description:project.mail_template_data_project_task
msgid ""
"Automatically send an email to customers when a task reaches a specific "
"stage in a project by setting this template on that stage"
msgstr "이 템플릿을 해당 단계에 할당하여 작업이 프로젝트의 특정 단계에 도달하면 고객에게 자동으로 이메일을 보내세요."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Avatar"
msgstr "아바타"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg
msgid "Average Rating"
msgstr "평균 평점"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "평균 점수 (%)"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_avg
msgid "Average Rating (1-5)"
msgstr "평균 점수 (1-5)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Dissatisfied"
msgstr "평균 점수: 불만족"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Okay"
msgstr "평균 점수: 보통"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Average Rating: Satisfied"
msgstr "평균 점수: 만족"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Backlog"
msgstr "기록"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__analytic_account_balance
msgid "Balance"
msgstr "잔액"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Billed"
msgstr "청구됨"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__dependent_ids
msgid "Block"
msgstr "블록"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocked"
msgstr "거부됨"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_ids
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked By"
msgstr "차단 대상"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Blocked Tasks"
msgstr "차단된 작업"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Blocking"
msgstr "차단"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Brainstorm"
msgstr "브레인스톰"

#. module: project
#: model:project.tags,name:project.project_tags_00
msgid "Bug"
msgstr "버그"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.action_project_task_burndown_chart_report
#: model:ir.model,name:project.model_project_task_burndown_chart_report
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Burndown Chart"
msgstr "번다운 차트"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_canceled
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_canceled
#: model:project.project.stage,name:project.project_project_stage_3
#: model:project.task.type,name:project.project_personal_stage_admin_6
#: model:project.task.type,name:project.project_personal_stage_demo_6
#: model:project.task.type,name:project.project_stage_3
msgid "Cancelled"
msgstr "취소됨"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__02_changes_requested
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__02_changes_requested
#: model:mail.message.subtype,description:project.mt_task_changes_requested
#: model:mail.message.subtype,name:project.mt_project_task_changes_requested
#: model:mail.message.subtype,name:project.mt_task_changes_requested
msgid "Changes Requested"
msgstr "변경 요청"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Choose a <b>name</b> for your project. <i>It can be anything you want: the "
"name of a customer, of a product, of a team, of a construction site, "
"etc.</i>"
msgstr ""
"프로젝트의 <b>이름</b>을 선택하세요. <i>고객 이름이나, 품목명, 팀명, 건설 현장명 등 무엇이든 선택하실 수 있습니다.</i>"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Choose a task <b>name</b> <i>(e.g. Website Design, Purchase Goods...)</i>"
msgstr " <b>이름</b>을 선택하세요 <i>(예. 웹사이트 디자인, 물품 구입...)</i>"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Choose one of the following access modes for your collaborators:"
msgstr "공동 작업자를 위해 다음 액세스 모드 중 하나를 선택합니다:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Client Review"
msgstr "고객 평가"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Close the sub-tasks list"
msgstr "하위 작업 목록 닫기"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__closed_depend_on_count
msgid "Closed Depending on Tasks"
msgstr "업무에 따라 휴무"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed On"
msgstr "마감일"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Closed Tasks"
msgstr "마감된 작업"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__is_closed
#: model:ir.model.fields,field_description:project.field_report_project_task_user__is_closed
msgid "Closed state"
msgstr "마감 상태"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__is_closed__closed
msgid "Closed tasks"
msgstr "마감된 작업"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__is_closed
msgid "Closing Stage"
msgstr "마감된 단계"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__partner_id
msgid "Collaborator"
msgstr "공동작업자"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__collaborator_ids
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Collaborators"
msgstr "공동작업자"

#. module: project
#: model:ir.model,name:project.model_project_collaborator
msgid "Collaborators in project shared"
msgstr "공유된 프로젝트의 공동 작업자"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_status
msgid ""
"Collect feedback from your customers by sending them a rating request when a task enters a certain stage. To do so, define a rating email template on the corresponding stages.\n"
"Rating when changing stage: an email will be automatically sent when the task reaches the stage on which the rating email template is set.\n"
"Periodic rating: an email will be automatically sent at regular intervals as long as the task remains in the stage in which the rating email template is set."
msgstr ""
"작업이 특정 단계에 도달하면 평가 요청을 보내어 고객으로부터 피드백을 수집합니다. 그렇게 하려면 해당 단계에 평가 이메일 서식을 지정해야 합니다. \n"
"단계 변경 시 평가: 평가 이메일 서식이 설정되어 있는 경우, 작업이 단계에 도달하면 자동으로 이메일을 발송합니다.\n"
"정기 평가: 평가 이메일 서식이 설정되어 있으면 작업이 단계에 있는 동안 정기적으로 자동 이메일을 발송합니다. "

#. module: project
#: model:ir.model.fields,field_description:project.field_project_tags__color
msgid "Color"
msgstr "색상"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Communicate with customers on the task using the email gateway. Attach logo designs to the task, so that information flows from\n"
"      designers to the workers who print the t-shirt."
msgstr ""
"이메일 게이트웨이를 사용하여 작업에 대해 고객과 소통합니다. 작업에 로고 디자인을 첨부하여\n"
"      디자이너로부터 티셔츠 프린트 담당자에게 정보가 전달될 수 있도록 합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Communication history"
msgstr "  대화 이력"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__company_id
#: model:ir.model.fields,field_description:project.field_project_project_stage__company_id
#: model:ir.model.fields,field_description:project.field_project_task__company_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__company_id
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Company"
msgstr "회사"

#. module: project
#: model:ir.model,name:project.model_res_config_settings
msgid "Config Settings"
msgstr "환경 설정"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_config
msgid "Configuration"
msgstr "설정"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Configure Stages"
msgstr "단계 설정"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Confirm"
msgstr "승인"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
#: code:addons/project/wizard/project_task_type_delete.py:0
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Confirmation"
msgstr "확인"

#. module: project
#: model_terms:web_tour.tour,rainbow_man_message:project.project_tour
msgid "Congratulations, you are now a master of project management."
msgstr "축하합니다, 프로젝트 관리의 마스터 레벨에 도달했습니다."

#. module: project
#: model:project.tags,name:project.project_tags_06
msgid "Construction"
msgstr "구축"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Consulting"
msgstr "컨설팅"

#. module: project
#: model:ir.model,name:project.model_res_partner
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Contact"
msgstr "연락처"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid "Convert Task"
msgstr "작업 전환"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.actions.server,name:project.action_server_convert_to_subtask
msgid "Convert to Task/Sub-Task"
msgstr "작업/하위작업으로 전환"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Copywriting"
msgstr "저작권"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Costs"
msgstr "비용"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__displayed_image_id
msgid "Cover Image"
msgstr "표지 이미지"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Create <b>activities</b> to set yourself to-dos or to schedule meetings."
msgstr "<b>작업</b>을 생성하여 할 일 목록이나 미팅 예약을 설정합니다."

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__create_date
msgid "Create Date"
msgstr "작성일자"

#. module: project
#: model:ir.actions.act_window,name:project.open_create_project
msgid "Create a Project"
msgstr "프로젝트 생성"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_plan_action_config_project_task_plan
msgid "Create a Task Activity Plan"
msgstr "작업 활동 계획 만들기"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid "Create a new stage in the task pipeline"
msgstr "파이프라인 활동 안에 새로운 단계 만들기"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Create a new sub-task"
msgstr "새 하위 작업 만들기"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
msgid "Create project"
msgstr "프로젝트 생성"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
msgid ""
"Create projects to organize your tasks and define a different workflow for "
"each project."
msgstr "프로젝트를 생성하여 작업을 체계화하고 각 프로젝트마다 다른 작업 절차를 지정합니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
msgid ""
"Create projects to organize your tasks. Define a different workflow for each"
" project."
msgstr "프로젝트를 생성하여 작업을 체계화합니다. 각 프로젝트마다 다른 작업 절차를 지정합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "Create tasks by sending an email to"
msgstr "다음으로 메일을 발송하여 작업을 생성합니다."

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Create tasks by sending an email to the email address of your project."
msgstr "프로젝트에 있는 이메일 주소로 메일을 보내면 작업이 생성됩니다."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__create_date
msgid "Created On"
msgstr "작성일자"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__create_uid
#: model:ir.model.fields,field_description:project.field_project_project__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_tags__create_uid
#: model:ir.model.fields,field_description:project.field_project_task__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__create_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_uid
#: model:ir.model.fields,field_description:project.field_project_update__create_uid
msgid "Created by"
msgstr "작성자"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__create_date
#: model:ir.model.fields,field_description:project.field_project_milestone__create_date
#: model:ir.model.fields,field_description:project.field_project_project__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__create_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_tags__create_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__create_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type__create_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__create_date
#: model:ir.model.fields,field_description:project.field_project_update__create_date
msgid "Created on"
msgstr "작성일자"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Creation Date"
msgstr "작성일자"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "Current project of the task"
msgstr "작업의 현재 프로젝트"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "Current stage of this task"
msgstr "작업의 현재 단계"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid ""
"Currently available to everyone viewing this document, click to restrict to "
"internal employees."
msgstr "현재 모든 사용자가 내용을 볼 수 있습니다. 내부 직원용으로 제한하려면 클릭하십시오."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid ""
"Currently restricted to internal employees, click to make it available to "
"everyone viewing this document."
msgstr "현재 내부 직원용으로 제한되어 있습니다. 모든 사용자가 볼 수 있도록 바꾸려면 클릭하세요."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "고객 반송 메시지"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_project__partner_id
#: model:ir.model.fields,field_description:project.field_project_task__partner_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__partner_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__partner_id
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Customer"
msgstr "고객"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Customer Email"
msgstr "고객 이메일"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Customer Feedback"
msgstr "고객 피드백"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__access_url
#: model:ir.model.fields,help:project.field_project_task__access_url
msgid "Customer Portal URL"
msgstr "고객 포털 URL"

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_project_report
#: model:ir.model.fields,field_description:project.field_project_project__rating_active
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_rating
#: model:ir.ui.menu,name:project.rating_rating_menu_project
msgid "Customer Ratings"
msgstr "고객 평가"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status
msgid "Customer Ratings Status"
msgstr "고객 평가 상태"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Customers propose feedbacks by email; Odoo creates tasks automatically, and you can\n"
"      communicate on the task directly."
msgstr ""
"고객이 이메일로 피드백을 제안하면 Odoo는 자동으로 작업을 생성하여\n"
"      고객이 작업에 대해 직접 소통할 수 있도록 합니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Customers will be added to the followers of their project and tasks."
msgstr "고객을 해당 프로젝트 및 작업의 팔로워로 추가합니다."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__daily
msgid "Daily"
msgstr "일별"

#. module: project
#: model:ir.actions.act_window,name:project.project_update_all_action
#: model:ir.embedded.actions,name:project.project_embedded_action_project_updates
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban_inherit_project
msgid "Dashboard"
msgstr "현황판"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date
#: model:ir.model.fields,field_description:project.field_project_update__date
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_graph
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Date"
msgstr "날짜"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_last_stage_update
msgid ""
"Date on which the state of your task has last been modified.\n"
"Based on this information you can identify tasks that are stalling and get statistics on the time it usually takes to move tasks from one stage/state to another."
msgstr ""
"마지막으로 작업 상태를 수정한 날짜입니다.\n"
"이 정보를 기준으로 지연된 작업에 대해 파악하고 작업이 한 단계/상태에서 다른 단계/상태로 이동하는데 일반적으로 소요되는 시간에 대한 통계를 얻을 수 있습니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__date
msgid ""
"Date on which this project ends. The timeframe defined on the project is "
"taken into account when viewing its planning."
msgstr "이 프로젝트가 종료되는 날짜입니다. 프로젝트에서 지정한 시간 범위를 일정 계획을 검토할 때 고려합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__date_assign
msgid ""
"Date on which this task was last assigned (or unassigned). Based on this, "
"you can get statistics on the time it usually takes to assign tasks."
msgstr ""
"이 작업이 최근 배정 (또는 배정 취소)된 날짜입니다. 이를 기반으로 하여 일반적으로 작업을 배정하는 데 걸리는 시간에 대한 통계를 얻을"
" 수 있습니다."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__day
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__day
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Days"
msgstr "일"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__delay_endings_days
msgid "Days to Deadline"
msgstr "마감까지 남은 날짜"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__deadline
#: model:ir.model.fields,field_description:project.field_project_task__date_deadline
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_deadline
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_deadline
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Deadline"
msgstr "마감 시한"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "Dear"
msgstr "친애하는"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_defaults
msgid "Default Values"
msgstr "기본값"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form_domain
msgid ""
"Define the steps that will be used in the project from the\n"
"                creation of the task, up to the closing of the task or issue.\n"
"                You will use these stages in order to track the progress in\n"
"                solving a task or an issue."
msgstr ""
"작업 작성에서부터 작업 또는 이슈 종료까지\n"
"                프로젝트에 사용될 단계를 정의하십시오.\n"
"                작업 또는 이슈 해결의 진행상황을 추적하기 위해\n"
"                이 단계를 사용합니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid ""
"Define the steps your projects move through from creation to completion."
msgstr "생성에서 완료까지 프로젝트가 이동하는 단계를 지정합니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
msgid "Define the steps your tasks move through from creation to completion."
msgstr "생성에서 완료까지 작업이 이동하는 단계를 지정합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_attachments_viewer.xml:0
#: model:ir.actions.server,name:project.unlink_project_stage_action
#: model:ir.actions.server,name:project.unlink_task_type_action
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid "Delete"
msgstr "삭제"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Delete Milestone"
msgstr "마일스톤 삭제"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid "Delete Project Stage"
msgstr "프로젝트 스테이지 삭제"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Delete Stage"
msgstr "단계 삭제"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__milestone_id
msgid ""
"Deliver your services automatically when a milestone is reached by linking "
"it to a sales order item."
msgstr "판매주문서의 항목과 연결하여 마일스톤에 도달하면 자동으로 서비스를 제공합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Delivered"
msgstr "배송완료"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Dependent Tasks"
msgstr "종속 작업"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__depend_on_count
msgid "Depending on Tasks"
msgstr "작업에 따라"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__description
#: model:ir.model.fields,field_description:project.field_project_task__description
#: model:ir.model.fields,field_description:project.field_project_update__description
#: model:ir.model.fields,field_description:project.field_report_project_task_user__description
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Description"
msgstr "설명"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__description
msgid "Description to provide more information and context about this project"
msgstr "이 프로젝트에 대한 추가 정보 및 컨텍스트를 확인할 수 있는 설명"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.tags,name:project.project_tags_08
msgid "Design"
msgstr "디자인"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Determine the order in which to perform tasks"
msgstr "작업을 수행할 순서를 결정"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Development"
msgstr "개발"

#. module: project
#: model:ir.model,name:project.model_digest_digest
msgid "Digest"
msgstr "요약"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Digital Marketing"
msgstr "디지털 마케팅"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified_footer
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid "Discard"
msgstr "취소"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__display_name
#: model:ir.model.fields,field_description:project.field_project_milestone__display_name
#: model:ir.model.fields,field_description:project.field_project_project__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage__display_name
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_share_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_tags__display_name
#: model:ir.model.fields,field_description:project.field_project_task__display_name
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__display_name
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type__display_name
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__display_name
#: model:ir.model.fields,field_description:project.field_project_update__display_name
#: model:ir.model.fields,field_description:project.field_report_project_task_user__display_name
msgid "Display Name"
msgstr "표시명"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Display the sub-task in your pipeline"
msgstr "파이프라인에 하위 작업 표시"

#. module: project
#. odoo-python
#: code:addons/project/models/digest_digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "사용 권한 없음, 이 데이터는 사용자 요약 이메일에서 무시됩니다"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__done
#: model:ir.model.fields.selection,name:project.selection__project_task__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__1_done
#: model:ir.model.fields.selection,name:project.selection__project_update__status__done
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__1_done
#: model:project.project.stage,name:project.project_project_stage_2
#: model:project.task.type,name:project.project_personal_stage_admin_5
#: model:project.task.type,name:project.project_personal_stage_demo_5
#: model:project.task.type,name:project.project_stage_2
msgid "Done"
msgstr "완료"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Draft"
msgstr "미결"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Duplicate"
msgstr "복사"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid ""
"Each user should have at least one personal stage. Create a new stage to "
"which the tasks can be transferred after the selected ones are deleted."
msgstr ""
"각 사용자에게는 개인 단계가 최소 하나 이상 있어야 합니다. 선택한 단계를 삭제한 후 작업을 이전할 수 있는 새로운 단계를 생성합니다."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__edit
msgid "Edit"
msgstr "편집"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__edit_limited
msgid "Edit with limited access"
msgstr "제한된 액세스 권한으로 편집"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"Edit with limited access: collaborators can view and edit tasks they follow "
"in the Kanban view."
msgstr "제한된 액세스 권한으로 편집: 공동 작업자는 칸반 보기에서 자신이 팔로우하는 작업을 보고 편집할 수 있습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid ""
"Edit: collaborators can view and edit all tasks in the Kanban view. "
"Additionally, they can choose which tasks they want to follow."
msgstr "편집: 공동 작업자는 칸반 보기에서 모든 작업을 보고 편집할 수 있습니다. 또한 팔로우할 작업을 선택할 수도 있습니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Editing"
msgstr "수정중"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_email
msgid "Email Alias"
msgstr "이메일 별칭"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__mail_template_id
#: model:ir.model.fields,field_description:project.field_project_task_type__mail_template_id
msgid "Email Template"
msgstr "이메일 서식"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__email_cc
msgid ""
"Email addresses that were in the CC of the incoming emails from this task "
"and that are not currently linked to an existing customer."
msgstr "이 작업에서 수신된 이메일에서 참조가 되어 있었으며 기존 고객과 현재 연결되어 있지 않은 이메일 주소입니다."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__email_cc
#: model:ir.model.fields,field_description:project.field_project_update__email_cc
msgid "Email cc"
msgstr "이메일 참조"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "이메일 도메인 예: '<EMAIL>'의 'example.com'"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Emails sent to"
msgstr "이메일이 다음 주소로 전송되었습니다"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Employees Only"
msgstr "직원용"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_until
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_until
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "End Date"
msgstr "종료일"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__date_end
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_end
msgid "Ending Date"
msgstr "종료일"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Error! You cannot create a recursive hierarchy of tasks."
msgstr "오류가 발생했습니다! 작업 순환 구조를 생성할 수 없습니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Everyone can propose ideas, and the Editor marks the best ones as"
msgstr "누구나 아이디어를 제시할 수 있으며, 최고의 아이디어는 편집자가 다음과 같이 표시합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Expected"
msgstr "예상됨"

#. module: project
#: model:project.tags,name:project.project_tags_02
msgid "Experiment"
msgstr "실험"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date
msgid "Expiration Date"
msgstr "만료일"

#. module: project
#: model:project.tags,name:project.project_tags_05
msgid "External"
msgstr "외부"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Extra Info"
msgstr "추가 정보"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Favorite"
msgstr "즐겨찾기"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Favorite Projects"
msgstr "즐겨찾는 프로젝트"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Final Document"
msgstr "최종 문서"

#. module: project
#: model:project.tags,name:project.project_tags_11
msgid "Finance"
msgstr "재무"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project_stage__fold
#: model:ir.model.fields,field_description:project.field_project_task_type__fold
msgid "Folded in Kanban"
msgstr "칸반 화면 접기"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/chatter/portal_chatter_patch.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.xml:0
msgid "Follow"
msgstr "팔로우"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow and comments tasks of your projects"
msgstr "내 프로젝트 작업의 팔로우 및 댓글"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
msgid "Follow the evolution of your projects"
msgstr "프로젝트 진행을 팔로우합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Followed"
msgstr "팔로우 중"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Followed Updates"
msgstr "후속 업데이트"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_follower_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_partner_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_type_icon
#: model:ir.model.fields,help:project.field_project_task__activity_type_icon
#: model:ir.model.fields,help:project.field_project_update__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "멋진 아이콘 폰트 예: fa-tasks"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__forever
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__forever
msgid "Forever"
msgstr "영원히"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Frequency"
msgstr "주기"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Future"
msgstr "앞으로"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Future Activities"
msgstr "향후 활동"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid ""
"Get a snapshot of the status of your project and share its progress with key"
" stakeholders."
msgstr "프로젝트 상태에 대한 정보를 확인하며 진행 상황을 관련자들에게 공유해주십시오."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Get customer feedback and evaluate the performance of your employees"
msgstr "고객 피드백을 받고 직원의 성과를 평가하세요."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Give the sub-task a <b>name</b>"
msgstr "하위 작업에 <b>이름</b>을 지정합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid "Grant Portal Access"
msgstr "포털 접근 권한 부여"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Grant employees access to your project or tasks by adding them as followers."
" Employees automatically get access to the tasks they are assigned to."
msgstr ""
"직원을 팔로워로 추가하여 프로젝트나 작업에 접근할 수 있도록 권한을 부여합니다. 직원은 배정받은 작업에 접근 권한을 자동으로 받게 "
"됩니다. "

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Grant portal users access to your project by adding them as followers (the "
"tasks of the project are not included). To grant access to tasks to a portal"
" user, add them as followers for these tasks."
msgstr ""
"포털 사용자를 팔로워로 추가하여 프로젝트에 대한 액세스 권한을 부여합니다 (프로젝트의 작업은 포함되지 않음). 포털 사용자에게 특정 "
"작업에 대한 액세스 권한을 부여하려면 해당 사용자를 해당 작업의 팔로워로 추가하세요."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Group By"
msgstr "그룹별"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Handle your idea gathering within Tasks of your new Project and discuss them"
" in the chatter of the tasks."
msgstr "새로운 프로젝트의 작업에서 수집한 아이디어를 처리하고 작업 메시지창에서 이에 대해 논의합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Handoff"
msgstr "전달"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Happy face"
msgstr "행복한 표정"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__has_message
#: model:ir.model.fields,field_description:project.field_project_project__has_message
#: model:ir.model.fields,field_description:project.field_project_task__has_message
#: model:ir.model.fields,field_description:project.field_project_update__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid "Hello"
msgstr "안녕하세요"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "Hide closed tasks"
msgstr "종료된 작업 숨기기"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hide the sub-task in your pipeline"
msgstr " 파이프라인에서 하위 작업 숨기기"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__1
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__1
msgid "High"
msgstr "높음"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
msgid "History"
msgstr "기록"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__html_field_history
msgid "History data"
msgstr "히스토리 데이터"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__html_field_history_metadata
msgid "History metadata"
msgstr "히스토리 메타데이터"

#. module: project
#: model:project.tags,name:project.project_tags_13
msgid "Home"
msgstr "홈"

#. module: project
#: model:account.analytic.account,name:project.analytic_construction
#: model:project.project,name:project.project_home_construction
msgid "Home Construction"
msgstr "주택 건설"

#. module: project
#: model:project.project,name:project.project_project_4
msgid "Home Make Over"
msgstr "주택 수리"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Hours"
msgstr "시간"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "How’s this project going?"
msgstr "프로젝트 진행 상황은 어떤가요?"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__id
#: model:ir.model.fields,field_description:project.field_project_milestone__id
#: model:ir.model.fields,field_description:project.field_project_project__id
#: model:ir.model.fields,field_description:project.field_project_project_stage__id
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__id
#: model:ir.model.fields,field_description:project.field_project_share_wizard__id
#: model:ir.model.fields,field_description:project.field_project_tags__id
#: model:ir.model.fields,field_description:project.field_project_task__id
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__id
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__id
#: model:ir.model.fields,field_description:project.field_project_task_type__id
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__id
#: model:ir.model.fields,field_description:project.field_project_update__id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__id
msgid "ID"
msgstr "ID"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "별칭을 담고 있는 상위 레코드의 ID (예: 별칭을 생성한 작업을 담고 있는 프로젝트)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,field_description:project.field_project_update__activity_exception_icon
msgid "Icon"
msgstr "아이콘"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_task__activity_exception_icon
#: model:ir.model.fields,help:project.field_project_update__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "예외 활동을 표시하기 위한 아이콘"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Ideas"
msgstr "아이디어"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction
#: model:ir.model.fields,help:project.field_project_project__message_needaction
#: model:ir.model.fields,help:project.field_project_task__message_needaction
#: model:ir.model.fields,help:project.field_project_update__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error
#: model:ir.model.fields,help:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_project__message_has_error
#: model:ir.model.fields,help:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_task__message_has_error
#: model:ir.model.fields,help:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,help:project.field_project_update__message_has_error
#: model:ir.model.fields,help:project.field_project_update__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__fold
msgid ""
"If enabled, this stage will be displayed as folded in the Kanban view of "
"your projects. Projects in a folded stage are considered as closed."
msgstr ""
"활성화한 경우, 프로젝트의 칸반 화면에서 이 단계는 접힌 상태로 표시됩니다. 프로젝트에서 접힘 단계인 경우에는 종료된 것으로 간주합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__rating_template_id
msgid ""
"If set, a rating request will automatically be sent by email to the customer when the task reaches this stage. \n"
"Alternatively, it will be sent at a regular interval as long as the task remains in this stage, depending on the configuration of your project. \n"
"To use this feature make sure that the 'Customer Ratings' option is enabled on your project."
msgstr ""
"설정할 경우, 작업이 이 단계에 도달하면 고객에게 자동으로 이메일을 통해 평가 요청을 전송합니다. \n"
"그렇지 않으면, 프로젝트의 환경 설정 내용에 따라서 작업이 이 단계에 있는 동안 정기적으로 텀을 두고 고객에게 전송하게 됩니다. \n"
"이 기능을 사용하려면 '고객 평가' 옵션이 프로젝트에서 활성화되어 있는지 확인하시기 바랍니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_project_stage__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the project"
" reaches this stage."
msgstr "설정할 경우, 프로젝트가 이 단계에 도달하면 고객에게 자동으로 이메일을 전송합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__mail_template_id
msgid ""
"If set, an email will be automatically sent to the customer when the task "
"reaches this stage."
msgstr "설정할 경우, 작업이 이 단계에 도달하면 고객에게 자동으로 이메일을 전송합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "설정할 경우, 권한이 없는 사용자에게 기본 메시지 대신 이 내용을 전송합니다. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__01_in_progress
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__01_in_progress
#: model:project.project.stage,name:project.project_project_stage_1
#: model:project.task.type,name:project.project_stage_1
msgid "In Progress"
msgstr "진행 중"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "In development"
msgstr "개발중"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_0
#: model:project.task.type,name:project.project_personal_stage_demo_0
msgid "Inbox"
msgstr "수신함"

#. module: project
#: model:project.tags,name:project.project_tags_09
msgid "Interior"
msgstr "인테리어"

#. module: project
#: model:project.tags,name:project.project_tags_04
msgid "Internal"
msgstr "내부"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Internal Note"
msgstr "내부 노트"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_id
msgid ""
"Internal email associated with this project. Incoming emails are "
"automatically synchronized with Tasks (or optionally Issues if the Issue "
"Tracker module is installed)."
msgstr ""
"이 프로젝트와 관련된 내부 이메일. 수신 이메일은 자동적으로 작업과 동기화됩니다(또는 이슈 추적 모듈이 설치되었으면 이슈와 동기화-"
"선택사항)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Internal notes are only displayed to internal users."
msgstr "내부용 메모는 내부 사용자들에게만 표시됩니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Invalid operator: %s"
msgstr "잘못된 운영자: %s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Invalid value: %s"
msgstr "잘못된 값: %s"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__followers
msgid "Invited internal users (private)"
msgstr "초대한 내부 사용자 (비공개)"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__privacy_visibility__portal
msgid "Invited portal users and all internal users (public)"
msgstr "초대한 포탈 사용자 및 내부 사용자 전체 (공개)"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Invoiced"
msgstr "발행 완료된 청구서"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Is Closed (Burn-up Chart)"
msgstr "마감됨 (번업 차트)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_project__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_task__message_is_follower
#: model:ir.model.fields,field_description:project.field_project_update__message_is_follower
#: model:ir.model.fields,field_description:project.field_report_project_task_user__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
msgid "Is toggle mode"
msgstr "토글 모드입니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_tags_search_view
msgid "Issue Version"
msgstr "이슈 버전"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__duration_tracking
#: model:ir.model.fields,help:project.field_project_task__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "many2one 필드의 ID를 초 단위의 소요 시간으로 매핑하는 JSON"

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"제작부터 완료까지 작업의 진행 상황을 추적해보세요.<br>\n"
"                    실시간 채팅이나 이메일을 통하여 효율적인 방식으로 협업할 수 있습니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_recurring_tasks_action
msgid ""
"Keep track of the progress of your tasks from creation to completion.<br>\n"
"                Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"제작부터 완료까지 작업의 진행 상황을 추적해보세요.<br>\n"
"                실시간 채팅이나 이메일을 통하여 효율적인 방식으로 협업할 수 있습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last 30 Days"
msgstr "최근 30일"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last 365 Days"
msgstr "최근 365일"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Last 7 Days"
msgstr "최근 7일"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Last Month"
msgstr "전 월"

#. module: project
#: model:ir.model.fields,field_description:project.field_report_project_task_user__rating_last_value
msgid "Last Rating (1-5)"
msgstr "최근 점수 (1-5)"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__date_last_stage_update
#: model:ir.model.fields,field_description:project.field_report_project_task_user__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Last Stage Update"
msgstr "최근 단계 업데이트"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__write_date
msgid "Last Updated On"
msgstr "최근 업데이트"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_uid
#: model:ir.model.fields,field_description:project.field_project_milestone__write_uid
#: model:ir.model.fields,field_description:project.field_project_project__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_uid
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_tags__write_uid
#: model:ir.model.fields,field_description:project.field_project_task__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type__write_uid
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_uid
#: model:ir.model.fields,field_description:project.field_project_update__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_collaborator__write_date
#: model:ir.model.fields,field_description:project.field_project_milestone__write_date
#: model:ir.model.fields,field_description:project.field_project_project__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage__write_date
#: model:ir.model.fields,field_description:project.field_project_project_stage_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_share_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_tags__write_date
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__write_date
#: model:ir.model.fields,field_description:project.field_project_task_stage_personal__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type__write_date
#: model:ir.model.fields,field_description:project.field_project_task_type_delete_wizard__write_date
#: model:ir.model.fields,field_description:project.field_project_update__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Late Activities"
msgstr "지연된 활동"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Late Milestones"
msgstr "최근 마일스톤"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_4
#: model:project.task.type,name:project.project_personal_stage_demo_4
msgid "Later"
msgstr "이전"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Leave a comment"
msgstr "의견을 남겨주세요"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>project</b>."
msgstr "첫번째 <b>프로젝트</b>를 생성합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>stage</b>."
msgstr "첫 번째 <b>단계</b>를 생성합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your first <b>task</b>."
msgstr "첫번째 <b>작업</b>을 생성합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's create your second <b>stage</b>."
msgstr "두 번째 <b>단계</b>를 생성합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Let's go back to the <b>kanban view</b> to have an overview of your next "
"tasks."
msgstr "다음 작업을 검토하기 위해 <b>칸반 보기</b>로 돌아갑니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Let's start working on your task."
msgstr "작업을 시작합니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "Let's wait for your customers to manifest themselves."
msgstr "고객이 분명히 의사 표시를 할 때까지 기다려봅니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Live"
msgstr "실시간"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "로컬 부품 기반 입고 감지"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Logo Design"
msgstr "로고 디자인"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Look for the"
msgstr "검색"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__priority__0
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__priority__0
msgid "Low"
msgstr "낮음"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Manage the lifecycle of your project using the kanban view. Add newly acquired projects,\n"
"      assign them and use the"
msgstr ""
"칸반 보기를 통하여 프로젝트의 수명 주기를 관리합니다. 새로 시작된 프로젝트를 추가하여,\n"
"     할당하고 다음에 사용합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Manufacturing"
msgstr "제조 관리"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
msgid "Mark as done"
msgstr "완료"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Mark as incomplete"
msgstr "완료되지 않음으로 표시"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.xml:0
msgid "Mark as reached"
msgstr "도착으로 표시"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Mark the task as <b>Cancelled</b>"
msgstr "작업을 <b>취소됨</b>으로 표시"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Material Sourcing"
msgstr "자재 수급"

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
msgid ""
"Measure your customer satisfaction by sending rating requests when your "
"tasks reach a certain stage."
msgstr "작업이 특정 단계에 도달하면 평가 요청을 전송하여 고객 만족도를 측정합니다."

#. module: project
#: model:project.tags,name:project.project_tags_15
msgid "Meeting"
msgstr "회의"

#. module: project
#: model:ir.model,name:project.model_ir_ui_menu
msgid "Menu"
msgstr "메뉴"

#. module: project
#: model:ir.model,name:project.model_mail_message
msgid "Message"
msgstr "메시지"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_ids
#: model:ir.model.fields,field_description:project.field_project_project__message_ids
#: model:ir.model.fields,field_description:project.field_project_task__message_ids
#: model:ir.model.fields,field_description:project.field_project_update__message_ids
msgid "Messages"
msgstr "메시지"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_right_side_panel/components/project_milestone.js:0
#: model:ir.model.fields,field_description:project.field_project_task__milestone_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__milestone_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__milestone_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Milestone"
msgstr "마일스톤"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
#: model:ir.model.fields,field_description:project.field_project_project__allow_milestones
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_milestone
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Milestones"
msgstr "마일스톤"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Mixing"
msgstr "혼합"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__month
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__month
msgid "Months"
msgstr "월"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__my_activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "내 활동 마감일"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "My Deadline"
msgstr "나의 마감일"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Favorites"
msgstr "내 즐겨찾기"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "My Projects"
msgstr "내 프로젝트"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_my_task
#: model:ir.ui.menu,name:project.menu_project_management_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "My Tasks"
msgstr "내 작업"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "My Updates"
msgstr "나의 업데이트"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_milestone__name
#: model:ir.model.fields,field_description:project.field_project_project__name
#: model:ir.model.fields,field_description:project.field_project_project_stage__name
#: model:ir.model.fields,field_description:project.field_project_tags__name
#: model:ir.model.fields,field_description:project.field_project_task_type__name
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Name"
msgstr "이름"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Name of the Tasks"
msgstr "작업 이름"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__label_tasks
msgid ""
"Name used to refer to the tasks of your project e.g. tasks, tickets, "
"sprints, etc..."
msgstr "프로젝트의 작업 표시에 사용되는 이름입니다. 예를 들어, 작업, 상담, 스프린트 등에 사용합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Neutral face"
msgstr "중립적인 얼굴"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_list.xml:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
#: model:project.task.type,name:project.project_stage_0
msgid "New"
msgstr "신규"

#. module: project
#: model:project.tags,name:project.project_tags_01
msgid "New Feature"
msgstr "새로운 기능"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
msgid "New Milestone"
msgstr "새로운 마일스톤"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Orders"
msgstr "신규 주문"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_project_calendar/project_project_calendar_controller.js:0
msgid "New Project"
msgstr "새 프로젝트"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Projects"
msgstr "신규프로젝트"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "New Request"
msgstr "새 요청"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_controller.js:0
msgid "New Task"
msgstr "새 작업"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Newest"
msgstr "최신"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
msgid "Next"
msgstr "다음"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Next Activity"
msgstr "다음 활동"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_calendar_event_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "다음 활동 캘린더 행사"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_task__activity_date_deadline
#: model:ir.model.fields,field_description:project.field_project_update__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "다음 활동 마감일"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_summary
#: model:ir.model.fields,field_description:project.field_project_task__activity_summary
#: model:ir.model.fields,field_description:project.field_project_update__activity_summary
msgid "Next Activity Summary"
msgstr "다음 활동 요약"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_type_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_type_id
msgid "Next Activity Type"
msgstr "다음 활동 유형"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Customer"
msgstr "고객이 없습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Milestone"
msgstr "마일스톤 없음"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "No Project"
msgstr "프로젝트 없음"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "No Subject"
msgstr "주제 없음"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid "No activity types found. Let's create one!"
msgstr "활동 유형이 없습니다. 지금 생성해 주세요."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_project_report
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_task
msgid "No customer ratings yet"
msgstr "고객의 평가가 수신되지 않았습니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_project_task_burndown_chart_report
#: model_terms:ir.actions.act_window,help:project.action_project_task_user_tree
msgid "No data yet!"
msgstr "데이터가 아직 없습니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid "No projects found. Let's create one!"
msgstr "프로젝트가 없습니다. 새로 작성해 주세요."

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_task_type_form
#: model_terms:ir.actions.act_window,help:project.project_project_stage_configure
msgid "No stages found. Let's create one!"
msgstr "단계가 없습니다. 지금 생성합니다!"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "No tags found. Let's create one!"
msgstr "태그가 없습니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.act_project_project_2_project_task_all
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.action_view_task
#: model_terms:ir.actions.act_window,help:project.action_view_task_from_milestone
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_recurring_tasks_action
#: model_terms:ir.actions.act_window,help:project.project_task_action_from_partner
#: model_terms:ir.actions.act_window,help:project.project_task_action_sub_task
msgid "No tasks found. Let's create one!"
msgstr "작업이 없습니다. 새로 작성해 주세요."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_update_all_action
msgid "No updates found. Let's create one!"
msgstr "업데이트가 없습니다. 새로 작성해 주세요."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "None"
msgstr "없음"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Not Implemented."
msgstr "구현되지 않음"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__note
msgid "Note"
msgstr "노트"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,field_description:project.field_project_update__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_project__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_task__message_needaction_counter
#: model:ir.model.fields,help:project.field_project_update__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_project__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_task__message_has_error_counter
#: model:ir.model.fields,help:project.field_project_update__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__off_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__off_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Off Track"
msgstr "오프 트랙"

#. module: project
#: model:project.tags,name:project.project_tags_10
msgid "Office"
msgstr "사무실"

#. module: project
#: model:account.analytic.account,name:project.analytic_office_design
#: model:project.project,name:project.project_project_1
msgid "Office Design"
msgstr "사무실 디자인"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Old Completed Sprint"
msgstr "예전에 완료된 스프린트"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_hold
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_hold
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Hold"
msgstr "대기 중"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__on_track
#: model:ir.model.fields.selection,name:project.selection__project_update__status__on_track
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "On Track"
msgstr "온 트랙"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__monthly
msgid "Once a Month"
msgstr "월 1회"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Only jpeg, png, bmp and tiff images are allowed as attachments."
msgstr "jpeg, png, bmp 및 tiff 이미지만 첨부할 수 있습니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Oops! Something went wrong. Try to reload the page and log in."
msgstr "오, 이런! 뭔가 잘못됐어요. 페이지를 다시 불러오고 재접속하세요."

#. module: project
#: model:ir.model.fields,field_description:project.field_digest_digest__kpi_project_task_opened
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Open Tasks"
msgstr "작업 열기"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Open sub-tasks notebook section"
msgstr "하위 작업 노트북 섹션 열기"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__is_closed__open
msgid "Open tasks"
msgstr "작업 열기"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Operation not supported"
msgstr "지원되지 않는 작업"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"회신하지 않은 것까지 모든 받는 메시지를 첨부할 스레드(레코드)의 선택사항 ID. 설정하면 완벽하게 새로운 레코드의 생성을 억제합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Organize priorities amongst orders using the"
msgstr "주문 간의 우선 순위를 정리하려면"

#. module: project
#: model_terms:ir.actions.act_window,help:project.action_view_all_task
#: model_terms:ir.actions.act_window,help:project.action_view_my_task
#: model_terms:ir.actions.act_window,help:project.project_task_action_from_partner
msgid ""
"Organize your tasks by dispatching them across the pipeline.<br>\n"
"                    Collaborate efficiently by chatting in real-time or via email."
msgstr ""
"파이프라인 전반에 걸쳐 작업을 보내어 체계적으로 작업을 관리하세요.<br>\n"
"                    실시간 채팅이나 이메일을 통하여 효율적인 방식으로 협업할 수 있습니다."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Others"
msgstr "기타"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Overdue"
msgstr "기한 초과"

#. module: project
#: model:ir.actions.act_window,name:project.action_view_task_overpassed_draft
msgid "Overpassed Tasks"
msgstr "마감기한이 지난 작업"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Page Ideas"
msgstr "페이지 아이디어"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_model_id
msgid "Parent Model"
msgstr "상위 모델"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "상위 레코드 스레드 ID"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__parent_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__parent_id
msgid "Parent Task"
msgstr "상위 작업"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"별칭이 있는 상위 모델입니다. 별칭 참조를 포함하는 모델은 반드시 alias_model_id(예: 프로젝트(parent_model) 및 "
"태스크(model)에서 제공하는 모델일 필요는 없습니다.)"

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid ""
"Partner company cannot be different from its assigned projects' company"
msgstr "협력사는 프로젝트에 배정된 회사와 다를 수 없습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/res_partner.py:0
msgid "Partner company cannot be different from its assigned tasks' company"
msgstr "협력사는 해당 업무에 배정된 회사와 다를 수 없습니다."

#. module: project
#: model:ir.actions.act_window,name:project.project_task_action_from_partner
msgid "Partner's Tasks"
msgstr "협력사의 업무"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"People invited to collaborate on the project will have portal access rights."
msgstr "프로젝트에 협업하도록 초대한 사람들에게는 포탈 접근 권한을 부여합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__privacy_visibility
#: model:ir.model.fields,help:project.field_project_task__project_privacy_visibility
msgid ""
"People to whom this project and its tasks will be visible.\n"
"\n"
"- Invited internal users: when following a project, internal users will get access to all of its tasks without distinction. Otherwise, they will only get access to the specific tasks they are following.\n"
" A user with the project > administrator access right level can still access this project and its tasks, even if they are not explicitly part of the followers.\n"
"\n"
"- All internal users: all internal users can access the project and all of its tasks without distinction.\n"
"\n"
"- Invited portal users and all internal users: all internal users can access the project and all of its tasks without distinction.\n"
"When following a project, portal users will only get access to the specific tasks they are following.\n"
"\n"
"When a project is shared in read-only, the portal user is redirected to their portal. They can view the tasks they are following, but not edit them.\n"
"When a project is shared in edit, the portal user is redirected to the kanban and list views of the tasks. They can modify a selected number of fields on the tasks.\n"
"\n"
"In any case, an internal user with no project access rights can still access a task, provided that they are given the corresponding URL (and that they are part of the followers if the project is private)."
msgstr ""
"이 프로젝트 및 관련 작업을 볼 수 있는 사람.\n"
"\n"
"- 초대 받은 내부 사용자: 프로젝트를 팔로우하면 내부 사용자는 동등하게 어떤 작업에나 접근할 수 있습니다. 그렇지 않은 경우, 팔로우하는 특정 작업에만 접근할 수 있습니다.\n"
" 프로젝트 > 관리자 접근 권한 수준을 가진 사용자는 직접 팔로우하지 않은 경우에도 여전히 이 프로젝트 및 관련 작업에 접근할 수 있습니다.\n"
"\n"
"- 모든 내부 사용자: 모든 내부 사용자는 동등하게 프로젝트 및 관련 작업에 접근할 수 있습니다.\n"
"\n"
"- 초대 받은 포털 사용자 및 모든 내부 사용자: 모든 내부 사용자는 프로젝트 및 모든 작업에 동등하게 접근할 수 있습니다.\n"
"프로젝트를 팔로우하는 경우, 포털 사용자는 팔로우 중인 특정 작업에만 액세스할 수 있습니다.\n"
"프로젝트가 읽기 전용으로 공유되면 포털 사용자는 해당 포털로 리디렉션됩니다. 팔로우 중인 작업을 볼 수는 있지만 편집할 수는 없습니다.\n"
"프로젝트가 편집 모드로 공유되면 포털 사용자는 작업의 칸반 및 목록 보기로 리디렉션됩니다. 이들은 작업에서 선택한 수의 필드를 수정할 수 있습니다.\n"
"\n"
"어떤 경우든 프로젝트 접근 권한이 없는 내부 사용자는 해당 URL을 통하여 (또한 비공개 프로젝트인 경우에는 팔로우하는 경우에) 여전히 작업에 접근할 수 있습니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "좋음 평가 비율"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_type_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__personal_stage_type_ids
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Personal Stage"
msgstr "개인 단계"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__personal_stage_id
msgid "Personal Stage State"
msgstr "개인 단계 상태"

#. module: project
#: model:ir.model,name:project.model_project_task_stage_personal
msgid "Personal Task Stage"
msgstr "개인 작업 단계"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "Planned Date"
msgstr "계획일"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
msgid ""
"Please remove existing tasks in the project linked to the accounts you want "
"to delete."
msgstr "삭제할 계정에 연결된 프로젝트에서 기존 작업을 제거하십시오."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Podcast and Video Production"
msgstr "팟캐스트 및 동영상 제작"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Mailgateway를 사용하여 문서에 메시지를 게시하는 정책.\n"
"- 모든 사용자: 모든 사용자가 게시할 수 있습니다\n"
"- 협력사: 인증된 협력사만 게시할 수 있습니다\n"
"- 팔로워: 관련 문서의 팔로워 또는 팔로잉 중인 채널의 사용자만 게시할 수 있습니다\n"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_url
#: model:ir.model.fields,field_description:project.field_project_task__access_url
msgid "Portal Access URL"
msgstr "포털 접근 URL"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"Portal users will be removed from the followers of the project and its "
"tasks."
msgstr "포털 사용자를 프로젝트 및 관련 작업 팔로워에서 삭제합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_pager.xml:0
msgid "Previous"
msgstr "이전"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Prioritize your tasks by marking important ones using the"
msgstr "중요한 작업을 표시하여 작업의 우선순위를 정하세요"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__priority
#: model:ir.model.fields,field_description:project.field_report_project_task_user__priority
#: model:project.tags,name:project.project_tags_16
msgid "Priority"
msgstr "우선 순위"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks_priority_widget_template
msgid "Priority: {{'Important' if task.priority == '1' else 'Normal'}}"
msgstr "우선순위: {{'Important' if task.priority == '1' else 'Normal'}}"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.js:0
#: code:addons/project/static/src/components/project_many2one_field/project_many2one_field.xml:0
#: code:addons/project/static/src/views/project_task_calendar/project_task_calendar_model.js:0
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Private"
msgstr "비공개"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Private Tasks"
msgstr "비공개 작업"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"Private tasks cannot be converted into sub-tasks. Please set a project on "
"the task to gain access to this feature."
msgstr "비공개 작업은 하위 작업으로 변환할 수 없습니다. 해당 기능에 액세스하려면 작업에 프로젝트를 설정하세요."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.js:0
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Profitability"
msgstr "수익성"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_update__progress
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.project_update_view_tree
msgid "Progress"
msgstr "진행"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model,name:project.model_project_project
#: model:ir.model.fields,field_description:project.field_project_milestone__project_id
#: model:ir.model.fields,field_description:project.field_project_task__project_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__project_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__project_id
#: model:ir.ui.menu,name:project.menu_main_pm
#: model_terms:ir.ui.view,arch_db:project.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:project.task_type_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_fsm_base
msgid "Project"
msgstr "프로젝트"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__account_id
msgid "Project Account"
msgstr "프로젝트 계정"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__user_id
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Project Manager"
msgstr "프로젝트 관리자"

#. module: project
#: model:ir.model,name:project.model_project_milestone
msgid "Project Milestone"
msgstr "프로젝트 마일스톤"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_activity
#: model_terms:ir.ui.view,arch_db:project.project_view_kanban
msgid "Project Name"
msgstr "프로젝트 이름"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_active
msgid "Project Rating Status"
msgstr "프로젝트 평가 상태"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action
#: model:ir.model,name:project.model_project_share_wizard
msgid "Project Sharing"
msgstr "공유 프로젝트"

#. module: project
#: model:ir.model,name:project.model_project_share_collaborator_wizard
msgid "Project Sharing Collaborator Wizard"
msgstr "프로젝트 공유 공동 작업자 마법사"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_recurring_tasks_action
msgid "Project Sharing Recurrence"
msgstr "프로젝트 공유 반복"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "Project Sharing: Task"
msgstr "공유 프로젝트: 작업"

#. module: project
#: model:ir.model,name:project.model_project_project_stage
msgid "Project Stage"
msgstr "프로젝트 단계"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_stage_change
msgid "Project Stage Changed"
msgstr "프로젝트 단계 변경"

#. module: project
#: model:ir.model,name:project.model_project_project_stage_delete_wizard
msgid "Project Stage Delete Wizard"
msgstr "프로젝트 단계 삭제 마법사"

#. module: project
#: model:ir.actions.act_window,name:project.project_project_stage_configure
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_stages
#: model:ir.ui.menu,name:project.menu_project_config_project_stage
msgid "Project Stages"
msgstr "프로젝트 단계"

#. module: project
#: model:ir.model,name:project.model_project_tags
msgid "Project Tags"
msgstr "프로젝트 태그"

#. module: project
#: model:ir.model,name:project.model_project_task_type_delete_wizard
msgid "Project Task Stage Delete Wizard"
msgstr "프로젝트 작업 단계 삭제 마법사"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_activity
msgid "Project Tasks"
msgstr "프로젝트 작업"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "Project Title"
msgstr "프로젝트 제목"

#. module: project
#: model:ir.model,name:project.model_project_update
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "Project Update"
msgstr "프로젝트 업데이트"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__project_privacy_visibility
msgid "Project Visibility"
msgstr "프로젝트 표시"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "Project description..."
msgstr "프로젝트 설명"

#. module: project
#. odoo-python
#: code:addons/project/wizard/project_share_wizard.py:0
msgid "Project shared with your collaborators."
msgstr "프로젝트를 공동 작업자와 공유합니다."

#. module: project
#: model:mail.template,subject:project.project_done_email_template
msgid "Project status - {{ object.name }}"
msgstr "프로젝트 단계 - {{ object.name }}"

#. module: project
#: model:ir.actions.act_window,name:project.dblc_proj
msgid "Project's tasks"
msgstr "프로젝트의 작업"

#. module: project
#: model:mail.template,name:project.project_done_email_template
msgid "Project: Project Completed"
msgstr "프로젝트: 완료된 프로젝트"

#. module: project
#: model:mail.template,name:project.mail_template_data_project_task
msgid "Project: Request Acknowledgment"
msgstr "프로젝트: 요청 확인"

#. module: project
#: model:ir.actions.server,name:project.ir_cron_rating_project_ir_actions_server
msgid "Project: Send rating"
msgstr "프로젝트 : 평가 보내기"

#. module: project
#: model:mail.template,name:project.rating_project_request_email_template
msgid "Project: Task Rating Request"
msgstr "프로젝트: 작업 평가 요청"

#. module: project
#. odoo-python
#: code:addons/project/models/account_analytic_account.py:0
#: model:ir.actions.act_window,name:project.open_view_project_all
#: model:ir.actions.act_window,name:project.open_view_project_all_config
#: model:ir.actions.act_window,name:project.open_view_project_all_config_group_stage
#: model:ir.actions.act_window,name:project.open_view_project_all_group_stage
#: model:ir.model.fields,field_description:project.field_project_task_type__project_ids
#: model:ir.ui.menu,name:project.menu_projects
#: model:ir.ui.menu,name:project.menu_projects_config
#: model:ir.ui.menu,name:project.menu_projects_config_group_stage
#: model:ir.ui.menu,name:project.menu_projects_group_stage
#: model_terms:ir.ui.view,arch_db:project.account_analytic_account_view_form_inherit
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
msgid "Projects"
msgstr "프로젝트"

#. module: project
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_config_group_stage
#: model_terms:ir.actions.act_window,help:project.open_view_project_all_group_stage
msgid ""
"Projects contain tasks on the same topic, and each has its own dashboard."
msgstr "프로젝트에는 동일한 주제의 작업이 포함되어 있으며, 각각의 고유 현황판이 있습니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_task_type__project_ids
msgid ""
"Projects in which this stage is present. If you follow a similar workflow in"
" several projects, you can share this stage among them and get consolidated "
"information this way."
msgstr ""
"이 단계가 속해 있는 프로젝트입니다. 여러 프로젝트에서 비슷한 작업 단계를 수행하는 경우, 이 단계를 공유하여 이러한 방식으로 통합된 "
"정보를 확인할 수 있습니다."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__task_properties
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Properties"
msgstr "속성"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__share_link
msgid "Public Link"
msgstr "공개 링크"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Published"
msgstr "게시 됨"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Published on"
msgstr "게시일"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Publishing"
msgstr "게시"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__quarterly
msgid "Quarterly"
msgstr "분기별"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid ""
"Quickly check the status of tasks for approvals or change requests and "
"identify those on hold until dependencies are resolved with the hourglass "
"icon."
msgstr ""
"작업 상태를 신속하게 확인하여 승인이나 변경 요청에 대응하며, 종속성 문제 때문에 보류 중인 작업은 모래시계 아이콘으로 구분합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_base
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Rating"
msgstr "평가"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
msgid "Rating (/5)"
msgstr "평가 (/5)"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_graph
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_pivot
msgid "Rating (1-5)"
msgstr "평점 (1-5)"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_avg_text
msgid "Rating Avg Text"
msgstr "평균 평가 텍스트"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__rating_template_id
msgid "Rating Email Template"
msgstr "평가용 이메일 서식"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_status_period
msgid "Rating Frequency"
msgstr "평가 주기"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "최근 의견의 평가"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_image
msgid "Rating Last Image"
msgstr "최근 이미지 평가"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_value
msgid "Rating Last Value"
msgstr "최근 값 평가"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:project.field_project_task__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "만족도 평가"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_last_text
msgid "Rating Text"
msgstr "평가 텍스트"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__rating_count
msgid "Rating count"
msgstr "평가 수 "

#. module: project
#: model:ir.actions.act_window,name:project.rating_rating_action_task
#: model:ir.actions.act_window,name:project.rating_rating_action_view_project_rating
#: model:ir.model.fields,field_description:project.field_project_milestone__rating_ids
#: model:ir.model.fields,field_description:project.field_project_project__rating_ids
#: model:ir.model.fields,field_description:project.field_project_task__rating_ids
#: model:ir.model.fields,field_description:project.field_project_update__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__is_reached
msgid "Reached"
msgstr "달성함"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_share_collaborator_wizard__access_mode__read
msgid "Read"
msgstr "읽기"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
msgid "Read: collaborators can view tasks but cannot edit them."
msgstr "읽기 전용: 공동 작업자는 작업을 볼 수는 있지만 편집할 수는 없습니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_share_collaborator_wizard__access_mode
msgid ""
"Read: collaborators can view tasks but cannot edit them.\n"
"Edit with limited access: collaborators can view and edit tasks they follow in the Kanban view.\n"
"Edit: collaborators can view and edit all tasks in the Kanban view. Additionally, they can choose which tasks they want to follow."
msgstr ""
"읽기: 공동 작업자는 편집 권한은 없으며, 작업을 볼 수 있습니다.\n"
"편집 액세스 제한: 공동 작업자는 칸반 보기 내에서 자신이 팔로우하는 작업을 보고 편집할 수 있습니다.\n"
"편집:  공동 작업자는 칸반 보기 내에서 모든 작업을 보고 편집할 수 있습니다. 또한 팔로우할 작업을 선택할 수도 있습니다."

#. module: project
#: model:mail.template,subject:project.mail_template_data_project_task
msgid "Reception of {{ object.name }}"
msgstr "다음을 수신함 {{ object.name }}"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__partner_ids
msgid "Recipients"
msgstr "수신인"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__alias_force_thread_id
msgid "Record Thread ID"
msgstr "레코드 스레드 ID"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Recording"
msgstr "기록중"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurrence_id
msgid "Recurrence"
msgstr "반복"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__recurring_task
msgid "Recurrent"
msgstr "반복"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_layout
msgid "Recurrent tasks"
msgstr "반복 작업"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_recurring_tasks
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Recurring Tasks"
msgstr "반복되는 작업"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Refused"
msgstr "반려됨"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__resource_ref
msgid "Related Document"
msgstr "관련 문서"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_id
msgid "Related Document ID"
msgstr "관련 문서 ID"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_wizard__res_model
msgid "Related Document Model"
msgstr "관련 문서 모델"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
msgid "Remove Collaborator"
msgstr "공동 작업자 삭제"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.xml:0
msgid "Rename"
msgstr "이름 바꾸기"

#. module: project
#: model:account.analytic.account,name:project.analytic_renovations
#: model:project.project,name:project.project_project_3
msgid "Renovations"
msgstr "개조"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_interval
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_interval
msgid "Repeat Every"
msgstr "계속 반복"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_unit
msgid "Repeat Unit"
msgstr "반복되는 단위"

#. module: project
#: model:ir.ui.menu,name:project.menu_project_report
msgid "Reporting"
msgstr "보고"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Research"
msgstr "연구"

#. module: project
#: model:account.analytic.account,name:project.analytic_research_development
#: model:project.project,name:project.project_project_2
msgid "Research & Development"
msgstr "연구 개발"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Research Project"
msgstr "연구 프로젝트"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Researching"
msgstr "연구중"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Resources Allocation"
msgstr "자원 배분"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_task__activity_user_id
#: model:ir.model.fields,field_description:project.field_project_update__activity_user_id
msgid "Responsible User"
msgstr "담당 사용자"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "Restore"
msgstr "복원"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid ""
"Restoring will replace the current content with the selected version. Any "
"unsaved changes will be lost."
msgstr "복원하면 현재 콘텐츠가 선택한 버전으로 변경됩니다. 저장하지 않은 변경 내용은 모두 손실됩니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Revenues"
msgstr "수익"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/subtask_kanban_list/subtask_kanban_create/subtask_kanban_create.xml:0
msgid "SAVE"
msgstr "저장"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_project__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_task__message_has_sms_error
#: model:ir.model.fields,field_description:project.field_project_update__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Sad face"
msgstr "슬픈 표정"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
msgid "Save the task to be able to drag images in description"
msgstr "설명에서 이미지를 드래그할 수 있도록 작업을 저장합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/views/form/project_sharing_form_controller.js:0
msgid "Save the task to be able to paste images in description"
msgstr "설명에 이미지를 붙여넣을 수 있도록 작업을 저장합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Schedule your activity once it is ready."
msgstr "준비가 완료되면 활동을 예약합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Script"
msgstr "스크립트"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Search Project"
msgstr "프로젝트 검색"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_search
msgid "Search Update"
msgstr "업데이트 검색"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Assignees"
msgstr "담당자 검색"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Customer"
msgstr "고객 검색"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Milestone"
msgstr "마일스톤 검색"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Priority"
msgstr "우선순위 검색"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Project"
msgstr "프로젝트에서 검색"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Stages"
msgstr "단계별 검색"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search in Status"
msgstr "상태 검색"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "Search%(left)s Tasks%(right)s"
msgstr "검색%(left)s의 작업 %(right)s"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__access_token
#: model:ir.model.fields,field_description:project.field_project_task__access_token
msgid "Security Token"
msgstr "보안 토큰"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "Select an assignee from the menu"
msgstr "메뉴에서 담당자를 선택합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Send"
msgstr "보내기"

#. module: project
#: model:ir.actions.act_window,name:project.action_send_mail_project_project
#: model:ir.actions.act_window,name:project.action_send_mail_project_task
msgid "Send Email"
msgstr "이메일 보내기"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_share_collaborator_wizard__send_invitation
msgid "Send Invitation"
msgstr "초대장 전송"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__sequence
msgid "Sequence"
msgstr "순서"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Set Cover Image"
msgstr "표지 이미지 설정"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.model.fields.selection,name:project.selection__project_project__last_update_status__to_define
msgid "Set Status"
msgstr "상태 설정"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Set a Rating Email Template on Stages"
msgstr "단계의 평가 이메일 양식 설정"

#. module: project
#: model:mail.template,description:project.project_done_email_template
msgid ""
"Set on project's stages to inform customers when a project reaches that "
"stage"
msgstr "프로젝트 상태를 설정하여 프로젝트가 해당 단계에 도달하면 고객에게 안내합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_priority_switch_field/project_task_priority_switch_field.js:0
msgid "Set priority as %s"
msgstr "우선순위를 %s로 설정 "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.js:0
msgid "Set state as..."
msgstr "상태를 다음으로 설정"

#. module: project
#: model:mail.template,description:project.rating_project_request_email_template
msgid ""
"Set this template on a project stage to request feedback from your "
"customers. Enable the \"customer ratings\" feature on the project"
msgstr "프로젝트 단계에서 서식을 설정하여 고객에게 피드백을 요청하세요. 프로젝트에서 \"고객 평가\" 기능을 활성화하십시오."

#. module: project
#: model:ir.actions.act_window,name:project.project_config_settings_action
#: model:ir.ui.menu,name:project.project_config_settings_menu_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Settings"
msgstr "설정"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.project_share_wizard_action
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_view_form
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "Share Project"
msgstr "프로젝트 공유"

#. module: project
#: model:ir.actions.act_window,name:project.portal_share_action
#: model_terms:ir.ui.view,arch_db:project.view_task_kanban
msgid "Share Task"
msgstr "작업 공유"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__show_display_in_project
msgid "Show Display In Project"
msgstr "프로젝트에 디스플레이 표시"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Show all records which has next action date is before today"
msgstr "다음 행동 날짜가 오늘 이전 인 모든 기록보기"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/notebook_task_one2many_field/notebook_task_list_renderer.js:0
msgid "Show closed tasks"
msgstr "종료된 작업 표시"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Since"
msgstr "다음 기간 이후"

#. module: project
#: model:project.tags,name:project.project_tags_12
msgid "Social"
msgstr "소셜"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Software Development"
msgstr "소프트웨어 개발"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_list/analytic_account_list_controller.js:0
msgid ""
"Some of the selected analytic accounts are associated with a project:\n"
"%(accountList)s\n"
"\n"
"Archiving these accounts will remove the option to log timesheets for their respective projects.\n"
"\n"
"Are you sure you want to proceed?"
msgstr ""
"선택한 분석 계정 중 일부가 프로젝트에 연결되어 있습니다:\n"
"%(accountList)s\n"
"\n"
"이 계정을 보관하면 해당 프로젝트에 대한 작업표 기록 옵션이 제거됩니다.\n"
"\n"
"계속 진행하시겠습니까?"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Sorry. You can't set a task as its parent task."
msgstr "죄송합니다. 이 작업의 상위작업으로 설정할 수 없습니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"Sort your tasks by sprint using milestones, tags, or a dedicated property. "
"At the end of each sprint, just pick the remaining tasks in your list and "
"move them all at once to the next sprint by editing the milestone, tag, or "
"property."
msgstr ""
"마일스톤, 태그 또는 특정 속성을 사용하여 스프린트별로 작업을 정리하세요. 각 스프린트가 끝나면 마일스톤, 태그 또는 속성을 업데이트하여"
" 남은 작업을 한 번에 모두 다음 스프린트로 옮깁니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Specifications"
msgstr "사양"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint Backlog"
msgstr "스프린트의 백로그"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint Complete"
msgstr "완료된 스프린트"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Sprint in Progress"
msgstr "진행중인 스프린트"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_project__stage_id
#: model:ir.model.fields,field_description:project.field_project_task__stage_id
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__stage_id
#: model:ir.model.fields,field_description:project.field_report_project_task_user__stage_id
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Stage"
msgstr "단계"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
msgid "Stage (Burndown Chart)"
msgstr "단계 (번다운 차트)"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_stage
msgid "Stage Changed"
msgstr "단계 변경됨"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task_type__user_id
msgid "Stage Owner"
msgstr "단계 소유자"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_stage
msgid "Stage changed"
msgstr "단계 변경됨"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Stage: %s"
msgstr "단계: %s"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Starred Tasks"
msgstr "별표 표시된 작업"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__date_start
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Start Date"
msgstr "시작일"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__state
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__state
#: model:ir.model.fields,field_description:project.field_report_project_task_user__state
msgid "State"
msgstr "시/도"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_stage_state_selection/project_task_stage_with_state_selection.js:0
msgid "State readonly"
msgstr "읽기 전용 상태"

#. module: project
#. odoo-javascript
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: code:addons/project/static/src/components/project_status_with_color_selection/project_status_with_color_selection_field.xml:0
#: model_terms:ir.ui.view,arch_db:project.view_project
#: model_terms:ir.ui.view,arch_db:project.view_project_calendar
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
msgid "Status"
msgstr "상태"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Status Update - %(date)s"
msgstr "상태 업데이트 - %(date)s"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_state
#: model:ir.model.fields,help:project.field_project_task__activity_state
#: model:ir.model.fields,help:project.field_project_update__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"활동 기준 상태\n"
"기한 초과: 기한이 이미 지났습니다.\n"
"오늘: 활동 날짜가 오늘입니다.\n"
"예정: 향후 계획된 활동입니다."

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__duration_tracking
#: model:ir.model.fields,field_description:project.field_project_task__duration_tracking
msgid "Status time"
msgstr "상태 시간"

#. module: project
#: model:ir.actions.act_window,name:project.project_sharing_project_task_action_sub_task
#: model:ir.actions.act_window,name:project.project_task_action_sub_task
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Sub-tasks"
msgstr "하위 작업"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
msgid "Submitted On"
msgstr "제출 날짜"

#. module: project
#: model:ir.model.fields,help:project.field_project_task__subtask_allocated_hours
msgid ""
"Sum of the hours allocated for all the sub-tasks (and their own sub-tasks) "
"linked to this task. Usually less than or equal to the allocated hours of "
"this task."
msgstr ""
"이 작업에 연결되어 있는 모든 하위 작업 (및 자체 하위 작업)에 할당되어 있는 총 합계 시간입니다. 일반적으로 이 작업에 할당된 시간과"
" 같거나 적습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "Summary"
msgstr "요약"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "T-shirt Printing"
msgstr "티셔츠 인쇄"

#. module: project
#: model:ir.actions.act_window,name:project.project_tags_action
#: model:ir.model.fields,field_description:project.field_project_project__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task__tag_ids
#: model:ir.model.fields,field_description:project.field_project_task_burndown_chart_report__tag_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__tag_ids
#: model:ir.ui.menu,name:project.menu_project_tags_act
#: model_terms:ir.ui.view,arch_db:project.project_tags_form_view
#: model_terms:ir.ui.view,arch_db:project.project_tags_tree_view
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tags"
msgstr "태그"

#. module: project
#: model:ir.model,name:project.model_project_task
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__task_ids
#: model:ir.model.fields,field_description:project.field_report_project_task_user__name
#: model_terms:ir.ui.view,arch_db:project.portal_my_task
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_form_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_search_project
#: model_terms:ir.ui.view,arch_db:project.rating_rating_view_tree_project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task"
msgstr "작업"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__tasks
msgid "Task Activities"
msgstr "작업 활동"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_approved
#: model:mail.message.subtype,name:project.mt_task_approved
msgid "Task Approved"
msgstr "승인된 작업"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_canceled
msgid "Task Canceled"
msgstr "작업 취소됨"

#. module: project
#: model:mail.message.subtype,name:project.mt_task_canceled
msgid "Task Cancelled"
msgstr "작업 취소됨"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Task Converted from To-Do"
msgstr "할 일에서 전환된 작업"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_new
#: model:mail.message.subtype,name:project.mt_project_task_new
#: model:mail.message.subtype,name:project.mt_task_new
msgid "Task Created"
msgstr "작업 생성됨"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__allow_task_dependencies
#: model:ir.model.fields,field_description:project.field_res_config_settings__group_project_task_dependencies
msgid "Task Dependencies"
msgstr "작업 종속성"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_done
#: model:mail.message.subtype,name:project.mt_task_done
msgid "Task Done"
msgstr "작업 완료"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_in_progress
#: model:mail.message.subtype,name:project.mt_project_task_in_progress
#: model:mail.message.subtype,name:project.mt_task_in_progress
msgid "Task In Progress"
msgstr "진행 중인 작업"

#. module: project
#: model:ir.model.fields,field_description:project.field_res_config_settings__module_hr_timesheet
msgid "Task Logs"
msgstr "작업 기록"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__task_properties_definition
msgid "Task Properties"
msgstr "작업 속성"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_rating
#: model:mail.message.subtype,name:project.mt_task_rating
msgid "Task Rating"
msgstr "작업 평가"

#. module: project
#: model:ir.model,name:project.model_project_task_recurrence
msgid "Task Recurrence"
msgstr "반복되는 작업"

#. module: project
#: model:ir.model,name:project.model_project_task_type
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "Task Stage"
msgstr "작업 단계"

#. module: project
#: model:mail.message.subtype,name:project.mt_project_task_stage
msgid "Task Stage Changed"
msgstr "작업 단계가 변경됨"

#. module: project
#: model:ir.actions.act_window,name:project.open_task_type_form
#: model:ir.actions.act_window,name:project.open_task_type_form_domain
#: model:ir.ui.menu,name:project.menu_project_config_project
msgid "Task Stages"
msgstr "작업 단계"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "Task Title"
msgstr "작업명"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Task Title..."
msgstr "작업명..."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"Task Transferred from Project %(source_project)s to %(destination_project)s"
msgstr "프로젝트 %(source_project)s에서 %(destination_project)s로 작업이 전송되었습니다."

#. module: project
#: model:mail.message.subtype,description:project.mt_task_waiting
#: model:mail.message.subtype,name:project.mt_project_task_waiting
#: model:mail.message.subtype,name:project.mt_task_waiting
msgid "Task Waiting"
msgstr "대기 중인 작업"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_approved
msgid "Task approved"
msgstr "승인된 작업"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_canceled
msgid "Task cancelled"
msgstr "작업 취소됨"

#. module: project
#: model:mail.message.subtype,description:project.mt_task_done
msgid "Task done"
msgstr "완료된 작업"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_track_depending_tasks
msgid "Task:"
msgstr "작업 :"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
msgid "Task: Rating Request"
msgstr "작업: 평가 요청"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
#: model:ir.actions.act_window,name:project.act_project_project_2_project_task_all
#: model:ir.actions.act_window,name:project.action_view_task
#: model:ir.actions.act_window,name:project.action_view_task_from_milestone
#: model:ir.embedded.actions,name:project.project_embedded_action_all_tasks_dashboard
#: model:ir.model.fields,field_description:project.field_report_project_task_user__task_id
#: model:ir.ui.menu,name:project.menu_project_management
#: model:project.project,label_tasks:project.project_home_construction
#: model:project.project,label_tasks:project.project_project_1
#: model:project.project,label_tasks:project.project_project_2
#: model:project.project,label_tasks:project.project_project_3
#: model:project.project,label_tasks:project.project_project_4
#: model_terms:ir.ui.view,arch_db:project.portal_layout
#: model_terms:ir.ui.view,arch_db:project.portal_my_home
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
#: model_terms:ir.ui.view,arch_db:project.project_update_view_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_project_task_graph
#: model_terms:ir.ui.view,arch_db:project.view_project_task_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_calendar
#: model_terms:ir.ui.view,arch_db:project.view_task_partner_info_form
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Tasks"
msgstr "작업"

#. module: project
#: model:ir.actions.act_window,name:project.action_project_task_user_tree
#: model:ir.model,name:project.model_report_project_task_user
#: model:ir.ui.menu,name:project.menu_project_report_task_analysis
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_graph
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_pivot
#: model_terms:ir.ui.view,arch_db:project.view_task_project_user_search
msgid "Tasks Analysis"
msgstr "작업 분석"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Tasks Management"
msgstr "작업 관리"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_type_search
msgid "Tasks Stages"
msgstr "작업 단계"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:ir.model.fields,field_description:project.field_project_task__recurring_count
msgid "Tasks in Recurrence"
msgstr "반복되는 자겅ㅂ"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Tests"
msgstr "테스트"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
msgid "The Burndown Chart must be grouped by Date"
msgstr "번다운 차트는 날짜별로 그룹화해야 합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_id
msgid "The current user's personal stage."
msgstr "현재 사용자의 개인 단계입니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__personal_stage_type_id
msgid "The current user's personal task stage."
msgstr "현재 사용자의 개인 작업 단계입니다."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid ""
"The document does not exist or you do not have the rights to access it."
msgstr "문서가 없거나 문서에 접근할 권한이 없습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
msgid "The end date should be in the future"
msgstr "종료일은 미래의 날짜여야 합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestone has been added:"
msgstr "다음의 마일스톤이 추가되었습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "The following milestones have been added:"
msgstr "다음의 마일스톤이 추가되었습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_recurrence.py:0
msgid "The interval should be greater than 0"
msgstr "간격은 0보다 큰 숫자여야 합니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"이 별칭에 해당하는 모델(Odoo 문서 종류)입니다. 모든 받는 메일은 기존 레코드로 기록되지 않고 이 모델의 새 레코드로 작성됩니다. "
"(예. 프로젝트 작업)"

#. module: project
#: model:ir.model.fields,help:project.field_project_project__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"<<EMAIL>>에 대한 이메일을 수신하도록 하려면 이메일의 별칭 이름을 적어주세요. 예: 'jobs'"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"The project and the associated partner must be linked to the same company."
msgstr "프로젝트와 관련된 협력사는 동일한 회사에 연결되어 있어야 합니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"The project's company cannot be changed if its analytic account has analytic"
" lines or if more than one project is linked to it."
msgstr "분석 계정에 분석 항목이 있거나 둘 이상의 프로젝트가 연결되어 있는 경우 프로젝트 회사를 변경할 수 없습니다."

#. module: project
#: model:ir.model.constraint,message:project.constraint_project_project_project_date_greater
msgid "The project's start date must be before its end date."
msgstr "프로젝트의 시작일은 종료일 이전의 날짜여야 합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/burndown_chart/burndown_chart_search_model.js:0
msgid ""
"The report should be grouped either by \"Stage\" to represent a Burndown "
"Chart or by \"Is Closed\" to represent a Burn-up chart. Without one of these"
" groupings applied, the report will not provide relevant information."
msgstr ""
"번다운 차트를 나타내려면 보고서를 \"단계\"로 그룹화하거나 번업 차트를 나타내는 \"종료됨\"으로 그룹화해야 합니다. 이러한 그룹 중 "
"하나를 적용하지 않으면 보고서에서 관련 정보를 제공하지 않습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "The search does not support operator %(operator)s or value %(value)s."
msgstr "검색에서는 %(operator)s 연산자나 %(value)s 값이 지원되지 않습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"The task and the associated partner must be linked to the same company."
msgstr "작업과 관련된 협력사는 동일한 회사에 연결되어 있어야 합니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid ""
"The task cannot be shared with the recipient(s) because the privacy of the "
"project is too restricted. Set the privacy of the project to "
"'%(visibility)s' in order to make it accessible by the recipient(s)."
msgstr ""
"프로젝트의 개인 정보가 너무 제한되어 있으므로 작업을 수신자와 공유할 수 없습니다. 수신자가 프로젝트에 접근할 수 있도록 프로젝트의 개인"
" 정보를 '%(visibility)s'로 설정하세요."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_form/project_task_form_controller.js:0
msgid "The task description was empty at the time."
msgstr "당시 작업 설명은 공백이었습니다."

#. module: project
#. odoo-python
#: code:addons/project/report/project_task_burndown_chart_report.py:0
msgid ""
"The view must be grouped by date and by Stage - Burndown chart or Is Closed "
"- Burnup chart"
msgstr "보기는 날짜 및 번다운 차트의 경우 단계별로, 번업 차트의 경우 마감됨을 기준으로 그룹화해야 합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
msgid "There are no comments for now."
msgstr "현재 댓글이 없습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_projects
msgid "There are no projects."
msgstr "프로젝트가 없습니다."

#. module: project
#: model_terms:ir.actions.act_window,help:project.rating_rating_action_view_project_rating
msgid "There are no ratings for this project at the moment"
msgstr "현재 이 프로젝트에 대한 평가가 없습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.portal_my_project
#: model_terms:ir.ui.view,arch_db:project.portal_my_tasks
msgid "There are no tasks."
msgstr "작업이 없습니다."

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
msgid "There is nothing to report."
msgstr "보고할 항목이 없습니다. "

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"They can edit shared project tasks and view specific documents in read mode "
"on your website. This includes leads/opportunities, quotations/sales orders,"
" purchase orders, invoices and bills, timesheets, and tickets."
msgstr ""
"공유 프로젝트를 편집하거나 특정 문서를 웹사이트에서 읽기 전용으로 볼 수 있습니다. 영업제안이나 영업기회, 견적서나 판매주문서, "
"구매발주서, 청구서나 거래처 청구서와 작업시간표와 상담까지 아우릅니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_3
#: model:project.task.type,name:project.project_personal_stage_demo_3
msgid "This Month"
msgstr "이번 달"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_2
#: model:project.task.type,name:project.project_personal_stage_demo_2
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "This Week"
msgstr "이번 주"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/analytic_account_form/analytic_account_form_controller.js:0
msgid ""
"This analytic account is associated with the following projects:\n"
"%(projectList)s\n"
"\n"
"Archiving the account will remove the option to log timesheets for these projects.\n"
"\n"
"Are you sure you want to proceed?"
msgstr ""
"이 분석 계정은 다음 프로젝트와 연결되어 있습니다:\n"
"%(projectList)s\n"
"\n"
"계정을 보관하면 이러한 프로젝트의 작업표를 기록하는 옵션이 제거됩니다.\n"
"\n"
"계속 진행하시겠습니까?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/core/web/follower_list_patch.js:0
msgid ""
"This follower is currently a project collaborator. Removing them will revoke"
" their portal access to the project. Are you sure you want to proceed?"
msgstr ""
"이 팔로워는 현재 프로젝트에 공동 작업자로 참여하고 있습니다. 이 팔로워를 제거하면 프로젝트에 대한 포털 액세스 권한이 사라집니다. 계속"
" 진행하시겠습니까?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid ""
"This is a preview of how the project will look when it's shared with "
"customers and they have editing access."
msgstr "고객에게 공유하여 편집 권한을 가지게 되는 경우 프로젝트가 어떻게 보이는지 확인할 수 있는 미리보기입니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is associated with %(project_company)s, whereas the selected "
"stage belongs to %(stage_company)s. There are a couple of options to "
"consider: either remove the company designation from the project or from the"
" stage. Alternatively, you can update the company information for these "
"records to align them under the same company."
msgstr ""
"이 프로젝트는 %(project_company)s에 연결되어 있으나, 선택한 단계는 %(stage_company)s에 연결되어 있습니다."
" 프로젝트 또는 단계에서 지정된 회사를 삭제하세요. 또는 이러한 레코드에 대한 회사 정보를 업데이트하여 동일한 회사 아래에 정렬하실 수 "
"있습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is currently restricted to \"Invited internal users\". The "
"project's visibility will be changed to \"invited portal users and all "
"internal users (public)\" in order to make it accessible to the recipients."
msgstr ""
"현재 이 프로젝트는 \"초대된 내부 사용자\"로만 제한되어 있습니다. 수신자에게 액세스 권한을 부여하기 위해 프로젝트의 공개 범위가 "
"\"초대된 포털 사용자 및 모든 내부 사용자 (공개)\"로 업데이트됩니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid ""
"This project is not associated with any company, while the stage is "
"associated with %s. There are a couple of options to consider: either change"
" the project's company to align with the stage's company or remove the "
"company designation from the stage"
msgstr ""
"해당 프로젝트에는 회사가 연결되어 있지 않으나, 단계는 %s에 연결되어 있습니다. 몇 가지 옵션 중에서 선택하세요: 프로젝트 회사를 "
"스테이지의 회사에 맞도록 변경하거나 단계에서 회사 지정을 삭제할 수 있습니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "This task has sub-tasks, so it can't be private."
msgstr "이 작업에는 하위 작업이 있으므로 비공개로 설정할 수 없습니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
msgid "This task is blocked by another unfinished task"
msgstr "다른 미완료 작업으로 인해 이 작업이 차단되었습니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/depend_on_ids_one2many/depend_on_ids_list_renderer.xml:0
msgid "This task is currently blocked by"
msgstr "이 작업은 현재 다음 사용자에 의해 차단되었습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_confirmation_wizard
msgid ""
"This will archive the stages and all the tasks they contain from the "
"following projects:"
msgstr "이렇게 하면 다음의 프로젝트에 해당하는 단계 및 전체 작업이 보관 처리됩니다:"

#. module: project
#: model_terms:ir.actions.act_window,help:project.mail_activity_type_action_config_project_types
msgid ""
"Those represent the different categories of things you have to do (e.g. "
"\"Call\" or \"Send email\")."
msgstr "이는 수행해야 하는 작업에 대한 다양한 카테고리를 나타냅니다 (예: \"전화\" 또는 \"이메일 전송\")."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Time Management"
msgstr "일정 관리"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_1
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "Tip: Create tasks from incoming emails"
msgstr "팁: 받은 메일에서 작업을 생성합니다."

#. module: project
#: model:digest.tip,name:project.digest_tip_project_3
#: model_terms:digest.tip,tip_description:project.digest_tip_project_3
msgid "Tip: Project-Specific Fields"
msgstr "팁: 프로젝트별 필드"

#. module: project
#: model:digest.tip,name:project.digest_tip_project_0
#: model_terms:digest.tip,tip_description:project.digest_tip_project_0
msgid "Tip: Use task states to keep track of your tasks' progression"
msgstr "팁: 작업 상태를 사용하여 작업 진행 상황을 추적합니다."

#. module: project
#: model:digest.tip,name:project.digest_tip_project_2
#: model_terms:digest.tip,tip_description:project.digest_tip_project_2
msgid "Tip: Your Own Personal Kanban"
msgstr "팁: 나만의 개인 칸반"

#. module: project
#. odoo-python
#: code:addons/project/controllers/portal.py:0
#: model:ir.model.fields,field_description:project.field_project_task__name
#: model:ir.model.fields,field_description:project.field_project_update__name
#: model_terms:ir.ui.view,arch_db:project.project_task_view_tree_main_base
msgid "Title"
msgstr "제목"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "To Bill"
msgstr "청구할 항목"

#. module: project
#: model:project.project.stage,name:project.project_project_stage_0
msgid "To Do"
msgstr "To Do"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "To Invoice"
msgstr "발행할 청구서"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "To Print"
msgstr "인쇄"

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_blocking_tasks
#: model_terms:ir.actions.act_window,help:project.project_sharing_project_task_action_sub_task
msgid ""
"To get things done, use activities and status on tasks.<br>\n"
"                Chat in real time or by email to collaborate efficiently."
msgstr ""
"완료하시려면, 작업 활동 및 상태를 활용하십시오.<br>\n"
"                실시간 채팅이나 이메일을 통해 효율적으로 함께 작업합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_task_convert_to_subtask_view_form
msgid ""
"To transform a task into a sub-task, select a parent task. Alternatively, "
"leave the parent task field blank to convert a sub-task into a standalone "
"task."
msgstr ""
"작업을 하위 작업으로 전환하려면, 상위 작업을 선택하세요. 또는 상위 작업 필드가 공란으로 비워져 있으면 하위 작업이 독립형 작업으로 "
"전환됩니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
#: model:project.task.type,name:project.project_personal_stage_admin_1
#: model:project.task.type,name:project.project_personal_stage_demo_1
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_search
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_project_base
msgid "Today"
msgstr "오늘"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Today Activities"
msgstr "오늘 활동"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total"
msgstr "총계"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total Costs"
msgstr "총 비용"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/components/project_profitability.xml:0
msgid "Total Revenues"
msgstr "총 수익"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track customer satisfaction on tasks"
msgstr "작업에 대한 고객 만족도 추적"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track major progress points that must be reached to achieve success"
msgstr "성공하기 위해서 반드시 도달해야 하는 주요 진행 사항을 추적합니다. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid "Track major progress points that must be reached to achieve success."
msgstr "성공하기 위해서 반드시 도달해야 하는 주요 진행 사항을 추적합니다. "

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_right_side_panel/project_right_side_panel.xml:0
msgid ""
"Track project costs, revenues, and margin by setting the analytic account "
"associated with the project on relevant documents."
msgstr "프로젝트 비용과 수익, 마진을 추적하세요. 관련 문서에서 프로젝트에 연결되어 있는 분석 계정을 설정합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track the progress of your projects"
msgstr "프로젝트의 진행 상황을 추적합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.res_config_settings_view_form
msgid "Track time spent on projects and tasks"
msgstr "프로젝트 및 작업에 소요된 시간 추적"

#. module: project
#: model:ir.model.fields,help:project.field_project_tags__color
msgid ""
"Transparent tags are not visible in the kanban view of your projects and "
"tasks."
msgstr "투명 태그는 프로젝트 및 작업의 칸반 화면에서 보이지 않습니다."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__bimonthly
msgid "Twice a Month"
msgstr "월 2회"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "Two tasks cannot depend on each other."
msgstr "작업 간에 상호 의존성이 있어서는 안됩니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_project__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_task__activity_exception_decoration
#: model:ir.model.fields,help:project.field_project_update__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "레코드에 있는 예외 활동의 유형입니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
msgid "Unarchive Projects"
msgstr "프로젝트 보관 취소"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task_type.py:0
msgid "Unarchive Tasks"
msgstr "작업 보관 취소"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_pivot/project_pivot_model.js:0
#: model_terms:ir.ui.view,arch_db:project.project_task_burndown_chart_report_view_search
#: model_terms:ir.ui.view,arch_db:project.view_project_project_filter
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form_base
msgid "Unassigned"
msgstr "미지정"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/chatter/portal_chatter_patch.xml:0
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_container.xml:0
msgid "Unfollow"
msgstr "언팔로우"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project.py:0
msgid "Unknown Analytic Account"
msgstr "알 수 없는 분석 계정"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_search_form
msgid "Unread Messages"
msgstr "읽지 않은 메세지"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__repeat_type
#: model:ir.model.fields,field_description:project.field_project_task_recurrence__repeat_type
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_type__until
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_type__until
msgid "Until"
msgstr "반복 종료일"

#. module: project
#: model:mail.message.subtype,description:project.mt_update_create
#: model:mail.message.subtype,name:project.mt_project_update_create
#: model:mail.message.subtype,name:project.mt_update_create
msgid "Update Created"
msgstr "업데이트 생성"

#. module: project
#: model:project.tags,name:project.project_tags_03
msgid "Usability"
msgstr "사용 가능"

#. module: project
#: model:res.groups,name:project.group_project_milestone
msgid "Use Milestones"
msgstr "마일스톤 사용"

#. module: project
#: model:res.groups,name:project.group_project_rating
msgid "Use Rating on Project"
msgstr "프로젝트에서 평가 사용"

#. module: project
#: model:res.groups,name:project.group_project_recurring_tasks
msgid "Use Recurring Tasks"
msgstr "반복작업 사용"

#. module: project
#: model:res.groups,name:project.group_project_stages
msgid "Use Stages on Project"
msgstr "프로젝트에서 단계 사용"

#. module: project
#: model:res.groups,name:project.group_project_task_dependencies
msgid "Use Task Dependencies"
msgstr "작업 종속성 사용"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__label_tasks
msgid "Use Tasks as"
msgstr "작업을 다음과 같이 사용"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Use This For My Project"
msgstr "내 프로젝트에 사용하기"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_2
msgid ""
"Use personal stages to organize your tasks and create your own workflow."
msgstr "개인 단계를 사용해 작업을 정리하고 사용자 지정 워크플로를 만드세요."

#. module: project
#: model_terms:ir.actions.act_window,help:project.project_tags_action
msgid "Use tags to categorize your tasks."
msgstr "작업 분류에 태그 사용하기"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Use the"
msgstr "다음 내용을 사용"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Use the chatter to <b>send emails</b> and communicate efficiently with your "
"customers. Add new people to the followers' list to make them aware of the "
"main changes about this task."
msgstr ""
"메시지창을 통해 <b>이메일을 보내거나</b> 고객들과 효율적으로 소통할 수 있습니다. 팔로워 목록에 신규 사용자를 추가하여 작업의 주요"
" 변경 내용을 알려줍니다."

#. module: project
#: model:ir.model.fields,help:project.field_project_task__display_name
msgid ""
"Use these keywords in the title to set new tasks:\n"
"\n"
"        30h Allocate 30 hours to the task\n"
"        #tags Set tags on the task\n"
"        @user Assign the task to a user\n"
"        ! Set the task a high priority\n"
"\n"
"        Make sure to use the right format and order e.g. Improve the configuration screen 5h #feature #v16 @Mitchell !"
msgstr ""
"새로운 작업을 설정하려면 제목에 다음 키워드를 사용하십시오:\n"
"\n"
"        30h 작업에 30시간 할당\n"
"        #tag 작업에 태그 설정\n"
"        @user 사용자에게 작업 배정\n"
"        ! 작업에 우선 순위를 설정\n"
"\n"
"        올바른 형식과 순서를 사용했는지 확인하십시오. 예. 환경 설정 화면 개선 5h #feature #v16 @Mitchell !"

#. module: project
#: model:res.groups,name:project.group_project_user
#: model_terms:ir.ui.view,arch_db:project.portal_tasks_list
msgid "User"
msgstr "사용자"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_kanban
msgid "View"
msgstr "화면"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
msgid "View Task"
msgstr "작업 보기"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_tree
#: model_terms:ir.ui.view,arch_db:project.view_project
msgid "View Tasks"
msgstr "작업 보기"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_project__privacy_visibility
msgid "Visibility"
msgstr "표시"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "Visible"
msgstr "표시하기"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_state_selection/project_task_state_selection.xml:0
#: model:ir.model.fields.selection,name:project.selection__project_task__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__project_task_burndown_chart_report__state__04_waiting_normal
#: model:ir.model.fields.selection,name:project.selection__report_project_task_user__state__04_waiting_normal
msgid "Waiting"
msgstr "대기 중"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"Want a better way to <b>manage your projects</b>? <i>It starts here.</i>"
msgstr "더 나은 <b>프로젝트 관리</b> 방법을 찾고 있으신가요? <i>여기에 그 해답이 있습니다.</i>"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_project__website_message_ids
#: model:ir.model.fields,field_description:project.field_project_update__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Website Redesign"
msgstr "웹사이트 재디자인"

#. module: project
#: model:ir.model.fields,help:project.field_project_milestone__website_message_ids
#: model:ir.model.fields,help:project.field_project_project__website_message_ids
#: model:ir.model.fields,help:project.field_project_task__website_message_ids
#: model:ir.model.fields,help:project.field_project_update__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__weekly
msgid "Weekly"
msgstr "매주"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__week
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__week
msgid "Weeks"
msgstr "주"

#. module: project
#: model:project.tags,name:project.project_tags_14
msgid "Work"
msgstr "직장"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_open
msgid "Working Days to Assign"
msgstr "할당을 위한  근무일"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_days_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_days_close
msgid "Working Days to Close"
msgstr "마감까지의 근무일"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_open
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_open
msgid "Working Hours to Assign"
msgstr "배정할 근무 시간"

#. module: project
#: model:ir.model.fields,field_description:project.field_project_task__working_hours_close
#: model:ir.model.fields,field_description:project.field_report_project_task_user__working_hours_close
msgid "Working Hours to Close"
msgstr "마감할 근무 시간"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Assign"
msgstr "지정된 근무 시간"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "Working Time to Close"
msgstr "마감까지 근무 시간"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_unarchive_wizard
msgid ""
"Would you like to unarchive all of the projects contained in these stages as"
" well?"
msgstr "이 단계에 포함되어 있는 모든 프로젝트도 보관 처리를 취소하시겠습니까?"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_unarchive_wizard
msgid ""
"Would you like to unarchive all of the tasks contained in these stages as "
"well?"
msgstr "이 단계에 포함되어 있는 모든 작업도 보관 처리를 취소하시겠습니까?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "Write a message..."
msgstr "메시지 작성..."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_examples.js:0
msgid "Writing"
msgstr "쓰기"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status_period__yearly
msgid "Yearly"
msgstr "매년"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_task__repeat_unit__year
#: model:ir.model.fields.selection,name:project.selection__project_task_recurrence__repeat_unit__year
msgid "Years"
msgstr "년"

#. module: project
#. odoo-python
#: code:addons/project/models/project_project_stage.py:0
msgid ""
"You are not able to switch the company of this stage to %(company_name)s "
"since it currently includes projects associated with "
"%(project_company_name)s. Please ensure that this stage exclusively consists"
" of projects linked to %(company_name)s."
msgstr ""
"이 단계에서는 %(company_name)s 회사로 변경할 수 없습니다. 현재 %(project_company_name)s 회사에 "
"연결되어 있는 프로젝트가 있습니다. 현재 단계에서 모든 프로젝트가 %(company_name)s 회사에 연결되어 있는지 확인하세요."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "You can change the sub-task state here!"
msgstr "여기에서 하위 작업 상태를 변경할 수 있습니다!"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You can only set a personal stage on a private task."
msgstr "비공개 작업에서만 개인 단계를 설정할 수 있습니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid "You can open sub-tasks from the kanban card!"
msgstr "칸반 카드에서 하위 작업을 열 수 있습니다!"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You can either archive them or"
" first delete all of their projects."
msgstr ""
"단계에 프포젝트가 포함되어 있는 경우에는 삭제할 수 없습니다. 프로젝트를 보관 처리하거나 먼저 모든 프로젝트를 삭제하시기 바랍니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_project_stage_delete_wizard
msgid ""
"You cannot delete stages containing projects. You should first delete all of"
" their projects."
msgstr "단계에 프로젝트가 포함되어 있는 경우에는 삭제할 수 없습니다. 먼저 모든 프로젝트를 삭제하시기 바랍니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You can either archive them or "
"first delete all of their tasks."
msgstr "단계에 작업이 포함되어 있는 경우에는 삭제할 수 없습니다. 프로젝트를 보관 처리하거나 먼저 모든 작업을 삭제하시기 바랍니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.view_project_task_type_delete_wizard
msgid ""
"You cannot delete stages containing tasks. You should first delete all of "
"their tasks."
msgstr "단계에 작업이 포함되어 있는 경우에는 삭제할 수 없습니다. 먼저 모든 작업을 삭제하시기 바랍니다."

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You cannot read the following fields on tasks: %(field_list)s"
msgstr "작업에서 다음 필드를 읽을 수 없습니다: %(field_list)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You cannot write on the following fields on tasks: %(field_list)s"
msgstr "작업의 다음 필드에는 쓸 수 없습니다: %(field_list)s"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You have been assigned to %s"
msgstr "%s에 할당되었습니다"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_message_user_assigned
msgid "You have been assigned to the"
msgstr "다음에 할당되었습니다"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "You have been invited to follow %s"
msgstr "%s를 팔로우하도록 초대되었습니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.task_invitation_follower
msgid "You have been invited to follow Task Document :"
msgstr "작업 문서를 팔로우하도록 초대되었습니다 :"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_share_wizard_confirm_form
msgid ""
"You have full control and can revoke portal access anytime. Are you ready to"
" proceed?"
msgstr "전체 권한이 있으며 포탈 접근을 언제든 취소할 수 있습니다. 계속 진행할까요?"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/js/tours/project.js:0
msgid ""
"You have unsaved changes - no worries! Odoo will automatically save it as "
"you navigate.<br/> You can discard these changes from here or manually save "
"your task.<br/>Let's save it manually."
msgstr ""
"저장하지 않은 변경 내용이 있습니다. 걱정 마십시오! Odoo에서는 자동 저장 기능을 사용하실 수 있습니다.<br/> 여기에서의 해당 "
"변경 내용을 삭제하\\거나 작업을 직접 저장하실 수 있습니다.<br/>직접 저장하겠습니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "You must be"
msgstr "해야할 것"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "Your managers decide which feedback is accepted"
msgstr "관리자가 어떤 피드백을 받아들일지 결정합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "alias"
msgstr "별칭"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "and"
msgstr "그리고"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"and which feedback is\n"
"      moved to the \"Refused\" column."
msgstr ""
"그리고 피드백을\n"
"     \"거부\" 열로 이동합니다."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_kanban
msgid "assignees"
msgstr "담당자"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_messages.xml:0
msgid "avatar"
msgstr "아바타"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "button."
msgstr "버튼."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_message_counter.xml:0
msgid "comments"
msgstr "의견"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_view_form
msgid "e.g. Monthly review"
msgstr "예: 월간 검토"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
#: model_terms:ir.ui.view,arch_db:project.quick_create_project_form
msgid "e.g. Office Party"
msgstr "예: 사무실 파티"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_project_task_view_form
#: model_terms:ir.ui.view,arch_db:project.view_task_form2
msgid "e.g. Product Launch"
msgstr "예: 제품 출시"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_sharing_quick_create_task_form
#: model_terms:ir.ui.view,arch_db:project.quick_create_task_form
msgid "e.g. Send Invitations"
msgstr "예: 초대장 전송"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
msgid "e.g. Tasks"
msgstr "예: 작업"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_form_quick_create
#: model_terms:ir.ui.view,arch_db:project.project_project_stage_view_tree
#: model_terms:ir.ui.view,arch_db:project.task_type_edit
#: model_terms:ir.ui.view,arch_db:project.task_type_tree
msgid "e.g. To Do"
msgstr "예: To Do"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.edit_project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. mycompany.com"
msgstr "예: mycompany.com"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_project_view_form_simplified
msgid "e.g. office-party"
msgstr "예: 오피스 파티"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_milestone_view_form
msgid "e.g: Product Launch"
msgstr "예: 제품 출시"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "icon to organize your daily activities."
msgstr "일별 활동을 아이콘을 활용하여 정리하세요."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"icon to see tasks waiting on other ones. Once a task is marked as complete "
"or cancelled, all of its dependencies will be unblocked."
msgstr ""
"아이콘을 클릭하면 다른 작업에서 대기 중인 작업을 볼 수 있습니다. 작업이 완료 또는 취소됨으로 표시되면 모든 종속성이 차단 해제됩니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "icon."
msgstr "아이콘."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "logged in"
msgstr "로그인됨"

#. module: project
#: model:ir.actions.server,name:project.action_server_view_my_task
msgid "menu view My Tasks"
msgstr "메뉴 보기 나의 작업"

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__periodic
msgid "on a periodic basis"
msgstr "주기적으로"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "project."
msgstr "프로젝트."

#. module: project
#: model_terms:ir.ui.view,arch_db:project.milestone_deadline
msgid "ready to be marked as reached"
msgstr "도달 완료로 표시할 준비됨"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"state to indicate a request for changes or a need for discussion on a task."
msgstr "작업에 대한 변경 요청이나 논의할 점을 표시합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"state to inform your colleagues that a task is approved for the next stage."
msgstr "동료에게 작업이 다음 단계로 진행할 준비가 되었음을 알립니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "state to mark the task as cancelled."
msgstr "상태를 설정하여 작업을 취소됨으로 표시합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "state to mark the task as complete."
msgstr "작업을 완료로 표시합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "state.message"
msgstr "state.message"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/components/project_task_name_with_subtask_count_char_field/project_task_name_with_subtask_count_char_field.xml:0
msgid "sub-tasks)"
msgstr "하위 작업)"

#. module: project
#. odoo-python
#: code:addons/project/models/project_task.py:0
msgid "task"
msgstr "작업"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestone has been updated:"
msgstr "다음의 마일스톤에 대한 기한이 변경되었습니다:"

#. module: project
#: model_terms:ir.ui.view,arch_db:project.project_update_default_description
msgid "the deadline for the following milestones has been updated:"
msgstr "다음의 마일스톤에 대한 기한이 변경되었습니다:"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid ""
"to define if the project is\n"
"      ready for the next step."
msgstr ""
"프로젝트가 다음 단계로 진행할\n"
"      준비가 되었는지 지정합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/project_sharing/components/chatter/chatter_composer.xml:0
msgid "to post a comment."
msgstr "의견을 게시합니다."

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/xml/project_task_kanban_examples.xml:0
msgid "to signalize what is the current status of your Idea."
msgstr "현재 아이디어가 어떤 상태인지 알려줍니다."

#. module: project
#: model:ir.model.fields.selection,name:project.selection__project_project__rating_status__stage
msgid "when reaching a given stage"
msgstr "특정 단계에 도달했을 때"

#. module: project
#: model_terms:digest.tip,tip_description:project.digest_tip_project_1
msgid "will generate tasks in your"
msgstr "다음에 대한 작업을 생성합니다."

#. module: project
#: model:mail.template,subject:project.rating_project_request_email_template
msgid ""
"{{ object.project_id.company_id.name or user.env.company.name }}: "
"Satisfaction Survey"
msgstr ""
"{{ object.project_id.company_id.name or user.env.company.name }}: 만족도 조사"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
msgid "👤 Unassigned"
msgstr "👤 미지정"

#. module: project
#. odoo-javascript
#: code:addons/project/static/src/views/project_task_graph/project_task_graph_model.js:0
#: code:addons/project/static/src/views/project_task_kanban/project_task_kanban_header.js:0
#: code:addons/project/static/src/views/project_task_list/project_task_list_renderer.js:0
msgid "🔒 Private"
msgstr "🔒 비공개"
