# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_cl
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 07:43+0000\n"
"PO-Revision-Date: 2023-09-07 07:43+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_res_partner__l10n_cl_sii_taxpayer_type
#: model:ir.model.fields,help:l10n_cl.field_res_users__l10n_cl_sii_taxpayer_type
msgid ""
"1 - VAT Affected (1st Category) (Most of the cases)\n"
"2 - Fees Receipt Issuer (Applies to suppliers who issue fees receipt)\n"
"3 - End consumer (only receipts)\n"
"4 - Foreigner"
msgstr ""
"1 - IVA Afecto (la mayoría de los casos)\n"
"2 - Emisor Boletas (aplica solo para proveedores emisores de boleta)\n"
"3 - Consumidor Final (siempre se le emitirán boletas)\n"
"4 - Extranjero"

#. module: l10n_cl
#: model:res.currency,l10n_cl_currency_code:l10n_cl.OTR
msgid "900"
msgstr "900"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid ""
"<br/>\n"
"\n"
"                <strong>Customer:</strong>"
msgstr ""
"<br/>\n"
"\n"
"                <strong>Cliente:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.custom_header
msgid ""
"<br/>\n"
"                                            <span style=\"font-family:arial; line-height: 180%;\">RUT:</span>"
msgstr ""
"<br/>\n"
"                                            <span style=\"font-family:arial; line-height: 180%;\">RUT:</span>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.custom_header
msgid ""
"<br/>\n"
"                                            <span>Nº:</span>"
msgstr ""
"<br/>\n"
"                                            <span>Nº:</span>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid ""
"<br/>\n"
"                    <strong>Delivery Address:</strong>"
msgstr ""
"<br/>\n"
"                    <strong>Dirección de entrega:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid ""
"<br/>\n"
"                    <strong>Incoterm:</strong>"
msgstr ""
"<br/>\n"
"                    <strong>Incoterm:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid ""
"<br/>\n"
"                <strong>GIRO:</strong>"
msgstr ""
"<br/>\n"
"                <strong>GIRO:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.report_invoice_document
msgid "<span>Disc.</span>"
msgstr "<span>Desc.</span>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid "<strong>Address:</strong>"
msgstr "<strong>Dirección:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid "<strong>Due Date:</strong>"
msgstr "<strong>Fecha de vencimiento:</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.tax_totals_widget
msgid "<strong>Exempt Amount</strong>"
msgstr "<strong>Total sin impuestos</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.tax_totals_widget
msgid "<strong>Net Amount</strong>"
msgstr "<strong>Total neto</strong>"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.tax_totals_widget
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_awb
msgid "AWB"
msgstr "AWB"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_awb
msgid "AWB Airway Bill"
msgstr "AWB Guía aérea"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla del plan de cuentas"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Accounting Date"
msgstr "Fecha contable"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_l10n_latam_document_type__l10n_cl_active
msgid "Active in localization"
msgstr "Activo en localización"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_partner__l10n_cl_activity_description
#: model:ir.model.fields,field_description:l10n_cl.field_res_users__l10n_cl_activity_description
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_company_l10n_cl_form
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_move_form
msgid "Activity Description"
msgstr "Descripción de la actividad"

#. module: l10n_cl
#: model:product.template,name:l10n_cl.product_product_ad_valorem_product_template
msgid "Ad-Valorem"
msgstr "Ad-Valorem"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_move_form
msgid "Additional data address and city"
msgstr "Datos adic. dirección y ciudad"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Amount Due"
msgstr "Monto adeudado"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Amount Untaxed"
msgstr "Monto sin impuestos"

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_account_bank_statement_line__l10n_latam_internal_type
#: model:ir.model.fields,help:l10n_cl.field_account_move__l10n_latam_internal_type
#: model:ir.model.fields,help:l10n_cl.field_l10n_latam_document_type__internal_type
msgid "Analog to odoo account.move.move_type but with more options allowing to identify the kind of document we are working with. (not only related to account.move, could be for documents of other models like stock.picking)"
msgstr "Análogo a account.move.type de Odoo pero con más opciones, permitiendo identificar el tipo de documento sobre el que estamos trabajando. (no solo relativo a account.move, podría ser relativo a otros modelos, por ejemplo a stock.picking)"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_bl_cemb
msgid "B/L"
msgstr "B/L"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_bl_cemb
msgid "B/L (Bill of Lading)"
msgstr "B/L (Conocimiento de embarque)"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_bar
msgid "BAR"
msgstr "BAR"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_b_f_dte
msgid "BEL"
msgstr "BEL"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_b_e_dtn
msgid "BEX"
msgstr "BEX"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_m_d_dtn
msgid "BHE"
msgstr "BHE"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_m_f_dtn
msgid "BHO"
msgstr "BHO"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_b_f_dtn
msgid "BOL"
msgstr "BOL"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_bzf_f_dtn
msgid "BOLETA ZF"
msgstr "BOLETA ZF"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_b_e_dte
msgid "BXE"
msgstr "BXE"

#. module: l10n_cl
#: model:account.report.column,name:l10n_cl.tax_report_balance
msgid "Balance"
msgstr "Saldo"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_bank
msgid "Bank"
msgstr "Banco"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_cpor
msgid "Bill of Lading"
msgstr "Carta de Porte"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_cem_ma
msgid "Bill of Lading (Sea or Air)"
msgstr "Conocimiento de Embarque (Marítimo o aéreo)"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_b_f_dtn
msgid "Bill of Sale"
msgstr "Boleta de Venta"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_carton
msgid "CARTON"
msgstr "CARTON"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_cem_ma
msgid "CEM"
msgstr "CEM"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_chq
msgid "CHQ"
msgstr "CHQ"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_cont
msgid "CONT"
msgstr "CONT"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_cpor
msgid "CPR"
msgstr "CPR"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_nc_f_dte
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_nc_f_dtn
msgid "CREDIT NOTE"
msgstr "NOTA DE CRÉDITO"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_cd_bol
msgid "CRTD"
msgstr "CRTD"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_cd_bol
msgid "Certificate of Deposit Bolsa Prod. Chile."
msgstr "Certificado de Depósito Bolsa Prod. Chile."

#. module: l10n_cl
#: model_terms:product.template,description:l10n_cl.product_product_ad_valorem_product_template
msgid "Charge for Ad-Valorem calculation in DIN"
msgstr "Cargo para calculo de Ad-Valorem en DIN"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_chq
msgid "Cheque"
msgstr "Cheque"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_fichc
msgid "Chile Purchase Form"
msgstr "Ficha Chile Compra"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_prchc
msgid "Chile Purchase Process"
msgstr "Proceso Chile Compra"

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_res_company__l10n_cl_activity_description
#: model:ir.model.fields,help:l10n_cl.field_res_partner__l10n_cl_activity_description
#: model:ir.model.fields,help:l10n_cl.field_res_users__l10n_cl_activity_description
msgid "Chile: Economic activity."
msgstr "Chile: Actividad económica."

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.res_config_settings_view_form
msgid "Chilean Localization"
msgstr "Localización chilena"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_bank__l10n_cl_sbif_code
msgid "Cod. SBIF"
msgstr "Cod. SBIF"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_move_form
msgid "Commune"
msgstr "Comuna"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_company__l10n_cl_activity_description
msgid "Company Activity Description"
msgstr "Descripción de la actividad de la empresa"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_cont
msgid "Contract"
msgstr "Contrato"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_country
msgid "Country"
msgstr "País"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_nc_f_dtn
msgid "Credit Note"
msgstr "Nota de crédito"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__credit_note
msgid "Credit Notes"
msgstr "Notas de Crédito"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_res_currency
msgid "Currency"
msgstr "Moneda"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_currency__l10n_cl_currency_code
msgid "Currency Code"
msgstr "Código de moneda"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_country__l10n_cl_customs_abbreviation
msgid "Customs Abbreviation"
msgstr "Abreviatura de aduana"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_country__l10n_cl_customs_code
msgid "Customs Code"
msgstr "Código de aduana"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_country__l10n_cl_customs_name
msgid "Customs Name"
msgstr "Nombre de aduana"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_nd_f_dte
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_nd_f_dtn
msgid "DEBIT NOTE"
msgstr "NOTA DE DEBITO"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_din_f_dtn
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_dizf_f_dtn
msgid "DEC ING"
msgstr "DEC ING"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_dus
msgid "DUS"
msgstr "DUS"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.informations
msgid "Date:"
msgstr "Fecha:"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_nd_f_dtn
msgid "Debit Note"
msgstr "Nota de débito"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__debit_note
msgid "Debit Notes"
msgstr "Notas de débito"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_dizf_f_dtn
msgid "Declaration of Entry to Primary Free Trade Zone"
msgstr "Declaración de Ingreso a Zona Franca Primaria"

#. module: l10n_cl
#: model:res.currency,currency_unit_label:l10n_cl.UF
#: model:res.currency,l10n_cl_short_name:l10n_cl.UF
msgid "Development Unit"
msgstr "Unidad de Fomento"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_gd
msgid "Dispatch Guide"
msgstr "Guía de Despacho"

#. module: l10n_cl
#. odoo-python
#: code:addons/l10n_cl/models/account_move.py:0
msgid "Document types for foreign customers must be export type (codes 110, 111 or 112) or you should define the customer as an end consumer and use receipts (codes 39 or 41)"
msgstr "Las clases de documentos para clientes extranjeros deben ser de tipo exportación (códigos 110, 111 o 112) o debe definir al cliente como consumidor final y utilizar recibos  (códigos 39 o 41)"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_nc_f_dte
msgid "Electronic Credit Note"
msgstr "Nota de Crédito Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_nd_f_dte
msgid "Electronic Debit Note"
msgstr "Nota de Débito Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_gd_dte
msgid "Electronic Dispatch Guide"
msgstr "Guía de Despacho Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_b_e_dte
msgid "Electronic Exempt Receipt"
msgstr "Boleta Exenta Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_ncex_dte
msgid "Electronic Export Credit Note"
msgstr "Nota de Crédito de Exportación Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_ndex_dte
msgid "Electronic Export Debit Note"
msgstr "Nota de Débito de Exportación Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_fe_dte
msgid "Electronic Export Invoice"
msgstr "Factura de Exportación Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_m_d_dtn
msgid "Electronic Fee Slips"
msgstr "Boleta de Honorarios Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_a_f_dte
msgid "Electronic Invoice"
msgstr "Factura Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_l_f_dte
msgid "Electronic Invoice Settlement"
msgstr "Liquidación Factura Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_fc_f_dte
msgid "Electronic Purchase Invoice"
msgstr "Factura de Compra Electrónica"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_b_f_dte
msgid "Electronic Receipt"
msgstr "Boleta Electrónica"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__res_partner__l10n_cl_sii_taxpayer_type__3
msgid "End Consumer"
msgstr "Consumidor final"

#. module: l10n_cl
#: model:uom.category,name:l10n_cl.uom_categ_energy
msgid "Energy"
msgstr "Energía"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_tcd_f_dtn
msgid "Exchange Rate Decrease Adjustment (code 501)"
msgstr "Ajuste disminución Tipo de Cambio (código 501)"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_tca_f_dtn
msgid "Exchange Rate Increase Adjustment (code 500)"
msgstr "Ajuste aumento Tipo de Cambio (código 500)"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_b_e_dtn
msgid "Exempt Receipt"
msgstr "Boleta exenta"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_ventas_exentas
msgid "Exempt Sales"
msgstr "Ventas Exentas"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_y_f_dte
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_y_f_dtn
msgid "F-EXENTA"
msgstr "F-EXENTA"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_frr_f_dtn
msgid "FACT RX"
msgstr "FACT RX"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_ftt_f_dtn
msgid "FACT TR"
msgstr "FACT TR"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_I_f_dtn
msgid "FAI"
msgstr "FAI"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_fichc
msgid "FCHC"
msgstr "FCHC"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_fe_dte
msgid "FCXE"
msgstr "FCXE"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_m_f_dtn
msgid "Fee Slips"
msgstr "Boleta de Honorarios"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_fees_amount
msgid "Fees - Amounts Subject to 2 Category Income Tax Withholding"
msgstr "Honorarios -  Montos Sujetos a Retención Renta 2 Categoría"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__res_partner__l10n_cl_sii_taxpayer_type__2
msgid "Fees Receipt Issuer (2nd category)"
msgstr "Emisor de boleta 2da categoría"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_impuestos_renta
msgid "First Category Income Taxes Payable"
msgstr "Impuesto a la Renta Primera Categoría a Pagar"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_bank__fiscal_country_codes
msgid "Fiscal Country Codes"
msgstr "Códigos de país fiscales"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Folio"
msgstr "Folio"

#. module: l10n_cl
#: model:l10n_latam.identification.type,description:l10n_cl.it_DNI
msgid "Foreign ID"
msgstr "DNI Extranjero"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__res_partner__l10n_cl_sii_taxpayer_type__4
msgid "Foreigner"
msgstr "Extranjero"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_gd
msgid "GD"
msgstr "GD"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_gd_dte
msgid "GDE"
msgstr "GDE"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_hem
msgid "HEM"
msgstr "HEM"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_hes
msgid "HES"
msgstr "HES"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_hl
msgid "HL"
msgstr "HL"

#. module: l10n_cl
#: model:l10n_latam.identification.type,name:l10n_cl.it_DNI
msgid "ID CARD"
msgstr "DNI"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_tax_ila_ventas
msgid "ILA Tax Ret Practiced (sales)"
msgstr "Impuesto Ret Practicadas ILA (ventas)"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_base_ila_compras
msgid "ILA Withholding Base (purchases)"
msgstr "Base Retenciones ILA (compras)"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_base_ila_ventas
msgid "ILA Withholding Base (sales)"
msgstr "Base Retenciones ILA (ventas)"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_a_f_dte
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_a_f_dtn
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_fc_f_dte
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_fc_f_dtn
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_ftf_f_dtn
msgid "INVOICE"
msgstr "FACTURA"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_fzf_f_dtn
msgid "INVOICE ZF"
msgstr "FACTURA ZF"

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_account_bank_statement_line__partner_id_vat
#: model:ir.model.fields,help:l10n_cl.field_account_move__partner_id_vat
msgid "Identification Number for selected type"
msgstr "Número de identificación para el tipo seleccionado"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_din_f_dtn
msgid "Income Statement (DIN)"
msgstr "Declaración de Ingreso (DIN)"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_l10n_latam_document_type__internal_type
msgid "Internal Type"
msgstr "Tipo interno"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_a_f_dtn
msgid "Invoice"
msgstr "Factura"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_l_f_dtn
msgid "Invoice Settlement"
msgstr "Liquidación Factura"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_ftf_f_dtn
msgid "Invoice for sales to companies in the preferential territory ( Ex. Res. No. 1057"
msgstr "Factura de ventas a empresas del territorio preferencial ( Res. Ex. N° 1057"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_I_f_dtn
msgid "Invoice of Initiation"
msgstr "Factura de Inicio"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_y_f_dtn
msgid "Invoice of Sales and Services not Affected or Exempt from VAT"
msgstr "Factura de Ventas y Servicios no Afectos o Exentos de IVA"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__invoice
msgid "Invoices"
msgstr "Facturas"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Invoices and Refunds"
msgstr "Facturas y notas de crédito"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_knfc
msgid "KNFC"
msgstr "KNFC"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_kwh
msgid "KWH"
msgstr "KWH"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_l_f_dte
msgid "L-FACTURAE"
msgstr "L-FACTURAE"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_l_f_dtn
msgid "L-FACTURAM"
msgstr "L-FACTURAM"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_bank_statement_line__l10n_latam_internal_type
#: model:ir.model.fields,field_description:l10n_cl.field_account_move__l10n_latam_internal_type
msgid "L10n Latam Internal Type"
msgstr "L10n Tipo Interno (Latam)"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_li
msgid "LIQ"
msgstr "LIQ"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_l10n_latam_document_type
msgid "Latam Document Type"
msgstr "Tipo de Documento Latam"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_li
msgid "Liquidation"
msgstr "Liquidación"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.custom_header
msgid "Logo"
msgstr "Logotipo"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_mm
msgid "M2/1MM"
msgstr "M2/1MM"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_mcub
msgid "MCUB"
msgstr "MCUB"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_mic_dta
msgid "MDT"
msgstr "MDT"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_mic_dta
msgid "MIC/DTA"
msgstr "MIC/DTA"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_migo
msgid "MIGO"
msgstr "MIGO"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_mkwh
msgid "MKWH"
msgstr "MKWH"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_mt2
msgid "MT2"
msgstr "MT2"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_hem
msgid "Material Entry Sheet (HEM)"
msgstr "Hoja de Entrada de Materiales (HEM)"

#. module: l10n_cl
#: model:res.currency,currency_unit_label:l10n_cl.UTM
#: model:res.currency,l10n_cl_short_name:l10n_cl.UTM
msgid "Monthly Tax Unit"
msgstr "Unidad Tributaria Mensual"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_migo
msgid "Movement of Goods (MIGO)"
msgstr "Movimiento de Mercancías (MIGO)"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_ncex_dte
msgid "NCXE"
msgstr "NCXE"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_ndex_dte
msgid "NDXE"
msgstr "NDXE"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_ndp
msgid "NP"
msgstr "NP"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_netas_gr_iva_recup
msgid "Net Purchases Taxed with VAT (recoverable)"
msgstr "Compras Netas Gravadas Con IVA (recuperable)"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_ventas_netas_gravadas_c_iva
msgid "Net Sales Taxed with VAT"
msgstr "Ventas Netas Gravadas con IVA"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_oc
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_odc
msgid "OC"
msgstr "OC"

#. module: l10n_cl
#: model:res.currency,l10n_cl_short_name:l10n_cl.OTR
msgid "OTR"
msgstr "OTR"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_ndp
msgid "Order Note"
msgstr "Nota de pedido"

#. module: l10n_cl
#: model:uom.category,name:l10n_cl.uom_categ_others
msgid "Others"
msgstr "Otros"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_pag
msgid "PAG"
msgstr "PAG"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_par
msgid "PAR"
msgstr "PAR"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_res_vn_sf
msgid "PASJ"
msgstr "PASJ"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_prchc
msgid "PCHC"
msgstr "PCHC"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_ppm
msgid "PPM"
msgstr "PPM"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_pasap
msgid "PSP"
msgstr "PSP"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_pasap
msgid "Passport"
msgstr "Pasaporte"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_vp_pren
msgid "Pledge Voucher Bolsa Prod. Chile"
msgstr "Vale de Prenda Bolsa Prod. Chile"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_pag
msgid "Promissory note"
msgstr "Pagaré"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_netas_gr_iva_uso_comun
msgid "Purchase Engraved Nets With VAT Communal Use"
msgstr "Compra Netas Gravadas Con IVA Uso Comun"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_fc_f_dtn
msgid "Purchase Invoice"
msgstr "Factura de Compra"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__invoice_in
msgid "Purchase Invoices"
msgstr "Facturas de proveedores"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_oc
#: model:l10n_latam.document.type,name:l10n_cl.dc_odc
msgid "Purchase Order"
msgstr "Orden de Compra"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_otros_imp
msgid "Purchases - Amount Other Taxes"
msgstr "Compras - Monto Otros Impuestos"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_iva_uso_comun
msgid "Purchases - Amount VAT Commun Use"
msgstr "Compras - Monto IVA Uso Comun"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_iva_actf_uso_comun
msgid "Purchases - Amount of Active Fixed VAT (Common Use)"
msgstr "Compras - Monto IVA Activo Fijo (Uso Común)"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_iva_actf_no_recup
msgid "Purchases - Amount of Active Fixed VAT (Non Deductible)"
msgstr "Compras - Monto IVA Activo Fijo (No Deducible)"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_iva
msgid "Purchases - Amount of Recoverable VAT"
msgstr "Compras - Monto IVA Recuperable"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_imp_42
msgid "Purchases - Credit Imp Art 42"
msgstr "Compras - Credito Imp Art 42"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_iva_actf
msgid "Purchases - Fixed VAT Amount"
msgstr "Compras - Monto IVA Activo Fijo"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_imp_vehic
msgid "Purchases - Motor Vehicle Tax"
msgstr "Compras - Impuesto a Vehículos Automotores"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_iva_no_rec
msgid "Purchases - Non Recoverable VAT Amount"
msgstr "Compras - Monto IVA No Recuperable"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_imp_sin_cred
msgid "Purchases - Tax No Credit"
msgstr "Compras - Impuesto Sin Credito"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_imp_sin_credito
msgid "Purchases - Taxes Without Credit"
msgstr "Compras - Impuestos Sin Crédito"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_tab_puros
msgid "Purchases - Total Manufactured Cigars"
msgstr "Compras - Total Tabacos Manufacturados Puros"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_tab_cigar
msgid "Purchases - Total Manufactured Tobacco Cigarettes"
msgstr "Compras - Total Tabacos Manufacturados Cigarrillos"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_tab_elab
msgid "Purchases - Total Manufactured Tobacco Products"
msgstr "Compras - Total Tabacos Manufacturados Elaborados"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_neto
msgid "Purchases - Total Net Amount"
msgstr "Compras - Total Monto Neto"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_neto_uso_comun
msgid "Purchases - Total Net Amount Common Use"
msgstr "Compras - Total Monto Neto Uso Común"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_neto_supermercado
msgid "Purchases - Total Net Amount Supermarket Purchases"
msgstr "Compras - Total Monto Neto Compras de Supermercado"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_neto_no_recup
msgid "Purchases - Total Net Amount not Recoverable"
msgstr "Compras - Total Monto Neto No Recuperable"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_neto_actf
msgid "Purchases - Total Nets Amount Fixed Assets"
msgstr "Compras - Total Monto Neto Activo Fijo"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_iva_no_reten
msgid "Purchases - VAT Not Withheld"
msgstr "Compras - IVA No Retenido"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_iva_no_ret
msgid "Purchases - VAT Not Withheld Purchasing fac"
msgstr "Compras - IVA No Retenido Fac de compra"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_iva_activo_fijo
msgid "Purchases Fixed Assets"
msgstr "Compras Activo Fijo"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_netas_gr_iva_no_recuperable
msgid "Purchases Non-recoverable VAT"
msgstr "Compras IVA No Recuperable"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_no_gravadas_iva
msgid "Purchases Not Taxed With VAT"
msgstr "Compras No Gravadas Con IVA"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_activo_fijo_uso_comun
#: model:account.report.line,name:l10n_cl.tax_report_compras_iva_activo_fijo_uso_comun
msgid "Purchases of Fixed Assets Common Use"
msgstr "Compras de Activo Fijo Uso Común"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_iva_activo_fijo_no_recup
msgid "Purchases of Non Recoverable Fixed Assets"
msgstr "Compras Activo Fijo No Recuperables"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_activo_fijo
msgid "Purchases of fixed assets"
msgstr "Compras de Activo Fijo"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_activo_fijo_no_recup
msgid "Purchases of non-recoverable fixed assets"
msgstr "Compras de Activo Fijo No Recuperable"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_qmb
msgid "QMB"
msgstr "QMB"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_qnt
msgid "QNT"
msgstr "QNT"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_resol
msgid "RES"
msgstr "RES"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_res_sna
msgid "RSN"
msgstr "RSN"

#. module: l10n_cl
#: model:l10n_latam.identification.type,name:l10n_cl.it_RUN
msgid "RUN"
msgstr "RUN"

#. module: l10n_cl
#: model:l10n_latam.identification.type,description:l10n_cl.it_RUT
#: model:l10n_latam.identification.type,name:l10n_cl.it_RUT
msgid "RUT"
msgstr "RUT"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__receipt_invoice
msgid "Receipt Invoice"
msgstr "Recibo Factura"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_move_form
msgid "Region"
msgstr "Región"

#. module: l10n_cl
#: model:l10n_latam.identification.type,description:l10n_cl.it_RUN
msgid "Registration"
msgstr "Cédula"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_frr_f_dtn
msgid "Reissue Invoice"
msgstr "Factura de Reexpedición"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_remanente_cf
msgid "Remaining Tax Credit"
msgstr "Remanente Crédito Fiscal"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_resol
msgid "Resolution"
msgstr "Resolución"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_res_sna
msgid "Resolution of the SNA where it qualifies Export Services"
msgstr "Resolución del SNA donde califica Servicios de Exportación"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_tax_ila_compras
msgid "Ret Suffered Tax ILA (purchases)"
msgstr "Impuesto Ret Sufrida ILA (compras)"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_sum
#: model:uom.uom,name:l10n_cl.product_uom_sum_99
msgid "S.U.M"
msgstr "S.U.M"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_tax__l10n_cl_sii_code
#: model:ir.model.fields,field_description:l10n_cl.field_uom_uom__l10n_cl_sii_code
msgid "SII Code"
msgstr "Código SII"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_s_f_dtn
msgid "SOL REGISTRO"
msgstr "SOL REGISTRO"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_s_f_dtn
msgid "SRF Invoice Registration Request"
msgstr "SRF Solicitud de Registro de Factura"

#. module: l10n_cl
#: model:ir.actions.act_window,name:l10n_cl.sale_invoices_credit_notes
msgid "Sale Invoices and Credit Notes"
msgstr "Facturas de Venta y Notas de Crédito"

#. module: l10n_cl
#: model:ir.ui.menu,name:l10n_cl.menu_sale_invoices_credit_notes
msgid "Sale Invoices and Credit Notes (CL)"
msgstr "Facturas de Vta y Notas de Crédito (CL)"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_dep_env
msgid "Sales - Container Depot"
msgstr "Ventas - Depósito de Envases"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_exento_vta_pasajes_nacional
msgid "Sales - Exempt Domestic Ticket Sales"
msgstr "Ventas - Exento Ventas de Pasajes Nacional"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_exento_vta_pasajes_internacional
msgid "Sales - Exempt Sales of International Tickets"
msgstr "Ventas - Exento Ventas de Pasajes Internacional"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_otros_imp
msgid "Sales - Other Taxes"
msgstr "Ventas - Otros Impuestos"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_iva_propio
msgid "Sales - Own VAT"
msgstr "Ventas - IVA Propio"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_cred_ec
msgid "Sales - Special Credit 65% Construction Companies"
msgstr "Ventas - Credito Especial 65% Empresas Constructoras"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_mnt_exe
msgid "Sales - Total Amount Exempt or Unrecorded"
msgstr "Ventas - Total Monto Exento o No Gravado"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_mnt_neto
msgid "Sales - Total Net Amount"
msgstr "Ventas - Total Monto Neto"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_valor_neto_comis
msgid "Sales - Total Net Value Commissions and Other Charges"
msgstr "Ventas - Total Valor Neto Comisiones y Otros Cargos"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_valor_comisiones_no_afecto
msgid "Sales - Total Net Value Commissions and Other Unaffected Charges"
msgstr "Ventas - Total Valor Neto Comisiones y Otros Cargos No Afectos"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_monto_no_facturable
msgid "Sales - Total Non-Billable Amount"
msgstr "Ventas - Total Monto No Facturable"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_iva_comisiones
msgid "Sales - VAT Commissions and Other Charges"
msgstr "Ventas - IVA Comisiones y Otros Cargos"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_iva_18211
msgid "Sales - VAT Law 18211"
msgstr "Ventas - IVA Ley 18211"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_mnt_fuera_plazo
msgid "Sales - VAT Out of Time"
msgstr "Ventas - IVA Fuera de Plazo"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_fzf_f_dtn
msgid "Sales Invoices ZF Module (all)"
msgstr "Facturas Venta Módulo ZF (todas)"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Sales Person"
msgstr "Comercial"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_impuestos_originados_venta
msgid "Sales Tax"
msgstr "Impuesto Originado por la Venta"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_retencion_segunda_categ
msgid "Second Category Withholding"
msgstr "Retención Segunda Categoría"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_base_retencion_segunda_categ
msgid "Second Category Withholding Base"
msgstr "Base Retención Segunda Categoría"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_hes
msgid "Service Entry Sheet (HES)"
msgstr "Hoja de Entrada de Servicio (HES)"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_iva_supermercado
msgid "Shopping - VAT amount for Supermarket purchases"
msgstr "Compras - Monto IVA Compras Supermercado"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_purchase_mnt_exe
msgid "Shopping Cart - Total Exempt or Unrecorded Amount"
msgstr "Compras - Total Monto Exento o No Gravado"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_currency__l10n_cl_short_name
msgid "Short Name"
msgstr "Nombre corto"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_dus
msgid "Single Exit Document (DUS)"
msgstr "Documento Único de Salida (DUS)"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_impuesto_unico_trabajadores
msgid "Single Tax Workers"
msgstr "Impuesto Único Trabajadores"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Source Document"
msgstr "Documento origen"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__l10n_latam_document_type__internal_type__stock_picking
msgid "Stock Delivery"
msgstr "Entrega de existencias"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_res_vn_sf
msgid "Summary of Domestic Sales without Invoice"
msgstr "Resumen Ventas de nacionales pasajes sin Factura"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_supermercado
msgid "Supermarket Shopping"
msgstr "Compras de Supermercado"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_tca_f_dtn
msgid "TC-A"
msgstr "TC-A"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_tcd_f_dtn
msgid "TC-D"
msgstr "TC-D"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_tmb
msgid "TMB"
msgstr "TMB"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_tmn
msgid "TMN"
msgstr "TMN"

#. module: l10n_cl
#: model:ir.model,name:l10n_cl.model_account_tax
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Tax"
msgstr "Impuesto"

#. module: l10n_cl
#: model:account.report,name:l10n_cl.tax_report
msgid "Tax Report"
msgstr "Informe fiscal"

#. module: l10n_cl
#. odoo-python
#: code:addons/l10n_cl/models/account_move.py:0
msgid "Tax payer type and vat number are mandatory for this type of document. Please set the current tax payer type of this customer"
msgstr "El tipo de contribuyente y el número de RUT son requeridos para este tipo de documento. Por favor establezca un valor para el tipo de contribuyente de este Cliente"

#. module: l10n_cl
#. odoo-python
#: code:addons/l10n_cl/models/account_move.py:0
msgid "Tax payer type and vat number are mandatory for this type of document. Please set the current tax payer type of this supplier"
msgstr "El tipo de contribuyente y el número de RUT son requeridos para este tipo de documento. Por favor establezca un valor para el tipo de contribuyente de este Proveedor"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_base_imponible_ventas
msgid "Taxable Sales Base"
msgstr "Base Imponible Ventas"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_impuestos_pagados_compra
msgid "Taxes Paid on Purchase"
msgstr "Impuestos Pagados en la Compra"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_res_partner__l10n_cl_sii_taxpayer_type
#: model:ir.model.fields,field_description:l10n_cl.field_res_users__l10n_cl_sii_taxpayer_type
msgid "Taxpayer Type"
msgstr "Tipo de Contribuyente"

#. module: l10n_cl
#. odoo-python
#: code:addons/l10n_cl/models/account_move.py:0
msgid "The DIN document is intended to be used only with RUT ********-0 (Tesorería General de La República)"
msgstr "El documento “declaración de ingreso” (DIN) solo se debe usar para “Tesorería General de La República” (RUT ********-0)"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.report_invoice_document
msgid "The VAT tax of this boleta is:"
msgstr "El IVA de esta boleta es:"

#. module: l10n_cl
#. odoo-python
#: code:addons/l10n_cl/models/account_move.py:0
msgid "The tax payer type of this supplier is incorrect for the selected type of document."
msgstr "El tipo de contribuyente de este proveedor es incorrecto para el tipo de documento seleccionado."

#. module: l10n_cl
#. odoo-python
#: code:addons/l10n_cl/models/account_move.py:0
msgid "The tax payer type of this supplier is not entitled to deliver fees documents"
msgstr "El tipo de contribuyente para este proveedor no puede emitir boletas de honorarios"

#. module: l10n_cl
#. odoo-python
#: code:addons/l10n_cl/models/account_move.py:0
msgid "The tax payer type of this supplier is not entitled to deliver imports documents"
msgstr "El tipo de contribuyente para este proveedor no puede emitir documentos de importación"

#. module: l10n_cl
#: model:ir.model.fields,help:l10n_cl.field_l10n_latam_document_type__l10n_cl_active
msgid "This boolean enables document to be included on invoicing"
msgstr "Este booleano permite incluir el documento en la facturación"

#. module: l10n_cl
#: model_terms:ir.ui.view,arch_db:l10n_cl.view_complete_invoice_refund_tree
msgid "Total"
msgstr "Total"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_retencion_total_compras
msgid "Total retention (purchases)"
msgstr "Retención Total (compras)"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_ftt_f_dtn
msgid "Transfer Invoice"
msgstr "Factura de Traspaso"

#. module: l10n_cl
#: model:uom.uom,name:l10n_cl.product_uom_u
msgid "U(JGO)"
msgstr "U(JGO)"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_y_f_dte
msgid "Unaffected or Exempt Electronic Invoice"
msgstr "Factura no Afecta o Exenta Electrónica"

#: model_terms:ir.ui.view,arch_db:l10n_cl.tax_totals_widget
msgid "VAT"
msgstr "IVA"

#. module: l10n_cl
#: model:ir.model.fields.selection,name:l10n_cl.selection__res_partner__l10n_cl_sii_taxpayer_type__1
msgid "VAT Affected (1st Category)"
msgstr "IVA afecto 1ª categoría"

#. module: l10n_cl
#: model:ir.model.fields,field_description:l10n_cl.field_account_bank_statement_line__partner_id_vat
#: model:ir.model.fields,field_description:l10n_cl.field_account_move__partner_id_vat
msgid "VAT No"
msgstr "RUT Nº"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_iva_no_recup
msgid "VAT Paid Not Recoverable"
msgstr "IVA Pagado No Recuperable"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_iva_uso_comun
msgid "VAT Paid Purchases Common Use"
msgstr "IVA Pagado Compras Uso Común"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_iva_recup
msgid "VAT Paid Purchases Recoverable"
msgstr "IVA Pagado Compras Recuperables"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_compras_iva_supermercado
msgid "VAT Paid Supermarket Purchases"
msgstr "IVA Pagado Compras Supermercado"

#. module: l10n_cl
#: model:account.report.line,name:l10n_cl.tax_report_iva_debito_fiscal
msgid "VAT Tax Debit"
msgstr "IVA Débito Fiscal"

#. module: l10n_cl
#: model:l10n_latam.document.type,report_name:l10n_cl.dc_vp_pren
msgid "VLPR"
msgstr "VLPR"

#. module: l10n_cl
#: model:ir.actions.act_window,name:l10n_cl.vendor_bills_and_refunds
msgid "Vendor Bills and Refunds"
msgstr "Facturas y notas de crédito de proveedores"

#. module: l10n_cl
#: model:ir.ui.menu,name:l10n_cl.menu_vendor_bills_and_refunds
msgid "Vendor Bills and Refunds (CL)"
msgstr "Facturas de Proveedor y Notas de Crédito (CL)"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_mnt_iva
msgid "Ventas - Amount VAT"
msgstr "Ventas - Monto IVA"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_iva_no_retenido
msgid "Ventas - IVA No Retenido"
msgstr "Ventas - IVA No Retenido"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_iva_ret_parcial
msgid "Ventas - IVA Retenido Parcial"
msgstr "Ventas - IVA Retenido Parcial"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_iva_ret_total
msgid "Ventas - IVA Retenido Total"
msgstr "Ventas - IVA Retenido Total"

#. module: l10n_cl
#: model:account.account.tag,name:l10n_cl.tag_cl_sale_iva_terceros
msgid "Ventas - IVA Terceros"
msgstr "Ventas - IVA Terceros"

#. module: l10n_cl
#. odoo-python
#: code:addons/l10n_cl/models/account_move.py:0
msgid "You need a journal without the use of documents for foreign suppliers"
msgstr "Necesita un diario que no use documentos para registrar facturas de proveedores extranjeros"

#. module: l10n_cl
#: model:l10n_latam.document.type,name:l10n_cl.dc_bzf_f_dtn
msgid "ZF Modules Sale Ballots (all)"
msgstr "Boletas Venta Módulos ZF (todas)"
