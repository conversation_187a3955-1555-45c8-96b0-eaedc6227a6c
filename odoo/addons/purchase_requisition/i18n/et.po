# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition
# 
# Translators:
# <PERSON><PERSON> <armaged<PERSON><EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON> Õigus <<EMAIL>>, 2024
# JanaAvalah, 2024
# <PERSON><PERSON><PERSON> avalah, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> avalah, 2024\n"
"Language-Team: Estonian (https://app.transifex.com/odoo/teams/41243/et/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: et\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "$50"
msgstr "$50"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "$500"
msgstr "$500"

#. module: purchase_requisition
#: model:ir.actions.report,print_report_name:purchase_requisition.action_report_purchase_requisitions
msgid "'Purchase Agreement - %s' % (object.name)"
msgstr "'Ostuleping - %s' % (object.name)"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "02/16/2024"
msgstr "02/16/2024"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "12/25/2024"
msgstr "12/25/2024"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "2023-09-15"
msgstr "2023-09-15"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "<span><strong>From</strong></span>"
msgstr "<span><strong>Alates </strong></span>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "<span><strong>to</strong></span>"
msgstr "<span><strong>kuni </strong></span>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "<strong>Contact:</strong><br/>"
msgstr "<strong>Kontakt: </strong><br/>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "<strong>Reference:</strong><br/>"
msgstr "<strong>Viide:</strong><br/>"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction
msgid "Action Needed"
msgstr "Vajalik toiming"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__active
msgid "Active"
msgstr "Aktiivne"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_ids
msgid "Activities"
msgstr "Tegevused"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Tegevuse erandlik kohendus"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_state
msgid "Activity State"
msgstr "Tegevuse staatus"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Activity Type Icon"
msgstr "Tegevustüübi ikoon"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__name
msgid "Agreement"
msgstr "Leping"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_type
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__requisition_type
msgid "Agreement Type"
msgstr "Lepingu tüüp"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Agreement Validity"
msgstr "Lepingu kehtivus"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Agreement Validity:"
msgstr "Lepingu kehtivus"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__alternative_po_ids
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__alternative_po_ids
msgid "Alternative POs"
msgstr "Alternatiivsed ostutellimused"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternative Purchase Order"
msgstr "Alternatiivne ostutellimus"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Alternative Warning"
msgstr "Alternatiivi hoiatus"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternatives"
msgstr "Alternatiivid"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "An example of a purchase agreement is a blanket order."
msgstr "Üks võimalik ostulepingu variant on üldine hinnakokkulepe."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analüütiline jaotus"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_precision
msgid "Analytic Precision"
msgstr "Analüütiline täpsus"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Archived"
msgstr "Arhiveeritud"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_attachment_count
msgid "Attachment Count"
msgstr "Manuste arv"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "BO00004"
msgstr "BO00004"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__requisition_type__blanket_order
msgid "Blanket Order"
msgstr "Üldine ostukokkulepe"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Blanket Orders"
msgstr "Üldised ostukokkulepped"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Buyer"
msgstr "Ostja"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel"
msgstr "Tühista"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Cancel Alternatives"
msgstr "Tühista alternatiivid"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__cancel
msgid "Cancelled"
msgstr "Tühistatud"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Cancelled by the agreement associated to this quotation."
msgstr "Selle hinnapakkumisega seotud leping on tühistatud."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid "Category"
msgstr "Kategooria"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Choose"
msgstr "Vali"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
msgid "Choose a vendor for alternative PO"
msgstr "Vali alternatiivse ostutellimuse tarnija"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear"
msgstr "Tühjenda"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear Selected"
msgstr "Tühjenda valik"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close"
msgstr "Sulge"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__done
msgid "Closed"
msgstr "Lõpetatud"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Code"
msgstr "Kood"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__company_id
msgid "Company"
msgstr "Ettevõte"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line__company_currency_id
msgid "Company Currency"
msgstr "Ettevõtte valuuta"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line__price_total_cc
msgid "Company Subtotal"
msgstr "Ettevõtte vahesumma"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Company Total"
msgstr "Ettevõtte summa"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Compare Order Lines"
msgstr "Võrdle tellimuste ridasid"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Compare Product Lines"
msgstr "Võrdle toodete ridasid"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_res_config_settings
msgid "Config Settings"
msgstr "Seadistused"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm"
msgstr "Kinnitage"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__confirmed
msgid "Confirmed"
msgstr "Kinnitatud"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Ühikute vaheline konversioon on võimalik ainult siis, kui ühikud kuuluvad "
"samasse kategooriasse. Konverteerimine toimub määrade alusel."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid "Copy Products"
msgstr "Kopeeri tooted"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
msgid "Create Alternative"
msgstr "Loo alternatiiv"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid ""
"Create a call for tender by adding alternative requests for quotation to different vendors.\n"
"                            Make your choice by selecting the best combination of lead time, OTD and/or total amount.\n"
"                            By comparing product lines you can also decide to order some products from one vendor and others from another vendor."
msgstr ""
"Loo hankekutse, lisades erinevatele müüjatele alternatiivseid hinnapäringuid.\n"
"Vali parim kombinatsioon tarneaja, OTD ja/või kogusumma alusel.\n"
"Võrreldes toote ridu võid otsustada tellida mõned tooted ühelt müüjalt ja teised teiselt."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Create alternative"
msgstr "Loo alternatiiv"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_uid
msgid "Created by"
msgstr "Loonud"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_date
msgid "Created on"
msgstr "Loodud"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid "Creation Blocked"
msgstr "Loomine on blokeeritud"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__currency_id
msgid "Currency"
msgstr "Valuuta"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Date"
msgstr "Kuupäev"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Demo Reference"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__description
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_description_variants
msgid "Description"
msgstr "Kirjeldus"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Discard"
msgstr "Loobu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__display_name
msgid "Display Name"
msgstr "Kuvatav nimi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Jaotumise analüütiline konto"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Done"
msgstr "Tehtud"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__draft
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Draft"
msgstr "Mustand"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_end
msgid "End Date"
msgstr "Lõppkuupäev"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"End date cannot be earlier than start date. Please check dates for "
"agreements: %s"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Expected on"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_follower_ids
msgid "Followers"
msgstr "Jälgijad"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_partner_ids
msgid "Followers (Partners)"
msgstr "Jälgijad(Partnerid)"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome icon nt. fa-tasks"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"For a blanket order, you can record an agreement for a specific period\n"
"            (e.g. a year) and you order products within this agreement to benefit\n"
"            from the negotiated prices."
msgstr ""
"Üldine ostukokkulepe võimaldab teha lepingut kindlaks ajaperioodiks\n"
"            (nt. aastaks) ja võimaldab tellida tooteid selle lepingu raames \n"
"            kokkulepitud hindadega."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Future Activities"
msgstr "Tulevased tegevused"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Rühmitamine"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__has_alternatives
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_kpis_tree_inherit_purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_tree_inherit_purchase_requisition
msgid "Has Alternatives"
msgstr "On alternatiive"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__has_message
msgid "Has Message"
msgstr "On sõnum"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__id
msgid "ID"
msgstr "ID"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon"
msgstr "sümbolit."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikoon, mis näitab erandi tegevust."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Kui kontrollitud, siis uued sõnumid nõuavad Teie tähelepanu."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Kui valitud, on mõningate sõnumitel saatmiserror"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid ""
"If the chosen vendor or if any of the products in the original PO have a "
"blocking warning then we prevent creation of alternative PO. This is because"
" normally these fields are cleared w/warning message within form view, but "
"we cannot recreate that in this case."
msgstr ""
"Kui valitud tarnijal või mõnel tootel on ostutellimusel blokeerimishoiatus, "
"siis süsteem ei võimalda alternatiivset ostutellimust luua. Selle põhjuseks "
"on asjaolu, et tavaliselt tühjendatakse need väljad vormivaates "
"hoiatusteadetega, kuid antud juhul ei saa me seda uuesti luua."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid ""
"If this is checked, the product quantities of the original PO will be copied"
msgstr "Kui on märgitud, kopeeritakse algse ostutellimuse tootekogused"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_is_follower
msgid "Is Follower"
msgstr "On jälgija"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Keep Alternatives"
msgstr "Hoia alternatiivsed hinnapäringud alles"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_uid
msgid "Last Updated by"
msgstr "Viimati uuendatud"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_date
msgid "Last Updated on"
msgstr "Viimati uuendatud"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Late Activities"
msgstr "Hilinenud tegevused"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.res_config_settings_view_form_purchase_requisition
msgid "Link RFQs together and compare them"
msgstr "Seo ostupäringud ja võrlde neid"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Link to Existing RfQ"
msgstr "Vali olemasolevate hinnapäringute hulgast"

#. module: purchase_requisition
#: model:res.groups,name:purchase_requisition.group_purchase_alternatives
msgid "Manage Purchase Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error
msgid "Message Delivery error"
msgstr "Sõnumi saatmise veateade"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_ids
msgid "Messages"
msgstr "Sõnum"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Mitchell Admin"
msgstr "Mitchell Admin"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Minu tegevuse tähtaeg"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "My Agreements"
msgstr "Minu lepingud"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Name, TIN, Email, or Reference"
msgstr "Nimi, TIN, e-post või viide"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "New"
msgstr "Uus"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Agreements"
msgstr "Uued lepingud"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "New Quotation"
msgstr "Uus pakkumine"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Järgmine tegevus kalendris"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Järgmise tegevuse tähtaeg"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_summary
msgid "Next Activity Summary"
msgstr "Järgmise tegevuse kokkuvõte"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_id
msgid "Next Activity Type"
msgstr "Järgmise tegevuse tüüp"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Nothing to clear"
msgstr "Pole midagi tühjendada"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of Actions"
msgstr "Tegevuste arv"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__order_count
msgid "Number of Orders"
msgstr "Tellimuste arv"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of errors"
msgstr "Vigade arv"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Tegevust nõudvate sõnumite arv"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Veateatega sõnumite arv"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__order_ids
msgid "Order"
msgstr "Ost"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__qty_ordered
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Ordered"
msgstr "Tellitud"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr "Tellimise kuupäev"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Orders"
msgstr "Tellimused"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "Origin Po"
msgstr "Algne ostutellimus"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__alternative_po_ids
msgid "Other potential purchase orders for purchasing products"
msgstr "Muud potentsiaalsed ostutellimused toodete ostmiseks"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__po_ids
msgid "POs to Confirm"
msgstr "Ostutellimused kinnitamiseks"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__product_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Product"
msgstr "Toode"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_id
msgid "Product Unit of Measure"
msgstr "Toote mõõtühik"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_product
msgid "Product Variant"
msgstr "Toote variatsioon"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Tooted"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__line_ids
msgid "Products to Purchase"
msgstr "Tooted ostmiseks"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__requisition_id
msgid "Purchase Agreement"
msgstr "Ostulepingud"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Purchase Agreement:"
msgstr "Ostuleping:"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.actions.report,name:purchase_requisition.action_report_purchase_requisitions
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Purchase Agreements"
msgstr "Ostulepingud"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_res_config_settings__group_purchase_alternatives
msgid "Purchase Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__purchase_group_id
msgid "Purchase Group"
msgstr "Ostugrupp"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
msgid "Purchase Order"
msgstr "Ostutellimus"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Ostutellimuse rida"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Purchase Order Lines"
msgstr "Ostutellimuse read"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__purchase_ids
msgid "Purchase Orders"
msgstr "Ostutellimused"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr "Ostutellimused koos ostupäringuga"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Purchase Reference"
msgstr "Ostu viide"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Representative"
msgstr "Ostuesindaja"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Ostupäring"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_line_id
msgid "Purchase Requisition Line"
msgstr "Ostupäringu rida"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__requisition_type__purchase_template
msgid "Purchase Template"
msgstr "Ostu mall"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Templates"
msgstr "Ostu mallid"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Quantity"
msgstr "Kogus"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "RFQ"
msgstr "Hinnapäring"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Orders"
msgstr "Hinnapäringud/tellimused"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__rating_ids
msgid "Ratings"
msgstr "Hinnangud"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__reference
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Reference"
msgstr "Viide"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_to_so
msgid "Request for Quotation"
msgstr "Hinnapäring"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_list
msgid "Request for Quotations"
msgstr "Hinnapäringud"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr "Päring"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Lähtesta mustandiks"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_user_id
msgid "Responsible User"
msgstr "Vastutav kasutaja"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sõnumi kohaletoimetamise viga"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Purchase Agreements"
msgstr "Otsi ostulepingut"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Show all records which has next action date is before today"
msgstr ""
"Näita kõiki andmeid, mille järgmise tegevuse kuupäev on ennem tänast "
"kuupäeva"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Some not cleared"
msgstr "Mõned on kustutamata"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid ""
"Some quantities were not cleared because their status is not a RFQ status."
msgstr ""
"Mõned kogused jäid kustutamata, kuna nad ei olnud hinnapäringu staatuses."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_start
msgid "Start Date"
msgstr "Alguskuupäev"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Start a new purchase agreement"
msgstr "Alusta uut ostulepingut"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Olek"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Tegevuspõhised staatused\n"
"Üle aja: Tähtaeg on juba möödas\n"
"Täna: Tegevuse tähtaeg on täna\n"
"Planeeritud: Tulevased tegevused."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__supplier_info_ids
msgid "Supplier Info"
msgstr "Tarnija info"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Tarnija hinnakiri"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_group
msgid "Technical model to group PO for call to tenders"
msgstr "Tehniline mudel võrdlevate hinnapakkumiste grupeerimiseks"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr "Tingimused"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "The original PO that this alternative PO is being created for."
msgstr "Algne ostutellimus, millele see alternatiivne ostutellimus luuakse"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"The vendor you have selected or at least one of the products you are copying"
" from the original order has a blocking warning on it and cannot be selected"
" to create an alternative."
msgstr ""
"Valitud tarnija või vähemalt üks valitud toodetest, mille oled algselt "
"hinnapäringult valinud, sisaldab blokeerivat hoiatust ning selle tarnija või"
" tootega ei saa alternatiivset hinnapakkumist luua."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "There are no quantities to clear."
msgstr "Kogused kustutamiseks puuduvad"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"There is already an open blanket order for this supplier. We suggest you "
"complete this open blanket order, instead of creating a new one."
msgstr ""
"Selle tarnija jaoks on juba avatud üldine ostukokkulepe. Soovitame uue "
"kokkuleppe loomise asemel lõpetada avatud kokkulepe."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid "This is a blocking warning!\n"
msgstr "See on blokeeriv hoiatus!\n"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"To close this purchase requisition, cancel related Requests for Quotation.\n"
"\n"
"Imagine the mess if someone confirms these duplicates: double the order, double the trouble :)"
msgstr ""
"Selle ostutaotluse sulgemiseks tühistage seotud hinnapakkumised.\n"
"\n"
"Kujutage ette segadust, kui keegi kinnitab need dublikaadid: topelttellimus -> topeltprobleem :)"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Today Activities"
msgstr "Tänased tegevused"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Total"
msgstr "Kokku"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kirjel oleva erandtegevuse tüüp."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Unit"
msgstr "Ühik"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Unit Price"
msgstr "Ühikhind"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "UoM"
msgstr "Mõõtühikud"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__vendor_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Vendor"
msgstr "Tarnija"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__purchase_warn_msg
msgid "Warning Messages"
msgstr "Hoiatavad sõnumid"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(partner)s:\n"
"%(warning_message)s\n"
msgstr ""
"Tarnija hoiatus %(partner)s:\n"
"%(warning_message)s\n"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(product)s:\n"
"%(warning_message)s\n"
msgstr ""
"Toote hoiatus %(product)s:\n"
"%(warning_message)s\n"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Warning for %s"
msgstr "Hoiatus %s"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website Messages"
msgstr "Veebilehe sõnumid"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website communication history"
msgstr "Veebilehe suhtluse ajalugu"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "What about the alternative Requests for Quotations?"
msgstr "Mis on alternatiivsed hinnapäringud?"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__has_alternatives
msgid ""
"Whether or not this purchase order is linked to another purchase order as an"
" alternative."
msgstr "See ostutellimus on teise ostutellimuse alternatiiv."

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_alternative_warning
msgid "Wizard in case PO still has open alternative requests for quotation"
msgstr ""
"Soovitus juhuks, kui ostutellimusel on veel avatud alternatiivseid pakkumise"
" taotlusi"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_create_alternative
msgid "Wizard to preset values for alternative PO"
msgstr "Soovitused alternatiivse ostutellimuse eelseadistatud väärtustele"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You can only delete draft or cancelled requisitions."
msgstr "Saate kustutada ainult mustand või tühistatud päringuid"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot change the Agreement Type or Company of a not draft purchase "
"agreement."
msgstr ""
"Te ei saa muuta lepingu tüüpi ega partnerit, kui leping ei ole mustand "
"staatuses"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm a blanket order with lines missing a price."
msgstr ""
"Te ei saa kinnitada üldist ostukokkulepet, kui ridadel puuduvad hinnad"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm a blanket order with lines missing a quantity."
msgstr "Te ei saa kinnitada üldist ostukokkulepet ilma koguseta"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot confirm agreement '%(agreement)s' because it does not contain any"
" product lines."
msgstr ""
"Te ei saa kinnitada üldist ostukokkulepet '%(agreement)s' kuna see ei "
"sisalda ühtegi toote rida"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot have a negative or unit price of 0 for an already confirmed "
"blanket order."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr "nt PO0025"
