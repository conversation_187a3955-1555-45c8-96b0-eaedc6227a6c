# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_crm_iap_reveal
# 
# Translators:
# emre <PERSON>tem, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# Halil, 2024
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid ""
"1 credit is consumed per visitor matching the website traffic conditions and"
" whose company can be identified.<br/>"
msgstr ""
"Web sitesi trafik koşullarına uyan ve şirketi tanımlanabilen ziyaretçi "
"başına 1 kredi tüketilir.<br/>"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Leads </span>"
msgstr "<span class=\"o_stat_text\"> Adaylar </span>"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Opportunities </span>"
msgstr "<span class=\"o_stat_text\"> Fırsatlar </span>"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__active
msgid "Active"
msgstr "Etkin"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Archived"
msgstr "Arşivlendi"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_reveal_rule
msgid "CRM Lead Generation Rules"
msgstr "CRM Aday Oluşturma Kuralı"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_reveal_view
msgid "CRM Reveal View"
msgstr "CRM Reveal View"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__lead_for
msgid "Choose whether to track companies only or companies and their contacts"
msgstr ""
"Yalnızca şirketleri mi yoksa şirketleri ve kişilerini mi izleyeceğinizi "
"seçin"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_for__companies
msgid "Companies"
msgstr "Şirketler"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_for__people
msgid "Companies and their Contacts"
msgstr "Şirketler ve Bağlantıları"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__company_size_min
msgid "Company Size"
msgstr "Şirket büyüklüğü"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__company_size_max
msgid "Company Size Max"
msgstr "Maksimum Şirket Büyüklüğü"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Contact Filter"
msgstr "Kontak Filtresi"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__country_ids
msgid "Countries"
msgstr "Ülke"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__create_date
msgid "Create Date"
msgstr "Oluşturma Tarihi"

#. module: website_crm_iap_reveal
#: model_terms:ir.actions.act_window,help:website_crm_iap_reveal.crm_reveal_rule_action
msgid "Create a conversion rule"
msgstr "Dönüşüm kuralı oluşturma"

#. module: website_crm_iap_reveal
#: model_terms:ir.actions.act_window,help:website_crm_iap_reveal.crm_reveal_rule_action
msgid ""
"Create rules to generate B2B leads/opportunities from your website visitors."
msgstr ""
"Web sitesi ziyaretçilerinizden B2B potansiyel müşteriler/fırsatlar "
"oluşturmak için kurallar oluşturun."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__create_uid
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_for
msgid "Data Tracking"
msgstr "Veri İzleme"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__display_name
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: website_crm_iap_reveal
#. odoo-python
#: code:addons/website_crm_iap_reveal/models/crm_reveal_rule.py:0
msgid "Enter Valid Regex."
msgstr "Geçerli değeri girin."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__contact_filter_type
msgid "Filter On"
msgstr "Filtre Açık"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__filter_on_size
msgid "Filter companies based on their size."
msgstr "Şirketleri büyüklüklerine göre filtreleyin."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__filter_on_size
msgid "Filter on Size"
msgstr "Boyuta Göre Filtrele"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "From"
msgstr "Başlama"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_ids
msgid "Generated Lead / Opportunity"
msgstr "Oluşturulmuş Aday / Fırsat"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP Yönlendirme"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__2
msgid "High"
msgstr "Yüksek"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_iap_credits
msgid "IAP Credits"
msgstr "IAP Kredileri"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__id
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__id
msgid "ID"
msgstr "ID"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_ip
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_ip
msgid "IP Address"
msgstr "IP Adresi"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__industry_tag_ids
msgid "Industries"
msgstr "Sektörler"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__write_uid
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__write_date
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_type__lead
msgid "Lead"
msgstr "Aday"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Lead Data"
msgstr "Aday Verisi"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_lead_opportunity_form
msgid "Lead Generation Information"
msgstr "Aday Oluşturma Bilgisi"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_rule_id
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_rule_id
msgid "Lead Generation Rule"
msgstr "Aday Oluşturma Kuralı"

#. module: website_crm_iap_reveal
#: model:ir.actions.act_window,name:website_crm_iap_reveal.crm_reveal_view_action
#: model:ir.ui.menu,name:website_crm_iap_reveal.crm_reveal_view_menu_action
msgid "Lead Generation Views"
msgstr "Potansiyel Müşteri Oluşturma Görünümleri"

#. module: website_crm_iap_reveal
#: model:ir.actions.server,name:website_crm_iap_reveal.ir_cron_crm_reveal_lead_ir_actions_server
msgid "Lead Generation: Leads/Opportunities Generation"
msgstr "Aday Oluşturma: Adaylar/Fırsatlar Oluşturma"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Aday/Fırsat"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__industry_tag_ids
msgid "Leave empty to always match. Odoo will not create lead if no match"
msgstr ""
"Her zaman eşleşecek şekilde boş bırakın. Odoo eşleşme olmazsa liderlik "
"yaratmayacak"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__0
msgid "Low"
msgstr "Düşük"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""
"Kişisel verileri depolamak için GDPR uyumlu olmanız gerekip gerekmediğini "
"bildiğinizden emin olun."

#. module: website_crm_iap_reveal
#: model:ir.model.constraint,message:website_crm_iap_reveal.constraint_crm_reveal_rule_limit_extra_contacts
msgid "Maximum 5 contacts are allowed!"
msgstr "En fazla 5 kişiye izin verilir!"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__1
msgid "Medium"
msgstr "Aracı:"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_view__reveal_state__not_found
msgid "Not Found"
msgstr "Bulunamadı"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__extra_contacts
msgid "Number of Contacts"
msgstr "Kontak Sayısı"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_count
msgid "Number of Generated Leads"
msgstr "Oluşturulan Adayların Sayısı"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__opportunity_count
msgid "Number of Generated Opportunity"
msgstr "Oluşturulan Fırsatların Sayısı"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__country_ids
msgid ""
"Only visitors of following countries will be converted into "
"leads/opportunities (using GeoIP)."
msgstr ""
"Sadece takip eden ülkelerin ziyaretçileri aday/fırsata dönüştürülür (GeoIP "
"kullanarak)."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__state_ids
msgid ""
"Only visitors of following states will be converted into "
"leads/opportunities."
msgstr ""
"Yalnızca aşağıdaki eyaletlerin ziyaretçileri potansiyel "
"müşterilere/fırsatlara dönüştürülecektir."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "Fırsat"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Opportunity Data"
msgstr "Fırsat Verisi"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Opportunity Generation Conditions"
msgstr "Fırsat Oluşturma Şartları"

#. module: website_crm_iap_reveal
#. odoo-python
#: code:addons/website_crm_iap_reveal/models/crm_reveal_rule.py:0
msgid "Opportunity created by Odoo Lead Generation"
msgstr "Odoo Aday Oluşturma ile oluşturulan fırsat"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__other_role_ids
msgid "Other Roles"
msgstr "Diğer Roller"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__preferred_role_id
msgid "Preferred Role"
msgstr "Tercih Edilen Rol"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__priority
msgid "Priority"
msgstr "Öncelik"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__regex_url
msgid ""
"Regex to track website pages. Leave empty to track the entire website, or / "
"to target the homepage. Example: /page* to track all the pages which begin "
"with /page"
msgstr ""
"Web sitesi sayfalarını izlemek için Regex. Web sitesinin tamamını izlemek "
"veya / ana sayfayı hedeflemek için boş bırakın. Örnek: /page* /page ile "
"başlayan tüm sayfaları izlemek için"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__website_id
msgid "Restrict Lead generation to this website."
msgstr "Müşteri adayı oluşturmayı bu web sitesiyle sınırlayın."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__contact_filter_type__role
msgid "Role"
msgstr "Rol"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Rule"
msgstr "Kural"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__name
msgid "Rule Name"
msgstr "Kural Adı"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__team_id
msgid "Sales Team"
msgstr "Satış Ekibi"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__user_id
msgid "Salesperson"
msgstr "Satış Temsilcisi"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Search CRM Reveal Rule"
msgstr "Arama CRM Gösterim Kuralı"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__seniority_id
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__contact_filter_type__seniority
msgid "Seniority"
msgstr "Kıdem"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__sequence
msgid "Sequence"
msgstr "Sıralama"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_state
msgid "State"
msgstr "İl/Eyalet"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__state_ids
msgid "States"
msgstr "İl/Eyalet"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__suffix
msgid "Suffix"
msgstr "Sonek"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__tag_ids
msgid "Tags"
msgstr "Etiketler"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__extra_contacts
msgid ""
"This is the number of contacts to track if their role/seniority match your "
"criteria. Their details will show up in the history thread of generated "
"leads/opportunities. One credit is consumed per tracked contact."
msgstr ""
"Bu, rollerinin / kıdemlerinin ölçütlerinizle eşleşip eşleşmediğini izlenecek"
" ilgili kişi sayısıdır. Ayrıntıları, oluşturulan potansiyel müşterilerin / "
"fırsatların geçmiş başlığında görünecektir. İzlenen kişi başına bir kredi "
"kullanılır."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__suffix
msgid ""
"This will be appended in name of generated lead so you can identify "
"lead/opportunity is generated with this rule"
msgstr ""
"Bu, oluşturulan adayın ismine eklenmiş olacaktır, bu yüzden, bu rol ile "
"oluşturulan adayı/fırsatı tanımlayabilirsiniz"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_view__reveal_state__to_process
msgid "To Process"
msgstr "İşlenecek"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_type
msgid "Type"
msgstr "Tip"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__regex_url
msgid "URL Expression"
msgstr "URL İfadesi"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Up to"
msgstr "Up to"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__sequence
msgid ""
"Used to order the rules with same URL and countries. Rules with a lower "
"sequence number will be processed first."
msgstr ""
"Aynı URL ve ülkelerle kuralları sipariş etmek için kullanılır. İlk önce sıra"
" numarası düşük olan kurallar işlenecektir."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__3
msgid "Very High"
msgstr "Çok Yüksek"

#. module: website_crm_iap_reveal
#: model:ir.actions.act_window,name:website_crm_iap_reveal.crm_reveal_rule_action
#: model:ir.ui.menu,name:website_crm_iap_reveal.crm_reveal_rule_menu_action
msgid "Visits to Leads Rules"
msgstr "Müşteri Adayları Kurallarına Ziyaretler"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__website_id
msgid "Website"
msgstr "Websitesi"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Website Traffic Conditions"
msgstr "Web Sitesi Trafik Koşulları"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "additional credit(s) are consumed if the company matches this rule."
msgstr "şirket bu kurala uyuyorsa ek kredi(ler) tüketilir."

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "e.g. /page"
msgstr "örn. /page"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "e.g. US Visitors"
msgstr "örn. Amerikan Ziyaretçiler"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "employees"
msgstr "personel"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "to"
msgstr "den"
