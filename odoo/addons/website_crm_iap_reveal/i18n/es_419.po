# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_crm_iap_reveal
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid ""
"1 credit is consumed per visitor matching the website traffic conditions and"
" whose company can be identified.<br/>"
msgstr ""
"Se consume 1 crédito por cada visitante que cumpla las condiciones de "
"tráfico del sitio web y cuya empresa se pueda identificar.<br/>"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Leads </span>"
msgstr "<span class=\"o_stat_text\"> Leads </span>"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "<span class=\"o_stat_text\"> Opportunities </span>"
msgstr "<span class=\"o_stat_text\"> Oportunidades </span>"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__active
msgid "Active"
msgstr "Activo"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Archived"
msgstr "Archivado"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_reveal_rule
msgid "CRM Lead Generation Rules"
msgstr "Reglas de generación de leads de CRM"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_reveal_view
msgid "CRM Reveal View"
msgstr "Vista de revelación de CRM"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__lead_for
msgid "Choose whether to track companies only or companies and their contacts"
msgstr ""
"Elija si quiere seguir solo a las empresas o a las empresas y sus contactos"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_for__companies
msgid "Companies"
msgstr "Empresas"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_for__people
msgid "Companies and their Contacts"
msgstr "Empresas y sus contactos"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__company_size_min
msgid "Company Size"
msgstr "Tamaño de la empresa"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__company_size_max
msgid "Company Size Max"
msgstr "Tamaño máximo de la empresa"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Contact Filter"
msgstr "Filtro de contacto"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__country_ids
msgid "Countries"
msgstr "Países"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__create_date
msgid "Create Date"
msgstr "Fecha de creación"

#. module: website_crm_iap_reveal
#: model_terms:ir.actions.act_window,help:website_crm_iap_reveal.crm_reveal_rule_action
msgid "Create a conversion rule"
msgstr "Crear una regla de conversión"

#. module: website_crm_iap_reveal
#: model_terms:ir.actions.act_window,help:website_crm_iap_reveal.crm_reveal_rule_action
msgid ""
"Create rules to generate B2B leads/opportunities from your website visitors."
msgstr ""
"Cree reglas para generar leads u oportunidades B2B con los visitantes de su "
"sitio web."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__create_uid
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__create_date
msgid "Created on"
msgstr "Creado el"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_for
msgid "Data Tracking"
msgstr "Rastreo de datos"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__display_name
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: website_crm_iap_reveal
#. odoo-python
#: code:addons/website_crm_iap_reveal/models/crm_reveal_rule.py:0
msgid "Enter Valid Regex."
msgstr "Escriba una expresión regular válida."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__contact_filter_type
msgid "Filter On"
msgstr "Filtrar por"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__filter_on_size
msgid "Filter companies based on their size."
msgstr "Filtre empresas según su tamaño."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__filter_on_size
msgid "Filter on Size"
msgstr "Filtrar por tamaño"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "From"
msgstr "Desde"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_ids
msgid "Generated Lead / Opportunity"
msgstr "Lead / Oportunidad generada"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_ir_http
msgid "HTTP Routing"
msgstr "Enrutamiento HTTP"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__2
msgid "High"
msgstr "Alta"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_iap_credits
msgid "IAP Credits"
msgstr "Créditos IAP"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__id
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__id
msgid "ID"
msgstr "ID"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_ip
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_ip
msgid "IP Address"
msgstr "Dirección IP"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__industry_tag_ids
msgid "Industries"
msgstr "Sectores"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__write_uid
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__write_date
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_type__lead
msgid "Lead"
msgstr "Lead"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Lead Data"
msgstr "Datos del lead"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_lead_opportunity_form
msgid "Lead Generation Information"
msgstr "Información sobre la generación de leads"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_lead__reveal_rule_id
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_rule_id
msgid "Lead Generation Rule"
msgstr "Regla de generación de leads"

#. module: website_crm_iap_reveal
#: model:ir.actions.act_window,name:website_crm_iap_reveal.crm_reveal_view_action
#: model:ir.ui.menu,name:website_crm_iap_reveal.crm_reveal_view_menu_action
msgid "Lead Generation Views"
msgstr "Vistas de generación de leads"

#. module: website_crm_iap_reveal
#: model:ir.actions.server,name:website_crm_iap_reveal.ir_cron_crm_reveal_lead_ir_actions_server
msgid "Lead Generation: Leads/Opportunities Generation"
msgstr "Generación de leads: Generación de leads y oportunidades"

#. module: website_crm_iap_reveal
#: model:ir.model,name:website_crm_iap_reveal.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Lead/oportunidad"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__industry_tag_ids
msgid "Leave empty to always match. Odoo will not create lead if no match"
msgstr ""
"Deje vacío para que siempre coincida. Odoo no creará un lead si no coincide"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__0
msgid "Low"
msgstr "Baja"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid ""
"Make sure you know if you have to be GDPR compliant for storing personal "
"data."
msgstr ""
"Asegúrese de saber si tiene que cumplir con el Reglamento general de "
"protección de datos (RGPD) para almacenar datos personales."

#. module: website_crm_iap_reveal
#: model:ir.model.constraint,message:website_crm_iap_reveal.constraint_crm_reveal_rule_limit_extra_contacts
msgid "Maximum 5 contacts are allowed!"
msgstr "¡Se permite un máximo de 5 contactos!"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__1
msgid "Medium"
msgstr "Media"

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_view__reveal_state__not_found
msgid "Not Found"
msgstr "No se encuentra"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__extra_contacts
msgid "Number of Contacts"
msgstr "Número de contactos"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_count
msgid "Number of Generated Leads"
msgstr "Número de leads generados"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__opportunity_count
msgid "Number of Generated Opportunity"
msgstr "Número de oportunidades generadas"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__country_ids
msgid ""
"Only visitors of following countries will be converted into "
"leads/opportunities (using GeoIP)."
msgstr ""
"Solo los visitantes de los siguientes países se convertirán en "
"leads/oportunidades (con el uso de GeoIP)."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__state_ids
msgid ""
"Only visitors of following states will be converted into "
"leads/opportunities."
msgstr ""
"Solo los visitantes de los siguientes estados se convertirán en "
"leads/oportunidades."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__lead_type__opportunity
msgid "Opportunity"
msgstr "Oportunidad"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Opportunity Data"
msgstr "Datos de la oportunidad"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Opportunity Generation Conditions"
msgstr "Condiciones de generación de oportunidades"

#. module: website_crm_iap_reveal
#. odoo-python
#: code:addons/website_crm_iap_reveal/models/crm_reveal_rule.py:0
msgid "Opportunity created by Odoo Lead Generation"
msgstr "Oportunidad creada mediante la generación de leads de Odoo"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__other_role_ids
msgid "Other Roles"
msgstr "Otras funciones "

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__preferred_role_id
msgid "Preferred Role"
msgstr "Función preferida"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__priority
msgid "Priority"
msgstr "Prioridad"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__regex_url
msgid ""
"Regex to track website pages. Leave empty to track the entire website, or / "
"to target the homepage. Example: /page* to track all the pages which begin "
"with /page"
msgstr ""
"Expresión regular para rastrear las páginas del sitio web. Déjela vacía para"
" rastrearlo por completo o use / para tener la página principal como "
"objetivo. Por ejemplo: /page* para rastrear todas las páginas que inician "
"con /page."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__website_id
msgid "Restrict Lead generation to this website."
msgstr "Restringir la generación de leads a este sitio web."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__contact_filter_type__role
msgid "Role"
msgstr "Función"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Rule"
msgstr "Regla"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__name
msgid "Rule Name"
msgstr "Nombre de la regla"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__team_id
msgid "Sales Team"
msgstr "Equipo de ventas"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__user_id
msgid "Salesperson"
msgstr "Vendedor"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_view_search
msgid "Search CRM Reveal Rule"
msgstr "Buscar regla de revelación de CRM"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__seniority_id
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__contact_filter_type__seniority
msgid "Seniority"
msgstr "Antigüedad"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_view__reveal_state
msgid "State"
msgstr "Estado"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__state_ids
msgid "States"
msgstr "Estados"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__suffix
msgid "Suffix"
msgstr "Sufijo"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__tag_ids
msgid "Tags"
msgstr "Etiquetas"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__extra_contacts
msgid ""
"This is the number of contacts to track if their role/seniority match your "
"criteria. Their details will show up in the history thread of generated "
"leads/opportunities. One credit is consumed per tracked contact."
msgstr ""
"Este es el número de contactos que hay que rastrear si su función o "
"antigüedad coinciden con sus criterios. Los datos aparecerán en el hilo del "
"historial de leads u oportunidades generadas. Se consume un crédito por cada"
" contacto rastreado."

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__suffix
msgid ""
"This will be appended in name of generated lead so you can identify "
"lead/opportunity is generated with this rule"
msgstr ""
"Se agregará al nombre del lead generado para que pueda identificar que el "
"lead u oportunidad se creó con esta regla."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_view__reveal_state__to_process
msgid "To Process"
msgstr "Por procesar"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__lead_type
msgid "Type"
msgstr "Tipo"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__regex_url
msgid "URL Expression"
msgstr "Expresión de la URL"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Up to"
msgstr "Hasta"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,help:website_crm_iap_reveal.field_crm_reveal_rule__sequence
msgid ""
"Used to order the rules with same URL and countries. Rules with a lower "
"sequence number will be processed first."
msgstr ""
"Se utiliza para ordenar las reglas con la misma URL y países. Las reglas con"
" un número de secuencia más bajo se procesarán primero."

#. module: website_crm_iap_reveal
#: model:ir.model.fields.selection,name:website_crm_iap_reveal.selection__crm_reveal_rule__priority__3
msgid "Very High"
msgstr "Muy alta"

#. module: website_crm_iap_reveal
#: model:ir.actions.act_window,name:website_crm_iap_reveal.crm_reveal_rule_action
#: model:ir.ui.menu,name:website_crm_iap_reveal.crm_reveal_rule_menu_action
msgid "Visits to Leads Rules"
msgstr "Reglas de visitas a leads"

#. module: website_crm_iap_reveal
#: model:ir.model.fields,field_description:website_crm_iap_reveal.field_crm_reveal_rule__website_id
msgid "Website"
msgstr "Sitio web"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "Website Traffic Conditions"
msgstr "Condiciones de tráfico del sitio web"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "additional credit(s) are consumed if the company matches this rule."
msgstr ""
"créditos adicionales se consumirán si la empresa cumple con esta regla."

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "e.g. /page"
msgstr "Por ejemplo, /pagina"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "e.g. US Visitors"
msgstr "Por ejemplo, visitantes de Estados Unidos"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "employees"
msgstr "empleados"

#. module: website_crm_iap_reveal
#: model_terms:ir.ui.view,arch_db:website_crm_iap_reveal.crm_reveal_rule_form
msgid "to"
msgstr "para"
