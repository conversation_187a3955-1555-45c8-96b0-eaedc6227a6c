<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_annual_report_vat_vj" model="account.report">
        <field name="name">VJ VAT Report</field>
        <field name="sequence">1</field>
        <field name="country_id" ref="base.it"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_annual_report_vat_balance_vj" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_annual_report_line_reverse_charge_iva" model="account.report.line">
                <field name="name">Reverse Charge</field>
                <field name="code">VJ</field>
                <field name="children_ids">
                    <record id="tax_annual_report_line_vj1" model="account.report.line">
                        <field name="name">VJ1 - Purchases of goods from Vatican City and San Marino</field>
                        <field name="code">VJ1</field>
                        <field name="tax_tags_formula">vj1</field>
                    </record>
                    <record id="tax_annual_report_line_vj2" model="account.report.line">
                        <field name="name">VJ2 - Extraction of goods from VAT warehouses</field>
                        <field name="code">VJ2</field>
                        <field name="tax_tags_formula">vj2</field>
                    </record>
                    <record id="tax_annual_report_line_vj3" model="account.report.line">
                        <field name="name">VJ3 - Purchases of goods already in Italy or services, from non-residents</field>
                        <field name="code">VJ3</field>
                        <field name="tax_tags_formula">vj3</field>
                    </record>
                    <record id="tax_annual_report_line_vj4" model="account.report.line">
                        <field name="name">VJ4 - Fees paid to resellers of travel tickets and resellers of parking documents</field>
                        <field name="code">VJ4</field>
                        <field name="tax_tags_formula">vj4</field>
                    </record>
                    <record id="tax_annual_report_line_vj5" model="account.report.line">
                        <field name="name">VJ5 - Commissions paid by travel agents to their intermediaries</field>
                        <field name="code">VJ5</field>
                        <field name="tax_tags_formula">vj5</field>
                    </record>
                    <record id="tax_annual_report_line_vj6" model="account.report.line">
                        <field name="name">VJ6 - Purchases of scrap and other recovered materials</field>
                        <field name="code">VJ6</field>
                        <field name="tax_tags_formula">vj6</field>
                    </record>
                    <record id="tax_annual_report_line_vj7" model="account.report.line">
                        <field name="name">VJ7 - Purchases of industrial gold and pure silver made in Italy</field>
                        <field name="code">VJ7</field>
                        <field name="tax_tags_formula">vj7</field>
                    </record>
                    <record id="tax_annual_report_line_vj8" model="account.report.line">
                        <field name="name">VJ8 - Investment gold purchases made in Italy</field>
                        <field name="code">VJ8</field>
                        <field name="tax_tags_formula">vj8</field>
                    </record>
                    <record id="tax_annual_report_line_vj9" model="account.report.line">
                        <field name="name">VJ9 - Intra-EU Purchases of Goods</field>
                        <field name="code">VJ9</field>
                        <field name="tax_tags_formula">vj9</field>
                    </record>
                    <record id="tax_annual_report_line_vj10" model="account.report.line">
                        <field name="name">VJ10 - Imports of scrap and other recovered materials</field>
                        <field name="code">VJ10</field>
                        <field name="tax_tags_formula">vj10</field>
                    </record>
                    <record id="tax_annual_report_line_vj11" model="account.report.line">
                        <field name="name">VJ11 - Imports of industrial gold and pure silver</field>
                        <field name="code">VJ11</field>
                        <field name="tax_tags_formula">vj11</field>
                    </record>
                    <record id="tax_annual_report_line_vj12" model="account.report.line">
                        <field name="name">VJ12 - Subcontracting of services in the construction field</field>
                        <field name="code">VJ12</field>
                        <field name="tax_tags_formula">vj12</field>
                    </record>
                    <record id="tax_annual_report_line_vj13" model="account.report.line">
                        <field name="name">VJ13 - Purchases of buildings or portions of buildings used for capital purposes</field>
                        <field name="code">VJ13</field>
                        <field name="tax_tags_formula">vj13</field>
                    </record>
                    <record id="tax_annual_report_line_vj14" model="account.report.line">
                        <field name="name">VJ14 - Purchases of cell phones</field>
                        <field name="code">VJ14</field>
                        <field name="tax_tags_formula">vj14</field>
                    </record>
                    <record id="tax_annual_report_line_vj15" model="account.report.line">
                        <field name="name">VJ15 - Purchases of electronic products</field>
                        <field name="code">VJ15</field>
                        <field name="tax_tags_formula">vj15</field>
                    </record>
                    <record id="tax_annual_report_line_vj16" model="account.report.line">
                        <field name="name">VJ16 - Provision of services in the construction field</field>
                        <field name="code">VJ16</field>
                        <field name="tax_tags_formula">vj16</field>
                    </record>
                    <record id="tax_annual_report_line_vj17" model="account.report.line">
                        <field name="name">VJ17 - Purchases of energy sector goods and services</field>
                        <field name="code">VJ17</field>
                        <field name="tax_tags_formula">vj17</field>
                    </record>
                    <record id="tax_annual_report_line_vj18" model="account.report.line">
                        <field name="name">VJ18 - Purchases made by VAT-registered public administrations</field>
                        <field name="code">VJ18</field>
                        <field name="tax_tags_formula">vj18</field>
                    </record>
                    <record id="tax_annual_report_line_vj19" model="account.report.line">
                        <field name="name">VJ19 - Total frame VJ</field>
                        <field name="code">VJ19</field>
                        <field name="aggregation_formula">VJ1.balance + VJ2.balance + VJ3.balance + VJ4.balance + VJ5.balance + VJ6.balance + VJ7.balance +
                                                VJ8.balance + VJ9.balance + VJ10.balance + VJ11.balance + VJ12.balance + VJ13.balance + VJ14.balance +
                                                VJ15.balance + VJ16.balance + VJ17.balance + VJ18.balance</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
