<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_annual_report_vat_vf" model="account.report">
        <field name="name">VF VAT Report</field>
        <field name="sequence">1</field>
        <field name="country_id" ref="base.it"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_annual_report_vat_balance_vf" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
            <record id="tax_annual_report_vat_tax_vf" model="account.report.column">
                <field name="name">Tax</field>
                <field name="expression_label">tax</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_annual_report_line_passive_op" model="account.report.line">
                <field name="name">Passive operations and VAT deduction allowed</field>
                <field name="code">VF</field>
                <field name="children_ids">
                    <record id="tax_annual_report_line_passive_op_1" model="account.report.line">
                        <field name="name">Purchases (Domestic, intra-Community and imports)</field>
                        <field name="code">VF_1</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_VF1" model="account.report.line">
                                <field name="name">VF1 - compensation percentage 2%</field>
                                <field name="code">VF1</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF1_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf1</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF1_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF1.balance * 0.02</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF2" model="account.report.line">
                                <field name="name">VF2 - compensation percentage 4%</field>
                                <field name="code">VF2</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf2</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF2_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF2.balance * 0.04</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF3" model="account.report.line">
                                <field name="name">VF3 - compensation percentage 5%</field>
                                <field name="code">VF3</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf3</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF3_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF3.balance * 0.5</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF4" model="account.report.line">
                                <field name="name">VF4 - compensation percentage 6,4%</field>
                                <field name="code">VF4</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf4</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF4_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF4.balance * 0.064</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF5" model="account.report.line">
                                <field name="name">VF5 - compensation percentage 7%</field>
                                <field name="code">VF5</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF5_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf5</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF5_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF5.balance * 0.07</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF6" model="account.report.line">
                                <field name="name">VF6 - compensation percentage 7,3%</field>
                                <field name="code">VF6</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF6_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf6</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF6_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF6.balance * 0.073</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF7" model="account.report.line">
                                <field name="name">VF7 - compensation percentage 7,5%</field>
                                <field name="code">VF7</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF7_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf7</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF7_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF7.balance * 0.075</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF8" model="account.report.line">
                                <field name="name">VF8 - compensation percentage 8,3%</field>
                                <field name="code">VF8</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF8_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf8</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF8_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF8.balance * 0.083</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF9" model="account.report.line">
                                <field name="name">VF9 - compensation percentage 8,5%</field>
                                <field name="code">VF9</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF9_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf9</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF9_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF9.balance * 0.085</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF10" model="account.report.line">
                                <field name="name">VF10 - compensation percentage 8,8%</field>
                                <field name="code">VF10</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF10_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf10</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF10_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF10.balance * 0.088</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF11" model="account.report.line">
                                <field name="name">VF11 - compensation percentage 10%</field>
                                <field name="code">VF11</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF11_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf11</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF11_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF11.balance * 0.10</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF12" model="account.report.line">
                                <field name="name">VF12 - compensation percentage 12,3%</field>
                                <field name="code">VF12</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF12_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf12</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF12_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF12.balance * 0.123</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF13" model="account.report.line">
                                <field name="name">VF13 - compensation percentage 22%</field>
                                <field name="code">VF13</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF13_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf13</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF13_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF13.balance * 0.22</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF17" model="account.report.line">
                                <field name="name">VF17 - Purchases and imports without payment of tax, with use of the ceiling</field>
                                <field name="code">VF17</field>
                                <field name="tax_tags_formula">vf17</field>
                            </record>
                            <record id="tax_annual_report_line_VF18_I" model="account.report.line">
                                <field name="name">VF18 - Other non-tax purchases, not subject to tax and relating to certain special regimes</field>
                                <field name="code">VF18_I</field>
                                <field name="tax_tags_formula">vf18_I</field>
                            </record>
                            <record id="tax_annual_report_line_VF18_II" model="account.report.line">
                                <field name="name">VF18 - Exempt purchases and imports not subject to the tax</field>
                                <field name="code">VF18_II</field>
                                <field name="tax_tags_formula">vf18_II</field>
                            </record>
                            <record id="tax_annual_report_line_VF19" model="account.report.line">
                                <field name="name">VF19 - Purchases from subjects who have made use of concessional schemes</field>
                                <field name="code">VF19</field>
                                <field name="tax_tags_formula">vf19</field>
                            </record>
                            <record id="tax_annual_report_line_VF20" model="account.report.line">
                                <field name="name">VF20 - Purchases and imports not subject to the tax made by earthquake victims</field>
                                <field name="code">VF20</field>
                                <field name="tax_tags_formula">vf20</field>
                            </record>
                            <record id="tax_annual_report_line_VF21" model="account.report.line">
                                <field name="name">VF21 - Purchases and imports for which the deduction is excluded or reduced (art. 19-bis1)</field>
                                <field name="code">VF21</field>
                                <field name="tax_tags_formula">vf21</field>
                            </record>
                            <record id="tax_annual_report_line_VF22" model="account.report.line">
                                <field name="name">VF22 - Purchases and imports for which the deduction is not permitted</field>
                                <field name="code">VF22</field>
                                <field name="tax_tags_formula">vf22</field>
                            </record>
                            <record id="tax_annual_report_line_VF23" model="account.report.line">
                                <field name="name">VF23 - Purchases recorded in the year but with tax deduction deferred to subsequent years</field>
                                <field name="code">VF23</field>
                                <field name="tax_tags_formula">vf23</field>
                            </record>
                            <record id="tax_annual_report_line_VF24" model="account.report.line">
                                <field name="name">VF24 - Purchases recorded in previous years but with tax</field>
                                <field name="code">VF24</field>
                                <field name="tax_tags_formula">vf24</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_passive_op_2" model="account.report.line">
                        <field name="name">Total purchases and imports, total tax, purchases intra-community, imports and purchases</field>
                        <field name="code">VF_2</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_VF25" model="account.report.line">
                                <field name="name">VF25 - Total purchases and imports</field>
                                <field name="code">VF25</field>
                                <field name="aggregation_formula">VF1.tax + VF2.tax + VF3.tax + VF4.tax + VF5.tax + VF6.tax
                                                    + VF7.tax + VF8.tax + VF9.tax + VF10.tax + VF11.tax + VF12.tax
                                                    + VF13.tax + VF17.balance + VF18_I.balance + VF18_II.balance + VF19.balance
                                                    + VF20.balance + VF21.balance + VF22.balance + VF23.balance - VF24.balance</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF25_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF1.tax + VF2.tax + VF3.tax + VF4.tax + VF5.tax + VF6.tax + VF7.tax
                                                            + VF8.tax + VF9.tax + VF10.tax + VF11.tax + VF12.tax + VF13.tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF26" model="account.report.line">
                                <field name="name">VF26 - Tax variations and roundings (indicate with the +/- sign)</field>
                                <field name="code">VF26</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF26_tag" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf26</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF27" model="account.report.line">
                                <field name="name">VF27 - Total tax on taxable purchases and imports</field>
                                <field name="code">VF27</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF27_tag" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF25.tax + VF26.tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF28_I" model="account.report.line">
                                <field name="name">VF28 - Intra-community purchases</field>
                                <field name="code">VF28_I</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF28_I_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf28_i base</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF28_I_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf28_i tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF28_II" model="account.report.line">
                                <field name="name">VF28 - Imports</field>
                                <field name="code">VF28_II</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF28_II_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf28_ii base</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF28_II_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf28_ii tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF28_III" model="account.report.line">
                                <field name="name">VF28 - Purchases from San Marino</field>
                                <field name="code">VF28_III</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF28_III_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf28_iii base</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF28_III_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf28_iii tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF29" model="account.report.line">
                                <field name="name">VF29 - Total distribution of purchases and imports</field>
                                <field name="code">VF29</field>
                                <field name="children_ids">
                                    <record id="tax_annual_report_line_VF29_I" model="account.report.line">
                                        <field name="name">Depreciable assets</field>
                                        <field name="code">VF29_I</field>
                                        <field name="tax_tags_formula">vf29_i</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF29_II" model="account.report.line">
                                        <field name="name">Non-depreciable capital goods</field>
                                        <field name="code">VF29_II</field>
                                        <field name="tax_tags_formula">vf29_ii</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF29_III" model="account.report.line">
                                        <field name="name">Goods intended for resale or for the production of goods and services</field>
                                        <field name="code">VF29_III</field>
                                        <field name="tax_tags_formula">vf29_iii</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF29_IV" model="account.report.line">
                                        <field name="name">Other purchases and imports</field>
                                        <field name="code">VF29_IV</field>
                                        <field name="tax_tags_formula">vf29_iv</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_passive_op_3a" model="account.report.line">
                        <field name="name">Exempt operations</field>
                        <field name="code">VF_3A</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_VF31" model="account.report.line">
                                <field name="name">VF31 - Purchases intended for occasional taxable transactions</field>
                                <field name="code">VF31</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF31_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf31</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF31_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF1.tax + VF2.tax + VF3.tax + VF4.tax + VF5.tax + VF6.tax + VF7.tax
                                                            + VF8.tax + VF9.tax + VF10.tax + VF11.tax + VF12.tax + VF13.tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF34" model="account.report.line">
                                <field name="name">VF34 - Data for calculating the deduction percentage</field>
                                <field name="code">VF34</field>
                                <field name="children_ids">
                                    <record id="tax_annual_report_line_VF34_I" model="account.report.line">
                                        <field name="name">Exempt transactions relating to gold from investments made by the subjects referred to in the art. 19, co. 3, letter. d)</field>
                                        <field name="code">VF34_I</field>
                                        <field name="tax_tags_formula">vf34_i</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF34_II" model="account.report.line">
                                        <field name="name">Exempt operations referred to in nos. from 1 to 9 of the art. 10 not included in their own business of the company or ancillary to taxable operations</field>
                                        <field name="code">VF34_II</field>
                                        <field name="tax_tags_formula">vf34_ii</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF34_III" model="account.report.line">
                                        <field name="name">Exempt operations referred to in art. 10, n. 27-quinquies</field>
                                        <field name="code">VF34_III</field>
                                        <field name="tax_tags_formula">vf34_iii</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF34_IV" model="account.report.line">
                                        <field name="name">Depreciable assets and transfers interiors exempt</field>
                                        <field name="code">VF34_IV</field>
                                        <field name="tax_tags_formula">vf34_iv</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF34_V" model="account.report.line">
                                        <field name="name">Operations not subject</field>
                                        <field name="code">VF34_V</field>
                                        <field name="tax_tags_formula">vf34_v</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF34_VI" model="account.report.line">
                                        <field name="name">Operations not subject to art. 74, co. 1</field>
                                        <field name="code">VF34_VI</field>
                                        <field name="tax_tags_formula">vf34_vi</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF34_VII" model="account.report.line">
                                        <field name="name">Exempt operations art. 19, co. 3, letter. a-bis) and d-bis)</field>
                                        <field name="code">VF34_VII</field>
                                        <field name="tax_tags_formula">vf34_vii</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF34_VIII" model="account.report.line">
                                        <field name="name">Article operations 7 to 7-septies without right to deduction</field>
                                        <field name="code">VF34_VIII</field>
                                        <field name="tax_tags_formula">vf34_viii</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF34_IX" model="account.report.line">
                                        <field name="name">Operations exempted by law no. 178/2020</field>
                                        <field name="code">VF34_IX</field>
                                        <field name="tax_tags_formula">vf34_ix</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF35" model="account.report.line">
                                <field name="name">VF35 - VAT not paid on purchases and imports indicated</field>
                                <field name="code">VF35</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF35_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf35</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF36" model="account.report.line">
                                <field name="name">VF36 - VAT deductible for gold purchases made by parties other than producers and transformers pursuant to art. 19, paragraph 5 bis</field>
                                <field name="code">VF36</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF36_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf36</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF37" model="account.report.line">
                                <field name="name">VF37 - VAT allowed as deduction</field>
                                <field name="code">VF37</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF37_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">((VF27.tax + VF35.tax - VF36.tax) * (
                                            VF34_I.balance + VF34_II.balance + VF34_III.balance + VF34_IV.balance + VF34_V.balance +
                                            VF34_VI.balance + VF34_VII.balance + VF34_VIII.balance + VF34_IX.balance
                                        )) - VF35.tax + VF36.tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_passive_op_3b" model="account.report.line">
                        <field name="name">Agricultural businesses (art.34)</field>
                        <field name="code">VF_3B</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_VF38" model="account.report.line">
                                <field name="name">VF38 - Reserved for mixed agricultural enterprises</field>
                                <field name="code">VF38</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF38_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf38 Base</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF38_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf38 Tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF39" model="account.report.line">
                                <field name="name">VF39 - compensation percentage 2%</field>
                                <field name="code">VF39</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF39_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf39</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF39_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF39.balance * 0.02</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF40" model="account.report.line">
                                <field name="name">VF40 - compensation percentage 4%</field>
                                <field name="code">VF40</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF40_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf40</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF40_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF40.balance * 0.04</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF41" model="account.report.line">
                                <field name="name">VF41 - compensation percentage 6.4%</field>
                                <field name="code">VF41</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF41_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf41</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF41_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF41.balance * 0.064</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF42" model="account.report.line">
                                <field name="name">VF42 - compensation percentage 7%</field>
                                <field name="code">VF42</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF42_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf42</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF42_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF42.balance * 0.07</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF43" model="account.report.line">
                                <field name="name">VF43 - compensation percentage 7,3%</field>
                                <field name="code">VF43</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF43_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf43</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF43_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF43.balance * 0.073</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF44" model="account.report.line">
                                <field name="name">VF44 - compensation percentage 7,5%</field>
                                <field name="code">VF44</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF44_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf44</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF44_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF44.balance * 0.075</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF45" model="account.report.line">
                                <field name="name">VF45 - compensation percentage 8,3%</field>
                                <field name="code">VF45</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF45_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf45</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF45_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF45.balance * 0.083</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF46" model="account.report.line">
                                <field name="name">VF46 - compensation percentage 8,5%</field>
                                <field name="code">VF46</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF46_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf46</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF46_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF46.balance * 0.085</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF47" model="account.report.line">
                                <field name="name">VF47 - compensation percentage 9.5%</field>
                                <field name="code">VF47</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF47_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf47</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF47_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF47.balance * 0.095</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF48" model="account.report.line">
                                <field name="name">VF48 - compensation percentage 10%</field>
                                <field name="code">VF48</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF48_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf48</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF48_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF48.balance * 0.10</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF49" model="account.report.line">
                                <field name="name">VF49 - compensation percentage 12,3%</field>
                                <field name="code">VF49</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF49_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf49</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF49_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF49.balance * 0.123</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF51" model="account.report.line">
                                <field name="name">VF51 - Tax variations and roundings (indicate with the +/- sign)</field>
                                <field name="code">VF51</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF51_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf51</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF52" model="account.report.line">
                                <field name="name">VF52 - TOTALS Algebraic sum of lines from VF39 to VF51</field>
                                <field name="code">VF52</field>
                                <field name="aggregation_formula">VF39.balance + VF40.balance + VF41.balance + VF42.balance + VF43.balance +
                                                        VF44.balance + VF45.balance + VF46.balance + VF47.balance + VF48.balance + 
                                                        VF49.balance + VF51.tax</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF52_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF39.tax + VF40.tax + VF41.tax + VF42.tax + VF43.tax +
                                                              VF44.tax + VF45.tax + VF46.tax + VF47.tax + VF48.tax +
                                                              VF49.tax + VF51.tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF53" model="account.report.line">
                                <field name="name">VF53 - Deductible VAT charged to the operations referred to in line VF38</field>
                                <field name="code">VF53</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF53_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf53</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF54" model="account.report.line">
                                <field name="name">VF54 - Deductible amount for transfers, including intra-community, of agricultural products referred to in art. 34</field>
                                <field name="code">VF54</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF54_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf54</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF55" model="account.report.line">
                                <field name="name">VF55 - TOTAL VAT allowed as deduction</field>
                                <field name="code">VF55</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF55_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF52.tax + VF53.tax + VF54.tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_passive_op_3c" model="account.report.line">
                        <field name="name">Special cases</field>
                        <field name="code">VF_3C</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_VF62" model="account.report.line">
                                <field name="name">VF62 - Reserved for agricultural businesses</field>
                                <field name="code">VF62</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF62_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">vf62</field>
                                    </record>
                                    <record id="tax_annual_report_line_VF62_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF62.balance * 0.5</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_annual_report_line_passive_op_4" model="account.report.line">
                        <field name="name">VAT allowed as deduction</field>
                        <field name="code">VF_4</field>
                        <field name="children_ids">
                            <record id="tax_annual_report_line_VF70" model="account.report.line">
                                <field name="name">VF70 - TOTAL adjustments (indicate with the +/- sign)</field>
                                <field name="code">VF70</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF70_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VF70</field>
                                    </record>
                                </field>
                            </record>
                            <record id="tax_annual_report_line_VF71" model="account.report.line">
                                <field name="name">VF71 - VAT allowed as deduction</field>
                                <field name="code">VF71</field>
                                <field name="expression_ids">
                                    <record id="tax_annual_report_line_VF71_tax" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">VF26.tax + VF27.tax + VF37.tax + VF55.tax + VF70.tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
