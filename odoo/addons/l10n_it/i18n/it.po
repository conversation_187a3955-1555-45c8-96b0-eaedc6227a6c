# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_it
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-15 23:04+0000\n"
"PO-Revision-Date: 2024-01-15 23:04+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_it
#: model:ir.model,name:l10n_it.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modello piano dei conti"

#. module: l10n_it
#: model:ir.model,name:l10n_it.model_account_report_expression
msgid "Accounting Report Expression"
msgstr "Espressione rendiconto contabile"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_D_ATT
msgid "Accruals and deferrals - Assets"
msgstr "Ratei e risconti - Attivi"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_E_PASS
msgid "Accruals and deferrals - Liabilities"
msgstr "Ratei e risconti - Passivi"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_3b
msgid "Agricultural businesses (art.34)"
msgstr "Aziende agricole (art. 34)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover_section_2
#: model:account.report.line,name:l10n_it.tax_report_line_turnover_section_2
msgid ""
"Agricultural taxable transactions (Article 34 paragraph 1) and commercial "
"and professional taxable transactions"
msgstr ""
"Operazioni imponibili agricole (art.34 comma 1) e operazioni imponibili "
"commerciali e professional"

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat
msgid "Annual Tax Report"
msgstr "Rapporto fiscale annuale"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_comp_fram
msgid "Annual tax for completed schedules"
msgstr "Imposta annuale per i piani compilati"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_VIII
msgid "Article operations 7 to 7-septies without right to deduction"
msgstr "Articolo operazioni da 7 a 7-septies senza diritto a detrazione"

#. module: l10n_it
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_va
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_ve
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_vf
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_vh
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_vj
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_balance_vl
#: model:account.report.column,name:l10n_it.tax_monthly_report_vat_balance
#: model:account.report.column,name:l10n_it.tax_report_vat_balance
msgid "Balance"
msgstr "Saldo"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_saldi_riporti_e_interessi
#: model:account.report.line,name:l10n_it.tax_report_line_saldi_riporti_e_interessi
msgid "Balances, carryovers and interest"
msgstr "Saldi, riporti e interessi"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_dag
msgid "Business Information"
msgstr "Informazioni commerciali"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_IMPEGNI
msgid "Commitments"
msgstr "Impegni"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover_section_1
#: model:account.report.line,name:l10n_it.tax_report_line_turnover_section_1
msgid ""
"Contributions of agricultural products and transfers from exempt farmers (in"
" case of exceeding 1/3"
msgstr ""
"Conferimenti di prodotti agricoli e cessioni da agricoltori esonerati (in "
"caso di superamento di 1/3"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_C_ATT
msgid "Current assets"
msgstr "Attivo circolante"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_D_PASS
msgid "Debts"
msgstr "Debiti"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29_I
msgid "Depreciable assets"
msgstr "Attività ammortizzabili"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_IV
msgid "Depreciable assets and transfers interiors exempt"
msgstr "Beni ammortizzabili e trasferimenti interni esenti"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_comp_fram_1
msgid "Determination of VAT due or credit for the tax period"
msgstr "Determinazione dell'IVA dovuta o a credito per il periodo d'imposta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_comp_fram_3
msgid ""
"Determination of debit or credit VAT relating to all activities carried out"
msgstr ""
"Determinazione dell'IVA a debito o a credito relativa a tutte le attività "
"svolte"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va5_section_1
msgid "Equipment purchases"
msgstr "Acquisti apparecchiature"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_3a
msgid "Exempt operations"
msgstr "Operazioni esenti"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_VII
msgid "Exempt operations art. 19, co. 3, letter. a-bis) and d-bis)"
msgstr "Operazioni esenti art. 19, co. 3, lett. a-bis) e d-bis)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_III
msgid "Exempt operations referred to in art. 10, n. 27-quinquies"
msgstr "Operazioni esenti di cui all'art. 10, n. 27-quinquies"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_II
msgid ""
"Exempt operations referred to in nos. from 1 to 9 of the art. 10 not "
"included in their own business of the company or ancillary to taxable "
"operations"
msgstr ""
"Le operazioni esenti di cui ai nn. da 1 a 9 dell'art. 10 non sono operazioni"
" di esenzione. 10 non rientranti nell'attività propria dell'impresa o "
"accessorie alle operazioni imponibili"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_I
msgid ""
"Exempt transactions relating to gold from investments made by the subjects "
"referred to in the art. 19, co. 3, letter. d)"
msgstr ""
"Esentare le operazioni relative all'oro dagli investimenti effettuati dai "
"soggetti di cui all'art. 19, co. 3, lett. d)"

#. module: l10n_it
#: model:ir.model.fields,field_description:l10n_it.field_account_tax__l10n_it_exempt_reason
msgid "Exoneration"
msgstr "Esenzione"

#. module: l10n_it
#: model:ir.model.fields,help:l10n_it.field_account_tax__l10n_it_exempt_reason
msgid "Exoneration type"
msgstr "Tipo esenzione"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_E_PL
msgid "Extraordinary income and expenses"
msgstr "Proventi e oneri straordinari"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_C_PL
msgid "Financial income and expenses"
msgstr "Proventi e oneri finanziari"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_B_ATT
msgid "Fixed assets"
msgstr "Immobilizzazioni"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_dag_section1
msgid "General analytical data"
msgstr "Dati analitici generali"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29_III
msgid "Goods intended for resale or for the production of goods and services"
msgstr "Beni destinati alla rivendita o alla produzione di beni e servizi"

#. module: l10n_it
#. odoo-python
#: code:addons/l10n_it/models/account_tax.py:0
#, python-format
msgid ""
"If the tax amount is 0%, you must enter the exoneration code and the related"
" law reference."
msgstr ""
"Se l'importo della tassa è 0%, devi inserire il codice di esenzione  ed il "
"relativo riferimento legislativo"

#. module: l10n_it
#: model:ir.model.fields,field_description:l10n_it.field_account_tax__l10n_it_law_reference
msgid "Law Reference"
msgstr "Riferimento legislativo"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va5_section_2
msgid "Management services"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_monthly_report_vat
msgid "Monthly VAT Report"
msgstr "Periodic Rapporto IVA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29_II
msgid "Non-depreciable capital goods"
msgstr "Beni strumentali non ammortizzabili"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_IX
msgid "Operations exempted by law no. 178/2020"
msgstr "Operazioni esenti dalla legge n. 178/2020"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_V
msgid "Operations not subject"
msgstr "Operazioni non soggette"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34_VI
msgid "Operations not subject to art. 74, co. 1"
msgstr "Operazioni non soggette all'art. 74, co. 1"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover_section_4
#: model:account.report.line,name:l10n_it.tax_report_line_turnover_section_4
msgid "Other operations"
msgstr "Altre operazioni"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29_IV
msgid "Other purchases and imports"
msgstr "Altri acquisti e importazioni"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op
msgid "Passive operations and VAT deduction allowed"
msgstr "Operazioni passive e detrazione dell'IVA consentita"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30_I
msgid "Periodic VAT due"
msgstr "IVA periodica dovuta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30_II
msgid "Periodic VAT paid"
msgstr "IVA periodica pagata"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30_III
msgid "Periodic VAT paid following notification of irregularities"
msgstr "Versamento periodico dell'IVA a seguito di notifica di irregolarità"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30_IV
msgid "Periodic VAT paid following payment orders"
msgstr "IVA periodica pagata a seguito di ordini di pagamento"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_comp_fram_2
msgid "Previous year credit"
msgstr "Credito dell'anno precedente"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_B_PL
msgid "Production costs"
msgstr "Costi della produzione"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_B_PASS
msgid "Provisions for risks and charges"
msgstr "Fondi per rischi e oneri"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_1
msgid "Purchases (Domestic, intra-Community and imports)"
msgstr "Acquisti (nazionali, intracomunitari e importazioni)"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_A_ATT
msgid "Receivables from shareholders"
msgstr "Crediti verso soci"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_reverse_charge_iva
#: model:account.report.line,name:l10n_it.tax_report_line_reverse_charge_iva
msgid "Reverse Charge"
msgstr "Inversione Contabile"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_RISCHI
msgid "Risks"
msgstr "Rischi"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_C_PASS
msgid "Severance pay"
msgstr "Trattamento di fine rapporto di lavoro subordinato"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_A_PASS
msgid "Shareholders' Equity"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_3c
msgid "Special cases"
msgstr "Casi speciali"

#. module: l10n_it
#. odoo-python
#: code:addons/l10n_it/models/account_tax.py:0
#, python-format
msgid "Split Payment is not compatible with exoneration of kind 'N6'"
msgstr "Lo Split Payment non è compatibile con l'esenzione di tipo 'N6'"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_dag_section2
msgid "Summary data for all activities"
msgstr "Dati riepilogativi relativi a tutte le attività"

#. module: l10n_it
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_tax_va
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_tax_ve
#: model:account.report.column,name:l10n_it.tax_annual_report_vat_tax_vf
#: model:ir.model,name:l10n_it.model_account_tax
msgid "Tax"
msgstr "Imposta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_operazione_imponibile
#: model:account.report.line,name:l10n_it.tax_report_line_operazione_imponibile
msgid "Taxable transaction"
msgstr "Operazione Imponibile"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_BENI
msgid "Third party assets"
msgstr "Beni di terzi"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_2
msgid ""
"Total purchases and imports, total tax, purchases intra-community, imports "
"and purchases"
msgstr ""
"Totale acquisti e importazioni, totale imposte, acquisti intracomunitari, "
"importazioni e acquisti"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover_section_3
#: model:account.report.line,name:l10n_it.tax_report_line_turnover_section_3
msgid "Total taxable income and tax"
msgstr "Totale imponibile e imposta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_turnover
#: model:account.report.line,name:l10n_it.tax_report_line_turnover
msgid "Turnover"
msgstr "Fatturato"

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_va
msgid "VA VAT Report"
msgstr "Rapporto IVA VA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va1
msgid ""
"VA1 - To be filled in by the entity of originator in cases of extraordinary "
"transactions"
msgstr ""
"VA1 - Da compilare a cura del soggetto dante causa nelle ipotesi di "
"operazioni straordinarie"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va16
msgid ""
"VA11 - Data on amounts suspended as a result of the health emergency by "
"COVID-19"
msgstr ""
"VA11 - Dati relativi agli importi sospesi a seguito dell'emergenza sanitaria"
" da COVID-19"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va12
msgid ""
"VA12 - Reserved for indication of surplus credit of former parent companies "
"to be guaranteed"
msgstr ""
"VA12 - Riservato all’indicazione di eccedenze di credito di società ex "
"controllanti da garantire"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va13
msgid "VA13 - Transactions carried out with respect to condominiums"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va5
msgid ""
"VA5 - Terminals for mobile telecommunication radio service with more than "
"50% deduction"
msgstr ""
"VA5 - Terminali per il servizio radiomobile di telecomunicazione con "
"detrazione superiore al 50%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_iva
#: model:account.report.line,name:l10n_it.tax_report_line_iva
msgid "VAT"
msgstr "IVA"

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_report_vat
msgid "VAT Report"
msgstr "Rapporto IVA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_conto_corrente_iva
#: model:account.report.line,name:l10n_it.tax_report_line_conto_corrente_iva
msgid "VAT account"
msgstr "Conto IVA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_passive_op_4
msgid "VAT allowed as deduction"
msgstr "IVA ammessa in detrazione"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_va1_4
msgid "VAT/2023 declaration credit transferred"
msgstr "Credito dichiarazione IVA/2023 ceduto"

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_ve
msgid "VE VAT Report"
msgstr "Rapporto IVA VE"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve1
#: model:account.report.line,name:l10n_it.tax_report_line_ve1
msgid ""
"VE1 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 2%"
msgstr ""
"VE1 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 2%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve10
#: model:account.report.line,name:l10n_it.tax_report_line_ve10
msgid ""
"VE10 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 10%"
msgstr ""
"VE10 - Passaggi a cooperative art.34 comma 2 con percentuale di "
"compensazione 10%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve11
#: model:account.report.line,name:l10n_it.tax_report_line_ve11
msgid ""
"VE11 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 12,3%"
msgstr ""
"VE11 - Passaggi a cooperative art.34 comma 2 con percentuale di "
"compensazione 12,3%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve2
#: model:account.report.line,name:l10n_it.tax_report_line_ve2
msgid ""
"VE2 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 4%"
msgstr ""
"VE2 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 4%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve20
#: model:account.report.line,name:l10n_it.tax_report_line_ve20
msgid "VE20 - Taxable transactions rate 4%"
msgstr "VE20 - Operazioni imponibili aliquota 4%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve21
#: model:account.report.line,name:l10n_it.tax_report_line_ve21
msgid "VE21 - Taxable transactions rate 5%"
msgstr "VE21 - Operazioni imponibili aliquota 5%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve22
#: model:account.report.line,name:l10n_it.tax_report_line_ve22
msgid "VE22 - Taxable transactions rate 10%"
msgstr "VE22 - Operazioni imponibili aliquota 10%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve23
#: model:account.report.line,name:l10n_it.tax_report_line_ve23
msgid "VE23 - Taxable transactions rate 22%"
msgstr "VE23 - Operazioni imponibili aliquota 22%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve24
#: model:account.report.line,name:l10n_it.tax_report_line_ve24
msgid "VE24 - Total lines VE1 to VE11 and lines VE20 to VE23"
msgstr "VE24 - Totale righe da VE1 a VE11 e linee da VE20 a VE23"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve25
#: model:account.report.line,name:l10n_it.tax_report_line_ve25
msgid "VE25 - Variations and rounding (use +/− sign)"
msgstr "VE25 - Variazioni e arrotondamenti (usare segno +/−)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve26
#: model:account.report.line,name:l10n_it.tax_report_line_ve26
msgid "VE26 - Total VE24 and VE25"
msgstr "VE26 - Totale VE24 e VE25"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve3
#: model:account.report.line,name:l10n_it.tax_report_line_ve3
msgid ""
"VE3 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 6,4%"
msgstr ""
"VE3 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 6,4%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30
#: model:account.report.line,name:l10n_it.tax_report_line_ve30
msgid "VE30 - Transactions that contribute to the formation of the ceiling"
msgstr "VE30 - Operazioni che concorrono alla formazione del plafond"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_I
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_I
msgid "VE30_I - Total"
msgstr "VE30_I - Totale"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_ii
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_ii
msgid "VE30_II - Exports"
msgstr "VE30_II - Esportazioni"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_iii
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_iii
msgid "VE30_III - Intra-Community supplies"
msgstr "VE30_III - Cessioni intracomunitarie"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_iv
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_iv
msgid "VE30_IV - Transfers to San Marino"
msgstr "VE30_IV - Cessioni verso San Marino"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve30_v
#: model:account.report.line,name:l10n_it.tax_report_line_ve30_v
msgid "VE30_V - Assimilated operations"
msgstr "VE30_V - Operazioni assimilate"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve31
#: model:account.report.line,name:l10n_it.tax_report_line_ve31
msgid "VE31 - Non-taxable transactions as a result of declarations of intent"
msgstr ""
"VE31 - Operazioni non imponibili a seguito di dichiarazioni di intento"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve32
#: model:account.report.line,name:l10n_it.tax_report_line_ve32
msgid "VE32 - Other non-taxable transactions"
msgstr "VE32 - Altre operazioni non imponibili"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve33
#: model:account.report.line,name:l10n_it.tax_report_line_ve33
msgid "VE33 - Exempt transactions (art.10"
msgstr "VE33 - Operazioni esenti (art.10"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve34
#: model:account.report.line,name:l10n_it.tax_report_line_ve34
msgid ""
"VE34 - Transactions not subject to the tax under Articles 7 to 7-septies"
msgstr ""
"VE34 - Operazioni non soggette all’imposta ai sensi degli articoli da 7 a "
"7-septies"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35
#: model:account.report.line,name:l10n_it.tax_report_line_ve35
msgid "VE35 - Transactions with application of internal reverse charge"
msgstr "VE35 - Operazioni con applicazione del reverse charge interno"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_I
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_I
msgid "VE35_I - Total"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_ii
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_ii
msgid "VE35_II - Disposal of scrap and other recovered materials"
msgstr "VE35_II - Cessioni di rottami e altri materiali di recupero"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_iii
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_iii
msgid "VE35_III - Disposals of pure gold and silver"
msgstr "VE35_III - Cessioni di oro e argento puro"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_iv
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_iv
msgid "VE35_IV - Subcontracting in the construction industry"
msgstr "VE35_IV - Subappalto nel settore edile"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_ix
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_ix
msgid "VE35_IX - Energy sector operations"
msgstr "VE35_IX - Operazioni settore energetico"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_v
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_v
msgid "VE35_V - Disposal of capital buildings"
msgstr "VE35_V - Cessioni di fabbricati strumentali"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_vi
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_vi
msgid "VE35_VI - Disposal of cell phones"
msgstr "VE35_VI - Cessioni di telefoni cellulari"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_vii
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_vii
msgid "VE35_VII - Disposal of electronic products"
msgstr "VE35_VII - Cessioni di prodotti elettronici"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve35_viii
#: model:account.report.line,name:l10n_it.tax_report_line_ve35_viii
msgid "VE35_VIII - Benefits construction and related industries"
msgstr "VE35_VIII - Prestazioni comparto edile e settori connessi"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve36
#: model:account.report.line,name:l10n_it.tax_report_line_ve36
msgid "VE36 - Non-taxable transactions made to earthquake victims"
msgstr ""
"VE36 - Operazioni non soggette all\"imposta effettuate nei confronti dei "
"terremotati"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve37
#: model:account.report.line,name:l10n_it.tax_report_line_ve37
msgid ""
"VE37 - Transactions made during the year but with tax due in subsequent "
"years"
msgstr ""
"VE37 - Operazioni effettuate nell\"anno ma con imposta esigibile negli anni "
"successivi"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve37_I
#: model:account.report.line,name:l10n_it.tax_report_line_ve37_I
msgid "VE37_I - Total"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve37_ii
#: model:account.report.line,name:l10n_it.tax_report_line_ve37_ii
msgid "VE37_II - ex art. 32-bis, DL no. 83/2012"
msgstr "VE37_II - ex art. 32-bis, DL n. 83/2012"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve38
#: model:account.report.line,name:l10n_it.tax_report_line_ve38
msgid "VE38 - Transactions with parties referred to in Article 17-ter."
msgstr "VE38 - Operazioni nei confronti di soggetti di cui all\"art.17-ter"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve39
#: model:account.report.line,name:l10n_it.tax_report_line_ve39
msgid ""
"VE39 - (minus) Transactions made in previous years but with tax due in 2022"
msgstr ""
"VE39 - (meno) Operazioni effettuate in anni precedenti ma con imposta "
"esigibile nel 2022"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve4
#: model:account.report.line,name:l10n_it.tax_report_line_ve4
msgid ""
"VE4 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 7,3%"
msgstr ""
"VE4 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 7,3%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve40
#: model:account.report.line,name:l10n_it.tax_report_line_ve40
msgid "VE40 - (minus) Disposals of depreciable assets and internal transfers."
msgstr "VE40 - (meno) Cessioni di beni ammortizzabili e passaggi interni"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve5
#: model:account.report.line,name:l10n_it.tax_report_line_ve5
msgid ""
"VE5 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 7,5%"
msgstr ""
"VE5 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 7,5%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve45
msgid "VE50 - TURNOVER"
msgstr "VE50 - VOLUME D’AFFARI"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve6
#: model:account.report.line,name:l10n_it.tax_report_line_ve6
msgid ""
"VE6 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 8,3%"
msgstr ""
"VE6 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 8,3%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve7
#: model:account.report.line,name:l10n_it.tax_report_line_ve7
msgid ""
"VE7 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 8,5%"
msgstr ""
"VE7 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 8,5%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve8
#: model:account.report.line,name:l10n_it.tax_report_line_ve8
msgid ""
"VE8 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 8,8%"
msgstr ""
"VE8 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 8,8%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_ve9
#: model:account.report.line,name:l10n_it.tax_report_line_ve9
msgid ""
"VE9 - Transfers to cooperatives art.34 paragraph 2 with compensation "
"percentage 9,5%"
msgstr ""
"VE9 - Passaggi a cooperative art.34 comma 2 con percentuale di compensazione"
" 9,5%"

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_vf
msgid "VF VAT Report"
msgstr "Rapporto IVA VF"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF1
msgid "VF1 - compensation percentage 2%"
msgstr "VF1 - percentuale di compensazione 2%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF10
msgid "VF10 - compensation percentage 9.5%"
msgstr "VF10 - percentuale di compensazione 9.5%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF11
msgid "VF11 - compensation percentage 10%"
msgstr "VF11 - percentuale di compensazione 10%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF12
msgid "VF12 - compensation percentage 12,3%"
msgstr "VF12 - percentuale di compensazione 12,3%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF13
msgid "VF13 - compensation percentage 22%"
msgstr "VF13 - percentuale di compensazione 22%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF17
msgid ""
"VF17 - Purchases and imports without payment of tax, with use of the ceiling"
msgstr ""
"VF17 - Acquisti e importazioni senza pagamento dell'imposta, con utilizzo "
"del plafond"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF18_II
msgid "VF18 - Exempt purchases and imports not subject to the tax"
msgstr "VF18 - Acquisti esenti e importazioni non soggette all'imposta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF18_I
msgid ""
"VF18 - Other non-tax purchases, not subject to tax and relating to certain "
"special regimes"
msgstr ""
"VF18 - Altri acquisti non fiscali, non soggetti a imposta e relativi a "
"determinati regimi speciali"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF19
msgid ""
"VF19 - Purchases from subjects who have made use of concessional schemes"
msgstr "VF19 - Acquisti da soggetti che hanno usufruito di regimi agevolati"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF2
msgid "VF2 - compensation percentage 4%"
msgstr "VF2 - percentuale di compensazione 4%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF20
msgid ""
"VF20 - Purchases and imports not subject to the tax made by earthquake "
"victims"
msgstr ""
"VF20 - Acquisti e importazioni non soggetti all'imposta effettuati dai "
"terremotati"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF21
msgid ""
"VF21 - Purchases and imports for which the deduction is excluded or reduced "
"(art. 19-bis1)"
msgstr ""
"VF21 - Acquisti e importazioni per i quali la detrazione è esclusa o ridotta"
" (art. 19-bis1)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF22
msgid "VF22 - Purchases and imports for which the deduction is not permitted"
msgstr ""
"VF22 - Acquisti e importazioni per i quali non è ammessa la detrazione"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF23
msgid ""
"VF23 - Purchases recorded in the year but with tax deduction deferred to "
"subsequent years"
msgstr ""
"VF23 - Acquisti registrati nell'esercizio ma con deduzione fiscale rinviata "
"agli esercizi successivi"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF24
msgid "VF24 - Purchases recorded in previous years but with tax"
msgstr "VF24 - Acquisti registrati negli anni precedenti ma con imposta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF25
msgid "VF25 - Total purchases and imports"
msgstr "VF25 - Acquisti e importazioni totali"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF26
msgid "VF26 - Tax variations and roundings (indicate with the +/- sign)"
msgstr ""
"VF26 - Variazioni fiscali e arrotondamenti (indicare con il segno +/-)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF27
msgid "VF27 - Total tax on taxable purchases and imports"
msgstr "VF27 - Imposta totale sugli acquisti e sulle importazioni imponibili"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF28_II
msgid "VF28 - Imports"
msgstr "VF28 - Importazioni"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF28_I
msgid "VF28 - Intra-community purchases"
msgstr "VF28 - Acquisti intracomunitari"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF28_III
msgid "VF28 - Purchases from San Marino"
msgstr "VF28 - Acquisti da San Marino"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF29
msgid "VF29 - Total distribution of purchases and imports"
msgstr "VF29 - Distribuzione totale degli acquisti e delle importazioni"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF3
msgid "VF3 - compensation percentage 5%"
msgstr "VF3 - percentuale di compensazione 5%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF31
msgid "VF31 - Purchases intended for occasional taxable transactions"
msgstr "VF31 - Acquisti destinati ad operazioni occasionali imponibili"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF34
msgid "VF34 - Data for calculating the deduction percentage"
msgstr "VF34 - Dati per il calcolo della percentuale di detrazione"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF35
msgid "VF35 - VAT not paid on purchases and imports indicated"
msgstr "VF35 - IVA non versata su acquisti e importazioni indicate"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF36
msgid ""
"VF36 - VAT deductible for gold purchases made by parties other than "
"producers and transformers pursuant to art. 19, paragraph 5 bis"
msgstr ""
"VF36 - IVA detraibile per gli acquisti di oro effettuati da soggetti diversi"
" dai produttori e trasformatori ai sensi dell'art. 19, comma 5 bis. 19, "
"comma 5 bis"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF37
msgid "VF37 - VAT allowed as deduction"
msgstr "VF37 - IVA ammessa in detrazione"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF38
msgid "VF38 - Reserved for mixed agricultural enterprises"
msgstr "VF38 - Riservato alle imprese agricole miste"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF39
msgid "VF39 - compensation percentage 2%"
msgstr "VF39 - percentuale di compensazione 2%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF4
msgid "VF4 - compensation percentage 6.4%"
msgstr "VF4 - percentuale di compensazione 6.4%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF40
msgid "VF40 - compensation percentage 4%"
msgstr "VF40 - percentuale di compensazione 4%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF41
msgid "VF41 - compensation percentage 6.4%"
msgstr "VF41 - percentuale di compensazione 6.4%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF42
msgid "VF42 - compensation percentage 7,3%"
msgstr "VF42 - percentuale di compensazione 7,3%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF43
msgid "VF43 - compensation percentage 7,5%"
msgstr "VF43 - percentuale di compensazione 7,5%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF44
msgid "VF44 - compensation percentage 8,3%"
msgstr "VF44 - percentuale di compensazione 8,3%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF45
msgid "VF45 - compensation percentage 8,5%"
msgstr "VF45 - percentuale di compensazione 8,5%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF46
msgid "VF46 - compensation percentage 8,8%"
msgstr "VF46 - percentuale di compensazione 8,8%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF47
msgid "VF47 - compensation percentage 9.5%"
msgstr "VF47 - percentuale di compensazione 9.5%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF48
msgid "VF48 - compensation percentage 10%"
msgstr "VF48 - percentuale di compensazione 10%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF49
msgid "VF49 - compensation percentage 12,3%"
msgstr "VF49 - percentuale di compensazione 12,3%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF5
msgid "VF5 - compensation percentage 7,3%"
msgstr "VF5 - percentuale di compensazione 7,3%"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF51
msgid "VF51 - Tax variations and roundings (indicate with the +/- sign)"
msgstr ""
"VF51 - Variazioni fiscali e arrotondamenti (indicare con il segno +/-)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF52
msgid "VF52 - TOTALS Algebraic sum of lines from VF39 to VF51"
msgstr "VF52 - TOTALI Somma algebrica delle righe da VF39 a VF51"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF53
msgid ""
"VF53 - Deductible VAT charged to the operations referred to in line VF38"
msgstr "VF53 - IVA detraibile imputata alle operazioni di cui al rigo VF38"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF54
msgid ""
"VF54 - Deductible amount for transfers, including intra-community, of "
"agricultural products referred to in art. 34"
msgstr ""
"VF54 - Importo deducibile per le cessioni, anche intracomunitarie, di "
"prodotti agricoli di cui all'art. 34."

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF55
msgid "VF55 - TOTAL VAT allowed as deduction"
msgstr "VF55 - TOTALE IVA ammessa in detrazione"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF6
msgid "VF6 - compensation percentage 7,5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF62
msgid "VF62 - Reserved for agricultural businesses"
msgstr "VF62 - Riservato alle aziende agricole"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF7
msgid "VF7 - compensation percentage 8,3%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF70
msgid "VF70 - TOTAL adjustments (indicate with the +/- sign)"
msgstr "VF70 - Regolazioni totali (indicare con il segno +/-)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF71
msgid "VF71 - VAT allowed as deduction"
msgstr "VF71 - IVA ammessa in deduzione"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF8
msgid "VF8 - compensation percentage 8,5%"
msgstr ""

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_VF9
msgid "VF9 - compensation percentage 8,8%"
msgstr ""

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_vh
msgid "VH VAT Report"
msgstr "Rapporto IVA VH"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh1
msgid "VH1 - January"
msgstr "VH1 - Gennaio"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh10
msgid "VH10 - August"
msgstr "VH10 - Agosto"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh11
msgid "VH11 - September"
msgstr "VH11 - Settembre"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh12
msgid "VH12 - QUARTER III"
msgstr "VH12 - QUARTO III"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh13
msgid "VH13 - October"
msgstr "VH13 - Ottobre"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh14
msgid "VH14 - November"
msgstr "VH14 - Novembre"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh15
msgid "VH15 - December"
msgstr "VH15 - Dicembre"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh16
msgid "VH16 - QUARTER IV"
msgstr "VH16 - QUARTO IV"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh17
msgid "VH17 - Deposit due"
msgstr "VH17 - Deposito dovuto"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh2
msgid "VH2 - February"
msgstr "VH2 - Febbraio"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh3
msgid "VH3 - March"
msgstr "VH3 - Marzo"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh4
msgid "VH4 - QUARTER I"
msgstr "VH4 - QUARTO I"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh5
msgid "VH5 - April"
msgstr "VH5 - Aprile"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh6
msgid "VH6 - May"
msgstr "VH6 - Maggio"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh7
msgid "VH7 - June"
msgstr "VH7 - Giugno"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh8
msgid "VH8 - QUARTER II"
msgstr "VH8 - QUARTO II"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vh9
msgid "VH9 - July"
msgstr "VH9 - Luglio"

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_vj
msgid "VJ VAT Report"
msgstr "Rapporto IVA VJ"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj1
#: model:account.report.line,name:l10n_it.tax_report_line_vj1
msgid "VJ1 - Purchases of goods from Vatican City and San Marino"
msgstr "VJ1 - Importazioni di beni da Città del Vaticano e da San Marino"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj10
#: model:account.report.line,name:l10n_it.tax_report_line_vj10
msgid "VJ10 - Imports of scrap and other recovered materials"
msgstr "VJ10 - Importazioni di rottami e altri materiali di recupero"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj11
#: model:account.report.line,name:l10n_it.tax_report_line_vj11
msgid "VJ11 - Imports of industrial gold and pure silver"
msgstr "VJ11 - Importazioni di oro industriale e argento puro"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj12
#: model:account.report.line,name:l10n_it.tax_report_line_vj12
msgid "VJ12 - Subcontracting of services in the construction field"
msgstr "VJ12 - Subappalto di servizi in campo edile"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj13
#: model:account.report.line,name:l10n_it.tax_report_line_vj13
msgid ""
"VJ13 - Purchases of buildings or portions of buildings used for capital "
"purposes"
msgstr "VJ13 - Acquisti di fabbricati o porzioni di fabbricati strumentali"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj14
#: model:account.report.line,name:l10n_it.tax_report_line_vj14
msgid "VJ14 - Purchases of cell phones"
msgstr "VJ14 - Acquisti di telefoni cellulari"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj15
#: model:account.report.line,name:l10n_it.tax_report_line_vj15
msgid "VJ15 - Purchases of electronic products"
msgstr "VJ15 - Acquisti di prodotti elettronici"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj16
#: model:account.report.line,name:l10n_it.tax_report_line_vj16
msgid "VJ16 - Provision of services in the construction field"
msgstr "VJ16 - Prestazioni di servizi in campo edile"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj17
#: model:account.report.line,name:l10n_it.tax_report_line_vj17
msgid "VJ17 - Purchases of energy sector goods and services"
msgstr "VJ17 - Acquiti di beni e servizi del settore energetico"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj18
#: model:account.report.line,name:l10n_it.tax_report_line_vj18
msgid "VJ18 - Purchases made by VAT-registered public administrations"
msgstr ""
"VJ18 - Acquisti effettuati dalle pubbliche amministrazioni titolari di "
"partita IVA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj19
#: model:account.report.line,name:l10n_it.tax_report_line_vj19
msgid "VJ19 - Total frame VJ"
msgstr "VJ19 - Totale quadro VJ"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj2
#: model:account.report.line,name:l10n_it.tax_report_line_vj2
msgid "VJ2 - Extraction of goods from VAT warehouses"
msgstr "VJ2 - Estrazione di beni da depositi Iva"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj3
#: model:account.report.line,name:l10n_it.tax_report_line_vj3
msgid ""
"VJ3 - Purchases of goods already in Italy or services, from non-residents"
msgstr ""
"VJ3 - Acquisti di beni giá presenti in Italia o servizi, da soggetti non "
"residenti"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj4
#: model:account.report.line,name:l10n_it.tax_report_line_vj4
msgid ""
"VJ4 - Fees paid to resellers of travel tickets and resellers of parking "
"documents"
msgstr ""
"VJ4 - Compensi corrisposti ai rivenditori di biglietti di viaggio ed ai "
"rivenditori di documenti di sosta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj5
#: model:account.report.line,name:l10n_it.tax_report_line_vj5
msgid "VJ5 - Commissions paid by travel agents to their intermediaries"
msgstr ""
"VJ5 - Provvigioni corrisposte dalle agenzie di viaggio ai propri "
"intermediari"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj6
#: model:account.report.line,name:l10n_it.tax_report_line_vj6
msgid "VJ6 - Purchases of scrap and other recovered materials"
msgstr "VJ6 - Acquisti di rottami e altri materiali di recupero"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj7
#: model:account.report.line,name:l10n_it.tax_report_line_vj7
msgid "VJ7 - Purchases of industrial gold and pure silver made in Italy"
msgstr "VJ7 - Acquisti di oro industriale e argento puro effettuati in Italia"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj8
#: model:account.report.line,name:l10n_it.tax_report_line_vj8
msgid "VJ8 - Investment gold purchases made in Italy"
msgstr "VJ8 - Acquisti di oro da investimento effettuati in Italia"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vj9
#: model:account.report.line,name:l10n_it.tax_report_line_vj9
msgid "VJ9 - Intra-EU Purchases of Goods"
msgstr "VJ9 - Acquisti intracomunitari di beni"

#. module: l10n_it
#: model:account.report,name:l10n_it.tax_annual_report_vat_vl
msgid "VL VAT Report"
msgstr "Rapporto IVA VL"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl1
msgid "VL1 - VAT payable"
msgstr "VL1 - IVA da pagare"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl10
msgid "VL10 - Non-transferable credit surplus"
msgstr "VL10 - Eccedenza di credito non trasferibile"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl11
msgid ""
"VL11 - Credits art. 8, paragraph 6-quater, Presidential Decree. n. 322/98"
msgstr "VL11 - Crediti art. 8, comma 6-quater, DPR. n. 322/98"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl12
msgid "VL12 - Periodic payments omitted"
msgstr "VL12 - Omissione di pagamenti periodici"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl2
msgid "VL2 - VAT deductible"
msgstr "VL2 - IVA deducibile"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl20
msgid "VL20 - Interim refunds required"
msgstr "VL20 - Rimborsi intermedi richiesti"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl21
msgid "VL21 - Amount of credits transferred"
msgstr "VL21 - Quantità di crediti trasferiti"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl22
msgid "VL22 - VAT credit resulting from the first 3 quarters"
msgstr "VL22 - Credito IVA risultante dai primi 3 trimestri"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl23
msgid "VL23 - Interest due for quarterly payments"
msgstr "VL23 - Interessi dovuti per pagamenti trimestrali"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl24
msgid "VL24 - Previous year transfers returned by the parent company"
msgstr ""
"VL24 - Trasferimenti dell'anno precedente restituiti dalla società madre"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl25
msgid "VL25 - Previous year credit surplus"
msgstr "VL25 - Eccedenza di credito dell'anno precedente"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl26
msgid ""
"VL26 - Credit requested for reimbursement in previous years computable as a "
"deduction following refusal by the office"
msgstr ""
"VL26 - Credito richiesto a rimborso in anni precedenti computabile in "
"detrazione a seguito di diniego da parte dell'ufficio"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl27
msgid ""
"VL27 - Tax credits used in periodic payments and for the advance payment"
msgstr ""
"VL27 - Crediti d'imposta utilizzati nelle liquidazioni periodiche e per "
"l'acconto"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl28
msgid ""
"VL28 - Credits received from savings management companies used in periodic "
"payments and for the advance payment"
msgstr ""
"VL28 - Crediti ricevuti da società di gestione del risparmio utilizzati per "
"i pagamenti periodici e per il pagamento anticipato"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl29
msgid "VL29 - EU car payments relating to sales carried out during the year"
msgstr ""
"VL29 - Pagamenti auto UE relativi a vendite effettuate nel corso dell'anno"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl3
msgid "VL3 - Tax Due"
msgstr "VL3 - Imposta dovuta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl30
msgid "VL30 - Periodic VAT amount"
msgstr "VL30 - Importo IVA periodico"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl31
msgid "VL31 - Amount of debts transferred"
msgstr "VL31 - Importo dei debiti trasferiti"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl32
msgid "VL32 - VAT DUE"
msgstr "VL32 - IVA DOVUTA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl33
msgid "VL33 - VAT CREDIT"
msgstr "VL33 - CREDITO IVA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl34
msgid "VL34 - Tax credits used in the annual return"
msgstr "VL34 - Crediti d'imposta utilizzati nella dichiarazione annuale"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl35
msgid ""
"VL35 - Credits received from asset management companies used in the annual "
"declaration"
msgstr ""
"VL35 - Crediti ricevuti da società di gestione del risparmio utilizzati "
"nella dichiarazione annuale"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl36
msgid "VL36 - Interest due on the annual return"
msgstr "VL36 - Interessi dovuti sulla dichiarazione annuale"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl37
msgid ""
"VL37 - Credit transferred by savings management companies pursuant to art. 8"
" of the legislative decree n. 351/2001"
msgstr ""
"VL37 - Crediti ceduti da società di gestione del risparmio ai sensi "
"dell'art. 8 del decreto legislativo n. 351/2001"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl38
msgid "VL38 - TOTAL VAT DUE"
msgstr "VL38 - TOTALE IVA DOVUTA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl39
msgid "VL39 - TOTAL VAT INPUT"
msgstr "VL39 - TOTALE IVA IN ENTRATA"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl4
msgid "VL4 - Tax Credit"
msgstr "VL4 - Credito d'imposta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl40
msgid "VL40 - Payments made following excess use of credit"
msgstr ""
"VL40 - Pagamenti effettuati a seguito di un utilizzo eccessivo del credito"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl41_II
msgid "VL41 - Difference between credit potential and actual credit"
msgstr "VL41 - Differenza tra credito potenziale e credito effettivo"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl41_I
msgid "VL41 - Difference between periodic VAT due and periodic VAT paid"
msgstr "VL41 - Differenza tra IVA periodica dovuta e IVA periodica versata"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl8
msgid "VL8 - Credit resulting from the previous year's declaration"
msgstr "VL8 - Credito risultante dalla dichiarazione dell'anno precedente"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vl9
msgid "VL9 - Credit offset in the template"
msgstr "VL9 - Compensazione del credito nel modello"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp10
#: model:account.report.line,name:l10n_it.tax_report_line_vp10
msgid "VP10 - EU car payments"
msgstr "VP10 - Versamenti auto UE"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp11
#: model:account.report.line,name:l10n_it.tax_report_line_vp11
msgid "VP11 - Tax Credit"
msgstr "VP11 - Credito d'imposta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp12
#: model:account.report.line,name:l10n_it.tax_report_line_vp12
msgid "VP12 - Interest due for quarterly settlements"
msgstr "VP12 - Interessi dovuti per liquidazioni trimestrali"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp13
#: model:account.report.line,name:l10n_it.tax_report_line_vp13
msgid "VP13 - Down payment due"
msgstr "VP13 - Acconto dovuto"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp14
#: model:account.report.line,name:l10n_it.tax_report_line_vp14
msgid "VP14 - VAT payable"
msgstr "VP14 - IVA da versare"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp14a
#: model:account.report.line,name:l10n_it.tax_report_line_vp14a
msgid "VP14a - VAT payable (debit)"
msgstr "VP14a - IVA da versare (debito)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp14b
#: model:account.report.line,name:l10n_it.tax_report_line_vp14b
msgid "VP14b - VAT payable (credit)"
msgstr "VP14b - IVA da versare (credito)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp2
#: model:account.report.line,name:l10n_it.tax_report_line_vp2
msgid "VP2 - Total active transactions"
msgstr "VP2 - Totale operazioni attive"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp3
#: model:account.report.line,name:l10n_it.tax_report_line_vp3
msgid "VP3 - Total passive transactions"
msgstr "VP3 - Totale operazioni passive"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp4
#: model:account.report.line,name:l10n_it.tax_report_line_vp4
msgid "VP4 - VAT due"
msgstr "VP4 - IVA esigibile"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp5
#: model:account.report.line,name:l10n_it.tax_report_line_vp5
msgid "VP5 - VAT Deductible"
msgstr "VP5 - IVA detraibile"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp6
#: model:account.report.line,name:l10n_it.tax_report_line_vp6
msgid "VP6 - VAT due"
msgstr "VP6 - IVA dovuta"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp6a
#: model:account.report.line,name:l10n_it.tax_report_line_vp6a
msgid "VP6a - VAT due (payable)"
msgstr "VP6a - IVA dovuta (debito)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp6b
#: model:account.report.line,name:l10n_it.tax_report_line_vp6b
msgid "VP6b - VAT due (credit)"
msgstr "VP6b - IVA dovuta (credito)"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp7
#: model:account.report.line,name:l10n_it.tax_report_line_vp7
msgid "VP7 - Previous period debt not to exceed 25,82"
msgstr "VP7 - Debito periodo precedente non superiore 25,82"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp8
#: model:account.report.line,name:l10n_it.tax_report_line_vp8
msgid "VP8 - Previous period credit"
msgstr "VP8 - Credito periodo precedente"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_monthly_report_line_vp9
#: model:account.report.line,name:l10n_it.tax_report_line_vp9
msgid "VP9 - Previous year credit"
msgstr "VP9 - Credito anno precedente"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_D_PL
msgid "Value adjustments of financial assets and liabilities"
msgstr "Rettifiche di valore di attività e passività finanziarie"

#. module: l10n_it
#: model:account.account.tag,name:l10n_it.account_tag_A_PL
msgid "Value of production"
msgstr "Valore della produzione"

#. module: l10n_it
#: model:account.report.line,name:l10n_it.tax_annual_report_line_vari_communi_perd
msgid "Changes in reporting periodicals"
msgstr "Variazioni dei periodici di comunicazione"

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n1
msgid "[N1] Escluse ex art. 15"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n2_1
msgid ""
"[N2.1] Non soggette ad IVA ai sensi degli artt. Da 7 a 7-septies del DPR "
"633/72"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n2_2
msgid "[N2.2] Non soggette - altri casi"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n2
msgid "[N2] Non soggette"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_1
msgid "[N3.1] Non imponibili - esportazioni"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_2
msgid "[N3.2] Non imponibili - cessioni intracomunitarie"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_3
msgid "[N3.3] Non imponibili - cessioni verso San Marino"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_4
msgid ""
"[N3.4] Non imponibili - operazioni assimilate alle cessioni all'esportazione"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_5
msgid "[N3.5] Non imponibili - a seguito di dichiarazioni d'intento"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3_6
msgid ""
"[N3.6] Non imponibili - altre operazioni che non concorrono alla formazione "
"del plafond"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n3
msgid "[N3] Non imponibili"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n4
msgid "[N4] Esenti"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n5
msgid "[N5] Regime del margine / IVA non esposta in fattura"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_1
msgid ""
"[N6.1] Inversione contabile - cessione di rottami e altri materiali di "
"recupero"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_2
msgid "[N6.2] Inversione contabile - cessione di oro e argento puro"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_3
msgid "[N6.3] Inversione contabile - subappalto nel settore edile"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_4
msgid "[N6.4] Inversione contabile - cessione di fabbricati"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_5
msgid "[N6.5] Inversione contabile - cessione di telefoni cellulari"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_6
msgid "[N6.6] Inversione contabile - cessione di prodotti elettronici"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_7
msgid ""
"[N6.7] Inversione contabile - prestazioni comparto edile esettori connessi"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_8
msgid "[N6.8] Inversione contabile - operazioni settore energetico"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6_9
msgid "[N6.9] Inversione contabile - altri casi"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n6
msgid ""
"[N6] Inversione contabile (per le operazioni in reverse charge ovvero nei "
"casi di autofatturazione per acquisti extra UE di servizi ovvero per "
"importazioni di beni nei soli casi previsti)"
msgstr ""

#. module: l10n_it
#: model:ir.model.fields.selection,name:l10n_it.selection__account_tax__l10n_it_exempt_reason__n7
msgid ""
"[N7] IVA assolta in altro stato UE (prestazione di servizi di "
"telecomunicazioni, tele-radiodiffusione ed elettronici ex art. 7-octies, "
"comma 1 lett. a, b, art. 74-sexies DPR 633/72)"
msgstr ""
