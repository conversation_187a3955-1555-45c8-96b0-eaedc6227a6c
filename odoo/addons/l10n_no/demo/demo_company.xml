<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_no" model="res.partner" forcecreate="1">
        <field name="name">NO Company</field>
        <field name="vat">NO072274687MVA</field>
        <field name="street"/>
        <field name="city">Åmot</field>
        <field name="country_id" ref="base.no"/>

        <field name="zip"/>
        <field name="phone">+47 406 12 345</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.noexample.com</field>
        <field name="l10n_no_bronnoysund_number">*********</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_no" model="res.company" forcecreate="1">
        <field name="name">NO Company</field>
        <field name="partner_id" ref="base.partner_demo_company_no"/>
    </record>

    <record id="base.demo_bank_no" model="res.partner.bank" forcecreate="1">
        <field name="acc_number">NO8330001234567</field>
        <field name="partner_id" ref="base.partner_demo_company_no"/>
        <field name="company_id" ref="base.demo_company_no"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_no')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_no'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>no</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_no')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
