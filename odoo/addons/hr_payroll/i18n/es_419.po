# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_payroll
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_worked_days.py:0
msgid " (Half-Day)"
msgstr " (Medio día)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count
msgid "# Payslip"
msgstr "# Recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__payslips_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_count
msgid "# Payslips"
msgstr "# Recibos de nómina"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "%(employee_name)s-declaration-%(year)s"
msgstr "%(employee_name)s-declaración-%(year)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid ""
"%(error_type)s\n"
"- Employee: %(employee)s\n"
"- Contract: %(contract)s\n"
"- Payslip: %(payslip)s\n"
"- Salary rule: %(name)s (%(code)s)\n"
"- Error: %(error_message)s"
msgstr ""
"%(error_type)s\n"
"- Empleado: %(employee)s\n"
"- Contrato: %(contract)s\n"
"- Recibo de nómina: %(payslip)s\n"
"- Regla salarial: %(name)s (%(code)s)\n"
"- Error: %(error_message)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "%(rate)s Hours/week"
msgstr "%(rate)s horas a la semana"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "%(start_date_string)s and %(end_date_string)s"
msgstr "%(start_date_string)s y %(end_date_string)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/models/hr_payroll_structure.py:0
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "%s (copy)"
msgstr "%s (copia)"

#. module: hr_payroll
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_light_payslip
#: model:ir.actions.report,print_report_name:hr_payroll.action_report_payslip
msgid "'Payslip - %s' % (object.name)"
msgstr "'Recibo de nómina - %s' % (object.name)"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "(%s Payslips)"
msgstr "(%s Recibos de nómina)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"                \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"                \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"                \n"
"* When the user cancels a payslip, the status is 'Canceled'."
msgstr ""
"* Cuando se crea el recibo de nómina, el estado es 'Borrador'\n"
"\n"
"* Si el recibo de nómina se está verificando, el estado es 'En espera'.\n"
"\n"
"* Si se confirma el recibo de nómina, el estado es 'Hecho'.\n"
"\n"
"* Cuando el usuario cancela el recibo de nómina, el estado es 'Cancelado'."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "-> Report"
msgstr "-> Reporte"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Hour"
msgstr "/ Hora"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "/ Month"
msgstr "/ Mes"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ day"
msgstr "/ día"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ half-month"
msgstr "/ medio mes"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ half-year"
msgstr "/ medio año"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ quarter"
msgstr "/ trimestre"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ two months"
msgstr "/ dos meses"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ two weeks"
msgstr "/ dos semanas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ week"
msgstr "/ semana"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "/ year"
msgstr "/ año"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "1. Save documents to terminated employees"
msgstr "1. Guarde los documentos en empleados despedidos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "100"
msgstr "100"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_13th_month_salary
msgid "13th pay salary"
msgstr "13º sueldo"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "1st semester of %s"
msgstr "1er semestre de %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "2. Index salaries for Marketing department"
msgstr "2. Indexe salarios para el departamento de Marketing"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "2023 Payroll"
msgstr "Nómina 2023"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "2nd semester of %s"
msgstr "2do semestre de %s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "3. Create a new contract for Marc Demo with his new position"
msgstr ""
"3. Cree un nuevo contrato para Marc Demo con su nuevo puesto de trabajo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid ""
"<span class=\"bg-white opacity-75 w-100 h-100 o_salary_rule_overlay position-absolute top-0 end-0 w-100 h-100 text-center d-flex justify-content-center flex-column fs-3\">\n"
"                            Save your Salary Rule in order to add Parameter Values.\n"
"                        </span>"
msgstr ""
"<span class=\"bg-white opacity-75 w-100 h-100 o_salary_rule_overlay position-absolute top-0 end-0 w-100 h-100 text-center d-flex justify-content-center flex-column fs-3\">\n"
"                            Guarde su regla salarial para agregar valores de parámetro.\n"
"                        </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span class=\"ms-2\"> hours/week</span>"
msgstr "<span class=\"ms-2\"> horas/semana</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Work Entries\n"
"                            </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                            Entradas de trabajo\n"
"                            </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"<span class=\"o_stat_text\"> Payslips </span>\n"
"                            <span class=\"o_stat_value\"> New </span>"
msgstr ""
"<span class=\"o_stat_text\"> Recibos de nómina </span>\n"
"                            <span class=\"o_stat_value\"> Nuevo </span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "<span class=\"o_stat_text\">Eligible Employees</span>"
msgstr "<span class=\"o_stat_text\">Empleados elegibles</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
msgid "<span class=\"o_stat_text\">Employees</span>"
msgstr "<span class=\"o_stat_text\">Empleados</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "<span class=\"o_stat_text\">Payslips</span>"
msgstr "<span class=\"o_stat_text\">Recibos de nómina</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"<span class=\"o_stat_text\">Salary Attachments</span>\n"
"                            <span class=\"o_stat_value\">New</span>"
msgstr ""
"<span class=\"o_stat_text\">Retenciones</span>\n"
"                            <span class=\"o_stat_value\">Nuevo</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"<span nolabel=\"1\" colspan=\"2\">This wizard will generate payslips for all"
" selected employee(s) based on the dates and credit note specified on "
"Payslips Run.</span>"
msgstr ""
"<span nolabel=\"1\" colspan=\"2\">Este asistente generará recibos de nómina "
"para todos los empleados seleccionados, según las fechas y notas de crédito "
"que se especificaron en la ejecución de los recibos.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid ""
"<span role=\"status\" class=\"alert alert-warning d-block\" invisible=\"not "
"display_warning\">You have selected contracts that are not running, this "
"wizard can only index running contracts.</span>"
msgstr ""
"<span role=\"status\" class=\"alert alert-warning d-block\" invisible=\"not "
"display_warning\">Seleccionó contratos que no están activos. Este asistente "
"solo puede indexar contratos activos.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "<span> %</span>"
msgstr "<span> %</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "<span>/ hour</span>"
msgstr "<span>/ hora</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid ""
"<span><strong>Tip:</strong> Each time you edit the quantity or the amount on"
" a line, we recompute the following lines. We recommend that you edit from "
"top to bottom to prevent your edition from being overwritten by the "
"automatic recalculation. Be careful that reordering the lines doesn't "
"recompute them.</span>"
msgstr ""
"<span><strong>Consejo:</strong> cada que edita la cantidad o el importe en "
"una línea, se recalculan las líneas que le siguen a esa. Le recomendamos que"
" las edite de arriba hacia abajo para evitar que este recálculo automático "
"sobrescriba sus cambios. Tenga en cuenta que reordenar las líneas no las "
"recalcula.</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "<span>Days</span>"
msgstr "<span>días</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "<span>Payslips</span>"
msgstr "<span>Recibos de nómina</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<span>Total</span>"
msgstr "<span>Total</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "<span>Versions</span>"
msgstr "<span>Versiones</span>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Address:</strong>"
msgstr "<strong class=\"me-2\">Dirección:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Children:</strong>"
msgstr "<strong class=\"me-2\">Hijos:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Computed On:</strong>"
msgstr "<strong class=\"me-2\">Calculado en:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Contract Start Date:</strong>"
msgstr "<strong class=\"me-2\">Fecha de inicio del contrato:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Contract Type:</strong>"
msgstr "<strong class=\"me-2\">Tipo de contrato:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Department:</strong>"
msgstr "<strong class=\"me-2\">Departamento:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Email:</strong>"
msgstr "<strong class=\"me-2\">Correo electrónico:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">ID:</strong>"
msgstr "<strong class=\"me-2\">ID:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Job Position:</strong>"
msgstr "<strong class=\"me-2\">Puesto de trabajo:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Marital Status:</strong>"
msgstr "<strong class=\"me-2\">Estado civil:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Name:</strong>"
msgstr "<strong class=\"me-2\">Nombre:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Pay Period:</strong>"
msgstr "<strong class=\"me-2\">Periodo de paga:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "<strong class=\"me-2\">Working Schedule:</strong>"
msgstr "<strong class=\"me-2\">Horario laboral:</strong>"

#. module: hr_payroll
#: model_terms:web_tour.tour,rainbow_man_message:hr_payroll.payroll_tours
msgid ""
"<strong>Congrats, Your first payslip is now finished. It's time for you to "
"explore the Payroll app by yourself.</strong>"
msgstr ""
"<strong>Felicidades, terminó con su primer recibo de nómina. Ahora debe "
"explorar la aplicación Nómina por usted mismo.</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Register Name:</strong>"
msgstr "<strong>Nombre registrado:</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "<strong>YTD</strong>"
msgstr "<strong>EHF</strong>"

#. module: hr_payroll
#: model:mail.template,body_html:hr_payroll.mail_template_new_payslip
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Dear <t t-esc=\"object.employee_id.name\"/>, a new payslip is available for you.<br/><br/>\n"
"        Please find the PDF in your employee portal.<br/><br/>\n"
"        Have a nice day,<br/>\n"
"        The HR Team\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto;\"><tbody>\n"
"    <tr><td valign=\"top\" style=\"text-align: left; font-size: 14px;\">\n"
"        Apreciable <t t-esc=\"object.employee_id.name\"/>, tiene un nuevo recibo de nómina disponible.<br/><br/>\n"
"        Encontrará el PDF en su portal de empleado.<br/><br/>\n"
"        Buen día,<br/>\n"
"        El equipo de RR. HH.\n"
"    </td></tr>\n"
"</tbody></table>\n"
"            "

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_work_entry_type_is_unforeseen_is_leave
msgid "A unforeseen absence must be a leave."
msgstr "Una ausencia imprevista debe ser un permiso."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Accounting"
msgstr "Contabilidad"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_needaction
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "Action Needed"
msgstr "Se requiere una acción"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__active
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__active
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Active"
msgstr "Activo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__active_amount
msgid "Active Amount"
msgstr "Importe activo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_decoration
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de la actividad de excepción"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_icon
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono del tipo de actividad"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/views/add_payslips_hook.js:0
#: code:addons/hr_payroll/static/src/views/payslip_batch_form/payslip_batch_form.js:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Add Payslips"
msgstr "Agregar recibos de nómina"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Add a <strong>name</strong> to the contract."
msgstr "Agregue un <strong>nombre</strong> al contrato."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Add a employee to your contract"
msgstr "Agregar un empleado a su contrato"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_from_type
msgid "Add a new salary structure"
msgstr "Agregar una nueva estructura salarial"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Add an internal note..."
msgstr "Agregue una nota interna..."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__ytd_computation
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__ytd_computation
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__ytd_computation
msgid ""
"Adds a column in the payslip that shows the accumulated amount paid for "
"different rules during the year"
msgstr ""
"Agrega una columna en la nómina de pago que muestre la cantidad pagada "
"acumulada para diferentes reglas durante el año."

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_manager
msgid "Administrator"
msgstr "Administrador"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.xml:0
msgid "All"
msgstr "Todos"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips
msgid "All Payslips"
msgstr "Todos los recibos de nómina"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.ALW
msgid "Allowance"
msgstr "Subsidio"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__none
msgid "Always True"
msgstr "Siempre verdadero"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Amount"
msgstr "Importe"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_other_input_id
msgid "Amount Other Input"
msgstr "Cantidad de otras entradas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_select
msgid "Amount Type"
msgstr "Tipo de importe"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Amount to pay"
msgstr "Importe a pagar"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Amount to pay each payslip."
msgstr "Importe a pagar en cada recibo de nómina."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__active_amount
msgid ""
"Amount to pay for this payslip, Payslip Amount or less depending on the "
"Remaining Amount."
msgstr ""
"Importe a pagar de este recibo de nómina, el monto del recibo o menos según "
"el importe restante."

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_payroll_employee_declaration_unique_employee_sheet
msgid "An employee can only have one declaration per sheet."
msgstr "Un empleado solo puede tener una declaración por hoja."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payroll_stats/payroll_stats.xml:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__annually
msgid "Annually"
msgstr "Anual"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid ""
"Another refund payslip with the same amount has been found. Do you want to "
"create a new one?"
msgstr ""
"Se encontro otro recibo de nómina de reembolso con el mismo importe. ¿Desea "
"crear uno nuevo? "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Appears on Payslip"
msgstr "Aparece en el recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""
"Se utiliza esta regla para el cálculo si la condición es verdadera. Puede "
"especificar una condición como: 'basic > 1000'."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Approximated end date."
msgstr "Fecha de finalización aproximada."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__4
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__4
msgid "April"
msgstr "Abril"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Archived"
msgstr "Archivado"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.js:0
msgid ""
"Are you sure you want to delete this note? All content will be definitely "
"lost."
msgstr ""
"¿Está seguro de que quiere eliminar esta nota? El contenido se borrará y no "
"lo podrá recuperar."

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_assignment_of_salary_rule
msgid "Assignment of Salary"
msgstr "Asignación salarial"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"At least one previous negative net could be reported on this payslip for %s"
msgstr ""
"Se registró al menos un neto negativo en este recibo de nómina para %s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_attachment_count
msgid "Attachment Count"
msgstr "Número de archivos adjuntos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment_name
msgid "Attachment Name"
msgstr "Nombre del archivo adjunto"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_attachment_of_salary_rule
msgid "Attachment of Salary"
msgstr "Deducción salarial"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__attendance
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__attendance
msgid "Attendances"
msgstr "Asistencias"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__8
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__8
msgid "August"
msgstr "Agosto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "August 2023 Payslip"
msgstr "Recibo de nómina de agosto de 2023"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid "Availability in Structure"
msgstr "Disponibilidad en estructura"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__available_in_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
msgid "Available in attachments"
msgstr "Disponible en retenciones"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__avatar_1920
msgid "Avatar"
msgstr "Avatar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Basic Wage"
msgstr "Salario básico promedio"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
msgid "Average of Net Wage"
msgstr "Salario neto promedio"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Bank account"
msgstr "Cuenta bancaria"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.BASIC
msgid "Basic"
msgstr "Básico"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_basic_salary_rule
msgid "Basic Salary"
msgstr "Salario básico"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__basic_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__basic_wage
msgid "Basic Wage"
msgstr "Salario básico"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__leave_basic_wage
msgid "Basic Wage for Time Off"
msgstr "Salario básico por tiempo personal"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Batch"
msgstr "Lote"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payslip_run_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Batch Name"
msgstr "Nombre del lote"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.xml:0
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_run
msgid "Batches"
msgstr "Lotes"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_be_hr_payroll
msgid "Belgium Payroll"
msgstr "Nómina de Bélgica "

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__bi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-monthly
msgid "Bi-monthly"
msgstr "Bimestral"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__bi-weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__bi-weekly
msgid "Bi-weekly"
msgstr "Quincenal"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Board meeting summary:"
msgstr "Resumen de la reunión de la junta directiva:"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_payment_report_wizard__export_format__csv
msgid "CSV"
msgstr "CSV"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Calculations"
msgstr "Cálculos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__calendar_changed
msgid "Calendar Changed"
msgstr "Cambio del calendario"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_cancel_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Cancel"
msgstr "Cancelar"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__cancel
msgid "Canceled"
msgstr "Cancelado"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__cancel
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__cancelled
msgid "Cancelled"
msgstr "Cancelado"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Cannot cancel a payslip that is done."
msgstr "No se puede cancelar un recibo de nómina que ya está finalizado."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Cannot mark payslip as paid if not confirmed."
msgstr ""
"No puede marcar un recibo de nómina como pagado si no se ha confirmado."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__code
msgid ""
"Careful, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Cuidado, el código se utiliza en muchas referencias, cambiarlo podría "
"ocasionar cambios no deseados."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_headcount_line__employee_type
msgid ""
"Categorize your Employees by type. This field also has an impact on "
"contracts. Only Employees, Students and Trainee will have contract history."
msgstr ""
"Categorice sus empleados por tipo. Este campo también afectará a sus "
"contactos, solo los Empleados, Estudiantes y Aprendices tendrán historial de"
" contracto."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__category_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__category_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Category"
msgstr "Categoría"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__is_refund
msgid ""
"Check if the value of the salary attachment must be taken into account as "
"negative (-X)"
msgstr ""
"Revise si el valor de la retención de sueldo debe considerarse como negativa"
" (-X)"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Check the <strong>Work Entries</strong> linked to your newly created "
"Contract."
msgstr ""
"Revise las <strong>entradas de trabajo</strong> vinculadas a su contrato "
"recién creado."

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.default_child_support
msgid "Child Support"
msgstr "Pensión alimenticia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__children_ids
msgid "Children"
msgstr "Hijos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Choose a Payroll Localization"
msgstr "Elija una localización de nómina"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click here to create a new <strong>Contract</strong>."
msgstr "Haga clic aquí para crear un nuevo <strong>contrato</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click here to generate a <strong>Batch</strong> for the displayed Employees."
msgstr ""
"Haga clic aquí para generar un <strong>lote</strong> para los empleados que "
"se muestran."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click on <strong>Salary Information</strong> to access additional fields."
msgstr ""
"Haga clic en <strong>información de salario</strong> para acceder a campos "
"adicionales."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on Employees to pick one of your <strong>Employees</strong>."
msgstr "Haga clic en Empleados para elegir sus <strong>empleados</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Click on Payroll to manage your employee's <strong>Work Entries</strong>, "
"<strong>Contracts</strong> and <strong>Payslips</strong>."
msgstr ""
"Haga clic en Nómina para gestionar las <strong>entradas de trabajo</strong>,"
" <strong>contratos</strong> y <strong>recibos de nómina</strong> de su "
"empleado."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on the <strong>Payslip</strong>."
msgstr "Haga clic en el <strong>recibo de nómina</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Click on the <strong>Work Entries</strong> menu."
msgstr "Haga clic en el menú de <strong>entradas de trabajo</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.xml:0
msgid "Close"
msgstr "Cerrar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid_date
msgid "Close Date"
msgstr "Fecha de cierre"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__half-up
msgid "Closest"
msgstr "Lo más cercano"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__structure_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Code"
msgstr "Código"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr "Código:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__color
msgid "Color"
msgstr "Color"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__company_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Company"
msgstr "Empresa"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.COMP
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Company Contribution"
msgstr "Contribución de la empresa"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__full_time_required_hours
msgid "Company Full Time"
msgstr "Tiempo completo de la empresa"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__close
msgid "Completed"
msgstr "Terminado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Computation"
msgstr "Cálculo"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_compute_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Compute Sheet"
msgstr "Calcular hoja"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__compute_date
msgid "Computed On"
msgstr "Calculado el"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_select
msgid "Condition Based on"
msgstr "Condición basada en"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_other_input_id
msgid "Condition Other Input"
msgstr "Otra condición de entrada"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Conditions"
msgstr "Condiciones"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Confirm"
msgstr "Confirmar"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Confirm the <strong>Payslip</strong>."
msgstr "Confirmar el <strong>recibo de nómina</strong>."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__verify
msgid "Confirmed"
msgstr "Confirmado"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__conflict
msgid "Conflict"
msgstr "Conflicto"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_work_entries_in_conflict
msgid "Conflicts"
msgstr "Conflictos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__contract_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__contract_ids
msgid "Contract"
msgstr "Contrato"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__contract_domain_ids
msgid "Contract Domain"
msgstr "Dominio de contrato"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Contract Expiration Notice Period"
msgstr "Plazo de notificación de vencimiento del contrato"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__contract_names
msgid "Contract Names"
msgstr "Nombres del contrato"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Contract Type"
msgstr "Tipo de contrato"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Contract Wage ("
msgstr "Salario del contrato ("

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "Historial de contratos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_index_form_view
msgid "Contract indexing"
msgstr "Indexación de contratos"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_contract_repository
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__contract_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_all_contracts
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employees_root
msgid "Contracts"
msgstr "Contratos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Contribution Register"
msgstr "Registro de contribución"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_contribution_registers
#: model:ir.actions.report,name:hr_payroll.action_report_register
msgid "Contribution Registers"
msgstr "Registros de contribución"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr "Subsidio de transporte"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_ca_gravie
msgid "Conveyance Allowance For Gravie"
msgstr "Subsidio de transporte"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__amount
msgid "Count"
msgstr "Número"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__country_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__country_id
msgid "Country"
msgstr "País"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__country_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__country_code
msgid "Country Code"
msgstr "Código de país"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_confirm_payroll
msgid "Create Draft Entry"
msgstr "Crear un borrador de entrada"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Create Individual Attachments"
msgstr "Crear deducciones salariales individuales"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Create Payment Report"
msgstr "Crear reporte de pago"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Create SEPA payment"
msgstr "Cree pagos SEPA"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_new_salary_attachment
msgid "Create Salary Attachment"
msgstr "Crear deducción salarial"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__create_uid
msgid "Create Uid"
msgstr "Crear UID"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_hr_contract_repository
msgid "Create a new contract"
msgstr "Crear un nuevo contrato"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_salary_rule_form
msgid "Create a new salary rule"
msgstr "Crear una nueva regla salarial"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_rule_parameter_action
msgid "Create a new salary rule parameter"
msgstr "Crear un nuevo parámetro para regla salarial"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Create a new salary structure"
msgstr "Crear una nueva estructura salarial"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/todo_list/todo_list.xml:0
msgid "Create new todo note"
msgstr "Crear una nueva nota de pendientes"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__create_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__create_date
msgid "Created on"
msgstr "Creado el"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__credit_note
msgid "Credit Note"
msgstr "Nota de crédito"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Credit Notes"
msgstr "Notas de crédito"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_credit_time
msgid "Credit Time"
msgstr "Tiempo de crédito"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__time_credit
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry__is_credit_time
msgid "Credit time"
msgstr "Hora de crédito"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__currency_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Current month"
msgstr "Mes actual"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__is_name_custom
msgid "Custom Name"
msgstr "Nombre personalizado"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__daily
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__daily
msgid "Daily"
msgstr "Diario"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_root
msgid "Dashboard"
msgstr "Tablero"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Date"
msgstr "Fecha"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_start
msgid "Date From"
msgstr "Fecha desde"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__date_start
msgid "Date Start"
msgstr "Fecha de inicio"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__date_end
msgid "Date To"
msgstr "Fecha hasta"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__date_end
msgid "Date at which this assignment has been set as completed or cancelled."
msgstr ""
"Fecha en la que se estableció esta actividad como completada o cancelada."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__full
msgid "Day"
msgstr "Día"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Day where the 'Year To Date' will be reset every year."
msgstr "Día en el que el \"Ejercicio hasta la fecha\" se reiniciará cada año."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_res_company__ytd_reset_day
#: model:ir.model.fields,help:hr_payroll.field_res_config_settings__ytd_reset_day
msgid ""
"Day where the YTD will be reset every year. If zero or negative, then the first day of the month will be selected instead.\n"
"        If greater than the last day of a month, then the last day of the month will be selected instead."
msgstr ""
"Día en el que EHF se reiniciará cada año. Si es cero o negativo, entonces el primer día del mes se seleccionará.\n"
"Si es mayor al último día del mes, entonces se seleccionará el último día del mes en su lugar."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__number_of_days
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Days"
msgstr "Días"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave
msgid "Days of Paid Time Off"
msgstr "Días de tiempo personal pagado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_unforeseen_absence
msgid "Days of Unforeseen Absence"
msgstr "Días de ausencia imprevista"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_leave_unpaid
msgid "Days of Unpaid Time Off"
msgstr "Días de tiempo personal sin pagar"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__12
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__12
msgid "December"
msgstr "Diciembre"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__res_id
msgid "Declaration Model Id"
msgstr "ID del modelo de declaración"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__res_model
msgid "Declaration Model Name"
msgstr "Nombre del modelo de declaración"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__line_ids
msgid "Declarations"
msgstr "Declaraciones"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_deduction_salary_rule
#: model:hr.salary.rule.category,name:hr_payroll.DED
msgid "Deduction"
msgstr "Deducción"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Default Scheduled Pay"
msgstr "Pago programado predeterminado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__wage_type
msgid "Default Wage Type"
msgstr "Tipo de salario predeterminado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "Tipo de entrada de trabajo predeterminado"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Define a <strong>Wage</strong>."
msgstr "Definir un <strong>salario</strong>."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__schedule_pay
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr "Define la frecuencia del pago de salario."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, according to the "
"contract chosen. If the contract is empty, this field isn't mandatory "
"anymore and all the valid rules of the structures of the employee's "
"contracts will be applied."
msgstr ""
"Define las reglas que se tienen que aplicar a este recibo de nómina según el"
" contrato elegido. Si el contrato está vacío, este campo ya no es "
"obligatorio y se aplicarán todas las reglas de estructuras validadas en los "
"contratos de los empleados."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__department_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Department"
msgstr "Departamento"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Department:"
msgstr "Departamento:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__description
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__note
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__note
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Description"
msgstr "Descripción"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__disabled
msgid "Disabled"
msgstr "Deshabilitado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Discard"
msgstr "Descartar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__display_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Display Payslip PDF File on a payslip form"
msgstr ""
"Mostrar el archivo PDF de los recibos de nómina en un formato para recibos "
"de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Display in Payslip"
msgstr "Mostrar en el recibo de nómina"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_payslip_display
msgid "Display payslip PDF"
msgstr "Mostrar recibo de nómina en formato PDF "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__attachment
msgid "Document"
msgstr "Documento"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__done
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__close
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done"
msgstr "Listo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Done Payslip Batches"
msgstr "Lotes de recibos de nómina finalizados"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Done Slip"
msgstr "Recibos realizados"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Double Holiday Pay"
msgstr "Pago doble en día festivo"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__down
msgid "Down"
msgstr "Abajo"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__draft
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__draft
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft"
msgstr "Borrador"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Draft Payslip Batches"
msgstr "Lotes de recibos de nómina en borrador"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Draft Slip"
msgstr "Borrador de recibo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Earnings"
msgstr "Ganancias"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.server,name:hr_payroll.action_edit_payslip_lines
msgid "Edit Payslip Lines"
msgstr "Editar líneas de recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__edit_payslip_lines_wizard_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__edit_payslip_lines_wizard_id
msgid "Edit Payslip Lines Wizard"
msgstr "Asistente de edición de líneas de recibo de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_worked_days_line
msgid "Edit payslip line wizard worked days"
msgstr "Asistente de edición de líneas de recibo de nómina de días trabajados"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_lines_wizard
msgid "Edit payslip lines wizard"
msgstr "Asistente de edición de líneas de recibo de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_edit_payslip_line
msgid "Edit payslip lines wizard line"
msgstr "Asistente de edición de líneas de recibo de nómina línea"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__edited
msgid "Edited"
msgstr "Editado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Edition of Payslip Lines in the Payslip"
msgstr "Edición de líneas de recibo de nómina en la nómina"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__eligible_employee_line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_employee_mixin_list_view
msgid "Eligible Employees"
msgstr "Empleados elegibles:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__eligible_employee_count
msgid "Eligible Employees Count"
msgstr "Conteo de empleados elegibles:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__email_cc
msgid "Email cc"
msgstr "CC del correo electrónico"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__employee_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__employee_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Employee"
msgstr "Empleado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Employee Code"
msgstr "Código del empleado"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Contrato del empleado"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__employee_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_count
msgid "Employee Count"
msgstr "Número de empleados"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "Employee Declarations"
msgstr "Declaraciones de los empleados"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_list_view
msgid "Employee Function"
msgstr "Función del empleado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Employee Information"
msgstr "Información del empleado"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payslip_batch/payslip_batch.js:0
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_form
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_month_form
msgid "Employee Payslips"
msgstr "Recibos de nómina del empleado"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Employee Trends"
msgstr "Tendencias del empleado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__employee_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Employee Type"
msgstr "Tipo de empleado"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Employee address"
msgstr "Dirección del empleado"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Employee name"
msgstr "Nombre del empleado"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_with_different_company_on_contract
msgid "Employee whose contracts and company are differents"
msgstr "Empleado cuyos contratos y empresa son diferentes"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__hourly_wage
msgid "Employee's hourly gross wage."
msgstr "Salario bruto por hora del empleado."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__resource_calendar_id
msgid ""
"Employee's working schedule.\n"
"        When left empty, the employee is considered to have a fully flexible schedule, allowing them to work without any time limit, anytime of the week.\n"
"        "
msgstr ""
"Horario laboral del empleado.\n"
"Cuando se deja vacío se considera que el empleado tiene un horario laboral flexible, lo cual les permite trabajar sin límite de tiempo, en cualquier momento de la semana."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__employee_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__employee_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Employees"
msgstr "Empleados"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_missing_from_open_batch
msgid "Employees (With Running Contracts) missing from open batches"
msgstr "Faltan empleados (con contratos activos) en los lotes abiertos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Employees Selection"
msgstr "Selección de empleados"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_ambiguous_contract
msgid "Employees With Both New And Running Contracts"
msgstr "Empleados con contratos tanto nuevos como activos"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employees_multiple_payslips
msgid "Employees With Multiple Open Payslips of Same Type"
msgstr "Empleados con múltiples recibos de nómina abiertos al mismo tiempo"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_bank_account
msgid "Employees Without Bank account Number"
msgstr "Empleados sin número de cuenta bancario"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_identification
msgid "Employees Without Identification Number"
msgstr "Empleados sin número de identificación"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_employee_without_contract
msgid "Employees Without Running Contracts"
msgstr "Empleados sin contratos activos"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_nearly_expired_contracts
msgid "Employees with running contracts coming to an end"
msgstr "Empleados con contratos actuales próximos a terminar"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Employer Cost"
msgstr "Costo del empleador"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__hide_basic_on_pdf
msgid ""
"Enable this option if you don't want to display the Basic Salary on the "
"printed pdf."
msgstr ""
"Active esta opción si no desea mostrar el salario básico en el PDF impreso."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_end
msgid "End Date"
msgstr "Fecha de finalización"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_dates
msgid "End date may not be before the starting date."
msgstr "La fecha de finalización no debe ser anterior a la fecha de inicio."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__display_warning
msgid "Error"
msgstr "Error"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule_category.py:0
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr ""
"¡Error! No puede crear una jerarquía recursiva de la categoría de regla "
"salarial."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_estimated_end
msgid "Estimated End Date"
msgstr "Fecha de finalización estimada"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__partner_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr ""
"Empresa externa eventual involucrada en el pago de salarios de los "
"empleados."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__export_id
msgid "Export"
msgstr "Exportar"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__payment_report
msgid "Export .csv file related to this batch"
msgstr "Exportar archivo .csv relacionado a este lote"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__payment_report
msgid "Export .csv file related to this payslip"
msgstr "Exportar archivo .csv relacionado a este recibo de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_list_view
msgid "Export Employee Work Entries"
msgstr "Exportar las entradas al trabajo de los empleados"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__export_file
msgid "Export File"
msgstr "Exportar archivo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__export_filename
msgid "Export Filename"
msgstr "Exportar nombre del archivo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__export_format
msgid "Export Format"
msgstr "Exportar formato"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Export Payslip"
msgstr "Exportar recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__2
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__2
msgid "February"
msgstr "Febrero"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "First, we'll create a new <strong>Contract</strong>."
msgstr "Primero, crearemos un nuevo <strong>contrato</strong>."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_fix
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_fix
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__fix
msgid "Fixed Amount"
msgstr "Importe fijo"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__wage_type__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__monthly
msgid "Fixed Wage"
msgstr "Salario fijo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_follower_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_partner_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (contactos)"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_type_icon
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome, por ejemplo, fa-tasks"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr "Por ejemplo, introduzca 50.0 para aplicar un porcentaje de 50%"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_fr_hr_payroll
msgid "French Payroll"
msgstr "Nómina de Francia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__date_from
msgid "From"
msgstr "Desde"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "From %(from_date)s to %(end_date)s"
msgstr "Del %(from_date)s al %(end_date)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "General"
msgstr "General"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr "Generar"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "Generate Export File"
msgstr "Generar archivo de exportación"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_tree
msgid "Generate PDFs"
msgstr "Generar PDF"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/hr_work_entries_gantt.xml:0
#: code:addons/hr_payroll/static/src/views/work_entry_calendar/work_entry_calendar.xml:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_by_employees
#: model:ir.actions.server,name:hr_payroll.action_hr_payslip_run_generate
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generate Payslips"
msgstr "Generar recibos de nómina"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_generate_payslips_from_work_entries
msgid "Generate payslips"
msgstr "Generar recibos de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Generar los recibos de nómina para todos los empleados seleccionados"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_generated
msgid "Generated PDF"
msgstr "PDF generado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Generated Payslips"
msgstr "Recibos de nómina generados"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Give insurance card to new registered employees"
msgstr ""
"Proporcionar tarjeta de seguro médico a los nuevos empleados registrados"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__gross_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__gross_wage
msgid "Gross Wage"
msgstr "Salario bruto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Group By"
msgstr "Agrupar por"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__group_payslip_display
msgid "Group Payslip Display"
msgstr "Agrupar visualización de recibos de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_payment_report_wizard
msgid "HR Payroll Payment Report Wizard"
msgstr "Asistente de reportes de pagos de nómina para RR. HH."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry
msgid "HR Work Entry"
msgstr "Entrada de trabajo de RR. HH."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "Tipo de entrada de trabajo de RR. HH."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__half
msgid "Half Day"
msgstr "Medio día"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_done_payslip
msgid "Has Done Payslip"
msgstr "¿Tiene un recibo de nómina elaborado?"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__has_message
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_negative_net_to_report
msgid "Has Negative Net To Report"
msgstr "Tiene neto negativo para reportar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__has_refund_slip
msgid "Has Refund Slip"
msgstr "Tiene recibo de reembolso"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment
msgid "Has Similar Attachment"
msgstr "Tiene archivos adjuntos similares"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_similar_attachment_warning
msgid "Has Similar Attachment Warning"
msgstr "Tiene advertencia de archivo adjunto similar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__has_total_amount
msgid "Has Total Amount"
msgstr "Tiene importe total"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payroll_headcount_action
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_headcount_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_tree
msgid "Headcount"
msgstr "Plantilla laboral"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount_line
msgid "Headcount Line"
msgstr "Línea de la plantilla laboral"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount for %(company_name)s from %(date_from)s to %(date_to)s"
msgstr ""
"Plantilla de personal de %(company_name)s del %(date_from)s al %(date_to)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount for %(company_name)s on the %(date)s"
msgstr "Plantilla de personal de %(company_name)s al %(date)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_view_tree
msgid "Headcount's Employees"
msgstr "Empleados de la plantilla laboral"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_headcount.py:0
msgid "Headcount's employees"
msgstr "Empleados de la plantilla laboral"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__hide_basic_on_pdf
msgid "Hide Basic On Pdf"
msgstr "Ocultar básico en PDF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hourly_wage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__wage_type__hourly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__wage_type__hourly
msgid "Hourly Wage"
msgstr "Salario por hora"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Hours"
msgstr "Horas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Hours / Week"
msgstr "Horas / semana"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__hours_per_week
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__hours_per_week
msgid "Hours per Week"
msgstr "Horas por semana"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr "Subsidio de alquiler de casa"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__id
msgid "ID"
msgstr "ID"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_exception_icon
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_icon
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono que indica una actividad de excepción."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_needaction
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren su atención."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_sms_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"If empty, the default salary structure from the Salary structure type of the"
" employee will be used"
msgstr ""
"Si se deja vacío, la estructura salarial del tipo de estructura salarial del"
" empleado se usará"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_employee__is_non_resident
#: model:ir.model.fields,help:hr_payroll.field_res_users__is_non_resident
msgid "If recipient lives in a foreign country"
msgstr "Si el destinatario vive en el extranjero"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__is_quantity
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__is_quantity
msgid ""
"If set, hide currency and consider the manual input as a quantity for every "
"rule computation using this input."
msgstr ""
"Si lo configura, se ocultará la moneda y se considera una entrada manual "
"como una cantidad para el cálculo de cada regla usando esta entrada."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""
"Si el campo activo se establece a falso, se puede ocultar la regla salarial "
"sin eliminarla."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_employee__disabled
msgid "If the employee is declared disabled by law"
msgstr "Si el empleado es una persona con discapacidad por ley"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__image_1920
msgid "Image"
msgstr "Imagen"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__image_128
msgid "Image 128"
msgstr "Imagen 128"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_tree
msgid "Index Contracts"
msgstr "Índice de contratos"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_index
#: model:ir.actions.server,name:hr_payroll.action_index_contracts
msgid "Index contract(s)"
msgstr "Índice de contrato(s)"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_index
msgid "Index contracts"
msgstr "Índice de contratos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_l10n_in_hr_payroll
msgid "Indian Payroll"
msgstr "Nómina de la India"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__credit_note
msgid "Indicates this payslip has a refund of another"
msgstr "Indica que este recibo de nómina incluye un reembolso de otra"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Input Data"
msgstr "Datos de entrada"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__note
msgid "Internal Note"
msgstr "Nota interna"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__internet_invoice
msgid "Internet Subscription Invoice"
msgstr "Factura de suscripción de internet"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_is_follower
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__is_fulltime
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__is_fulltime
msgid "Is Full Time"
msgstr "Es de tiempo completo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__is_paid
msgid "Is Paid"
msgstr "Pagado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Is Register"
msgstr "Es registro"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_regular
msgid "Is Regular"
msgstr "Es regular"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_superuser
msgid "Is Superuser"
msgstr "Es un superusuario "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__is_wrong_duration
msgid "Is Wrong Duration"
msgstr "No es la duración correcta "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__is_quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__is_quantity
msgid "Is quantity?"
msgstr "¿Es una cantidad?"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__quantity
msgid ""
"It is used in computation for percentage and fixed amount. E.g. a rule for "
"Meal Voucher having fixed amount of 1€ per worked day can have its quantity "
"defined in expression like worked_days['WORK100'].number_of_days."
msgstr ""
"Se utiliza en el cálculo para el porcentaje y el importe fijo. Por ejemplo, "
"una regla para un vale de comida que tiene un importe fijo de 1 euro por día"
" trabajado puede tener su cantidad definida en una expresión como "
"worked_days['WORK100'].number_of_days."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__amount
msgid ""
"It is used in computation. E.g. a rule for salesmen having 1%% commission of"
" basic salary per product can defined in expression like: result = "
"inputs['SALEURO'].amount * contract.wage * 0.01."
msgstr ""
"Se utiliza en el cálculo. Por ejemplo, una regla para que un vendedor tenga "
"1% de comisión de salario básico por producto se puede definir en una "
"expresión como: result = inputs['SALEURO'].amount * contract.wage * 0.01."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__1
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__1
msgid "January"
msgstr "Enero"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Job"
msgstr "Puesto"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__job_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__job_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Job Position"
msgstr "Puesto de trabajo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__job_id
msgid "Job Title"
msgstr "Nombre del puesto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Job Title:"
msgstr "Puesto de trabajo:"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__7
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__7
msgid "July"
msgstr "Julio"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__6
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__6
msgid "June"
msgstr "Junio"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Last 365 Days Payslip"
msgstr "Recibos de nómina de los últimos 365 días"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Last Departures"
msgstr "Últimas salidas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Month"
msgstr "Mes pasado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
msgid "Last Quarter"
msgstr "Trimestre anterior"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_uid
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__write_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__line_ids
msgid "Line"
msgstr "Línea"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Line Name"
msgstr "Nombre de línea"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__lines_count
msgid "Lines Count"
msgstr "Número de líneas"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule_category__parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""
"Solo se vincula una categoría salarial con la principal para los reportes."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Links:"
msgstr "Enlaces:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__paid
msgid "Made Payment Order? "
msgstr "¿Realizó la orden de pago? "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_main_attachment_id
msgid "Main Attachment"
msgstr "Archivo adjunto principal"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__3
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__3
msgid "March"
msgstr "Marzo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Mark as Completed"
msgstr "Marcar como completado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Mark as paid"
msgstr "Marcar como pagado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__master_department_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Master Department"
msgstr "Departamento maestro"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "Maximum Range"
msgstr "Intervalo máximo"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__5
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__5
msgid "May"
msgstr "Mayo"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr "Vale de despensa"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error
msgid "Message Delivery error"
msgstr "Error al enviar el mensaje"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "Minimum Range"
msgstr "Intervalo mínimo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__mobile_invoice
msgid "Mobile Subscription Invoice"
msgstr "Factura de suscripción móvil"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_report_hr_payroll_contribution_register
msgid "Model for Printing hr.payslip.line grouped by register"
msgstr "Modelo de impresión hr.payslip.line agrupado por registro"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/payroll_stats/payroll_stats.xml:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__monthly
msgid "Monthly"
msgstr "Mensual"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Monthly Amount"
msgstr "Importe mensual"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__my_activity_date_deadline
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Fecha límite de mi actividad"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Name"
msgstr "Nombre"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__payslip_name
msgid ""
"Name to be set on a payslip. Example: 'End of the year bonus'. If not set, "
"the default value is 'Salary Slip'"
msgstr ""
"Nombre que se establecerá en un recibo de nómina. Ejemplo: 'Bono de fin de "
"año'. Si no se establece, el valor predeterminado es 'Salario'"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Name:"
msgstr "Nombre:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_amount
msgid "Negative Net To Report Amount"
msgstr "Importe neto negativo para reportar "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_display
msgid "Negative Net To Report Display"
msgstr "Pantalla de neto negativo para reportar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__negative_net_to_report_message
msgid "Negative Net To Report Message"
msgstr "Mensaje de neto negativo para reportar"

#. module: hr_payroll
#: model:mail.activity.type,name:hr_payroll.mail_activity_data_hr_payslip_negative_net
msgid "Negative Net to Report"
msgstr "Neto negativo para reportar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__is_refund
msgid "Negative Value"
msgstr "Valor negativo"

#. module: hr_payroll
#: model:hr.salary.rule.category,name:hr_payroll.NET
msgid "Net"
msgstr "Neto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_view_kanban
msgid "Net -"
msgstr "Neto - "

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_net_salary
msgid "Net Salary"
msgstr "Salario neto"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__net_wage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__net_wage
msgid "Net Wage"
msgstr "Salario neto"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__draft
msgid "New"
msgstr "Nuevo"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_new_contracts
msgid "New Contracts"
msgstr "Nuevos contratos"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "New Employees"
msgstr "Nuevos empleados"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "New Payslip"
msgstr "Nuevo recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_calendar_event_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Siguiente evento en el calendario de actividades"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_date_deadline
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Siguiente fecha límite de la actividad"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_summary
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__no_end_date
msgid "No End Date"
msgstr "Sin fecha de finalización "

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "No ID number on the employee !!!"
msgstr "No hay un número de identificación en el empleado."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days__no
msgid "No Rounding"
msgstr "Sin redondeo"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_employee_unique_registration_number
msgid "No duplication of registration numbers is allowed"
msgstr "No se permite la duplicación de números de registro"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input_type__default_no_end_date
msgid "No end date by default"
msgstr "Por defecto no hay fecha de finalización"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
msgid "No rule parameter with code \"%(code)s\" was found for %(date)s"
msgstr ""
"No se encontró ningún parámetro de regla con el código \"%(code)s\" para el "
"%(date)s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__is_non_resident
#: model:ir.model.fields,field_description:hr_payroll.field_res_users__is_non_resident
msgid "Non-resident"
msgstr "No es residente"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_company.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_note__note
msgid "Note"
msgstr "Nota"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid ""
"Note a description of your parameter, when it's used, how is it computed, "
"what's the source, ..."
msgstr ""
"Anota una descripción de su parámetro, cuándo se utiliza, cómo se calcula, "
"cuál es su origen, etc."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Note: There are previous payslips with a negative amount for a total of %s "
"to report."
msgstr ""
"Nota: hay recibos de nómina anteriores con un importe negativo para un total"
" de %s para reportar."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Notes"
msgstr "Notas"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "Nothing to show"
msgstr "Nada que mostrar"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__11
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__11
msgid "November"
msgstr "Noviembre"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_needaction_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_days
msgid "Number of Days"
msgstr "Número de días"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__number_of_hours
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__number_of_hours
msgid "Number of Hours"
msgstr "Número de horas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid ""
"Number of days prior to the contract end date that a contract expiration "
"warning is triggered."
msgstr ""
"Número de días antes de la fecha de terminación del contrato en los que se "
"activa una advertencia de vencimiento del contrato."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid ""
"Number of days prior to the work permit expiration date that a warning is "
"triggered."
msgstr ""
"Número de días antes de la fecha de vencimiento del permiso de trabajo en "
"que se activa una advertencia."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_error_counter
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of errors"
msgstr "Número de errores"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__full_time_required_hours
msgid ""
"Number of hours to work on the company schedule to be considered as "
"fulltime."
msgstr ""
"Número de horas a trabajar en el horario de la empresa para considerarse "
"como tiempo completo. "

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_needaction_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__message_has_error_counter
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__occurrences
msgid "Number of times the salary attachment will appear on the payslip."
msgstr ""
"Número de veces que la deducción salarial aparecerá en el recibo de nómina."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__occurrences
msgid "Occurrences"
msgstr "Ocurrencias"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__10
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__10
msgid "October"
msgstr "Octubre"

#. module: hr_payroll
#: model:res.groups,name:hr_payroll.group_hr_payroll_user
msgid "Officer: Manage all contracts"
msgstr "Encargado: gestionar todos los contratos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "On the"
msgstr "En el "

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the first tab is the amount of worked time giving you a <strong>gross "
"amount</strong>."
msgstr ""
"En la primera pestaña está el importe del tiempo trabajado, el cual da un "
"<strong>importe bruto</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the second tab is the computation of the rules linked to the Structure "
"resulting in a <strong>net amount</strong>."
msgstr ""
"En la segunda pestaña está el cálculo de las reglas vinculadas a la "
"estructura, lo que resulta en un <strong>importe neto</strong>."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"On the smartbutton, you can find all the <strong>Payslips</strong> included "
"in the Batch."
msgstr ""
"En el botón inteligente puede encontrar todos los <strong>recibos de "
"nómina</strong> incluidos en el lote."

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_actions_server_action_open_reporting
msgid "Open Payroll Reporting"
msgstr "Abrir reporte de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Info"
msgstr "Otra información"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Other Information"
msgstr "Otra información"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__input
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__input
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Other Input"
msgstr "Otras entradas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__input_line_type_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input___allowed_input_type_ids
msgid "Other Input Line"
msgstr "Otra línea de entrada"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payslip_entry_type_view
msgid "Other Input Types"
msgstr "Otros tipos de entrada"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Other Inputs"
msgstr "Otras entradas"

#. module: hr_payroll
#: model:hr.work.entry.type,name:hr_payroll.hr_work_entry_type_out_of_contract
msgid "Out of Contract"
msgstr "Fuera de contrato"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__pdf_error
msgid "PDF Error Message"
msgstr "Mensaje de error del PDF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_file
msgid "PDF File"
msgstr "Archivo PDF"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "PDF generation started. It will be available shortly."
msgstr "Se empezó a crear el PDF, pronto estará disponible."

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__paid
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip_run__state__paid
msgid "Paid"
msgstr "Pagado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__paid_amount
msgid "Paid Amount"
msgstr "Importe pagado"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__2
msgid "Paid Time Off"
msgstr "Tiempo personal pagado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Parameter Value"
msgstr "Valor de parámetro"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule_category__parent_id
msgid "Parent"
msgstr "Principal"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__time_credit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_search_inherit
msgid "Part Time"
msgstr "Medio tiempo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__time_credit_type_id
msgid "Part Time Work Entry Type"
msgstr "Tipo de entrada de trabajo de medio tiempo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid "Part time"
msgstr "Medio tiempo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__partner_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__partner_id
msgid "Partner"
msgstr "Contacto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
msgid "Pay Period:"
msgstr "Periodo de paga:"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__slip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__payslip_id
msgid "Pay Slip"
msgstr "Recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__slip_id
msgid "PaySlip"
msgstr "Recibo de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Lines by Contribution Register"
msgstr "Líneas de recibo de nómina por registro de contribución"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "PaySlip Name"
msgstr "Nombre del recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_payment_report_view_form
msgid "Payment Report"
msgstr "Reporte de pago"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report_date
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report_date
msgid "Payment Report Date"
msgstr "Fecha del reporte de pago"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__payment_report_filename
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payment_report_filename
msgid "Payment Report Filename"
msgstr "Nombre de archivo del reporte de pago"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.open_payroll_modules
#: model:ir.ui.menu,name:hr_payroll.menu_report_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr "Nómina"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_reset_work_entries
msgid "Payroll - Technical: Reset Work Entries"
msgstr "Nómina - Técnico: Restablecer entradas de trabajo"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.payroll_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_report_view_search
msgid "Payroll Analysis"
msgstr "Análisis de la nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "Reporte del análisis de la nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__code
msgid "Payroll Code"
msgstr "Código de nómina"

#. module: hr_payroll
#: model:ir.actions.client,name:hr_payroll.hr_payroll_dashboard_open
msgid "Payroll Dashboard"
msgstr "Tablero de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_dashboard_warning
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_tree
msgid "Payroll Dashboard Warning"
msgstr "Advertencia del tablero de nómina"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_dashboard_warning
msgid "Payroll Dashboard Warnings"
msgstr "Advertencias del tablero de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_declaration_mixin
msgid "Payroll Declaration Mixin"
msgstr "Mixin de declaración de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_employee_declaration
msgid "Payroll Employee Declaration"
msgstr "Declaración de nómina del empleado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr "Asientos de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount
msgid "Payroll Headcount"
msgstr "Plantilla laboral de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_note
msgid "Payroll Note"
msgstr "Nota de nómina "

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll Rules"
msgstr "Reglas de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll SEPA"
msgstr "Nómina SEPA"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Payroll Structures"
msgstr "Estructuras de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payroll rules that apply to your country"
msgstr "Reglas salariales que aplican a su país"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "Payroll tips &amp; tricks:"
msgstr "Consejos y trucos de nómina:"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account
msgid "Payroll with Accounting"
msgstr "Nómina y contabilidad"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__module_hr_payroll_account_iso20022
msgid "Payroll with SEPA payment"
msgstr "Nómina con pago SEPA"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_generate_payslip_pdfs_ir_actions_server
msgid "Payroll: Generate pdfs"
msgstr "Nómina: generar PDFs"

#. module: hr_payroll
#: model:mail.template,name:hr_payroll.mail_template_new_payslip
msgid "Payroll: New Payslip"
msgstr "Nómina: nuevo recibo de nómina"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.ir_cron_update_payroll_data_ir_actions_server
msgid "Payroll: Update data"
msgstr "Nómina: actualizar datos"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_new
#: model:ir.actions.report,name:hr_payroll.action_report_payslip
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__payslip_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__payslip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__payslip_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Payslip"
msgstr "Recibo de nómina"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Payslip 'Date From' must be earlier than 'Date To'."
msgstr "La 'Fecha de' recibo de nómina debe ser antes de la 'Fecha a'."

#. module: hr_payroll
#: model:ir.actions.report,name:hr_payroll.action_report_light_payslip
msgid "Payslip (Light)"
msgstr "Recibo de nómina (claro)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__monthly_amount
msgid "Payslip Amount"
msgstr "Monto del recibo de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Payslip Batch"
msgstr "Lote de recibos de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Lotes de recibos de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__payslip_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__payslip_count
msgid "Payslip Count"
msgstr "Número de recibos de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslip Generation"
msgstr "Generación de un recibo de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input"
msgstr "Entrada de recibo de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_tree
msgid "Payslip Input Name"
msgstr "Nombre de entrada del recibo de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_input_type
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_search
msgid "Payslip Input Type"
msgstr "Tipo de entrada del recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__input_line_ids
msgid "Payslip Inputs"
msgstr "Entradas de recibo de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_employee_view_form
msgid "Payslip Language"
msgstr "Idioma del recibo de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_form
msgid "Payslip Line"
msgstr "Línea de recibo de nómina"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.act_contribution_reg_payslip_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__line_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__line_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Payslip Lines"
msgstr "Líneas de recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__payslip_name
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__name
msgid "Payslip Name"
msgstr "Nombre de recibo de nómina"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payslip_input_type
msgid "Payslip Other Input Types"
msgstr "Otros tipos de entrada de recibos de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Payslip PDF Display"
msgstr "Visualización del recibo de nómina en PDF"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Payslip Period"
msgstr "Periodo del recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_payment_report_wizard__payslip_run_id
msgid "Payslip Run"
msgstr "Ejecución del recibo de nómina"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__worked_days_line_ids
msgid "Payslip Worked Days"
msgstr "Días trabajados en el recibo de nómina"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_monthly_amount
msgid "Payslip amount must be strictly positive."
msgstr "El monto del recibo de nómina debe ser estrictamente positivo."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.actions.act_window,name:hr_payroll.act_hr_employee_payslip_list
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__slip_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__payslip_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_payslips
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Payslips"
msgstr "Recibos de nómina"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payslip_run_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Batches"
msgstr "Lotes de recibos de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_tree
msgid "Payslips Count"
msgstr "Número de recibos de nómina"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_payslips_previous_contract
msgid "Payslips Generated On Previous Contract"
msgstr "Recibos generados en el contrato anterior"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_payslip_action_view_to_pay
msgid "Payslips To Pay"
msgstr "Recibos de nómina por pagar"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_payslips_negative_net
msgid "Payslips With Negative NET"
msgstr "Recibos de nómina con neto negativo"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr "Recibos de nómina por empleados"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_filename
msgid "Pdf Filename"
msgstr "Nombre del archivo PDF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__pdf_to_generate
msgid "Pdf To Generate"
msgstr "PDF a generar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_index__percentage
msgid "Percentage"
msgstr "Porcentaje"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__percentage
msgid "Percentage (%)"
msgstr "Porcentaje (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "Percentage based on"
msgstr "Porcentaje basado en"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Period"
msgstr "Periodo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__period_start
msgid "Period Start"
msgstr "Inicio del periodo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__period_stop
msgid "Period Stop"
msgstr "Detener periodo"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__planning
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__planning
msgid "Planning"
msgstr "Planeación"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_employee_declaration.py:0
msgid "Please select the declarations for which you want to generate a PDF."
msgstr "Seleccione las declaraciones para las que desea generar un PDF."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_mixin_form_view
msgid "Populate"
msgstr "Poblar"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr "Registre los recibos de nómina en su contabilidad"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Previous Negative Payslip to Report"
msgstr "Recibo de nómina negativo anterior para reportar"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_tree
msgid "Print"
msgstr "Imprimir"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr "Impuesto profesional"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Prorated end-of-year bonus"
msgstr "Bono de fin de año prorrateado"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr "Fondo de previsión"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__evaluation_code
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__amount_python_compute
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__amount_select__code
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Python Code"
msgstr "Código Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_python
msgid "Python Condition"
msgstr "Condición Python"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__python
msgid "Python Expression"
msgstr "Expresión Python"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__parameter_value
msgid "Python data structure"
msgstr "Estructura de datos de Python"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__quantity
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__quantity
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Quantity"
msgstr "Cantidad"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
msgid "Quantity/Rate"
msgstr "Cantidad/Tasa"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Quarter %(quarter)s of %(year)s"
msgstr "Trimestre %(quarter)s de %(year)s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__quarterly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__quarterly
msgid "Quarterly"
msgstr "Trimestral"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__queued_for_pdf
msgid "Queued For Pdf"
msgstr "En cola para el PDF"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_employee_declaration__state__pdf_to_generate
msgid "Queued PDF generation"
msgstr "Creación de PDF en pausa"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_rule__condition_select__range
msgid "Range"
msgstr "Intervalo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__condition_range
msgid "Range Based on"
msgstr "Intervalo basado en"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_working_rate__rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Rate"
msgstr "Tasa"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__rate
msgid "Rate (%)"
msgstr "Tasa (%)"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__rating_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__rating_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__rating_ids
msgid "Ratings"
msgstr "Calificaciones"

#. module: hr_payroll
#: model:ir.actions.server,name:hr_payroll.action_hr_payroll_recompute_whole_sheet
msgid "Recompute Whole Sheet"
msgstr "Recalcular la hoja entera"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Recompute the payslip lines only, not the worked days / input lines"
msgstr ""
"Recalcular solo las líneas de recibo de nómina, no los días trabajados/las "
"líneas de entrada"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "Recorded a new payment of %s."
msgstr "Registró un nuevo pago de %s."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__number
msgid "Reference"
msgstr "Referencia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__reference_month
msgid "Reference Month"
msgstr "Mes de referencia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_mixin__reference_year
msgid "Reference Year"
msgstr "Año de referencia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Refund"
msgstr "Reembolso"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Refund: %(payslip)s"
msgstr "Reembolsar: %(payslip)s"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Refunds"
msgstr "Reembolsos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__registration_number
msgid "Registration Number of the Employee"
msgstr "Número de registro del empleado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__default_struct_id
msgid "Regular Pay Structure"
msgstr "Estructura de pago regular"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__1
msgid "Regular Working Day"
msgstr "Día laborable normal"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_reimbursement_salary_rule
msgid "Reimbursement"
msgstr "Reembolso"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
msgid "Related Payslips"
msgstr "Recibos de nómina relacionados"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining Amount"
msgstr "Importe restante"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_remaining_amount
msgid "Remaining amount must be positive."
msgstr "El importe restante debe ser positivo."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__remaining_amount
msgid "Remaining amount to be paid."
msgstr "El importe restante a pagar."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Remove \"Conflicting\" filter"
msgstr "Quitar el filtro \"conflictivo\""

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Report Date"
msgstr "Fecha del reporte"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_report
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit_contract
msgid "Reporting"
msgstr "Reportes"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Resolve Conflicts"
msgstr "Resolver conflictos"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_resource_calendar
msgid "Resource Working Time"
msgstr "Tiempo de trabajo de recursos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__activity_user_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Round Type"
msgstr "Tipo de redondeo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__round_days
msgid "Rounding"
msgstr "Redondeo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__salary_rule_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__salary_rule_id
msgid "Rule"
msgstr "Regla"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_rule_category
msgid "Rule Categories"
msgstr "Categorías de reglas"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "Rule Name"
msgstr "Nombre de la regla"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter_value__rule_parameter_id
msgid "Rule Parameter"
msgstr "Parámetro de regla"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_parameter
msgid "Rule Parameters"
msgstr "Parámetros de regla"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_action_hr_salary_rule_form
msgid "Rules"
msgstr "Reglas"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_salary_attachment__state__open
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Running"
msgstr "En proceso "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__sim_card
msgid "SIM Card Copy"
msgstr "Copia de la tarjeta SIM"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__message_has_sms_error
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error en el envío del SMS"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_salary_configuration
msgid "Salary"
msgstr "Salario"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_contract.py:0
#: code:addons/hr_payroll/report/hr_contract_history.py:0
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_attachment_new
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action
#: model:ir.actions.act_window,name:hr_payroll.hr_salary_attachment_action_view_employee
#: model:ir.model,name:hr_payroll.model_hr_salary_attachment
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_form
msgid "Salary Attachment"
msgstr "Deducción salarial"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__salary_attachments_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__salary_attachment_count
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_count
msgid "Salary Attachment Count"
msgstr "Número de deducciones salariales"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_count
msgid "Salary Attachment count"
msgstr "Número de deducciones salariales"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__salary_attachment_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__salary_attachment_ids
#: model:ir.ui.menu,name:hr_payroll.hr_menu_salary_attachments
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Attachments"
msgstr "Deducciones salariales"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_form
msgid "Salary Categories"
msgstr "Categorías salariales"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Salary Computation"
msgstr "Cálculo del salario"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Regla salarial"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_category_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_salary_rule_category_filter
msgid "Salary Rule Categories"
msgstr "Categorías de reglas salariales"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Salary Rule Category"
msgstr "Categoría de regla salarial"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Salary Rule Parameter"
msgstr "Parámetro de regla salarial"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_rule_parameter_value
msgid "Salary Rule Parameter Value"
msgstr "Valor de parámetro de la regla salarial"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_rule_parameter_action
msgid "Salary Rule Parameters"
msgstr "Parámetros de la regla salarial"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__rule_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_list
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Rules"
msgstr "Reglas salariales"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Salary Slip"
msgstr "Recibo de salario"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_tree_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Salary Structure"
msgstr "Estructura salarial"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_structure_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_employee__structure_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_employees__structure_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_tree_inherit
msgid "Salary Structure Type"
msgstr "Tipo de estructura salarial"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_from_type
#: model:ir.actions.act_window,name:hr_payroll.action_view_hr_payroll_structure_list_form
msgid "Salary Structures"
msgstr "Estructuras salariales"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__schedule_pay
msgid "Schedule Pay"
msgstr "Programar pago"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
msgid "Search Declarations"
msgstr "Buscar declaraciones"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_view_search
msgid "Search Headcount"
msgstr "Buscar en la plantilla laboral"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_search
msgid "Search Payroll Dashboard Warning"
msgstr "Advertencia en búsqueda de recibos de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
msgid "Search Payslip Batches"
msgstr "Buscar lotes de recibos de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_line_view_search_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_line_filter
msgid "Search Payslip Lines"
msgstr "Buscar líneas de recibos de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Search Payslips"
msgstr "Buscar recibos de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Search Salary Attachment"
msgstr "Buscar deducción salarial"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_rule_filter
msgid "Search Salary Rule"
msgstr "Buscar regla salarial"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_search
msgid "Search Structure Type"
msgstr "Buscar tipo de estructura"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__semi-annually
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-annually
msgid "Semi-annually"
msgstr "Semestral"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__semi-monthly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__semi-monthly
msgid "Semi-monthly"
msgstr "Quincenal"

#. module: hr_payroll
#: model:mail.template,description:hr_payroll.mail_template_new_payslip
msgid "Sent to employee to notify them about their new payslip"
msgstr "Se envía al empleado para notificarle sobre su nuevo recibo de nómina"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_export_mixin__reference_month__9
#: model:ir.model.fields.selection,name:hr_payroll.selection__res_company__ytd_reset_month__9
msgid "September"
msgstr "Septiembre"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__sequence
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific department if you wish to select all the employees from this "
"department (and subdepartments) at once."
msgstr ""
"Establece un departamento específico si desea seleccionar todos los "
"empleados de este departamento (y subdepartamentos) a la vez."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific job if you wish to select all the employees from this job at "
"once."
msgstr ""
"Configure un trabajo especifico si quiere seleccionar a todos los empleados "
"de este puesto de trabajo al mismo tiempo."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_by_employees
msgid ""
"Set a specific structure type if you wish to select all the employees from "
"this structure type at once."
msgstr ""
"Configurar un tipo de estructura específico si desea seleccionar a todos los"
" empleados de este tipo de estructura al mismo tiempo."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid "Set the Contract as <strong><q>Running</q></strong>."
msgstr "Establecer el contrato como <strong><q>Activo</q></strong>."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Set to Draft"
msgstr "Establecer como borrador"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_configuration
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_global_settings
msgid "Settings"
msgstr "Ajustes"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Slips to Confirm"
msgstr "Recibos por confirmar"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "Some employees (%s) don't have a bank account."
msgstr "Algunos empleados (%s) no tienen una cuenta bancaria."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid ""
"Some work entries are in conflict. Please resolve the conflicts before "
"exporting."
msgstr ""
"Algunas entradas de trabajo están en conflicto. Resuélvalos antes de "
"exportarlos."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "Some work entries could not be validated."
msgstr "No se pudieron validar algunas entradas de trabajo."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__standard_calendar_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__standard_calendar_id
msgid "Standard Calendar"
msgstr "Calendario estándar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__date_from
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__date_start
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_employee_declaration__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_employee_declaration_view_search
msgid "State"
msgstr "Estado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__state
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__state
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_filter
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Status"
msgstr "Estado"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_state
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado según las actividades\n"
"Vencida: ya pasó la fecha límite\n"
"Hoy: hoy es la fecha de la actividad\n"
"Planeada: futuras actividades."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__struct_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "Structure"
msgstr "Estructura"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Name"
msgstr "Nombre de estructura"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__name
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_tree
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payroll_structure_filter
msgid "Structure Type"
msgstr "Tipo de estructura"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_type_count
msgid "Structure Type Count"
msgstr "Número de tipos de estructura"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.action_hr_payroll_structure_type
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_type
msgid "Structure Types"
msgstr "Tipos de estructura"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure_type__struct_ids
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_structure_view
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "Structures"
msgstr "Estructuras"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Sum Worked Hours"
msgstr "Suma de horas trabajadas"

#. module: hr_payroll
#: model:hr.salary.rule,name:hr_payroll.hr_salary_rule_sum_alw_category
msgid "Sum of Allowance category"
msgstr "Suma de la categoría de subsidio"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Sum of Days"
msgstr "Número de días"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_dashboard_warning__color
msgid "Tag color. No color means black."
msgstr "Color de la etiqueta. Si no hay color, es negro."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:hr_payroll.default_gross_salary_rule
#: model:hr.salary.rule.category,name:hr_payroll.GROSS
msgid "Taxable Salary"
msgstr "Sueldo imponible"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__report_id
msgid "Template"
msgstr "Plantilla"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__country_code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"El código ISO del país en dos caracteres.\n"
"Puede utilizar este campo para realizar una búsqueda rápida."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_form_inherit
msgid ""
"The Standard Calendar is the calendar used by the people working at a 100% "
"rate. It's used here to compute your part-time percentage."
msgstr ""
"El calendario estándar es el que utilizan las personas que trabajan con una "
"tasa del 100%. Se utiliza aquí para calcular su porcentaje de medio tiempo."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid ""
"The Work Entry checked as Unforeseen Absence will be counted in absenteeism "
"at work report."
msgstr ""
"La entrada de trabajo marcada como ausencia imprevista se contabilizará en "
"el reporte de ausencias laborales."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_company.py:0
msgid ""
"The YTD reset day must be a valid day of the month : since the current month"
" is %(month)s, it should be between 1 and %(day)s."
msgstr ""
"El día de de reinicio de EHF debe ser un día válido del mes: como el mes en "
"curso es %(month)s, debe ser entre 1 y %(day)s."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_edit_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""
"Se puede usar el código de las reglas salariales como referencia en el "
"cálculo de otras reglas. En ese caso, se distingue entre mayúsculas y "
"minúsculas."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__code
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
msgid "The code that can be used in the salary rules"
msgstr "Se puede usar el código en las reglas salariales"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_select
msgid "The computation method for the rule amount."
msgstr "El método de cálculo para el importe de la regla."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input__contract_id
msgid "The contract this input should be applied to"
msgstr "El contrato al que se debe aplicar esta entrada"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_worked_days__contract_id
msgid "The contract this worked days should be applied to"
msgstr "El contrato al que se debe aplicar estos días trabajados"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__paid_date
msgid "The date on which the payment is made to the employee."
msgstr "La fecha en que se realiza el pago al empleado."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The duration of the payslip is not accurate according to the structure type."
msgstr ""
"La duración de un recibo de nómina no es exacta según el tipo de estructura."
" "

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The following employees have a contract outside of the payslip period:\n"
"%s"
msgstr ""
"Los siguientes empleados tienen un contrato fuera del periodo de nómina:\n"
"%s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"The following values are not valid:\n"
"%s"
msgstr ""
"Los siguientes valores no son válidos:\n"
"%s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr "El importe máximo aplicado a esta regla."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr "El importe mínimo aplicado a esta regla."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid ""
"The net amount will be recovered from the first positive remuneration "
"established after this."
msgstr ""
"El importe neto se recuperará de la primera remuneración positiva "
"establecida después de esto."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The payslips should be in Draft or Waiting state."
msgstr "El recibo de nómina debe estar en estado de borrador o de espera."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/views/add_payslips_hook.js:0
msgid "The payslips(s) are now added to the batch"
msgstr "Los recibos de nómina ya se agregaron al lote"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The period selected does not match the contract validity period."
msgstr ""
"El periodo seleccionado no coincide con el periodo de validez del contrato. "

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "The selected payslips should be linked to the same batch"
msgstr ""
"Los recibos de nómina seleccionados deben estar vinculados al mismo lote"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_payroll_headcount_date_range
msgid "The start date must be anterior to the end date."
msgstr "La fecha de inicio debe ser anterior a la fecha de finalización."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__time_credit_type_id
msgid ""
"The work entry type used when generating work entries to fit full time "
"working schedule."
msgstr ""
"El tipo de entrada de trabajo que se utiliza al generar entradas de trabajo "
"que se ajustan al horario de trabajo."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "The work entry won’t grant any money to employee in payslip."
msgstr ""
"La entrada de trabajo no otorgará dinero al empleado en el recibo de nómina."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "There is no declaration to generate for the given period"
msgstr "No hay ninguna declaración que generar para el periodo proporcionado"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"There is no valid payslip (done and net wage > 0) to generate the file."
msgstr ""
"No hay un recibo de nómina válido (hecho y salario neto > 0) para generar el"
" archivo."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid "There should be at least one payslip to generate the file."
msgstr "Debería haber al menos un recibo de nómina para generar el archivo."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"There's no contract set on payslip %(payslip)s for %(employee)s. Check that "
"there is at least a contract set on the employee form."
msgstr ""
"No hay un contrato establecido en el recibo de nómina %(payslip)s para "
"%(employee)s. Compruebe que por lo menos haya un contrato establecido en el "
"formulario de empleado."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This action is forbidden on validated payslips."
msgstr "Esta acción está prohibida en recibos de nómina validados."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This action is restricted to payroll managers only."
msgstr "Esta acción está restringida a solo gerentes de nómina."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter__code
#: model:ir.model.fields,help:hr_payroll.field_hr_rule_parameter_value__code
msgid "This code is used in salary rules to refer to this parameter."
msgstr ""
"Este código se usa en las reglas salariales para referirse a este parámetro."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_input_type__struct_ids
msgid ""
"This input will be only available in those structure. If empty, it will be "
"available in all payslip."
msgstr ""
"Esta entrada solo estará disponible en esas estructuras. Si está vacía, "
"estará disponible en todos los recibos de nómina."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_history__time_credit
msgid "This is a credit time contract."
msgstr "Este es un contrato de tiempo de crédito."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry__is_credit_time
msgid "This is a credit time work entry."
msgstr "Esta es una entrada de trabajo de tiempo de crédito."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "This payslip can be erroneous :"
msgstr "Este recibo de nómina puede estar incorrecto: "

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_edit_payslip_lines_wizard.py:0
msgid "This payslip has been manually edited by %s."
msgstr "Este recibo de nómina fue editado manualmente por %s."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "This payslip is not validated. This is not a legal document."
msgstr "Este recibo de nómina no es válido. No es un documento legal."

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.payroll_report_action
msgid "This report performs analysis on your payslip."
msgstr "Este reporte realiza un análisis en su recibo de nómina."

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.hr_work_entry_report_action
msgid "This report performs analysis on your work entries."
msgstr "Este reporte realiza un análisis de sus entradas de trabajo."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic,"
" but you can also use categories code fields in lowercase as a variable "
"names (hra, ma, lta, etc.) and the variable basic."
msgstr ""
"Se usará para calcular el valor de los campos %; en general se utiliza lo "
"básico, pero también puede usar campos de código de categorías en minúscula "
"como nombres de variables (hra, ma, lta, etc.) y las variables básicas."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "Time intervals to look for:%s"
msgstr "Intervalos de tiempo que buscar:%s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__date_to
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__date_to
msgid "To"
msgstr "Hasta"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Compute"
msgstr "Por calcular"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_filter
msgid "To Confirm"
msgstr "Por confirmar"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_employee_payslips_to_pay
msgid "To Pay"
msgstr "Por pagar"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "To pay on"
msgstr "Pagar el"

#. module: hr_payroll
#: model_terms:ir.actions.act_window,help:hr_payroll.action_contribution_registers
msgid "To see something in this report, compute a payslip."
msgstr "Para ver algo en este reporte, calcule un recibo de nómina."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__total
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__total
#: model_terms:ir.ui.view,arch_db:hr_payroll.contribution_register
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "Total"
msgstr "Total"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__total_amount
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Amount"
msgstr "Importe total"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Days"
msgstr "Días de trabajo totales"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Total Working Hours"
msgstr "Número de horas trabajadas"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_salary_attachment_check_total_amount
msgid ""
"Total amount must be strictly positive and greater than or equal to the "
"payslip amount."
msgstr ""
"El monto total debe ser estrictamente positivo y superior o igual al importe"
" del recibo de nómina."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__total_amount
msgid "Total amount to be paid."
msgstr "Importe total que debe pagarse."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__sum_worked_hours
msgid "Total hours of attendance and time off (paid or not)"
msgstr "Total de horas de asistencia y de tiempo personal (pagado o no)"

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter__unique
msgid "Two rule parameters cannot have the same code."
msgstr "Dos parámetros de regla no pueden tener el mismo código."

#. module: hr_payroll
#: model:ir.model.constraint,message:hr_payroll.constraint_hr_rule_parameter_value__unique
msgid "Two rules with the same code cannot start the same day"
msgstr "Dos reglas con el mismo código no pueden comenzar el mismo día"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__struct_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_input__input_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__other_input_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_attachment_view_search
msgid "Type"
msgstr "Tipo"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__activity_exception_decoration
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de la actividad de excepción registrada."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__is_unforeseen
msgid "Unforeseen Absence"
msgstr "Ausencia imprevista"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Unknown State"
msgstr "Estado desconocido"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_hr_work_entry_type_view_form_inherit
msgid "Unpaid"
msgstr "Sin pagar"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_type__3
msgid "Unpaid Time Off"
msgstr "Tiempo personal sin pagar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__unpaid_work_entry_type_ids
msgid "Unpaid Work Entry Type"
msgstr "Tipo de entrada de trabajo sin pagar"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "Unpaid Work Entry Types"
msgstr "Tipo de entradas de trabajo sin pagar"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__unpaid_structure_ids
msgid "Unpaid in Structures Types"
msgstr "Sin pagar en tipos de estructura"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payment_report_wizard.py:0
msgid ""
"Untrusted bank account for the following employees:\n"
"%s"
msgstr ""
"Cuenta bancaria no confiable para los siguientes empleados:\n"
"%s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_type__round_days_type__up
msgid "Up"
msgstr "Arriba"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__use_worked_day_lines
msgid "Use Worked Day Lines"
msgstr "Usar líneas de día trabajado"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__sequence
msgid "Use to arrange calculation sequence"
msgstr "Se utiliza para organizar la secuencia de cálculo"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr "Se utiliza para mostrar la regla salarial en el recibo de nómina."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__appears_on_employee_cost_dashboard
msgid "Used to display the value in the employer cost dashboard."
msgstr ""
"Se utiliza para mostrar el valor en el tablero de costos del empleador."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_res_users
msgid "User"
msgstr "Usuario"

#. module: hr_payroll
#: model:res.groups,comment:hr_payroll.group_hr_payroll_user
msgid "User can manage all contracts, work entries and create payslips."
msgstr ""
"El usuario puede gestionar todos los contratos y entradas de trabajo y crear"
" recibos de nómina."

#. module: hr_payroll
#: model:res.groups,comment:hr_payroll.group_hr_payroll_manager
msgid "User have full access on the application."
msgstr "El usuario tiene acceso total en la aplicación."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "Validate"
msgstr "Validar "

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
msgid "Validate Edition"
msgstr "Edición validada"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__state__validated
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Validated"
msgstr "Validado"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_rule_parameter__parameter_version_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_rule_parameter_view_form
msgid "Versions"
msgstr "Versiones"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_employee_cost_dashboard
msgid "View on Employer Cost Dashboard"
msgstr "Ver en el tablero de costos del empleador"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_rule__appears_on_payroll_report
msgid "View on Payroll Reporting"
msgstr "Ver en el reporte de nómina"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_contract_view_kanban
msgid "Wage :"
msgstr "Salario : "

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__wage_on_payroll
msgid "Wage On Payroll"
msgstr "Salario en la nómina"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__wage_type
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__wage_type
msgid "Wage Type"
msgstr "Tipo de salario"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
msgid "Wage indexed by %(percentage).2f%% on %(date)s"
msgstr "Salario indexado por %(percentage).2f%% el %(date)s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payslip__state__verify
msgid "Waiting"
msgstr "En espera"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_dashboard_warning__color
msgid "Warning Color"
msgstr "Color de advertencia"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__warning_message
msgid "Warning Message"
msgstr "Mensaje de advertencia"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "Warning, a similar attachment has been found."
msgstr "Advertencia: se encontró un archivo adjunto similar."

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/components/dashboard/action_box/action_box.xml:0
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_dashboard_warning
msgid "Warnings"
msgstr "Advertencias"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry.py:0
msgid ""
"Watch out for gaps in %(employee_name)s's calendar\n"
"\n"
"Please complete the missing work entries of %(employee_name)s:%(time_intervals_str)s \n"
"\n"
"Missing work entries are like the Bermuda Triangle for paychecks. Let's keep your colleague's earnings from vanishing into thin air!"
msgstr ""
"Hay algunos espacios vacíos en en el calendario de %(employee_name)s\n"
"\n"
"Complete las entradas de trabajo restantes de %(employee_name)s: %(time_intervals_str)s \n"
"\n"
"No se podrá realizar el pago si no hay entradas de trabajo registradas."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days_type
msgid "Way of rounding the work entry type."
msgstr "Método de redondeo del tipo de entrada de trabajo."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "We have to improve our Payroll flow with the new Odoo process"
msgstr ""
"Tenemos que mejorar nuestro flujo de nómina con el nuevo proceso de Odoo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_run__website_message_ids
#: model:ir.model.fields,field_description:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website Messages"
msgstr "Mensajes del sitio web"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip_run__website_message_ids
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_attachment__website_message_ids
msgid "Website communication history"
msgstr "Historial de comunicación del sitio web"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Week %(week_number)s of %(year)s"
msgstr "Semana %(week_number)s de %(year)s"

#. module: hr_payroll
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_contract__schedule_pay__weekly
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_structure_type__default_schedule_pay__weekly
msgid "Weekly"
msgstr "Semanalmente"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "Weeks %(week)s and %(week1)s of %(year)s"
msgstr "Semanas %(week)s y %(week1)s de %(year)s"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_work_entry_type__round_days
msgid ""
"When the work entry is displayed in the payslip, the value is rounded "
"accordingly."
msgstr ""
"Cuando la entrada de trabajo se muestra en el recibo de nómina, el valor se "
"redondea en consecuencia."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__calendar_changed
msgid "Whether the previous or next contract has a different schedule or not"
msgstr ""
"Si el contrato anterior o el siguiente tiene un horario diferente o no"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_index__description
msgid ""
"Will be used as the message specifying why the wage on the contract has been"
" modified"
msgstr ""
"Se utilizará como mensaje para especificar por qué se modificó el salario "
"del contrato"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work
msgid "Work Days"
msgstr "Días laborables"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_payroll_work_entries_root
msgid "Work Entries"
msgstr "Entradas de trabajo"

#. module: hr_payroll
#: model:ir.actions.act_window,name:hr_payroll.hr_work_entry_report_action
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_tree
msgid "Work Entries Analysis"
msgstr "Análisis de entradas de trabajo"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_report
msgid "Work Entries Analysis Report"
msgstr "Reporte de análisis de entradas de trabajo"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Work Entries Export"
msgstr "Exportación de las entradas al trabajo"

#. module: hr_payroll
#. odoo-javascript
#: code:addons/hr_payroll/static/src/js/tours/hr_payroll.js:0
msgid ""
"Work Entries are generated for each <strong>time period</strong> defined in "
"the Working Schedule of the Contract."
msgstr ""
"Las entradas de trabajo se generan por cada <strong>periodo de "
"tiempo</strong> que se definió en el horario de trabajo del contrato."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "Work Entries for %(employee)s"
msgstr "Entradas al trabajo de %(employee)s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_export_employee_mixin__work_entry_ids
msgid "Work Entry"
msgstr "Entrada de trabajo"

#. module: hr_payroll
#: model:ir.ui.menu,name:hr_payroll.menu_hr_work_entry_report
msgid "Work Entry Analysis"
msgstr "Análisis de entrada de trabajo"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_export_employee_mixin
msgid "Work Entry Export Employee"
msgstr "Exportar las entradas al trabajo por empleado"

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_work_entry_export_mixin
msgid "Work Entry Export Mixin"
msgstr "MIxin de exportación de las entradas al trabajo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_entry_source
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_source
msgid "Work Entry Source"
msgstr "Origen de la entrada de trabajo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__work_entry_type_id
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_report__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_report_view_search
msgid "Work Entry Type"
msgstr "Tipo de entrada de trabajo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__count_work_hours
msgid "Work Hours"
msgstr "Horas laborables"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "Work Permit Expiration Notice Period"
msgstr "Plazo de notificación de vencimiento del permiso de trabajo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_resource_calendar__work_time_rate
#: model_terms:ir.ui.view,arch_db:hr_payroll.payroll_resource_calendar_view_form
msgid "Work Time Rate"
msgstr "Tasa de tiempo de trabajo"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Work entries may not be generated for the period from %(start)s to %(end)s."
msgstr ""
"Puede que no se generen entradas de trabajo del periodo de%(start)s a "
"%(end)s."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure_type__default_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "Tipo de entrada de trabajo para asistencias regulares."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__work_time_rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract_history__work_time_rate
msgid "Work time rate"
msgstr "Tasa de tiempo de trabajo"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_resource_calendar__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""
"La tasa de tiempo de trabajo versus el horario de trabajo a tiempo completo "
"debe estar entre 0 y 100%."

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_contract__work_time_rate
#: model:ir.model.fields,help:hr_payroll.field_hr_contract_history__work_time_rate
msgid "Work time rate versus full time working schedule."
msgstr "Tiempo de trabajo versus horario de trabajo de tiempo completo."

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_code
msgid "Work type"
msgstr "Tipo de trabajo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_report__work_type
msgid "Work, (un)paid Time Off"
msgstr "Trabajo, tiempo personal (no) pagado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Day"
msgstr "Día trabajado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_edit_payslip_lines_form_wizard
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days"
msgstr "Días trabajados"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_payslip_form
msgid "Worked Days & Inputs"
msgstr "Días trabajados y entradas"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__worked_days_line_ids
msgid "Worked Days Lines"
msgstr "Líneas de días trabajados"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_payroll_structure__use_worked_day_lines
#: model:ir.model.fields,help:hr_payroll.field_hr_payslip__use_worked_day_lines
msgid "Worked days won't be computed/displayed in payslips."
msgstr ""
"No se calcularán o mostrarán los días trabajados en recibos de nómina."

#. module: hr_payroll
#: model:ir.model,name:hr_payroll.model_hr_payroll_headcount_working_rate
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__working_rate_ids
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_headcount_line_search
msgid "Working Rate"
msgstr "Tasa de trabajo"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_contract__resource_calendar_id
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_payroll_report__work_entry_source__calendar
#: model:ir.model.fields.selection,name:hr_payroll.selection__hr_work_entry_report__work_entry_source__calendar
msgid "Working Schedule"
msgstr "Horario de trabajo"

#. module: hr_payroll
#: model:hr.payroll.dashboard.warning,name:hr_payroll.hr_payroll_dashboard_warning_working_schedule_change
msgid "Working Schedule Changes"
msgstr "Cambios en el horario de trabajo"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong percentage base or quantity defined for:"
msgstr "Base porcentual o cantidad errónea definida para:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong python code defined for:"
msgstr "Código Python erróneo definido para:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong python condition defined for:"
msgstr "Condición Python errónea definida para:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong quantity defined for:"
msgstr "Cantidad errónea definida para:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_rule.py:0
msgid "Wrong range condition defined for:"
msgstr "Condición de rango errónea definida para:"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_rule_parameter.py:0
msgid ""
"Wrong rule parameter value for %(rule_parameter_name)s at date %(date)s.\n"
"%(error)s"
msgstr ""
"Valor de parámetro de regla incorrecto para %(rule_parameter_name)s en la fecha %(date)s.\n"
"%(error)s"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid ""
"Wrong warning computation code defined for:\n"
"- Warning: %(warning)s\n"
"- Error: %(error)s"
msgstr ""
"Código de cálculo de advertencia incorrecto definido para:\n"
"- Advertencia: %(warning)s\n"
"- Error: %(error)s"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_worked_days_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_line__ytd
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip_worked_days__ytd
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "YTD"
msgstr "EHF"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.res_config_settings_view_form
msgid "YTD Reset Date"
msgstr "Fecha de reinicio EHF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_company__ytd_reset_day
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__ytd_reset_day
msgid "YTD Reset Day of the month"
msgstr "Día del mes en el que se reinicia EHF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_res_company__ytd_reset_month
#: model:ir.model.fields,field_description:hr_payroll.field_res_config_settings__ytd_reset_month
msgid "YTD Reset Month"
msgstr "Mes de reinicio EHF"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_declaration_mixin__year
msgid "Year"
msgstr "Año"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_edit_payslip_lines_wizard__ytd_computation
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_structure__ytd_computation
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payslip__ytd_computation
msgid "Year to Date Computation"
msgstr "Cálculo del día hasta la fecha"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/res_users.py:0
msgid ""
"You are receiving this message because you are the HR Responsible of this "
"employee."
msgstr ""
"Recibió este mensaje porque usted es el responsable de RR.HH. para este "
"empleado"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
msgid ""
"You can't delete a batch with payslips if they are not draft or cancelled."
msgstr ""
"No puede borrar un lote de recibos de nómina si no están en borrador o "
"canceladas."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You can't validate a cancelled payslip."
msgstr "No puede validar un recibo de nómina cancelado."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_input_type.py:0
msgid ""
"You cannot delete %s as it is used in another module but you can archive it "
"instead."
msgstr ""
"No puede eliminar %s ya que se utiliza en otro módulo pero puede archivarlo."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You cannot delete a payslip which is not draft or cancelled!"
msgstr ""
"No puede eliminar un recibo de nómina que no esté en estado de borrador o "
"cancelado."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "You cannot delete a running salary attachment!"
msgstr "No puede eliminar una deducción salarial activa."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_salary_attachment.py:0
msgid "You cannot record a payment on multi employees attachments."
msgstr "No puede registrar un pago en archivos adjuntos de multi empleados."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_run.py:0
msgid ""
"You cannot reset a batch to draft if some of the payslips have already been "
"paid."
msgstr ""
"No puede restablecer un lote a borrador si ya se pagaron algunos recibos de "
"nómina."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip.py:0
msgid "You cannot validate a payslip on which the contract is cancelled"
msgstr ""
"No puede validar un recibo de nómina en el que el contrato se canceló."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_index_wizard.py:0
msgid ""
"You have selected non running contracts, if you really need to index them, "
"please do it by hand"
msgstr ""
"Seleccionó contratos no vigentes, si realmente necesita indexarlos, hágalo a"
" mano"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_work_entry_export_mixin.py:0
msgid "You must be logged in a %(country_code)s company to use this feature"
msgstr ""
"Debe iniciar sesión en una empresa %(country_code)s para usar esta función"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_declaration_mixin.py:0
msgid "You must be logged in a %s company to use this feature"
msgstr "Debe iniciar sesión en una empresa de %s para utilizar esta función"

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/wizard/hr_payroll_payslips_by_employees.py:0
msgid "You must select employee(s) to generate payslip(s)."
msgstr ""
"Debe seleccionar un empleado o empleados para generar los recibos de nómina."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payslip_line.py:0
msgid "You must set a contract to create a payslip line."
msgstr "Debe establecer un contrato para crear una línea de recibo de nómina."

#. module: hr_payroll
#. odoo-python
#: code:addons/hr_payroll/models/hr_payroll_structure_type.py:0
msgid "You should also be logged into a company in %s to set this country."
msgstr ""
"También debe iniciar sesión en una empresa en %s para configurar este país."

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_work_entry_export_employee_mixin_list_view
msgid "contracts"
msgstr "contratos"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_work_entry_type__current_companies_country_codes
msgid "country codes"
msgstr "códigos de país"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_run_form
msgid "e.g. April 2021"
msgstr "Por ejemplo, abril del 2021"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payslip_input_type_view_form
msgid "e.g. Child Support"
msgstr "Por ejemplo, pensión alimenticia"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_structure_type_view_form
msgid "e.g. Employee"
msgstr "Por ejemplo, empleado"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_dashboard_warning_view_form
msgid "e.g. Employee Without Contracts"
msgstr "Por ejemplo, Empleado sin contratos"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net"
msgstr "Por ejemplo, neto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_salary_rule_form
msgid "e.g. Net Salary"
msgstr "Por ejemplo, salario neto"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.view_hr_employee_grade_form
msgid "e.g. Regular Pay"
msgstr "Por ejemplo, pago regular"

#. module: hr_payroll
#: model:ir.model.fields,field_description:hr_payroll.field_hr_payroll_headcount_line__headcount_id
msgid "headcount_id"
msgstr "headcount_id"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.hr_payroll_note_demo_content
msgid "https://www.odoo.com/fr_FR/slides/slide/manage-payroll-1002"
msgstr "https://www.odoo.com/fr_FR/slides/slide/manage-payroll-1002"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "of"
msgstr "de"

#. module: hr_payroll
#: model:ir.model.fields,help:hr_payroll.field_hr_salary_rule__amount_percentage_base
msgid "result will be affected to a variable"
msgstr "el resultado afectará a una variable"

#. module: hr_payroll
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_light_payslip
#: model_terms:ir.ui.view,arch_db:hr_payroll.report_payslip
msgid "xxxxxxxxxxxx"
msgstr "xxxxxxxxxxxx"

#. module: hr_payroll
#: model:mail.template,subject:hr_payroll.mail_template_new_payslip
msgid "{{ object.employee_id.name }}, a new payslip is available for you"
msgstr ""
"{{ object.employee_id.name }}, hay un nuevo recibo de nómina disponible para"
" usted"
