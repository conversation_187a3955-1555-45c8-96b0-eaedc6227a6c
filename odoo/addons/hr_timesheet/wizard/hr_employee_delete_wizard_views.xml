<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="hr_employee_delete_wizard_form" model="ir.ui.view">
            <field name="name">Delete Employee</field>
            <field name="model">hr.employee.delete.wizard</field>
            <field name="arch" type="xml">
                <form string="Delete Employee">
                    <field name="has_timesheet" invisible="1"/>
                    <field name="has_active_employee" invisible="1"/>
                    <span invisible="not has_timesheet">
                        You cannot delete employees who have timesheets.
                        <span invisible="not has_active_employee">
                            You can either archive these employees or first delete all of their timesheets.
                        </span>
                        <span invisible="has_active_employee" groups="hr_timesheet.group_hr_timesheet_approver">
                            Please first delete all of their timesheets.
                        </span>
                    </span>
                    <span invisible="has_timesheet">
                        Are you sure you want to delete these employees?
                    </span>
                    <footer invisible="not has_timesheet">
                        <button string="Archive Employees" type="object" name="action_archive" class="btn btn-primary"
                            invisible="not has_active_employee" data-hotkey="q"/>
                        <button string="See Timesheets" type="object" name="action_open_timesheets" class="btn btn-primary" groups="hr_timesheet.group_hr_timesheet_approver"
                            invisible="has_active_employee" data-hotkey="w"/>
                        <button string="See Timesheets" type="object" name="action_open_timesheets" class="btn btn-secondary" groups="hr_timesheet.group_hr_timesheet_approver"
                            invisible="not has_active_employee" data-hotkey="w"/>
                        <button string="Discard" special="cancel" data-hotkey="x"/>
                    </footer>
                    <footer invisible="has_timesheet">
                        <button string="Ok" type="object" name="action_confirm_delete" class="btn btn-primary" data-hotkey="q"/>
                        <button string="Discard" special="cancel" data-hotkey="x"/>
                    </footer>
                </form>
            </field>
        </record>
    </data>
</odoo>
