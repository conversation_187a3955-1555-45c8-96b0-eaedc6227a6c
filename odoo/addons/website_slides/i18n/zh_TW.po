# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_engaged_count
msgid "# Active Attendees"
msgstr "活躍參加者數目"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "# Attendees"
msgstr "參加者數目"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "# Completed"
msgstr "已完成數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_completed_count
msgid "# Completed Attendees"
msgstr "已完成參加者數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completed_slides_count
msgid "# Completed Contents"
msgstr "已完成內容數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_count
msgid "# Enrolled Attendees"
msgstr "已報名參加者數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_all_count
msgid "# Enrolled or Invited Attendees"
msgstr "已報名或獲邀參加者數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__members_invited_count
msgid "# Invited Attendees"
msgstr "獲邀請參加者數目"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Likes"
msgstr "讚好數目"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "# Questions"
msgstr "問題數目"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Quizz Attempts"
msgstr "測驗嘗試次數"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "# Total Attempts"
msgstr "總嘗試次數"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_views
msgid "# Total Views"
msgstr "總瀏覽次數"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__count_views
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "# Views"
msgstr "瀏覽次數"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_count
msgid "# of Embeds"
msgstr "嵌入數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__public_views
msgid "# of Public Views"
msgstr "公眾瀏覽次數"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_views
msgid "# of Website Views"
msgstr "網站瀏覽次數"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__completion
msgid "% Completed Contents"
msgstr "% 已完成內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "'. Showing results for '"
msgstr "'.顯示結果 '"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_slides_list.js:0
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
msgid "(empty)"
msgstr "（空白）"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ". This way, they will be secured."
msgstr ".這樣，他們將得到保護。"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_5
msgid "3 Main Methodologies"
msgstr "3 主要方法"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "<b>%s</b> is requesting access to this course."
msgstr "<b>%s</b>正在請求存取此課程。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<b>(empty)</b>"
msgstr "<b>(空白)</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<b>Order by</b>"
msgstr "<b>按序排列</b>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"<b>Save & Publish</b> your lesson to make it available to your attendees."
msgstr "<b>儲存並發佈</b>你的課程，以供你的參加者使用。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "<b>Save</b> your question."
msgstr "<b>儲存</b>您的問題。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<b>Uncategorized</b>"
msgstr "<b>未分類</b>"

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_enroll
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been enrolled to a new course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        你好！<br/><br/>\n"
"        你已報名參加一門新課程： <t t-out=\"object.channel_id.name or ''\">園藝基礎</t>。\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_slide_channel_invite
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Hello<br/><br/>\n"
"        You have been invited to check out this course: <t t-out=\"object.channel_id.name or ''\">Basics of Gardening</t>.\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        你好！<br/><br/>\n"
"        你獲邀請查看此課程： <t t-out=\"object.channel_id.name or ''\">園藝基礎</t>。\n"
"    </p>\n"
"</div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_completed
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\">Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,</p><br/>\n"
"                        <p><b>Congratulations!</b></p>\n"
"                        <p>You've completed the course <b t-out=\"object.channel_id.name or ''\">Basics of Gardening</b></p>\n"
"                        <p>Check out the other available courses.</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                Explore courses\n"
"                            </a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"object.channel_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        <p style=\"margin: 0px;\"><t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t> 你好！</p><br/>\n"
"                        <p><b>恭喜！</b></p>\n"
"                        <p>你已完成以下課程： <b t-out=\"object.channel_id.name or ''\">園藝基礎</b></p>\n"
"                        <p>看看其他可以報名的課程。</p><br/>\n"
"\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a href=\"/slides/all\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                探索課程\n"
"                            </a>\n"
"                        </div>\n"
"                        希望你喜歡這獨家內容！\n"
"                        <t t-if=\"object.channel_id.user_id.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"object.channel_id.user_id.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    </div>\n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.mail_template_channel_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <strong t-out=\"object.name or ''\">document</strong> with you!\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.channel/{{ object.id }}/image_256\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px;                                 text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                View <strong t-out=\"object.name or ''\">Document</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        你好！<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> 向你分享了 <strong t-out=\"object.name or ''\">文件</strong>！\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.channel/{{ object.id }}/image_256\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px;                                 text-decoration: none; color: #fff; border-radius: 5px;\">\n"
"                                檢視 <strong t-out=\"object.name or ''\">文件</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_shared
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> shared the <t t-out=\"object.slide_category or ''\">document</t> <strong t-out=\"object.name or ''\">Trees</strong> with you!\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View <strong t-out=\"object.name or ''\">Trees</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        你好！<br/><br/>\n"
"                        <t t-out=\"user.name or ''\">Mitchell Admin</t> 向你分享了 <t t-out=\"object.slide_category or ''\">文件</t>「<strong t-out=\"object.name or ''\">樹木</strong>」！\n"
"                        </p><div style=\"margin: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">檢視 <strong t-out=\"object.name or ''\">樹木</strong></a>\n"
"                        </div>\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "

#. module: website_slides
#: model:mail.template,body_html:website_slides.slide_template_published
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        Hello<br/><br/>\n"
"                        There is something new in the course <strong t-out=\"object.channel_id.name or ''\">Trees, Wood and Gardens</strong> you are following:<br/><br/>\n"
"                        </p><center><strong t-out=\"object.name or ''\">Trees</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">View content</a>\n"
"                        </div>\n"
"                        Enjoy this exclusive content!\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"                        你好！<br/><br/>\n"
"                        你關注的課程「<strong t-out=\"object.channel_id.name or ''\">樹木、木材與花園</strong>」已加入新內容：<br/><br/>\n"
"                        </p><center><strong t-out=\"object.name or ''\">樹木</strong></center>\n"
"                        <t t-if=\"object.image_1024\">\n"
"                            <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                                <a t-att-href=\"object.website_share_url\">\n"
"                                <img t-att-alt=\"object.name\" t-attf-src=\"{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024\" style=\"height:auto; width:150px; margin: 16px;\"/>\n"
"                            </a>\n"
"                        </div>\n"
"                        </t>\n"
"                        <div style=\"padding: 16px 8px 16px 8px; text-align: center;\">\n"
"                            <a t-att-href=\"object.website_share_url\" style=\"background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;\">查看內容</a>\n"
"                        </div>\n"
"                        希望你喜歡這獨家內容！\n"
"                        <t t-if=\"user.signature\" data-o-mail-quote-container=\"1\">\n"
"                            <br/>\n"
"                            <t t-out=\"user.signature or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"                        </t>\n"
"                    \n"
"                </div>\n"
"            "

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"fa fa-arrow-right\"/> Start Learning"
msgstr "<i class=\"fa fa-arrow-right\"/> 開始學習"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-bar-chart\"/> Statistics"
msgstr "<i class=\"fa fa-bar-chart\"/> 統計資料"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-bars\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Lessons</span>"
msgstr ""
"<i class=\"fa fa-bars\"/> <span class=\"d-none d-md-inline-block "
"ms-1\">課節</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "<i class=\"fa fa-check me-1\"/>Completed"
msgstr "<i class=\"fa fa-check me-1\"/> 完成"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "<i class=\"fa fa-check\"/> Completed"
msgstr "<i class=\"fa fa-check\"/> 完成"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "<i class=\"fa fa-circle-o-notch fa-spin me-2\"/><b>Loading...</b>"
msgstr "<i class=\"fa fa-circle-o-notch fa-spin me-2\"/><b>載入中⋯</b>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "<i class=\"fa fa-clipboard\"/> Copy Embed Code"
msgstr "<i class=\"fa fa-clipboard\"/> 複製嵌入程式碼"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "<i class=\"fa fa-clipboard\"/> Copy Link"
msgstr "<i class=\"fa fa-clipboard\"/> 複製連結"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-clock-o me-2\" aria-label=\"Duration\" role=\"img\" "
"title=\"Duration\"/>"
msgstr "<i class=\"fa fa-clock-o me-2\" aria-label=\"時長\" role=\"img\" title=\"時長\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-cloud-upload me-1\"/>Add Content"
msgstr "<i class=\"fa fa-cloud-upload me-1\"/> 加入內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-comments\"/> Comments ("
msgstr "<i class=\"fa fa-comments\"/>評論 （</i>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-desktop me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-desktop me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">全螢幕</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<i class=\"fa fa-envelope\"/> Send Email"
msgstr "<i class=\"fa fa-envelope\"/> 傳送電郵"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-eraser me-1\"/>Clear filters"
msgstr "<i class=\"fa fa-eraser me-1\"/> 清除篩選"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "<i class=\"fa fa-eraser\"/> Clear filters"
msgstr "<i class=\"fa fa-eraser\"/> 清除篩選"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide_forbidden
msgid ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-"
"label=\"Attention\" title=\"Attention\"/> This document is private."
msgstr ""
"<i class=\"fa fa-exclamation-triangle\" role=\"img\" aria-label=\"注意\" "
"title=\"注意\"/> 這是私人文件。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-eye me-2\" aria-label=\"Views\" role=\"img\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-2\" aria-label=\"瀏覽次數\" role=\"img\" title=\"瀏覽次數\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-code-o me-2\" aria-label=\"article\" role=\"img\" "
"title=\"Article\"/>"
msgstr "<i class=\"fa fa-file-code-o me-2\" aria-label=\"文章\" role=\"img\" title=\"文章\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-image-o me-2\" aria-label=\"Infographic\" role=\"img\""
" title=\"Infographic\"/>"
msgstr "<i class=\"fa fa-file-image-o me-2\" aria-label=\"資訊圖\" role=\"img\" title=\"資訊圖\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-pdf-o me-2\" aria-label=\"Document\" role=\"img\" "
"title=\"Document\"/>"
msgstr "<i class=\"fa fa-file-pdf-o me-2\" aria-label=\"文件\" role=\"img\" title=\"文件\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-file-video-o me-2\" aria-label=\"Video\" role=\"img\" "
"title=\"Video\"/>"
msgstr "<i class=\"fa fa-file-video-o me-2\" aria-label=\"影片\" role=\"img\" title=\"影片\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "<i class=\"fa fa-flag me-2\" aria-label=\"Quiz\" role=\"img\" title=\"Quiz\"/>"
msgstr "<i class=\"fa fa-flag me-2\" aria-label=\"測驗\" role=\"img\" title=\"測驗\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
msgid "<i class=\"fa fa-flag text-warning\"/> Quiz"
msgstr "<i class=\"fa fa-flag text-warning\"/> 測驗"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen_sidebar_category
msgid "<i class=\"fa fa-flag-checkered text-warning\"/>Quiz"
msgstr "<i class=\"fa fa-flag-checkered text-warning\"/> 測驗"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-folder-o me-1\"/><span>Add Section</span>"
msgstr "<i class=\"fa fa-folder-o me-1\"/> <span>新增章節</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<i class=\"fa fa-folder-o me-1\"/>Add a section"
msgstr "<i class=\"fa fa-folder-o me-1\"/> 新增章節"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"網站\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "<i class=\"fa fa-graduation-cap me-1\"/>All courses"
msgstr "<i class=\"fa fa-graduation-cap me-1\"/> 所有課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<i class=\"fa fa-home\"/> About"
msgstr "<i class=\"fa fa-home\"/> 關於"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "<i class=\"fa fa-home\"/> Course"
msgstr "<i class=\"fa fa-home\"/> 課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-home\"/><span class=\"d-none d-md-inline-block ms-1\">Back "
"to course</span>"
msgstr ""
"<i class=\"fa fa-home\"/> <span class=\"d-none d-md-inline-block "
"ms-1\">返回課程</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid ""
"<i class=\"fa fa-lock\"/>\n"
"                        <span>Course Locked</span>"
msgstr ""
"<i class=\"fa fa-lock\"/>\n"
"                        <span>課程被鎖定</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<i class=\"fa fa-plus me-1\"/> <span class=\"d-none d-md-inline-block\">Add "
"Content</span>"
msgstr ""
"<i class=\"fa fa-plus me-1\"/> <span class=\"d-none d-md-inline-"
"block\">加入內容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "<i class=\"fa fa-plus me-1\"/><span>Add Content</span>"
msgstr "<i class=\"fa fa-plus me-1\"/> <span>加入內容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Add Question</span>"
msgstr ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>加入問題</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_content_quiz_add_buttons
msgid ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>Add Quiz</span>"
msgstr ""
"<i class=\"fa fa-plus me-2\"/>\n"
"            <span>加入測驗</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid ""
"<i class=\"fa fa-question me-2\" aria-label=\"Number of Questions\" "
"role=\"img\" title=\"Number of Questions\"/>"
msgstr "<i class=\"fa fa-question me-2\" aria-label=\"問題數目\" role=\"img\" title=\"問題數目\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"fa fa-share-alt me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt me-xl-2 my-1\"/>\n"
"                <span class=\"d-none d-xl-inline-block\">分享</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>\n"
"                                            Share"
msgstr ""
"<i class=\"fa fa-share-alt\" aria-label=\"分享\" title=\"分享\"/>\n"
"                                            分享"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-share-alt\"/>\n"
"                        <span class=\"d-none d-md-inline-block ms-2\">Share</span>"
msgstr ""
"<i class=\"fa fa-share-alt\"/>\n"
"                        <span class=\"d-none d-md-inline-block ms-2\">分享</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "<i class=\"fa fa-share-alt\"/> Share"
msgstr "<i class=\"fa fa-share-alt\"/> 分享"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid ""
"<i class=\"fa fa-sign-out\"/><span class=\"d-none d-md-inline-block "
"ms-1\">Exit Fullscreen</span>"
msgstr ""
"<i class=\"fa fa-sign-out\"/> <span class=\"d-none d-md-inline-block "
"ms-1\">離開全螢幕</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid ""
"<i class=\"fa fa-tag me-2 text-muted\"/>\n"
"                      My Courses"
msgstr ""
"<i class=\"fa fa-tag me-2 text-muted\"/>\n"
"                      我的課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Courses"
msgstr "<i class=\"oi oi-arrow-right me-1\"/> 所有課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<i class=\"oi oi-chevron-left me-2\"/> <span class=\"d-none d-sm-inline-"
"block\">Prev</span>"
msgstr ""
"<i class=\"oi oi-chevron-left me-2\"/> <span class=\"d-none d-sm-inline-"
"block\">上一個</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid ""
"<small class=\"text-success\">\n"
"                        Request already sent\n"
"                    </small>"
msgstr ""
"<small class=\"text-success\">\n"
"                        已發送請求\n"
"                    </small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid ""
"<small><span class=\"badge text-bg-success fw-normal\"><i class=\"fa fa-"
"check\"/> Completed</span></small>"
msgstr ""
"<small><span class=\"badge text-bg-success fw-normal\"><i class=\"fa fa-"
"check\"/> 已完成</span></small>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge fw-bold m-1 text-bg-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"
msgstr ""
"<span class=\"badge fw-bold m-1 text-bg-warning\">\n"
"                            <i class=\"fa fa-fw fa-flag\"/> 10 xp\n"
"                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid ""
"<span class=\"badge text-bg-info badge-arrow-right fw-normal "
"m-1\">New</span>"
msgstr ""
"<span class=\"badge text-bg-info badge-arrow-right fw-normal m-1\">新</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge text-bg-primary badge-hide fw-normal m-1\">Add "
"Quiz</span>"
msgstr ""
"<span class=\"badge text-bg-primary badge-hide fw-normal m-1\">新增小測驗</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid ""
"<span class=\"badge text-bg-success fw-normal "
"m-1\"><span>Preview</span></span>"
msgstr ""
"<span class=\"badge text-bg-success fw-normal m-1\"><span>預覽</span></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"badge text-bg-success fw-normal m-1\">Preview</span>"
msgstr "<span class=\"badge text-bg-success fw-normal m-1\">預覽</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid ""
"<span class=\"badge text-bg-success pull-right\"><i class=\"fa fa-check\"/> "
"Completed</span>"
msgstr ""
"<span class=\"badge text-bg-success pull-right\"><i class=\"fa fa-check\"/> "
"已完成</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"d-none d-sm-inline-block\">Next</span> <i class=\"oi oi-"
"chevron-right ms-2\"/>"
msgstr ""
"<span class=\"d-none d-sm-inline-block\">下一個</span> <i class=\"oi oi-"
"chevron-right ms-2\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "<span class=\"fw-bold text-muted me-2\">Current rank:</span>"
msgstr "<span class=\"fw-bold text-muted me-2\">目前排名：</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "<span class=\"fw-normal\">Last update:</span>"
msgstr "<span class=\"fw-normal\">最後更新：</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "<span class=\"input-group-text\">Start at Page</span>"
msgstr "<span class=\"input-group-text\">起始頁</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid ""
"<span class=\"ms-1\">Lessons</span>\n"
"                                <span class=\"ms-1\">·</span>"
msgstr ""
"<span class=\"ms-1\">課節</span>\n"
"                                <span class=\"ms-1\"> · </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"o_form_label\">Slides</span>\n"
"                    <span class=\"fa fa-lg fa-globe\" title=\"Values set here are website-specific.\" groups=\"website.group_multi_website\"/>"
msgstr ""
"<span class=\"o_form_label\">投影片</span>\n"
"                    <span class=\"fa fa-lg fa-globe\" title=\"此處設定的值是特定於每個網站的。\" groups=\"website.group_multi_website\"/>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "<span class=\"o_stat_text\">Attendees</span>"
msgstr "<span class=\"o_stat_text\">參加者</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_partner_view_form
msgid "<span class=\"o_stat_text\">Courses</span>"
msgstr "<span class=\"o_stat_text\">課程</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span class=\"o_stat_text\">Embed Views</span>"
msgstr "<span class=\"o_stat_text\">嵌入檢視</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card_information_arrow
msgid "<span class=\"o_wslides_arrow\">New Content</span>"
msgstr "<span class=\"o_wslides_arrow\">新內容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid ""
"<span class=\"oi oi-arrow-right\"/>\n"
"                                Create a Google Project and Get a Key"
msgstr ""
"<span class=\"oi oi-arrow-right\"/>\n"
"                                建立 Google 專案項目並獲取密鑰"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training
msgid "<span class=\"p-2\">Course content</span>"
msgstr "<span class=\"p-2\">課程內容</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "<span class=\"text-500 mx-2\">•</span>"
msgstr "<span class=\"text-500 mx-2\">•</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">\n"
"                        Additional Resources\n"
"                    </span>"
msgstr ""
"<span class=\"text-muted fw-bold col-4 col-md-3\">\n"
"                        其他資源\n"
"                    </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted fw-bold col-4 col-md-3\">External sources</span>"
msgstr "<span class=\"text-muted fw-bold col-4 col-md-3\">外部來源</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span class=\"text-muted fw-bold me-3\">Rating</span>"
msgstr "<span class=\"text-muted fw-bold me-3\">評分</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span class=\"text-muted text-truncate mw-100\" "
"title=\"Invited\">Invited</span>"
msgstr "<span class=\"text-muted text-truncate mw-100\" title=\"已邀請\">已邀請</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span class=\"text-muted text-truncate mw-100\" "
"title=\"Ongoing\">Ongoing</span>"
msgstr "<span class=\"text-muted text-truncate mw-100\" title=\"進行中\">進行中</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "<span class=\"text-muted text-truncate mw-100\" title=\"Total\">Total</span>"
msgstr "<span class=\"text-muted text-truncate mw-100\" title=\"總數\">總數</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Common tasks for a computer scientist</span>"
msgstr "<span class=\"text-muted\">計算機科學家的常見任務</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span class=\"text-muted\">Parts of computer science</span>"
msgstr "<span class=\"text-muted\">計算機科學的部分</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"first\" class=\"me-1 me-sm-2\" title=\"First slide\" aria-label=\"First slide\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"Previous slide\" aria-label=\"Previous slide\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"Next slide\" aria-label=\"Next slide\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"Last slide\" aria-label=\"Last slide\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"
msgstr ""
"<span id=\"first\" class=\"me-1 me-sm-2\" title=\"第一頁\" aria-label=\"第一頁\" role=\"button\"><i class=\"fa fa-step-backward\"/></span>\n"
"                                        <span id=\"previous\" class=\"mx-1 mx-sm-2\" title=\"上頁\" aria-label=\"上頁\" role=\"button\"><i class=\"fa fa-arrow-circle-left\"/></span>\n"
"                                        <span id=\"next\" class=\"mx-1 mx-sm-2\" title=\"下頁\" aria-label=\"下頁\" role=\"button\"><i class=\"fa fa-arrow-circle-right\"/></span>\n"
"                                        <span id=\"last\" class=\"mx-1 mx-sm-2\" title=\"最後一頁\" aria-label=\"最後一頁\" role=\"button\"><i class=\"fa fa-step-forward\"/></span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"fullscreen\" class=\"ms-1 ms-sm-2\" title=\"View fullscreen\" aria-label=\"Fullscreen\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"fullscreen\" class=\"ms-1 ms-sm-2\" title=\"全螢幕檢視\" aria-label=\"全螢幕\" role=\"button\">\n"
"                                            <i class=\"fa fa-arrows-alt\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ms-2 me-2\" title=\"Zoom out\" aria-label=\"Zoom out\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"Zoom in\" aria-label=\"Zoom in\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"
msgstr ""
"<span id=\"zoomout\" class=\"d-none d-sm-inline ms-2 me-2\" title=\"縮小\" aria-label=\"縮小\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-minus\"/>\n"
"                                        </span>\n"
"                                        <span id=\"zoomin\" class=\"d-none d-sm-inline\" title=\"放大\" aria-label=\"放大\" role=\"button\">\n"
"                                            <i class=\"fa fa-search-plus\"/>\n"
"                                        </span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid ""
"<span name=\"done_members_count_label\" class=\"text-muted text-truncate "
"mw-100\" title=\"Finished\">Finished</span>"
msgstr ""
"<span name=\"done_members_count_label\" class=\"text-muted text-truncate "
"mw-100\" title=\"已完成\">已完成</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"<span name=\"members_completed_count_label\" "
"class=\"o_stat_text\">Finished</span>"
msgstr "<span name=\"members_completed_count_label\" class=\"o_stat_text\">完成</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "<span> hours</span>"
msgstr "<span> 小時</span>"

#. module: website_slides
#: model_terms:web_tour.tour,rainbow_man_message:website_slides.slides_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>做得好！</b>你已完成本次導覽的所有步驟。</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "<span>Add Tag</span>"
msgstr "<span>新增標籤</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Answering Questions</span>"
msgstr "<span>回答問題</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking Question</span>"
msgstr "<span>提問</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Asking the right question</span>"
msgstr "<span>問正確的問題</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>Content only accessible to course attendees.</span>"
msgstr "<span>內容只供課程參加者查看。</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Logic</span>"
msgstr "<span>邏輯</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Mathematics</span>"
msgstr "<span>數學</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "<span>Preview</span>"
msgstr "<span>預覽</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "<span>Science</span>"
msgstr "<span>科學</span>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "<span>XP</span>"
msgstr "<span>XP</span>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid "<strong>Create a course</strong>"
msgstr "<strong>建立一個課程</strong>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_partner_action_from_slide
msgid "<strong>No Attendee Yet!</strong>"
msgstr "<strong>未有參加者。</strong>"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action_report
msgid "<strong>No Attendees Yet!</strong>"
msgstr "<strong>未有參加者。</strong>"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<strong>Sharing is caring!</strong> Email(s) sent."
msgstr "<strong>分享就是關愛！</strong>電子郵件已發送。"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_1
msgid "A Mighty Forest from Ages"
msgstr "古老而強大的森林"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_0
msgid "A fruit"
msgstr "一個水果"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"A good course has a structure. Pick a name for your first <b>Section</b>."
msgstr "好的課程是有結構的。請為第一個<b>章節</b>命名。"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_4
msgid "A little chat with Harry Potted"
msgstr "與哈利波特的小聊天"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_2_gard2
msgid ""
"A lot of nice documentation: trees, wood, gardens. A gold mine for "
"references."
msgstr "很多不錯的文件：樹木、木材、花園。園藝的知識寶庫。"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_partner_channel_partner_uniq
msgid "A partner membership to a channel must be unique!"
msgstr "合作夥伴的頻道會員資格必須是唯一的！"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_partner_slide_partner_uniq
msgid "A partner membership to a slide must be unique!"
msgstr "合作夥伴的投影片會員資格必須是唯一的！"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_resource_check_file_type
msgid "A resource of type file cannot contain a link."
msgstr "檔案類型的資源，不可包含連結。"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_resource_check_url
msgid "A resource of type url must contain a link."
msgstr "網址（URL）類型的資源，必須包含連結。"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_0
msgid "A shovel"
msgstr "鏟子"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_exclusion_html_content_and_url
msgid "A slide is either filled with a url or HTML content. Not both."
msgstr "投影片只可填寫網址，或以 HTML 編寫內容，不可兩者兼有。"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_1_1
msgid "A spoon"
msgstr "湯匙"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_5
msgid "A summary of know-how: how and what."
msgstr "技術訣竅的總結：如何和什麼。"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_0
msgid ""
"A summary of know-how: how and what. All the basics for this course about "
"gardening."
msgstr "技術訣竅的總結：如何和什麼。本課程有關園藝的所有基礎知識。"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_0
msgid ""
"A summary of know-how: what are the main trees categories and how to "
"differentiate them."
msgstr "訣竅總結：主要的樹木類別是什麼以及如何區分它們。"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_2
msgid "A table"
msgstr "在餐桌"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_tag_slide_tag_unique
msgid "A tag must be unique!"
msgstr "標籤必須唯一!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_0_4_question_0_1
msgid "A vegetable"
msgstr "蔬菜"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "API Key"
msgstr "API 金鑰"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Access Granted"
msgstr "授予存取權限"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_groups
msgid "Access Groups"
msgstr "存取組"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Access Refused"
msgstr "拒絕存取"

#. module: website_slides
#: model:mail.activity.type,name:website_slides.mail_activity_data_access_request
msgid "Access Request"
msgstr "存取請求"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_requested_access
msgid "Access Requested"
msgstr "請求存取"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Access Rights"
msgstr "存取權"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
msgid "Accessed on"
msgstr "存取於"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "Achievements"
msgstr "成就"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction
msgid "Action Needed"
msgstr "需要採取行動"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__active
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__active
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__active
msgid "Active"
msgstr "啟用"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__members_engaged_count
msgid "Active attendees include both 'joined' and 'ongoing' attendees."
msgstr "「活躍」參加者包括「已加入」及「進行中」參加者。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_ids
msgid "Activities"
msgstr "活動"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活動異常圖示"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_state
msgid "Activity State"
msgstr "活動狀態"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_icon
msgid "Activity Type Icon"
msgstr "活動類型圖示"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Add"
msgstr "加入"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Attendees"
msgstr "加入參加者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_comment
msgid "Add Comment"
msgstr "新增評論"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Content"
msgstr "添加內容"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_review
msgid "Add Review"
msgstr "新增評論"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Add Section"
msgstr "添加章節"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Add Tag"
msgstr "添加標籤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
msgid "Add a section"
msgstr "加入分段標題"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Add a tag"
msgstr "添加標籤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Add an answer below this one"
msgstr "在這個下面添加一個答案"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Add comment on this answer"
msgstr "添加對此答案的評論"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Add contacts..."
msgstr "加入聯絡人⋯"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Add quiz"
msgstr "加入小測驗"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Add your content here..."
msgstr "在此處加入你的內容⋯"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Added On"
msgstr "加入於"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_ids
msgid "Additional Resource for this slide"
msgstr "此投影片的其他資源"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Additional Resources"
msgstr "其他資源"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_resource
msgid "Additional resource for a particular slide"
msgstr "特定投影片的額外資料"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_advanced
msgid "Advanced"
msgstr "進階"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_all_ids
msgid "All Attendees Information"
msgstr "所有參加者資訊"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_overview
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "All Courses"
msgstr "所有課程"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "All progress will be lost until you rejoin this course."
msgstr "你將會失去所有課程進度，除非你重新加入本課程。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "All questions must be answered!"
msgstr "必須回答所有問題！"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_question.py:0
msgid ""
"All questions must have at least one correct answer and one incorrect answer: \n"
"%s\n"
msgstr ""
"所有問題都必須有最少一個正確答案以及一個錯誤答案：\n"
"%s\n"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "All the courses you attend will appear here. <br/>"
msgstr "此處會顯示你參加的所有課程。<br/>"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_5_furn2
msgid "All you need to know about furniture creation."
msgstr "您需要了解的有關家具製作的所有資訊"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__allow_comment
#: model:ir.model.fields,help:website_slides.field_slide_slide__channel_allow_comment
msgid ""
"Allow Attendees to like and comment your content and to submit reviews on "
"your course."
msgstr "允許參加者讚好及評論你的內容，以及提交對你課程的評價。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow Download"
msgstr "允許下載"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "允許預覽"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "Allow Rating"
msgstr "允許評級"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Allow Reviews"
msgstr "允許評價"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__allow_comment
msgid "Allow rating on Course"
msgstr "允許對課程評分"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_resource_downloadable
msgid "Allow the user to download the content of the slide."
msgstr "允許用戶下載投影片的內容。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_allow_comment
msgid "Allows comment"
msgstr "允許評論"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Already Requested"
msgstr "已提出請求"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Already member"
msgstr "已經是會員"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.js:0
msgid "Amazing!"
msgstr "了不起!"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_2
msgid "And also bananas"
msgstr "還有香蕉"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__text_value
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answer_ids
msgid "Answer"
msgstr "答案"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Answers"
msgstr "答案"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "出現在"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Archive"
msgstr "封存"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
msgid "Archive Content"
msgstr "封存內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Archived"
msgstr "已封存"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_archive.js:0
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
msgid "Are you sure you want to archive this content?"
msgstr "確定要封存此內容？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: code:addons/website_slides/static/src/xml/slide_management.xml:0
msgid "Are you sure you want to delete this category?"
msgstr "確定刪除此類別？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Are you sure you want to delete this question \"<strong>%s</strong>\"?"
msgstr "確定刪除此問題？（<strong>%s</strong>）"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__article
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__article
msgid "Article"
msgstr "文章"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_article
msgid "Articles"
msgstr "篇文章"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_attachment_count
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_attachment_count
msgid "Attachment Count"
msgstr "附件數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__attachment_ids
msgid "Attachments"
msgstr "附件"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_avg
msgid "Attempts Avg"
msgstr "嘗試平均"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__attempts_count
msgid "Attempts Count"
msgstr "嘗試計數"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_form
msgid "Attendee"
msgstr "參加者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__member_status
msgid "Attendee Status"
msgstr "參加者狀態"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action
#: model:ir.actions.act_window,name:website_slides.slide_channel_partner_action_report
#: model:ir.actions.act_window,name:website_slides.slide_slide_partner_action_from_slide
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_ids
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_attendees
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_pivot
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Attendees"
msgstr "參加者"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Attendees of %s"
msgstr "%s 的參加者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_group_ids
msgid "Auto Enroll Groups"
msgstr "自動組隊"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg
msgid "Average Rating"
msgstr "平均評分"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Average Review"
msgstr "平均評價"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "BUILDING BLOCKS DROPPED HERE WILL BE SHOWN ACROSS ALL LESSONS"
msgstr "拖放至此處的內容方塊，會在所有課程中顯示"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
msgid "Back"
msgstr "返回"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Back to course"
msgstr "返回課程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_default_background_image_url
msgid "Background image URL"
msgstr "背景圖片網址"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Badges"
msgstr "徽章"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_basic
msgid "Basic"
msgstr "基礎"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_5_furn2
msgid "Basics of Furniture Creation"
msgstr "家具製作基礎"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_0_gard_0
msgid "Basics of Gardening"
msgstr "園藝基礎"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Be notified when a new content is added."
msgstr "添加新內容時收到通知。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body_has_template_value
msgid "Body content is the same as the template"
msgstr "正文內容與範本相同"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_comment
msgid "Can Comment"
msgstr "可評論的"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__can_edit_body
msgid "Can Edit Body"
msgstr "允許編輯內文"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_self_mark_completed
msgid "Can Mark Completed"
msgstr "可標記為完成"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_self_mark_uncompleted
msgid "Can Mark Uncompleted"
msgstr "可標記為未完成"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__can_publish
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__can_publish
msgid "Can Publish"
msgstr "可以發佈"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_review
msgid "Can Review"
msgstr "可評價"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_upload
msgid "Can Upload"
msgstr "可以上傳"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__can_vote
msgid "Can Vote"
msgstr "可以投票"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Can not be marked as done"
msgstr "不可標記為做完"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Can not be marked as not done"
msgstr "不可標記為未做完"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Cancel"
msgstr "取消"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_carpenter
msgid "Carpenter"
msgstr "木匠"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
msgid "Catchy Headline"
msgstr "吸引人的標題"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_category_ids
msgid "Categories"
msgstr "類別"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_category
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_category
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Category"
msgstr "類別"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_1
msgid "Certification"
msgstr "認證"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Certifications"
msgstr "認證"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_certification
#: model:gamification.goal.definition,name:website_slides.badge_data_certification_goal
msgid "Certified Knowledge"
msgstr "認證知識"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Change video privacy settings"
msgstr "更改影片私隱設定"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__channel_id
msgid "Channel"
msgstr "群組"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "頻道/合作夥伴（會員）"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_invite
msgid "Channel Invitation Wizard"
msgstr "頻道邀請精靈"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_channel_template_id
msgid "Channel Share Template"
msgstr "頻道分享範本"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_channel_shared
msgid "Channel Shared"
msgstr "頻道已分享"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_type
msgid "Channel type"
msgstr "頻道類型"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag_group
msgid "Channel/Course Groups"
msgstr "頻道/課程群組"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel_tag
msgid "Channel/Course Tag"
msgstr "頻道/課程標籤"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__channel_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Channels"
msgstr "頻道"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_cheatsheet
msgid "CheatSheet"
msgstr "備忘錄"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "Check Profile"
msgstr "檢查個人資料"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Check answers"
msgstr "檢查答案"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Check your answers"
msgstr "檢查一下您的答案"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Choose a <b>File</b> on your computer."
msgstr "在您的電腦上選擇一個<b>檔案</b>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Choose a PDF"
msgstr "選擇 PDF 檔案"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "Choose a layout"
msgstr "選擇排版格式"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Choose an Image"
msgstr "選擇一張圖片"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_3_furn0
msgid "Choose your wood!"
msgstr "選擇您的木材!"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "Clear filters"
msgstr "清除篩選"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click <b>Save</b> to create it."
msgstr "按一下「<b>儲存</b>」以建立它。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to get started"
msgstr "按此開始"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Click here to start the course"
msgstr "按此開始課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Click on \"New\" in the top-right corner to write your first course."
msgstr "點選右上角的「新建」以編寫您的第一個課程。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"Click on the \"Edit\" button in the top corner of the screen to edit your "
"slide content."
msgstr "按螢幕上角的「編輯」按鈕，以編輯投影片內容。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click on the <b>Save</b> button to create your first course."
msgstr "按一下<b>儲存</b>按鈕，建立你的第一個課程。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Click on your <b>Course</b> to go back to the table of content."
msgstr "點選您的<b>課程</b>來返回目錄。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Close"
msgstr "關閉"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Color"
msgstr "顏色"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__color
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__color
msgid "Color Index"
msgstr "顏色索引"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_colorful
msgid "Colorful"
msgstr "繽紛"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
msgid "Come back later to check the feedbacks given by your Attendees."
msgstr "請稍後回來，查看參加者回應的意見。"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "Come back later to oversee how well your Attendees are doing."
msgstr "請稍後回來，監察參加者表現。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__comment
msgid "Comment"
msgstr "評論"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Commenting is not enabled on this course."
msgstr "本課程沒有啟用評論功能。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Comments"
msgstr "評論"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions. In this course, you'll study those topics with "
"activities about mathematics, science and logic."
msgstr "計算機科學家的常見任務是提出正確的問題並回答問題。在本課程中，您將通過有關數學、科學和邏輯的活動來學習這些主題。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid ""
"Common tasks for a computer scientist is asking the right questions and "
"answering questions..."
msgstr "計算機科學家的常見任務是提出正確的問題並回答問題..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Communication"
msgstr "溝通"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_karma
#: model:gamification.goal.definition,name:website_slides.badge_data_karma_goal
msgid "Community hero"
msgstr "社區英雄"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_company_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_company_count
msgid "Company Course Count"
msgstr "公司課程數目"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_0
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_0
msgid "Comparing Hardness of Wood Species"
msgstr "比較木材種類的硬度"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_course_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_course
msgid "Complete a course"
msgstr "完成的課程"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_profile_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_profile
msgid "Complete your profile"
msgstr "完成你的個人資料"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
msgid "Completed"
msgstr "已完成"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Completed Course"
msgstr "完成課程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_completed_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_completed_ids
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Completed Courses"
msgstr "完成課程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completion
msgid "Completion"
msgstr "完成"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed_template_id
msgid "Completion Notification"
msgstr "課程完成通知"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Completion Time"
msgstr "完成時間"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Compose Email"
msgstr "撰寫電郵"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_config_settings
msgid "Config Settings"
msgstr "配置設定"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_configuration
msgid "Configuration"
msgstr "配置"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_completed
msgid "Congratulations! You completed {{ object.channel_id.name }}"
msgstr "恭喜！你已完成 {{ object.channel_id.name }}"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations! Your first lesson is available. Let's see the options "
"available here. The tag \"<b>New</b>\" indicates that this lesson was "
"created less than 7 days ago."
msgstr "恭喜！你的第一課已經可用。讓我們看看這裡可用的選項。標籤 <b>新</b> ”表示這節課是在不到 7 天前建立的。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Congratulations, you have reached the last rank!"
msgstr "恭喜您，您已經達到了最近的排名！"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations, you've created your first course.<br/>Click on the title of"
" this content to see it in fullscreen mode."
msgstr "恭喜，您已經建立了您的第一門課程。<br/>點選此內容的標題以全螢幕模式查看。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Congratulations, your course has been created, but there isn't any content "
"yet. First, let's add a <b>Section</b> to give your course a structure."
msgstr "恭喜，您的課程已建立，但還沒有任何內容。首先，讓我們添加一個<b>章節</b>來為您的課程提供一個結構。"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_partner
msgid "Contact"
msgstr "聯絡人"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_2_gard2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_5_furn2
#: model_terms:slide.channel,enroll_msg:website_slides.slide_channel_demo_6_furn3
msgid "Contact Responsible"
msgstr "聯絡負責人"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Contact the responsible to enroll."
msgstr "聯絡負責人以報名。"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Contact us"
msgstr "聯絡我們"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_content_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_question__slide_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__slide_id
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Content"
msgstr "內容"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Content Preview"
msgstr "內容預覽"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_question
msgid "Content Quiz Question"
msgstr "內容測驗問題"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_tag
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_content_tags
msgid "Content Tags"
msgstr "內容標籤"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Title"
msgstr "內容標題"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Content Type"
msgstr "內容類型"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action
msgid ""
"Content are the lessons that compose a course\n"
"                    <br>and can be of different types (presentations, documents, videos, ...)."
msgstr ""
"「內容」是指構成一個課程的不同課節，\n"
"                    <br>可以是不同類型的（例如：簡報、文件、影片等）。"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_slide_action
#: model:ir.actions.act_window,name:website_slides.slide_slide_action_report
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__body
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_content
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_contents
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Contents"
msgstr "內容"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Continue"
msgstr "繼續"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copied"
msgstr "複製"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copy Embed Code"
msgstr "複製嵌入程式碼"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.js:0
msgid "Copy Link"
msgstr "複製連結"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_1
msgid "Correct!"
msgstr "正確！"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_0
msgid "Correct! A shovel is the perfect tool to dig a hole."
msgstr "正確！ 鏟子是挖洞的完美工具。"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_0
msgid "Correct! A strawberry is a fruit because it's the product of a tree."
msgstr "正確！ 草莓是一種水果，因為它是樹的產物。"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_0
msgid "Correct! Congratulations you have time to loose"
msgstr "正確的！ 恭喜你有時間放鬆"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Correct! You did it!"
msgstr "正確！你做到了！"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Could not find your video. Please check if your link is correct and if the "
"video can be accessed."
msgstr "找不到你的影片。請檢查連結是否正確，以及影片是否開放存取。"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_channel
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__channel_id
#: model_terms:ir.ui.view,arch_db:website_slides.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_form_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_search_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Course"
msgstr "課程"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__members
msgid "Course Attendees"
msgstr "課程參加者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_count
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_count
msgid "Course Count"
msgstr "課程數目"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Finished"
msgstr "已完成課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Group Name"
msgstr "課程群組名稱"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_group_action
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_course_groups
msgid "Course Groups"
msgstr "課程群組"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_invite_url
msgid "Course Link"
msgstr "課程連結"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Course Member"
msgstr "課程成員"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Course Name"
msgstr "課程名稱"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.action_slide_channel_pages_list
msgid "Course Pages"
msgstr "課程頁"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/gamification_karma_tracking.py:0
msgid "Course Quiz"
msgstr "課程測驗"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Ranked"
msgstr "有排名的課程"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course Set Uncompleted"
msgstr "設為未完成的課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_form
msgid "Course Tag"
msgstr "課程標籤"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Course Tag Group"
msgstr "課程標籤組"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Course Tag Groups"
msgstr "課程標籤組"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_tag_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_view_tree
msgid "Course Tags"
msgstr "課程標籤"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Course Title"
msgstr "課程名稱"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
msgid "Course Type"
msgstr "課程類型"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_finish
msgid "Course finished"
msgstr "課程已完成"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Course not published yet"
msgstr "課程尚未公開"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_gen_channel_rank
msgid "Course ranked"
msgstr "課程排名"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_type
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_type
msgid "Course type"
msgstr "課程類型"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Course: %s"
msgstr "課程:%s"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/res_partner.py:0
#: code:addons/website_slides/models/website.py:0
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_report
#: model:ir.ui.menu,name:website_slides.menu_slide_channel_pages
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_courses_courses
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_courses
#: model:website.menu,name:website_slides.website_menu_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_graph
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Courses"
msgstr "課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "Courses Page"
msgstr "課程頁"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__prerequisite_of_channel_ids
msgid "Courses that have this course as prerequisite."
msgstr "以本課程為先修課程的課程。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__cover_properties
msgid "Cover Properties"
msgstr "封面屬性"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Create New Category \""
msgstr "建立新類別 \""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Create New Tag \""
msgstr "建立新標籤 \""

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_tag
msgid "Create a Content Tag"
msgstr "建立內容標籤"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_tag_group_action
msgid "Create a Course Group"
msgstr "建立課程組"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Create a community and let Attendees answer each others' questions."
msgstr "建立社群，並讓參加者回答大家的問題。"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "Create new content for your eLearning"
msgstr "為您的線上學習建立新內容"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Create this tag \""
msgstr "建立此標籤 \""

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Create this tag group\""
msgstr "建立此標籤組 \""

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__create_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__create_date
msgid "Created on"
msgstr "建立於"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__html_content
msgid "Custom HTML content for slides of category 'Article'."
msgstr "文章類別投影片的自訂 HTML 內容。"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_6_furn3
msgid "DIY Furniture"
msgstr "DIY家具"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (new to old)"
msgstr "日期（從新到舊）"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Date (old to new)"
msgstr "日期（從舊到新）"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_kanban
msgid "Default training image"
msgstr "預設培訓圖片"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "通過選單定義的可見性挑戰"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_enroll
msgid "Defines how people can enroll to your Course."
msgstr "設定人們如何註冊你的課程。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__promote_strategy
msgid "Defines the content that will be promoted on the course home page"
msgstr "定義將在課程主頁上推廣的內容"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__publish_template_id
msgid ""
"Defines the email your Attendees will receive each time you upload new "
"content."
msgstr "定義你每次上載新內容時，參加者將收到的電子郵件。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__completed_template_id
msgid ""
"Defines the email your Attendees will receive once they reach the end of "
"your course."
msgstr "定義參加者完成課程後收到的電子郵件。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__channel_visibility
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_visibility
msgid "Defines who can access your courses and their content."
msgstr "定義誰人可存取你的課程及其內容。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Delete"
msgstr "刪除"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_category_delete.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list
msgid "Delete Category"
msgstr "刪除分類"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Delete Question"
msgstr "刪除問題"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__description
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Description"
msgstr "說明"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_html
msgid "Detailed Description"
msgstr "詳細說明"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_1
msgid "Did you read the whole article?"
msgstr "你看完整篇文章嗎？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Discard"
msgstr "捨棄"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Discover more"
msgstr "發現更多"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Dislike"
msgstr "不喜歡"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__dislikes
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Dislikes"
msgstr "不喜歡"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Display"
msgstr "顯示"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_question__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__display_name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_0
msgid "Do you make beams out of lemon trees?"
msgstr "你用檸檬樹做橫樑嗎？"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_2_0_question_1
msgid "Do you make lemons out of beams?"
msgstr "你用橫樑製作檸檬嗎？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Do you really want to leave the course?"
msgstr "您真的想離開課程嗎？"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_1_4_question_0
msgid "Do you think Harry Potted has a good name?"
msgstr "Do you think Harry Potted has a good name?"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Do you want to install \"%s\"?"
msgstr "你想安裝「%s」嗎？"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_5_3_question_0
msgid "Do you want to reply correctly?"
msgstr "你想正確回覆嗎？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Do you want to request access to this course?"
msgstr "你想要求存取此課程嗎？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__document
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Document"
msgstr "文件"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__doc
msgid "Document (Word, Google Doc, ...)"
msgstr "文件（Word、Google 文件等）"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_google_url
msgid "Document Link"
msgstr "文件連結"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Document Source"
msgstr "文件來源"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__documentation
msgid "Documentation"
msgstr "系統使用說明"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_document
#: model:slide.slide,name:website_slides.slide_category_demo_4_0
msgid "Documents"
msgstr "文件"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_2
msgid "Dog Friendly"
msgstr "狗狗友善"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Don't have an account?"
msgstr "還沒有帳戶？"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__completed
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
msgid "Done"
msgstr "完成"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__done_count
msgid "Done Count"
msgstr "完成計數"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Done!"
msgstr "完成！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download"
msgstr "下載"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Download Content"
msgstr "下載內容"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__download_url
msgid "Download URL"
msgstr "下載網址"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_10
msgid "Drawing 1"
msgstr "圖1"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_11
msgid "Drawing 2"
msgstr "圖2"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_time
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__completion_time
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Duration"
msgstr "時長"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Earn more Karma to leave a comment."
msgstr "必須有更多貢獻值才可留言。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "Edit"
msgstr "編輯"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "Edit in backend"
msgstr "在後台編輯"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_enroll
msgid "Elearning: Add Attendees to Course"
msgstr "網上學習：為課程加入參加者"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_channel_completed
msgid "Elearning: Completed Course"
msgstr "網上學習：已完成課程"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_shared
msgid "Elearning: Course Share"
msgstr "網上學習：課程分享"

#. module: website_slides
#: model:mail.template,name:website_slides.slide_template_published
msgid "Elearning: New Course Content Notification"
msgstr "網上學習：新課程內容通知"

#. module: website_slides
#: model:mail.template,name:website_slides.mail_template_slide_channel_invite
msgid "Elearning: Promotional Course Invitation"
msgstr "網上學習：推廣性質的課程邀請"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_email
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Email"
msgstr "電郵"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_channel_template_id
msgid "Email template used when sharing a channel"
msgstr "分享頻道時所用的電郵範本"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__share_slide_template_id
msgid "Email template used when sharing a slide"
msgstr "分享投影片時所用的電郵範本"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "Email(s) sent."
msgstr "電郵已發送。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code
msgid "Embed Code"
msgstr "嵌入程式碼"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_embed_action
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
msgid "Embed Views"
msgstr "嵌入檢視"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Embed code"
msgstr "嵌入程式碼"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_embed
msgid "Embed in another Website"
msgstr "嵌入另一網站"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_embed
msgid "Embedded Slides View Counter"
msgstr "嵌入投影片觀看個數"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "End course"
msgstr "結束課程"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_2
msgid "Energy Efficiency Facts"
msgstr "能源效率科學證據"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Enjoy this exclusive content!"
msgstr "希望你喜歡這獨家內容！"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Enroll Attendees to %(course_name)s"
msgstr "將參加者註冊至 %(course_name)s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll_msg
msgid "Enroll Message"
msgstr "註冊訊息"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__enroll
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_enroll
msgid "Enroll Policy"
msgstr "註冊政策"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__enroll_mode
msgid "Enroll partners"
msgstr "註冊合作夥伴"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__channel_partner_ids
msgid "Enrolled Attendees Information"
msgstr "已報名參加者資訊"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__partner_ids
msgid "Enrolled partners in the course"
msgstr "已註冊此課程的合作夥伴"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Enter at least two possible <b>Answers</b>."
msgstr "輸入至少兩個可能的<b>答案</b>。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Enter your <b>Question</b>. Be clear and concise."
msgstr "輸入您的<b>問題</b>，盡可能簡潔明了"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__answers_validation_error
msgid "Error on Answers"
msgstr "答案發生錯誤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Estimated Completion Time"
msgstr "預計完成時間"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Evaluate the knowledge of your Attendees and certify their skills."
msgstr "評估參加者的知識，認證他們的技能。"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__public
msgid "Everyone"
msgstr "每一個人"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_exercises
msgid "Exercises"
msgstr "練習"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Exit Fullscreen"
msgstr "退出全螢幕"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_code_external
msgid "External Embed Code"
msgstr "外部嵌入程式碼"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__embed_ids
msgid "External Slide Embeds"
msgstr "外部投影片嵌入"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__url
msgid "External URL"
msgstr "外部網址"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_embed_view_tree
msgid "External Website"
msgstr "外部網站"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Failed to install \"%s\""
msgstr "未能成功安裝「%s」"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promote_strategy
msgid "Featured Content"
msgstr "精選內容"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__binary_content
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide_resource__resource_type__file
msgid "File"
msgstr "文件"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__file_name
msgid "File Name"
msgstr "檔案名稱"

#. module: website_slides
#. odoo-javascript
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "File is too big. File size cannot exceed 25MB"
msgstr "文件太大。文件大小不能超過 25 MB"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Filter &amp; order"
msgstr "篩選及排序"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Finally you can click here to enjoy your content in fullscreen"
msgstr "最後，你可按此處，以全螢幕觀賞內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Finish"
msgstr "結束"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Finish Course"
msgstr "完成課程"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__completed
msgid "Finished"
msgstr "已完成"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "First Try"
msgstr "首次嘗試"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"First, create your lesson, then edit it with the website builder. You'll be "
"able to drop building blocks on your page and edit them."
msgstr "首先，建立您的課程，然後使用網站構建器進行編輯。您將能夠在頁面上放置構建塊並對其進行編輯。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "First, let's add a <b>Document</b>. It has to be a .pdf file."
msgstr "首先，我們加入一個<b>文件</b>。它必須是 PDF 檔案。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload the file on your Google Drive account."
msgstr "首先，將檔案上載至你的 Google 雲端硬碟帳戶。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload your videos on Vimeo and mark them as"
msgstr "首先，將你的影片上載至 Vimeo，並標記為"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "First, upload your videos on YouTube and mark them as"
msgstr "首先，將您的影片上傳到 YouTube 並將其標記為"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_follower_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_follower_ids
msgid "Followers"
msgstr "關注人"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_partner_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_partner_ids
msgid "Followers (Partners)"
msgstr "關注人（業務夥伴）"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font awesome 圖示，例如，fa-task"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_0
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Foreword"
msgstr "前言"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_0
msgid "Foreword for this documentation: how to use it, main attention points"
msgstr "本文件前言：如何使用，主要注意事項"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_slides_forum
msgid "Forum"
msgstr "討論區"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Fourth Try & More"
msgstr "第四次嘗試或更多"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_2
msgid "From a piece of wood to a fully functional furniture, step by step."
msgstr "從一塊木頭到功能齊全的家具，一步一步來安裝。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_partner_action_report
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_partner_action_from_slide
msgid ""
"From here you'll be able to monitor attendees and to track their progress."
msgstr "此處讓你能監察參加者學習情況，及追蹤他們的進度。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Fullscreen"
msgstr "全螢幕"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_furniture
msgid "Furniture Designer"
msgstr "家具設計師"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_4_furn1
msgid "Furniture Technical Specifications"
msgstr "家具技術規格"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_12
msgid "GLork"
msgstr "GLork"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "遊戲化挑戰"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_role_gardener
msgid "Gardener"
msgstr "園丁"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_0
msgid "Gardening: The Know-How"
msgstr "園藝：訣竅"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_certification_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_certification
msgid "Get a certification"
msgstr "獲得認證"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_register
#: model:gamification.goal.definition,name:website_slides.badge_data_register_goal
msgid "Get started"
msgstr "開始"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Give your course a helpful <b>Description</b>."
msgstr "給你的課程一個易懂的<b>說明</b>。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Give your course an engaging <b>Title</b>."
msgstr "給你的課程一個吸引人的<b>標題</b>。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Go through all its content to see a Course in this section. <br/>"
msgstr "瀏覽它的所有內容，以查看本章節的課程。<br/>"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__website_slide_google_app_key
#: model:ir.model.fields,field_description:website_slides.field_website__website_slide_google_app_key
msgid "Google Doc Key"
msgstr "Google文檔密鑰"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__google_drive
msgid "Google Drive"
msgstr "Google雲端硬碟"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Google Drive API Key"
msgstr "Google Drive API 密鑰"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__google_drive_id
msgid "Google Drive ID of the external URL"
msgstr "外部網址 Google 雲端硬碟識別碼"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__google_drive_video
msgid "Google Drive Video"
msgstr "Google 雲端硬碟影片"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/activity/activity_patch.xml:0
msgid "Grant Access"
msgstr "批准存取"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_graph
msgid "Graph of Contents"
msgstr "圖的內容"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_id
msgid "Group"
msgstr "組"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Group By"
msgstr "分組依據"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__name
msgid "Group Name"
msgstr "群組名稱"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__upload_group_ids
msgid "Group of users allowed to publish contents on a documentation course."
msgstr "允許使用者組在文件課程上發佈內容。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__group_sequence
msgid "Group sequence"
msgstr "組序列"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__html_content
msgid "HTML Content"
msgstr "HTML 內容"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_1
msgid "Hand on!"
msgstr "Hand on!"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_user_has_completed
msgid "Has Completed Prerequisite"
msgstr "已完成先修課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_search
msgid "Has Menu Entry"
msgstr "有選單項目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__has_message
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__has_message
msgid "Has Message"
msgstr "有訊息"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_6
msgid "Here is How to get the Sweetest Strawberries you ever tasted!"
msgstr "這是如何獲得您嚐過的最甜的草莓！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Home"
msgstr "首頁"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_1
msgid "Home Gardening"
msgstr "家庭園藝"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_2
msgid "How To Build a HIGH QUALITY Dining Table with LIMITED TOOLS"
msgstr "如何用有限的工具建造高品質的餐桌"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How do I add new content?"
msgstr "如何加入新內容？"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_3
msgid "How to Grow and Harvest The Best Strawberries | Basics"
msgstr "如何種植和收穫最好的草莓。基本"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_6
msgid ""
"How to Grow and Harvest The Best Strawberries | Gardening Tips and Tricks"
msgstr "如何種植和收穫最好的草莓。園藝技巧和竅門"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to create a Lesson as an Article?"
msgstr "如何建立文章形式的課節？"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_1
msgid "How to find quality wood"
msgstr "如何找到優質木材"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_3
msgid "How to plant a potted.list"
msgstr "如何種植盆栽列表"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to upload your PowerPoint Presentations or Word Documents?"
msgstr "如何上傳您的 PowerPoint 投影片或 Word 文檔？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to upload your videos?"
msgstr "如何上載你的影片？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to use Google Drive?"
msgstr "如何使用 Google 雲端硬碟？"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_2
msgid "How to wall decorating by tree planting in hanging plastic bottles."
msgstr "如何通過在懸掛的塑料瓶中植樹來裝飾牆壁。"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_howto
msgid "HowTo"
msgstr "教學"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__id
#: model:ir.model.fields,field_description:website_slides.field_slide_question__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__id
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__id
msgid "ID"
msgstr "識別號"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon"
msgstr "圖示"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "用於指示異常活動的圖示。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction
msgid "If checked, new messages require your attention."
msgstr "勾選代表有新訊息需要您留意。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "勾選代表有訊息發生傳送錯誤。"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_4_furn1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_4_furn1
msgid ""
"If you are looking for technical specifications, have a look at this "
"documentation."
msgstr "如果您正在尋找技術規範，請查看此文件。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"If you want to be sure that attendees have understood and memorized the "
"content, you can add a Quiz on the lesson. Click on <b>Add Quiz</b>."
msgstr "如果您想確保參加者已經理解並記住了內容，您可以在課程中添加測驗。點選<b>添加測驗</b>。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"If you want to use other types of files, you may want to use an external "
"source (Google Drive) instead."
msgstr "若想使用其他類型文件，你可能需要改為使用外部來源（即 Google 雲端硬碟）。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1920
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1920
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__infographic
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__image
msgid "Image"
msgstr "圖片"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_1024
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_1024
msgid "Image 1024"
msgstr "圖像 1024"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_128
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_128
msgid "Image 128"
msgstr "圖像 128"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_256
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_256
msgid "Image 256"
msgstr "圖像 256"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__image_512
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_512
msgid "Image 512"
msgstr "圖像 512"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_binary_content
msgid "Image Content"
msgstr "圖片內容"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__image_google_url
msgid "Image Link"
msgstr "圖片連結"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Image Source"
msgstr "圖片來源"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid ""
"Impossible to send emails. Select a \"Channel Share Template\" for courses "
"%(course_names)s first"
msgstr "未能發送電郵。請先選擇「頻道分享範本」給課程 %(course_names)s 使用。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Impossible to send emails. Select a \"Share Template\" for courses "
"%(course_names)s first"
msgstr "未能發送電郵。請先選擇「分享範本」給課程 %(course_names)s 使用。"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_0
msgid "Incorrect!"
msgstr "不正確！"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_1
msgid "Incorrect! A strawberry is not a vegetable."
msgstr "不正確！ 草莓不是蔬菜。"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_0_2
msgid "Incorrect! A table is a piece of furniture."
msgstr "不正確！ 桌子是一種家具。"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_0_4_question_1_1
msgid "Incorrect! Good luck digging a hole with a spoon..."
msgstr "不正確！ 祝你好運，用匙子挖洞..."

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_2
msgid "Incorrect! Seriously?"
msgstr "不正確！你認真的嗎？"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_5_3_question_0_1
msgid "Incorrect! You better think twice..."
msgstr "不正確！你最好再想想⋯"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_1_4_question_1_1
msgid "Incorrect! You really should read it."
msgstr "不正確！你真的應該再讀一次。"

#. module: website_slides
#: model:slide.answer,comment:website_slides.slide_slide_demo_2_0_question_1_2
msgid "Incorrect! of course not ..."
msgstr "不正確！當然不是啦⋯"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_infographic
msgid "Infographics"
msgstr "資訊圖"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.xml:0
msgid "Install"
msgstr "安裝"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog_select.xml:0
msgid "Install the"
msgstr "安裝"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.js:0
msgid "Installing \"%s\"..."
msgstr "正在安裝「%s」⋯"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_0
msgid "Interesting Facts"
msgstr "有趣的證據指出"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_1
msgid "Interesting List Facts"
msgstr "有趣軼事列表"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_1
msgid "Interesting information about home gardening. Keep it close!"
msgstr "有關家庭園藝的有趣資訊。密切留意！"

#. module: website_slides
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_level_intermediate
msgid "Intermediate"
msgstr "中級"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Internal server error, please try again later or contact administrator.\n"
"Here is the error message: %s"
msgstr ""
"伺服器內部錯誤，請稍後再嘗試或者聯繫管理員。\n"
"這裡是錯誤訊息：%s"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Invalid file type. Please select pdf or image file"
msgstr "錯誤的文件類型。請選擇pfd文件或者圖像"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__invitation_link
msgid "Invitation Link"
msgstr "邀請連接"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Invite"
msgstr "邀請"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Invite Attendees to %(course_name)s"
msgstr "邀請參加者參加 %(course_name)s"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__invited
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Invite Sent"
msgstr "邀請已發送"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_has_completed_category
msgid "Is Category Completed"
msgstr "類別已完成"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__is_mail_template_editor
msgid "Is Editor"
msgstr "是編輯者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member
msgid "Is Enrolled Attendee"
msgstr "是已報名參加者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_is_follower
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_is_follower
msgid "Is Follower"
msgstr "是關注者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_member_invited
msgid "Is Invited Attendee"
msgstr "是獲邀請參加者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_has_completed
msgid "Is Member"
msgstr "是會員"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_new_slide
msgid "Is New Slide"
msgstr "是新投影片"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__is_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_published
msgid "Is Published"
msgstr "已發佈"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_category
msgid "Is a category"
msgstr "是一個類別"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__is_correct
msgid "Is correct answer"
msgstr "是正確的答案"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__is_member
msgid "Is the attendee actively enrolled."
msgstr "參加者是否主動報名。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__is_member_invited
msgid "Is the invitation for this attendee pending."
msgstr "邀請是否仍待此參加者回覆"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "It should look similar to"
msgstr "它看起來應該類似"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_3
msgid ""
"Jim and Todd plant a potted tree for a customer of Knecht's Nurseries and "
"Landscaping. Narrated by Leif Knecht, owner."
msgstr ""
"吉姆和托德為 Knecht's Nurseries and Landscaping 的客戶種植盆栽樹。由業主 Leif Knecht 講述。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "Join & Submit"
msgstr "加入並提交"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "Join the Course"
msgstr "加入課程"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Join the course to take the quiz and verify your answers!"
msgstr "參加課程來參加測驗並驗證您的答案！"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "Join this Course"
msgstr "參加此課程"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__joined
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Joined"
msgstr "已加入"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_2
msgid "Just some basics Energy Efficiency Facts."
msgstr "只是一些基本的能源效率事實。"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_1
msgid "Just some basics Interesting Tree Facts."
msgstr "只是一些基礎知識有趣的樹事實。"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_0
msgid "Just some basics Tree Infographic."
msgstr "只是一些基礎知識的樹木資訊圖。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Karma"
msgstr "貢獻值"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_comment
msgid "Karma needed to add a comment on a slide of this course"
msgstr "要在本課程的投影片上添加評論所需活躍度"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_review
msgid "Karma needed to add a review on the course"
msgstr "在課程上添加評論所需活躍度"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__karma_slide_vote
msgid "Karma needed to like/dislike a slide of this course."
msgstr "讚/不喜歡這門課程的投影片所需活躍度。"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_profile
#: model:gamification.goal.definition,name:website_slides.badge_data_profile_goal
msgid "Know yourself"
msgstr "認識您自己"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_3_furn0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_3_furn0
msgid ""
"Knowing which kind of wood to use depending on your application is important. In this course you\n"
"will learn the basics of wood characteristics."
msgstr ""
"根據您的應用了解使用哪種木材很重要。在本課程中你\n"
"將學習木材特性的基礎知識。"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_3
msgid ""
"Knowing wood characteristics is a requirement in order to know which kind of"
" wood to use in a given situation."
msgstr "了解木材特性是了解在特定情況下使用哪種木材的必要條件。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__lang
msgid "Language"
msgstr "語言"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Action On"
msgstr "上次操作於"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Last Invitation"
msgstr "上次邀請"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__last_invitation_date
msgid "Last Invitation Date"
msgstr "上次邀請日期"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_last_update
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Last Update"
msgstr "最近更新"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_uid
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_question__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__write_date
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__latest
msgid "Latest Created"
msgstr "最新建立"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_latest_achievements
msgid "Latest achievements"
msgstr "最新成就"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "Leaderboard"
msgstr "排行榜"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_1_gard1
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_1_gard1
msgid ""
"Learn how to take care of your favorite trees. Learn when to plant, how to "
"manage potted trees, ..."
msgstr "了解如何照顧您最喜歡的樹木。了解何時種植，如何管理盆栽樹木，..."

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_0_gard_0
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_0_gard_0
msgid "Learn the basics of gardening!"
msgstr "學習園藝基礎知識！"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_1
msgid "Learn to identify quality wood in order to create solid furnitures."
msgstr "學習識別優質木材以製作堅固的家具。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "Leave the course"
msgstr "離開課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Lesson"
msgstr "章節"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Lesson Nav"
msgstr "課程導航"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Lessons"
msgstr "課程"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.js:0
msgid "Level up!"
msgstr "升級！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Like"
msgstr "讚好"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__likes
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Likes"
msgstr "讚好"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__link
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide_resource__resource_type__url
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Link"
msgstr "連結"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__document_google_url
msgid ""
"Link of the document (we currently only support Google Drive as source)"
msgstr "文件連結（我們目前只支援 Google 雲端硬碟作為來源）"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__image_google_url
msgid "Link of the image (we currently only support Google Drive as source)"
msgstr "圖片連結（我們目前只支援 Google 雲端硬碟作為來源）"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__video_url
msgid ""
"Link of the video (we support YouTube, Google Drive and Vimeo as sources)"
msgstr "影片連結（我們支援 YouTube、Google 雲端硬碟及 Vimeo 作為來源）"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_1_0
msgid "List Infographic"
msgstr "列表資訊圖"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_2
msgid "List planting in hanging bottles on wall"
msgstr "掛牆瓶子種植列表"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Loading content..."
msgstr "正在載入內容⋯"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "Log in"
msgstr "登入"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__template_id
msgid "Mail Template"
msgstr "信件範本"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_mass_mailing_slides
msgid "Mailing"
msgstr "郵寄"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_0
msgid "Main Trees Categories"
msgstr "主要樹木類別"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_manager
msgid "Manager"
msgstr "經理"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Mark Done"
msgstr "標記完成"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Mark To Do"
msgstr "標記為待辦"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Mark as done"
msgstr "標記為完成"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_sidebar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_sidebar_done_button
msgid "Mark as not done"
msgstr "標記為未做完"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Mark the correct answer by checking the <b>correct</b> mark."
msgstr "<b>通過檢查正確的</b>標記來標記正確的答案。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Members"
msgstr "成員"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Members Views"
msgstr "成員瀏覽數"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_group_ids
msgid ""
"Members of those groups are automatically added as members of the channel."
msgstr "這些組的成員將自動添加為頻道成員。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_tree
msgid "Menu Entry"
msgstr "選單輸入"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error
msgid "Message Delivery error"
msgstr "訊息遞送錯誤"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__enroll_msg
msgid "Message explaining the enroll process"
msgstr "說明註冊過程的消息"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_ids
msgid "Messages"
msgstr "訊息"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_1_1
msgid "Methods"
msgstr "方式"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_2
msgid "Mighty Carrots"
msgstr "益處非凡的胡蘿蔔"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_2_1
msgid ""
"Mighty forest just don't appear in a few weeks. Learn how time made our "
"forests mighty and mysterious."
msgstr "強大的森林只是在幾週內不會出現。了解時間如何讓我們的森林變得強大而神秘。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Minutes"
msgstr "分鐘"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Missing \"Tag Group\" for creating a new \"Tag\"."
msgstr "缺少用於建立新“標籤”的“標籤組”。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Mobile sub-nav"
msgstr "流動子導航"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "More info"
msgstr "更多資訊"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_viewed
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Most Viewed"
msgstr "瀏覽最多的"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__most_voted
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Most Voted"
msgstr "最多投票的"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Most popular courses"
msgstr "最受歡迎的課程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活動截止時間"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "My Content"
msgstr "我的內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "My Courses"
msgstr "我的課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "My courses"
msgstr "我的課程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__name
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__name
#: model:ir.model.fields,field_description:website_slides.field_slide_tag__name
msgid "Name"
msgstr "名稱"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Nav"
msgstr "導航"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Need help? Review related content:"
msgstr "需要幫忙？請看看相關內容："

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/views/slide_channel_partner_list/slide_channel_partner_list_view.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
msgid "New"
msgstr "新增"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__publish_template_id
msgid "New Content Notification"
msgstr "新內容通知"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.snippet_options
msgid "New Content Ribbon"
msgstr "新內容絲帶"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_channel_action_add
msgid "New Course"
msgstr "新課程"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_published
msgid ""
"New {{ object.slide_category }} published on {{ object.channel_id.name }}"
msgstr "新的 {{ object.slide_category }} 已在 {{ object.channel_id.name }} 發佈"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Newest"
msgstr "最新"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Newest courses"
msgstr "最新課程"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Next"
msgstr "下一頁"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "下一個活動日曆事件"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活動截止日期"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_summary
msgid "Next Activity Summary"
msgstr "下一活動摘要"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_type_id
msgid "Next Activity Type"
msgstr "下一活動類型"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__next_slide_id
msgid "Next Lesson"
msgstr "下一課節"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_profile_small
msgid "Next rank:"
msgstr "下一個等級："

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_1
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_1
msgid "No"
msgstr "否"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "No Attendee has completed this course yet!"
msgstr "尚未有參加者完成本課程！"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "No Attendees Yet!"
msgstr "未有參加者。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "No Course created yet."
msgstr "尚未建立課程。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "No Notification"
msgstr "沒有通知"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_question_action_report
msgid "No Quiz data yet!"
msgstr "未有測驗數據。"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.rating_rating_action_slide_channel
msgid "No Reviews yet!"
msgstr "未有評價。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No completed courses yet!"
msgstr "未有課程已完成。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "No content was found using your search"
msgstr "找不到使用您的搜尋內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search"
msgstr "找不到符合您搜尋條件的課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No course was found matching your search."
msgstr "找不到符合您搜尋條件的課程."

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_slide_action_report
msgid "No data yet!"
msgstr "暫無資料！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "No leaderboard currently :("
msgstr "目前沒有排行榜:("

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_placeholder
msgid "No lessons are available yet."
msgstr "未有可用的課節。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "No ongoing courses yet!"
msgstr "未有進行中的課程。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "No presentation available."
msgstr "沒有可用的簡報。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
msgid "No results found for '"
msgstr "找不到:"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__none
msgid "None"
msgstr "無"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
msgid "Not Published"
msgstr "未發佈"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Not enough karma to comment"
msgstr "沒有足夠的活躍度進行評論"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Not enough karma to review"
msgstr "沒有足夠的活躍度進行審查"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
msgid "Notifications"
msgstr "通知"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of Actions"
msgstr "操作數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_article
msgid "Number of Articles"
msgstr "文章數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_slides
msgid "Number of Contents"
msgstr "內容數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_document
msgid "Number of Documents"
msgstr "文件數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_infographic
msgid "Number of Images"
msgstr "圖片數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_quiz
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_quiz
msgid "Number of Quizs"
msgstr "測驗數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__nbr_video
msgid "Number of Videos"
msgstr "影片數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__comments_count
msgid "Number of comments"
msgstr "留言數目"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of errors"
msgstr "錯誤數量"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_needaction_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要採取行動的訊息數目"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__message_has_error_counter
#: model:ir.model.fields,help:website_slides.field_slide_slide__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "有發送錯誤的郵件數量"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__questions_count
msgid "Numbers of Questions"
msgstr "問題數量"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Odoo"
msgstr "Odoo"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Odoo • Image and Text"
msgstr "Odoo • 圖片及文字"

#. module: website_slides
#: model:res.groups,name:website_slides.group_website_slides_officer
msgid "Officer"
msgstr "人員"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On Google Drive"
msgstr "在 Google 雲端硬碟"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__invite
msgid "On Invitation"
msgstr "應邀"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On Vimeo"
msgstr "在 Vimeo"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "On YouTube"
msgstr "在 YouTube"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Once you're done, don't forget to <b>Publish</b> your course."
msgstr "完成後，不要忘記<b>發佈</b>您的課程。"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel_partner__member_status__ongoing
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
msgid "Ongoing"
msgstr "進行中"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.user_profile_content
msgid "Ongoing Courses"
msgstr "進行中課程"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Only a single review can be posted per course."
msgstr "每個課程只可發佈一則評論。"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__enroll__public
msgid "Open"
msgstr "開啟"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Operation not supported"
msgstr "不支援該操作"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"發送電子郵件時要選擇的可選翻譯語言（ISO 代碼）。如果未設置，將使用英文版本。這通常應該是提供適當語言的佔位符表達式，例如{{ "
"object.partner_id.lang }}。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Options"
msgstr "選項"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__pdf
msgid "PDF"
msgstr "PDF"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__document_binary_content
msgid "PDF Content"
msgstr "PDF 內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Paid Courses"
msgstr "已付費課程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__partner_id
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__partner_id
msgid "Partner"
msgstr "業務夥伴"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__partner_has_new_content
msgid "Partner Has New Content"
msgstr "合作夥伴有新內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_pivot
msgid "Pivot"
msgstr "樞紐分析表"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Please"
msgstr "請"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid ""
"Please <a href=\"/web/login?redirect=%(url)s\">login</a> or <a "
"href=\"/web/signup?redirect=%(url)s\">create an account</a> to vote for this"
" lesson"
msgstr ""
"請<a href=\"/web/login?redirect=%(url)s\">登入</a>或<a "
"href=\"/web/signup?redirect=%(url)s\">建立帳戶</a>，以對本課程投票"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid ""
"Please <a href=\"/web/login?redirect=%(url)s\">login</a> to vote for this "
"lesson"
msgstr "請<a href=\"/web/login?redirect=%(url)s\">登入</a>以對本課程投票"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Please enter a valid Vimeo video link"
msgstr "請輸入有效的 Vimeo 影片連結"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Please enter valid Google Drive Link"
msgstr "請輸入有效的 Google 雲端硬碟連結"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.js:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Please enter valid email(s)"
msgstr "請輸入有效的電郵地址"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz_question_form.js:0
msgid "Please fill in the question"
msgstr "請填寫問題"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
msgid "Please select at least one recipient."
msgstr "請至少選擇一位收件人。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Points Rewards"
msgstr "積分獎勵"

#. module: website_slides
#: model:gamification.badge,name:website_slides.badge_data_course
#: model:gamification.goal.definition,name:website_slides.badge_data_course_goal
msgid "Power User"
msgstr "超級使用者"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Powered by"
msgstr "官方技術支援"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_of_channel_ids
msgid "Prerequisite Of"
msgstr "是以下課程的先修課程："

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__prerequisite_channel_ids
msgid "Prerequisite courses to complete before accessing this one."
msgstr "存取此課程之前需要完成的先修課程。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__prerequisite_channel_ids
msgid "Prerequisites"
msgstr "先修課程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__slide_id
#: model:slide.slide,name:website_slides.slide_slide_demo_4_12
msgid "Presentation"
msgstr "簡報"

#. module: website_slides
#: model:mail.message.subtype,description:website_slides.mt_channel_slide_published
#: model:mail.message.subtype,name:website_slides.mt_channel_slide_published
msgid "Presentation Published"
msgstr "簡報已發佈"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Preview"
msgstr "預覽"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Previous"
msgstr "上一頁"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Private"
msgstr "私人"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Private Course"
msgstr "私人課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Progress"
msgstr "專案進度"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_xp_progress_bar.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.slide_main
msgid "Progress bar"
msgstr "進度列"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__promoted_slide_id
msgid "Promoted Slide"
msgstr "推廣的投影片"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Public Views"
msgstr "公眾瀏覽數目"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_searchbar_input_snippet_options
msgid "Publication Date"
msgstr "發佈日期"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__date_published
msgid "Publish Date"
msgstr "發佈日期"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_pages_kanban_view
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Published"
msgstr "已發佈"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Published Contents"
msgstr "已發佈內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Published Date"
msgstr "發佈日期"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Publishing is restricted to the responsible of training courses or members "
"of the publisher group for documentation courses"
msgstr "出版僅限於負責培訓課程或出版商組成員的文件課程"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__question_id
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Question"
msgstr "提問"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_question__question
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Question Name"
msgstr "問題名稱"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__question_ids
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Questions"
msgstr "問題"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__quiz
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__quiz
#: model:slide.channel.tag,name:website_slides.slide_channel_tag_other_0
#: model_terms:ir.ui.view,arch_db:website_slides.gamification_karma_tracking_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Quiz"
msgstr "測驗"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Quiz Completed"
msgstr "測驗已完成"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Quiz Demo Data"
msgstr "測驗展示資料"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "Quiz Set Uncompleted"
msgstr "設為未完成的測驗"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__quiz_attempts_count
msgid "Quiz attempts count"
msgstr "測試嘗試計數"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.slide_question_action_report
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_quizzes
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_tree_report
msgid "Quizzes"
msgstr "測驗"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_stars
msgid "Rating Average (Stars)"
msgstr "平均評分（星級）"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "平均評分文字"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "最新回饋評分"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_image
msgid "Rating Last Image"
msgstr "最新評分圖像"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_value
msgid "Rating Last Value"
msgstr "最新評分值"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "評級滿意度"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_last_text
msgid "Rating Text"
msgstr "評分文字"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_count
msgid "Rating count"
msgstr "評分數"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "Rating of %s"
msgstr "等級 %s"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__rating_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__rating_ids
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_form_slides
msgid "Ratings"
msgstr "評分"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_karma_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_karma
msgid "Reach 2000 XP"
msgstr "達到2000 XP"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Reach new heights"
msgstr "達到新的記錄"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__partner_ids
msgid "Recipients"
msgstr "接收者"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/activity/activity_patch.xml:0
msgid "Refuse Access"
msgstr "拒絕存取"

#. module: website_slides
#: model:gamification.challenge,name:website_slides.badge_data_register_challenge
#: model_terms:gamification.badge,description:website_slides.badge_data_register
msgid "Register to the platform"
msgstr "註冊平台"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_documentation
msgid "Related"
msgstr "相關的"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Remove the answer comment"
msgstr "刪除答案評論"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Remove this answer"
msgstr "刪除此答案"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__render_model
msgid "Rendering Model"
msgstr "呈現模型"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report
msgid "Reporting"
msgstr "報告"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Request Access."
msgstr "申請進入"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Request sent!"
msgstr "請求已發送！"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Reset"
msgstr "重設"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide_resource.py:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__data
msgid "Resource"
msgstr "資源"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide_resource.py:0
msgid ""
"Resource %(resource_name)s is a link and should not contain a data file"
msgstr "資源 %(resource_name)s 是一個連結，不應包含數據檔案"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__resource_type
msgid "Resource Type"
msgstr "資源類型"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__user_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_user_id
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Responsible"
msgstr "負責人"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__activity_user_id
msgid "Responsible User"
msgstr "責任使用者"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Responsible already contacted."
msgstr "已經聯繫上負責人。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,help:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_id
msgid "Restrict to a specific website."
msgstr "只限特定網站使用。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__source_type__external
msgid "Retrieve from Google Drive"
msgstr "從 Google 雲端硬碟讀取"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_install_module.xml:0
msgid "Retry"
msgstr "重試"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.profile_access_denied
msgid "Return to the course."
msgstr "返回課程。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Review Course"
msgstr "評價課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
msgid "Review Date"
msgstr "評價日期"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.rating_rating_action_slide_channel
#: model:ir.ui.menu,name:website_slides.website_slides_menu_report_reviews
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_graph_slide_channel
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Reviews"
msgstr "評價"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_fourth_attempt_reward
msgid "Reward: every attempt after the third try"
msgstr "獎勵：第三次嘗試後的每一次嘗試"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_first_attempt_reward
msgid "Reward: first attempt"
msgstr "獎勵：第一次嘗試"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_second_attempt_reward
msgid "Reward: second attempt"
msgstr "獎勵：第二次嘗試"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__quiz_third_attempt_reward
msgid "Reward: third attempt"
msgstr "獎勵：第三次嘗試"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Rewards"
msgstr "獎勵"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__is_seo_optimized
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 已最佳化"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__message_has_sms_error
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__message_has_sms_error
msgid "SMS Delivery error"
msgstr "簡訊發送錯誤"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__embed_code_external
msgid ""
"Same as 'Embed Code' but used to embed the content on an external website."
msgstr "與「嵌入程式碼」相同，但用於將內容嵌入到外部網站上。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_sample
msgid "Sample"
msgstr "樣品"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: code:addons/website_slides/static/src/js/slides_category_add.js:0
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Save"
msgstr "儲存"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Save and Publish"
msgstr "儲存並發佈"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Save your presentations or documents as PDF files and upload them."
msgstr "將您的展示文件或檔案儲存為 PDF 文件並上傳。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.rating_rating_view_tree_slide_channel
msgid "Score"
msgstr "分數"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search"
msgstr "搜尋"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Search Contents"
msgstr "搜尋內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Search courses"
msgstr "搜索課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Search in content"
msgstr "搜尋的內容"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Second Try"
msgstr "第二次嘗試"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__category_id
msgid "Section"
msgstr "章節"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Section Subtitle"
msgstr "段落子標題"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/category_add_dialog/category_add_dialog.xml:0
msgid "Section name"
msgstr "章節名稱"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__access_token
msgid "Security Token"
msgstr "安全金鑰"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/res_users.py:0
msgid "See our eLearning"
msgstr "看看我們的網上學習平台"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid "Select <b>Course</b> to create it and manage it."
msgstr "選擇<b>課程</b>來建立與管理。"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__promote_strategy__specific
msgid "Select Manually"
msgstr "手動選擇"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Select or create a category"
msgstr "選擇或建立類別"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.js:0
msgid "Select or create a tag"
msgstr "選擇或建立標籤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.js:0
msgid "Select or create a tag group"
msgstr "選擇或建立標籤組"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_select_tags.xml:0
msgid "Select or create tags"
msgstr "選擇或建立標籤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Select the correct answer below:"
msgstr "選擇下方正確答案："

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Sell access to your courses on your website and track revenues."
msgstr "在你的網站上銷售你課程的存取權限，並追蹤收入。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_config_settings__module_website_sale_slides
msgid "Sell on eCommerce"
msgstr "在電子商務銷售"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Send"
msgstr "發送"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__send_email
msgid "Send Email"
msgstr "發送電子郵件"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_channel_completed
msgid "Sent to attendees once they've completed the course"
msgstr "參加者完成課程後發送給他們"

#. module: website_slides
#: model:mail.template,description:website_slides.slide_template_published
msgid "Sent to attendees when new course is published"
msgstr "新課程發佈時發送給參加者"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_slide_channel_enroll
msgid "Sent to attendees when they are added to a course"
msgstr "參加者被加入至課程時發送給他們"

#. module: website_slides
#: model:mail.template,description:website_slides.mail_template_slide_channel_invite
msgid "Sent to potential attendees to check out the course."
msgstr "發送給潛在參加者以查看課程。"

#. module: website_slides
#: model:mail.template,description:website_slides.slide_template_shared
msgid "Sent when attendees share the course by email"
msgstr "參加者以電郵分享課程時發送"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__seo_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__seo_name
msgid "Seo name"
msgstr "SEO 名稱"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_answer__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_question__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__sequence
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__sequence
msgid "Sequence"
msgstr "序列號"

#. module: website_slides
#: model:ir.actions.act_window,name:website_slides.website_slides_action_settings
#: model:ir.ui.menu,name:website_slides.website_slides_menu_config_settings
msgid "Settings"
msgstr "設定"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_fullscreen
msgid "Share"
msgstr "分享"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_sidebar
msgid "Share Channel"
msgstr "分享頻道"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_link
msgid "Share Link"
msgstr "分享連結"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__share_slide_template_id
msgid "Share Template"
msgstr "分享模板"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_header
msgid "Share This Content"
msgstr "分享此內容"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_share_url
msgid "Share URL"
msgstr "分享網址"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "Share by Email"
msgstr "以電郵分享"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Facebook"
msgstr "分享至 Facebook"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on LinkedIn"
msgstr "分享至 LinkedIn"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Pinterest"
msgstr "分享至 Pinterest"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_modal_body
msgid "Share on Social Media"
msgstr "分享至社交媒體"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on Whatsapp"
msgstr "分享至 WhatsApp"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_share_social
msgid "Share on X"
msgstr "分享至 X"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Share this Content"
msgstr "分享此內容"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Share this Course"
msgstr "分享此課程"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "Sharing is caring!"
msgstr "分享就是關愛！"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__sheet
msgid "Sheet (Excel, Google Sheet, ...)"
msgstr "試算表（Excel、Google 試算表等）"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__description_short
msgid "Short Description"
msgstr "簡短說明"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__channel_visibility
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_visibility
msgid "Show Course To"
msgstr "顯示課程給"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_4
msgid "Show your newly mastered knowledge!"
msgstr "展示你新掌握的知識！"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Sign Up!"
msgstr "註冊"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Sign in and join the course to verify your answers!"
msgstr "登入並加入課程以驗證您的答案！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "Sign up"
msgstr "註冊"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__visibility__connected
msgid "Signed In"
msgstr "已登入"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid ""
"Skill up and have an impact! Your business career starts here.<br/>Time to "
"start a course."
msgstr "提高技能並產生影響！您的商業生涯從這裡開始。<br/>是時候開始課程了。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_resource__slide_id
msgid "Slide"
msgstr "幻燈片"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "投影片 / 合作夥伴裝飾 m2m"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_icon_class
msgid "Slide Icon fa-class"
msgstr "投影片圖示fa-class"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_answer
msgid "Slide Question's Answer"
msgstr "投影片問題的答案"

#. module: website_slides
#: model:ir.model,name:website_slides.model_slide_tag
msgid "Slide Tag"
msgstr "投影片標籤"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_type
msgid "Slide Type"
msgstr "投影片類型"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_partner_ids
msgid "Slide User Data"
msgstr "投影片使用者資料"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.embed_slide
msgid "Slide image"
msgstr "投影片圖片"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid ""
"Slide with questions must be marked as done when submitting all good answers"
" "
msgstr "提交所有好答案時，必須將帶問題的投影片標記為已完成"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/website_slides_fullscreen.xml:0
#: model:ir.model,name:website_slides.model_slide_slide
msgid "Slides"
msgstr "投影片"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__slides
msgid "Slides (PowerPoint, Google Slides, ...)"
msgstr "投影片（PowerPoint、Google 簡報等）"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__slide_ids
msgid "Slides and categories"
msgstr "投影片和類別"

#. module: website_slides
#: model_terms:slide.channel,description:website_slides.slide_channel_demo_6_furn3
#: model_terms:slide.channel,description_short:website_slides.slide_channel_demo_6_furn3
msgid "So much amazing certification."
msgstr "這麼多驚人的認證"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Sort by"
msgstr "排序"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__source_type
msgid "Source Type"
msgstr "來源類型"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Start Course"
msgstr "課程開始"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/slide_share_dialog.xml:0
msgid "Start at Page"
msgstr "開始頁面"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "Start this Course"
msgstr "開始此課程"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid "Start with the customer – find out what they want and give it to them."
msgstr "從客戶角度出發：找出他們想要甚麼，並盡量配合提供。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Start your online course today!"
msgstr "立即開始您的線上課程！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Status"
msgstr "狀態"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"根據活動的狀態 \n"
" 逾期：已經超過截止日期 \n"
" 現今：活動日期是當天 \n"
" 計劃：未來活動。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_invite__subject
msgid "Subject"
msgstr "主題"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Subject..."
msgstr "主題..."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.js:0
msgid "Subscribe"
msgstr "訂閱"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information"
msgstr "訂閱者資訊"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__user_membership_id
msgid "Subscriber information for the current logged in user"
msgstr "目前登入使用者的訂閱資訊"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__partner_ids
msgid "Subscribers"
msgstr "訂閱者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__slide_partner_ids
msgid "Subscribers information"
msgstr "訂閱者資訊"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__slide_type
msgid ""
"Subtype of the slide category, allows more precision on the actual file type"
" / source type."
msgstr "投影片類別的子類型，可讓實際檔案類型 / 來源類型的分類更精確。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "Succeed and gain karma"
msgstr "成功達標並提升活躍度"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_form
msgid "Tag"
msgstr "標籤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/course_tag_add_dialog/course_tag_add_dialog.xml:0
msgid "Tag Group"
msgstr "標籤分組"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "Tag Name"
msgstr "標籤名稱"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag__color
msgid ""
"Tag color used in both backend and website. No color means no display in "
"kanban or front-end, to distinguish internal tags from public categorization"
" tags"
msgstr "後端和網站中使用的標籤顏色。無顏色表示看板或前端不顯示，用於區分內部標籤和公共分類標籤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__tag_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__tag_ids
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_data_other
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_search
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
msgid "Tags"
msgstr "標籤"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Tags..."
msgstr "標籤..."

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Take Quiz"
msgstr "參加測驗"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_1_gard1
msgid "Taking care of Trees"
msgstr "照顧樹木"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_4_1
msgid "Technical Drawings"
msgstr "技術圖紙"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_10
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_11
msgid "Technical drawing"
msgstr "技術圖紙"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_2
msgid "Test Yourself"
msgstr "測試自己"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_0_4
msgid "Test your knowledge"
msgstr "測試你的學習成果"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_3
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_3
msgid "Test your knowledge!"
msgstr "測試你的知識！"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Test your students with small Quizzes"
msgstr "利用小測驗，測試你的學生。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"The <b>Duration</b> of the lesson is based on the number of pages of your "
"document. You can change this number if your attendees will need more time "
"to assimilate the content."
msgstr "課程的<b>持續時間</b>基於文件的頁數。如果您的與會者需要更多時間來理解內容，您可以更改此數字。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"The <b>Title</b> of your lesson is autocompleted but you can change it if "
"you want.</br>A <b>Preview</b> of your file is available on the right side "
"of the screen."
msgstr "<b>您的課程標題</b>是自動完成的，但您可以根據需要進行更改。</br>屏幕右側提供文件<b>預覽</b>"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_check_enroll
msgid ""
"The Enroll Policy should be set to 'On Invitation' when visibility is set to"
" 'Course Attendees'"
msgstr "若課程設定為只向「課程參加者」顯示，註冊政策應設為「只限獲邀者」"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The Google Drive link can be obtained by using the 'share' button in the "
"Google interface."
msgstr "可使用 Google 介面的「分享」按鈕，獲取 Google 雲端硬碟連結。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The Google Drive link to use here can be obtained by clicking the \"Share\" "
"button in the Google interface."
msgstr "此處使用的 Google 雲端硬碟連結，可在 Google 介面按「分享」按鈕獲取。"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_channel_partner_check_completion
msgid ""
"The completion of a channel is a percentage and should be between 0% and "
"100."
msgstr "頻道完成率以百分比表示，應在 0% 至 100% 之間。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "The contact associated with this invitation does not seem to be valid."
msgstr "與此邀請相關的聯絡人似乎無效。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr "任何人都可以存取該課程：使用者無需加入頻道即可存取課程內容。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description_short
msgid "The description that is displayed on the course card"
msgstr "課程卡片上顯示的說明"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__description
msgid ""
"The description that is displayed on top of the course page, just below the "
"title"
msgstr "顯示在課程頁面頂部、標題下方的描述"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,help:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_url
msgid "The full URL to access the document through the website."
msgstr "通過網站存取此文件的完整網址."

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__can_self_mark_completed
msgid "The slide can be marked as completed even without opening it"
msgstr "無需開啟投影片也可標記為完成"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__can_self_mark_uncompleted
msgid "The slide can be marked as not completed and the progression"
msgstr "投影片可標記為未完成，以及標記進度"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"The video link to input here can be obtained by using the 'share' button in "
"the Vimeo interface."
msgstr "此處輸入的影片連結，可在 Vimeo 介面使用「分享」按鈕獲得。"

#. module: website_slides
#: model:ir.model.constraint,message:website_slides.constraint_slide_slide_partner_check_vote
msgid "The vote must be 1, 0 or -1."
msgstr "投票數值必須為 1、0 或 -1。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Then, go into the file permissions and set it as \"Anyone with the link\"."
msgstr "然後，前往文件權限，將其設為「任何擁有連結的人」。"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_theory
msgid "Theory"
msgstr "理論"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "There are no comments for now."
msgstr "暫時沒有留言。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid ""
"There are no comments for now. Earn more Karma to be the first to leave a "
"comment."
msgstr "暫時沒有留言。要成為第一個發表評論的人，必須賺取更多貢獻值。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "There was an error validating this quiz."
msgstr "驗證此測試時出錯。"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__url
msgid "Third Party Website URL"
msgstr "第三方網站網址"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "Third Try"
msgstr "第三次嘗試"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_answer__comment
msgid "This comment will be displayed to the user if they select this answer"
msgstr "如果用戶選擇此答案，將向用戶顯示此備註"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This course does not exist."
msgstr "此課程不存在。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid ""
"This course is not published. Attendees may not be able to access its "
"contents."
msgstr "此課程尚未發佈。參加者可能無法存取其內容。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "This course is private."
msgstr "這門課程是不公開的。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This identification link does not seem to be valid."
msgstr "此識別連結似乎無效。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link has an invalid hash."
msgstr "此邀請連結的雜湊值無效。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link has expired."
msgstr "此邀請連結已過期。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This invitation link is not for this contact."
msgstr "邀請連結不是給這名聯絡人的。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "This is the correct answer"
msgstr "這是正確答案"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "This is the correct answer, congratulations"
msgstr "這是正確答案，恭喜"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_question.py:0
msgid ""
"This question must have at least one correct answer and one incorrect "
"answer."
msgstr "此問題必須有最少一個正確答案以及一個錯誤答案。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "This quiz is already done. Retaking it is not possible."
msgstr "這個測驗已經做完了, 重新開始是不可能的。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This slide can not be marked as completed."
msgstr "此投影片不可標記為已完成。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This slide can not be marked as uncompleted."
msgstr "此投影片不可標記為未完成。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "This video already exists in this channel on the following content: %s"
msgstr "頻道已存在此影片，有關內容如下： %s"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"Through Google Drive, we support most common types of documents.\n"
"            Including regular documents (Google Doc, .docx), Sheets (Google Sheet, .xlsx), PowerPoints, ..."
msgstr ""
"我們透過 Google 雲端硬碟，支援最常見的文件類型。\n"
"            包括一般文書（Google 文件、.docx）、試算表（Google 試算表、.xlsx）、PowerPoint 簡報等。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__name
msgid "Title"
msgstr "標題"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "Toggle navigation"
msgstr "切換導覽"

#. module: website_slides
#: model:slide.tag,name:website_slides.slide_tag_demo_tools
msgid "Tools"
msgstr "工具"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_5_0
msgid "Tools and Methods"
msgstr "工具和方法"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_5_0
msgid "Tools you will need to complete this course."
msgstr "完成本課程所需的工具。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_tree
msgid "Total"
msgstr "總計"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Total Attendees"
msgstr "參加者總數"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
msgid "Total Completed"
msgstr "已完成總數"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_tree_report
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Duration"
msgstr "總時長"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Questions"
msgstr "問題總數"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__total_slides
msgid "Total Slides"
msgstr "投影片總數"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides.slide_slide_view_tree_report
msgid "Total Views"
msgstr "總瀏覽次數"

#. module: website_slides
#: model:ir.model,name:website_slides.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "跟踪活躍度變化"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_channel__channel_type__training
msgid "Training"
msgstr "培訓"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_0
msgid "Trees"
msgstr "樹木"

#. module: website_slides
#: model:slide.channel,name:website_slides.slide_channel_demo_2_gard2
msgid "Trees, Wood and Gardens"
msgstr "樹木、木材和花園"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "Triumphant hero"
msgstr "凱旋的英雄"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Type"
msgstr "類型"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "記錄的異常活動的類型。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_slide__url
msgid "URL of the Google Drive file or URL of the YouTube video"
msgstr "Google 雲端硬碟檔案或 YouTube 影片的網址"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/wizard/slide_channel_invite.py:0
msgid "Unable to post message, please configure the sender's email address."
msgstr "未能張貼訊息。請配置發件人電郵地址。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_partner_view_tree
msgid "Unarchive"
msgstr "取消封存"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "Uncategorized"
msgstr "未歸類"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_5_0
msgid "Unforgettable Tools"
msgstr "難忘的工具"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_embed.py:0
msgid "Unknown Website"
msgstr "未知網站"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "Unknown error"
msgstr "未知的錯誤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
msgid "Unknown error, try again."
msgstr "未知錯誤，請重試。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.lesson_card
msgid "Unpublished"
msgstr "未公開"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "Update"
msgstr "更新"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "Update all your Attendees at once through mass mailings."
msgstr "使用群發電郵，即時同步通知所有參加者。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
msgid "Upload Document"
msgstr "上載文件"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__upload_group_ids
msgid "Upload Groups"
msgstr "上載組"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__source_type__local_file
msgid "Upload from Device"
msgstr "從裝置上載"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_id
msgid "Uploaded by"
msgstr "上載者"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "Uploading document ..."
msgstr "正在上載文件⋯"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.action_slide_tag
msgid "Use Content Tags to classify your Content."
msgstr "使用內容標籤，對你的內容進行分類。"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_tag_group_action
msgid "Use Course Groups to classify and organize your Courses."
msgstr "使用課程組別，整理你的課程並加以分類。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_invite_view_form
msgid "Use template"
msgstr "使用範本"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__tag_ids
msgid "Used to categorize and filter displayed channels/courses"
msgstr "用於分類和過濾顯示的頻道/課程"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__color
msgid "Used to decorate kanban view"
msgstr "用於裝飾看板"

#. module: website_slides
#: model:ir.model,name:website_slides.model_res_users
msgid "User"
msgstr "使用者"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__user_vote
msgid "User vote"
msgstr "使用者投票"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "Validation error"
msgstr "驗證錯誤"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_category__video
msgid "Video"
msgstr "影片"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__video_url
msgid "Video Link"
msgstr "影片連結"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__video_source_type
msgid "Video Source"
msgstr "影片來源"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__vimeo_id
msgid "Video Vimeo ID"
msgstr "影片 Vimeo 識別碼"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__youtube_id
msgid "Video YouTube ID"
msgstr "影片 YouTube 識別碼"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__nbr_video
msgid "Videos"
msgstr "影片"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "View"
msgstr "檢視"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_cards
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
#: model_terms:ir.ui.view,arch_db:website_slides.slides_home_user_achievements_small
#: model_terms:ir.ui.view,arch_db:website_slides.toggle_leaderboard
msgid "View all"
msgstr "查看全部"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "View course"
msgstr "查看課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "Views"
msgstr "視圖"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_card
msgid "Views •"
msgstr "瀏覽數 •"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__vimeo
msgid "Vimeo"
msgstr "Vimeo"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__vimeo_video
msgid "Vimeo Video"
msgstr "Vimeo 影片"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_published
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_published
msgid "Visible on current website"
msgstr "在當前網站可見"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_views
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "Visits"
msgstr "訪問"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__karma_slide_vote
#: model:ir.model.fields,field_description:website_slides.field_slide_slide_partner__vote
msgid "Vote"
msgstr "投票"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__total_votes
msgid "Votes"
msgstr "投票"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "Votes and comments are disabled for this course"
msgstr "本課程禁止投票和評論"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_search
msgid "Waiting for validation"
msgstr "等待驗證"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "Want to test and certify your students?"
msgstr "想要測試及認證你的學生嗎？"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_3_1
msgid "Watching the master(s) at work"
msgstr "觀看大師工作"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_1_4
msgid ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say!"
msgstr ""
"We had a little chat with Harry Potted, sure he had interesting things to "
"say!"

#. module: website_slides
#: model:ir.model,name:website_slides.model_website
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_partner__channel_website_id
#: model:ir.model.fields,field_description:website_slides.field_slide_embed__website_name
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_id
msgid "Website"
msgstr "網站"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__gamification_challenge__challenge_category__slides
msgid "Website / Slides"
msgstr "網站/投影片"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_message_ids
msgid "Website Messages"
msgstr "網站資訊"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_channel_tag_group__website_url
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_url
msgid "Website URL"
msgstr "網站網址"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel__website_message_ids
#: model:ir.model.fields,help:website_slides.field_slide_slide__website_message_ids
msgid "Website communication history"
msgstr "網站溝通記錄"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_description
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_description
msgid "Website meta description"
msgstr "網站 meta 說明"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_keywords
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_keywords
msgid "Website meta keywords"
msgstr "網站meta關鍵字"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_title
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_title
msgid "Website meta title"
msgstr "網站meta標題"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_slide_channel__website_meta_og_img
#: model:ir.model.fields,field_description:website_slides.field_slide_slide__website_meta_og_img
msgid "Website opengraph image"
msgstr "網站 opengraph 圖片"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Welcome on your course's home page. It's still empty for now. Click on "
"\"<b>New</b>\" to write your first course."
msgstr "歡迎訪問您的課程主頁。目前它仍然是空的。點選<b>新建</b>來編輯您的第一門課程。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "What does"
msgstr "什麼會"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_0
msgid "What is a strawberry?"
msgstr "草莓是甚麼？"

#. module: website_slides
#: model:slide.question,question:website_slides.slide_slide_demo_0_4_question_1
msgid "What is the best tool to dig a hole for your plants?"
msgstr "為植物剷泥挖土的最佳工具是甚麼？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "What types of documents do we support?"
msgstr "我們支援哪些類型文件？"

#. module: website_slides
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_2
msgid "What was the question again?"
msgstr "再問一次那問題？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "When using local files, we only support PDF files."
msgstr "若使用本地文件，我們只支援 PDF 檔案。"

#. module: website_slides
#: model:ir.model.fields,help:website_slides.field_slide_channel_invite__enroll_mode
msgid ""
"Whether invited partners will be added as enrolled. Otherwise, they will be "
"added as invited."
msgstr "是否將受邀請合作夥伴加入為已報名參加者。若否，他們將被加入為受邀者。"

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_4_1
msgid ""
"Which wood type is best for my solid wood furniture? That's the question we "
"help you answer in this video!"
msgstr "哪種木材最適合我的實木家具？ 這就是我們在本影片中幫助你解答的問題！"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"With Quizzes you can keep your students focused and motivated by answering "
"some questions and gaining some karma points"
msgstr "通過測驗，您可以通過回答一些問題並獲得一些活躍度來讓您的學生保持專注與積極"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_2_1
msgid "Wood"
msgstr "木材"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_3_1
msgid "Wood Bending With Steam Box"
msgstr "帶蒸汽箱的木材彎曲"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_2_3
msgid "Wood Characteristics"
msgstr "木材特性"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_slide_demo_4_1
msgid "Wood Types"
msgstr "木材類型"

#. module: website_slides
#: model:slide.slide,name:website_slides.slide_category_demo_3_0
msgid "Working with Wood"
msgstr "使用木材"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product or services. <br>To be "
"successful your content needs to be useful to your readers."
msgstr "寫一兩段文字，描述你的產品或服務。<br>成功的內容必須對讀者有用。"

#. module: website_slides
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_1_4
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_2_1
#: model_terms:slide.slide,html_content:website_slides.slide_slide_demo_4_0
msgid ""
"Write one or two paragraphs describing your product, services or a specific "
"feature.<br> To be successful your content needs to be useful to your "
"readers."
msgstr "寫一兩段文字，描述你的產品或服務，或個別特色及功能。<br>成功的內容必須對讀者有用。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "XP"
msgstr "經驗值"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_enroll_email.js:0
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_1_4_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_0_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_2_0_question_1_0
#: model:slide.answer,text_value:website_slides.slide_slide_demo_5_3_question_0_0
msgid "Yes"
msgstr "是"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid ""
"You are not allowed to add members to this course. Please contact the course"
" responsible or an administrator."
msgstr "你不可將新成員加入至此課程。請聯絡課程負責人或系統管理員。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"You can add <b>comments</b> on answers. This will be visible with the "
"results if the user select this answer."
msgstr "您可以對答案<b>添加評論。</b>如果用戶選擇此答案，這將與結果一起顯示。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "You can add questions to this quiz in the 'Quiz' tab."
msgstr "可在「測驗」分頁中，新增問題至此測驗。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"You can either upload a file from your computer or insert a Google Drive "
"link."
msgstr "你可選擇由電腦上載檔案，或插入 Google 雲端硬碟連結。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "You can not upload password protected file."
msgstr "您無法上傳密碼保護的文件。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You cannot add tags to this course."
msgstr "您不能為此課程添加標籤。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "You cannot mark a slide as completed if you are not among its members."
msgstr "非成員不可將投影片標記為已完成。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide as uncompleted if you are not among its members."
msgstr "非成員不可將投影片標記為未完成。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid "You cannot mark a slide as viewed if you are not among its members."
msgstr "非成員不可將投影片標記為已觀看。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide quiz as completed if you are not among its members "
"or it is unpublished."
msgstr "若你不是成員，或測驗尚未發佈，便不可將投影片測驗標記為已完成。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"You cannot mark a slide quiz as not completed if you are not among its "
"members or it is unpublished."
msgstr "若你不是成員，或測驗尚未發佈，便不可將投影片測驗標記為未完成。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You cannot upload on this channel."
msgstr "您無法在此頻道上傳。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "You did it!"
msgstr "你做到了！"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/controllers/main.py:0
msgid "You do not have permission to access this course."
msgstr "你沒有權限存取此課程。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You don't have access to this lesson"
msgstr "您無權存取本課程"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You don't have enough karma to vote"
msgstr "您沒有足夠的活躍度進行投票"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_quiz_finish_dialog/slide_quiz_finish_dialog.xml:0
msgid "You gained"
msgstr "您獲得了"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_join.js:0
msgid "You have already joined this channel"
msgstr "您已加入此頻道"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_invite
msgid "You have been invited to check out {{ object.channel_id.name }}"
msgstr "你獲邀請查看 {{ object.channel_id.name }}"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_slide_channel_enroll
msgid "You have been invited to join {{ object.channel_id.name }}"
msgstr "您已受邀加入 {{ object.channel_id.name }}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "You have been invited to this course."
msgstr "你獲邀請參加本課程。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "You have to sign in before"
msgstr "您必須先登入"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "You may now participate in our eLearning."
msgstr "您現在可以參加我們的線上課程。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_course_quiz.js:0
msgid "You must be logged to submit the quiz."
msgstr "您必須登入後才能提交測驗。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/slides_slide_like.js:0
msgid "You must be member of this course to vote"
msgstr "您必須是本課程的成員才能投票"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "You need to join this course to access \""
msgstr "須加入本課程才可存取 \""

#. module: website_slides
#: model_terms:slide.slide,description:website_slides.slide_slide_demo_0_2
msgid "You won't believe those facts about carrots."
msgstr "你不會相信那些關於胡蘿蔔的事實。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "You're enrolled"
msgstr "您已經註冊了"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__video_source_type__youtube
msgid "YouTube"
msgstr "YouTube"

#. module: website_slides
#: model:ir.model.fields.selection,name:website_slides.selection__slide_slide__slide_type__youtube_video
msgid "YouTube Video"
msgstr "YouTube 影片"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.mail_notification_channel_invite
msgid "Your"
msgstr "您的"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_level
msgid "Your Level"
msgstr "你的等級"

#. module: website_slides
#: model:slide.channel.tag.group,name:website_slides.slide_channel_tag_group_role
msgid "Your Role"
msgstr "你的角色"

#. module: website_slides
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_overview
#: model_terms:ir.actions.act_window,help:website_slides.slide_channel_action_report
msgid ""
"Your eLearning platform starts here!<br>\n"
"                    Upload content, set up rewards, manage attendees..."
msgstr ""
"你的電子學習平台從這裏開始！<br>\n"
"                    上載內容、設立獎勵、管理參加者⋯"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your file could not be found on Google Drive, please check the link and/or "
"privacy settings"
msgstr "在 Google 雲端硬碟上找不到你的文件。請檢查連結及/或私隱設定"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/tours/slides_tour.js:0
msgid ""
"Your first section is created, now it's time to add lessons to your course. "
"Click on <b>Add Content</b> to upload a document, create an article or link "
"a video."
msgstr "你已建立第一個章節，現在是時候向你的課程加入課節了。請按「<b>新增內容</b>」以上載文件、建立文章，或插入影片連結。"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your video could not be found on Vimeo, please check the link and/or privacy"
" settings"
msgstr "在 Vimeo 上找不到你的影片，請檢查連結及/或私隱設定"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_slide.py:0
msgid ""
"Your video could not be found on YouTube, please check the link and/or "
"privacy settings"
msgstr "在 YouTube 上找不到你的影片，請檢查連結及/或私隱設定"

#. module: website_slides
#. odoo-python
#: code:addons/website_slides/models/slide_channel.py:0
msgid "a course"
msgstr "一個課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.achievement_card
msgid "achieved"
msgstr "已完成"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.courses_all
#: model_terms:ir.ui.view,arch_db:website_slides.courses_home
msgid "and join this Community"
msgstr "並加入此社群"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "anyway"
msgstr "無論如何"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog_select.xml:0
msgid "app."
msgstr "應用程式。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "breadcrumb"
msgstr "頁面路徑"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "by email."
msgstr "以電郵傳送。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "course"
msgstr "課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "courses"
msgstr "課程"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "create an account"
msgstr "建立一個帳戶"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "direct access"
msgstr "直接存取"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g \"https://drive.google.com/file/...\""
msgstr "例：https://drive.google.com/file/…"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_source_types.xml:0
msgid "e.g \"https://www.youtube.com/watch?v=ebBez6bcSEc\""
msgstr "例：https://www.youtube.com/watch?v=ebBez6bcSEc"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g \"www.youtube.com/watch?v=ebBez6bcSEc\""
msgstr "例：www.youtube.com/watch?v=ebBez6bcSEc"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_tag_tree
msgid "e.g 'HowTo'"
msgstr "例：技巧教學"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "e.g. \"15\""
msgstr "例：15"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_channel_form
msgid "e.g. \"Computer Science for kids\""
msgstr "例：兒童計算機科學"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/category_add_dialog/category_add_dialog.xml:0
msgid "e.g. \"Introduction\""
msgstr "例：入門簡介"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "e.g. \"Which animal cannot fly?\""
msgstr "例：甚麼動物不會飛？"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz_create.xml:0
msgid "e.g. \"{{placeholder || \"another animal\"}}\""
msgstr "例：{{placeholder || \"其他動物\"}}"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_form_add
msgid "e.g. Computer Science for kids"
msgstr "例：兒童計算機科學"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid ""
"e.g. In this video, we'll give you the keys on how Odoo can help you to grow"
" your business. At the end, we'll propose you a quiz to test your knowledge."
msgstr "例如: 在本影片中，我們將向您介紹Odoo如何幫助您拓展業務。 最後，我們將為測試您的知識。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.view_slide_slide_form
msgid "e.g. Setting up your computer"
msgstr "例：設置你的電腦"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_question_view_form
msgid "e.g. What powers a computer?"
msgstr "例：甚麼驅動電腦和計算機？"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_tag_group_view_form
msgid "e.g. Your Level"
msgstr "e.g. 您的等級"

#. module: website_slides
#: model:ir.ui.menu,name:website_slides.website_slides_menu_root
#: model_terms:ir.ui.view,arch_db:website_slides.res_config_settings_view_form
msgid "eLearning"
msgstr "網上教學"

#. module: website_slides
#: model:ir.model.fields,field_description:website_slides.field_res_partner__slide_channel_ids
#: model:ir.model.fields,field_description:website_slides.field_res_users__slide_channel_ids
msgid "eLearning Courses"
msgstr "電子教學課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_channel_view_kanban
msgid "eLearning Overview"
msgstr "電子教學課程概覽"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "for 'Private' videos and similar to"
msgstr "for 'Private' videos and similar to"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "for public ones."
msgstr "for public ones."

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
msgid "<EMAIL>, <EMAIL>"
msgstr "<EMAIL>, <EMAIL>"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://drive.google.com/file/d/ABC/view?usp=sharing"
msgstr "https://drive.google.com/file/d/ABC/view?usp=sharing"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://vimeo.com/558907333/30da9ff3d8"
msgstr "https://vimeo.com/558907333/30da9ff3d8"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "https://vimeo.com/558907555"
msgstr "https://vimeo.com/558907555"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "join"
msgstr "參加"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "login"
msgstr "登入"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"mean? The Vimeo \"Private\" privacy setting means it is a video which can be viewed only by the users with the link to it.\n"
"                Your video will never come up in the search results nor on your channel."
msgstr ""
"是甚麼意思？ － Vimeo 的「私人」私隱設定選項，是指影片只能由擁有連結的用戶觀看。\n"
"                該影片永遠不會出現在搜尋結果或你的頻道中。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"means? The YouTube \"unlisted\" means it is a video which can be viewed only"
" by the users with the link to it. Your video will never come up in the "
"search results nor on your channel."
msgstr ""
"是甚麼意思？ － YouTube 的「不公開列出」私隱設定選項，是指影片只能由擁有連結的用戶觀看。該影片永遠不會出現在搜尋結果或你的頻道中。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "or"
msgstr "或"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_unsubscribe_dialog/slide_unsubscribe_dialog.xml:0
msgid "or Leave the course"
msgstr "或離開課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "request"
msgstr "請求"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "sign in"
msgstr "登入"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "start"
msgstr "開始"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_card
msgid "steps"
msgstr "步驟"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.join_course_link
msgid "to access resources"
msgstr "以存取學習資源"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_content_detailed
msgid "to be the first to leave a comment."
msgstr "成為第一個留言的人。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_main
msgid "to browse preview content and enroll."
msgstr "查看預覽內容及報名。"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to contact responsible"
msgstr "聯絡負責人"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_quiz.xml:0
msgid "to enroll."
msgstr "報名參加。"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/xml/slide_course_join.xml:0
msgid "to join this course"
msgstr "參加本課程"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to request access"
msgstr "要求存取"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_share_dialog/email_sharing_input.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "to share this"
msgstr "分享這個"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_join
msgid "to unlock"
msgstr "解鎖"

#. module: website_slides
#. odoo-javascript
#: code:addons/website_slides/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "unlisted"
msgstr "不公開列出"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_slides_list_slide
#: model_terms:ir.ui.view,arch_db:website_slides.slide_aside_training_category
#: model_terms:ir.ui.view,arch_db:website_slides.user_quickkarma_card
msgid "xp"
msgstr "xp"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.slide_social_email
msgid "<EMAIL>, <EMAIL>"
msgstr "<EMAIL>, <EMAIL>"

#. module: website_slides
#: model:mail.template,subject:website_slides.mail_template_channel_shared
msgid "{{ user.name }} shared a Course"
msgstr "{{ user.name }} 分享了課程"

#. module: website_slides
#: model:mail.template,subject:website_slides.slide_template_shared
msgid "{{ user.name }} shared a {{ object.slide_category }} with you!"
msgstr "{{ user.name }} 向你分享了 {{ object.slide_category }}！"

#. module: website_slides
#: model_terms:ir.ui.view,arch_db:website_slides.course_nav
msgid "└<span class=\"ms-1\">Uncategorized</span>"
msgstr "└ <span class=\"ms-1\">未分類</span>"
