<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
         <record id="slide_template_published" model="mail.template">
            <field name="name">Elearning: New Course Content Notification</field>
            <field name="model_id" ref="model_slide_slide"/>
            <field name="subject">New {{ object.slide_category }} published on {{ object.channel_id.name }}</field>
            <field name="description">Sent to attendees when new course is published</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        Hello<br/><br/>
                        There is something new in the course <strong t-out="object.channel_id.name or ''">Trees, Wood and Gardens</strong> you are following:<br/><br/>
                        <center><strong t-out="object.name or ''">Trees</strong></center>
                        <t t-if="object.image_1024">
                            <div style="padding: 16px 8px 16px 8px; text-align: center;">
                                <a t-att-href="object.website_share_url">
                                <img t-att-alt="object.name" t-attf-src="{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024" style="height:auto; width:150px; margin: 16px;"/>
                            </a>
                        </div>
                        </t>
                        <div style="padding: 16px 8px 16px 8px; text-align: center;">
                            <a t-att-href="object.website_share_url"
                                style="background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">View content</a>
                        </div>
                        Enjoy this exclusive content!
                        <t t-if="user.signature">
                            <br />
                            <t t-out="user.signature or ''">--<br/>Mitchell Admin</t>
                        </t>
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <record id="slide_template_shared" model="mail.template">
            <field name="name">Elearning: Course Share</field>
            <field name="model_id" ref="model_slide_slide"/>
            <field name="subject">{{ user.name }} shared a {{ object.slide_category }} with you!</field>
            <field name="email_from">{{ user.email_formatted }}</field>
            <field name="email_to">{{ ctx.get('email', '') }}</field>
            <field name="description">Sent when attendees share the course by email</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        Hello<br/><br/>
                        <t t-out="user.name or ''">Mitchell Admin</t> shared the <t t-out="object.slide_category or ''">document</t> <strong t-out="object.name or ''">Trees</strong> with you!
                        <div style="margin: 16px 8px 16px 8px; text-align: center;">
                            <a t-att-href="(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url">
                                <img t-att-alt="object.name" t-attf-src="{{ ctx.get('base_url') }}/web/image/slide.slide/{{ object.id }}/image_1024" style="height:auto; width:150px; margin: 16px;"/>
                            </a>
                        </div>
                        <div style="padding: 16px 8px 16px 8px; text-align: center;">
                            <a t-att-href="(object.website_share_url + '?fullscreen=1') if ctx.get('fullscreen') else object.website_share_url"
                                style="background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">View <strong t-out="object.name or ''">Trees</strong></a>
                        </div>
                        <t t-if="user.signature">
                            <br />
                            <t t-out="user.signature or ''">--<br/>Mitchell Admin</t>
                        </t>
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <!-- Completed Channel Message -->
        <record id="mail_template_channel_completed" model="mail.template">
            <field name="name">Elearning: Completed Course</field>
            <field name="model_id" ref="model_slide_channel_partner"/>
            <field name="subject">Congratulations! You completed {{ object.channel_id.name }}</field>
            <field name="email_from">{{ (object.channel_id.user_id.email_formatted or object.channel_id.user_id.company_id.catchall_formatted) }}</field>
            <field name="partner_to">{{ object.partner_id.id }}</field>
            <field name="description">Sent to attendees once they've completed the course</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <div style="margin: 0px; padding: 0px; font-size: 13px;">
                        <p style="margin: 0px;">Hello <t t-out="object.partner_id.name or ''">Brandon Freeman</t>,</p><br/>
                        <p><b>Congratulations!</b></p>
                        <p>You've completed the course <b t-out="object.channel_id.name or ''">Basics of Gardening</b></p>
                        <p>Check out the other available courses.</p><br/>

                        <div style="padding: 16px 8px 16px 8px; text-align: center;">
                            <a href="/slides/all" style="background-color: #875a7b; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px;">
                                Explore courses
                            </a>
                        </div>
                        Enjoy this exclusive content!
                        <t t-if="object.channel_id.user_id.signature">
                            <br />
                            <t t-out="object.channel_id.user_id.signature or ''">--<br/>Mitchell Admin</t>
                        </t>
                    </div>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
            <field name="lang">{{ object.partner_id.lang }}</field>
        </record>

        <record id="mail_template_channel_shared" model="mail.template">
            <field name="name">Channel Shared</field>
            <field name="model_id" ref="model_slide_channel"/>
            <field name="subject">{{ user.name }} shared a Course</field>
            <field name="email_from">{{ user.email_formatted }}</field>
            <field name="body_html" type="html">
                <div style="margin: 0px; padding: 0px;">
                    <p style="margin: 0px; padding: 0px; font-size: 13px;">
                        Hello<br/><br/>
                        <t t-out="user.name or ''">Mitchell Admin</t> shared the <strong t-out="object.name or ''">document</strong> with you!
                        <div style="margin: 16px 8px 16px 8px; text-align: center;">
                            <a t-att-href="object.website_url">
                                <img t-att-alt="object.name"
                                    t-attf-src="{{ ctx.get('base_url') }}/web/image/slide.channel/{{ object.id }}/image_256"
                                    style="height:auto; width:150px; margin: 16px;"/>
                            </a>
                        </div>
                        <div style="padding: 16px 8px 16px 8px; text-align: center;">
                            <a t-att-href="object.website_url"
                                style="background-color: #875a7b; padding: 8px 16px 8px 16px;
                                text-decoration: none; color: #fff; border-radius: 5px;">
                                View <strong t-out="object.name or ''">Document</strong></a>
                        </div>
                        <t t-if="user.signature">
                            <br />
                            <t t-out="user.signature or ''">--<br/>Mitchell Admin</t>
                        </t>
                    </p>
                </div>
            </field>
            <field name="auto_delete" eval="True"/>
        </record>

        <!-- Slide channel invite feature -->
        <record id="mail_template_slide_channel_enroll" model="mail.template">
            <field name="name">Elearning: Add Attendees to Course</field>
            <field name="model_id" ref="model_slide_channel_partner" />
            <field name="subject">You have been invited to join {{ object.channel_id.name }}</field>
            <field name="email_from">{{ user.email_formatted }}</field>
            <field name="use_default_to" eval="True"/>
            <field name="description">Sent to attendees when they are added to a course</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px; font-size: 13px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Hello<br/><br/>
        You have been enrolled to a new course: <t t-out="object.channel_id.name or ''">Basics of Gardening</t>.
    </p>
</div>
            </field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>

        <!-- Slide channel sharing feature -->
        <record id="mail_template_slide_channel_invite" model="mail.template">
            <field name="name">Elearning: Promotional Course Invitation</field>
            <field name="model_id" ref="model_slide_channel_partner" />
            <field name="subject">You have been invited to check out {{ object.channel_id.name }}</field>
            <field name="email_from">{{ user.email_formatted }}</field>
            <field name="use_default_to" eval="True"/>
            <field name="description">Sent to potential attendees to check out the course.</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px; font-size: 13px;">
    <p style="margin: 0px; padding: 0px; font-size: 13px;">
        Hello<br/><br/>
        You have been invited to check out this course: <t t-out="object.channel_id.name or ''">Basics of Gardening</t>.
    </p>
</div>
            </field>
            <field name="lang">{{ object.partner_id.lang }}</field>
            <field name="auto_delete" eval="True"/>
        </record>
    </data>
</odoo>
