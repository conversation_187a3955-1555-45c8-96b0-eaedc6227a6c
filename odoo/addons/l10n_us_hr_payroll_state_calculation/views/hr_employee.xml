<?xml version="1.0"?>
<odoo>
    <record id="hr_employee_view_form" model="ir.ui.view">
        <field name="name">hr.employee.view.form.inherit.l10n.us.hr.payroll</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr_payroll.payroll_hr_employee_view_form"/>
        <field name="arch" type="xml">
             <xpath expr="//field[@name='l10n_us_state_filing_status']" position="after">
                <field name="l10n_us_state_withholding_allowance" invisible="company_country_code != 'US'"/>
                <field name="l10n_us_state_extra_withholding" invisible="company_country_code != 'US'"/>
            </xpath>
        </field>
    </record>
</odoo>
