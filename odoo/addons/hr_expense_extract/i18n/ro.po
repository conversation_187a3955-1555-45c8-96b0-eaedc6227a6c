# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_expense_extract
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "<b>Wasting time recording your receipts?</b> Let’s try a better way."
msgstr ""

#. module: hr_expense_extract
#: model_terms:web_tour.tour,rainbow_man_message:hr_expense_extract.hr_expense_extract_tour
msgid ""
"<span><b>Congratulations</b>, you are now an expert of Expenses.\n"
"        </span>"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_needaction
msgid "Action Needed"
msgstr "Acțiune necesară"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__amount
msgid "Amount"
msgstr "Sumă"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_attachment_count
msgid "Attachment Count"
msgstr "Număr atașamente"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__available_payment_method_line_ids
msgid "Available Payment Method Line"
msgstr "Linie de metodă de plată disponibilă"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_can_show_send_button
msgid "Can show the ocr send button"
msgstr "Poate afișa butonul de trimitire ocr"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Cancel"
msgstr "Anulează"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Choose a receipt."
msgstr "Alegeți o chitanță."

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_company
msgid "Companies"
msgstr "Companii"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__company_id
msgid "Company"
msgstr "Companie"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_res_config_settings
msgid "Config Settings"
msgstr "Setări de configurare"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Create Payment"
msgstr "Crează plata"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_uid
msgid "Created by"
msgstr "Creat de"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__create_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__create_date
msgid "Created on"
msgstr "Creat pe"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__currency_id
msgid "Currency"
msgstr "Monedă"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__auto_send
msgid "Digitize automatically"
msgstr "Digitalizați automat"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.hr_expense_parse_action_server
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.hr_expense_extract_view_form
msgid "Digitize document"
msgstr "Digitalizați documentul"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__manual_send
msgid "Digitize on demand only"
msgstr "Digitalizați numai la cerere"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__display_name
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__display_name
msgid "Display Name"
msgstr "Nume afișat"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__res_company__expense_extract_show_ocr_option_selection__no_send
msgid "Do not digitize"
msgstr "Nu digitalizați"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_error_message
msgid "Error message"
msgstr "Mesaj de eroare"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__sheet_id
msgid "Expense"
msgstr "Cheltuială"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_update_ocr_status_ir_actions_server
msgid "Expense OCR: Update All Status"
msgstr "Cheltuială OCR: Actualizați toată starea"

#. module: hr_expense_extract
#: model:ir.actions.server,name:hr_expense_extract.ir_cron_ocr_validate_ir_actions_server
msgid "Expense OCR: Validate Expenses"
msgstr "Cheltuială OCR: Validarea cheltuielilor"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_hr_expense_sheet
msgid "Expense Report"
msgstr "Raport de cheltuieli"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_config_settings__expense_extract_show_ocr_option_selection
msgid "Expense processing option"
msgstr "Opțiune procesare cheltuieli"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__sample
msgid "Expenses created from sample receipt"
msgstr "Cheltuieli create din modelul de chitanță"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state_processed
msgid "Extract State Processed"
msgstr "Extract State Processed"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_state
msgid "Extract state"
msgstr "Extragere Stare"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_status
msgid "Extract status"
msgstr "Statusul extrasului"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_follower_ids
msgid "Followers"
msgstr "Urmăritori"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_partner_ids
msgid "Followers (Partners)"
msgstr "Urmăritori (Parteneri)"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__has_message
msgid "Has Message"
msgstr "Are mesaj"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_partial
msgid "Hide Partial"
msgstr "Ascundeți parțial"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__hide_payment_method_line
msgid "Hide Payment Method Line"
msgstr "Ascunde linia metodei de plată"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__id
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__id
msgid "ID"
msgstr "ID"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__extract_document_uuid
msgid "ID of the request to IAP-OCR"
msgstr "ID-ul cererii către IAP-OCR"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Dacă este bifat, mesaje noi necesită atenția dumneavoastră."

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_error
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Dacă este bifată, există mesaje cu eroare de livrare."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_is_follower
msgid "Is Follower"
msgstr "Este urmăritor"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__is_in_extractable_state
msgid "Is In Extractable State"
msgstr "Este în stare de extracție"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__journal_id
msgid "Journal"
msgstr "Jurnal"

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__open
msgid "Keep open"
msgstr "Ține Deschis"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_uid
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_uid
msgid "Last Updated by"
msgstr "Ultima actualizare făcută de"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_receipt__write_date
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__write_date
msgid "Last Updated on"
msgstr "Ultima actualizare pe"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_main_attachment_id
msgid "Main Attachment"
msgstr "Atașament principal"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid ""
"Manual: Pay or Get paid by any method outside of Odoo.\n"
"Payment Providers: Each payment provider has its own Payment Method. Request a transaction on/to a card thanks to a payment token saved by the partner when buying or subscribing online.\n"
"Check: Pay bills by check and print it from Odoo.\n"
"Batch Deposit: Collect several customer checks at once generating and submitting a batch deposit to your bank. Module account_batch_payment is necessary.\n"
"SEPA Credit Transfer: Pay in the SEPA zone by submitting a SEPA Credit Transfer file to your bank. Module account_iso20022 is necessary.\n"
"SEPA Direct Debit: Get paid in the SEPA zone thanks to a mandate your partner will have granted to you. Module account_iso20022 is necessary.\n"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields.selection,name:hr_expense_extract.selection__expense_sample_register__partial_mode__paid
msgid "Mark as fully paid"
msgstr "Marcați ca plătit complet"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__memo
msgid "Memo"
msgstr "Memo"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_error
msgid "Message Delivery error"
msgstr "Eroare de livrare a mesajului"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_ids
msgid "Messages"
msgstr "Mesaje"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_needaction_counter
msgid "Number of Actions"
msgstr "Număr de acțiuni"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_error_counter
msgid "Number of errors"
msgstr "Număr de erori"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Numărul de mesaje care necesită acțiune"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Numărul de mesaje cu eroare de livrare"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__date
msgid "Payment Date"
msgstr "Data plății"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__partial_mode
msgid "Payment Difference"
msgstr "Diferență de plată"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_expense_sample_register__payment_method_line_id
msgid "Payment Method"
msgstr "Metodă de plată"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__rating_ids
msgid "Ratings"
msgstr "Ratings"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_register_view_form
msgid "Register Payment"
msgstr "Înregistrare plată"

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_register
msgid "Register Sample Payment"
msgstr "Înregistrați exemplu de plată"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_register
msgid "Register Sample Payments"
msgstr "Înregistrați exemplu de plăți"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Report this expense to your manager for validation."
msgstr "Raportați această cheltuială managerului dvs. pentru validare."

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Eroare livrare SMS"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__sample
msgid "Sample"
msgstr "Mostră"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
msgid "Sample Employee"
msgstr "Exemplu de angajat"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/wizard/expense_sample_receipt.py:0
msgid "Sample Receipt: %s"
msgstr "Exemplu de chitanță:%s"

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_res_company__expense_extract_show_ocr_option_selection
msgid "Send mode on expense attachments"
msgstr "Modul de trimitere la atașamentele de cheltuieli"

#. module: hr_expense_extract
#: model_terms:ir.ui.view,arch_db:hr_expense_extract.expense_sample_receipt_view_form
msgid "Try Expense Digitization"
msgstr ""

#. module: hr_expense_extract
#: model:ir.actions.act_window,name:hr_expense_extract.action_expense_sample_receipt
msgid "Try Sample Receipt"
msgstr "Încercați un exemplar de chitanță"

#. module: hr_expense_extract
#: model:ir.model,name:hr_expense_extract.model_expense_sample_receipt
msgid "Try Sample Receipts"
msgstr "Încercați exemple de chitanțe"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Try the AI with a sample receipt."
msgstr "Încercați AI cu un exemplar de chitanță."

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "Upload or drop an expense receipt"
msgstr ""

#. module: hr_expense_extract
#: model:ir.model.fields,field_description:hr_expense_extract.field_hr_expense__website_message_ids
msgid "Website Messages"
msgstr "Mesaje de pe site-ul web"

#. module: hr_expense_extract
#: model:ir.model.fields,help:hr_expense_extract.field_hr_expense__website_message_ids
msgid "Website communication history"
msgstr "Istoricul comunicării pe site-ul web"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "You can't mix sample expenses and regular ones"
msgstr "Nu poți amesteca eșantioane și cheltuieli obișnuite"

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "You cannot send a expense that is not in draft state!"
msgstr "Nu puteți trimite o cheltuială care nu este în starea de ciornă!"

#. module: hr_expense_extract
#. odoo-javascript
#: code:addons/hr_expense_extract/static/src/js/tours/expense_tour.js:0
msgid "Your manager will have to approve (or refuse) your expense reports."
msgstr ""
"Managerul dvs. va trebui să vă aprobe (sau să refuze) rapoartele de "
"cheltuieli."

#. module: hr_expense_extract
#. odoo-python
#: code:addons/hr_expense_extract/models/hr_expense.py:0
msgid "try a sample receipt"
msgstr ""
