# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_automation_sms
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid "\" <strong>bounced</strong>,"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid "\" gets clicked,"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_campaign__mailing_sms_count
msgid "# SMS Mailings"
msgstr "# SMS-рассылки"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Bounced after"
msgstr "<i class=\"fa fa-exclamation-circle\"/> Отскочил после"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Clicked after"
msgstr "<i class=\"fa fa-hand-pointer-o\"/> Нажав после"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Not clicked within"
msgstr "<i class=\"fa fa-hand-pointer-o\"/> Не нажимается в пределах"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-pie-chart\"/> Details"
msgstr "<i class=\"fa fa-pie-chart\"/> Подробности"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid ""
"<i data-trigger-type=\"sms_click\" class=\"fa fa-hand-pointer-o o_ma_text_processed o_add_child_activity text-success\" title=\"Clicked\" role=\"img\" aria-label=\"Clicked\"/>\n"
"                    <i data-trigger-type=\"sms_not_click\" class=\"fa fa-hand-pointer-o o_ma_text_rejected o_add_child_activity text-danger\" title=\"Not Clicked\" role=\"img\" aria-label=\"Not Clicked\"/>\n"
"                    <i data-trigger-type=\"sms_bounce\" class=\"fa fa fa-exclamation-circle o_ma_text_rejected o_add_child_activity text-danger\" title=\"Bounced\" role=\"img\" aria-label=\"Bounced\"/>"
msgstr ""
"<i data-trigger-type=\"sms_click\" class=\"fa fa-hand-pointer-o o_ma_text_processed o_add_child_activity text-success\" title=\"Clicked\" role=\"img\" aria-label=\"Clicked\"/>\n"
"                    <i data-trigger-type=\"sms_not_click\" class=\"fa fa-hand-pointer-o o_ma_text_rejected o_add_child_activity text-danger\" title=\"Not Clicked\" role=\"img\" aria-label=\"Not Clicked\"/>\n"
"                    <i data-trigger-type=\"sms_bounce\" class=\"fa fa fa-exclamation-circle o_ma_text_rejected o_add_child_activity text-danger\" title=\"Bounced\" role=\"img\" aria-label=\"Bounced\"/>"

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__activity_type
msgid "Activity Type"
msgstr "Тип активности"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Clicked"
msgstr "Нажатый"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
msgid "Exception in SMS Marketing: %s"
msgstr "Исключение в SMS-маркетинге: %s"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Статистика рассылок"

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__mass_mailing_id_mailing_type
msgid "Mailing Type"
msgstr "Тип рассылки"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_activity
#: model:ir.model.fields,field_description:marketing_automation_sms.field_sms_composer__marketing_activity_id
msgid "Marketing Activity"
msgstr "Маркетинговая деятельность"

#. module: marketing_automation_sms
#: model:ir.actions.act_window,name:marketing_automation_sms.mail_mass_mailing_action_marketing_automation_sms
msgid "Marketing Automation SMS"
msgstr "Автоматизация маркетинга SMS"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_campaign
msgid "Marketing Campaign"
msgstr "Маркетинговая кампания"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_trace
msgid "Marketing Trace"
msgstr "Маркетинговый след"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_mailing_mailing
msgid "Mass Mailing"
msgstr "Массовая рассылка"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Opened"
msgstr "Открыто"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_trace.py:0
msgid "Parent activity SMS bounced"
msgstr "Родительская активность SMS отклонено"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_trace.py:0
msgid "Parent activity SMS clicked"
msgstr "Активность родителей Нажатие SMS"

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__activity_type__sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__mass_mailing_id_mailing_type__sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_category__sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_participant_view_form
msgid "SMS"
msgstr "СМС"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.mailing_mailing_view_form_marketing_activity
msgid "SMS Content"
msgstr "Содержание SMS"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.mailing_mailing_view_form_marketing_activity
msgid "SMS Options"
msgstr "Опции SMS"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "SMS Sent"
msgstr "Сообщение отправлено"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_view_form
msgid "SMS Template"
msgstr "SMS шаблон"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
msgid "SMS cancelled"
msgstr ""

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/mailing_trace.py:0
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
msgid "SMS failed"
msgstr "SMS не удалось"

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_bounce
msgid "SMS: bounced"
msgstr "SMS: отклонено"

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_click
msgid "SMS: clicked"
msgstr "SMS: нажато"

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_not_click
msgid "SMS: not clicked"
msgstr "SMS: не нажимается"

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr "Мастер отправки SMS"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Sent"
msgstr "Отправлено"

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
msgid ""
"To use this feature you should be an administrator or belong to the "
"marketing automation group."
msgstr ""
"Чтобы воспользоваться этой функцией, вы должны быть администратором или "
"входить в группу автоматизации маркетинга."

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__trigger_category
msgid "Trigger Category"
msgstr "Категория триггера"

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__trigger_type
msgid "Trigger Type"
msgstr "Тип Триггера"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid "after the <strong>SMS text message</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid ""
"after the Participant <strong>clicked</strong>,<br/>on any link included in "
"the <strong>SMS text message</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid ""
"if no link included in the <strong>SMS text message</strong> sent by the "
"Activity \""
msgstr ""
