# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_automation_sms
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:27+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid "\" <strong>bounced</strong>,"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid "\" gets clicked,"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_campaign__mailing_sms_count
msgid "# SMS Mailings"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Bounced after"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Clicked after"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Not clicked within"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "<i class=\"fa fa-pie-chart\"/> Details"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid ""
"<i data-trigger-type=\"sms_click\" class=\"fa fa-hand-pointer-o o_ma_text_processed o_add_child_activity text-success\" title=\"Clicked\" role=\"img\" aria-label=\"Clicked\"/>\n"
"                    <i data-trigger-type=\"sms_not_click\" class=\"fa fa-hand-pointer-o o_ma_text_rejected o_add_child_activity text-danger\" title=\"Not Clicked\" role=\"img\" aria-label=\"Not Clicked\"/>\n"
"                    <i data-trigger-type=\"sms_bounce\" class=\"fa fa fa-exclamation-circle o_ma_text_rejected o_add_child_activity text-danger\" title=\"Bounced\" role=\"img\" aria-label=\"Bounced\"/>"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__activity_type
msgid "Activity Type"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Clicked"
msgstr ""

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
msgid "Exception in SMS Marketing: %s"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_mailing_trace
msgid "Mailing Statistics"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__mass_mailing_id_mailing_type
msgid "Mailing Type"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_activity
#: model:ir.model.fields,field_description:marketing_automation_sms.field_sms_composer__marketing_activity_id
msgid "Marketing Activity"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.actions.act_window,name:marketing_automation_sms.mail_mass_mailing_action_marketing_automation_sms
msgid "Marketing Automation SMS"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_campaign
msgid "Marketing Campaign"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_marketing_trace
msgid "Marketing Trace"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_mailing_mailing
msgid "Mass Mailing"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Opened"
msgstr ""

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_trace.py:0
msgid "Parent activity SMS bounced"
msgstr ""

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_trace.py:0
msgid "Parent activity SMS clicked"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__activity_type__sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__mass_mailing_id_mailing_type__sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_category__sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_participant_view_form
msgid "SMS"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.mailing_mailing_view_form_marketing_activity
msgid "SMS Content"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.mailing_mailing_view_form_marketing_activity
msgid "SMS Options"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "SMS Sent"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_view_form
msgid "SMS Template"
msgstr ""

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
msgid "SMS cancelled"
msgstr ""

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/mailing_trace.py:0
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
msgid "SMS failed"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_bounce
msgid "SMS: bounced"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_click
msgid "SMS: clicked"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields.selection,name:marketing_automation_sms.selection__marketing_activity__trigger_type__sms_not_click
msgid "SMS: not clicked"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model,name:marketing_automation_sms.model_sms_composer
msgid "Send SMS Wizard"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_campaign_view_form
msgid "Sent"
msgstr ""

#. module: marketing_automation_sms
#. odoo-python
#: code:addons/marketing_automation_sms/models/marketing_activity.py:0
msgid ""
"To use this feature you should be an administrator or belong to the "
"marketing automation group."
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__trigger_category
msgid "Trigger Category"
msgstr ""

#. module: marketing_automation_sms
#: model:ir.model.fields,field_description:marketing_automation_sms.field_marketing_activity__trigger_type
msgid "Trigger Type"
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid "after the <strong>SMS text message</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid ""
"after the Participant <strong>clicked</strong>,<br/>on any link included in "
"the <strong>SMS text message</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation_sms
#: model_terms:ir.ui.view,arch_db:marketing_automation_sms.marketing_activity_summary_template_sms
msgid ""
"if no link included in the <strong>SMS text message</strong> sent by the "
"Activity \""
msgstr ""
