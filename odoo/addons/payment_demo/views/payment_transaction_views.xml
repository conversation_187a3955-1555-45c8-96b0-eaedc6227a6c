<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="payment_transaction_form" model="ir.ui.view">
        <field name="name">Demo Transaction Form</field>
        <field name="model">payment.transaction</field>
        <field name="inherit_id" ref="payment.payment_transaction_form"/>
        <field name="arch" type="xml">
            <header position="inside">
                <field name="capture_manually" invisible="1"/>
                <button string="Authorize"
                        type="object"
                        name="action_demo_set_done"
                        class="oe_highlight"
                        invisible="provider_code != 'demo' or not capture_manually or state != 'pending'"/>
                <button string="Confirm"
                        type="object"
                        name="action_demo_set_done"
                        class="oe_highlight"
                        invisible="provider_code != 'demo' or capture_manually or state != 'pending'"/>
                <button string="Cancel"
                        type="object"
                        name="action_demo_set_canceled"
                        invisible="provider_code != 'demo' or state != 'pending'"/>
                <button string="Set to Error"
                        type="object"
                        name="action_demo_set_error"
                        invisible="provider_code != 'demo' or state != 'pending'"/>
            </header>
        </field>
    </record>

</odoo>
