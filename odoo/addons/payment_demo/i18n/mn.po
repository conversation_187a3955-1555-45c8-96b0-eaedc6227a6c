# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_demo
# 
# Translators:
# Bayarkhuu Bataa, 2024
# <PERSON><PERSON><PERSON><PERSON> <baskhuu<PERSON><PERSON><EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_details
msgid ""
"<select id=\"simulated_payment_state\" class=\"form-select\">\n"
"                    <option value=\"done\" title=\"Successful payment\">\n"
"                        Successful\n"
"                    </option>\n"
"                    <option value=\"pending\" title=\"Payment processing\">\n"
"                        Pending\n"
"                    </option>\n"
"                    <option value=\"cancel\" title=\"Payment cancelled by customer\">\n"
"                        Cancelled\n"
"                    </option>\n"
"                    <option value=\"error\" title=\"Processing error\">\n"
"                        Error\n"
"                    </option>\n"
"                </select>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>City</b></small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>Country</b></small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>Name</b></small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>Street and Number</b></small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small><b>Zip Code</b></small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small>Email</small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_details
msgid "<small>Payment Details (test data)</small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_details
msgid "<small>Payment Status</small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_inline_form
msgid "<small>Street 2</small>"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_transaction_form
msgid "Authorize"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_transaction_form
msgid "Cancel"
msgstr "Цуцлах"

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_token__demo_simulated_state__cancel
msgid "Canceled"
msgstr "Цуцлагдсан"

#. module: payment_demo
#: model:ir.model.fields,field_description:payment_demo.field_payment_transaction__capture_manually
msgid "Capture Amount Manually"
msgstr "Дүнг гараар оруулах"

#. module: payment_demo
#: model:ir.model.fields,help:payment_demo.field_payment_transaction__capture_manually
msgid ""
"Capture the amount from Odoo, when the delivery is completed.\n"
"Use this if you want to charge your customers cards only when\n"
"you are sure you can ship the goods to them."
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Close"
msgstr "Хаах"

#. module: payment_demo
#: model:ir.model.fields,field_description:payment_demo.field_payment_provider__code
msgid "Code"
msgstr "Код"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_transaction_form
msgid "Confirm"
msgstr "Батлах"

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_token__demo_simulated_state__done
msgid "Confirmed"
msgstr "Баталсан"

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_provider__code__demo
#: model:payment.method,name:payment_demo.payment_method_demo
msgid "Demo"
msgstr "Туршилтын хувилбар"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Demo Express Checkout"
msgstr ""

#. module: payment_demo
#. odoo-python
#: code:addons/payment_demo/models/payment_provider.py:0
msgid "Demo providers should never be enabled."
msgstr ""

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_token__demo_simulated_state__error
msgid "Error"
msgstr "Алдаа"

#. module: payment_demo
#. odoo-javascript
#: code:addons/payment_demo/static/src/js/express_checkout_form.js:0
msgid "No delivery method is available."
msgstr ""

#. module: payment_demo
#. odoo-python
#: code:addons/payment_demo/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Pay"
msgstr "Төлөх"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Pay with Demo"
msgstr ""

#. module: payment_demo
#: model:ir.model,name:payment_demo.model_payment_provider
msgid "Payment Provider"
msgstr "Төлбөр дамжуулагч"

#. module: payment_demo
#: model:ir.model,name:payment_demo.model_payment_token
msgid "Payment Token"
msgstr "Төлбөрийн Токен"

#. module: payment_demo
#: model:ir.model,name:payment_demo.model_payment_transaction
msgid "Payment Transaction"
msgstr "Төлбөрийн гүйлгээ"

#. module: payment_demo
#. odoo-javascript
#: code:addons/payment_demo/static/src/js/payment_demo_mixin.js:0
msgid "Payment processing failed"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.token_inline_form
msgid "Payments made with this payment method will be <b>successful</b>."
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.token_inline_form
msgid ""
"Payments made with this payment method will be automatically "
"<b>cancelled</b>."
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.token_inline_form
msgid "Payments made with this payment method will remain <b>pending</b>."
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.token_inline_form
msgid ""
"Payments made with this payment method will simulate a processing "
"<b>error</b>."
msgstr ""

#. module: payment_demo
#: model:ir.model.fields.selection,name:payment_demo.selection__payment_token__demo_simulated_state__pending
msgid "Pending"
msgstr "Хүлээгдэж буй"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_transaction_form
msgid "Set to Error"
msgstr ""

#. module: payment_demo
#: model:ir.model.fields,field_description:payment_demo.field_payment_token__demo_simulated_state
msgid "Simulated State"
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Test Mode"
msgstr "Тестийн горим"

#. module: payment_demo
#: model:ir.model.fields,help:payment_demo.field_payment_token__demo_simulated_state
msgid "The state in which transactions created from this token should be set."
msgstr ""

#. module: payment_demo
#: model:ir.model.fields,help:payment_demo.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_demo
#. odoo-python
#: code:addons/payment_demo/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.express_checkout_form
msgid "Unpublished"
msgstr "Нийтлэгдээгүй"

#. module: payment_demo
#. odoo-javascript
#: code:addons/payment_demo/static/src/js/express_checkout_form.js:0
msgid "Validation Error"
msgstr "Шалгалтын зөрчил"

#. module: payment_demo
#: model_terms:ir.ui.view,arch_db:payment_demo.payment_details
msgid "XXXX XXXX XXXX XXXX"
msgstr ""

#. module: payment_demo
#. odoo-python
#: code:addons/payment_demo/models/payment_transaction.py:0
msgid "You selected the following demo payment status: %s"
msgstr ""
