# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_holidays_attendance
# 
# Translators:
# NoaFarkash, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-02-06 14:58+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_type.py:0
msgid "%s hours available"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_accrual_level
msgid "Accrual Plan Level"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_attendance
msgid "Attendance"
msgstr "נוכחות"

#. module: hr_holidays_attendance
#: model:ir.model.fields.selection,name:hr_holidays_attendance.selection__hr_leave_accrual_level__frequency_hourly_source__attendance
msgid "Attendances"
msgstr "נוכחות"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Available"
msgstr "זמין"

#. module: hr_holidays_attendance
#: model:ir.model.fields.selection,name:hr_holidays_attendance.selection__hr_leave_accrual_level__frequency_hourly_source__calendar
msgid "Calendar"
msgstr "יומן"

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Days"
msgstr "ימים"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_employee_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.res_users_view_form
msgid "Deduct Extra Hours"
msgstr ""

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Discard"
msgstr "בטל"

#. module: hr_holidays_attendance
#: model:hr.leave.type,name:hr_holidays_attendance.holiday_status_extra_hours
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_id
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_id
msgid "Extra Hours"
msgstr "שעות נוספות"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_attendance_holidays_hr_leave_allocation_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_view_form
msgid "Extra Hours Available"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_accrual_level__frequency_hourly_source
msgid "Frequency Hourly Source"
msgstr ""

#. module: hr_holidays_attendance
#. odoo-javascript
#: code:addons/hr_holidays_attendance/static/src/xml/time_off_calendar.xml:0
msgid "Hours"
msgstr "שעות"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_accrual_level__frequency_hourly_source
msgid ""
"If the source is set to \"Calendar\", the amount of worked hours will be "
"computed based on the Employee's working schedule. Otherwise, the amount of "
"worked hours will be based on Attendance records."
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_action
#: model:ir.actions.act_window,name:hr_holidays_attendance.hr_leave_allocation_overtime_manager_action
msgid "New Allocation Request"
msgstr "בקשת הקצאה חדשה"

#. module: hr_holidays_attendance
#: model:ir.model.fields,help:hr_holidays_attendance.field_hr_leave_type__overtime_deductible
msgid ""
"Once a time off of this type is approved, extra hours in attendances will be"
" deducted."
msgstr ""

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"Only an Officer or Administrator is allowed to edit the allocation duration "
"in this status."
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__overtime_deductible
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__overtime_deductible
msgid "Overtime Deductible"
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_res_users__request_overtime
msgid "Request Overtime"
msgstr ""

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_allocation_overtime_view_form
msgid "Save"
msgstr "שמור"

#. module: hr_holidays_attendance
#: model_terms:ir.ui.view,arch_db:hr_holidays_attendance.hr_leave_accrual_level_view_form
msgid "Source"
msgstr "מקור"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"The employee does not have enough extra hours to extend this allocation."
msgstr ""

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "The employee does not have enough extra hours to extend this leave."
msgstr ""

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "The employee does not have enough extra hours to request this leave."
msgstr ""

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave_allocation.py:0
msgid ""
"The employee does not have enough overtime hours to request this leave."
msgstr ""

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave
msgid "Time Off"
msgstr "מאשר חופשות"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_allocation
msgid "Time Off Allocation"
msgstr "הקצאת יום חופש"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_hr_leave_type
msgid "Time Off Type"
msgstr "סוג חופשה"

#. module: hr_holidays_attendance
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave__employee_overtime
#: model:ir.model.fields,field_description:hr_holidays_attendance.field_hr_leave_allocation__employee_overtime
msgid "Total Overtime"
msgstr "סה\"כ שעות נוספות"

#. module: hr_holidays_attendance
#: model:ir.model,name:hr_holidays_attendance.model_res_users
msgid "User"
msgstr "משתמש"

#. module: hr_holidays_attendance
#. odoo-python
#: code:addons/hr_holidays_attendance/models/hr_leave.py:0
msgid "You do not have enough extra hours to request this leave"
msgstr ""
