<?xml version="1.0" encoding="UTF-8"?>

<templates id="template" xml:space="preserve">
    <t t-name="hr_holidays_attendance.TimeOffCard" t-inherit="hr_holidays.TimeOffCard" t-inherit-mode="extension">
        <xpath expr="//t[@t-set='duration']" position="replace">
            <t t-set="duration" t-value="props.requires_allocation || props.data['overtime_deductible']?props.data['virtual_remaining_leaves']:props.data['virtual_leaves_taken']" />
        </xpath>
        <xpath expr="//t[@t-set='show_popover']" position="replace">
            <t t-set="show_popover" t-value="!props.data['overtime_deductible']"/>
        </xpath>
        <xpath expr="//t[@name='duration_unit']" position="attributes">
            <attribute name="t-if">props.data.request_unit == 'hour' || props.data['overtime_deductible']</attribute>
        </xpath>
        <xpath expr="//t[@name='duration_type']" position="attributes">
            <attribute name="t-if">props.requires_allocation || props.data['overtime_deductible']</attribute>
        </xpath>
    </t>

    <t t-name="hr_holidays_attendance.TimeOffCardMobile" t-inherit="hr_holidays.TimeOffCardMobile" t-inherit-mode="extension">
        <xpath expr="//t[@t-set='duration']" position="replace">
            <t t-set="duration" t-value="props.requires_allocation || props.data['overtime_deductible']?props.data['virtual_remaining_leaves']:props.data['virtual_leaves_taken']" />
        </xpath>
        <xpath expr="//t[@name='duration_type']" position="before">
            <t t-if="props.data['overtime_deductible'] == true &amp;&amp; !props.requires_allocation">
                <strong t-esc="duration" class="o_timeoff_green"/> <t t-if="props.data['request_unit'] == 'hour'">Hours</t><t t-else="">Days</t> <span class="o_timeoff_green">Available</span>
            </t>
        </xpath>
        <xpath expr="//t[@name='duration_type']" position="attributes">
            <attribute name="t-if"/>
            <attribute name="t-elif">props.requires_allocation</attribute>
        </xpath>
    </t>
</templates>
