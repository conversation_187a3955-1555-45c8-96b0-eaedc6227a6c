# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_lu
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-06-19 13:53+0000\n"
"PO-Revision-Date: 2019-08-30 08:44+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_overall_turnover
msgid "012 - Overall turnover"
msgstr "012 - Chiffre d'affaires global"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_2_export
msgid "014"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_2_export
msgid "014 - Exports"
msgstr "014 - Exportations"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_3_other_exemptions_art_43
msgid "015"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_3_other_exemptions_art_43
msgid "015 - Other exemptions"
msgstr "015 - Autres exonérations"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater
msgid "016"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_4_other_exemptions_art_44_et_56quater
msgid "016 - Other exemptions"
msgstr "016 - Autres exonérations"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_5_manufactured_tobacco_vat_collected
msgid "017"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_5_manufactured_tobacco_vat_collected
msgid ""
"017 - Manufactured tobacco whose VAT was collected at the source or at the "
"exit of the tax..."
msgstr ""
"017 - Tabacs fabriqués dont la TVA a été perçue à la source respectivement à"
" la sortie de l'entrepôt fiscal..."

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_a_subsequent_to_intra_community
msgid "018"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_a_subsequent_to_intra_community
msgid ""
"018 - Supply, subsequent to intra-Community acquisitions of goods, in the "
"context of triangular transactions, when the customer identified,..."
msgstr ""
"018 - Livraisons subséquentes à des acqu. Intra. dans le cadre d'opérations "
"triangulaires…"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_d_supplies_other_referred
msgid "019"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_d_supplies_other_referred
msgid "019 - Supplies other than referred to in 018 and 423 or 424"
msgstr "019 - Autres opérations réalisées (imposables) à l'étranger"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_exemptions_deductible_amounts
msgid "021 - Exemptions and deductible amounts"
msgstr "021 - Exonérations et montants déductibles"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1c_taxable_turnover
msgid "022 - Taxable turnover"
msgstr "022 - Chiffre d'affaires imposable"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_3
msgid "031"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_3
msgid "031 - base 3%"
msgstr "031 - base 3%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_0
msgid "033"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_0
msgid "033 - base 0%"
msgstr "033 - base 0%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_breakdown_taxable_turnover_base
msgid "037 - Breakdown of taxable turnover – base"
msgstr "037 - Chiffre d'affaires imposable – base"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_3
msgid "040"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_3
msgid "040 - tax 3%"
msgstr "040 - taxe 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_breakdown_taxable_turnover_tax
msgid "046 - Breakdown of taxable turnover – tax"
msgstr "046 - Chiffre d'affaires imposable – taxe"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_3
msgid "049"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_3
msgid "049 - base 3%"
msgstr "049 - base 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_intra_community_acqui_of_goods_base
msgid "051 - Intra-Community acquisitions of goods – base"
msgstr "051 - Acquisitions intracommunautaires de biens - base"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_3
msgid "054"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_3
msgid "054 - tax 3%"
msgstr "054 - taxe 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_intra_community_acquisitions_goods_tax
msgid "056 - Intra-Community acquisitions of goods – tax"
msgstr "056 - Acquisitions intracommunautaires de biens - taxe"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_3
msgid "059"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_3
msgid "059 - for business purposes: base 3%"
msgstr "059 - à des fins de l'entreprise: base 3%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_3
msgid "063"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_3
msgid "063 - for non-business purposes: base 3%"
msgstr "063 - à des fins étrangères à l'entreprise: base 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_importation_of_goods_base
msgid "065 - Importation of goods – base"
msgstr "065 - Importations de biens - base"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_3
msgid "068"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_3
msgid "068 - for business purposes: tax 3%"
msgstr "068 - à des fins de l'entreprise: taxe de 3%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_3
msgid "073"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_3
msgid "073 - for non-business purposes: tax 3%"
msgstr "073 - à des fins étrangères à l'entreprise: taxe de 3%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2h_total_tax_due
msgid "076 - Total tax due"
msgstr "076 - Total de la taxe en aval"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_3a_4_due_respect_application_goods
msgid "090"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_4_due_respect_application_goods
msgid "090 - Due in respect of the application of goods for business purposes"
msgstr "090 - Taxe déclarée pour l'affectation de biens à l'entreprise"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_3a_6_paid_joint_several_guarantee
msgid "092"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_6_paid_joint_several_guarantee
msgid "092 - Paid as joint and several guarantee"
msgstr "092 - Taxe acquittée comme caution solidaire"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_total_input_tax
msgid "093 - Total input tax"
msgstr "093 - Total de la taxe en amont"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3b1_rel_trans
msgid ""
"094 - relating to transactions which are exempt pursuant to articles 44 and "
"56quater"
msgstr ""
"094 - Taxe non déductible en rapport avec des opérations exonérées en vertu "
"des articles 44 et 56quater"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3b2_ded_prop
msgid ""
"095 - where the deductible proportion determined in accordance to article 50"
" is applied"
msgstr ""
"095 - Taxe non déductible en application du prorata visé à l'article 50"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_3b2_input_tax_margin
msgid "096"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3b2_input_tax_margin
msgid ""
"096 - Non recoverable input tax in accordance with Art. 56ter-1(7) and "
"56ter-2(7) (when applying the margin scheme)"
msgstr ""
"096 - Taxe non déductible en application des articles 56ter-1/7 et 56ter-2/7"
" (en cas d'option pour le régime d'imposition de la marge bénéficiaire)"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3b_total_input_tax_nd
msgid "097 - Total input tax non-deductible"
msgstr "097 - Total de la taxe en amont non déductible"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3c_total_input_tax_deductible
msgid "102 - Total input tax deductible"
msgstr "102 - Total de la taxe en amont déductible"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_4a_total_tax_due
msgid "103 - Total tax due"
msgstr "103 - Total de la taxe en aval"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_4a_total_input_tax_deductible
msgid "104 - Total input tax deductible"
msgstr "104 - Total de la taxe en amont déductible"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_4c_exceeding_amount
msgid "105 - Exceeding amount"
msgstr "105 - Excédent"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2c_acquisitions_triangular_transactions_base
msgid "152"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2c_acquisitions_triangular_transactions_base
msgid "152 - Acquisitions, in the context of triangular transactions – base"
msgstr "152 - Acquisitions triangulaires – base"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_exempt
msgid "194"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_exempt
msgid "194 - base exempt"
msgstr "194 - base exonérée"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_exempt
msgid "195"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_exempt
msgid "195 - for business purposes: base exempt"
msgstr "195 - à des fins de l'entreprise: base exonérée"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_exempt
msgid "196"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_exempt
msgid "196 - for non-business purposes: base exempt"
msgstr "196 - à des fins étrangères à l'entreprise: base exonérée"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_c_supplies_scope_special_arrangement
msgid "226"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_c_supplies_scope_special_arrangement
msgid ""
"226 - Supplies carried out within the scope of the special arrangement of "
"art. 56sexies"
msgstr ""
"226 - Opérations réalisées dans le cadre du régime particulier de l'article "
"56sexies"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2g_special_arrangement
msgid "227"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2g_special_arrangement
msgid "227 - Special arrangement for tax suspension: adjustment"
msgstr "227 - Régime particulier suspensif: régularisation"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_3a_7_adjusted_tax_special_arrangement
msgid "228"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_7_adjusted_tax_special_arrangement
msgid "228 - Adjusted tax - special arrangement for tax suspension"
msgstr "228 - Taxe régularisée - régime particulier suspensif"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_importation_of_goods_tax
msgid "407 - Importation of goods – tax"
msgstr "407 - Importations de biens - taxe"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_supply_of_service_for_customer
msgid ""
"409 - Supply of services for which the customer is liable for the payment of"
" VAT – base"
msgstr ""
"409 - Prestations de services à déclarer par le preneur redevable de la taxe"
" - base"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_supply_of_service_for_customer_liable_for_payment_tax
msgid ""
"410 - Supply of services for which the customer is liable for the payment of"
" VAT – tax"
msgstr ""
"410 - Prestations de services à déclarer par le preneur redevable de la taxe"
" - taxe"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_7_inland_supplies_for_customer
msgid "419"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_7_inland_supplies_for_customer
msgid ""
"419 - Inland supplies for which the customer is liable for the payment of "
"VAT"
msgstr ""
"419 - Opérations à l'intérieur du pays pour lesquelles le preneur est le "
"redevable"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_b1_non_exempt_customer_vat
msgid "423"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_b1_non_exempt_customer_vat
msgid ""
"423 - not exempt in the MS where the customer is liable for payment of VAT"
msgstr ""
"423 - Prestations de services non exonérées dans l'Etat membre du preneur "
"redevable"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_6_b2_exempt_ms_customer
msgid "424"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_6_b2_exempt_ms_customer
msgid "424 - exempt in the MS where the customer is identified"
msgstr "424 - Prestations de services exonérées dans l'Etat membre du preneur"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_3
msgid "431"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_3
msgid "431 - not exempt within the territory: base 3%"
msgstr "431 - non exonérées à l'intérieur du pays: base 3%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_3
msgid "432"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_3
msgid "432 - not exempt within the territory: tax 3%"
msgstr "432 - non exonérées à l'intérieur du pays: tax 3%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_b_exempt
msgid "435"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_b_exempt
msgid "435 - exempt within the territory: exempt"
msgstr "435 - exonérées à l'intérieur du pays: exonérées"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_base
msgid "436 - base"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_3
msgid "441"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_3
msgid "441 - not established or residing within the Community: base 3%"
msgstr ""
"441 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: base 3%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_3
msgid "442"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_3
msgid "442 - not established or residing within the Community: tax 3%"
msgstr ""
"442 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: tax 3%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_exempt
msgid "445"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_exempt
msgid "445 - not established or residing within the Community: exempt"
msgstr ""
"445 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: exonérées"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_total_sale
msgid "454 - Total Sales / Receipts"
msgstr "454 - Total Ventes / Recettes"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1a_app_goods_non_bus
msgid "455"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_app_goods_non_bus
msgid ""
"455 - Application of goods for non-business use and for business purposes"
msgstr ""
"455 - Application de biens de l'utilisation privée et à des fins de "
"l'entreprise"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1a_non_bus_gs
msgid "456"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_non_bus_gs
msgid "456 - Non-business use of goods and supply of services free of charge"
msgstr ""
"456 - Prestations de services effectuées à des fins étrangères à "
"l'entreprise"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1b_1_intra_community_goods_pi_vat
msgid "457"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1b_1_intra_community_goods_pi_vat
msgid ""
"457 - Intra-Community supply of goods to persons identified for VAT purposes"
" in another Member State (MS)"
msgstr ""
"457 - Livraisons intracommunautaires de biens à des personnes identifiées à "
"la TVA dans un autre État membre"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_3a_1_invoiced_by_other_taxable_person
msgid "458"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_1_invoiced_by_other_taxable_person
msgid "458 - Invoiced by other taxable persons for goods or services supplied"
msgstr ""
"458 - Taxe facturée par d'autres assujettis pour des biens et des services "
"fournis"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_3a_2_due_respect_intra_comm_goods
msgid "459"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_2_due_respect_intra_comm_goods
msgid "459 - Due in respect of intra-Community acquisitions of goods"
msgstr ""
"459 - Taxe déclarée ou payée sur des acquisitions intracommunautaires de "
"biens"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_3a_3_due_paid_respect_importation_goods
msgid "460"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_3_due_paid_respect_importation_goods
msgid "460 - Due or paid in respect of importation of goods"
msgstr "460 - Taxe déclarée ou payée sur des biens importés"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_3a_5_due_under_reverse_charge
msgid "461"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3a_5_due_under_reverse_charge
msgid "461 - Due under the reverse charge (see points II.E and F)"
msgstr "461 - Taxe déclarée comme débiteur (cf points II.E et F)"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax
msgid "462 - tax"
msgstr "462 - taxe"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base
msgid "463 - base"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax
msgid "464 - tax"
msgstr "464 - taxe"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_1a_telecom_service
msgid "471"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_telecom_service
msgid ""
"471 - Telecommunications services, radio and television broadcasting "
"services..."
msgstr "471 - Prestations de services de télécom., de radio et de tv..."

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1a_other_sales
msgid "472 - Other sales / receipts"
msgstr "472 - Autres Ventes / Recettes"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_17
msgid "701"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_17
msgid "701 - base 17%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_17
msgid "702"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_17
msgid "702 - tax 17%"
msgstr "702 - taxe 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_14
msgid "703"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_14
msgid "703 - base 14%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_14
msgid "704"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_14
msgid "704 - tax 14%"
msgstr "704 - taxe 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_base_8
msgid "705"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_8
msgid "705 - base 8%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2a_tax_8
msgid "706"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_8
msgid "706 - tax 8%"
msgstr "706 - taxe 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_17
msgid "711"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_17
msgid "711 - base 17%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_17
msgid "712"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_17
msgid "712 - tax 17%"
msgstr "712 - taxe 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_14
msgid "713"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_14
msgid "713 - base 14%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_14
msgid "714"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_14
msgid "714 - tax 14%"
msgstr "714 - taxe 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_base_8
msgid "715"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_8
msgid "715 - base 8%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_tax_8
msgid "716"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_8
msgid "716 - tax 8%"
msgstr "716 - taxe 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2b_manufactured_tobacco
msgid "719"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_manufactured_tobacco
msgid ""
"719 - of manufactured tobacco (VAT is collected at the exit of the tax "
"warehouse with excise duties)"
msgstr ""
"719 - de tabacs fabriqués dont la TVA est perçue à la sortie de l'entrepôt "
"fiscal conjointement avec les accises"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_17
msgid "721"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_17
msgid "721 - for business purposes: base 17%"
msgstr "721 - à des fins de l'entreprise: base 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_17
msgid "722"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_17
msgid "722 - for business purposes: tax 17%"
msgstr "722 - à des fins de l'entreprise: taxe 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_14
msgid "723"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_14
msgid "723 - for business purposes: base 14%"
msgstr "723 - à des fins de l'entreprise: base 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_14
msgid "724"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_14
msgid "724 - for business purposes: tax 14%"
msgstr "724 - à des fins de l'entreprise: taxe 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_base_8
msgid "725"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_8
msgid "725 - for business purposes: base 8%"
msgstr "725 - à des fins de l'entreprise: base 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_tax_8
msgid "726"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_8
msgid "726 - for business purposes: tax 8%"
msgstr "726 - à des fins de l'entreprise: taxe 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_1_manufactured_tobacco
msgid "729"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_manufactured_tobacco
msgid ""
"729 - of manufactured tobacco (VAT is collected at the exit of the tax "
"warehouse with excise duties)"
msgstr ""
"729 - de tabacs fabriqués dont la TVA est perçue à la sortie de l'entrepôt "
"fiscal conjointement avec les accises"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_17
msgid "731"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_17
msgid "731 - for non-business purposes: base 17%"
msgstr "731 - à des fins étrangères à l'entreprise: base 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_17
msgid "732"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_17
msgid "732 - for non-business purposes: tax 17%"
msgstr "732 - à des fins étrangères à l'entreprise: taxe 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_14
msgid "733"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_14
msgid "733 - for non-business purposes: base 14%"
msgstr "733 - à des fins étrangères à l'entreprise: base 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_14
msgid "734"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_14
msgid "734 - for non-business purposes: tax 14%"
msgstr "734 - à des fins étrangères à l'entreprise: taxe 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_base_8
msgid "735"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_8
msgid "735 - for non-business purposes: base 8%"
msgstr "735 - à des fins étrangères à l'entreprise: base 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2d_2_tax_8
msgid "736"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_8
msgid "736 - for non-business purposes: tax 8%"
msgstr "736 - à des fins étrangères à l'entreprise: taxe 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_17
msgid "741"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_17
msgid "741 - not exempt within the territory: base 17%"
msgstr "741 - non exonérées à l'intérieur du pays: base 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_17
msgid "742"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_17
msgid "742 - not exempt within the territory: tax 17%"
msgstr "742 - non exonérées à l'intérieur du pays: taxe 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_14
msgid "743"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_14
msgid "743 - not exempt within the territory: base 14%"
msgstr "743 - non exonérées à l'intérieur du pays: base 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_14
msgid "744"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_14
msgid "744 - not exempt within the territory: tax 14%"
msgstr "744 - non exonérées à l'intérieur du pays: taxe 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_base_8
msgid "745"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_8
msgid "745 - not exempt within the territory: base 8%"
msgstr "745 - non exonérées à l'intérieur du pays: base 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_1_a_tax_8
msgid "746"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_8
msgid "746 - not exempt within the territory: tax 8%"
msgstr "746 - non exonérées à l'intérieur du pays: taxe 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_17
msgid "751"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_17
msgid "751 - not established or residing within the Community: base 17%"
msgstr ""
"751 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: base 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_17
msgid "752"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_17
msgid "752 - not established or residing within the Community: tax 17%"
msgstr ""
"752 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: taxe 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_14
msgid "753"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_14
msgid "753 - not established or residing within the Community: base 14%"
msgstr ""
"753 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: base 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_14
msgid "754"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_14
msgid "754 - not established or residing within the Community: tax 14%"
msgstr ""
"754 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: taxe 14%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_base_8
msgid "755"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_8
msgid "755 - not established or residing within the Community: base 8%"
msgstr ""
"755 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: base 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_2_tax_8
msgid "756"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_8
msgid "756 - not established or residing within the Community: tax 8%"
msgstr ""
"756 - effectuées au déclarant par des assujettis établis en dehors de la "
"Communauté: taxe 8%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_3_base_17
msgid "761"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_base_17
msgid "761 - suppliers established within the territory: base 17%"
msgstr ""
"761 - effectuées au déclarant par des assujettis établis à l'intérieur du "
"pays: base 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2e_3_tax_17
msgid "762"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax_17
msgid "762 - suppliers established within the territory: tax 17%"
msgstr ""
"762 - effectuées au déclarant par des assujettis établis à l'intérieur du "
"pays: taxe 17%"

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2f_supply_goods_base_8
msgid "763"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base_8
msgid "763 - base 8%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,tag_name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_8
msgid "764"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_8
msgid "764 - tax 8%"
msgstr "764 - taxe 8%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_base
msgid "765 - base"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax
msgid "766 - tax"
msgstr "766 - taxe"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base
msgid ""
"767 - Supply of goods for which the purchaser is liable for the payment of "
"VAT - base"
msgstr ""
"767 - Livraisons de biens à déclarer par l'acquéreur redevable de la taxe - "
"base"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax
msgid ""
"768 - Supply of goods for which the purchaser is liable for the payment of "
"VAT - tax"
msgstr ""
"768 - Livraisons de biens à déclarer par l'acquéreur redevable de la taxe - "
"taxe"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_16
msgid "921 - for business purposes: base 16%"
msgstr "921 - à des fins de l'entreprise: base 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_13
msgid "923 - for business purposes: base 13%"
msgstr "923 - à des fins de l'entreprise: base 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_base_7
msgid "925 - for business purposes: base 7%"
msgstr "925 - à des fins de l'entreprise: base 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_16
msgid "931 - for non-business purposes: base 16%"
msgstr "931 - à des fins étrangères à l'entreprise: base 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_13
msgid "933 - for non-business purposes: base 13%"
msgstr "933 - à des fins étrangères à l'entreprise: base 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_base_7
msgid "935 - for non-business purposes: base 7%"
msgstr "935 - à des fins étrangères à l'entreprise: base 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_16
msgid "941 - not exempt within the territory: base 16%"
msgstr "941 - non exonérées à l'intérieur du pays: base 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_13
msgid "943 - not exempt within the territory: base 13%"
msgstr "943 - non exonérées à l'intérieur du pays: base 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_base_7
msgid "945 - not exempt within the territory: base 7%"
msgstr "945 - non exonérées à l'intérieur du pays: base 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_16
msgid "951 - not established or residing within the Community: base 16%"
msgstr "951 - effectuées au déclarant par des assujettis établis en dehors "
"de la Communauté: base 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_13
msgid "953 - not established or residing within the Community: base 13%"
msgstr "953 - effectuées au déclarant par des assujettis établis en dehors "
"de la Communauté: base 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_base_7
msgid "955 - not established or residing within the Community: base 7%"
msgstr "955 - effectuées au déclarant par des assujettis établis en dehors "
"de la Communauté: base 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_base_16
msgid "961 - suppliers established within the territory: base 16%"
msgstr "961 - effectuées au déclarant par des assujettis établis "
"à l'intérieur du pays: base 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_base_7
msgid "963 - base 7%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2f_supply_goods_tax_7
msgid "964 - tax 7%"
msgstr "964 - taxe 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_16
msgid "901 - base 16%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_13
msgid "903 - base 13%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_base_7
msgid "905 - base 7%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_16
msgid "902 - tax 16%"
msgstr "902 - taxe 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_13
msgid "904 - tax 13%"
msgstr "904 - taxe 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2a_tax_7
msgid "906 - tax 7%"
msgstr "906 - taxe 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_16
msgid "911 - base 16%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_13
msgid "913 - base 13%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_base_7
msgid "915 - base 7%"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_16
msgid "912 - tax 16%"
msgstr "912 - taxe 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_13
msgid "914 - tax 13%"
msgstr "914 - taxe 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2b_tax_7
msgid "916 - tax 7%"
msgstr "916 - taxe 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_16
msgid "922 - for business purposes: tax 16%"
msgstr "922 - à des fins de l'entreprise: taxe 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_13
msgid "924 - for business purposes: tax 13%"
msgstr "924 - à des fins de l'entreprise: taxe 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_1_tax_7
msgid "926 - for business purposes: tax 7%"
msgstr "926 - à des fins de l'entreprise: taxe 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_16
msgid "932 - for non-business purposes: tax 16%"
msgstr "932 - à des fins étrangères à l'entreprise: taxe 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_13
msgid "934 - for non-business purposes: tax 13%"
msgstr "934 - à des fins étrangères à l'entreprise: taxe 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2d_2_tax_7
msgid "936 - for non-business purposes: tax 7%"
msgstr "936 - à des fins étrangères à l'entreprise: taxe 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_16
msgid "942 - not exempt within the territory: tax 16%"
msgstr "942 - non exonérées à l'intérieur du pays: taxe 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_13
msgid "944 - not exempt within the territory: tax 13%"
msgstr "944 - non exonérées à l'intérieur du pays: taxe 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_1_a_tax_7
msgid "946 - not exempt within the territory: tax 7%"
msgstr "946 - non exonérées à l'intérieur du pays: taxe 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_16
msgid "952 - not established or residing within the Community: tax 16%"
msgstr "952 - effectuées au déclarant par des assujettis établis en dehors "
"de la Communauté: taxe 16%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_13
msgid "954 - not established or residing within the Community: tax 13%"
msgstr "954 - effectuées au déclarant par des assujettis établis en dehors "
"de la Communauté: taxe 13%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_2_tax_7
msgid "956 - not established or residing within the Community: tax 7%"
msgstr "956 - effectuées au déclarant par des assujettis établis en dehors "
"de la Communauté: taxe 7%"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2e_3_tax_16
msgid "962 - suppliers established within the territory: tax 16%"
msgstr "962 - effectuées au déclarant par des assujettis établis "
"à l'intérieur du pays: taxe 16%"

#. module: l10n_lu
#: model:ir.model,name:l10n_lu.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_1_assessment_taxable_turnover
msgid "I. ASSESSMENT OF TAXABLE TURNOVER"
msgstr "I. CALCUL DU CHIFFRE D'AFFAIRES IMPOSABLE"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_2_assesment_of_tax_due
msgid "II. ASSESSMENT OF TAX DUE (output tax)"
msgstr "II. CALCUL DE LA TAXE DUE (taxe en aval)"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_3_assessment_deducible_tax
msgid "III. ASSESSMENT OF DEDUCTIBLE TAX (input tax)"
msgstr "III. CALCUL DE LA TAXE DEDUCTIBLE (taxe en amont)"

#. module: l10n_lu
#: model:account.report.line,name:l10n_lu.account_tax_report_line_4_tax_tobe_paid_or_reclaimed
msgid "IV. TAX TO BE PAID OR TO BE RECLAIMED"
msgstr "IV. CALCUL DE L'EXCEDENT"

#. module: l10n_lu
#: model:ir.ui.menu,name:l10n_lu.account_reports_lu_statements_menu
msgid "Luxembourg"
msgstr ""
