# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* im_livechat
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:02+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
msgid "\"%s\" is not a valid email."
msgstr "\"%s\" は有効なEメールではありません。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "# Messages"
msgstr "# メッセージ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_count
msgid "# Ratings"
msgstr "評価数"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__nbr_channel
msgid "# of Sessions"
msgstr "セッション数"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "# of speakers"
msgstr "# スピーカー"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "% Happy"
msgstr "% 満足"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating
msgid "% of Happiness"
msgstr "満足度% "

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
msgid "%s (copy)"
msgstr "%s (コピー)"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script.py:0
#: code:addons/im_livechat/models/discuss_channel.py:0
msgid ""
"'%(input_email)s' does not look like a valid email. Can you please try "
"again?"
msgstr "'%(input_email)s' は有効なEメールではないようです。再度試しますか？"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__action
msgid ""
"* 'Show' displays the chat button on the pages.\n"
"* 'Show with notification' is 'Show' in addition to a floating text just next to the button.\n"
"* 'Open automatically' displays the button and automatically opens the conversation pane.\n"
"* 'Hide' hides the chat button on the pages.\n"
msgstr ""
"* '表示' はページ上にチャットボタンを表示します。\n"
"* '通知で表示'は'表示'に加え、ボタンのすぐ横にフローティングテキストを表示します。\n"
"* '自動的に開く'はボタンを表示し、自動的に会話ペインを開きます。\n"
"* '隠す'はページ上のチャットボタンを隠します。\n"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid ", on the"
msgstr "、以下で:"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__day_number
msgid "1 is Monday, 7 is Sunday"
msgstr "1は月曜、7は日曜"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"
msgstr ""
"<i class=\"fa fa-smile-o text-success\" title=\"Percentage of happy "
"ratings\" role=\"img\" aria-label=\"Happy face\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<i class=\"oi oi-fw oi-arrow-right\"/>Back to edit mode"
msgstr "<i class=\"oi oi-fw oi-arrow-right\"/>編集モードに戻る"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "<i title=\"Remove operator\" class=\"fa fa-fw fa-lg fa-close\"/>"
msgstr "<i title=\"Remove operator\" class=\"fa fa-fw fa-lg fa-close\"/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
msgid "<span class=\"fw-bold\">Country: </span>"
msgstr "<span class=\"fw-bold\">国: </span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span style=\"font-size: 10px;\">Livechat Conversation</span><br/>"
msgstr "<span style=\"font-size: 10px;\">ライブチャット会話</span><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Best regards,</span><br/><br/>"
msgstr "<span>宜しくお願い致します。</span><br/><br/>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "<span>Hello,</span><br/>Here's a copy of your conversation with"
msgstr "<span>こんにちは</span><br/>あなたの以下との会話のコピーです:"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Reminder: This step will only be played if no operator is "
"available.</span>"
msgstr "<span>リマインダ：このステップは、オペレーターが不在の場合のみ再生されます。</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before the Bot can perform more complex actions (Forward to an Operator, ...). </span>\n"
"                    <span>Use Channel Rules if you want the Bot to interact with visitors only when no operator is available.</span>"
msgstr ""
"<span>ヒント: ボットがより複雑なアクション(オペレータに転送、...)を実行する前に、少なくとも1回の対話(質問、Eメール、...)が必要です。 </span>\n"
"                    <span>オペレータが利用できない場合にのみボットに訪問者と対話させたい場合は、チャネル規則を使用します。</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid ""
"<span>Tip: At least one interaction (Question, Email, ...) is needed before "
"the Bot can perform more complex actions (Forward to an Operator, "
"...).</span>"
msgstr ""
"<span>ヒント: "
"ボットがより複雑なアクション(オペレータに転送、...)を実行する前に、少なくとも1回の対話(質問、Eメール、...)が必要です。</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid ""
"<span>Tip: Plan further steps for the Bot in case no operator is "
"available.</span>"
msgstr "<span>ヒント: オペレーターがいない場合に備えて、ボットのさらなる手順を計画しましょう。</span>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "<span>You are currently testing</span>"
msgstr "<span>現在テスト中です</span>"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_chatbot_message__unique_mail_message_id
msgid "A mail.message can only be linked to a single chatbot message"
msgstr "mail.messageは1つのチャットボットメッセージにのみリンクできます。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid ""
"A session is without answer if the operator did not answer. \n"
"                                       If the visitor is also the operator, the session will always be answered."
msgstr ""
"オペレータが応答しなかった場合、セッションには応答がありません。\n"
"　　　　　　　　　　訪問者がオペレータでもある場合、セッションには必ず応答があります。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction
msgid "Action Needed"
msgstr "要アクション"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__active
msgid "Active"
msgstr "有効化"

#. module: im_livechat
#: model:res.groups,name:im_livechat.im_livechat_group_manager
msgid "Administrator"
msgstr "管理者"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
msgid "An error occurred. Please try again."
msgstr "エラーが発生しました。もう一度試して下さい。"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_redirect
msgid "And tadaaaa here you go! 🌟"
msgstr "では、こちらでございます！ 🌟"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__anonymous_name
msgid "Anonymous Name"
msgstr "匿名"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__name
msgid "Answer"
msgstr "回答"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__answer_ids
msgid "Answers"
msgstr "回答"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Archived"
msgstr "アーカイブ済"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__are_you_inside
msgid "Are you inside the matrix?"
msgstr "マトリクス内にいますか？"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_attachment_count
msgid "Attachment Count"
msgstr "添付数"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__available_operator_ids
msgid "Available Operator"
msgstr "対応可能なオペレータ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg
msgid "Average Rating"
msgstr "平均評価"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_avg_percentage
msgid "Average Rating (%)"
msgstr "平均評価（％）"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__duration
msgid "Average duration"
msgstr "平均時間"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Average message"
msgstr "平均メッセージ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating"
msgstr "評価平均"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__rating
msgid "Average rating given by the visitor"
msgstr "訪問者による評価平均"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Average time in seconds to give the first answer to the visitor"
msgstr "訪問者に対する初期回答までの平均時間"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Average time to give the first answer to the visitor"
msgstr "訪問者に対する初期回答までの平均時間"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Bad Ratings"
msgstr "低評価"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__operator_partner_id
msgid "Bot Operator"
msgstr "ボットオペレータ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_background_color
msgid "Button Background Color"
msgstr "ボタン背景色"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text_color
msgid "Button Text Color"
msgstr "ボタンテキスト色"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.canned_responses
msgid "Canned Responses"
msgstr "返信定型文"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__livechat_channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__livechat_channel_id
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Channel"
msgstr "チャネル"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Header Color"
msgstr "チャネルヘッダ色"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel_member
msgid "Channel Member"
msgstr "チャネルメンバ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_name
msgid "Channel Name"
msgstr "チャネル名"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Channel Rule"
msgstr "チャネル規則"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Channel Rules"
msgstr "チャネル規則"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "チャネルタイプ"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.support_channels
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Channels"
msgstr "チャネル"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Chat Input Placeholder"
msgstr "チャット入力ボックス"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""
"チャットは2人だけのプライベートなものです。グループは招待された人だけのプライベートなものです。チャネルは (設定により)自由に参加できます。"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.chatbot_script_action
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__chatbot_script_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_script_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Chatbot"
msgstr "チャットボット"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_current_step_id
msgid "Chatbot Current Step"
msgstr "チャットボット現在のステップ"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_message
msgid "Chatbot Message"
msgstr "チャットボットメッセージ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__chatbot_message_ids
msgid "Chatbot Messages"
msgstr "チャットボットメッセージ"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Chatbot Name"
msgstr "チャットボット名"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script
msgid "Chatbot Script"
msgstr "チャットボットスクリプト"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_answer
msgid "Chatbot Script Answer"
msgstr "チャットボットスクリプト回答"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_chatbot_script_step
msgid "Chatbot Script Step"
msgstr "チャットボットスクリプト ステップ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__script_step_id
msgid "Chatbot Step"
msgstr "チャットボットステップ"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.chatbot_config
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Chatbots"
msgstr "チャットボット"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_test_script_page
msgid "Close"
msgstr "閉じる"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Close conversation"
msgstr "会話を閉じる"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__technical_name
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Code"
msgstr "コード"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.livechat_config
msgid "Configuration"
msgstr "設定"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Configure Channel"
msgstr "チャネル設定"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_partner
msgid "Contact"
msgstr "連絡先"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__channel_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__channel_id
msgid "Conversation"
msgstr "会話"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
msgid "Conversation ended..."
msgstr "会話が終了しました。"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
msgid "Conversation with %s"
msgstr "%sとの会話"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations
msgid "Conversations handled"
msgstr "会話対応済"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Copy and paste this code into your website, within the &lt;head&gt; tag:"
msgstr "このコードをコピーして、あなたのウェブサイトの &lt;head&gt; タグ内に貼り付けて下さい:"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__country_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__country_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
msgid "Country"
msgstr "国"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__country_id
msgid "Country of the visitor"
msgstr "訪問者の国"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__country_id
msgid "Country of the visitor of the channel"
msgstr "チャネル訪問者の国"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid "Create a Chatbot"
msgstr "チャットボットを作成"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Create a channel and start chatting to fill up your history."
msgstr "チャネルを作成してチャットを開始し、履歴を埋めて下さい。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_uid
msgid "Created by"
msgstr "作成者"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__create_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__create_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__create_date
msgid "Created on"
msgstr "作成日"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Creation date"
msgstr "作成日"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Creation date (hour)"
msgstr "作成日 (時間)"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat_report
#: model:ir.ui.menu,name:im_livechat.rating_rating_menu_livechat
msgid "Customer Ratings"
msgstr "顧客評価"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
msgid "Date:"
msgstr "日付:"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__day_number
msgid "Day Number"
msgstr "日付番号"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Days of activity"
msgstr "活動日数"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_background_color
msgid "Default background color of the Livechat button"
msgstr "Webチャットボタンのデフォルト背景色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__header_background_color
msgid "Default background color of the channel header once open"
msgstr "チャンネルヘッダを開いたときのデフォルト背景色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text_color
msgid "Default text color of the Livechat button"
msgstr "Webチャットボタンのデフォルト文字色"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__button_text
msgid "Default text displayed on the Livechat Support Button"
msgstr "Webチャットサポートボタンに表示されるデフォルトテキスト"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__title_color
msgid "Default title color of the channel once open"
msgstr "チャネルを開いたときのデフォルトタイトル色"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid "Define a new website live chat channel"
msgstr "新規ウェブサイトチャットチャネルを定義"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Define rules for your live support channel. You can apply an action for the "
"given URL, and per country.<br/>To identify the country, GeoIP must be "
"installed on your server, otherwise, the countries of the rule will not be "
"taken into account."
msgstr ""
"ライブサポートチャネルの規則を定義します。与えられたURLと国ごとにアクションを適用することができます。<br/>国を特定するには、GeoIPをサーバーにインストールする必要があります。そうでない場合は、規則上の国が考慮されません。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid ""
"Delay (in seconds) to automatically open the conversation window. Note: the "
"selected action must be 'Open automatically' otherwise this parameter will "
"not be taken into account."
msgstr "会話ウィンドウを自動的に開くまでの遅延時間（秒）。注：選択したアクションが「自動で開く」でなければ、このパラメータは考慮されません。"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Did we correctly answer your question?"
msgstr "あなたの質問に正しく回答しましたか？"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_digest_digest
msgid "Digest"
msgstr "ダイジェスト"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/views/livechat_view_controller_mixin.js:0
msgid "Discuss"
msgstr "ディスカス"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_discuss_channel
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__discuss_channel_id
msgid "Discussion Channel"
msgstr "ディスカッションチャンネル"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__display_name
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__display_name
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__display_name
msgid "Display Name"
msgstr "表示名"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_button.xml:0
msgid "Drag to Move"
msgstr "ドラッグして移動"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__duration
msgid "Duration"
msgstr "時間"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Duration of Session (min)"
msgstr "セッション時間(分)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__duration
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_operator__duration
msgid "Duration of the conversation (in seconds)"
msgstr "会話の長さ(秒)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__duration
msgid "Duration of the session in hours"
msgstr "会話の長さ(時間)"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
msgid "Duration:"
msgstr "期間:"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_email
msgid "Email"
msgstr "メール"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
msgid "Enable the bot only if there is no operator available"
msgstr "オペレータが対応不可能な場合のみボットを有効にします"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__chatbot_only_if_no_operator
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "Enabled only if no operator"
msgstr "オペレーターがいない場合のみ有効"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Explain your note"
msgstr "メモを説明"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_invalid
msgid "First Step Invalid"
msgstr "第1ステップ無効"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script__first_step_warning__first_step_operator
msgid "First Step Operator"
msgstr "第1ステップオペレータ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__first_step_warning
msgid "First Step Warning"
msgstr "第1ステップ警告"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_follower_ids
msgid "Followers"
msgstr "フォロワー"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_partner_ids
msgid "Followers (Partners)"
msgstr "フォロワー (取引先)"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"For websites built with the Odoo CMS, go to Website &gt; Configuration &gt; "
"Settings and select the Website Live Chat Channel you want to add to your "
"website."
msgstr ""
"Odoo CMSで構築されたウェブサイトの場合、ウェブサイト > 設定 > "
"管理設定で、ウェブサイトに追加したいウェブサイトWebチャットチャネルを選択してください。"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__forward_operator
msgid "Forward to Operator"
msgstr "オペレーターに転送"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_single
msgid "Free Input"
msgstr "自由入力"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__free_input_multi
msgid "Free Input (Multi-Line)"
msgstr "自由入力 (複数行)"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__sequence
msgid ""
"Given the order to find a matching rule. If 2 rules are matching for the "
"given url/country, the one with the lowest sequence will be chosen."
msgstr "一致する規則を見つけるための順序が与えられます。与えられた URL/国に一致する規則が2つある場合、順序が最も低いものが選択されます。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Good Ratings"
msgstr "高評価"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Group By..."
msgstr "グループ化…"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__has_message
msgid "Has Message"
msgstr "メッセージあり"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__has_access_livechat
msgid "Has access to Livechat"
msgstr "ライブチャットへのアクセスあり"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: model:im_livechat.channel,button_text:im_livechat.im_livechat_channel_data
msgid "Have a Question? Chat with us."
msgstr "ご質問ですか？チャットでお問い合わせください。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__header_background_color
msgid "Header Background Color"
msgstr "ヘッダ背景色"

#. module: im_livechat
#: model:im_livechat.channel,default_message:im_livechat.im_livechat_channel_data
msgid "Hello, how may I help you?"
msgstr "こんにちは。どのようなご用件でしょうか？"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__hide_button
msgid "Hide"
msgstr "非表示"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "History"
msgstr "履歴"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing
msgid ""
"Hmmm, let me check if I can find someone that could help you with that..."
msgstr "それについてお手伝いできる者がいるかどうか確認してみます"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date_hour
msgid "Hour of start Date of session"
msgstr "セッション開始日の時間"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/im_livechat_channel.py:0
msgid "How may I help you?"
msgstr "どのようなご用件でしょうか？"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "How to use the Website Live Chat widget?"
msgstr "ウェブサイトWebチャットウィジェットの使用方法"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_noone_available
msgid "Hu-ho, it looks like none of our operators are available 🙁"
msgstr "申し訳ございません。オペレーターは誰もいないようです 🙁"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_just_looking
msgid "I am just looking around"
msgstr "ただ見て回っているだけです"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_documentation
msgid "I am looking for your documentation"
msgstr "ドキュメントを探しています"

#. module: im_livechat
#: model:chatbot.script.answer,name:im_livechat.chatbot_script_welcome_step_dispatch_answer_pricing
msgid "I have a pricing question"
msgstr "価格について質問があります"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__id
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__id
msgid "ID"
msgstr "ID"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction
msgid "If checked, new messages require your attention."
msgstr "チェックした場合は、新しいメッセージに注意が必要です。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "チェックした場合は、一部のメッセージに配信エラーが発生されました。"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_documentation_exit
msgid "If you need anything else, feel free to get back in touch"
msgstr "何かございましたら、お気軽にご連絡ください。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1920
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__image_128
msgid "Image"
msgstr "画像"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_1024
msgid "Image 1024"
msgstr "画像1024"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_128
msgid "Image 128"
msgstr "画像128"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_256
msgid "Image 256"
msgstr "画像256"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__image_512
msgid "Image 512"
msgstr "画像512"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_is_follower
msgid "Is Follower"
msgstr "フォロー中　"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__is_forward_operator_child
msgid "Is Forward Operator Child"
msgstr "オペレーター子に転送"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_active
msgid "Is livechat ongoing?"
msgstr "ライブチャットが進行中ですか？"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_anonymous
msgid "Is visitor anonymous"
msgstr "匿名訪問者"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Join"
msgstr "参加"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/livechat_channel_model_patch.js:0
msgid "Join %s"
msgstr "参加%s"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Join Channel"
msgstr "チャネルに参加"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_conversations_value
msgid "Kpi Livechat Conversations Value"
msgstr "KPI ライブチャット会話 値"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_rating_value
msgid "Kpi Livechat Rating Value"
msgstr "KPI ライブチャット評価値"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response_value
msgid "Kpi Livechat Response Value"
msgstr "KPI ライブチャットレスポンス値"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_invitation_patch.xml:0
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
msgid "Lang"
msgstr "Lang"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Last 24h"
msgstr "直近24時間"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_uid
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__write_date
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__write_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Leave"
msgstr "解除"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/livechat_channel_model_patch.js:0
msgid "Leave %s"
msgstr "退出%s"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Leave Channel"
msgstr "チャネルから退席"

#. module: im_livechat
#: model:ir.module.category,name:im_livechat.module_category_im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_livechat_root
#: model_terms:ir.ui.view,arch_db:im_livechat.digest_digest_view_form_inherit
msgid "Live Chat"
msgstr "Webチャット"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__action
msgid "Live Chat Button"
msgstr "Webチャットボタン"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_search
msgid "LiveChat Channel Search"
msgstr "ライブチャットチャネル検索"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/public_web/discuss_app_model_patch.js:0
#: code:addons/im_livechat/static/src/core/public_web/messaging_menu_patch.js:0
#: code:addons/im_livechat/static/src/core/web/thread_icon_patch.js:0
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
msgid "Livechat"
msgstr "ライブチャット"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button"
msgstr "Webチャットボタン"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Button Color"
msgstr "Webチャットボタン色"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "Livechat Channel"
msgstr "Webチャットチャネル"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__livechat_channel_count
msgid "Livechat Channel Count"
msgstr "ライブチャットチャネルカウント"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_channel_rule
msgid "Livechat Channel Rules"
msgstr "ライブチャットチャネル規則"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__discuss_channel__channel_type__livechat
msgid "Livechat Conversation"
msgstr "Webチャットの会話"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.unit_embed_suite
msgid "Livechat External Tests"
msgstr "ライブチャット外部テスト"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_lang_ids
msgid "Livechat Languages"
msgstr "ライブチャット言語"

#. module: im_livechat
#: model:ir.model.constraint,message:im_livechat.constraint_discuss_channel_livechat_operator_id
msgid "Livechat Operator ID is required for a channel of type livechat."
msgstr "タイプライブチャットのチャネル用にライブチャットオペレータIDが必要です。"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_channel
msgid "Livechat Support Channel Report"
msgstr "ライブチャットサポートチャネルレポート"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_action
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_operator_action
msgid ""
"Livechat Support Channel Statistics allows you to easily check and analyse "
"your company livechat session performance. Extract information about the "
"missed sessions, the audience, the duration of a session, etc."
msgstr ""
"ライブチャットサポートチャネル統計は、会社のライブチャットセッションのパフォーマンスを簡単に確認し、分析することができます。見逃したセッション、視聴者、セッション時間などの情報を抽出します。"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_im_livechat_report_operator
msgid "Livechat Support Operator Report"
msgstr "ライブチャット サポートオペレータ レポート"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_pivot
msgid "Livechat Support Statistics"
msgstr "Webチャットサポート統計"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users__livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_username
msgid "Livechat Username"
msgstr "ライブチャットユーザ名"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Livechat Window"
msgstr "Webチャットウィンドウ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_users_settings__livechat_lang_ids
msgid "Livechat languages"
msgstr "ライブチャット言語"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__livechat_active
msgid "Livechat session is active until visitor leaves the conversation."
msgstr "ライブチャットセッションは、訪問者が会話を離れるまで有効です。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__sequence
msgid "Matching order"
msgstr "一致するオーダ"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_mail_message
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__message
msgid "Message"
msgstr "メッセージ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error
msgid "Message Delivery error"
msgstr "メッセージ配信エラー"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_ids
msgid "Messages"
msgstr "メッセージ"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_graph
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_pivot
msgid "Messages per session"
msgstr "セッション毎メッセージ"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Missed sessions"
msgstr "逃したセッション"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "My Sessions"
msgstr "自分のセッション"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__name
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_search
msgid "Name"
msgstr "名称"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
msgid "No available collaborator, please try again later."
msgstr "対応可能な協力者がいません。後で試して下さい。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat_report
msgid "No customer ratings on live chat session yet"
msgstr "ライブチャットセッションに顧客評価がまだありません。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_report_channel_time_to_answer_action
msgid "No data yet!"
msgstr "まだデータはありません！"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/res_partner.py:0
msgid "No history found"
msgstr "履歴が見つかりません"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Notification text"
msgstr "通知テキスト"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of Actions"
msgstr "アクション数"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__chatbot_script_count
msgid "Number of Chatbot"
msgstr "チャットボット数"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__nbr_channel
msgid "Number of conversation"
msgstr "会話数"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__days_of_activity
msgid "Number of days since the first session of the operator"
msgstr "オペレータの初めてのセッションからの日数"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_speaker
msgid "Number of different speakers"
msgstr "異なるスピーカーの数"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of errors"
msgstr "エラー数"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_report_channel__nbr_message
msgid "Number of message in the conversation"
msgstr "会話の中のメッセージ数"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "アクションを必要とするメッセージの数"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "配信エラーが発生されたメッセージ数"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Odoo"
msgstr "Odoo"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Language"
msgstr "オンラインチャット言語"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.res_users_form_view_simple_modif
msgid "Online Chat Name"
msgstr "オンラインチャット名"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Only If"
msgstr "表示条件"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__auto_popup
msgid "Open automatically"
msgstr "自動で開く"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__auto_popup_timer
msgid "Open automatically timer"
msgstr "自動で開くのタイマー"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__livechat_operator_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__partner_id
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__partner_id
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Operator"
msgstr "担当者"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_operator_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_operator
msgid "Operator Analysis"
msgstr "オペレーター分析"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__user_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Operators"
msgstr "オペレーター"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid ""
"Operators that do not show any activity In Odoo for more than 30 minutes "
"will be considered as disconnected."
msgstr "30分以上、Odooで何の活動も見られないオペレーターは、切断されたものとみなされます。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_answer_view_tree
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "Optional Link"
msgstr "オプションリンク"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Options"
msgstr "オプション"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_author_name
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_author_name
msgid "Parent Author Name"
msgstr "親投稿者名"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_mail_mail__parent_body
#: model:ir.model.fields,field_description:im_livechat.field_mail_message__parent_body
msgid "Parent Body"
msgstr "親本文"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Participant"
msgstr "参加者"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Participants"
msgstr "対象者"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Percentage of happy ratings"
msgstr "高評価割合"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_phone
msgid "Phone"
msgstr "電話"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
msgid "Please call me on: "
msgstr "以下にお電話下さい:"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/chatbot_script_step.py:0
msgid "Please contact me on: "
msgstr "以下にご連絡下さい:"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_just_looking
msgid "Please do! If there is anything we can help with, let us know"
msgstr "どうぞ見て回ってください。何かお手伝いできることがあれば、お知らせください"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "Powered by"
msgstr "Powered by"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__question_selection
msgid "Question"
msgstr "質問"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: model:ir.model,name:im_livechat.model_rating_rating
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_kanban
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Rating"
msgstr "評価"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_avg_text
msgid "Rating Avg Text"
msgstr "評価平均テキスト"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_feedback
msgid "Rating Last Feedback"
msgstr "評価前回のフィードバック"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_image
msgid "Rating Last Image"
msgstr "評価前回の画像"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_value
msgid "Rating Last Value"
msgstr "評価前回の数値"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_percentage_satisfaction
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_percentage_satisfaction
msgid "Rating Satisfaction"
msgstr "評価満足度"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_last_text
msgid "Rating Text"
msgstr "評価テキスト"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_count
msgid "Rating count"
msgstr "評価数"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__rating_ids
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rating_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Ratings"
msgstr "評価"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.rating_rating_action_livechat
msgid "Ratings for livechat channel"
msgstr "ライブチャットチャネル評価"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
msgid "Receive a copy of this conversation."
msgstr "この会話のコピーを受取る"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__redirect_link
msgid "Redirect Link"
msgstr "リダイレクトリンク"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__regex_url
msgid ""
"Regular expression specifying the web pages this rule will be applied on."
msgstr "この規則を適用するウェブページを指定する正規表現。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__mail_message_id
msgid "Related Mail Message"
msgstr "関連するメールメッセージ"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat
msgid "Report"
msgstr "レポート"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/js/colors_reset_button/colors_reset_button.xml:0
msgid "Reset to default colors"
msgstr "デフォルト色へ再設定"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/thread_actions.js:0
msgid "Restart Conversation"
msgstr "会話を再開する"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
msgid "Restarting conversation..."
msgstr "会話を再開しています..."

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__rule_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_tree
msgid "Rules"
msgstr "規則"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS配信エラー"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__rating_text
msgid "Satisfaction Rate"
msgstr "満足度"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Save your Channel to get your configuration widget."
msgstr "チャネルを保存すると、設定ウィジェットが表示されます。"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
msgid "Say something"
msgstr "何かお話し下さい"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/composer_patch.js:0
msgid "Say something..."
msgstr "何かお話下さい..."

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "Script"
msgstr "スクリプト"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__script_external
msgid "Script (external)"
msgstr "スクリプト (外部)"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__script_step_id
msgid "Script Step"
msgstr "スクリプトステップ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__script_step_ids
msgid "Script Steps"
msgstr "スクリプトステップ"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Search history"
msgstr "検索履歴"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "Search report"
msgstr "レポートを検索"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_commands_patch.js:0
msgid "See 15 last visited pages"
msgstr "最後に訪問された15ページを見る"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/chatbot/chatbot_service.js:0
msgid "Select an option above"
msgstr "上記のオプションを選択"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Send"
msgstr "送信"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_answer__sequence
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__sequence
msgid "Sequence"
msgstr "シーケンス"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_tree
msgid "Session Date"
msgstr "セッション日"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_form
msgid "Session Form"
msgstr "セッションフォーム"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_action
#: model:ir.actions.act_window,name:im_livechat.im_livechat_report_channel_time_to_answer_action
#: model:ir.ui.menu,name:im_livechat.menu_reporting_livechat_channel
msgid "Session Statistics"
msgstr "セッション統計"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_unrated
msgid "Session not rated"
msgstr "評価なしセッション"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_without_answer
msgid "Session(s) without answer"
msgstr "回答なしセッション"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.discuss_channel_action_from_livechat_channel
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__channel_ids
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_kanban
msgid "Sessions"
msgstr "セッション"

#. module: im_livechat
#: model:ir.ui.menu,name:im_livechat.session_history
msgid "Sessions History"
msgstr "セッション履歴"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button
msgid "Show"
msgstr "表示"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_step__triggering_answer_ids
msgid "Show this step only if all of these answers have been selected."
msgstr "これらの回答がすべて選択されている場合のみ、このステップを表示します。"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__im_livechat_channel_rule__action__display_button_and_text
msgid "Show with notification"
msgstr "通知と表示する"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__source_id
msgid "Source"
msgstr "情報源"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_date
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__start_date
msgid "Start Date of session"
msgstr "セッション開始日"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__start_hour
msgid "Start Hour of session"
msgstr "セッションの開始時間"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script_step__step_type
msgid "Step Type"
msgstr "ステップタイプ"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/composer_patch.js:0
#: code:addons/im_livechat/static/src/core/web/composer_patch.xml:0
msgid "Tab to next livechat"
msgstr "次のライブチャットへのタブ"

#. module: im_livechat
#: model:ir.model.fields.selection,name:im_livechat.selection__chatbot_script_step__step_type__text
msgid "Text"
msgstr "テキスト"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__button_text
msgid "Text of the Button"
msgstr "ボタンのテキスト"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__input_placeholder
msgid "Text that prompts the user to initiate the chat."
msgstr "ユーザにチャットの開始を促すテキスト"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Text to display on the notification"
msgstr "通知に表示されるテキスト"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/feedback_panel.xml:0
msgid "Thank you for your feedback"
msgstr "ご意見頂きありがとうございます"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__channel_id
msgid "The channel of the rule"
msgstr "規則のチャネル"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
msgid "The conversation was sent."
msgstr "会話が送信されました。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel_rule__country_ids
msgid ""
"The rule will only be applied for these countries. Example: if you select "
"'Belgium' and 'United States' and that you set the action to 'Hide', the "
"chat button will be hidden on the specified URL from the visitors located in"
" these 2 countries. This feature requires GeoIP installed on your server."
msgstr ""
"規則はこれらの国にのみ適用されます。例: 'ベルギー' と '米国' を選択し、アクションを '隠す' に設定した場合、これらの 2 "
"つの国にいる訪問者に対しては指定されたURLのチャットボタンが非表示になります。この機能を使用するには、サーバに GeoIP "
"がインストールされている必要があります。"

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_manager
msgid "The user will be able to delete support channels."
msgstr "ユーザはサポートチャネルを削除できるようになります。"

#. module: im_livechat
#: model:res.groups,comment:im_livechat.im_livechat_group_user
msgid "The user will be able to join support channels."
msgstr "ユーザはサポートチャネルに参加できるようになります。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_chatbot_script_answer__redirect_link
msgid ""
"The visitor will be redirected to this link upon clicking the option (note "
"that the script will end if the link is external to the livechat website)."
msgstr ""
"訪問者は、オプションをクリックすると、このリンクにリダイレクトされます(リンクがライブチャットウェブサイトの外部である場合、スクリプトは終了することに注意して下さい)。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.rating_rating_action_livechat
msgid "There is no rating for this channel at the moment"
msgstr "このチャネルに対する評価はまだありません。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_lang_ids
msgid ""
"These languages, in addition to your main language, will be used to assign "
"you to Live Chat sessions."
msgstr "これらの言語は、メイン言語に加えて、ライブチャットセッションの割当に使用されます。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
msgid "This Month"
msgstr "今月"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_operator_view_search
#: model_terms:ir.ui.view,arch_db:im_livechat.rating_rating_view_search_livechat
msgid "This Week"
msgstr "今週"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__default_message
msgid ""
"This is an automated 'welcome' message that your visitor will see when they "
"initiate a new conversation."
msgstr "これは、訪問者が新しい会話を始めたときに表示される自動の’ウェルカム'メッセージです。"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_res_users_settings__livechat_username
msgid "This username will be used as your name in the livechat channels."
msgstr "このユーザ名は、ライブチャットチャンネルでの名前として使用されます。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_operator__time_to_answer
msgid "Time to answer"
msgstr "回答時間"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_digest_digest__kpi_livechat_response
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__time_to_answer
msgid "Time to answer (sec)"
msgstr "回答時間(秒)"

#. module: im_livechat
#: model:digest.tip,name:im_livechat.digest_tip_im_livechat_0
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid "Tip: Use canned responses to chat faster"
msgstr "ヒント：定型文を使ってチャットを高速化する"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_script__title
msgid "Title"
msgstr "タイトル"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__title_color
msgid "Title Color"
msgstr "タイトル色"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_report_channel_view_search
msgid "Treated sessions"
msgstr "対応されたセッション"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel_rule__regex_url
msgid "URL Regex"
msgstr "URL"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_im_livechat_channel__web_page
msgid ""
"URL to a static page where you client can discuss with the operator of the "
"channel."
msgstr "顧客がチャネルのオペレータとディスカッションできる静的ページへのURL。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.discuss_channel_view_search
msgid "Unrated"
msgstr "未評価"

#. module: im_livechat
#: model_terms:digest.tip,tip_description:im_livechat.digest_tip_im_livechat_0
msgid ""
"Use canned responses to define templates of messages in the livechat app. To"
" load a canned response, start your sentence with ':' and select the "
"template."
msgstr ""
"定型応答を使用して、ライブチャットアプリでメッセージのテンプレートを定義します。定型応答を読み込むには、テキストを ' : ' "
"で開始し、テンプレートを選択します。"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users
#: model:res.groups,name:im_livechat.im_livechat_group_user
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "User"
msgstr "ユーザ"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_res_partner__user_livechat_username
#: model:ir.model.fields,field_description:im_livechat.field_res_users__user_livechat_username
msgid "User Livechat Username"
msgstr "ユーザライブチャットユーザ名"

#. module: im_livechat
#: model:ir.model,name:im_livechat.model_res_users_settings
msgid "User Settings"
msgstr "ユーザ設定"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_script_answer_id
msgid "User's answer"
msgstr "ユーザの回答"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_chatbot_message__user_raw_answer
msgid "User's raw answer"
msgstr "ユーザの生の回答"

#. module: im_livechat
#. odoo-javascript
#. odoo-python
#: code:addons/im_livechat/controllers/main.py:0
#: code:addons/im_livechat/models/im_livechat_channel.py:0
#: code:addons/im_livechat/static/src/embed/common/livechat_service.js:0
msgid "Visitor"
msgstr "訪問者"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_report_channel__is_happy
msgid "Visitor is Happy"
msgstr "高満足度訪問者"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/models/discuss_channel.py:0
msgid "Visitor left the conversation."
msgstr "訪問者は会話から退出しました。"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__web_page
msgid "Web Page"
msgstr "ウェブページ"

#. module: im_livechat
#: model:ir.actions.act_window,name:im_livechat.im_livechat_channel_action
msgid "Website Live Chat Channels"
msgstr "ウェブサイトWebチャットチャネル"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_discuss_channel__website_message_ids
msgid "Website Messages"
msgstr "ウェブサイトメッセージ"

#. module: im_livechat
#: model:ir.model.fields,help:im_livechat.field_discuss_channel__website_message_ids
msgid "Website communication history"
msgstr "ウェブサイト通信履歴"

#. module: im_livechat
#: model:chatbot.script,title:im_livechat.chatbot_script_welcome_bot
msgid "Welcome Bot"
msgstr "ウェルカムボット"

#. module: im_livechat
#: model:ir.model.fields,field_description:im_livechat.field_im_livechat_channel__default_message
msgid "Welcome Message"
msgstr "ウェルカムメッセージ"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_welcome
msgid "Welcome to CompanyName! 👋"
msgstr "CompanyNameへようこそ！👋"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_dispatch
msgid "What are you looking for?"
msgstr "何かお探しでしょうか。"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "Widget"
msgstr "ウィジェット"

#. module: im_livechat
#: model:chatbot.script.step,message:im_livechat.chatbot_script_welcome_step_pricing_email
msgid ""
"Would you mind leaving your email address so that we can reach you back?"
msgstr "折り返しご連絡させていただきますので、メールアドレスを教えていただけますでしょうか？"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/close_confirmation.xml:0
msgid "Yes, leave conversation"
msgstr "はい、会話からでます"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "You"
msgstr "あなた"

#. module: im_livechat
#. odoo-python
#: code:addons/im_livechat/controllers/attachment.py:0
msgid "You are not allowed to upload attachments on this channel."
msgstr "このチャネルに添付をアップロードする権限がありません。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.chatbot_script_action
msgid ""
"You can create a new Chatbot with a defined script to speak to your website "
"visitors."
msgstr "定義されたスクリプトで新しいチャットボットを作成し、ウェブサイトの訪問者に話しかけることができます。"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.im_livechat_channel_action
msgid ""
"You can create channels for each website on which you want\n"
"                to integrate the website live chat widget, allowing your website\n"
"                visitors to talk in real time with your operators."
msgstr ""
"ウェブサイトライブチャットウィジェットを統合したい各ウェブサイトに\n"
"チャネルを作成することができます。\n"
"これにより、ウェブサイト訪問者は、オペレータとリアルタイムで会話することができます。"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/livechat_channel_model_patch.js:0
msgid "You joined %s."
msgstr "%sに参加しました。"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/livechat_channel_model_patch.js:0
msgid "You left %s."
msgstr " %sから退出しました。"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/close_confirmation.xml:0
msgid "You're about to leave the conversation, proceed?"
msgstr "会話から出ようとしています。このまま出ますか？"

#. module: im_livechat
#: model_terms:ir.actions.act_window,help:im_livechat.discuss_channel_action
msgid "Your chatter history is empty"
msgstr "チャッター履歴は空です"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/core/web/channel_member_list_patch.xml:0
msgid "country"
msgstr "国"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_view_form
msgid "e.g. \"Meeting Scheduler Bot\""
msgstr "例: \"ミーティングスケジューラボット\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.chatbot_script_step_view_form
msgid "e.g. 'How can I help you?'"
msgstr "例: \"どのようなご用件でしょうか？\""

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "e.g. /contactus"
msgstr "例: /contactus"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. Hello, how may I help you?"
msgstr "例: こんにちは。どのようなご用件でしょうか？"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "e.g. YourWebsite.com"
msgstr "例: YourWebsite.com"

#. module: im_livechat
#. odoo-javascript
#: code:addons/im_livechat/static/src/embed/common/feedback_panel/transcript_sender.xml:0
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_view_form
msgid "or copy this url and send it by email to your customers or suppliers:"
msgstr "または、このURLをコピーして、顧客や仕入先にメールで送信して下さい:"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.im_livechat_channel_rule_view_form
msgid "seconds"
msgstr "秒"

#. module: im_livechat
#: model_terms:ir.ui.view,arch_db:im_livechat.livechat_email_template
msgid "{{author_name}}"
msgstr "{{author_name}}"
