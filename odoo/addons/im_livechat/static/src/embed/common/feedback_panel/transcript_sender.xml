<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="im_livechat.TranscriptSender">
    <div class="form-text">
        <t t-if="state.status === STATUS.SENT">The conversation was sent.</t>
        <t t-elif="state.status === STATUS.FAILED">An error occurred. Please try again.</t>
        <t t-else="">Receive a copy of this conversation.</t>
    </div>
    <div class="input-group has-validation mb-3">
        <input t-model="state.email" t-att-disabled="[STATUS.SENDING, STATUS.SENT].includes(state.status)" type="text" class="form-control" placeholder="<EMAIL>"/>
        <button class="btn btn-primary" type="button" data-action="sendTranscript" t-att-disabled="!state.email or !isValidEmail(state.email) or [STATUS.SENDING, STATUS.SENT].includes(state.status)" t-on-click="onClickSend">
            <i class="fa" t-att-class="{
                'fa-spinner fa-spin': state.status === STATUS.SENDING,
                'fa-check': state.status === STATUS.SENT,
                'fa-paper-plane': state.status === STATUS.IDLE,
                'fa-repeat': state.status === STATUS.FAILED,
            }"/>
        </button>
    </div>
</t>
</templates>
