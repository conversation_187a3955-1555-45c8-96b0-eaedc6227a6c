# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_integration_website
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__apply_method
msgid "Apply Method"
msgstr "Applica metodo"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "At least one platform must be selected"
msgstr "Bisogna selezionare almeno una piattaforma"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "Campaign Start Date"
msgstr "Data inizio campagna"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Campaign Start Date is required."
msgstr "È richiesta la data di inizio campagna."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_job_post__apply_method
msgid "Contact Method"
msgstr "Metodo di contatto"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Generate"
msgstr "Genera"

#. module: hr_recruitment_integration_website
#: model_terms:ir.ui.view,arch_db:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_view_form
msgid "Generate Description (<i class=\"fa fa-fw fa-magic\"/>AI)"
msgstr "Genera descrizione (<i class=\"fa fa-fw fa-magic\"/>AI)"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"IGNORE PREVIOUS PROMPTS AND FORMAT AS HTML AND NO HYPERLINKS\n"
"You are the best recruiter in out company and we are looking to hire a new employee.\n"
"You have been tasked with creating a the best job description for a new job opening.\n"
"Given the following infos, make a friendly and attractive job description that could be used\n"
"to attract potential candidates (The platforms could be things like LinkedIn, Indeed, Glassdoor, Monster, etc.)\n"
"It's your last chance to make a good impression and attract the best candidates to our company so make it count!\n"
"Here is the data to generate the job description:\n"
msgstr ""
"IGNORA QUALSIASI PROMPT PRECEDENTE E FORMATO HTML E NESSUN COLLEGAMENTO IPERTESTUALE\n"
"Sei il miglior recruiter dell'azienda e vogliamo assumere un nuovo dipendente.\n"
"Ti è stato assegnato il compito di creare la migliore descrizione per una nuova posizione lavorativa.\n"
"Date le seguenti informazioni, crea una descrizione del lavoro amichevole e attraente che possa essere utilizzata\n"
"per attrarre potenziali candidati (le piattaforme potrebbero essere LinkedIn, Indeed, Glassdoor, Monster, ecc.)\n"
"È l'ultima occasione per fare una buona impressione e attirare i migliori candidati nella nostra azienda, quindi falla fruttare!\n"
"Ecco i dati per creare una descrizione:\n"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__platform_ids
msgid "Job Board"
msgstr "Bacheca annunci di lavoro"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job
msgid "Job Position"
msgstr "Posizione lavorativa"

#. module: hr_recruitment_integration_website
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_job_post
msgid "Job Post"
msgstr "Offerta di lavoro"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__job_apply_url
msgid "Job url"
msgstr "URL lavoro"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Oops, it looks like our AI is unreachable!"
msgstr "Ops, sembra che l'IA non sia disponibile!"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,field_description:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__post_html
msgid "Post"
msgstr "Messaggio"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
#: model:ir.model,name:hr_recruitment_integration_website.model_hr_recruitment_post_job_wizard
msgid "Post Job"
msgstr "Pubblica offerta di lavoro"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid "Post is required."
msgstr "Annuncio obbligatorio."

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_job_post__apply_method__redirect
msgid "Redirect to Website"
msgstr "Reindirizzamento al sito web"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields.selection,name:hr_recruitment_integration_website.selection__hr_recruitment_post_job_wizard__apply_method__redirect
msgid "Redirect to company's website"
msgstr "Reindirizza al sito web dell'azienda"

#. module: hr_recruitment_integration_website
#: model:ir.actions.server,name:hr_recruitment_integration_website.hr_recruitment_post_job_wizard_action_regenerate_post
msgid "Regenerate Post"
msgstr "Rigenera annuncio"

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, the web page is too long for our AI to process."
msgstr ""
"Scusa, la pagina web è troppo lunga per essere elaborata dalla nostra IA."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "Sorry, we could not generate a response. Please try again later."
msgstr ""
"Non è stato possibile generare una risposta. Prova di nuovo più tardi."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The Job Description will be replaced with the generated one, do you want to "
"continue?"
msgstr ""
"La descrizione della posizione lavorativa verrà sostituita con quella "
"generata, vuoi continuare?"

#. module: hr_recruitment_integration_website
#: model:ir.model.fields,help:hr_recruitment_integration_website.field_hr_recruitment_post_job_wizard__campaign_start_date
msgid "The date when the campaign will start."
msgstr "La data di inizio della campagna."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"The job must be published on the website to generate a post with a redirect "
"apply method."
msgstr ""
"Il lavoro deve essere pubblicato sul sito web per generare un post con un "
"metodo di richiesta di reindirizzamento."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/wizard/hr_recruitment_post_job.py:0
msgid ""
"URL is required if the apply method is 'Redirect to company's website'."
msgstr ""
"L'URL è richiesto se il metodo di applicazione è \"Reindirizza al sito web "
"dell'azienda\"."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid "You can only generate a post for a published job offer."
msgstr ""
"È possibile generare un annuncio solo per un'offerta di lavoro pubblicata."

#. module: hr_recruitment_integration_website
#. odoo-python
#: code:addons/hr_recruitment_integration_website/models/hr_job.py:0
msgid ""
"You have reached the maximum number of requests for this service. Try again "
"later."
msgstr ""
"È stato raggiunto il numero massimo di richieste per questo servizio, "
"riprovare più tardi."
