<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="600" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M0.11,0A0.11,0.11,-90,0,0,0,0.11L0,0.89A0.11,0.11,-90,0,0,0.11,1L0.89,1A0.11,0.11,270,0,0,1,0.89L1,0.11A0.11,0.11,-90,0,0,0.89,0Z">
        </path>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
