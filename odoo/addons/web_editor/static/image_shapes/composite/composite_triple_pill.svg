<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="600" height="600">
    <defs>
        <clipPath id="clip-path" clipPathUnits="objectBoundingBox">
            <use xlink:href="#filterPath" fill="none"/>
        </clipPath>
        <path id="filterPath" d="M0,0.1703C0,0.0762,0.0746,0,0.1667,0H0.8333C0.9254,0,1,0.0762,1,0.1703C1,0.2495,0.9471,0.3161,0.8754,0.3352C0.9471,0.3542,1,0.4208,1,0.5C1,0.5792,0.9471,0.6458,0.8754,0.6648C0.9471,0.6839,1,0.7505,1,0.8297C1,0.9238,0.9254,1,0.8333,1H0.1667C0.0746,1,0,0.9238,0,0.8297C0,0.7505,0.0529,0.6839,0.1247,0.6648C0.0529,0.6458,0,0.5792,0,0.5C0,0.4208,0.0529,0.3542,0.1246,0.3352C0.0529,0.3161,0,0.2495,0,0.1703Z">
        </path>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#clip-path)"/>
</svg>
