<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1370 835" width="1370" height="835" data-forced-size="true">
    <style>
        image { 
            width: calc(100% - 245px);
            height: calc(100% - 129px);
        }
    </style>
    <defs>
       <linearGradient id="gradient_01" x1="69" y1="5.09" x2="142.5" y2="5.09" gradientTransform="matrix(1, 0, 0, -1, 0, 837.09)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#494949"/>
            <stop offset="0.2" stop-color="#202020"/>
            <stop offset="0.79" stop-color="#202020"/>
            <stop offset="1" stop-color="#494949"/>
        </linearGradient>
        <linearGradient id="gradient_02" x1="106" y1="2.09" x2="106" y2="5.09" gradientTransform="matrix(1, 0, 0, -1, 0, 837.09)" gradientUnits="userSpaceOnUse">
            <stop offset="0.46" stop-color="#555"/>
            <stop offset="1" stop-color="#494949" stop-opacity="0"/>
        </linearGradient>
        <linearGradient id="gradient_03" x1="65" y1="6.59" x2="146" y2="6.59" gradientTransform="matrix(1, 0, 0, -1, 0, 837.09)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#8e8f93"/>
            <stop offset="0.05" stop-color="#a7a7a9"/>
            <stop offset="0.15" stop-color="#535459"/>
            <stop offset="0.38" stop-color="#a7a7a9"/>
            <stop offset="0.57" stop-color="#a7a7a9"/>
            <stop offset="0.84" stop-color="#535459"/>
            <stop offset="0.94" stop-color="#a7a7a9"/>
            <stop offset="1" stop-color="#8e8f93"/>
        </linearGradient>
        <linearGradient id="gradient_01-2" x1="1227" y1="5.09" x2="1300.5" y2="5.09" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_02-2" x1="1264" y1="2.09" x2="1264" y2="5.09" xlink:href="#gradient_02"/>
        <linearGradient id="gradient_03-2" x1="1223" y1="6.59" x2="1304" y2="6.59" xlink:href="#gradient_03"/>
        <linearGradient id="gradient_04" y1="32.09" x2="1370" y2="32.09" gradientTransform="matrix(1, 0, 0, -1, 0, 837.09)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#a0a1a5"/>
            <stop offset="0.02" stop-color="#d3d4d9"/>
            <stop offset="0.06" stop-color="#6a6b70"/>
            <stop offset="0.09" stop-color="#797a7f"/>
            <stop offset="0.21" stop-color="#a8a9ae"/>
            <stop offset="0.79" stop-color="#a8a9ae"/>
            <stop offset="0.92" stop-color="#797a7f"/>
            <stop offset="0.95" stop-color="#6a6b70"/>
            <stop offset="0.98" stop-color="#d3d4d9"/>
            <stop offset="1" stop-color="#a0a1a5"/>
        </linearGradient>
        <linearGradient id="gradient_05" x1="685" y1="56.09" x2="685" y2="8.09" gradientTransform="matrix(1, 0, 0, -1, 0, 837.09)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#858688" stop-opacity="0"/>
            <stop offset="0.65" stop-color="#737479"/>
            <stop offset="0.93" stop-color="#b7b8bd"/>
            <stop offset="1" stop-color="#858688"/>
        </linearGradient>
        <linearGradient id="gradient_06" x1="580" y1="48.59" x2="790" y2="48.59" gradientTransform="matrix(1, 0, 0, -1, 0, 837.09)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#575759"/>
            <stop offset="0.1" stop-color="#cbccd1"/>
            <stop offset="0.5" stop-color="#d0d1d6"/>
            <stop offset="0.9" stop-color="#cbccd1"/>
            <stop offset="1" stop-color="#575759"/>
        </linearGradient>
        <linearGradient id="gradient_07" x1="685" y1="83.09" x2="685" y2="56.09" gradientTransform="matrix(1, 0, 0, -1, 0, 837.09)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#232323"/>
            <stop offset="1" stop-color="#0a0a0c"/>
        </linearGradient>
        <radialGradient id="gradient_08" cx="265.29" cy="676.34" r="1" gradientTransform="matrix(0, 5, 5, 0, -2696.72, -1304.46)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#9797ab"/>
            <stop offset="0.39" stop-color="#2c2c46"/>
            <stop offset="1" stop-color="#3f3f46"/>
        </radialGradient>
        <path id="filterPath" d="M0.9095,0.8886H0.0899V0.0443H0.9095Z"/>
    </defs>
    <rect x="123.16" y="37" width="1122.84" height="705"/>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" preserveAspectRatio="xMidYMin slice" x="122" y="36">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M66,829h80l-5.41,5.41a2,2,0,0,1-1.42.59H72.83a2,2,0,0,1-1.42-.59Z" fill="url(#gradient_01)"/>
        <path d="M66,829h80l-5.41,5.41a2,2,0,0,1-1.42.59H72.83a2,2,0,0,1-1.42-.59Z" fill-opacity="0.7" fill="url(#gradient_02)"/>
        <path d="M65,829h82l-1.83,1.83a4,4,0,0,1-2.83,1.17H69.66a4,4,0,0,1-2.83-1.17Z" fill="url(#gradient_03)"/>
        <path d="M1224,829h80l-5.41,5.41a2,2,0,0,1-1.42.59h-66.34a2,2,0,0,1-1.42-.59Z" fill="url(#gradient_01-2)"/>
        <path d="M1224,829h80l-5.41,5.41a2,2,0,0,1-1.42.59h-66.34a2,2,0,0,1-1.42-.59Z" fill-opacity="0.7" fill="url(#gradient_02-2)"/>
        <path d="M1223,829h82l-1.83,1.83a4,4,0,0,1-2.83,1.17h-72.68a4,4,0,0,1-2.83-1.17Z" fill="url(#gradient_03-2)"/>
        <g>
            <path d="M0,783a2,2,0,0,1,2-2H1368a2,2,0,0,1,2,2v21a25,25,0,0,1-25,25H25A25,25,0,0,1,0,804Z" fill="url(#gradient_04)"/>
            <path d="M0,783a2,2,0,0,1,2-2H1368a2,2,0,0,1,2,2v21a25,25,0,0,1-25,25H25A25,25,0,0,1,0,804Z" fill-opacity="0.7" fill="url(#gradient_05)"/>
        </g>
        <path d="M580,781H790a15,15,0,0,1-15,15H595A15,15,0,0,1,580,781Z" fill="url(#gradient_06)"/>
        <path d="M1234,0H136a29,29,0,0,0-29,29V781H1263V29A29,29,0,0,0,1234,0Zm12,742H123.16V37H1246Z" fill="#6d6e70"/>
        <path d="M1234,2H136a27,27,0,0,0-27,27V781H1261V29A27,27,0,0,0,1234,2Zm12,740H123.16V37H1246Z" fill="#1f1f1f"/>
        <path d="M1234,5H136a24,24,0,0,0-24,24V754H1258V29A24,24,0,0,0,1234,5Zm12,737H123.16V37H1246Z"/>
        <rect x="112" y="754" width="1146" height="27" fill="url(#gradient_07)"/>
        <path d="M1232,17H138a14,14,0,0,0-14,14v6H1246V31A14,14,0,0,0,1232,17Z" fill="#fff"/>
        <path d="M124,31a14,14,0,0,1,14-14H1232a14,14,0,0,1,14,14V741a1,1,0,0,1-1,1H125a1,1,0,0,1-1-1Z" fill="none"/>
        <path d="M614,17h9a3,3,0,0,1,3,3V31a6,6,0,0,0,6,6H739a5,5,0,0,0,5-5V20a3,3,0,0,1,3-3H614Z"/>
        <rect x="123.16" y="16.63" width="1124.89" height="20.37"/>
        <circle cx="685" cy="22" r="5" fill="url(#gradient_08)"/>
        <path d="M1368,781H1263V29a29,29,0,0,0-29-29H136a29,29,0,0,0-29,29V781H2a2,2,0,0,0-2,2v21a25,25,0,0,0,25,25H65l1.83,1.83a4,4,0,0,0,2.1,1.1l2.48,2.48a2,2,0,0,0,1.42.59h66.34a2,2,0,0,0,1.42-.59l2.48-2.48a4,4,0,0,0,2.1-1.1L147,829H1223l1.83,1.83a4,4,0,0,0,2.1,1.1l2.48,2.48a2,2,0,0,0,1.42.59h66.34a2,2,0,0,0,1.42-.59l2.48-2.48a4,4,0,0,0,2.1-1.1L1305,829h40a25,25,0,0,0,25-25V783A2,2,0,0,0,1368,781Zm-107,0H109V29A27,27,0,0,1,136,2H1234a27,27,0,0,1,27,27Z" fill="#000" style="mix-blend-mode: saturation"/>
        <path d="M1368,781H1263V29a29,29,0,0,0-29-29H136a29,29,0,0,0-29,29V781H2a2,2,0,0,0-2,2v21a25,25,0,0,0,25,25H65l1.83,1.83a4,4,0,0,0,2.1,1.1l2.48,2.48a2,2,0,0,0,1.42.59h66.34a2,2,0,0,0,1.42-.59l2.48-2.48a4,4,0,0,0,2.1-1.1L147,829H1223l1.83,1.83a4,4,0,0,0,2.1,1.1l2.48,2.48a2,2,0,0,0,1.42.59h66.34a2,2,0,0,0,1.42-.59l2.48-2.48a4,4,0,0,0,2.1-1.1L1305,829h40a25,25,0,0,0,25-25V783A2,2,0,0,0,1368,781Zm-107,0H109V29A27,27,0,0,1,136,2H1234a27,27,0,0,1,27,27Z" fill="#000" opacity="0.25"/>
        <path d="M1368,781H1263V29a29,29,0,0,0-29-29H136a29,29,0,0,0-29,29V781H2a2,2,0,0,0-2,2v21a25,25,0,0,0,25,25H65l1.83,1.83a4,4,0,0,0,2.1,1.1l2.48,2.48a2,2,0,0,0,1.42.59h66.34a2,2,0,0,0,1.42-.59l2.48-2.48a4,4,0,0,0,2.1-1.1L147,829H1223l1.83,1.83a4,4,0,0,0,2.1,1.1l2.48,2.48a2,2,0,0,0,1.42.59h66.34a2,2,0,0,0,1.42-.59l2.48-2.48a4,4,0,0,0,2.1-1.1L1305,829h40a25,25,0,0,0,25-25V783A2,2,0,0,0,1368,781Zm-107,0H109V29A27,27,0,0,1,136,2H1234a27,27,0,0,1,27,27Z" fill="#F6F6F6" style="mix-blend-mode: overlay" opacity="0.75"/>
    </g>
</svg>
