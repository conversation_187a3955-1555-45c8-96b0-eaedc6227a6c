<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 2000 1400" data-forced-size="true" width="2000" height="1400" data-img-aspect-ratio="19.5:9" data-img-perspective="[[34.68, -0.44], [100.33, 67.02], [65.22, 94.77], [-0.43, 26.44]]">
    <defs>
       <linearGradient id="gradient_01" y1="700" x2="2000" y2="700" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4d4d4d"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.04" stop-color="#3a3330"/>
            <stop offset="0.07" stop-color="#575757"/>
            <stop offset="0.56" stop-color="#646464"/>
            <stop offset="0.59" stop-color="#414141"/>
            <stop offset="0.62" stop-color="#2c2c2c"/>
            <stop offset="0.66"/>
            <stop offset="0.93" stop-color="#2e2e2e"/>
            <stop offset="0.95" stop-color="#262626"/>
            <stop offset="0.97" stop-color="#1a1a1a"/>
            <stop offset="1" stop-color="#2e2e2e"/>
        </linearGradient>
       <linearGradient id="light_adjust" y1="700" x2="2000" y2="700" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="0.5" stop-color="#fff" stop-opacity=".5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <radialGradient id="gradient_02" cx="309.91" cy="223.3" r="7.82" gradientTransform="translate(407.69 -145.95) rotate(69.06)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#658088"/>
            <stop offset="0.07" stop-color="#4f6571"/>
            <stop offset="0.16" stop-color="#374756"/>
            <stop offset="0.27" stop-color="#232d40"/>
            <stop offset="0.39" stop-color="#131a2f"/>
            <stop offset="0.52" stop-color="#080c23"/>
            <stop offset="0.69" stop-color="#02041c"/>
            <stop offset="1" stop-color="#00021a"/>
        </radialGradient>
        <linearGradient id="gradient_03" x1="1686.37" y1="1157.25" x2="1686.37" y2="1283.61" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#333"/>
            <stop offset="0.49"/>
            <stop offset="0.49" stop-color="#c4c4c4"/>
            <stop offset="0.67" stop-color="#c4c4c4"/>
            <stop offset="0.92" stop-color="#333"/>
            <stop offset="0.98" stop-color="#c4c4c4"/>
            <stop offset="1" stop-color="#333"/>
        </linearGradient>
        <linearGradient id="gradient_04" x1="7101.39" y1="16559.66" x2="7155.71" y2="16559.66" gradientTransform="matrix(0.81, -0.59, -0.59, -0.81, 4345.14, 18367.61)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#141414"/>
            <stop offset="0.35" stop-color="#343434"/>
            <stop offset="0.5" stop-color="#424242"/>
            <stop offset="0.65" stop-color="#343434"/>
            <stop offset="1" stop-color="#141414"/>
        </linearGradient>
        <linearGradient id="gradient_04-2" x1="7051.58" y1="16709.72" x2="7105.9" y2="16709.72" xlink:href="#gradient_04"/>
        <linearGradient id="gradient_04-3" x1="7016.99" y1="16857.3" x2="7047.85" y2="16857.3" xlink:href="#gradient_04"/>
        <linearGradient id="gradient_01-2" x1="165.58" y1="419.25" x2="58.3" y2="560.76" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_01-3" x1="1225.31" y1="1196.01" x2="1121.36" y2="1333.13" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_01-4" x1="5890.84" y1="1201.21" x2="5776.39" y2="1352.17" gradientTransform="matrix(-1, 0, 0, 1, 7278.49, 0)" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_05" x1="1609.44" y1="622.45" x2="565.64" y2="906.53" gradientUnits="userSpaceOnUse">
            <stop offset="0.4" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <path d="M491.38,91.79,623.79,23.3,704.7,8.43l77.64,29.42L1969.9,901.75v128.32l-620.34,330.19L1218,1329.66,20.61,440l4.08-89.91Z"/>
        </clipPath>
        <path id="filterPath" d="M0.2457,0.0656,0.3119,0.0166,0.3523,0.006l0.0388,0.021L0.9849,0.6441v0.0917l-0.3102,0.2359L0.609,0.9498,0.0103,0.3143l0.002-0.0642Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1965,873.5c-7.1-5-1125.56-812.22-1177.5-847.68S638.3-5.36,591.42,19.22C551.94,39.91,133.73,264.48,80.53,292.06,25,320.81,0,356.66,0,376.18v80.43c0,41.76,56.7,79.06,80.53,97.91,46.59,37,1048,763.75,1122.74,815.72s184.79,25.06,230.4,0,494.55-274.11,512.94-284.7,53.39-44.19,53.39-69.16v-81.1C2000,899.92,1972.09,878.46,1965,873.5Zm-56.61,119.28c-87.63,48-432.4,239.24-490.37,271.29s-129,53.62-218.73-13S139.37,478,104.06,452.62-2.24,372.88,86.75,321c0,0,123.56-68.27,138.44-76s22.57-9.13,35-1.07,46.78,22.25,87-1.55,110.4-60.08,144.15-78c28.3-15,13.81-38.07,8-43S478,109.62,499,98.54,607.27,40.88,607.27,40.88c36.87-19.62,110.2-28.66,157.56,4.47s1110,798.14,1143.55,823.2S1996,944.8,1908.38,992.78Z" fill="url(#gradient_01)"/>
        <path d="M1965,873.5c-7.1-5-1125.56-812.22-1177.5-847.68S638.3-5.36,591.42,19.22C551.94,39.91,133.73,264.48,80.53,292.06,25,320.81,0,356.66,0,376.18v80.43c0,41.76,56.7,79.06,80.53,97.91,46.59,37,1048,763.75,1122.74,815.72s184.79,25.06,230.4,0,494.55-274.11,512.94-284.7,53.39-44.19,53.39-69.16v-81.1C2000,899.92,1972.09,878.46,1965,873.5ZM297.13,218.4c2-5.11,9.29-7.05,16.36-4.33s11.21,9.05,9.24,14.16-9.29,7.05-16.35,4.33S295.17,223.51,297.13,218.4ZM1614.84,1234.47c-.75,4-5,9-9.51,11.12-4.88,2.25-8.13.75-7.38-3.62s5.5-9.5,10.13-11.5C1612.59,1228.72,1615.59,1230.6,1614.84,1234.47Zm118.4-65.61c-.36,6.52-4,13.73-8.17,16.11l-78.69,42.59c-4.15,2.38-7.23-1-6.88-7.48h0c.36-6.51,4-13.72,8.17-16.1l78.69-42.59C1730.52,1159,1733.6,1162.35,1733.24,1168.86Zm34.85-20.9c-1,4-5.13,8.75-9.26,10.75-4.38,2.12-7,.37-6-4s5.63-9.38,9.89-11.13S1769.22,1144,1768.09,1148Zm154.29-155.63c-87.63,48-444,251.59-502,283.65s-129,53.61-218.73-13S127.64,479.35,92.33,454-14,374.25,75,322.38c0,0,139.94-77.57,154.82-85.34s22.56-9.13,35-1.07,39.15,21.62,79.41-2.18,110.41-60.08,144.16-78c28.3-15,8.8-29.71,3-34.67S470,109.42,491,98.35,607.44,35.53,607.44,35.53C644.3,15.91,717.63,6.87,765,40S1888.83,843,1922.38,868.1,2010,944.35,1922.38,992.33Z" fill="url(#light_adjust)" opacity="0.2"/>
        <path d="M1965,873.5c-7.1-5-1125.56-812.22-1177.5-847.68S638.3-5.36,591.42,19.22C551.94,39.91,133.83,264.48,80.53,292.06,25,320.81,0,356.66,0,376.18V387c0,21.18,3.5,41.67,61.08,83.63s1097.35,800.09,1131.78,825,126.14,66.15,259.68-8,452.05-250.31,496.5-274.69,50.86-62.07,50.86-69.84v-7.77C1999.9,900,1972,878.46,1965,873.5ZM1934.55,1003c-95.71,52.94-471.31,259-505.55,278.38s-137,68.19-245.48-11.56S96.67,476.52,65.26,453.6-40.36,366.08,83,300.13,467.52,93.81,597,26c26.84-14.09,118-35,176.32,2.81S1933.87,860.1,1950.11,872.14,2030.34,950,1934.55,1003Z" fill="#fff" opacity="0.5"/>
        <path d="M1950.11,872.14C1933.87,860.1,831.55,66.62,773.29,28.83S623.91,11.93,597,26C467.52,93.91,206.19,234.17,83,300.13S33.85,430.67,65.26,453.6s1009.82,736.46,1118.26,816.2,211.15,31,245.48,11.56,409.85-225.44,505.55-278.38S1966.35,884.09,1950.11,872.14Zm-41.73,120.64c-87.63,48-432.4,239.24-490.37,271.29s-129,53.62-218.73-13S139.37,478,104.06,452.62-2.24,372.88,86.75,321c0,0,123.56-68.27,138.44-76s22.57-9.13,35-1.07,46.78,22.25,87-1.55,110.4-60.08,144.15-78c28.3-15,13.81-38.07,8-43S478,109.62,499,98.54,607.27,40.88,607.27,40.88c36.87-19.62,110.2-28.66,157.56,4.47s1110,798.14,1143.55,823.2S1996,944.8,1908.38,992.78Z"/>
        <g id="details">
            <g>
                <ellipse cx="309.93" cy="223.32" rx="9.91" ry="13.71" transform="translate(-9.41 432.95) rotate(-69.06)" fill="#131516"/>
                <ellipse cx="309.91" cy="223.3" rx="6.51" ry="8.95" transform="translate(-9.41 432.92) rotate(-69.06)" fill="url(#gradient_02)"/>
            </g>
            <g>
                <path d="M1510.49,1282.43c-4.77,1.94-9.24,7.09-9.73,11.17s3.21,5.34,8.07,3.11c4.57-2.23,8.46-7,8.85-10.78S1515,1280.49,1510.49,1282.43Z" fill="#000102"/>
                <path d="M1508.64,1296.61c-4.77,2.43-8.46,1.07-8.07-3.1l11.08,1.16A11.92,11.92,0,0,1,1508.64,1296.61Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1515,1291.37a17.27,17.27,0,0,1-6.32,5.15l-6.13-4.18,2.34-2Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1508.44,1284.57c-3.89,1.75-7.58,5.83-8.07,9.23-.29,3.3,2.63,4.37,6.71,2.52,3.8-1.84,7.1-5.83,7.49-8.84C1514.86,1284.28,1512.24,1282.92,1508.44,1284.57Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1534.88,1270.59c-4.67,1.94-9.14,7-9.72,11.17s3,5.44,7.78,3.2c4.47-2.13,8.36-6.89,8.85-10.68S1539.36,1268.74,1534.88,1270.59Z" fill="#000102"/>
                <path d="M1539.26,1279.62a17.73,17.73,0,0,1-6.23,5.15l-5.93-4.27,2.34-2Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1533,1272.73c-3.79,1.65-7.58,5.73-8.07,9.13s2.53,4.46,6.42,2.52c3.7-1.84,7-5.73,7.39-8.84S1536.73,1271.07,1533,1272.73Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1557.76,1258.81c-4.57,1.84-9,7-9.63,11.07s2.82,5.44,7.59,3.3c4.47-2.13,8.27-6.89,8.85-10.68S1562,1257,1557.76,1258.81Z" fill="#000102"/>
                <path d="M1555.52,1273c-4.66,2.33-8.07,1-7.58-3.3l10.5,1.36A7.42,7.42,0,0,1,1555.52,1273Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1561.75,1267.94a19.26,19.26,0,0,1-6.13,5l-5.74-4.27,2.34-2Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1555.72,1261c-3.79,1.65-7.49,5.73-8,9.13s2.34,4.47,6.23,2.63c3.69-1.75,7-5.74,7.39-8.75C1561.94,1260.75,1559.41,1259.39,1555.72,1261Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1610.58,1227.72c-5.63,2.25-11.26,8.75-12.26,14s3.13,7.12,8.89,4.5c5.5-2.63,10.38-8.62,11.26-13.5S1615.84,1225.6,1610.58,1227.72Z" fill="#000102"/>
                <path d="M1607.08,1246.34c-5.76,2.88-9.76,1-8.88-4.5l12.51,2.25C1609.58,1244.72,1608.46,1245.59,1607.08,1246.34Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1614.84,1240a22.32,22.32,0,0,1-7.76,6.37l-6.63-5.75,2.88-2.49Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1608.08,1230.47c-4.63,2-9.26,7.13-10.13,11.5s2.5,5.87,7.38,3.62c4.5-2.12,8.76-7.12,9.51-11.12S1612.59,1228.72,1608.08,1230.47Z" fill="#3c3d3d"/>
            </g>
            <g>
                <path d="M1765.09,1141c-5.13,2-10.64,8.25-12,13.62s2,7.5,7.26,5c5-2.37,9.89-8.25,11-13.12C1772.72,1141.46,1770,1139,1765.09,1141Z" fill="#000102"/>
                <path d="M1760.33,1159.58c-5.25,2.63-8.63.5-7.26-5l10.64,2.88C1762.71,1158.08,1761.46,1159,1760.33,1159.58Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1767.71,1153.46a21.71,21.71,0,0,1-7.25,6l-5.13-6.25,2.75-2.38Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1762.71,1143.58c-4.26,1.75-8.76,6.75-9.89,11.13s1.63,6.12,6,4c4.13-2,8.26-6.75,9.26-10.75S1766.84,1142,1762.71,1143.58Z" fill="#3c3d3d"/>
            </g>
            <g>
                <path d="M1815.94,1116c-3.79,1.46-8.07,6.22-9.24,10.4s1.27,5.82,5.16,4.07a17.82,17.82,0,0,0,8.55-10.1C1821.48,1116.56,1819.54,1114.52,1815.94,1116Z" fill="#000102"/>
                <path d="M1811.86,1130.45c-3.89,2-6.33.2-5.16-4.07l7.69,2.42A15.55,15.55,0,0,1,1811.86,1130.45Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1817.5,1125.79a16.22,16.22,0,0,1-5.55,4.57l-3.6-5,2.14-1.84Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1814.09,1118a15.28,15.28,0,0,0-7.58,8.55c-.88,3.4,1,4.76,4.28,3.3a14.79,14.79,0,0,0,7.1-8.25C1818.76,1118.51,1817.11,1116.86,1814.09,1118Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1836.46,1103.84c-3.79,1.46-8,6.22-9.24,10.39s1.07,5.93,5,4.08a17.82,17.82,0,0,0,8.46-10C1841.91,1104.52,1840.16,1102.48,1836.46,1103.84Z" fill="#000102"/>
                <path d="M1832.38,1118.22c-3.89,1.94-6.23.19-5-4.08l7.49,2.52A26.92,26.92,0,0,1,1832.38,1118.22Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1837.82,1113.65a17.45,17.45,0,0,1-5.54,4.57l-3.4-5,2.14-1.84Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1834.71,1105.78a14.7,14.7,0,0,0-7.58,8.55c-1,3.4,1,4.86,4.18,3.3a15.15,15.15,0,0,0,7.1-8.25C1839.28,1106.27,1837.73,1104.62,1834.71,1105.78Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1856.11,1092.86c-3.7,1.46-8,6.12-9.14,10.3s1,5.93,4.86,4.18a17.58,17.58,0,0,0,8.46-10C1861.55,1093.54,1859.8,1091.5,1856.11,1092.86Z" fill="#000102"/>
                <path d="M1851.93,1107.34c-3.8,1.84-6,.09-4.87-4.18l7.3,2.53A12.29,12.29,0,0,1,1851.93,1107.34Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1857.37,1102.87a18.21,18.21,0,0,1-5.44,4.56l-3.31-5,2-1.84Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1854.36,1094.9a15.53,15.53,0,0,0-7.59,8.45c-1,3.4.88,4.86,4.09,3.4a15.39,15.39,0,0,0,7.1-8.15C1858.83,1095.49,1857.37,1093.84,1854.36,1094.9Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1875.37,1081.79c-3.7,1.36-7.88,6.12-9.15,10.3s.88,5.92,4.67,4.17c3.6-1.65,7.3-6.12,8.46-9.9S1878.87,1080.43,1875.37,1081.79Z" fill="#000102"/>
                <path d="M1871,1096.26c-3.79,1.85-5.93.1-4.67-4.17l7.2,2.62A17.77,17.77,0,0,1,1871,1096.26Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1876.43,1091.8a15.53,15.53,0,0,1-5.44,4.46l-3.21-5,2-1.75Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1873.52,1083.83a15.46,15.46,0,0,0-7.59,8.45c-1,3.4.78,4.86,3.89,3.4a15.32,15.32,0,0,0,7.1-8.16C1878,1084.41,1876.43,1082.76,1873.52,1083.83Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1893.94,1071.3c-3.6,1.36-7.78,6-9.14,10.2s.78,5.92,4.57,4.27c3.6-1.65,7.29-6.12,8.46-9.9S1897.54,1069.94,1893.94,1071.3Z" fill="#000102"/>
                <path d="M1889.56,1085.77c-3.69,1.85-5.83,0-4.57-4.27l7,2.72A24.14,24.14,0,0,1,1889.56,1085.77Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1894.91,1081.31a16.1,16.1,0,0,1-5.35,4.46l-3-5.14,2-1.75Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1892.19,1073.34a15.13,15.13,0,0,0-7.49,8.35c-1,3.4.68,4.86,3.79,3.4a15.22,15.22,0,0,0,7-8.16C1896.57,1073.92,1895.11,1072.27,1892.19,1073.34Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M1912.42,1061c-3.6,1.36-7.78,6-9,10.2s.68,5.93,4.37,4.28a18.13,18.13,0,0,0,8.37-9.81C1917.48,1061.78,1915.82,1059.64,1912.42,1061Z" fill="#000102"/>
                <path d="M1907.85,1075.28c-3.7,1.85-5.74,0-4.38-4.27l6.81,2.72A9.87,9.87,0,0,1,1907.85,1075.28Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M1913.2,1070.82a16.2,16.2,0,0,1-5.35,4.46l-2.92-5.14,2-1.75Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M1910.67,1063a15.85,15.85,0,0,0-7.49,8.35c-1.07,3.4.58,4.86,3.7,3.5a15.07,15.07,0,0,0,7-8.06C1914.85,1063.43,1913.49,1061.78,1910.67,1063Z" fill="#0a0e0e"/>
            </g>
            <path d="M1728.36,1186l-84,45.45c-4.43,2.55-8.4-2.7-8-9.65h0c.38-7,2.5-17.14,6.94-19.68l84-45.45c4.44-2.55,9.5,3.51,9.12,10.46h0C1736,1174.09,1732.8,1183.47,1728.36,1186Z" fill="url(#gradient_03)"/>
            <path d="M1725.07,1185l-78.69,42.59c-4.15,2.38-7.23-1-6.88-7.48h0c.36-6.51,4-13.72,8.17-16.1l78.69-42.59c4.16-2.38,7.24,1,6.88,7.47h0C1732.88,1175.38,1729.23,1182.59,1725.07,1185Z" fill="#131313"/>
            <g>
                <path d="M467.22,813.76l-87.92-60.9h0a18.73,18.73,0,0,1-4.28-5.24,32.18,32.18,0,0,1-3-7.29,28.3,28.3,0,0,1-1.17-7.09c0-2-.29-1.84.49-2.62,1.46-1.17,3.21-3,4.28-3.5.68-.39,1.36-.58,1.85-.87a18.29,18.29,0,0,1,4-1.46,5.48,5.48,0,0,1,3.31.1,15.64,15.64,0,0,1,3.79,2.14l80.14,59.44A20.68,20.68,0,0,1,476.85,806h0a6.24,6.24,0,0,1-1,2.82,5.16,5.16,0,0,1-1.85,2c-1.46,1.17-2.62,2-6.81,2.91" fill="url(#gradient_04)"/>
                <path d="M378.53,752.77c-6.13-4.67-10.31-10.79-9.63-17.1a7.28,7.28,0,0,1,5.25-6.12,9.94,9.94,0,0,1,9.24,1.65l78.78,58.38c6,4.76,9.53,11.65,8.94,18.16a6.32,6.32,0,0,1-4.86,5.64,10.21,10.21,0,0,1-8.75-2.14Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.69"/>
            </g>
            <g>
                <path d="M338.94,721.29,251,660.39h0a18.39,18.39,0,0,1-4.28-5.24,32.18,32.18,0,0,1-3-7.29,28.3,28.3,0,0,1-1.17-7.09c0-2-.29-1.84.49-2.62,1.45-1.17,3.21-3,4.27-3.5.69-.39,1.37-.58,1.85-.87a18.35,18.35,0,0,1,4-1.46,6.38,6.38,0,0,1,3.31.1,14.35,14.35,0,0,1,3.79,2.13L340.4,694a20.7,20.7,0,0,1,8.17,19.52h0a6.49,6.49,0,0,1-1,2.82,5.24,5.24,0,0,1-1.85,2c-1.46,1.26-2.63,2-6.81,2.91" fill="url(#gradient_04-2)"/>
                <path d="M250.34,660.29c-6.13-4.66-10.31-10.78-9.63-17.09a7.28,7.28,0,0,1,5.25-6.12,9.93,9.93,0,0,1,9.24,1.65L334,697.11c6,4.76,9.53,11.65,8.95,18.16a6.33,6.33,0,0,1-4.86,5.64,10.22,10.22,0,0,1-8.76-2.14Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.69"/>
            </g>
            <g>
                <path d="M196.36,609.69l-50-34.58h0a12.74,12.74,0,0,1-2.43-2.91,20.14,20.14,0,0,1-1.75-4.18,16.26,16.26,0,0,1-.68-4c0-1.17-.2-1.07.29-1.56a19.58,19.58,0,0,1,2.43-2,9.06,9.06,0,0,1,1.07-.48,10.58,10.58,0,0,1,2.24-.78,2.9,2.9,0,0,1,1.85.1,8.7,8.7,0,0,1,2.14,1.26L197,594.34a11.73,11.73,0,0,1,4.57,11.07h0a3.65,3.65,0,0,1-.58,1.66,3,3,0,0,1-1.07,1.16c-.49.49-1.17,1-3.6,1.46" fill="url(#gradient_04-3)"/>
                <path d="M146.08,575c-3.5-2.62-5.84-6.12-5.45-9.71a4.19,4.19,0,0,1,3-3.5,5.83,5.83,0,0,1,5.25.88l44.74,33.12c3.4,2.72,5.45,6.6,5.06,10.29a3.63,3.63,0,0,1-2.82,3.21,5.44,5.44,0,0,1-5-1.26Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.39"/>
            </g>
            <g>
                <path d="M1011.77,1197.85c2-1.41,5.75.15,8.56,3.58s3.3,7.49,1.29,8.9-5.75-.24-8.46-3.67S1009.86,1199.26,1011.77,1197.85Z"/>
                <path d="M1021.62,1210.33c2-1.41,1.42-5.38-1.29-8.9l-.25,9.43A2.94,2.94,0,0,0,1021.62,1210.33Z" fill="#656565" fill-rule="evenodd"/>
                <path d="M1017.43,1210.24c1.68.74,3.14.9,4.29.09l-2.26-8.69-1.56-.15Z" fill="#fff" fill-rule="evenodd"/>
                <path d="M1015.31,1199.22c1.15-.9,3.5.11,5.21,2.2s2.06,4.52.82,5.42-3.51-.1-5.21-2.2S1014,1200.13,1015.31,1199.22Z" fill="#0d0d0d"/>
                <path d="M1017.43,1190.62c5.64,3.65,20.06,16.11,12.65,23.49-6.45,6.47-19.8-2.13-25.35-6.07-39.21-27.49-127.18-94.27-134.72-99.91s-14.7-12.07-9.74-19.87,15.55-.68,21.08,2.48C886.88,1094.1,977.37,1164.42,1017.43,1190.62Z" fill="none" stroke="#0d0d0d" stroke-miterlimit="10" stroke-width="0.97"/>
                <path d="M1028.65,1215.41a5.61,5.61,0,0,0,3.3-4.56" fill="none" stroke="#ededed" stroke-linecap="round" stroke-width="1.59"/>
            </g>
            <polygon points="80.53 554.52 80.53 485.78 93.94 474.96 104.14 481.99 91.38 492.69 91.38 562.78 80.53 554.52" fill="url(#gradient_01-2)"/>
            <polygon points="1143.19 1327.35 1143.19 1259.41 1154.69 1248.68 1164.64 1255.95 1154.05 1267.31 1153.94 1335.08 1143.19 1327.35" fill="url(#gradient_01-3)"/>
            <polygon points="1478.46 1345.5 1478.3 1273.29 1465.19 1261.27 1454.43 1267.21 1466.17 1280.01 1466.18 1352.29 1478.46 1345.5" fill="url(#gradient_01-4)"/>
        </g>
        <path d="M1965,873.5c-7.1-5-1125.56-812.22-1177.5-847.68S638.3-5.36,591.42,19.22C551.94,39.91,133.73,264.48,80.53,292.06,25,320.81,0,356.66,0,376.18v80.43c0,41.76,56.7,79.06,80.53,97.91,46.59,37,1048,763.75,1122.74,815.72s184.79,25.06,230.4,0,494.55-274.11,512.94-284.7,53.39-44.19,53.39-69.16v-81.1C2000,899.92,1972.09,878.46,1965,873.5ZM297.13,218.4c2-5.11,9.29-7.05,16.36-4.33s11.21,9.05,9.24,14.16-9.29,7.05-16.35,4.33S295.17,223.51,297.13,218.4ZM1614.84,1234.47c-.75,4-5,9-9.51,11.12-4.88,2.25-8.13.75-7.38-3.62s5.5-9.5,10.13-11.5C1612.59,1228.72,1615.59,1230.6,1614.84,1234.47Zm118.4-65.61c-.36,6.52-4,13.73-8.17,16.11l-78.69,42.59c-4.15,2.38-7.23-1-6.88-7.48h0c.36-6.51,4-13.72,8.17-16.1l78.69-42.59C1730.52,1159,1733.6,1162.35,1733.24,1168.86Zm34.85-20.9c-1,4-5.13,8.75-9.26,10.75-4.38,2.12-7,.37-6-4s5.63-9.38,9.89-11.13S1769.22,1144,1768.09,1148Zm154.29-155.63c-87.63,48-444,251.59-502,283.65s-129,53.61-218.73-13S127.64,479.35,92.33,454-14,374.25,75,322.38c0,0,139.94-77.57,154.82-85.34s22.56-9.13,35-1.07,39.15,21.62,79.41-2.18,110.41-60.08,144.16-78c28.3-15,8.8-29.71,3-34.67S470,109.42,491,98.35,607.44,35.53,607.44,35.53C644.3,15.91,717.63,6.87,765,40S1888.83,843,1922.38,868.1,2010,944.35,1922.38,992.33Z" fill="#000" opacity="0.1"/>
        <path d="M1965,873.5c-7.1-5-1125.56-812.22-1177.5-847.68S638.3-5.36,591.42,19.22C551.94,39.91,133.73,264.48,80.53,292.06,25,320.81,0,356.66,0,376.18v80.43c0,41.76,56.7,79.06,80.53,97.91,46.59,37,1048,763.75,1122.74,815.72s184.79,25.06,230.4,0,494.55-274.11,512.94-284.7,53.39-44.19,53.39-69.16v-81.1C2000,899.92,1972.09,878.46,1965,873.5ZM297.13,218.4c2-5.11,9.29-7.05,16.36-4.33s11.21,9.05,9.24,14.16-9.29,7.05-16.35,4.33S295.17,223.51,297.13,218.4ZM1614.84,1234.47c-.75,4-5,9-9.51,11.12-4.88,2.25-8.13.75-7.38-3.62s5.5-9.5,10.13-11.5C1612.59,1228.72,1615.59,1230.6,1614.84,1234.47Zm118.4-65.61c-.36,6.52-4,13.73-8.17,16.11l-78.69,42.59c-4.15,2.38-7.23-1-6.88-7.48h0c.36-6.51,4-13.72,8.17-16.1l78.69-42.59C1730.52,1159,1733.6,1162.35,1733.24,1168.86Zm34.85-20.9c-1,4-5.13,8.75-9.26,10.75-4.38,2.12-7,.37-6-4s5.63-9.38,9.89-11.13S1769.22,1144,1768.09,1148Zm154.29-155.63c-87.63,48-444,251.59-502,283.65s-129,53.61-218.73-13S127.64,479.35,92.33,454-14,374.25,75,322.38c0,0,139.94-77.57,154.82-85.34s22.56-9.13,35-1.07,39.15,21.62,79.41-2.18,110.41-60.08,144.16-78c28.3-15,8.8-29.71,3-34.67S470,109.42,491,98.35,607.44,35.53,607.44,35.53C644.3,15.91,717.63,6.87,765,40S1888.83,843,1922.38,868.1,2010,944.35,1922.38,992.33Z" fill="#383E45" style="mix-blend-mode: overlay"/>
        <path d="M1130.91,1200.84c33.6,24.59,57.47,42.12,68.37,50.22,89.77,66.63,160.77,45.16,218.73,13s402.74-223.31,490.37-271.29,33.56-99.17,0-124.23c-24.72-18.47-608.29-438.77-932.58-671.89l-.06,0Z" opacity="0.4" fill="url(#gradient_05)"/>
    </g>
</svg>
