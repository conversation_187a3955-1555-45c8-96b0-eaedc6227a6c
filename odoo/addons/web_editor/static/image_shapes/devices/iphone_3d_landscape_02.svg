<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xhtml="http://www.w3.org/1999/xhtml" viewBox="0 0 2000 1400" data-forced-size="true" width="2000" height="1400" data-img-aspect-ratio="19.5:9" data-img-perspective="[[-0.33, 67.02], [65.31, -0.44], [100.43, 26.44], [34.77, 94.77]]">
    <defs>
        <linearGradient id="gradient_01" x1="11995.99" y1="12345.22" x2="13995.99" y2="12345.22" gradientTransform="translate(13995.99 13045.22) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#4d4d4d"/>
            <stop offset="0.01" stop-color="#121212"/>
            <stop offset="0.04" stop-color="#3a3330"/>
            <stop offset="0.07" stop-color="#575757"/>
            <stop offset="0.56" stop-color="#646464"/>
            <stop offset="0.59" stop-color="#414141"/>
            <stop offset="0.62" stop-color="#2c2c2c"/>
            <stop offset="0.66"/>
            <stop offset="0.93" stop-color="#2e2e2e"/>
            <stop offset="0.95" stop-color="#262626"/>
            <stop offset="0.97" stop-color="#1a1a1a"/>
            <stop offset="1" stop-color="#2e2e2e"/>
        </linearGradient>
        <linearGradient id="light_adjust" x1="11995.99" y1="12345.22" x2="13995.99" y2="12345.22" gradientTransform="translate(13995.99 13045.22) rotate(180)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#fff"/>
            <stop offset="0.5" stop-color="#fff" stop-opacity=".5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <radialGradient id="gradient_02" cx="1534.98" cy="138.66" r="7.82" gradientTransform="translate(150.95 -539.47) rotate(20.94)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#658088"/>
            <stop offset="0.07" stop-color="#4f6571"/>
            <stop offset="0.16" stop-color="#374756"/>
            <stop offset="0.27" stop-color="#232d40"/>
            <stop offset="0.39" stop-color="#131a2f"/>
            <stop offset="0.52" stop-color="#080c23"/>
            <stop offset="0.69" stop-color="#02041c"/>
            <stop offset="1" stop-color="#00021a"/>
        </radialGradient>
        <linearGradient id="gradient_03" x1="13682.36" y1="1157.25" x2="13682.36" y2="1283.61" gradientTransform="matrix(-1, 0, 0, 1, 13995.99, 0)" gradientUnits="userSpaceOnUse">
            <stop offset="0.05" stop-color="#333"/>
            <stop offset="0.49"/>
            <stop offset="0.49" stop-color="#c4c4c4"/>
            <stop offset="0.67" stop-color="#c4c4c4"/>
            <stop offset="0.92" stop-color="#333"/>
            <stop offset="0.98" stop-color="#c4c4c4"/>
            <stop offset="1" stop-color="#333"/>
        </linearGradient>
        <linearGradient id="gradient_04" x1="16358.65" y1="8234.15" x2="16442.72" y2="8234.15" gradientTransform="translate(10086.76 17019.59) rotate(-144.15)" gradientUnits="userSpaceOnUse">
            <stop offset="0" stop-color="#141414"/>
            <stop offset="0.35" stop-color="#343434"/>
            <stop offset="0.5" stop-color="#424242"/>
            <stop offset="0.65" stop-color="#343434"/>
            <stop offset="1" stop-color="#141414"/>
        </linearGradient>
        <linearGradient id="gradient_01-2" x1="12161.57" y1="419.25" x2="12054.29" y2="560.76" gradientTransform="matrix(-1, 0, 0, 1, 13995.99, 0)" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_01-3" x1="13221.31" y1="1196.01" x2="13117.35" y2="1333.13" gradientTransform="matrix(-1, 0, 0, 1, 13995.99, 0)" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_01-4" x1="-11139" y1="966.53" x2="-11250.87" y2="1114.1" gradientTransform="translate(11320.62)" xlink:href="#gradient_01"/>
        <linearGradient id="gradient_05" x1="1681.46" y1="378.53" x2="581.43" y2="677.9" gradientUnits="userSpaceOnUse">
            <stop offset="0.4" stop-color="#fff" stop-opacity="0.5"/>
            <stop offset="1" stop-color="#fff"/>
        </linearGradient>
        <clipPath id="screen_path">
            <path d="M1975.31,350.06l4.08,89.91L782,1329.66l-131.59,30.6L30.1,1030.07V901.75L1217.66,37.85,1295.3,8.43l80.91,14.87,132.41,68.49Z"/>
        </clipPath>
        <path id="filterPath" d="M0.9877,0.25l0.002,0.0642L0.391,0.9498l-0.0658,0.0219L0.0151,0.7358V0.6441L0.6088,0.027,0.6476,0.006l0.0405,0.0106,0.0662,0.0489Z"/>
    </defs>
    <svg viewBox="0 0 1 1" id="preview" preserveAspectRatio="none">
        <use xlink:href="#filterPath" fill="darkgrey"/>
    </svg>  
    <image xlink:href="" clip-path="url(#screen_path)" preserveAspectRatio="none" width="100%" height="100%">
        <animateMotion dur="1ms" repeatCount="indefinite"/>
    </image>
    <g id="device">
        <path d="M1919.47,292.06c-53.2-27.58-471.41-252.15-510.89-272.84-46.88-24.58-144.14-28.85-196.07,6.6S42.11,868.55,35,873.5,0,899.92,0,935.28v81.1c0,25,35,58.58,53.39,69.16s467.42,259.74,512.94,284.7,155.71,52.06,230.4,0S1872.88,591.52,1919.47,554.52c23.83-18.85,80.53-56.15,80.53-97.91V376.18C2000,356.66,1975,320.81,1919.47,292.06Zm-23.53,160.56C1860.63,478,890.49,1184.33,800.72,1251.06S640,1296.22,582,1264.07,179.25,1040.76,91.62,992.78s-33.56-99.17,0-124.23S1187.8,78.47,1235.17,45.35s120.69-24.09,157.56-4.47c0,0,87.27,46.59,108.28,57.66s5.45,17.78-.39,22.73-20.32,28.07,8,43c33.75,18,103.88,54.25,144.15,78s74.6,9.62,87,1.55,20.14-6.7,35,1.07,138.44,76,138.44,76C2002.24,372.88,1931.24,427.27,1895.94,452.62Z" fill="url(#gradient_01)"/>
        <path d="M1919.47,292.06c-53.2-27.58-471.41-252.15-510.89-272.84-46.88-24.58-144.14-28.85-196.07,6.6S42.11,868.55,35,873.5,0,899.92,0,935.28v81.1c0,25,35,58.58,53.39,69.16s467.42,259.74,512.94,284.7,155.71,52.06,230.4,0S1872.88,591.52,1919.47,554.52c23.83-18.85,80.53-56.15,80.53-97.91V376.18C2000,356.66,1975,320.81,1919.47,292.06ZM1531.4,129.43c7.07-2.71,14.39-.77,16.36,4.34s-2.17,11.44-9.24,14.16-14.4.77-16.36-4.34S1524.33,132.14,1531.4,129.43ZM241.17,1158.71c-4.13-2-8.26-6.75-9.26-10.75s1.25-6,5.38-4.38,8.76,6.75,9.89,11.13S245.55,1160.83,241.17,1158.71Zm112.45,68.85L274.93,1185c-4.16-2.38-7.81-9.59-8.17-16.11s2.72-9.85,6.88-7.47L352.33,1204c4.16,2.38,7.81,9.59,8.17,16.1h0C360.85,1226.6,357.77,1229.94,353.62,1227.56Zm41.05,18c-4.5-2.12-8.76-7.12-9.51-11.12s2.25-5.75,6.76-4c4.63,2,9.26,7.12,10.13,11.5S399.55,1247.84,394.67,1245.59Zm1516.24-794c-35.31,25.35-1020.27,744.08-1110,810.81s-160.77,45.17-218.73,13-416.89-234-504.52-282-33.56-99.17,0-124.23,1109.61-799.45,1157-832.57,120.7-24.09,157.56-4.47c0,0,94.42,53.88,115.42,64.95s5.45,17.78-.39,22.73-20.82,25,7.48,39.93c33.75,18,103.88,54.25,144.15,78.05s65.71,6.84,78.16-1.22,20.13-6.7,35,1.07S1928.22,320,1928.22,320C2017.21,371.82,1946.21,426.22,1910.91,451.57Z" fill="url(#light_adjust)" opacity="0.2"/>
        <path d="M.1,935.28v7.77c0,7.77,6.32,45.46,50.86,69.84s363,200.58,496.5,274.69,225.25,32.83,259.68,8,1074.21-783,1131.78-824.95S2000,408.14,2000,387V376.18c0-19.52-25-55.37-80.53-84.12-53.3-27.58-471.41-252.15-510.89-272.84-46.88-24.58-144.14-28.85-196.07,6.6S42.11,868.55,35,873.5.1,900,.1,935.28Zm49.79-63.14c16.25-12,1118.56-805.52,1176.82-843.31S1376.19,11.93,1403,26c129.45,67.8,390.69,208.15,514,274.11s49.11,130.54,17.7,153.47S924.92,1190.06,816.48,1269.8s-211.25,31-245.48,11.56S161.16,1055.92,65.45,1003,33.65,884.09,49.89,872.14Z" fill="#fff" opacity="0.5"/>
        <path d="M1917,300.13c-123.23-66-384.56-206.22-514-274.11-26.94-14.09-118.07-35-176.32,2.81S66.13,860.1,49.89,872.14-30.34,950,65.45,1003s471.31,259,505.55,278.38,137,68.19,245.48-11.56S1903.33,476.52,1934.74,453.6,2040.36,366.08,1917,300.13Zm-21.1,152.49C1860.63,478,890.49,1184.33,800.72,1251.06S640,1296.22,582,1264.07,179.25,1040.76,91.62,992.78s-33.56-99.17,0-124.23S1187.8,78.47,1235.17,45.35s120.69-24.09,157.56-4.47c0,0,87.27,46.59,108.28,57.66s5.45,17.78-.39,22.73-20.32,28.07,8,43c33.75,18,103.88,54.25,144.15,78s74.6,9.62,87,1.55,20.14-6.7,35,1.07,138.44,76,138.44,76C2002.24,372.88,1931.24,427.27,1895.94,452.62Z"/>
        <g id="details">
            <g>
                <ellipse cx="1534.96" cy="138.68" rx="13.71" ry="9.91" transform="translate(51.84 557.82) rotate(-20.94)" fill="#131516"/>
                <ellipse cx="1534.98" cy="138.66" rx="8.95" ry="6.51" transform="translate(51.84 557.79) rotate(-20.94)" fill="url(#gradient_02)"/>
            </g>
            <g>
                <path d="M389.42,1227.72c5.63,2.25,11.26,8.75,12.26,14s-3.13,7.12-8.89,4.5c-5.5-2.63-10.38-8.62-11.26-13.5S384.16,1225.6,389.42,1227.72Z" fill="#000102"/>
                <path d="M392.92,1246.34c5.76,2.88,9.76,1,8.88-4.5l-12.51,2.25C390.42,1244.72,391.54,1245.59,392.92,1246.34Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M385.16,1240a22.32,22.32,0,0,0,7.76,6.37l6.63-5.75-2.88-2.49Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M391.92,1230.47c4.63,2,9.26,7.13,10.13,11.5s-2.5,5.87-7.38,3.62c-4.5-2.12-8.76-7.12-9.51-11.12S387.41,1228.72,391.92,1230.47Z" fill="#3c3d3d"/>
            </g>
            <g>
                <path d="M234.91,1141c5.13,2,10.64,8.25,12,13.62s-2,7.5-7.26,5c-5-2.37-9.89-8.25-11-13.12C227.28,1141.46,230,1139,234.91,1141Z" fill="#000102"/>
                <path d="M239.67,1159.58c5.25,2.63,8.63.5,7.26-5l-10.64,2.88C237.29,1158.08,238.54,1159,239.67,1159.58Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M232.29,1153.46a21.71,21.71,0,0,0,7.25,6l5.13-6.25-2.75-2.38Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M237.29,1143.58c4.26,1.75,8.76,6.75,9.89,11.13s-1.63,6.12-6,4c-4.13-2-8.26-6.75-9.26-10.75S233.16,1142,237.29,1143.58Z" fill="#3c3d3d"/>
            </g>
            <g>
                <path d="M184.06,1116c3.79,1.46,8.07,6.22,9.24,10.4s-1.27,5.82-5.16,4.07a17.82,17.82,0,0,1-8.55-10.1C178.52,1116.56,180.46,1114.52,184.06,1116Z" fill="#000102"/>
                <path d="M188.14,1130.45c3.89,2,6.33.2,5.16-4.07l-7.69,2.42A15.55,15.55,0,0,0,188.14,1130.45Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M182.5,1125.79a16.22,16.22,0,0,0,5.55,4.57l3.6-5-2.14-1.84Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M185.91,1118a15.28,15.28,0,0,1,7.58,8.55c.88,3.4-1,4.76-4.28,3.3a14.79,14.79,0,0,1-7.1-8.25C181.24,1118.51,182.89,1116.86,185.91,1118Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M163.54,1103.84c3.79,1.46,8,6.22,9.24,10.39s-1.07,5.93-5,4.08a17.82,17.82,0,0,1-8.46-10C158.09,1104.52,159.84,1102.48,163.54,1103.84Z" fill="#000102"/>
                <path d="M167.62,1118.22c3.89,1.94,6.23.19,5-4.08l-7.49,2.52A26.92,26.92,0,0,0,167.62,1118.22Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M162.18,1113.65a17.45,17.45,0,0,0,5.54,4.57l3.4-5-2.14-1.84Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M165.29,1105.78a14.7,14.7,0,0,1,7.58,8.55c1,3.4-1,4.86-4.18,3.3a15.15,15.15,0,0,1-7.1-8.25C160.72,1106.27,162.27,1104.62,165.29,1105.78Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M143.89,1092.86c3.7,1.46,8,6.12,9.14,10.3s-1,5.93-4.86,4.18a17.58,17.58,0,0,1-8.46-10C138.45,1093.54,140.2,1091.5,143.89,1092.86Z" fill="#000102"/>
                <path d="M148.07,1107.34c3.8,1.84,6,.09,4.87-4.18l-7.3,2.53A12.29,12.29,0,0,0,148.07,1107.34Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M142.63,1102.87a18.21,18.21,0,0,0,5.44,4.56l3.31-5-2-1.84Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M145.64,1094.9a15.53,15.53,0,0,1,7.59,8.45c1,3.4-.88,4.86-4.09,3.4a15.39,15.39,0,0,1-7.1-8.15C141.17,1095.49,142.63,1093.84,145.64,1094.9Z" fill="#0a0e0e"/>
            </g>
            <path d="M271.64,1186l84,45.45c4.43,2.55,8.4-2.7,8-9.65h0c-.38-7-2.5-17.14-6.94-19.68l-84-45.45c-4.44-2.55-9.5,3.51-9.12,10.46h0C264,1174.09,267.2,1183.47,271.64,1186Z" fill="url(#gradient_03)"/>
            <path d="M274.93,1185l78.69,42.59c4.15,2.38,7.23-1,6.88-7.48h0c-.36-6.51-4-13.72-8.17-16.1l-78.69-42.59c-4.16-2.38-7.24,1-6.88,7.47h0C267.12,1175.38,270.77,1182.59,274.93,1185Z" fill="#131313"/>
            <g>
                <path d="M1532.78,813.76,1700.5,693.11h0a18.39,18.39,0,0,0,4.28-5.24,32.18,32.18,0,0,0,3-7.29,28.3,28.3,0,0,0,1.17-7.09c0-2,.29-1.85-.49-2.62-1.45-1.17-3.2-3-4.27-3.5-.69-.39-1.37-.58-1.85-.87a18.35,18.35,0,0,0-4-1.46,5.48,5.48,0,0,0-3.31.1,15.89,15.89,0,0,0-3.79,2.13l-159.94,119.2a20.68,20.68,0,0,0-8.17,19.52h0a6.24,6.24,0,0,0,1,2.82,5.16,5.16,0,0,0,1.85,2c1.46,1.17,2.62,2,6.81,2.91" fill="url(#gradient_04)"/>
                <path d="M1701.28,693c6.12-4.66,10.31-10.78,9.63-17.09a7.29,7.29,0,0,0-5.26-6.12,9.94,9.94,0,0,0-9.24,1.65L1537.83,789.58c-6,4.76-9.53,11.65-8.94,18.16a6.32,6.32,0,0,0,4.86,5.64,10.21,10.21,0,0,0,8.75-2.14Z" fill="#898989" stroke="#1c1c1c" stroke-width="0.69"/>
            </g>
            <g>
                <path d="M1147.32,1068.79c-5.64,3.64-20.08,16.08-12.69,23.47,6.44,6.49,19.81-2.1,25.36-6,39.26-27.42,127.34-94.06,134.88-99.69s14.72-12,9.78-19.85-15.55-.71-21.08,2.45C1278,972.49,1187.42,1042.65,1147.32,1068.79Z" fill="none" stroke="#0d0d0d" stroke-miterlimit="10" stroke-width="0.97"/>
            </g>
            <polygon points="1919.47 554.52 1919.47 485.78 1906.06 474.96 1895.86 481.99 1908.62 492.69 1908.62 562.78 1919.47 554.52" fill="url(#gradient_01-2)"/>
            <polygon points="856.8 1327.35 856.8 1259.41 845.31 1248.68 835.36 1255.95 845.95 1267.31 846.05 1335.08 856.8 1327.35" fill="url(#gradient_01-3)"/>
            <g>
                <path d="M536.76,1314.52c3.8,1.45,8.08,6.21,9.24,10.39s-1.26,5.83-5.15,4.08a17.9,17.9,0,0,1-8.56-10.1C531.22,1315.1,533.17,1313.06,536.76,1314.52Z" fill="#000102"/>
                <path d="M540.85,1329c3.89,1.94,6.32.19,5.15-4.08l-7.68,2.43A17,17,0,0,0,540.85,1329Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M535.21,1324.33a16.16,16.16,0,0,0,5.54,4.56l3.6-5-2.14-1.85Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M538.61,1316.56a15.31,15.31,0,0,1,7.59,8.54c.87,3.4-1,4.76-4.28,3.31a14.77,14.77,0,0,1-7.1-8.26C533.94,1317,535.6,1315.39,538.61,1316.56Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M516.24,1302.38c3.8,1.45,8,6.21,9.24,10.39s-1.07,5.92-5,4.08a17.88,17.88,0,0,1-8.46-10C510.8,1303.06,512.55,1301,516.24,1302.38Z" fill="#000102"/>
                <path d="M520.33,1316.75c3.89,1.94,6.22.2,5-4.08l-7.49,2.53A23.73,23.73,0,0,0,520.33,1316.75Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M514.88,1312.19a17.49,17.49,0,0,0,5.54,4.56l3.41-5-2.14-1.85Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M518,1304.32a14.65,14.65,0,0,1,7.59,8.55c1,3.4-1,4.85-4.18,3.3a15.12,15.12,0,0,1-7.1-8.26C513.42,1304.8,515,1303.15,518,1304.32Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M496.6,1291.4c3.69,1.46,8,6.12,9.14,10.3s-1,5.92-4.86,4.17a17.65,17.65,0,0,1-8.47-10C491.15,1292.08,492.9,1290,496.6,1291.4Z" fill="#000102"/>
                <path d="M500.78,1305.87c3.79,1.85,6,.1,4.86-4.17l-7.29,2.52A12,12,0,0,0,500.78,1305.87Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M495.33,1301.4a18.13,18.13,0,0,0,5.45,4.57l3.31-5-2-1.85Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M498.35,1293.44a15.48,15.48,0,0,1,7.58,8.45c1,3.4-.87,4.86-4.08,3.4a15.32,15.32,0,0,1-7.1-8.16C493.87,1294,495.33,1292.37,498.35,1293.44Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M477.34,1280.33c3.7,1.36,7.88,6.12,9.14,10.29s-.87,5.93-4.67,4.18c-3.59-1.65-7.29-6.12-8.46-9.91S473.84,1279,477.34,1280.33Z" fill="#000102"/>
                <path d="M481.72,1294.8c3.79,1.84,5.93.1,4.66-4.18l-7.19,2.62A16.8,16.8,0,0,0,481.72,1294.8Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M476.27,1290.33a15.59,15.59,0,0,0,5.45,4.47l3.21-5-2.05-1.75Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M479.19,1282.37a15.48,15.48,0,0,1,7.58,8.45c1,3.4-.77,4.85-3.89,3.4a15.41,15.41,0,0,1-7.1-8.16C474.71,1283,476.27,1281.3,479.19,1282.37Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M458.76,1269.84c3.6,1.36,7.78,6,9.15,10.2s-.78,5.92-4.57,4.27c-3.6-1.65-7.3-6.12-8.47-9.91S455.17,1268.48,458.76,1269.84Z" fill="#000102"/>
                <path d="M463.14,1284.31c3.7,1.84,5.84,0,4.57-4.27l-7,2.71A23.19,23.19,0,0,0,463.14,1284.31Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M457.79,1279.84a16.36,16.36,0,0,0,5.35,4.47l3-5.15-2.05-1.75Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M460.51,1271.88a15.1,15.1,0,0,1,7.49,8.35c1,3.4-.68,4.86-3.79,3.4a15.2,15.2,0,0,1-7-8.16C456.14,1272.46,457.6,1270.81,460.51,1271.88Z" fill="#0a0e0e"/>
            </g>
            <g>
                <path d="M440.29,1259.54c3.59,1.36,7.78,6,9,10.2s-.68,5.92-4.38,4.27a18.11,18.11,0,0,1-8.36-9.81C435.23,1260.32,436.88,1258.18,440.29,1259.54Z" fill="#000102"/>
                <path d="M444.86,1273.82c3.69,1.84,5.73,0,4.37-4.28l-6.81,2.72A10.17,10.17,0,0,0,444.86,1273.82Z" fill="#7a7a7a" fill-rule="evenodd"/>
                <path d="M439.51,1269.35a16.26,16.26,0,0,0,5.35,4.47l2.91-5.15-2-1.75Z" fill="#f4f4f4" fill-rule="evenodd"/>
                <path d="M442,1261.48a15.9,15.9,0,0,1,7.48,8.36c1.07,3.4-.58,4.85-3.69,3.49a15,15,0,0,1-7-8.06C437.85,1262,439.22,1260.32,442,1261.48Z" fill="#0a0e0e"/>
            </g>
            <polygon points="92.94 1107.64 93.1 1036.11 105.54 1025.1 116.38 1031.08 105.22 1042.81 105.22 1114.48 92.94 1107.64" fill="url(#gradient_01-4)"/>
        </g>
        <path d="M1919.47,292.06c-53.2-27.58-471.41-252.15-510.89-272.84-46.88-24.58-144.14-28.85-196.07,6.6S42.11,868.55,35,873.5,0,899.92,0,935.28v81.1c0,25,35,58.58,53.39,69.16s467.42,259.74,512.94,284.7,155.71,52.06,230.4,0S1872.88,591.52,1919.47,554.52c23.83-18.85,80.53-56.15,80.53-97.91V376.18C2000,356.66,1975,320.81,1919.47,292.06ZM1531.4,129.43c7.07-2.71,14.39-.77,16.36,4.34s-2.17,11.44-9.24,14.16-14.4.77-16.36-4.34S1524.33,132.14,1531.4,129.43ZM241.17,1158.71c-4.13-2-8.26-6.75-9.26-10.75s1.25-6,5.38-4.38,8.76,6.75,9.89,11.13S245.55,1160.83,241.17,1158.71Zm112.45,68.85L274.93,1185c-4.16-2.38-7.81-9.59-8.17-16.11s2.72-9.85,6.88-7.47L352.33,1204c4.16,2.38,7.81,9.59,8.17,16.1h0C360.85,1226.6,357.77,1229.94,353.62,1227.56Zm41.05,18c-4.5-2.12-8.76-7.12-9.51-11.12s2.25-5.75,6.76-4c4.63,2,9.26,7.12,10.13,11.5S399.55,1247.84,394.67,1245.59Zm1516.24-794c-35.31,25.35-1020.27,744.08-1110,810.81s-160.77,45.17-218.73,13-416.89-234-504.52-282-33.56-99.17,0-124.23,1109.61-799.45,1157-832.57,120.7-24.09,157.56-4.47c0,0,94.42,53.88,115.42,64.95s5.45,17.78-.39,22.73-20.82,25,7.48,39.93c33.75,18,103.88,54.25,144.15,78.05s65.71,6.84,78.16-1.22,20.13-6.7,35,1.07S1928.22,320,1928.22,320C2017.21,371.82,1946.21,426.22,1910.91,451.57Z" fill="#000" opacity="0.1"/>
        <path d="M1919.47,292.06c-53.2-27.58-471.41-252.15-510.89-272.84-46.88-24.58-144.14-28.85-196.07,6.6S42.11,868.55,35,873.5,0,899.92,0,935.28v81.1c0,25,35,58.58,53.39,69.16s467.42,259.74,512.94,284.7,155.71,52.06,230.4,0S1872.88,591.52,1919.47,554.52c23.83-18.85,80.53-56.15,80.53-97.91V376.18C2000,356.66,1975,320.81,1919.47,292.06ZM1531.4,129.43c7.07-2.71,14.39-.77,16.36,4.34s-2.17,11.44-9.24,14.16-14.4.77-16.36-4.34S1524.33,132.14,1531.4,129.43ZM241.17,1158.71c-4.13-2-8.26-6.75-9.26-10.75s1.25-6,5.38-4.38,8.76,6.75,9.89,11.13S245.55,1160.83,241.17,1158.71Zm112.45,68.85L274.93,1185c-4.16-2.38-7.81-9.59-8.17-16.11s2.72-9.85,6.88-7.47L352.33,1204c4.16,2.38,7.81,9.59,8.17,16.1h0C360.85,1226.6,357.77,1229.94,353.62,1227.56Zm41.05,18c-4.5-2.12-8.76-7.12-9.51-11.12s2.25-5.75,6.76-4c4.63,2,9.26,7.12,10.13,11.5S399.55,1247.84,394.67,1245.59Zm1516.24-794c-35.31,25.35-1020.27,744.08-1110,810.81s-160.77,45.17-218.73,13-416.89-234-504.52-282-33.56-99.17,0-124.23,1109.61-799.45,1157-832.57,120.7-24.09,157.56-4.47c0,0,94.42,53.88,115.42,64.95s5.45,17.78-.39,22.73-20.82,25,7.48,39.93c33.75,18,103.88,54.25,144.15,78.05s65.71,6.84,78.16-1.22,20.13-6.7,35,1.07S1928.22,320,1928.22,320C2017.21,371.82,1946.21,426.22,1910.91,451.57Z" fill="#383E45" style="mix-blend-mode: overlay"/>
        <path d="M1913.25,321s-123.56-68.27-138.44-76-22.57-9.13-35-1.07-46.78,22.25-87-1.55-110.4-60.08-144.15-78c-28.3-15-13.81-38.07-8-43s21.4-11.65.39-22.73-108.28-57.66-108.28-57.66c-36.87-19.62-110.2-28.66-157.56,4.47-11.48,8-82.62,59.06-184.29,132.13l123.66,800.39c309.71-225.81,699.26-509.35,721.4-525.25C1931.24,427.27,2002.24,372.88,1913.25,321Z" opacity="0.4" fill="url(#gradient_05)"/>
    </g>
</svg>
