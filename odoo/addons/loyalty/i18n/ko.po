# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* loyalty
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:03+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__trigger
msgid ""
"\n"
"        Automatic: Customers will be eligible for a reward automatically in their cart.\n"
"        Use a code: Customers will be eligible for a reward if they enter a code.\n"
"        "
msgstr ""
"\n"
"        자동: 고객이 장바구니에서 자동으로 리워드를 받을 수 있습니다.\n"
"        코드 사용: 고객이 코드를 입력하면 리워드를 받을 수 있습니다.\n"
"        "

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__available_on
msgid ""
"\n"
"        Manage where your program should be available for use.\n"
"        "
msgstr ""
"\n"
"        프로그램을 사용해야 할 장소를 관리하십시오.\n"
"        "

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__portal_visible
msgid ""
"\n"
"        Show in web portal, PoS customer ticket, eCommerce checkout, the number of points available and used by reward.\n"
"        "
msgstr ""
"\n"
"        웹 포탈에서나 POS 고객 항목 및 이커머스 결제 시에 사용 가능한 포인트 및 리워드에 사용된 포인트를 표시합니다. .\n"
"        "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid " (Max %s)"
msgstr " (최대 %s)"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "$100"
msgstr "$100"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "%g%% on "
msgstr "다음에서%g%% "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "%s on "
msgstr "%s"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "%s per point"
msgstr "포인트당 %s"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "%s per point on "
msgstr "다음에서 포인트당 %s"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "( Max"
msgstr "(최대"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "(if at least"
msgstr "(최소"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "(tax excluded)"
msgstr "(세금 제외)"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/js/portal/loyalty_card_dialog/loyalty_card_dialog.xml:0
msgid "-> View History"
msgstr "-> 내역 보기"

#. module: loyalty
#: model:loyalty.program,name:loyalty.10_percent_coupon
msgid "10% Discount Coupons"
msgstr "10% 할인 쿠폰"

#. module: loyalty
#: model:loyalty.reward,description:loyalty.10_percent_coupon_reward
#: model:loyalty.reward,description:loyalty.10_percent_with_code_reward
msgid "10% on your order"
msgstr "주문 시 10%"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "1000"
msgstr "1000"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "2+1 Free"
msgstr "2+1 무료 행사"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "2023-08-20"
msgstr "2023-08-20"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "2023-12-31"
msgstr "2023-12-31"

#. module: loyalty
#: model:mail.template,body_html:loyalty.mail_template_gift_card
msgid ""
"<div style=\"background: #ffffff\">\n"
"                <div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                    Here is your gift card!\n"
"                </div>\n"
"                <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                    <img src=\"/loyalty/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\"/>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                        <strong t-out=\"format_amount(object.points, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                    <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                        <strong>Gift Card Code</strong>\n"
"                    </p>\n"
"                    <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"                </div>\n"
"                <div t-if=\"object.expiration_date\" style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">Card expires <t t-out=\"format_date(object.expiration_date) or ''\">05/05/2021</t></h3>\n"
"                </div>\n"
"                <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                    <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">Use it right now!</a>\n"
"                    </span>\n"
"                </div>\n"
"            </div>\n"
"        "
msgstr ""
"<div style=\"background: #ffffff\">\n"
"                <div style=\"margin:0px; font-size:24px; font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:36px; color:#333333; text-align: center\">\n"
"                    기프트 카드 정보를 확인하세요!\n"
"                </div>\n"
"                <div style=\"padding-top:20px; padding-bottom:20px\">\n"
"                    <img src=\"/loyalty/static/img/gift_card.png\" style=\"display:block; border:0; outline:none; text-decoration:none; margin:auto;\" width=\"300\"/>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:48px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:40px; font-style:normal; font-weight:normal; color:#333333; text-align:center\">\n"
"                        <strong t-out=\"format_amount(object.points, object.currency_id) or ''\">$ 150.00</strong></h3>\n"
"                </div>\n"
"                <div style=\"padding:0; margin:0px; padding-top:35px; padding-bottom:35px; background-color:#efefef; text-align:center;\">\n"
"                    <p style=\"margin:0px; font-size:14px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:21px; color:#333333\">\n"
"                        <strong>기프트 카드 코드</strong>\n"
"                    </p>\n"
"                    <p style=\"margin:0px; font-size:25px;font-family:arial, 'helvetica neue', helvetica, sans-serif; line-height:38px; color:#A9A9A9\" t-out=\"object.code or ''\">4f10-15d6-41b7-b04c-7b3e</p>\n"
"                </div>\n"
"                <div t-if=\"object.expiration_date\" style=\"padding:0; margin:0px; padding-top:10px; padding-bottom:10px; text-align:center;\">\n"
"                    <h3 style=\"margin:0px; line-height:17px; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:14px; font-style:normal; font-weight:normal; color:#A9A9A9; text-align:center\">카드 사용 만료일 <t t-out=\"format_date(object.expiration_date) or ''\">05/05/2021</t></h3>\n"
"                </div>\n"
"                <div style=\"padding:20px; margin:0px; text-align:center;\">\n"
"                    <span style=\"background-color:#999999; display:inline-block; width:auto; border-radius:5px;\">\n"
"                        <a t-attf-href=\"{{ object.get_base_url() }}/shop\" target=\"_blank\" style=\"text-decoration:none; font-family:arial, 'helvetica neue', helvetica, sans-serif; font-size:22px; color:#FFFFFF; border-style:solid; border-color:#999999; border-width:20px 30px; display:inline-block; background-color:#999999; border-radius:5px; font-weight:bold; font-style:normal; line-height:26px; width:auto; text-align:center\">지금 바로 사용해보세요!</a>\n"
"                    </span>\n"
"                </div>\n"
"            </div>\n"
"        "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Product Domain\"/>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"Product Domain\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/><span>All Products</span>"
msgstr "<i class=\"fa fa-cube fa-fw\" title=\"Products\"/><span>전체 품목</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-cubes fa-fw\" title=\"Product Categories\"/>"
msgstr "<i class=\"fa fa-cubes fa-fw\" title=\"Product Categories\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-search fa-fw\" title=\"Product Domain\"/>"
msgstr "<i class=\"fa fa-search fa-fw\" title=\"Product Domain\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "<i class=\"fa fa-tags fa-fw\" title=\"Product Tags\"/>"
msgstr "<i class=\"fa fa-tags fa-fw\" title=\"Product Tags\"/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"program_type not in ('coupons', 'next_order_coupons')\">Coupons</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'loyalty'\">Loyalty Cards</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type not in ('promotion', 'buy_x_get_y')\">Promos</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'promo_code'\">Discount</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'gift_card'\">Gift Cards</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'ewallet'\">eWallets</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"program_type not in ('coupons', 'next_order_coupons')\">쿠폰</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'loyalty'\">적립 카드</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type not in ('promotion', 'buy_x_get_y')\">프로모션</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'promo_code'\">할인</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'gift_card'\">기프트 카드</span>\n"
"                                <span class=\"o_stat_text\" invisible=\"program_type != 'ewallet'\">e월렛</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid ""
"<span class=\"text-center\">OR</span>\n"
"                                        <br/>"
msgstr ""
"<span class=\"text-center\">또는</span>\n"
"                                        <br/>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid ""
"<span colspan=\"2\" invisible=\"program_type != 'coupons'\">Grant the amount"
" of coupon points defined as the coupon value</span>"
msgstr ""
"<span colspan=\"2\" invisible=\"program_type != 'coupons'\">지정된 쿠폰 가격만큼 쿠폰 "
"포인트 금액을 부여합니다.</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "<span invisible=\"not clear_wallet\"> (or more)</span>"
msgstr "<span invisible=\"not clear_wallet\"> (또는 그 이상)</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid ""
"<span invisible=\"not will_send_mail\">\n"
"                            Generate and Send \n"
"                        </span>\n"
"                        <span invisible=\"will_send_mail\">\n"
"                            Generate \n"
"                        </span>"
msgstr ""
"<span invisible=\"not will_send_mail\">\n"
"                            생성 후 전송 \n"
"                        </span>\n"
"                        <span invisible=\"will_send_mail\">\n"
"                            생성 \n"
"                        </span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.portal_loyalty_history_breadcrumbs
msgid "<span>History</span>"
msgstr "<span>내역</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>Minimum purchase of</span>"
msgstr "<span>최소 구매 수량</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>Valid for purchase above</span>"
msgstr "<span>위와 같이 구매 시 사용 가능</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "<span>on</span>"
msgstr " "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "<span>products</span>"
msgstr "<span>품목</span>"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "<strong>Gift Card Code</strong>"
msgstr "<strong>기프트 카드 코드</strong>"

#. module: loyalty
#: model:mail.template,body_html:loyalty.mail_template_loyalty_card
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto; background:#ffffff; color:#333333;\"><tbody>\n"
"<tr>\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object._get_mail_partner().name\">\n"
"            Congratulations <t t-out=\"object._get_mail_partner().name or ''\">Brandon Freeman</t>,<br/>\n"
"        </t>\n"
"\n"
"        Here is your reward from <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>.<br/>\n"
"\n"
"        <t t-foreach=\"object.program_id.reward_ids\" t-as=\"reward\">\n"
"            <t t-if=\"reward.required_points &lt;= object.points\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-esc=\"reward.description\">Reward Description</span>\n"
"                <br/>\n"
"            </t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"<tr style=\"margin-top: 16px\">\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        Use this promo code\n"
"        <t t-if=\"object.expiration_date\">\n"
"            before <t t-out=\"object.expiration_date or ''\">2021-06-16</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-foreach=\"object.program_id.rule_ids\" t-as=\"rule\">\n"
"            <t t-if=\"rule.minimum_qty not in [0, 1]\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Minimum purchase of <t t-out=\"rule.minimum_qty or ''\">10</t> products\n"
"                </span><br/>\n"
"            </t>\n"
"            <t t-if=\"rule.minimum_amount != 0.00\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    Valid for purchase above <t t-out=\"rule.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(rule.minimum_amount) or ''\">10.00</t>\n"
"                </span><br/>\n"
"            </t>\n"
"        </t>\n"
"        <br/>\n"
"        Thank you,\n"
"        <t t-if=\"object._get_signature()\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"object._get_signature() or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%; margin:0px auto; background:#ffffff; color:#333333;\"><tbody>\n"
"<tr>\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        <t t-if=\"object._get_mail_partner().name\">\n"
"            축하합니다, <t t-out=\"object._get_mail_partner().name or ''\">Brandon Freeman</t>님.<br/>\n"
"        </t>\n"
"\n"
"        <t t-out=\"object.program_id.company_id.name or ''\">YourCompany</t>에서 고객님에게 리워드를 발급해드렸습니다.<br/>\n"
"\n"
"        <t t-foreach=\"object.program_id.reward_ids\" t-as=\"reward\">\n"
"            <t t-if=\"reward.required_points &lt;= object.points\">\n"
"                <span style=\"font-size: 50px; color: #875A7B; font-weight: bold;\" t-esc=\"reward.description\">리워드 세부 내용</span>\n"
"                <br/>\n"
"            </t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"<tr style=\"margin-top: 16px\">\n"
"    <td valign=\"top\" style=\"text-align: center; font-size: 14px;\">\n"
"        다음 구매 시 프로모션 코드를 사용하세요.\n"
"        <t t-if=\"object.expiration_date\">\n"
"            만료일: <t t-out=\"object.expiration_date or ''\">2021-06-16</t>\n"
"        </t>\n"
"        <p style=\"margin-top: 16px;\">\n"
"            <strong style=\"padding: 16px 8px 16px 8px; border-radius: 3px; background-color: #F1F1F1;\" t-out=\"object.code or ''\">15637502648479132902</strong>\n"
"        </p>\n"
"        <t t-foreach=\"object.program_id.rule_ids\" t-as=\"rule\">\n"
"            <t t-if=\"rule.minimum_qty not in [0, 1]\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    최소 구매 수량: <t t-out=\"rule.minimum_qty or ''\">10</t>개\n"
"                </span><br/>\n"
"            </t>\n"
"            <t t-if=\"rule.minimum_amount != 0.00\">\n"
"                <span style=\"font-size: 14px;\">\n"
"                    최소 주문 금액: <t t-out=\"rule.company_id.currency_id.symbol or ''\">$</t><t t-out=\"'%0.2f' % float(rule.minimum_amount) or ''\">10.00</t>\n"
"                </span><br/>\n"
"            </t>\n"
"        </t>\n"
"        <br/>\n"
"        감사합니다.\n"
"        <t t-if=\"object._get_signature()\" data-o-mail-quote-container=\"1\">\n"
"            <br/>\n"
"            <t t-out=\"object._get_signature() or ''\" data-o-mail-quote=\"1\">--<br data-o-mail-quote=\"1\"/>Mitchell Admin</t>\n"
"        </t>\n"
"    </td>\n"
"</tr>\n"
"</tbody></table>\n"
"        "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
msgid "A coupon with the same code was found."
msgstr "동일한 코드를 사용 중인 쿠폰이 이미 존재합니다."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_card_card_code_unique
msgid "A coupon/loyalty card must have a unique code."
msgstr "쿠폰/적립 카드에는 고유 코드가 있어야 합니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "A program must have at least one reward."
msgstr "프로그램에는 한 가지 이상의 혜택이 포함되어야 합니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_buttons
msgid "A reward is waiting for you"
msgstr "보상이 기다리고 있습니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "A reward product can't be of type \"combo\"."
msgstr "보상 상품은 \"콤보\" 유형이 될 수 없습니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
msgid "A trigger with the same code as one of your coupon already exists."
msgstr "쿠폰 중에서 같은 코드로 동작되는 내용이 이미 존재합니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "ABCDE12345"
msgstr "ABCDE12345"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__active
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__active
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_search
msgid "Active"
msgstr "활성화"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_res_partner__loyalty_card_count
#: model:ir.model.fields,field_description:loyalty.field_res_users__loyalty_card_count
msgid "Active loyalty cards"
msgstr "사용 중인 적립 카드"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
msgid "Add"
msgstr "추가"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Add a reward"
msgstr "리워드 추가"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Add a rule"
msgstr "규칙 추가"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__all_discount_product_ids
msgid "All Discount Product"
msgstr "전체 할인 품목"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Among"
msgstr "다음 중에서 "

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "Among:"
msgstr "다음 중에서:"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_generate_wizard__mode__anonymous
msgid "Anonymous Customers"
msgstr "익명 고객"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__mode
msgid "Application"
msgstr "응용 프로그램"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "Applied to:"
msgstr "적용 대상:"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__applies_on
msgid "Applies On"
msgstr "적용 대상"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_search
msgid "Archived"
msgstr "보관됨"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_mail__trigger__create
msgid "At Creation"
msgstr "다음을 생성할 때"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__trigger__auto
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__mode__auto
msgid "Automatic"
msgstr "자동"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Automatic promo: 10% off on orders higher than $50"
msgstr "프로모션 자동 적용: $50 이상 주문 시 10% 할인"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__available_on
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Available On"
msgstr "다음에 사용 가능"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Balance"
msgstr "잔액"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Barcode"
msgstr "바코드"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Buy 10 products to get 10$ off on the 11th one"
msgstr "10개 상품 구매 시 11번째 상품에는 $10 할인 혜택을 적용합니다"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Buy 2 products and get a third one for free"
msgstr "상품을 2개 구입하면 세번째 상품은 무료로 증정합니다"

#. module: loyalty
#: model:loyalty.program,name:loyalty.3_cabinets_plus_1_free
msgid "Buy 3 large cabinets, get one for free"
msgstr "대형 캐비닛 3개를 구입하시면 하나를 더 드립니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__buy_x_get_y
msgid "Buy X Get Y"
msgstr "X개 구매 시 Y개 무료 증정"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
msgid "Can not generate coupon, no program is set."
msgstr "쿠폰을 생성할 수 없습니다. 설정한 프로그램이 없습니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_update_balance_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Cancel"
msgstr "취소"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__card_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__card_id
msgid "Card"
msgstr "카드"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Card expires"
msgstr "만료된 카드입니다"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_category_id
msgid "Categories"
msgstr "카테고리"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__cheapest
msgid "Cheapest Product"
msgstr "최저가 상품"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__clear_wallet
msgid "Clear Wallet"
msgstr "지갑 초기화"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Clear all promo point(s)"
msgstr "전체 프로모션 포인트 초기화"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__code
msgid "Code"
msgstr "코드"

#. module: loyalty
#: model:loyalty.program,name:loyalty.10_percent_with_code
msgid "Code for 10% on orders"
msgstr "주문시 10% 코드"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__communication_plan_ids
msgid "Communication Plan"
msgstr "커뮤니케이션 계획"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Communications"
msgstr "연락"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__company_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__company_id
msgid "Company"
msgstr "회사"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
msgid "Compose Email"
msgstr "이메일 작성"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__rule_ids
msgid "Conditional rules"
msgstr "조건부 규칙"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Conditions"
msgstr "조건"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_update_balance_form
msgid "Confirm"
msgstr "승인"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__confirmation_message
msgid "Confirmation Message"
msgstr "확인 메시지"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Congratulations"
msgstr "축하합니다."

#. module: loyalty
#: model:ir.model,name:loyalty.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
msgid "Control panel buttons"
msgstr "제어판 버튼 "

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_ids
msgid "Coupon"
msgstr "쿠폰"

#. module: loyalty
#: model:ir.actions.report,name:loyalty.report_loyalty_card
msgid "Coupon Code"
msgstr "쿠폰 코드"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_count
msgid "Coupon Count"
msgstr "쿠폰 수"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Coupon point(s)"
msgstr "쿠폰 포인트"

#. module: loyalty
#: model:loyalty.program,portal_point_name:loyalty.10_percent_coupon
msgid "Coupon points"
msgstr "쿠폰 포인트"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Coupon value"
msgstr "쿠폰 값"

#. module: loyalty
#: model:mail.template,name:loyalty.mail_template_loyalty_card
msgid "Coupon: Coupon Information"
msgstr "쿠폰: 쿠폰 정보"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.actions.act_window,name:loyalty.loyalty_card_action
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__coupons
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Coupons"
msgstr "쿠폰"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_gift_ewallet_action
msgid "Create a new one from scratch, or use one of the templates below."
msgstr "처음부터 새로 생성하시거나 아래의 서식 중에서 선택하십시오."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_discount_loyalty_action
msgid "Create one from scratch, or use a templates below:"
msgstr "처음부터 새로 생성하거나 아래의 서식을 사용하십시오:"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
msgid "Create record"
msgstr "레코드 생성"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__create_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__create_uid
msgid "Created by"
msgstr "작성자"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__create_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__create_date
msgid "Created on"
msgstr "작성일자"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,portal_point_name:loyalty.3_cabinets_plus_1_free
msgid "Credit(s)"
msgstr "크레딧"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__currency_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__currency_id
msgid "Currency"
msgstr "통화"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__currency_symbol
msgid "Currency sign, to be used when printing amounts."
msgstr "통화 기호입니다. 금액을 인쇄할 때 사용됩니다."

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__both
msgid "Current & Future orders"
msgstr "현재 및 다음 주문"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__current
msgid "Current order"
msgstr "현재 주문"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__customer_tag_ids
msgid "Customer Tags"
msgstr "고객 태그"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__customer_ids
msgid "Customers"
msgstr "고객"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "DEMO_CODE"
msgstr "DEMO_CODE"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "DEMO_TEXT"
msgstr "DEMO_TEXT"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/controllers/portal.py:0
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_history_template
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_form
msgid "Date"
msgstr "날짜"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Define Discount codes on conditional rules then share it with your customers"
" for rewards."
msgstr "조건부 규칙에 할인 코드를 설정한 후 고객에게 리워드 지급 시 공유해주십시오."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/controllers/portal.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__description
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__description
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__description
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__description
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_history_template
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Description"
msgstr "설명"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Description on order"
msgstr "주문에 대한 설명"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__reward_type__discount
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Discount"
msgstr "할인"

#. module: loyalty
#: model:ir.actions.act_window,name:loyalty.loyalty_program_discount_loyalty_action
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Discount & Loyalty"
msgstr "할인 및 적립"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_applicability
msgid "Discount Applicability"
msgstr "할인 적용"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__promo_code
msgid "Discount Code"
msgstr "할인 코드"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_line_product_id
msgid "Discount Line Product"
msgstr "할인 적용 상품"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_mode
msgid "Discount Mode"
msgstr "할인 모드"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__payment_program_discount_product_id
msgid "Discount Product"
msgstr "할인 품목"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_domain
msgid "Discount Product Domain"
msgstr "할인 품목 범위"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__code
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "Discount code"
msgstr "할인 코드"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,portal_point_name:loyalty.10_percent_with_code
msgid "Discount point(s)"
msgstr "할인 포인트"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Discount product"
msgstr "할인 품목"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_category_id
msgid "Discounted Prod. Categories"
msgstr "할인 품목 카테고리"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_tag_id
msgid "Discounted Prod. Tag"
msgstr "할인 품목 태그"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_product_ids
msgid "Discounted Products"
msgstr "할인 품목"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Discounts"
msgstr "할인"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__display_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__display_name
msgid "Display Name"
msgstr "표시명"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Displayed as"
msgstr "표시명 지정"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Drive repeat purchases by sending a unique, single-use coupon code for the "
"next purchase when a customer buys something in your store."
msgstr ""
"고객이 매장에서 물품을 구매할 때, 다음 구매 시 사용할 수 있는 고유한 일회성 쿠폰 코드를 지급함으로써 반복하여 구매하도록 합니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Each rule can grant points to the customer he will be able to exchange "
"against rewards"
msgstr "각 규칙에서 고객에게 포인트를 부여하여 리워드로 교환할 수 있도록 합니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__mail_template_id
msgid "Email Template"
msgstr "이메일 서식"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__mail_template_id
msgid "Email template"
msgstr "이메일 서식"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__date_to
msgid "End date"
msgstr "종료일"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_update_balance_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Example: Gift for customer"
msgstr "예시: 고객용 선물"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__expiration_date
msgid "Expiration Date"
msgstr "만료일"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
msgid "Expiration date cannot be set on a loyalty card."
msgstr "포인트 카드의 유효 기간을 설정할 수 없습니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Fidelity Card"
msgstr "스탬프 카드"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Fidelity Cards"
msgstr "스탬프 카드"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Fill in your eWallet, to pay future orders"
msgstr "나중에 주문할 때 사용하시도록 전자 지갑을 충전해두세요"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__mode
msgid "For"
msgstr "다음"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "For all customers"
msgstr "모든 고객"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__reward_type__product
msgid "Free Product"
msgstr "무료 상품"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "Free Product - %s"
msgstr "무료 상품 - %s"

#. module: loyalty
#: model:loyalty.reward,description:loyalty.3_cabinets_plus_1_free_reward
msgid "Free Product - Large Cabinet"
msgstr "무료 상품 - 대형 캐비넷"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "Free Product - [%s]"
msgstr "무료 상품 - [%s]"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "Free product"
msgstr "무료 상품"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__applies_on__future
msgid "Future orders"
msgstr "다음 주문"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
#: model:ir.actions.act_window,name:loyalty.loyalty_generate_wizard_action
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Generate"
msgstr "생성"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Generate &amp; share coupon codes manually. It can be used in eCommerce, "
"Point of Sale or regular orders to claim the Reward. You can define "
"constraints on its usage through conditional rule."
msgstr ""
"수기로 쿠폰 코드를 생성하여 공유해보세요. 온라인 몰이나 POS 또는 일반 주문에서도 리워드 요청에 사용할 수 있습니다. 조건부 규칙으로"
" 사용에 대한 제한 설정을 할 수 있습니다."

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_generate_wizard
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate Coupons"
msgstr "쿠폰 생성"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate Gift Cards"
msgstr "기프트 카드 생성"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Generate and share unique coupons with your customers"
msgstr "고유한 쿠폰을 생성하고 고객과 공유합니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Generate eWallet"
msgstr "전자 지갑 생성"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
msgid "Generate {{props.context.program_item_name}}"
msgstr "{{props.context.program_item_name}} 생성"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Get 10% off on some products, with a code"
msgstr "코드를 사용하시면 일부 품목에 10% 할인이 적용됩니다"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_gift_ewallet_view_form
msgid "Gift &amp; Ewallet"
msgstr "사은품 및 전자 지갑"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.actions.report,name:loyalty.report_gift_card
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__gift_card
#: model:loyalty.reward,description:loyalty.gift_card_program_reward
#: model:product.template,name:loyalty.gift_card_product_50_product_template
msgid "Gift Card"
msgstr "기프트 카드"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Gift Card Products"
msgstr "기프트 카드 상품"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Gift Card value"
msgstr "기프트 카드 값"

#. module: loyalty
#: model:mail.template,name:loyalty.mail_template_gift_card
msgid "Gift Card: Gift Card Information"
msgstr "기프트 카드: 기프트 카드 정보"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:loyalty.program,name:loyalty.gift_card_program
msgid "Gift Cards"
msgstr "기프트 카드"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Gift Cards are created manually or automatically sent by email when the customer orders a gift card product.\n"
"                                    <br/>\n"
"                                    Then, Gift Cards can be used to pay orders."
msgstr ""
"기프트 카드를 수기로 생성하거나 고객이 기프트 카드 품목을 주문하면 이메일로 자동 발송합니다.\n"
"                                    <br/>\n"
"                                    그러면 기프트 카드를 사용하여 주문 결제를 할 수 있습니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
msgid "Gift For Customer"
msgstr "고객을 위한 선물"

#. module: loyalty
#: model:ir.actions.act_window,name:loyalty.loyalty_program_gift_ewallet_action
msgid "Gift cards & eWallet"
msgstr "기프트 카드 및 e월렛"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_card_update_balance.py:0
msgid "Gift for customer"
msgstr "고객을 위한 선물"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__points_granted
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "Grant"
msgstr "수여"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Grant 1 credit for each item bought then reward the customer with Y items in"
" exchange of X credits."
msgstr "구매 항목마다 1 크레딧을 부여한 후 고객에게 X 크레딧을 Y 항목으로 교환하여 리워드를 받을 수 있도록 합니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.gift_card_report
msgid "Here is your gift card!"
msgstr "고객님의 기프트 카드를 확인하세요!"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Here is your reward from"
msgstr "다음에 대한 리워드를 확인해보세요."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__history_ids
msgid "History"
msgstr "기록"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_form
msgid "History Lines"
msgstr "내역 명세"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_history
msgid "History for Loyalty cards and Ewallets"
msgstr "멤버십 카드 및 전자지갑에 대한 내역"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__id
msgid "ID"
msgstr "ID"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_error
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "If minimum"
msgstr "최소"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "In exchange of"
msgstr "교환 대상"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_search
msgid "Inactive"
msgstr "비활성"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
msgid "Invalid quantity."
msgstr "잘못된 수량입니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__is_global_discount
msgid "Is Global Discount"
msgstr "전체 할인입니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__is_nominative
msgid "Is Nominative"
msgstr "이름 형식입니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__is_payment_program
msgid "Is Payment Program"
msgstr "결제 프로그램입니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/controllers/portal.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__issued
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_history_template
msgid "Issued"
msgstr "발행"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__coupon_count_display
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_tree
msgid "Items"
msgstr "항목"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "John Doe"
msgstr "홍길동"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/js/portal/loyalty_card_dialog/loyalty_card_dialog.xml:0
msgid "Last Transactions"
msgstr "최근 거래"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__write_uid
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__write_date
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_mail_view_tree
msgid "Limit"
msgstr "한도"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__limit_usage
msgid "Limit Usage"
msgstr "사용 제한"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Logo"
msgstr "로고"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Loyalty Card"
msgstr "적립 카드"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.res_partner_form
msgid "Loyalty Cards"
msgstr "적립 카드"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_mail
msgid "Loyalty Communication"
msgstr "적립 커뮤니케이션"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "적립 쿠폰"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "로열티 프로그램"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_reward
msgid "Loyalty Reward"
msgstr "로열티 보상"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "로열티 규칙"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_history_template
msgid "Loyalty Transaction"
msgstr "포인트 거래"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Loyalty point(s)"
msgstr "적립 포인트"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__discount_max_amount
msgid "Max Discount"
msgstr "최대 할인"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__max_usage
msgid "Max Usage"
msgstr "최대 사용"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_program_check_max_usage
msgid "Max usage must be strictly positive if a limit is used."
msgstr "제한을 적용하는 경우, 최대 사용 항목은 반드시 양수여야 합니다."

#. module: loyalty
#: model:ir.model,name:loyalty.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "협력사 병합 마법사"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_ids
msgid "Messages"
msgstr "메시지"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_amount_tax_mode
msgid "Minimum Amount Tax Mode"
msgstr "최소 금액 세금 모드"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_amount
msgid "Minimum Purchase"
msgstr "최소 구매"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__minimum_qty
msgid "Minimum Quantity"
msgstr "최소 수량"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__multi_product
msgid "Multi Product"
msgstr "여러 개의 제품"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__new_balance
msgid "New Balance"
msgstr "새 잔액"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_card_update_balance.py:0
msgid "New Balance should be positive and different then old balance."
msgstr "새 잔액은 양수이고 이전 잔액과 달라야 합니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Next Order Coupon"
msgstr "다음 방문시 사용 가능한 쿠폰"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__next_order_coupons
msgid "Next Order Coupons"
msgstr "다음 방문시 사용 가능한 쿠폰"

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_card_action
msgid "No Coupons Found."
msgstr "쿠폰을 찾을 수 없습니다."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_gift_ewallet_action
msgid "No loyalty program found."
msgstr "적립 제도를 찾을 수 없습니다."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_program_discount_loyalty_action
msgid "No program found."
msgstr "프로그램을 찾을 수 없습니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Odoo"
msgstr "Odoo"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__order_id
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__order
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_history_template
msgid "Order"
msgstr "주문"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__order_model
msgid "Order Model"
msgstr "주문 모델"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__partner_id
msgid "Partner"
msgstr "협력사"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_form
msgid "Point(s)"
msgstr "포인트"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__points
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card_update_balance__old_balance
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__points
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Points"
msgstr "포인트"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__points_display
msgid "Points Display"
msgstr "포인트 표시"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Points Unit"
msgstr "포인트 단위"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__required_points
msgid "Points needed"
msgstr "필요한 포인트"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__points_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__portal_point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__point_name
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_name
msgid "Portal Point Name"
msgstr "포털 포인트 이름"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__portal_visible
msgid "Portal Visible"
msgstr "포털 표시"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__pricelist_ids
msgid "Pricelist"
msgstr "가격표"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_template
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_id
msgid "Product"
msgstr "품목"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_domain
msgid "Product Domain"
msgstr "품목 범위"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_tag_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_tag_id
msgid "Product Tag"
msgstr "품목 테그"

#. module: loyalty
#: model:ir.model,name:loyalty.model_product_product
msgid "Product Variant"
msgstr "세부 품목"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__payment_program_discount_product_id
msgid "Product used in the sales order to apply the discount."
msgstr "할인을 적용하기 위해 판매 주문에 사용된 상품."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__discount_line_product_id
msgid ""
"Product used in the sales order to apply the discount. Each reward has its "
"own product for reporting purpose"
msgstr "할인을 적용하기 위해서 판매 주문 시에 사용된 제품입니다. 리워드마다 목적으로 자체 상품이 있습니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__trigger_product_ids
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__product_ids
msgid "Products"
msgstr "품목"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__program_id
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__program_id
msgid "Program"
msgstr "프로그램"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__name
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Program Name"
msgstr "프로그램명"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__program_type
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__program_type
msgid "Program Type"
msgstr "프로그램 유형"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Program trigger"
msgstr "프로그램 작동"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Promo Code"
msgstr "프로모션 코드"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Promo point(s)"
msgstr "프로모션 포인트"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Promos"
msgstr "프로모션"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Promotional Program"
msgstr "다양한 프로모션"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__promotion
msgid "Promotions"
msgstr "프로모션"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__coupon_qty
msgid "Quantity"
msgstr "수량"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Quantity rewarded"
msgstr "리워드 수량"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "Quantity to generate"
msgstr "생성할 수량"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_amount
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Reward"
msgstr "보상"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_mode
msgid "Reward Point Mode"
msgstr "리워드 포인트 모드"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_domain
msgid "Reward Product Domain"
msgstr "리워드 품목 범위"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_qty
msgid "Reward Product Qty"
msgstr "리워드 품목 수량"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_uom_id
msgid "Reward Product Uom"
msgstr "리워드 품목 측정 단위"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_product_ids
msgid "Reward Products"
msgstr "리워드 품목"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__reward_type
msgid "Reward Type"
msgstr "보상 유형"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__reward_ids
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Rewards"
msgstr "보상"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_rule_reward_point_amount_positive
msgid "Rule points reward must be strictly positive."
msgstr "포인트 보상 규칙은 반드시 양수여야 합니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Rules & Rewards"
msgstr "규칙 및 리워드"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 오류"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_generate_wizard__mode__selected
msgid "Selected Customers"
msgstr "선택한 고객"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Sell Gift Cards, that allows to purchase products"
msgstr "품목을 구매할 수 있도록 기프트 카드 판매"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_view_tree
msgid "Send"
msgstr "보내기"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Send a coupon after an order, valid for next purchase"
msgstr "주문 후 다음 구매 시 사용할 수 있는 쿠폰 발송"

#. module: loyalty
#: model:mail.template,description:loyalty.mail_template_gift_card
msgid "Sent to customer who purchased a gift card"
msgstr "기프트 카드를 구매한 고객에게 발송"

#. module: loyalty
#: model:mail.template,description:loyalty.mail_template_loyalty_card
msgid "Sent to customer with coupon information"
msgstr "쿠폰 정보를 고객에게 발송"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__sequence
msgid "Sequence"
msgstr "순서"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"Set up conditional rules on the order that will give access to rewards for "
"customers"
msgstr "고객이 리워드를 받을 수 있는 주문에 대해 조건부 규칙을 설정합니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Show points Unit"
msgstr "포인트 단위 표시"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_reward__discount_applicability__specific
msgid "Specific Products"
msgstr "특정 제품"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__reward_point_split
msgid "Split per unit"
msgstr "단위 당 분할"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
msgid "Split per unit is not allowed for Loyalty and eWallet programs."
msgstr "적립 및 전자 지갑 프로그램에서는 단위 당 분할이 허용되지 않습니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__date_from
msgid "Start Date"
msgstr "시작일"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__currency_symbol
msgid "Symbol"
msgstr "통화 기호"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__tax_ids
msgid "Taxes"
msgstr "세금"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__tax_ids
msgid "Taxes to add on the discount line."
msgstr "할인 금액에 세금을 추가합니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Thank you,"
msgstr "감사합니다,"

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_discount_positive
msgid "The discount must be strictly positive."
msgstr "할인은 반드시 양수이어야 합니다."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__date_to
msgid "The end date is included in the validity period of this program"
msgstr "종료 날짜는 이 프로그램의 유효 기간에 포함됩니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid ""
"The loyalty program's currency must be the same as all it's pricelists ones."
msgstr "적립 프로그램의 통화는 모든 가격 목록의 통화와 일치해야 합니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
msgid "The promo code must be unique."
msgstr "프로모션 코드는 고유해야 합니다."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_required_points_positive
msgid "The required points for a reward must be strictly positive."
msgstr "리워드에 필요한 포인트는 반드시 양수이어야 합니다."

#. module: loyalty
#: model:ir.model.constraint,message:loyalty.constraint_loyalty_reward_product_qty_positive
msgid "The reward product quantity must be strictly positive."
msgstr "리워드 품목 수량은 반드시 양수이어야 합니다."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__date_from
msgid "The start date is included in the validity period of this program"
msgstr "시작 날짜는 이 프로그램의 유효 기간에 포함됩니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid ""
"The validity period's start date must be anterior or equal to its end date."
msgstr "유효 기간 중 시작 날짜는 종료 날짜보다 전이거나 같아야 합니다."

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/js/portal/loyalty_card_dialog/loyalty_card_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_history_template
msgid "There are currently no transaction lines for this card."
msgstr "현재 이 카드의 거래 항목이 없습니다."

#. module: loyalty
#: model_terms:ir.actions.act_window,help:loyalty.loyalty_card_action
msgid "There haven't been any coupons generated yet."
msgstr "아직 생성된 쿠폰이 없습니다."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__reward_product_ids
msgid "These are the products that can be claimed with this rule."
msgstr "이 규칙에서 신청할 수 있는 상품입니다."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_reward__discount_max_amount
msgid ""
"This is the max amount this reward may discount, leave to 0 for no limit."
msgstr "이 리워드 항목에서 할인받을 수 있는 최대 금액이며 제한하지 않으려면 0으로 두십시오."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_product.py:0
msgid ""
"This product may not be archived. It is being used for an active promotion "
"program."
msgstr "이 품목은 보관 처리 할 수 없습니다. 활성화되어 있는 프로모션 프로그램에 사용 중입니다."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_program__pricelist_ids
msgid "This program is specific to this pricelist set."
msgstr "이 프로그램은 이 가격표 세트에만 적용됩니다."

#. module: loyalty
#: model:product.template,name:loyalty.ewallet_product_50_product_template
msgid "Top-up eWallet"
msgstr "전자 지갑 충전"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__total_order_count
msgid "Total Order Count"
msgstr "총 주문 수"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_program__trigger
msgid "Trigger"
msgstr "자동 실행"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_form
msgid "Untaxed discount"
msgstr "면세 할인"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_card.py:0
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_update_balance_form
msgid "Update Balance"
msgstr "잔고 업데이트"

#. module: loyalty
#: model:ir.model,name:loyalty.model_loyalty_card_update_balance
msgid "Update Loyalty Card Points"
msgstr "로열티 카드 포인트 갱신"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__use_count
msgid "Use Count"
msgstr "사용 횟수"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__trigger__with_code
msgid "Use a code"
msgstr "코드 사용하기"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "Use points on"
msgstr "다음에 포인트 사용"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "Use this promo code before"
msgstr "먼저 이 프로모션 코드 사용하기"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/controllers/portal.py:0
#: model:ir.model.fields,field_description:loyalty.field_loyalty_history__used
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_history_template
msgid "Used"
msgstr "사용됨"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_reward__user_has_debug
#: model:ir.model.fields,field_description:loyalty.field_loyalty_rule__user_has_debug
msgid "User Has Debug"
msgstr "사용자 디버그"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__valid_until
msgid "Valid Until"
msgstr "유효기간"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/js/portal/loyalty_card_dialog/loyalty_card_dialog.xml:0
msgid "Valid until"
msgstr "유효 기간"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_card__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_card__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_mail__trigger
msgid "When"
msgstr "기간"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_mail__trigger__points_reach
msgid "When Reaching"
msgstr "연락 시"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"When customers make an order, they accumulate points they can exchange for "
"rewards on the current order or on a future one."
msgstr "고객이 주문할 때 현재 주문이나 다음 주문에 대한 리워드로 교환할 수 있는 포인트가 적립됩니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"When generating coupon, you can define a specific points value that can be "
"exchanged for rewards."
msgstr "쿠폰을 생성할 때 리워드로 교환할 수 있는 특정 포인트 값을 설정할 수 있습니다."

#. module: loyalty
#: model:ir.model.fields,help:loyalty.field_loyalty_rule__reward_point_split
msgid ""
"Whether to separate reward coupons per matched unit, only applies to "
"'future' programs and trigger mode per money spent or unit paid.."
msgstr ""
"일치하는 단위 별로 리워드 쿠폰을 분리할지 여부는 '다음' 프로그램에만 적용할 수 있으며 지출 또는 지불 단위 당으로 모드를 작동 시킬 "
"수 있습니다."

#. module: loyalty
#: model:ir.model.fields,field_description:loyalty.field_loyalty_generate_wizard__will_send_mail
msgid "Will Send Mail"
msgstr "메일을 전송해드리겠습니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "Win points with each purchase, and claim gifts"
msgstr "구매할 때마다 포인트를 적립하여 사은품을 받으세요"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__mode__with_code
msgid "With a promotion code"
msgstr "프로모션 코드 포함"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_card_update_balance_form
msgid "You are about to change the balance of the card"
msgstr "카드 잔액이 변경됩니다."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "You can not delete a program in an active state"
msgstr "활성화 상태의 프로그램은 삭제할 수 없습니다"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/product_product.py:0
#: code:addons/loyalty/models/product_template.py:0
msgid ""
"You cannot delete %(name)s as it is used in 'Coupons & Loyalty'. Please "
"archive it instead."
msgstr "'쿠폰 및 적립'에서 사용 중이므로 %(name)s 항목을 삭제할 수 없습니다. 대신 보관 처리하십시오."

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/wizard/loyalty_generate_wizard.py:0
msgid ""
"You're about to generate %(program_type)s with a value of %(value)s for "
"%(customer_number)i customers"
msgstr ""
"%(program_type)s을 %(value)s의 값으로 %(customer_number)i 고객에 대해 생성하려고 합니다."

#. module: loyalty
#: model:mail.template,subject:loyalty.mail_template_gift_card
msgid "Your Gift Card at {{ object.company_id.name }}"
msgstr "고객님의 기프트 카드는 다음에 해당됩니다 {{ object.company_id.name }}"

#. module: loyalty
#: model:mail.template,subject:loyalty.mail_template_loyalty_card
msgid "Your reward coupon from {{ object.program_id.company_id.name }} "
msgstr "{{ object.program_id.company_id.name }}님의 리워드 쿠폰"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "all"
msgstr "전체"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "discount"
msgstr "할인"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "e.g. 10% discount on laptops"
msgstr "예: 노트북 10% 할인"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
#: code:addons/loyalty/models/loyalty_reward.py:0
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_program__program_type__ewallet
msgid "eWallet"
msgstr "e월렛"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "eWallet Products"
msgstr "전자 지갑 품목"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_generate_wizard_view_form
msgid "eWallet value"
msgstr "전자 지갑 금액"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_program.py:0
msgid "eWallets"
msgstr "e월렛"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid ""
"eWallets are created manually or automatically when the customer orders a eWallet product.\n"
"                                    <br/>\n"
"                                    Then, eWallets are proposed during the checkout, to pay orders."
msgstr ""
"고객이 전자 지급 품목을 주문할 경우 수동이나 자동으로 전자 지갑이 생성됩니다.\n"
"                                     <br/>\n"
"                                     그 후 결제 단계에서 주문 결제용으로 전자 지갑 사용이 제안됩니다."

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "item(s) bought"
msgstr "구매한 항목"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "loyalty Reward"
msgstr "적립 보상"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "on the cheapest product"
msgstr "가장 저렴한 상품에"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_report
msgid "on your next order"
msgstr "다음 주문에서"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_reward_view_kanban
msgid "on your order"
msgstr "주문 시"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
msgid "per %s spent"
msgstr "지출한 %s별"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
msgid "per order"
msgstr "주문별"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_rule.py:0
msgid "per unit paid"
msgstr "지불 단위별"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "specific products"
msgstr "특정 품목"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "spent"
msgstr "발송"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__minimum_amount_tax_mode__excl
msgid "tax excluded"
msgstr "세금 제외"

#. module: loyalty
#: model:ir.model.fields.selection,name:loyalty.selection__loyalty_rule__minimum_amount_tax_mode__incl
msgid "tax included"
msgstr "세금 포함"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "the cheapest product"
msgstr "최저가 품목"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_rule_view_kanban
msgid "the value of the coupon"
msgstr "쿠폰의 가치"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "to"
msgstr "종료"

#. module: loyalty
#: model_terms:ir.ui.view,arch_db:loyalty.loyalty_program_view_form
msgid "usages"
msgstr "사용"

#. module: loyalty
#. odoo-python
#: code:addons/loyalty/models/loyalty_reward.py:0
msgid "your order"
msgstr "내 주문"

#. module: loyalty
#. odoo-javascript
#: code:addons/loyalty/static/src/xml/loyalty_templates.xml:0
msgid "{{loyalty_el_title}}"
msgstr "{{loyalty_el_title}}"
