<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Basic product for gift card program -->
    <record id="gift_card_product_50" model="product.product">
        <field name="name">Gift Card</field>
        <field name="list_price">50</field>
        <field name="type">service</field>
        <field name="purchase_ok" eval="False"/>
        <field name="image_1920" type="base64" file="loyalty/static/img/gift_card.png"/>
    </record>
    <!-- Basic product for eWallet programs -->
    <record id="ewallet_product_50" model="product.product">
        <field name="name">Top-up eWallet</field>
        <field name="list_price">50</field>
        <field name="type">service</field>
        <field name="purchase_ok" eval="False"/>
    </record>

    <data noupdate="1">
        <record forcecreate="0" id="config_online_sync_proxy_mode" model="ir.config_parameter">
            <field name="key">loyalty.compute_all_discount_product_ids</field>
            <field name="value">False</field>
        </record>
    </data>

    <!-- Gift Cards -->
    <record id="gift_card_program" model="loyalty.program">
        <field name="name">Gift Cards</field>
        <field name="program_type">gift_card</field>
        <field name="applies_on">future</field>
        <field name="trigger">auto</field>
        <field name="portal_visible">True</field>
        <field name="portal_point_name">$</field>
        <field name="mail_template_id" ref="loyalty.mail_template_gift_card"/>
    </record>
    <record id="gift_card_program_reward" model="loyalty.reward">
        <field name="reward_type">discount</field>
        <field name="discount_mode">per_point</field>
        <field name="discount">1</field>
        <field name="discount_applicability">order</field>
        <field name="required_points">1</field>
        <field name="program_id" ref="loyalty.gift_card_program"/>
    </record>
    <record id="gift_card_program_rule" model="loyalty.rule">
        <field name="reward_point_amount">1</field>
        <field name="reward_point_mode">money</field>
        <field name="reward_point_split">True</field>
        <field name="product_ids" eval="[(4, ref('loyalty.gift_card_product_50'))]"/>
        <field name="program_id" ref="loyalty.gift_card_program"/>
    </record>
</odoo>
