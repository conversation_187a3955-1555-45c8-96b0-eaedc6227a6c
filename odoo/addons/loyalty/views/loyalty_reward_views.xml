<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="loyalty_reward_view_form" model="ir.ui.view">
        <field name="name">loyalty.reward.view.form</field>
        <field name="model">loyalty.reward</field>
        <field name="arch" type="xml">
            <form>
                <field name="program_type" invisible="1"/>
                <field name="user_has_debug" invisible="1"/>
                <field name="multi_product" invisible="1"/>
                <field name="reward_product_uom_id" invisible="1"/>
                <field name="reward_product_ids" invisible="1"/>
                <field name="all_discount_product_ids" invisible="1"/>
                <sheet>
                    <group>
                        <group string="Reward" name="reward_type_group" invisible="program_type in ('ewallet','gift_card')">
                            <field name="reward_type" widget="selection" force_save="1" readonly="program_type == 'buy_x_get_y'"/>
                            <label for="discount" invisible="reward_type != 'discount'"/>
                            <div class="d-flex flex-row" invisible="reward_type != 'discount'">
                                <field
                                    name="discount"
                                    class="oe_inline me-1 text-end"
                                    style="max-width: 4rem;"
                                />
                                <field name="discount_mode" no_label="1" class="w-auto me-1"/>
                                <span>on</span>
                            </div>
                            <label for="discount_applicability" string="" invisible="reward_type != 'discount'"/>
                            <field name="discount_applicability" nolabel="1" widget="radio" invisible="reward_type != 'discount'"/>
                        </group>

                        <group string="Among" invisible="reward_type != 'product'">
                            <field name="reward_product_qty" string="Quantity rewarded"/>
                            <field name="reward_product_id" required="reward_type == 'product' and not reward_product_ids"/>
                            <field name="reward_product_tag_id" required="reward_type == 'product' and not reward_product_ids"/>
                        </group>

                        <group string="Discount" invisible="reward_type != 'discount' or program_type in ('gift_card','ewallet')">
                            <field name="discount_max_amount"/>
                            <field
                                name="tax_ids"
                                placeholder="Untaxed discount"
                                widget="many2many_tags"
                                options="{'no_create': True}"
                                invisible="discount_applicability != 'order' or
                                    discount_mode not in ('per_order','per_point')"
                            />
                            <field name="discount_product_domain" groups="base.group_no_one" widget="domain" options="{'model': 'product.product', 'in_dialog': true}" invisible="discount_applicability != 'specific'"/>
                            <field name="discount_product_ids" widget="many2many_tags" invisible="discount_applicability != 'specific'"/>
                            <field name="discount_product_category_id" invisible="discount_applicability != 'specific'"/>
                            <field name="discount_product_tag_id" invisible="discount_applicability != 'specific'"/>
                        </group>
                    </group>
                    <group string="Points" invisible="not user_has_debug and program_type not in ('loyalty', 'buy_x_get_y')">
                        <group>
                            <label for="required_points" string="In exchange of"/>
                            <div class="o_row">
                                <field
                                    name="required_points"
                                    class="oe_edit_only col-2 oe_inline text-end pe-2"
                                    style="max-width: 4rem;"
                                />
                                <field name="point_name" no_label="1"/>
                                <span invisible="not clear_wallet"> (or more)</span>
                            </div>
                            <label for="clear_wallet" string="Clear all promo point(s)" invisible="(not user_has_debug and program_type in ('loyalty', 'buy_x_get_y')) or program_type in ('gift_card', 'ewallet')"/>
                            <div class="o_row" invisible="(not user_has_debug and program_type in ('loyalty', 'buy_x_get_y')) or program_type in ('gift_card', 'ewallet')">
                                <field name="clear_wallet"/>
                            </div>
                        </group>
                    </group>
                    <group>
                        <field name="description" string="Description on order"/>
                        <field name="discount_line_product_id" string="Discount product" groups="base.group_no_one"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="loyalty_reward_view_kanban" model="ir.ui.view">
        <field name="name">loyalty.reward.view.kanban</field>
        <field name="model">loyalty.reward</field>
        <field name="arch" type="xml">
            <kanban>
                <field name="company_id"/>
                <field name="currency_id"/>
                <field name="reward_type"/>
                <field name="discount_applicability"/>
                <field name="clear_wallet"/>
                <field name="program_type"/>
                <field name="user_has_debug"/>
                <templates>
                    <t t-name="card" class="flex-row">
                        <div class="mw-75 flex-grow-1" name="reward_info">
                            <t t-if="record.reward_type.raw_value === 'discount'">

                                <t t-if="record.discount">
                                    <field name="discount"/><field name="discount_mode"/> discount <t t-if="record.discount_max_amount.raw_value > 0">( Max <field name="discount_max_amount"/> )</t>
                                </t>

                                <t t-if="record.discount_applicability.raw_value === 'specific'">

                                    <div class="fw-bold text-decoration-underline mt-3">Applied to:</div>

                                    <div t-if="record.discount_product_ids.raw_value.length > 0" class="d-flex">
                                        <i class="fa fa-cube fa-fw" title="Products"/> <field name="discount_product_ids" widget="many2many_tags"/>
                                    </div>
                                    <div t-if="record.discount_product_category_id.raw_value" class="d-flex">
                                        <i class="fa fa-cubes fa-fw" title="Product Categories"/> <field name="discount_product_category_id"/>
                                    </div>
                                    <div t-if="record.discount_product_tag_id.raw_value" class="d-flex">
                                        <i class="fa fa-tags fa-fw" title="Product Tags"/> <field name="discount_product_tag_id"/>
                                    </div>
                                    <div t-if="record.discount_product_domain.raw_value &amp;&amp; record.discount_product_domain.raw_value !== '[]'" groups="base.group_no_one" class="d-flex">
                                        <i class="fa fa-search fa-fw" title="Product Domain"/> <field name="discount_product_domain"/>
                                    </div>
                                </t>

                                <t t-elif="record.discount_applicability.raw_value === 'cheapest'">
                                    on the cheapest product
                                </t>

                                <t t-elif="record.discount_applicability.raw_value === 'order'">
                                    on your order
                                </t>
                            </t>

                            <t t-elif="record.reward_type.raw_value === 'product'">
                                <div class="mb-3">Free product</div>
                                <div t-if="record.reward_product_id.raw_value" class="d-flex">
                                    <i class="fa fa-cube fa-fw" title="Product Domain"/><field name="reward_product_id" class="me-1"/> <t t-if="record.reward_product_qty.raw_value > 1"> x <field name="reward_product_qty" class="ms-1"/></t>
                                </div>
                                <div t-if="record.reward_product_tag_id.raw_value" class="d-flex">
                                    <i class="fa fa-tags fa-fw" title="Product Tags"/> <field name="reward_product_tag_id"/>
                                </div>
                            </t>
                        </div>

                        <div class="o_loyalty_kanban_card_right float-end text-muted" invisible="not user_has_debug and program_type not in ('loyalty', 'buy_x_get_y')">
                            <div class="fw-bold text-decoration-underline">In exchange of</div>
                            <t t-if="record.clear_wallet.raw_value">
                                all <field name="point_name"/> (if at least <field name="required_points"/> <field name="point_name"/>)
                            </t>
                            <t t-else="">
                                <field name="required_points"/> <field name="point_name"/>
                            </t>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
</odoo>
