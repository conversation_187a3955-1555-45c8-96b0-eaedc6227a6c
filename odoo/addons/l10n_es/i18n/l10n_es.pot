# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_es
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~17.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-20 08:09+0000\n"
"PO-Revision-Date: 2023-03-20 08:09+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_es
#: model_terms:ir.ui.view,arch_db:l10n_es.res_config_settings_view_form
msgid "Above this limit the simplified invoice won't be made"
msgstr ""

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_7
#: model:account.report.line,name:l10n_es.mod_390_title_56
msgid "Actividades con regímenes de deducción diferenciados"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_16
msgid "Additional information"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_8
msgid "Adquisiciones intracomunitarias de bienes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_9
msgid "Adquisiciones intracomunitarias de servicios"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_17
msgid ""
"Amount of sale of goods and services to which special regime of the cash "
"criterion ha dbeen applied would have been accrued in accordance with the "
"general accrual rule contained in art. 75 LIVA"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_19
msgid ""
"Amount of the purchases of goods and services to which the special regime of"
" cash criterion is applicable or affected"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_3
msgid "Amount of withholdings"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e1
msgid "Art. 20"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e2
msgid "Art. 21"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e3
msgid "Art. 22"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e4
msgid "Art. 23 y 24"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e5
msgid "Art. 25"
msgstr ""

#. module: l10n_es
#: model:account.report.column,name:l10n_es.mod_111_column
#: model:account.report.column,name:l10n_es.mod_115_column
#: model:account.report.column,name:l10n_es.mod_303_column
#: model:account.report.column,name:l10n_es.mod_390_column_sect1
#: model:account.report.column,name:l10n_es.mod_390_column_sect2
#: model:account.report.column,name:l10n_es.mod_390_column_sect3
#: model:account.report.column,name:l10n_es.mod_390_column_sect4
#: model:account.report.column,name:l10n_es.mod_390_column_sect5
#: model:account.report.column,name:l10n_es.mod_390_column_sect6
#: model:account.report.column,name:l10n_es.mod_390_column_sect7
msgid "Balance"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__l10n_es_bien_inversion
msgid "Bien de Inversion"
msgstr ""

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_common.py:0
msgid "Common"
msgstr ""

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_61
#: model:account.report.line,name:l10n_es.mod_390_title_67
#: model:account.report.line,name:l10n_es.mod_390_title_73
msgid ""
"Compensaciones en régimen especial de la agricultura, ganadería y pesca"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_41
msgid "Compensación en régimen especial de la agricultura, ganaderia y pesca"
msgstr ""

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_full.py:0
msgid "Complete (2008)"
msgstr ""

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_13
msgid ""
"Considerations for the assignment of image rights: income payments on "
"account provided for article 92.8 of the Tax Law"
msgstr ""

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_coop_full.py:0
msgid "Cooperatives - Complete (2008)"
msgstr ""

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_coop_pymes.py:0
msgid "Cooperatives - SMEs (2008)"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_42
msgid ""
"Cuotas deducibles en virtud de resolución administrativa o sentencia firmes "
"con tipos no vigentes"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__dua
msgid "DUA"
msgstr ""

#. module: l10n_es
#: model:product.template,name:l10n_es.product_dua_valuation_10_product_template
msgid "DUA VAT Valuation 10%"
msgstr ""

#. module: l10n_es
#: model:product.template,name:l10n_es.product_dua_valuation_21_product_template
msgid "DUA VAT Valuation 21%"
msgstr ""

#. module: l10n_es
#: model:product.template,name:l10n_es.product_dua_valuation_4_product_template
msgid "DUA VAT Valuation 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_1
msgid "Employment income"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_53
msgid ""
"Exclusivamente para aquellos sujetos pasivos acogidos al régimen especial "
"del criterio de caja y para aquéllos que sean destinatarios de operaciones "
"afectadas por el mismo"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__l10n_es_exempt_reason
msgid "Exempt Reason (Spain)"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__exento
msgid "Exento"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_12
msgid "For contributions supported in import of investment goods"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_11
msgid "For contributions supported in imports of current goods"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_10
msgid ""
"For contributions supported in internal operations with investment goods"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_9
msgid "For contributions supported in the current internal operations"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_1
msgid "General regime"
msgstr ""

#. module: l10n_es
#: model_terms:ir.ui.view,arch_db:l10n_es.report_invoice_document
msgid "ID:"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_2
#: model:account.report.line,name:l10n_es.mod_390_title_19
msgid "IVA deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_60
#: model:account.report.line,name:l10n_es.mod_390_title_66
#: model:account.report.line,name:l10n_es.mod_390_title_72
msgid "IVA deducible en adquisiciones intracomunitarias"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_35
msgid "IVA deducible en adquisiciones intracomunitarias de bienes corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_37
msgid ""
"IVA deducible en adquisiciones intracomunitarias de bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_39
msgid "IVA deducible en adquisiciones intracomunitarias de servicios"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_59
#: model:account.report.line,name:l10n_es.mod_390_title_65
#: model:account.report.line,name:l10n_es.mod_390_title_71
msgid "IVA deducible en importaciones"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_31
msgid "IVA deducible en importaciones de bienes corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_33
msgid "IVA deducible en importaciones de bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_58
#: model:account.report.line,name:l10n_es.mod_390_title_64
#: model:account.report.line,name:l10n_es.mod_390_title_70
msgid "IVA deducible en operaciones interiores"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_26
msgid "IVA deducible en operaciones interiores de bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_21
msgid ""
"IVA deducible en operaciones interiores de bienes y servicios corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_28
msgid "IVA deducible en operaciones intragrupo de bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_23
msgid ""
"IVA deducible en operaciones intragrupo de bienes y servicios corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_57
msgid "IVA deducible: Grupo 1"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_63
msgid "IVA deducible: Grupo 2"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_69
msgid "IVA deducible: Grupo 3"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_1
#: model:account.report.line,name:l10n_es.mod_390_title_2
msgid "IVA devengado"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_10
msgid "IVA devengado en otros supuestos de inversión del sujeto pasivo"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__ignore
msgid "Ignore even the base amount"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_30
msgid "Importaciones y adquisiciones intracomunitarias de bienes y servicios"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_55
msgid ""
"Importe de las adquisiciones de bienes y servicios a las que sea de "
"aplicación o afecte el régimen especial del criterio de caja conforme a la "
"regla general de devengo contenida en el art. 75 LIVA"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_54
msgid ""
"Importes de las entregas de bienes y prestaciones de servicios a las que "
"habiéndoles sido aplicado el régimen especial del criterio de caja hubieran "
"resultado devengadas conforme a la regla general de devengo contenida en el "
"art. 75 LIVA"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_13
msgid "In intra-community purchases of current goods and services"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_14
msgid "In intra-community purchases of investment goods"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_4
msgid "Income from economic activities"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_6
msgid "Income in kind"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_3
msgid "Intra-community purchases of goods and services"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_bank_statement_line__l10n_es_is_simplified
#: model:ir.model.fields,field_description:l10n_es.field_account_move__l10n_es_is_simplified
msgid "Is Simplified"
msgstr ""

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_11
msgid "Modificación de bases y cuotas"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_12
msgid "Modificación de bases y cuotas de operaciones intragrupo"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_13
msgid ""
"Modificación de bases y cuotas por auto de declaración de concurso de "
"acreedores"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_16
msgid "Modificación recargo equivalencia"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_17
msgid ""
"Modificación recargo equivalencia por auto de declaraciónde concurso de "
"acreedores"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_5
msgid "Modification bases and contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_7
msgid "Modification bases and contributions of the equivalence surcharge"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_2
#: model:account.report.line,name:l10n_es.mod_111_title_5
msgid "Monetary income"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_11
msgid "Monetary perceptions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_8
msgid "Monetary prizes"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__no_deducible
msgid "No Deducible"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__no_sujeto
msgid "No Sujeto"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__no_sujeto_loc
msgid "No Sujeto por reglas de Localization"
msgstr ""

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_assec.py:0
msgid "Non-profit entities (2008)"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_6
#: model:account.report.line,name:l10n_es.mod_390_title_51
msgid "Operaciones específicas"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_20
msgid "Operaciones interiores corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_25
msgid "Operaciones interiores de bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_4
msgid "Operaciones intragrupo"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_50
#: model:account.report.line,name:l10n_es.mod_390_title_52
msgid "Operaciones realizadas en el ejercicio"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_4
msgid "Other operations with reverse charge (except intracom. adq.)"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_exempt_reason__e6
msgid "Otros"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields,help:l10n_es.field_res_company__l10n_es_simplified_invoice_limit
#: model:ir.model.fields,help:l10n_es.field_res_config_settings__l10n_es_simplified_invoice_limit
msgid ""
"Over this amount is not legally possible to create a simplified invoice"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_10
msgid ""
"Patrimonial gains derived from the forest exploitation of the neighbors in "
"public forests"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_12
msgid "Perceptions in kind"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_47
msgid "Períodos que no tributan en Régimen especial del grupo de entidades"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_48
msgid "Períodos que tributan en Régimen especial del grupo de entidades"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_7
msgid "Prizes for participation in games"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_9
msgid "Prizes in kind"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__recargo
msgid "Recargo de Equivalencia"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_15
msgid "Recargo de equivalencia"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_43
#: model:account.report.line,name:l10n_es.mod_390_title_62
#: model:account.report.line,name:l10n_es.mod_390_title_68
#: model:account.report.line,name:l10n_es.mod_390_title_74
msgid "Rectificación de deducciones"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_44
msgid "Rectificación de deducciones por operaciones intragrupo"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_20
msgid "Rectification"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_15
msgid "Rectification of deductions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_18
msgid "Result"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_4
#: model:account.report.line,name:l10n_es.mod_390_title_46
msgid "Resultado de las liquidaciones"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_3
msgid "Resultado liquidación anual"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_45
msgid ""
"Resultado liquidación anual (Sólo para sujetos pasivos que tributan "
"exclusivamente en territorio común)"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__retencion
msgid "Retencion"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_7
msgid "Régimen especial de agencias de viaje"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_6
msgid ""
"Régimen especial de bienes usados, objetos de arte, antigüedades y objetos "
"de colección"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_5
msgid "Régimen especial del criterio de caja"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_3
msgid "Régimen ordinario"
msgstr ""

#. module: l10n_es
#. odoo-python
#: code:addons/l10n_es/models/template_es_pymes.py:0
msgid "SMEs (2008)"
msgstr ""

#. module: l10n_es
#: model_terms:ir.ui.view,arch_db:l10n_es.res_config_settings_view_form
msgid "Simplified Invoice Limit"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_res_company__l10n_es_simplified_invoice_limit
#: model:ir.model.fields,field_description:l10n_es.field_res_config_settings__l10n_es_simplified_invoice_limit
msgid "Simplified Invoice limit amount"
msgstr ""

#. module: l10n_es
#: model_terms:ir.ui.view,arch_db:l10n_es.res_config_settings_view_form
msgid "Spain Localization"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__sujeto
msgid "Sujeto"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__sujeto_agricultura
msgid "Sujeto Agricultura"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields.selection,name:l10n_es.selection__account_tax__l10n_es_type__sujeto_isp
msgid "Sujeto ISP"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_6
msgid "Surcharge equivalence"
msgstr ""

#. module: l10n_es
#: model:ir.model,name:l10n_es.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_303
msgid "Tax Report (Mod 303)"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390
msgid "Tax Report (Mod 390)"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_111
msgid "Tax Report(Mod 111)"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_115
msgid "Tax Report(Mod 115)"
msgstr ""

#. module: l10n_es
#: model:ir.model.fields,field_description:l10n_es.field_account_tax__l10n_es_type
msgid "Tax Type (Spain)"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_36
msgid ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de bienes corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_38
msgid ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_40
msgid ""
"Total bases imponibles y cuotas deducibles en adquisiciones "
"intracomunitarias de servicios"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_32
msgid ""
"Total bases imponibles y cuotas deducibles en importaciones de bienes "
"corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_34
msgid ""
"Total bases imponibles y cuotas deducibles en importaciones de bienes de "
"inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_27
msgid ""
"Total bases imponibles y cuotas deducibles en operaciones interiores de "
"bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_22
msgid ""
"Total bases imponibles y cuotas deducibles en operaciones interiores de "
"bienes y servicios corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_29
msgid ""
"Total bases imponibles y cuotas deducibles en operaciones intragrupo de "
"bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_24
msgid ""
"Total bases imponibles y cuotas deducibles en operaciones intragrupo de "
"bienes y servicios corrientes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_14
msgid "Total bases y cuotas IVA"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_title_18
msgid "Total cuotas IVA y recargo de equivalencia"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_title_14
#: model:account.report.line,name:l10n_es.mod_115_title_2
msgid "Total liquidation"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_2
msgid "VAT accrued"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_title_8
msgid "VAT receivable"
msgstr ""

#. module: l10n_es
#: model:account.report,name:l10n_es.mod_390_section_5
#: model:account.report.line,name:l10n_es.mod_390_title_49
msgid "Volumen de operaciones"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_115_title_1
msgid "Withholdings and payments on account"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_01
msgid "[01] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_01
msgid "[01] Base taxable 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_01
#: model:account.report.line,name:l10n_es.mod_115_casilla_01
msgid "[01] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_02
msgid "[02] Amount of perceptions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_115_casilla_02
msgid "[02] Base withholdings"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_02
msgid "[02] Cuota devengada 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_03
msgid "[03] Amount of withholdings"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_03
msgid "[03] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_03
msgid "[03] Contribution 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_115_casilla_03
msgid "[03] Withholdings"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_04
msgid "[04] Base taxable 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_04
msgid "[04] Cuota devengada 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_04
msgid "[04] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_115_casilla_04
msgid "[04] Previous results to report"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_05
msgid "[05] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_115_casilla_05
msgid "[05] Result to report"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_05
msgid "[05] Value of perceptions in kind"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_06
msgid "[06] Amount of income on account"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_06
msgid "[06] Contribution 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_06
msgid "[06] Cuota devengada 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_07
msgid "[07] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_07
msgid "[07] Base taxable 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_07
msgid "[07] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_08
msgid "[08] Amount of perceptions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_08
msgid "[08] Cuota devengada 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_09
msgid "[09] Amount of withholdings"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_09
msgid "[09] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_09
msgid "[09] Contribution 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_100
msgid "[100] Operaciones en régimen simplificado"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_101
msgid ""
"[101] Operaciones en régimen especial de la agricultura, ganadería y pesca"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_102
msgid ""
"[102] Operaciones realizadas por sujetos pasivos acogidos al régimen "
"especial del recargo de equivalencia"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_103
msgid "[103] Entregas intracomunitarias exentas"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_104
msgid ""
"[104] Exportaciones y otras operaciones exentas con derecho a deducción"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_105
msgid "[105] Operaciones exentas sin derecho a deducción"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_106
msgid ""
"[106] Entregas de bienes inmuebles, operaciones fi nancieras y relativas al "
"oro de inversión no habituales"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_107
msgid "[107] Entregas de bienes de inversión"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_108
msgid ""
"[108] Exclusively for certain cases of corrective self-assessment due to a "
"discrepancy in administrative criteria that should not be included in other "
"boxes. Other adjustments"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_108
msgid "[108] Total volumen de operaciones (Art. 121 Ley IVA)"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_109
msgid "[109] Adquisiciones intracomunitarias exentas"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_109
msgid ""
"[109] Refunds agreed by the Tax Agency as a consequence of the processing of"
" previous self-assessments corresponding to the year and period of the self-"
"assessment"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_10
msgid "[10] Base taxable"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_10
msgid "[10] Cuota devengada 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_10
msgid "[10] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_110
msgid "[110] Contributions to be compensated pending from previous periods"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_110
msgid ""
"[110] Operaciones no sujetas por reglas de localización o con inversión del "
"sujeto pasivo"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_111
msgid ""
"[111] Operaciones sujetas y no exentas que originan el derecho a la "
"devolución mensual"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_111
msgid "[111] Rectification - Amount"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_113
msgid ""
"[113] Entregas interiores de bienes devengadas por inversión del sujeto "
"pasivo como consecuencia de operaciones triangulares"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_11
msgid "[11] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_11
msgid "[11] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_11
msgid "[11] Value of perceptions in kind"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_120
msgid ""
"[120] Operations not subject to location rules (excepted those included in "
"box 123)"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_122
msgid "[122] Operations subject to reverse charge"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_123
msgid ""
"[123] Operation not subjected to rule of location covered by the special "
"one-stop shop regime"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_124
msgid ""
"[124] Operations subjected and covered by special regime of one-stop shop"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_125
msgid "[125] Operaciones sujetas con inversión del sujeto pasivo"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_126
msgid ""
"[126] OSS. Operaciones no sujetas por reglas de localización acogidas a la "
"OSS"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_127
msgid "[127] Operaciones sujetas y acogidas a la OSS"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_128
msgid ""
"[128] Operaciones intragrupo valoradas conforme a lo dispuesto en los arts. "
"78 y 79 LIVA"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_12
msgid "[12] Amount of income on account"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_12
msgid "[12] Base taxable"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_12
msgid "[12] Cuota devengada 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_139
msgid "[139] Bienes y servicios corrientes : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_13
msgid "[13] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_13
msgid "[13] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_13
msgid "[13] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_140
msgid "[140] Bienes y servicios corrientes: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_141
msgid "[141] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_142
msgid "[142] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_143
msgid "[143] Bienes corrientes : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_144
msgid "[144] Bienes corrientes: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_145
msgid "[145] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_146
msgid "[146] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_147
msgid "[147] Bienes corrientes y servicios: base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_148
msgid "[148] Bienes corrientes y servicios: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_149
msgid "[149] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_14
msgid "[14] Amount of perceptions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_14
msgid "[14] Base taxable"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_14
msgid "[14] Cuota devengada 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_150
msgid "[150] Base taxable 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_150
msgid "[150] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_151
msgid "[151] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_152
msgid "[152] Contribution 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_152
msgid "[152] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_153
msgid "[153] Base taxable 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_153
msgid "[153] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_154
msgid "[154] Regularización de bienes de inversión : cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_155
msgid "[155] Contributions 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_155
msgid "[155] Suma de deducciones"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_156
msgid "[156] Base taxable 1.75%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_156
msgid "[156] Bienes y servicios corrientes : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_157
msgid "[157] Bienes y servicios corrientes: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_158
msgid "[158] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_158
msgid "[158] Contributions 1.75%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_159
msgid "[159] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_15
msgid "[15] Amount of withholdings"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_15
msgid "[15] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_160
msgid "[160] Bienes corrientes : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_161
msgid "[161] Bienes corrientes: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_162
msgid "[162] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_163
msgid "[163] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_164
msgid "[164] Bienes corrientes y servicios: base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_165
msgid "[165] Base taxable 2%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_165
msgid "[165] Bienes corrientes y servicios: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_166
msgid "[166] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_167
msgid "[167] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_167
msgid "[167] Contributions 2%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_168
msgid "[168] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_168
msgid "[168] Base taxable 0.26%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_169
msgid "[169] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_16
msgid "[16] Base taxable 1%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_16
msgid "[16] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_170
msgid "[170] Contribution 0.26%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_170
msgid "[170] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_171
msgid "[171] Regularización de bienes de inversión : cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_172
msgid "[172] Suma de deducciones"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_173
msgid "[173] Bienes y servicios corrientes : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_174
msgid "[174] Bienes y servicios corrientes: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_175
msgid "[175] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_176
msgid "[176] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_177
msgid "[177] Bienes corrientes : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_178
msgid "[178] Bienes corrientes: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_179
msgid "[179] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_17
msgid "[17] Value of perceptions in kind"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_180
msgid "[180] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_181
msgid "[181] Bienes corrientes y servicios: base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_182
msgid "[182] Bienes corrientes y servicios: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_183
msgid "[183] Bienes de inversión : base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_184
msgid "[184] Bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_185
msgid "[185] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_186
msgid "[186] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_187
msgid "[187] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_188
msgid "[188] Regularización de bienes de inversión : cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_189
msgid "[189] Suma de deducciones"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_18
msgid "[18] Amount of income on account"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_18
msgid "[18] Contribution 1%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_190
msgid "[190] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_191
msgid "[191] Cuota deducible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_196
msgid "[196] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_197
msgid "[197] Cuota deducible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_19
msgid "[19] Base taxable 1.4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_19
msgid "[19] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_202
msgid "[202] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_203
msgid "[203] Cuota deducible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_208
msgid "[208] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_209
msgid "[209] Cuota deducible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_20
msgid "[20] Amount of perceptions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_214
msgid "[214] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_215
msgid "[215] Cuota deducible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_21
msgid "[21] Amount of withholdings"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_21
msgid "[21] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_21
msgid "[21] Contributions 1.4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_220
msgid "[220] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_221
msgid "[221] Cuota deducible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_227
msgid ""
"[227] Operaciones en Régimen especial de bienes usados, objetos de arte, "
"antigüedades y objetos de colección"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_228
msgid "[228] Operaciones en régimen especial de Agencias de Viajes"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_22
msgid "[22] Base taxable 5.2%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_22
msgid "[22] Cuota devengada 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_22
msgid "[22] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_230
msgid "[230] Adquisiciones interiores exentas"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_231
msgid "[231] Importaciones exentas"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_232
msgid "[232] Bases imponibles del IVA soportado no deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_23
msgid "[23] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_23
msgid "[23] Value of perceptions in kind"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_24
msgid "[24] Amount of income on account"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_24
msgid "[24] Contributinos 5.2%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_24
msgid "[24] Cuota devengada 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_25
msgid "[25] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_25
msgid "[25] Base taxable"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_25
msgid "[25] Nº of recipients"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_26
msgid "[26] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_26
msgid "[26] Cuota devengada 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_26
msgid "[26] Satisfied considerations"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_27
msgid "[27] Amount of income on account"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_27
msgid "[27] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_27
msgid "[27] Total accrual contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_28
msgid "[28] Base"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_28
msgid "[28] Cuota devengada"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_28
msgid "[28] Sum of withholdings"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_29
msgid "[29] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_29
msgid "[29] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_29
msgid "[29] Previous results to report"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_30
msgid "[30] Base"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_30
msgid "[30] Cuota devengada"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_111_casilla_30
msgid "[30] Result to report"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_31
msgid "[31] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_31
msgid "[31] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_32
msgid "[32] Base"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_32
msgid "[32] Cuota devengada"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_33
msgid "[33] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_33
msgid "[33] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_34
msgid "[34] Base"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_34
msgid "[34] Cuotas"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_35
msgid "[35] Base imponible 0.5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_35
msgid "[35] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_36
msgid "[36] Base"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_36
msgid "[36] Cuota devengada 0.5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_37
msgid "[37] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_38
msgid "[38] Base"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_39
msgid "[39] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_40
msgid "[40] Base"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_41
msgid "[41] Base imponible 1.75%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_41
msgid "[41] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_42
msgid "[42] Compensations Special Regime A. G. y P. - Purchases contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_42
msgid "[42] Cuota devengada 1.75%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_43
msgid "[43] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_43
msgid "[43] Regularization of investment assets"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_44
msgid "[44] Cuota devengada"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_44
msgid ""
"[44] Regularization by application of the definitive prorrata percentage"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_45
msgid "[45] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_45
msgid "[45] Total to deduct"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_46
msgid "[46] Cuota devengada"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_46
msgid "[46] Result of general regime"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_47
msgid "[47] Cuotas"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_48
msgid "[48] Bases imponibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_49
msgid "[49] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_500
msgid "[500] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_501
msgid "[501] Cuota devengada 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_502
msgid "[502] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_503
msgid "[503] Cuota devengada 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_504
msgid "[504] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_505
msgid "[505] Cuota devengada 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_506
msgid "[506] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_507
msgid "[507] Cuota 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_50
msgid "[50] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_512
msgid "[512] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_513
msgid "[513] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_514
msgid "[514] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_515
msgid "[515] Cuota deducible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_51
msgid "[51] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_520
msgid "[520] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_521
msgid "[521] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_522
msgid ""
"[522] Regularización por aplicación porcentaje definitivo de prorrata: cuota"
" deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_523
msgid ""
"[523] Servicios localizados en el territorio de aplicación del impuesto por "
"inversión del sujeto pasivo"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_524
msgid ""
"[524] Total devoluciones solicitadas por cuotas soportadas en la adquisición"
" de elementos de transporte (Art. 30 bis RIVA)"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_525
msgid ""
"[525] Total resultados positivos autoliquidaciones del ejercicio (modelo "
"322)"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_526
msgid ""
"[526] Total resultados negativos autoliquidaciones del ejercicio (modelo "
"322)"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_52
msgid "[52] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_53
msgid "[53] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_545
msgid "[545] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_546
msgid "[546] Cuota devengada 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_547
msgid "[547] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_548
msgid "[548] Cuota devengada 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_54
msgid "[54] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_551
msgid "[551] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_552
msgid "[552] Cuota devengada 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_55
msgid "[55] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_56
msgid "[56] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_57
msgid "[57] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_587
msgid "[587] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_588
msgid "[588] Cuota deducible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_58
msgid "[58] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_597
msgid "[597] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_598
msgid "[598] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_599
msgid "[599] Base imponible 1.4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_59
msgid "[59] Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_59
msgid "[59] Intra-community sales of goods and services - Base sales"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_600
msgid "[600] Cuota devengada 1.4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_601
msgid "[601] Base imponible 5.2%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_602
msgid "[602] Cuota devengada 5.2%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_603
msgid "[603] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_604
msgid "[604] Cuota deducible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_605
msgid "[605] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_606
msgid "[606] Cuota deducible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_607
msgid "[607] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_608
msgid "[608] Cuota 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_609
msgid "[609] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_60
msgid "[60] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_60
msgid "[60] Exportations and similar operations - Base sales"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_610
msgid "[610] Cuota 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_611
msgid "[611] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_612
msgid "[612] Cuota deducible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_613
msgid "[613] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_614
msgid "[614] Cuota deducible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_615
msgid "[615] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_616
msgid "[616] Cuota deducible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_617
msgid "[617] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_618
msgid "[618] Cuota deducible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_619
msgid "[619] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_61
msgid "[61] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_620
msgid "[620] Cuota deducible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_621
msgid "[621] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_622
msgid "[622] Cuota deducible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_623
msgid "[623] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_624
msgid "[624] Cuota deducible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_625
msgid "[625] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_626
msgid "[626] Cuota deducible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_627
msgid "[627] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_628
msgid "[628] Cuota deducible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_629
msgid "[629] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_62
msgid "[62] Base taxable"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_62
msgid "[62] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_630
msgid "[630] Cuota deducible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_631
msgid "[631] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_632
msgid "[632] Cuota deducible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_633
msgid "[633] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_634
msgid "[634] Cuota deducible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_635
msgid "[635] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_636
msgid "[636] Cuota deducible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_637
msgid "[637] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_638
msgid "[638] Cuota deducible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_639
msgid "[639] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_63
msgid "[63] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_63
msgid "[63] Regularización de bienes de inversión: cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_640
msgid "[640] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_641
msgid "[641] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_642
msgid "[642] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_643
msgid "[643] Base imponible 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_644
msgid "[644] Cuota devengada 4%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_645
msgid "[645] Base imponible 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_646
msgid "[646] Cuota devengada 10%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_647
msgid "[647] Base imponible 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_648
msgid "[648] Cuota devengada 21%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_649
msgid "[649] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_64
msgid "[64] Suma de deducciones: Cuotas deducibles"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_650
msgid "[650] Cuota devengada"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_651
msgid "[651] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_652
msgid "[652] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_653
msgid ""
"[653] Operaciones a las que habiéndoles sido aplicado el régimen especial "
"del criterio de caja hubieran resultado devengadas conforme a la regla "
"general de devengo contenida en el art. 75 LIVA"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_654
msgid "[654] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_655
msgid "[655] Cuota"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_656
msgid "[656] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_657
msgid "[657] Cuota soportada"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_658
msgid "[658] Regularización cuotas art. 80.Cinco.5ª LIVA"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_659
msgid ""
"[659] IVA a la importación liquidado por la Aduana (sólo sujetos pasivos con"
" opción de diferimiento)"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_65
msgid "[65] Percentage attributable to the State Administration"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_65
msgid "[65] Resultado régimen general"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_660
msgid "[660] Base imponible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_661
msgid "[661] Cuota deducible"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_662
msgid "[662] Cuotas pendientes de compensación al término del ejercicio"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_66
msgid "[66] Attributable to the State Administration"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_67
msgid "[67] Contributions to be compensated from previous periods"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_68
msgid "[68] Result of the annual regularization"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_69
msgid "[69] Result of self-liquidation"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_700
msgid "[700] Base imponible 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_701
msgid "[701] Cuota 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_702
msgid "[702] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_703
msgid "[703] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_704
msgid "[704] Base imponible 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_705
msgid "[705] Cuota 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_706
msgid "[706] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_707
msgid "[707] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_708
msgid "[708] Base imponible 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_709
msgid "[709] Cuota 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_70
msgid ""
"[70] Results to be paid from previous self-assessments or administrative "
"liquidations corresponding to the year and period of self-assessment"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_710
msgid "[710] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_711
msgid "[711] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_712
msgid "[712] Base imponible 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_713
msgid "[713] Cuota 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_714
msgid "[714] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_715
msgid "[715] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_716
msgid "[716] Base imponible 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_717
msgid "[717] Cuota 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_718
msgid "[718] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_719
msgid "[719] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_71
msgid "[71] Result of settlement"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_720
msgid "[720] Base imponible 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_721
msgid "[721] Cuota 0%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_722
msgid "[722] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_723
msgid "[723] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_724
msgid "[724] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_725
msgid "[725] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_726
msgid "[726] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_727
msgid "[727] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_728
msgid "[728] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_729
msgid "[729] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_730
msgid "[730] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_731
msgid "[731] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_732
msgid "[732] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_733
msgid "[733] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_734
msgid "[734] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_735
msgid "[735] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_736
msgid "[736] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_737
msgid "[737] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_738
msgid "[738] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_739
msgid "[739] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_740
msgid "[740] Base imponible 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_741
msgid "[741] Cuota 5%"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_74
msgid "[74] Base taxable"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_75
msgid "[75] Contributions"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_77
msgid "[77] Import VAT settled by custom pending entry"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_78
msgid ""
"[78] Contributions to be compensated from previous periods applied in this "
"period"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_84
msgid "[84] Suma de resultados"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_85
msgid "[85] Compensación de cuotas del ejercicio anterior"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_86
msgid "[86] Suma de resultados"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_303_casilla_87
msgid ""
"[87] Contributions to be compensated from previous periods pending for "
"subsequent periods"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_95
msgid ""
"[95] Total resultados a ingresar en las autoliquidaciones de IVA del "
"ejercicio"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_96
msgid ""
"[96] Total devoluciones mensuales de IVA solicitadas por sujetos pasivos "
"inscritos en el Registro de devolución mensual"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_97
msgid ""
"[97] Si el resultado de la autoliquidación del último periodo es a compensar"
" o a devolver consigne su importe: a compensar"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_98
msgid ""
"[98] Si el resultado de la autoliquidación del último periodo es a compensar"
" o a devolver consigne su importe: a devolver"
msgstr ""

#. module: l10n_es
#: model:account.report.line,name:l10n_es.mod_390_casilla_99
msgid "[99] Operaciones en régimen general"
msgstr ""
