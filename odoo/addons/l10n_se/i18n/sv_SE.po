# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_se
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-12-24 08:49+0000\n"
"PO-Revision-Date: 2021-12-24 08:49+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_se
#: model:ir.model.fields,field_description:l10n_se.field_res_partner__l10n_se_check_vendor_ocr
#: model:ir.model.fields,field_description:l10n_se.field_res_users__l10n_se_check_vendor_ocr
msgid "Check Vendor OCR"
msgstr "Kontrollera Leverantörens OCR Nummer"

#. module: l10n_se
#: model:ir.model.fields,field_description:l10n_se.field_account_bank_statement_import_journal_creation__invoice_reference_model
#: model:ir.model.fields,field_description:l10n_se.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr "Kommunikationsstandard"

#. module: l10n_se
#: model:ir.model,name:l10n_se.model_res_company
msgid "Companies"
msgstr "Företag"

#. module: l10n_se
#: model:ir.model,name:l10n_se.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: l10n_se
#: model:ir.model.fields,field_description:l10n_se.field_res_partner__l10n_se_default_vendor_payment_ref
#: model:ir.model.fields,field_description:l10n_se.field_res_users__l10n_se_default_vendor_payment_ref
msgid "Default Vendor Payment Ref"
msgstr "Standard Lev. betalningsref."

#. module: l10n_se
#: code:addons/l10n_se/models/res_partner.py:0
msgid "Default vendor OCR number isn't a valid OCR number."
msgstr "Standard OCR nummer för leverantören är inte ett godkänt OCR nummer."

#. module: l10n_se
#: model:ir.model.fields,help:l10n_se.field_res_partner__l10n_se_default_vendor_payment_ref
#: model:ir.model.fields,help:l10n_se.field_res_users__l10n_se_default_vendor_payment_ref
msgid "If set, the vendor uses the same Default Payment Reference or OCR Number on all their Vendor Bills."
msgstr "Om satt, kommer denna texten eller OCR numret att användas som betalnings referensen för alla leverantörens fakturor."

#. module: l10n_se
#: model:ir.model,name:l10n_se.model_account_journal
msgid "Journal"
msgstr "Liggare"

#. module: l10n_se
#: model:ir.model,name:l10n_se.model_account_move
msgid "Journal Entry"
msgstr "dagboksinlägg"

#. module: l10n_se
#: model:ir.model.fields,field_description:l10n_se.field_account_bank_statement_import_journal_creation__l10n_se_invoice_ocr_length
#: model:ir.model.fields,field_description:l10n_se.field_account_journal__l10n_se_invoice_ocr_length
msgid "OCR Number Length"
msgstr "OCR Nummerlängd"

#. module: l10n_se
#: code:addons/l10n_se/models/account_move.py:0
msgid "OCR Reference Number length is greater than allowed. Allowed length in invoice journal setting is %s."
msgstr "OCR referensnummer är längre än tillåtet. Ändra inställningar i inställningarna för fakturaverifikat. Inställningarn är idag %s."

#. module: l10n_se
#: code:addons/l10n_se/models/account_journal.py:0
msgid "OCR Reference Number length need to be greater than 5. Please correct settings under invoice journal settings."
msgstr "OCR referensnummerlängd behöver vara större än 5. Ändra inställningar för fakturaverifikat."

#. module: l10n_se
#: model:ir.model.fields,field_description:l10n_se.field_res_company__org_number
msgid "Org Number"
msgstr ""

#. module: l10n_se
#: model_terms:ir.ui.view,arch_db:l10n_se.res_partner_ocr_form
msgid "Payment Options Sweden"
msgstr "Betalningsalternativ Sverige"

#. module: l10n_se
#: model:ir.ui.menu,name:l10n_se.account_reports_se_statements_menu
msgid "Sweden"
msgstr "Sverige"

#. module: l10n_se
#: model:ir.model.fields.selection,name:l10n_se.selection__account_journal__invoice_reference_model__se_ocr2
msgid "Sweden OCR Level 1 & 2"
msgstr "Sverige OCR typ 1 & 2"

#. module: l10n_se
#: model:ir.model.fields.selection,name:l10n_se.selection__account_journal__invoice_reference_model__se_ocr3
msgid "Sweden OCR Level 3"
msgstr "Sverige OCR typ 3"

#. module: l10n_se
#: model:ir.model.fields.selection,name:l10n_se.selection__account_journal__invoice_reference_model__se_ocr4
msgid "Sweden OCR Level 4"
msgstr "Sverige OCR typ 4"

#. module: l10n_se
#: model:ir.model.fields,help:l10n_se.field_res_partner__l10n_se_check_vendor_ocr
#: model:ir.model.fields,help:l10n_se.field_res_users__l10n_se_check_vendor_ocr
msgid "This Vendor uses OCR Number on their Vendor Bills."
msgstr ""

#. module: l10n_se
#: model:ir.model.fields,help:l10n_se.field_account_bank_statement_import_journal_creation__l10n_se_invoice_ocr_length
#: model:ir.model.fields,help:l10n_se.field_account_journal__l10n_se_invoice_ocr_length
msgid "Total length of OCR Reference Number including checksum."
msgstr "Denna leverantör använder sig av OCR referensnummer på deras leverantörsfakturor."

#. module: l10n_se
#: code:addons/l10n_se/models/account_move.py:0
msgid "Vendor require OCR Number as payment reference. Payment reference isn't a valid OCR Number."
msgstr "Leverantör kräver OCR nummer som sin betalningsreferens. Betalningsrefrensen är inte ett godkänt OCR nummer."

#. module: l10n_se
#: code:addons/l10n_se/models/account_move.py:0
#: code:addons/l10n_se/models/res_partner.py:0
msgid "Warning"
msgstr "Varning"

#. module: l10n_se
#: model:ir.model.fields,help:l10n_se.field_account_bank_statement_import_journal_creation__invoice_reference_model
#: model:ir.model.fields,help:l10n_se.field_account_journal__invoice_reference_model
msgid "You can choose different models for each type of reference. The default one is the Odoo reference."
msgstr "Du kan välja olika modeller för varje typ av referens. Standard är Odoo-referensen."
