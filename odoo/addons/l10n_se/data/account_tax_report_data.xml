<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report" model="account.report">
        <field name="name">skatterapport</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.se"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_title_sales" model="account.report.line">
                <field name="name">Block A – Momspliktig försäljning eller uttag exklusive moms</field>
                <field name="code">se_a</field>
                <field name="aggregation_formula">se_05.balance + se_06.balance + se_07.balance + se_08.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_05" model="account.report.line">
                        <field name="name">Fält 05 – Momspliktig försäljning som inte ingår i fält 06, 07 eller 08</field>
                        <field name="code">se_05</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_05_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_05</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_06" model="account.report.line">
                        <field name="name">Fält 06 – Momspliktiga uttag</field>
                        <field name="code">se_06</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_06_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_06</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_07" model="account.report.line">
                        <field name="name">Fält 07 – Beskattningsunderlag vid vinstmarginalbeskattning</field>
                        <field name="code">se_07</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_07_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_07</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_08" model="account.report.line">
                        <field name="name">Fält 08 – Hyresinkomster vid frivillig skattskyldighet</field>
                        <field name="code">se_08</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_08_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_08</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_title_output_vat_sales" model="account.report.line">
                <field name="name">Block B – Utgående moms på försäljning eller uttag i fält 05–08</field>
                <field name="code">se_b</field>
                <field name="aggregation_formula">se_10.balance + se_11.balance + se_12.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_10" model="account.report.line">
                        <field name="name">Fält 10 – Utgående moms 25 %</field>
                        <field name="code">se_10</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_10_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_10</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_11" model="account.report.line">
                        <field name="name">Fält 11 – Utgående moms 12 %</field>
                        <field name="code">se_11</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_11_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_11</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_12" model="account.report.line">
                        <field name="name">Fält 12 – Utgående moms 6 %</field>
                        <field name="code">se_12</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_12_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_12</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_title_purchases" model="account.report.line">
                <field name="name">Block C – Momspliktiga inköp vid omvänd skattskyldighet</field>
                <field name="code">se_c</field>
                <field name="aggregation_formula">se_20.balance + se_21.balance + se_22.balance + se_23.balance + se_24.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_20" model="account.report.line">
                        <field name="name">Fält 20 – Inköp av varor från annat EU-land</field>
                        <field name="code">se_20</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_20_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_20</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_21" model="account.report.line">
                        <field name="name">Fält 21 – Inköp av tjänster från ett annat EU-land, enligt huvudregeln</field>
                        <field name="code">se_21</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_21_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_21</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_22" model="account.report.line">
                        <field name="name">Fält 22 – Inköp av tjänster från länder utanför EU</field>
                        <field name="code">se_22</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_22_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_22</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_23" model="account.report.line">
                        <field name="name">Fält 23 – Inköp av varor i Sverige</field>
                        <field name="code">se_23</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_23_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_23</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_24" model="account.report.line">
                        <field name="name">Fält 24 – Övriga inköp av tjänster</field>
                        <field name="code">se_24</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_24_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_24</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_title_output_vat_purchases" model="account.report.line">
                <field name="name">Block D – Utgående moms på inköp i fält 20–24</field>
                <field name="code">se_d</field>
                <field name="aggregation_formula">se_30.balance + se_31.balance + se_32.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_30" model="account.report.line">
                        <field name="name">Fält 30 – Utgående moms 25 %</field>
                        <field name="code">se_30</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_30_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_30</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_31" model="account.report.line">
                        <field name="name">Fält 31 – Utgående moms 12 %</field>
                        <field name="code">se_31</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_31_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_31</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_32" model="account.report.line">
                        <field name="name">Fält 32 – Utgående moms 6 %</field>
                        <field name="code">se_32</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_32_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_32</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_title_imports" model="account.report.line">
                <field name="name">Block H - moms vid import</field>
                <field name="code">se_h</field>
                <field name="aggregation_formula">se_50.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_50" model="account.report.line">
                        <field name="name">Fält 50 - Beskattningsunderlag vid import</field>
                        <field name="code">se_50</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_50_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_50</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_title_output_vat_imports" model="account.report.line">
                <field name="name">Block I - Utgående moms på import i fält 50</field>
                <field name="code">se_i</field>
                <field name="aggregation_formula">se_60.balance + se_61.balance + se_62.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_60" model="account.report.line">
                        <field name="name">Fält 60 – Utgående moms 25 %</field>
                        <field name="code">se_60</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_60_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_60</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_61" model="account.report.line">
                        <field name="name">Fält 61 – Utgående moms 12 %</field>
                        <field name="code">se_61</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_61_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_61</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_62" model="account.report.line">
                        <field name="name">Fält 62 – Utgående moms 6 %</field>
                        <field name="code">se_62</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_62_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_62</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_title_exempt_sales" model="account.report.line">
                <field name="name">Block E – Försäljning m.m. som är undantagen från moms</field>
                <field name="code">se_e</field>
                <field name="aggregation_formula">se_35.balance + se_36.balance + se_37.balance + se_38.balance + se_39.balance + se_40.balance + se_41.balance + se_42.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_35" model="account.report.line">
                        <field name="name">Fält 35 – Försäljning av varor till ett annat EU-land</field>
                        <field name="code">se_35</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_35_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_35</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_36" model="account.report.line">
                        <field name="name">Fält 36 – Försäljning av varor utanför EU</field>
                        <field name="code">se_36</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_36_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_36</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_37" model="account.report.line">
                        <field name="name">Fält 37 – Mellanmans inköp av varor vid trepartshandel</field>
                        <field name="code">se_37</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_37_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_37</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_38" model="account.report.line">
                        <field name="name">Fält 38 – Mellanmans försäljning av varor vid trepartshandel</field>
                        <field name="code">se_38</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_38_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_38</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_39" model="account.report.line">
                        <field name="name">Fält 39 – Försäljning av tjänster till en beskattningsbar person (näringsidkare) i ett annat EU-land, enligt huvudregeln </field>
                        <field name="code">se_39</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_39_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_39</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_40" model="account.report.line">
                        <field name="name">Fält 40 – Övrig försäljning av tjänster omsatta utanför Sverige</field>
                        <field name="code">se_40</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_40_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_40</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_41" model="account.report.line">
                        <field name="name">Fält 41 – Försäljning när köparen är skattskyldig i Sverige</field>
                        <field name="code">se_41</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_41_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_41</field>
                            </record>
                        </field>
                    </record>
                    <record id="tax_report_line_42" model="account.report.line">
                        <field name="name">Fält 42 – Övrig försäljning m.m.</field>
                        <field name="code">se_42</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_42_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_42</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_title_input_vat" model="account.report.line">
                <field name="name">Block F – Ingående moms</field>
                <field name="code">se_f</field>
                <field name="aggregation_formula">se_48.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_48" model="account.report.line">
                        <field name="name">Fält 48 – Ingående moms att dra av</field>
                        <field name="code">se_48</field>
                        <field name="expression_ids">
                            <record id="tax_report_line_48_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">se_48</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tax_report_title_vat_debt_credit" model="account.report.line">
                <field name="name">Block G – Moms att betala eller få tillbaka</field>
                <field name="code">se_g</field>
                <field name="aggregation_formula">se_b.balance+se_i.balance+se_d.balance-se_f.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tax_report_line_49" model="account.report.line">
                        <field name="name">Fält 49 – Moms att betala eller få tillbaka</field>
                        <field name="code">se_49</field>
                        <field name="aggregation_formula">se_b.balance+se_i.balance+se_d.balance-se_f.balance</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
