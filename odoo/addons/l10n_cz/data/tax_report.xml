<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_cz_vat_declaration" model="account.report">
        <field name="name">VAT tax return</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="availability_condition">country</field>
        <field name="country_id" ref="base.cz"/>
        <field name="filter_hierarchy">optional</field>
        <field name="filter_multi_company">disabled</field>
        <field name="default_opening_date_filter">previous_month</field>
        <field name="filter_date_range" eval="True"/>
        <field name="filter_period_comparison" eval="True"/>
        <field name="filter_show_draft" eval="True"/>
        <field name="column_ids">
            <record id="l10n_cz_vat_declaration_tax_base" model="account.report.column">
                <field name="name">Tax base</field>
                <field name="expression_label">tax_base</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="l10n_cz_vat_declaration_output_tax" model="account.report.column">
                <field name="name">Output Tax</field>
                <field name="expression_label">output_tax</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="l10n_cz_vat_declaration_value" model="account.report.column">
                <field name="name">Value</field>
                <field name="expression_label">value</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="l10n_cz_vat_declaration_in_full_amount" model="account.report.column">
                <field name="name">In full</field>
                <field name="expression_label">in_full_amount</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="l10n_cz_vat_declaration_reduced_claim" model="account.report.column">
                <field name="name">Short deduction</field>
                <field name="expression_label">reduced_claim</field>
                <field name="figure_type">monetary</field>
            </record>
            <record id="l10n_cz_vat_declaration_coefficient" model="account.report.column">
                <field name="name">Coefficient</field>
                <field name="expression_label">coefficient</field>
                <field name="figure_type">percentage</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_cz_vat_declaration_line_1" model="account.report.line">
                <field name="name">I. Taxable transactions</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_cz_vat_declaration_line_2" model="account.report.line">
                        <field name="name">Supply of goods or services with a place of performance in the domestic territory</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_3" model="account.report.line">
                                <field name="name">Basic - line 1</field>
                                <field name="code">R1</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_3_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 1 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_3_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 1 Tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_4" model="account.report.line">
                                <field name="name">Reduced - line 2</field>
                                <field name="code">R2</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_4_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 2 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_4_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 2 Tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_5" model="account.report.line">
                        <field name="name">Acquisition of goods from another Member State (16;17(6)(e);19(3)/19(6))</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_6" model="account.report.line">
                                <field name="name">Basic - line 3</field>
                                <field name="code">R3</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_6_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 3 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_6_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 3 Tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_7" model="account.report.line">
                                <field name="name">Reduced - line 4</field>
                                <field name="code">R4</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_7_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 4 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_7_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 4 Tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_8" model="account.report.line">
                        <field name="name">Receipt of services with a place of supply referred to in Article 9(1) from a person registered for tax in another Member State</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_9" model="account.report.line">
                                <field name="name">Basic - line 5</field>
                                <field name="code">R5</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_9_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 5 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_9_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 5 Tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_10" model="account.report.line">
                                <field name="name">Reduced - line 6</field>
                                <field name="code">R6</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_10_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 6 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_10_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 6 Tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_11" model="account.report.line">
                        <field name="name">Import of goods (23)</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_12" model="account.report.line">
                                <field name="name">Basic - line 7</field>
                                <field name="code">R7</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_12_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 7 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_12_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 7 Tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_13" model="account.report.line">
                                <field name="name">Reduced - line 8</field>
                                <field name="code">R8</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_13_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 8 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_13_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 8 Tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_14" model="account.report.line">
                        <field name="name">Acquisition of a new means of transport (19(4)/19(6)) - line 9</field>
                        <field name="code">R9</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_14_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 9 Base</field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_14_output_tax" model="account.report.expression">
                                <field name="label">output_tax</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 9 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_15" model="account.report.line">
                        <field name="name">Tax reverse charge scheme (92a) - buyer of goods or recipient of services</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_16" model="account.report.line">
                                <field name="name">Basic - line 10</field>
                                <field name="code">R10</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_16_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 10 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_16_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 10 Tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_17" model="account.report.line">
                                <field name="name">Reduced - line 11</field>
                                <field name="code">R11</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_17_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 11 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_17_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 11 Tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_18" model="account.report.line">
                        <field name="name">Other taxable supplies which are subject to the obligation to declare tax on receipt (108)</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_19" model="account.report.line">
                                <field name="name">Basic - line 12</field>
                                <field name="code">R12</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_19_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 12 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_19_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 12 Tax</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_20" model="account.report.line">
                                <field name="name">Reduced - line 13</field>
                                <field name="code">R13</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_20_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 13 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_20_output_tax" model="account.report.expression">
                                        <field name="label">output_tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 13 Tax</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="l10n_cz_vat_declaration_line_21" model="account.report.line">
                <field name="name">II. Other supplies and supplies with a place of supply outside the domestic territory with a right to tax deduction</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_cz_vat_declaration_line_22" model="account.report.line">
                        <field name="name">Delivery of goods to another Member State (64) - line 20</field>
                        <field name="code">R20</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_22_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 20</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_23" model="account.report.line">
                        <field name="name">Supply of services with a place of supply in another Member State as defined in Article 102(1)(d) and (2) - line 21</field>
                        <field name="code">R21</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_23_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 21</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_24" model="account.report.line">
                        <field name="name">Export of goods (66) - line 22</field>
                        <field name="code">R22</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_24_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 22</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_25" model="account.report.line">
                        <field name="name">Supply of a new means of transport to a person not registered for tax in another Member State (Article 19(4)) - line 23</field>
                        <field name="code">R23</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_25_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 23</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_26" model="account.report.line">
                        <field name="name">Selected transactions (110b(2)) - line 24</field>
                        <field name="code">R24</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_26_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 24</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_27" model="account.report.line">
                        <field name="name">Tax reverse charge scheme (92a) - supplier of goods or services - line 25</field>
                        <field name="code">R25</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_27_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 25</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_28" model="account.report.line">
                        <field name="name">Other transactions with the right to tax deduction (e.g., 24a, 67, 68, 69, 70, 71h, 89, 90, 92) - line 26</field>
                        <field name="code">R26</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_28_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 26</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="l10n_cz_vat_declaration_line_29" model="account.report.line">
                <field name="name">III. Additional data</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_cz_vat_declaration_line_30" model="account.report.line">
                        <field name="name">Simplified procedure for the supply of goods in the form of a triangular transaction (17) by a middle person</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_31" model="account.report.line">
                                <field name="name">Acquisition of goods - line 30</field>
                                <field name="code">R30</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_31_value" model="account.report.expression">
                                        <field name="label">value</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 30</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_32" model="account.report.line">
                                <field name="name">Delivery of goods - line 31</field>
                                <field name="code">R31</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_32_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 31</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_33" model="account.report.line">
                        <field name="name">Import of goods exempted according to 71g - line 32</field>
                        <field name="code">R32</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_33_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 32</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_34" model="account.report.line">
                        <field name="name">Adjustment of tax in case of bad debt (46 et seq. or 74a)</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_35" model="account.report.line">
                                <field name="name">Creditor - line 33</field>
                                <field name="code">R33</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_35_value" model="account.report.expression">
                                        <field name="label">value</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 33</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_36" model="account.report.line">
                                <field name="name">Debtor - line 34</field>
                                <field name="code">R34</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_36_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 34</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="l10n_cz_vat_declaration_line_37" model="account.report.line">
                <field name="name">IV. Entitlement to tax deduction</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_cz_vat_declaration_line_38" model="account.report.line">
                        <field name="name">Taxable supplies received from payers</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_39" model="account.report.line">
                                <field name="name">Basic - line 40</field>
                                <field name="code">R40</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_39_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 40 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_39_in_full_amount" model="account.report.expression">
                                        <field name="label">in_full_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 40 Total</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_39_reduced_claim" model="account.report.expression">
                                        <field name="label">reduced_claim</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_40" model="account.report.line">
                                <field name="name">Reduced - line 41</field>
                                <field name="code">R41</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_40_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 41 Base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_40_in_full_amount" model="account.report.expression">
                                        <field name="label">in_full_amount</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 41 Total</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_40_reduced_claim" model="account.report.expression">
                                        <field name="label">reduced_claim</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_41" model="account.report.line">
                        <field name="name">When importing goods where the customs office is the tax administrator - line 42</field>
                        <field name="code">R42</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_41_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 42 Base</field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_41_in_full_amount" model="account.report.expression">
                                <field name="label">in_full_amount</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 42 Total</field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_41_reduced_claim" model="account.report.expression">
                                <field name="label">reduced_claim</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_42" model="account.report.line">
                        <field name="name">Of the taxable supplies shown on lines 3 to 13</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_43" model="account.report.line">
                                <field name="name">Basic - line 43</field>
                                <field name="code">R43</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_43_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">R3.tax_base + R5.tax_base + R7.tax_base + R9.tax_base + R10.tax_base + R12.tax_base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_43_in_full_amount" model="account.report.expression">
                                        <field name="label">in_full_amount</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">R3.output_tax + R5.output_tax + R7.output_tax + R9.output_tax + R10.output_tax + R12.output_tax</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_43_reduced_claim" model="account.report.expression">
                                        <field name="label">reduced_claim</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_44" model="account.report.line">
                                <field name="name">Reduced - line 44</field>
                                <field name="code">R44</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_44_tax_base" model="account.report.expression">
                                        <field name="label">tax_base</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">R4.tax_base + R6.tax_base + R8.tax_base + R11.tax_base + R13.tax_base</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_44_in_full_amount" model="account.report.expression">
                                        <field name="label">in_full_amount</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">R4.output_tax + R6.output_tax + R8.output_tax + R11.output_tax + R13.output_tax</field>
                                    </record>
                                    <record id="l10n_cz_vat_declaration_line_44_reduced_claim" model="account.report.expression">
                                        <field name="label">reduced_claim</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_45" model="account.report.line">
                        <field name="name">Adjustment of tax deductions according to 75, 77, 79 to 79e - line 45</field>
                        <field name="code">R45</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_45_in_full_amount" model="account.report.expression">
                                <field name="label">in_full_amount</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_45_reduced_claim" model="account.report.expression">
                                <field name="label">reduced_claim</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_46" model="account.report.line">
                        <field name="name">Total tax deduction (40 + 41 + 42 + 43 + 44 + 45) - line 46</field>
                        <field name="code">R46</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_46_in_full_amount" model="account.report.expression">
                                <field name="label">in_full_amount</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">R40.in_full_amount + R41.in_full_amount + R42.in_full_amount + R43.in_full_amount + R44.in_full_amount + R45.in_full_amount</field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_46_reduced_claim" model="account.report.expression">
                                <field name="label">reduced_claim</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">R40.reduced_claim + R41.reduced_claim + R42.reduced_claim + R43.reduced_claim + R44.reduced_claim + R45.reduced_claim</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_47" model="account.report.line">
                        <field name="name">Value of acquired assets as defined in Section 4(4)(d) and (e) - line 47</field>
                        <field name="code">R47</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_47_tax_base" model="account.report.expression">
                                <field name="label">tax_base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 47 Base</field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_47_in_full_amount" model="account.report.expression">
                                <field name="label">in_full_amount</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 47 Total</field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_47_reduced_claim" model="account.report.expression">
                                <field name="label">reduced_claim</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="l10n_cz_vat_declaration_line_48" model="account.report.line">
                <field name="name">V. Reduction of tax deduction entitlement</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_cz_vat_declaration_line_49" model="account.report.line">
                        <field name="name">Transactions exempt from tax without the right to tax deduction - line 50</field>
                        <field name="code">R50</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_49_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 50</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_50" model="account.report.line">
                        <field name="name">Value of transactions not included in the calculation of the coefficient (76(4))</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_51" model="account.report.line">
                                <field name="name">Eligible for deduction - line 51a</field>
                                <field name="code">R51A</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_51_value" model="account.report.expression">
                                        <field name="label">value</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 51 with deduction</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_52" model="account.report.line">
                                <field name="name">No deduction - line 51b</field>
                                <field name="code">R51B</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_52_value" model="account.report.expression">
                                        <field name="label">value</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">VAT 51 without deduction</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_53" model="account.report.line">
                        <field name="name">Part of the tax deduction in a reduced amount</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_54" model="account.report.line">
                                <field name="name">Coefficient (%) - line 52a</field>
                                <field name="code">R52A</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_54_coefficient" model="account.report.expression">
                                        <field name="label">coefficient</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_55" model="account.report.line">
                                <field name="name">Deduction - line 52b</field>
                                <field name="code">R52B</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_55_value" model="account.report.expression">
                                        <field name="label">value</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_56" model="account.report.line">
                        <field name="name">Settlement of tax deduction (76(7) to (10))</field>
                        <field name="children_ids">
                            <record id="l10n_cz_vat_declaration_line_57" model="account.report.line">
                                <field name="name">Settlement coefficient (%) - line 53a</field>
                                <field name="code">R53A</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_57_coefficient" model="account.report.expression">
                                        <field name="label">coefficient</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="l10n_cz_vat_declaration_line_58" model="account.report.line">
                                <field name="name">Change of deduction - line 53b</field>
                                <field name="code">R53B</field>
                                <field name="expression_ids">
                                    <record id="l10n_cz_vat_declaration_line_58_value" model="account.report.expression">
                                        <field name="label">value</field>
                                        <field name="engine">external</field>
                                        <field name="formula">sum</field>
                                        <field name="subformula">editable;rounding=2</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="l10n_cz_vat_declaration_line_59" model="account.report.line">
                <field name="name">VI. Calculation of the tax</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="l10n_cz_vat_declaration_line_60" model="account.report.line">
                        <field name="name">Adjustment of tax deduction (78 et seq.) - line 60</field>
                        <field name="code">R60</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_60_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_61" model="account.report.line">
                        <field name="name">Refund of tax (84) - line 61</field>
                        <field name="code">R61</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_61_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">VAT 61</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_62" model="account.report.line">
                        <field name="name">Output tax (sum of 1 to 13 - 61 + tax according to 108 not mentioned elsewhere) - line 62</field>
                        <field name="code">R62</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_62_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">R1.output_tax + R2.output_tax + R3.output_tax + R4.output_tax + R5.output_tax + R6.output_tax + R7.output_tax + R8.output_tax + R9.output_tax +
                                    R10.output_tax + R11.output_tax + R12.output_tax + R13.output_tax - R61.value</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_63" model="account.report.line">
                        <field name="name">Tax deduction (46 In full + 52 Deduction + 53 Change in deduction + 60) - line 63</field>
                        <field name="code">R63</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_63_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">R46.in_full_amount + R52B.value + R53B.value + R60.value</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_64" model="account.report.line">
                        <field name="name">Own tax (62 - 63) - line 64</field>
                        <field name="code">R64</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_64_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">R62.value - R63.value</field>
                                <field name="subformula">if_above(CUR(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_65" model="account.report.line">
                        <field name="name">Excess deduction (63 - 62) - line 65</field>
                        <field name="code">R65</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_65_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">R63.value - R62.value</field>
                                <field name="subformula">if_above(CUR(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="l10n_cz_vat_declaration_line_66" model="account.report.line">
                        <field name="name">Difference from the last known tax when filing the additional tax return (62 - 63) - line 66</field>
                        <field name="code">R66</field>
                        <field name="expression_ids">
                            <record id="l10n_cz_vat_declaration_line_66_value" model="account.report.expression">
                                <field name="label">value</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable;rounding=2</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
