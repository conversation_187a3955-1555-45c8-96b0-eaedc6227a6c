# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_cz
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-18 09:59+0000\n"
"PO-Revision-Date: 2023-12-18 09:59+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2023\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n "
"<= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: l10n_cz
#: model_terms:ir.ui.view,arch_db:l10n_cz.report_invoice_document
msgid "<strong>Taxable Supply:</strong>"
msgstr "<strong>DUZP:</strong>"

#. module: l10n_cz
#: model_terms:ir.ui.view,arch_db:l10n_cz.report_invoice_document
msgid "<strong>Trade registry: </strong>"
msgstr "<strong>Zápis v obchodním rejstříku: </strong>"

#. module: l10n_cz
#: model:ir.model,name:l10n_cz.model_account_chart_template
msgid "Account Chart Template"
msgstr "Šablona účtové osnovy"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_14
msgid "Acquisition of a new means of transport (19(4)/19(6)) - line 9"
msgstr ""
"Pořízení nového dopravního prostředku (§ 19 odst. 4/ § 19 odst. 6) - ř.9"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_31
msgid "Acquisition of goods - line 30"
msgstr "Pořízení zboží - ř.30"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_5
msgid ""
"Acquisition of goods from another Member State (16;17(6)(e);19(3)/19(6))"
msgstr ""
"Pořízení zboží z jiného členského státu (§ 16; § 17 odst. 6 písm. e); § 19 "
"odst. 3/ § 19 odst. 6)"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_60
msgid "Adjustment of tax deduction (78 et seq.) - line 60"
msgstr "Úprava odpočtu daně (§ 78 a násl.) - ř.60"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_45
msgid "Adjustment of tax deductions according to 75, 77, 79 to 79e - line 45"
msgstr "Korekce odpočtů daně podle § 75, § 77, § 79 až § 79e - ř.45"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_34
msgid "Adjustment of tax in case of bad debt (46 et seq. or 74a)"
msgstr ""
"Oprava daně v případě nedobytné pohledávky (§ 46 a násl., resp. § 74a)"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_3
msgid "Basic - line 1"
msgstr "Základní - ř.1"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_16
msgid "Basic - line 10"
msgstr "Základní - ř.10"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_19
msgid "Basic - line 12"
msgstr "Základní - ř.12"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_6
msgid "Basic - line 3"
msgstr "Základní - ř.3"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_39
msgid "Basic - line 40"
msgstr "Základní - ř.40"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_43
msgid "Basic - line 43"
msgstr "Základní - ř.43"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_9
msgid "Basic - line 5"
msgstr "Základní - ř.5"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_12
msgid "Basic - line 7"
msgstr "Základní - ř.7"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_58
msgid "Change of deduction - line 53b"
msgstr "Změna odpočtu - ř.53b"

#. module: l10n_cz
#: model:account.report.column,name:l10n_cz.l10n_cz_vat_declaration_coefficient
msgid "Coefficient"
msgstr "Koeficient"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_54
msgid "Coefficient (%) - line 52a"
msgstr "Koeficient (%) - ř.52a"

#. module: l10n_cz
#: model:ir.model,name:l10n_cz.model_res_company
msgid "Companies"
msgstr "Společnosti"

#. module: l10n_cz
#: model:ir.model,name:l10n_cz.model_base_document_layout
msgid "Company Document Layout"
msgstr "Vzhled firemních dokumentů"

#. module: l10n_cz
#: model:ir.model.fields,field_description:l10n_cz.field_base_document_layout__company_registry
msgid "Company ID"
msgstr "IČO společnosti"

#. module: l10n_cz
#: model_terms:ir.ui.view,arch_db:l10n_cz.l10n_cz_external_layout_bold
#: model_terms:ir.ui.view,arch_db:l10n_cz.l10n_cz_external_layout_boxed
#: model_terms:ir.ui.view,arch_db:l10n_cz.l10n_cz_external_layout_standard
#: model_terms:ir.ui.view,arch_db:l10n_cz.l10n_cz_external_layout_striped
#: model_terms:ir.ui.view,arch_db:l10n_cz.report_invoice_document
msgid "Company ID:"
msgstr "IČO:"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_35
msgid "Creditor - line 33"
msgstr "Věřitel - ř.33"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_36
msgid "Debtor - line 34"
msgstr "Dlužník - ř.34"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_55
msgid "Deduction - line 52b"
msgstr "Odpočet - ř.52b"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_32
msgid "Delivery of goods - line 31"
msgstr "Dodání zboží - ř.31"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_22
msgid "Delivery of goods to another Member State (64) - line 20"
msgstr "Dodání zboží do jiného členského státu (§ 64) - ř.20"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_66
msgid ""
"Difference from the last known tax when filing the additional tax return (62"
" - 63) - line 66"
msgstr ""
"Rozdíl oproti poslední známé dani při podání dodatečného daňového přiznání "
"(62 – 63) - ř.66"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_51
msgid "Eligible for deduction - line 51a"
msgstr "S nárokem na odpočet - ř.51a"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_65
msgid "Excess deduction (63 - 62) - line 65"
msgstr "Nadměrný odpočet (63 – 62) - ř.65"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_24
msgid "Export of goods (66) - line 22"
msgstr "Vývoz zboží (§ 66) - ř.22"

#. module: l10n_cz
#: model:ir.model.fields,field_description:l10n_cz.field_base_document_layout__account_fiscal_country_id
msgid "Fiscal Country"
msgstr "Daňová země"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_1
msgid "I. Taxable transactions"
msgstr "I. Zdanitelná plnění"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_21
msgid ""
"II. Other supplies and supplies with a place of supply outside the domestic "
"territory with a right to tax deduction"
msgstr ""
"II. Ostatní plnění a plnění s místem plnění mimo tuzemsko s nárokem na "
"odpočet daně"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_29
msgid "III. Additional data"
msgstr "III. Doplňující údaje"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_37
msgid "IV. Entitlement to tax deduction"
msgstr "IV. Nárok na odpočet daně"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_11
msgid "Import of goods (23)"
msgstr "Dovoz zboží (§ 23)"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_33
msgid "Import of goods exempted according to 71g - line 32"
msgstr "Dovoz zboží osvobozený podle § 71g - ř.32"

#. module: l10n_cz
#: model:account.report.column,name:l10n_cz.l10n_cz_vat_declaration_in_full_amount
msgid "In full"
msgstr "V plném rozsahu"

#. module: l10n_cz
#: model:ir.model,name:l10n_cz.model_account_move
msgid "Journal Entry"
msgstr "Účetní záznam"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_52
msgid "No deduction - line 51b"
msgstr "Bez nároku na odpočet - ř.51b"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_42
msgid "Of the taxable supplies shown on lines 3 to 13"
msgstr "Ze zdanitelných plnění vykázaných na řádcích 3 až 13"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_18
msgid ""
"Other taxable supplies which are subject to the obligation to declare tax on"
" receipt (108)"
msgstr ""
"Ostatní zdanitelná plnění, u kterých je povinnost přiznat daň při jejich "
"přijetí (§ 108)"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_28
msgid ""
"Other transactions with the right to tax deduction (e.g., 24a, 67, 68, 69, "
"70, 71h, 89, 90, 92) - line 26"
msgstr ""
"Ostatní uskutečněná plnění s nárokem na odpočet daně (např. § 24a, § 67, § "
"68, § 69, § 70, § 71h, § 89, § 90, § 92) - ř.26"

#. module: l10n_cz
#: model:account.report.column,name:l10n_cz.l10n_cz_vat_declaration_output_tax
msgid "Output Tax"
msgstr "Daň z výstupu"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_62
msgid ""
"Output tax (sum of 1 to 13 - 61 + tax according to 108 not mentioned "
"elsewhere) - line 62"
msgstr ""
"Daň na výstupu (součet 1 až 13 – 61 + daň podle § 108 jinde neuvedená) - "
"ř.62"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_64
msgid "Own tax (62 - 63) - line 64"
msgstr "Vlastní daň (62 – 63) - ř.64"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_53
msgid "Part of the tax deduction in a reduced amount"
msgstr "Část odpočtu daně v krácené výši"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_8
msgid ""
"Receipt of services with a place of supply referred to in Article 9(1) from "
"a person registered for tax in another Member State"
msgstr ""
"Přijetí služby s místem plnění podle § 9 odst. 1 od osoby registrované k "
"dani v jiném členském státě"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_17
msgid "Reduced - line 11"
msgstr "Snížená - ř.11"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_20
msgid "Reduced - line 13"
msgstr "Snížená - ř.13"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_4
msgid "Reduced - line 2"
msgstr "Snížená - ř.2"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_7
msgid "Reduced - line 4"
msgstr "Snížená - ř.4"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_40
msgid "Reduced - line 41"
msgstr "Snížená - ř.41"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_44
msgid "Reduced - line 44"
msgstr "Snížená - ř.44"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_10
msgid "Reduced - line 6"
msgstr "Snížená - ř.6"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_13
msgid "Reduced - line 8"
msgstr "Snížená - ř.8"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_61
msgid "Refund of tax (84) - line 61"
msgstr "Vrácení daně (§ 84) - ř.61"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_26
msgid "Selected transactions (110b(2)) - line 24"
msgstr "Vybraná plnění (§ 110b odst. 2) - ř.24"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_57
msgid "Settlement coefficient (%) - line 53a"
msgstr "Vypořádací koeficient (%) - ř.53a"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_56
msgid "Settlement of tax deduction (76(7) to (10))"
msgstr "Vypořádání odpočtu daně (§ 76 odst. 7 až 10)"

#. module: l10n_cz
#: model:account.report.column,name:l10n_cz.l10n_cz_vat_declaration_reduced_claim
msgid "Short deduction"
msgstr "Krátká dedukce"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_30
msgid ""
"Simplified procedure for the supply of goods in the form of a triangular "
"transaction (17) by a middle person"
msgstr ""
"Zjednodušený postup při dodání zboží formou třístranného obchodu (§ 17) "
"prostřední osobou"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_25
msgid ""
"Supply of a new means of transport to a person not registered for tax in "
"another Member State (Article 19(4)) - line 23"
msgstr ""
"Dodání nového dopravního prostředku osobě neregistrované k dani v jiném "
"členském státě (§ 19 odst. 4) - ř.23"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_2
msgid ""
"Supply of goods or services with a place of performance in the domestic "
"territory"
msgstr "Dodání zboží nebo poskytnutí služby s místem plnění v tuzemsku"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_23
msgid ""
"Supply of services with a place of supply in another Member State as defined"
" in Article 102(1)(d) and (2) - line 21"
msgstr ""
"Poskytnutí služeb s místem plnění v jiném členském státě vymezených v § 102 "
"odst. 1 písm. d) a odst. 2 - ř.21"

#. module: l10n_cz
#: model:account.report.column,name:l10n_cz.l10n_cz_vat_declaration_tax_base
msgid "Tax base"
msgstr "Základ daně"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_63
msgid ""
"Tax deduction (46 In full + 52 Deduction + 53 Change in deduction + 60) - "
"line 63"
msgstr ""
"Odpočet daně (46 V plné výši + 52 Odpočet + 53 Změna odpočtu + 60) - ř.63"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_15
msgid ""
"Tax reverse charge scheme (92a) - buyer of goods or recipient of services"
msgstr ""
"Režim přenesení daňové povinnosti (§ 92a) – odběratel zboží nebo příjemce "
"služeb"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_27
msgid ""
"Tax reverse charge scheme (92a) - supplier of goods or services - line 25"
msgstr ""
"Režim přenesení daňové povinnosti (§ 92a) – dodavatel zboží nebo "
"poskytovatel služeb - ř.25"

#. module: l10n_cz
#: model:ir.model.fields,field_description:l10n_cz.field_account_bank_statement_line__taxable_supply_date
#: model:ir.model.fields,field_description:l10n_cz.field_account_move__taxable_supply_date
#: model:ir.model.fields,field_description:l10n_cz.field_account_payment__taxable_supply_date
msgid "Taxable Supply Date"
msgstr "DUZP"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_38
msgid "Taxable supplies received from payers"
msgstr "Z přijatých zdanitelných plnění od plátců"

#. module: l10n_cz
#: model:ir.model.fields,help:l10n_cz.field_base_document_layout__account_fiscal_country_id
msgid "The country to use the tax reports from for this company"
msgstr "Země, jejíž daňové výkazy se použijí pro tuto společnost"

#. module: l10n_cz
#: model:ir.model.fields,help:l10n_cz.field_base_document_layout__company_registry
msgid ""
"The registry number of the company. Use it if it is different from the Tax "
"ID. It must be unique across all partners of a same country"
msgstr ""
"IČO společnosti.Musí být unikátní v rámci všech kontaktů pod danou zemí."

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_46
msgid "Total tax deduction (40 + 41 + 42 + 43 + 44 + 45) - line 46"
msgstr "Odpočet daně celkem (40 + 41 + 42 + 43 + 44 + 45) - ř.46"

#. module: l10n_cz
#: model:ir.model.fields,field_description:l10n_cz.field_res_company__trade_registry
msgid "Trade Registry"
msgstr "Zápis v obchodním rejstříku"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_49
msgid ""
"Transactions exempt from tax without the right to tax deduction - line 50"
msgstr "Plnění osvobozená od daně bez nároku na odpočet daně - ř.50"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_48
msgid "V. Reduction of tax deduction entitlement"
msgstr "V. Krácení nároku na odpočet daně"

#. module: l10n_cz
#: model:account.report,name:l10n_cz.l10n_cz_vat_declaration
msgid "VAT tax return"
msgstr "Daňové přiznání k DPH"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_59
msgid "VI. Calculation of the tax"
msgstr "VI. Výpočet daně"

#. module: l10n_cz
#: model:account.report.column,name:l10n_cz.l10n_cz_vat_declaration_value
msgid "Value"
msgstr "Hodnota"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_47
msgid ""
"Value of acquired assets as defined in Section 4(4)(d) and (e) - line 47"
msgstr ""
"Hodnota pořízeného majetku vymezeného v § 4 odst. 4 písm. d) a e) - ř.47"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_50
msgid ""
"Value of transactions not included in the calculation of the coefficient "
"(76(4))"
msgstr "Hodnota plnění nezapočítávaných do výpočtu koeficientu (§ 76 odst. 4)"

#. module: l10n_cz
#: model:account.report.line,name:l10n_cz.l10n_cz_vat_declaration_line_41
msgid ""
"When importing goods where the customs office is the tax administrator - "
"line 42"
msgstr "Při dovozu zboží, kdy je správcem daně celní úřad - ř.42"
