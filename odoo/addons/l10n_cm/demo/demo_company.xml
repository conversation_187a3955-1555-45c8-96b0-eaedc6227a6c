<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_cm" model="res.partner" forcecreate="1">
        <field name="name">CM Company</field>
        <field name="vat"></field>
        <field name="street"></field>
        <field name="city"></field>
        <field name="country_id" ref="base.cm"/>
        <field name="zip"></field>
        <field name="phone">+237 95 55 22 48 74</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.cameroonexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_cm" model="res.company" forcecreate="1">
        <field name="name">CM Company</field>
        <field name="partner_id" ref="base.partner_demo_company_cm"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_cm')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_cm'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>cm</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_cm')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
