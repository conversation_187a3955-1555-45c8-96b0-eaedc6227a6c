# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_intrastat
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (https://app.transifex.com/odoo/teams/41243/es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid ", which cannot be reported."
msgstr " que no se pueden informar."

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_kwh
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__1_000_kwh
msgid "1 000 kWh"
msgstr "1.000 kWh"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_m3
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__1_000_m3
msgid "1 000 m3"
msgstr "1.000 m3"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__1_000_p/st
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__1_000_p/st
msgid "1 000 p/st"
msgstr "1.000 p/st"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__100_p/st
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__100_p/st
msgid "100 p/st"
msgstr "100 p/st"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o ms-2\" title=\"Values set here are "
"company-specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o ms-2\" title=\"Values set here are "
"company-specific.\"/>"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_move_line_tree_view_account_intrastat_transaction_codes
msgid "Account move line"
msgstr "Línea de movimiento de cuenta"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Active"
msgstr "Activo"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/filters/filters.js:0
msgid "All partners"
msgstr "Todos los contactos"

#. module: account_intrastat
#. odoo-javascript
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: code:addons/account_intrastat/static/src/components/intrastat_report/filters/filters.js:0
msgid "Arrival"
msgstr "Llegada"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
msgid "Arrival country"
msgstr "País de llegada"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By country"
msgstr "Por país"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "By type"
msgstr "Por tipo"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "Check the expired"
msgstr "Revisar elementos vencidos para"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "Check the premature"
msgstr "Revisar elementos anticipados para"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_commodity_code
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__commodity
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Commodity"
msgstr "Mercancía"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_code_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_weight
msgid "Commodity Code"
msgstr "Código de mercancía"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_code_id
msgid "Commodity code"
msgstr "Código de mercancía "

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_company
msgid "Companies"
msgstr "Compañías"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__company_country_id
msgid "Company country"
msgstr "País de la compañía"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_country_code
#: model:ir.model,name:account_intrastat.model_res_country
msgid "Country"
msgstr "País"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_origin_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_origin_country_id
msgid "Country of Origin"
msgstr "País de origen"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_default_invoice_transaction_code_id
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__intrastat_default_invoice_transaction_code_id
msgid "Default invoice transaction code"
msgstr "Código de transacción de factura por defecto"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_default_refund_transaction_code_id
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__intrastat_default_refund_transaction_code_id
msgid "Default refund transaction code"
msgstr "Código de reembolso de factura por defecto"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_transport_mode_id
msgid "Default transport mode"
msgstr "Modo de transporte por defecto"

#. module: account_intrastat
#. odoo-javascript
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
#: code:addons/account_intrastat/static/src/components/intrastat_report/filters/filters.js:0
msgid "Dispatch"
msgstr "Expedición"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/filters/filter_intrastat_report.xml:0
msgid "Extended Mode"
msgstr "Modo extendido"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/filters/filters.js:0
msgid "Extended mode"
msgstr "Modo extendido"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__has_country_regions
msgid "Has Country Regions"
msgstr "Tiene regiones del país"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_incoterm_code
msgid "Incoterm"
msgstr "Incoterm"

#. module: account_intrastat
#: model:account.report.line,name:account_intrastat.intrastat_line
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_transaction_id
#: model_terms:ir.ui.view,arch_db:account_intrastat.invoice_form_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_form_view_inherit_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_template_form_view_inherit_account_intrastat
msgid "Intrastat"
msgstr "Intrastat"

#. module: account_intrastat
#: model:ir.actions.act_window,name:account_intrastat.action_report_intrastat_code_tree
#: model:ir.model,name:account_intrastat.model_account_intrastat_code
#: model:ir.ui.menu,name:account_intrastat.menu_report_intrastat_code
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_form
msgid "Intrastat Code"
msgstr "Código Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_country_id
msgid "Intrastat Country"
msgstr "País de Intrastat"

#. module: account_intrastat
#: model:account.report,name:account_intrastat.intrastat_report
#: model:ir.actions.client,name:account_intrastat.action_account_report_intrastat
#: model:ir.ui.menu,name:account_intrastat.menu_action_account_report_intrastat
msgid "Intrastat Report"
msgstr "Informe de Intrastat"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_intrastat_report_handler
msgid "Intrastat Report Custom Handler"
msgstr "Gestor personalizado del informe Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_supplementary_unit
msgid "Intrastat Supplementary Unit"
msgstr "Unidad suplementaria de Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_template__intrastat_supplementary_unit_amount
msgid "Intrastat Supplementary Unit Amount"
msgstr "Importe de la unidad suplementaria de Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_bank_statement_line__intrastat_transport_mode_id
#: model:ir.model.fields,field_description:account_intrastat.field_account_move__intrastat_transport_mode_id
msgid "Intrastat Transport Mode"
msgstr "Modo de transporte Intrastat"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_report_intrastat_code_tree
msgid "Intrastat code"
msgstr "Código Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_account_bank_statement_line__intrastat_country_id
#: model:ir.model.fields,help:account_intrastat.field_account_move__intrastat_country_id
msgid "Intrastat country, arrival for sales, dispatch for purchases"
msgstr "País de Intrastat, llegada para ventas, expedición para compras"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_country__intrastat
msgid "Intrastat member"
msgstr "Miembro de Intrastat"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_res_company__intrastat_region_id
#: model:ir.model.fields,field_description:account_intrastat.field_res_config_settings__intrastat_region_id
msgid "Intrastat region"
msgstr "Región de Instrastat"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_move_line_tree_view_account_intrastat_transaction_codes
msgid "Intrastat transaction code"
msgstr "Código de transacción Intrastat"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
msgid "Invalid commodity intrastat code products."
msgstr "Productos con código de intrastat no válidos."

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
msgid "Invalid transaction intrastat code entries."
msgstr "Asientos de códigos intrastat de transacciones no válidas."

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
msgid "Invalid transport mode code entries."
msgstr "Asiento de código de modo de transporte no válido"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_move_line_tree_view_account_intrastat_product_origin_country_id
msgid "Invoice Lines"
msgstr "Líneas de factura"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.account_move_tree_view_account_intrastat_transport_codes
msgid "Journal Entries"
msgstr "Asientos contables"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
msgid "Missing country of origin"
msgstr "País de origen faltante"

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
msgid "Missing product"
msgstr "Producto faltante"

#. module: account_intrastat
#: model:res.country,name:account_intrastat.xi
msgid "Northern Ireland"
msgstr "Irlanda del Norte"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/filters/filter_intrastat_report.xml:0
msgid "Only with VAT numbers"
msgstr "Solo con NIF"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.report_invoice_document_intrastat_2019
msgid "Origin"
msgstr "Origen"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_product_origin_country
msgid "Origin Country"
msgstr "País de origen"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_partner_vat
msgid "Partner VAT"
msgstr "NIF del contacto"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/filters/filters.js:0
msgid "Partners with VAT numbers"
msgstr "Contactos con NIF"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_template
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_weight
msgid "Product"
msgstr "Producto"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_account_move_line__intrastat_product_origin_country_id
msgid "Product Country"
msgstr "País del producto"

#. module: account_intrastat
#: model:ir.model,name:account_intrastat.model_product_product
msgid "Product Variant"
msgstr "Variante de producto"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_region_code
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__region
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Region"
msgstr "Región"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "Some lines have expired intrastat"
msgstr "Algunas líneas tienen el intrastat vencido"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "Some lines have premature intrastat"
msgstr "Algunas líneas tienen el Intrastat anticipado"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "Some lines have undefined"
msgstr "Algunas líneas tienen un carácter indefinido en"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "Some products have undefined"
msgstr "Algunos productos tienen un carácter indefinido en"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/filters/filters.js:0
msgid "Standard mode"
msgstr "Modo estándar"

#. module: account_intrastat
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_supplementary_unit
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Supplementary Unit"
msgstr "Unidad suplementaria"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_supplementary_units
#: model:ir.model.fields,field_description:account_intrastat.field_product_product__intrastat_supplementary_unit_amount
msgid "Supplementary Units"
msgstr "Unidades suplementarias"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
msgid "Supplementary Units per Product"
msgstr "Unidades suplementarias por producto"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_system
msgid "System"
msgstr "Sistema"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__tj
msgid "TJ"
msgstr "TJ"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_res_config_settings__company_country_id
msgid "The country to use the tax reports from for this company"
msgstr "El país para usar los informes de impuestos de esta empresa"

#. module: account_intrastat
#: model:ir.model.fields,help:account_intrastat.field_product_product__intrastat_supplementary_unit_amount
#: model:ir.model.fields,help:account_intrastat.field_product_template__intrastat_supplementary_unit_amount
msgid "The number of supplementary units per product quantity."
msgstr "El número de unidades suplementarias por cantidad del producto."

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "There are some"
msgstr "Hay algunos"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_transaction_code
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__type__transaction
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transaction"
msgstr "Transacción"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_transport_code
#: model_terms:ir.ui.view,arch_db:account_intrastat.view_intrastat_code_search
msgid "Transport"
msgstr "Transporte"

#. module: account_intrastat
#: model:ir.model.constraint,message:account_intrastat.constraint_account_intrastat_code_intrastat_region_code_unique
msgid "Triplet code/type/country_id must be unique."
msgstr "El triplete code/type/country_id tiene que ser único."

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
msgid "Undefined supplementary unit products."
msgstr "Productos unitarios suplementarios no definidos."

#. module: account_intrastat
#. odoo-python
#: code:addons/account_intrastat/models/account_intrastat_report.py:0
msgid "Undefined weight products."
msgstr "Productos sin peso definido"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_value
msgid "Value"
msgstr "Valor"

#. module: account_intrastat
#: model:account.report.column,name:account_intrastat.intrastat_report_column_weight
msgid "Weight (kg)"
msgstr "Peso (kg)"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__c/k
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__c/k
msgid "c/k"
msgstr "c/k"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__ce/el
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__ce/el
msgid "ce/el"
msgstr "ce/el"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "commodity codes"
msgstr "códigos de mercancía "

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__ct/l
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__ct/l
msgid "ct/l"
msgstr "ct/l"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__gi_f_/_s
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__gi_f_/_s
msgid "gi F / S"
msgstr "gi F / S"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "journals items with no product"
msgstr "asientos contables sin producto"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_90_%_sdt
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_90_%_sdt
msgid "kg 90 % sdt"
msgstr "kg 90 % sdt"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_h2o2
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_h2o2
msgid "kg H2O2"
msgstr "kg H2O2"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_k2o
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_k2o
msgid "kg K2O"
msgstr "kg K2O"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_koh
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_koh
msgid "kg KOH"
msgstr "kg KOH"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_n
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_n
msgid "kg N"
msgstr "kg N"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_naoh
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_naoh
msgid "kg NaOH"
msgstr "kg NaOH"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_p2o5
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_p2o5
msgid "kg P2O5"
msgstr "kg P2O5"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_u
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_u
msgid "kg U"
msgstr "kg U"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg_met_am_
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg_met_am_
msgid "kg met.am."
msgstr "kg met.am."

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__kg/net_eda
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__kg/net_eda
msgid "kg/net eda"
msgstr "kg/net eda"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__l_alc__100_%
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__l_alc__100_%
msgid "l alc. 100 %"
msgstr "l alc. 100 %"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__m2
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__m2
msgid "m2"
msgstr "m2"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__m3
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__m3
msgid "m3"
msgstr "m3"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__p/st
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__p/st
msgid "p/st"
msgstr "p/st"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__pa
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__pa
msgid "pa"
msgstr "pa"

#. module: account_intrastat
#: model_terms:ir.ui.view,arch_db:account_intrastat.product_product_tree_view_account_intrastat_supplementary_unit
msgid "product"
msgstr "producto"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "product commodity codes"
msgstr "códigos de mercancía de los productos"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "product's commodity codes"
msgstr "códigos de mercancía de los productos "

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "supplementary units"
msgstr "unidades suplementarias"

#. module: account_intrastat
#: model:ir.model.fields.selection,name:account_intrastat.selection__account_intrastat_code__supplementary_unit__t__co2
#: model:ir.model.fields.selection,name:account_intrastat.selection__product_template__intrastat_supplementary_unit__t__co2
msgid "t. CO2"
msgstr "t. CO2"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "transaction codes"
msgstr "códigos de transacción"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "weights"
msgstr "pesos"

#. module: account_intrastat
#. odoo-javascript
#: code:addons/account_intrastat/static/src/components/intrastat_report/warnings.xml:0
msgid "when they are required."
msgstr "cuando se requieren."
