# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* privacy_lookup
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 3eec91a23d05c632ffac786ac42b81b8_b6fff7b <8985b7bc57db860af29969457dbb51b3_1018915>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <mika<PERSON>.a<PERSON><PERSON>@mariaakerberg.com>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Swedish (https://app.transifex.com/odoo/teams/41243/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__additional_note
msgid "Additional Note"
msgstr "Ytterligare information"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_email
msgid "Anonymized Email"
msgstr "Anonymiserad e-post"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__anonymized_name
msgid "Anonymized Name"
msgstr "Anonymiserat namn"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_archive_all
msgid "Archive Selection"
msgstr "Arkivval"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Archived"
msgstr "Arkiverad"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Can be archived"
msgstr "Kan bli arkiverad"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_uid
msgid "Created by"
msgstr "Skapad av"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__create_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__create_date
msgid "Created on"
msgstr "Skapad den"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__date
msgid "Date"
msgstr "Datum"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Delete"
msgstr "Radera"

#. module: privacy_lookup
#: model:ir.actions.server,name:privacy_lookup.ir_actions_server_unlink_all
msgid "Delete Selection"
msgstr "Ta bort markering"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
msgid "Deleted"
msgstr "Borttagen"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__display_name
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__display_name
msgid "Display Name"
msgstr "Visningsnamn"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model
msgid "Document Model"
msgstr "Dokumentmodell"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__email
msgid "Email"
msgstr "E-post"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__execution_details
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__execution_details
msgid "Execution Details"
msgstr "Utförandedetaljer"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__records_description
msgid "Found Records"
msgstr "Hittade poster"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Group By"
msgstr "Grupp Av"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__user_id
msgid "Handled By"
msgstr "Hanterad av "

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__has_active
msgid "Has Active"
msgstr "Har aktiv"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__id
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__id
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "ID"
msgstr "ID"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_active
msgid "Is Active"
msgstr "Är aktiv"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__is_unlinked
msgid "Is Unlinked"
msgstr "Är osannolikt"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_uid
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_uid
msgid "Last Updated by"
msgstr "Senast uppdaterad av"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_log__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__write_date
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__write_date
msgid "Last Updated on"
msgstr "Senast uppdaterad den"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_ids
msgid "Line"
msgstr "Rad"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__line_count
msgid "Line Count"
msgstr "Antal rader"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__log_id
msgid "Log"
msgstr "Logg"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "Lookup"
msgstr "Slå upp"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Model"
msgstr "Modell"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__name
msgid "Name"
msgstr "Namn"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid "Open Record"
msgstr "Öppen post"

#. module: privacy_lookup
#: model:ir.ui.menu,name:privacy_lookup.privacy_menu
msgid "Privacy"
msgstr "Integritet"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_log
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_form
msgid "Privacy Log"
msgstr "Sekretesslogg"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_action
#: model:ir.actions.act_window,name:privacy_lookup.privacy_log_form_action
#: model:ir.ui.menu,name:privacy_lookup.pricacy_log_menu
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_log_view_list
msgid "Privacy Logs"
msgstr "Sekretessloggar"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_partner
#: model:ir.actions.server,name:privacy_lookup.ir_action_server_action_privacy_lookup_user
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "Privacy Lookup"
msgstr "Integritetsrapport"

#. module: privacy_lookup
#: model:ir.actions.act_window,name:privacy_lookup.action_privacy_lookup_wizard_line
msgid "Privacy Lookup Line"
msgstr "Integritetsrapportrad"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard
msgid "Privacy Lookup Wizard"
msgstr "Guide till integritetsrapport"

#. module: privacy_lookup
#: model:ir.model,name:privacy_lookup.model_privacy_lookup_wizard_line
msgid "Privacy Lookup Wizard Line"
msgstr "Guide till integritetsrapportsrad"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__resource_ref
msgid "Record"
msgstr "Post"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard__records_description
msgid "Records Description"
msgstr "Postbeskrivning"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_view_form
msgid "References"
msgstr "Referenser"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_model_id
msgid "Related Document Model"
msgstr "Modell för relaterade dokument"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_id
msgid "Resource ID"
msgstr "Resurs ID"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__res_name
msgid "Resource name"
msgstr "Resursnamn"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_search
msgid "Search References"
msgstr "Sök referenser"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
msgid "The record is already unlinked."
msgstr "Posten är redan bortkopplad."

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/models/privacy_log.py:0
msgid "This email address is not valid (%s)"
msgstr "Den här e-postadressen är inte giltig (%s)"

#. module: privacy_lookup
#: model_terms:ir.ui.view,arch_db:privacy_lookup.privacy_lookup_wizard_line_view_tree
msgid ""
"This operation is irreversible. Do you wish to proceed to the record "
"deletion?"
msgstr ""
"Denna operation är oåterkallelig. Vill du gå vidare till att radera posten?"

#. module: privacy_lookup
#. odoo-python
#: code:addons/privacy_lookup/wizard/privacy_lookup_wizard.py:0
msgid "Unarchived"
msgstr "Avarkivera"

#. module: privacy_lookup
#: model:ir.model.fields,field_description:privacy_lookup.field_privacy_lookup_wizard_line__wizard_id
msgid "Wizard"
msgstr "Guide"
