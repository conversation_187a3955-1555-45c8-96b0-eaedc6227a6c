# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Dutch (https://app.transifex.com/odoo/teams/41243/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Buy Credit"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Krediet kopen"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "Account informatie"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "Account token"

#. module: iap
#: model:ir.model.fields,help:iap.field_iap_account__account_token
msgid ""
"Account token is your authentication key for this service. Do not share it."
msgstr ""
"Het account token is je verificatiesleutel voor deze dienst. Deel het niet."

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"An error occurred while reaching %s. Please contact Odoo support if this "
"error persists."
msgstr ""
"Er is een fout opgetreden bij het bereiken van %s. Neem contact op met Odoo "
"support als deze fout zich blijft voordoen."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__balance
msgid "Balance"
msgstr "Saldo"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__banned
msgid "Banned"
msgstr "Verboden"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Buy credits"
msgstr "Credits kopen"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Cancel"
msgstr "Annuleren"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "Bedrijf"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
#: model:ir.model.fields,field_description:iap.field_iap_service__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: iap
#: model:iap.service,unit_name:iap.iap_service_reveal
msgid "Credits"
msgstr "Krediet"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__description
#: model:ir.model.fields,field_description:iap.field_iap_service__description
msgid "Description"
msgstr "Omschrijving"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
#: model:ir.model.fields,field_description:iap.field_iap_service__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_user_ids
msgid "Email Alert Recipients"
msgstr "Ontvangers van e-mailwaarschuwingen"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_threshold
msgid "Email Alert Threshold"
msgstr "Drempel e-mailwaarschuwing"

#. module: iap
#: model:iap.service,description:iap.iap_service_reveal
msgid ""
"Get quality leads and opportunities: convert your website visitors into "
"leads, generate leads based on a set of criteria and enrich the company data"
" of your opportunities."
msgstr ""
"Krijg kwalitatieve leads en kansen: zet je websitebezoekers om in leads, "
"genereer leads op basis van een set criteria en verrijk de bedrijfsgegevens "
"van je kansen."

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "IAP account"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "IAP accounts"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "IAP leadverrijking API"

#. module: iap
#: model:ir.model,name:iap.model_iap_service
msgid "IAP Service"
msgstr "IAP-service"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
#: model:ir.model.fields,field_description:iap.field_iap_service__id
msgid "ID"
msgstr "ID"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Insufficient credit to perform this service."
msgstr "Ontoereikend krediet om deze service uit te voeren."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__integer_balance
msgid "Integer Balance"
msgstr "Integer Saldo"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
#: model:ir.model.fields,field_description:iap.field_iap_service__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
msgid "Manage Service & Buy Credits"
msgstr "Dienst beheren & credits kopen"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__name
#: model:ir.model.fields,field_description:iap.field_iap_service__name
msgid "Name"
msgstr "Naam"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid ""
"One of the email alert recipients doesn't have an email address set. Users: "
"%s"
msgstr ""
"Een van de ontvangers van de e-mailwaarschuwing heeft geen e-mailadres "
"ingesteld. Gebruikers: %s"

#. module: iap
#: model:ir.model.constraint,message:iap.constraint_iap_service_unique_technical_name
msgid "Only one service can exist with a specific technical_name"
msgstr "Er kan maar één service bestaan met een specifieke technical_name"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid "Please set a positive email alert threshold."
msgstr "Stel een positieve waarschuwingsdrempel in voor e-mails."

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__registered
msgid "Registered"
msgstr "Geregistreerd"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_id
msgid "Service"
msgstr "Dienst"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_locked
msgid "Service Locked"
msgstr "Service Vergrendeld"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Start a Trial at Odoo"
msgstr "Start een proefperiode bij Odoo"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__state
msgid "State"
msgstr "Status"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
#: model:ir.model.fields,field_description:iap.field_iap_service__technical_name
msgid "Technical Name"
msgstr "Technische naam"

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"The request to the service timed out. Please contact the author of the app. "
"The URL it tried to contact was %s"
msgstr ""
"De aanvraag naar de service is verlopen. Neem contact op met de auteur van "
"de app. De URL die geprobeerd werd te benaderen was %s"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__unit_name
msgid "Unit Name"
msgstr "Eenheidsnaam"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__unregistered
msgid "Unregistered"
msgstr "Niet geregistreerd"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View My Services"
msgstr "Mijn diensten bekijken"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "Bekijk je IAP diensten en laad je credits op"
