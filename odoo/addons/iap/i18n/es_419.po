# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Buy Credit"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Comprar créditos"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "Información de la cuenta"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "Token de la cuenta"

#. module: iap
#: model:ir.model.fields,help:iap.field_iap_account__account_token
msgid ""
"Account token is your authentication key for this service. Do not share it."
msgstr ""
"El token de su cuenta es su clave de autenticación para este servicio. No lo"
" comparta."

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"An error occurred while reaching %s. Please contact Odoo support if this "
"error persists."
msgstr ""
"Ocurrió un error al comunicarse con %s, póngase en contacto con el soporte "
"de Odoo si este error persiste."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__balance
msgid "Balance"
msgstr "Balance"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__banned
msgid "Banned"
msgstr "Suspendido"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Buy credits"
msgstr "Comprar créditos"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Cancel"
msgstr "Cancelar"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "Empresa"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
#: model:ir.model.fields,field_description:iap.field_iap_service__create_date
msgid "Created on"
msgstr "Creado el"

#. module: iap
#: model:iap.service,unit_name:iap.iap_service_reveal
msgid "Credits"
msgstr "Créditos"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__description
#: model:ir.model.fields,field_description:iap.field_iap_service__description
msgid "Description"
msgstr "Descripción"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
#: model:ir.model.fields,field_description:iap.field_iap_service__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_user_ids
msgid "Email Alert Recipients"
msgstr "Destinatarios de la alerta por correo electrónico"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_threshold
msgid "Email Alert Threshold"
msgstr "Umbral de alerta por correo electrónico"

#. module: iap
#: model:iap.service,description:iap.iap_service_reveal
msgid ""
"Get quality leads and opportunities: convert your website visitors into "
"leads, generate leads based on a set of criteria and enrich the company data"
" of your opportunities."
msgstr ""
"Obtenga leads y oportunidades de calidad: convierta a los visitantes de su "
"sitio web en leads, genere leads basándose en un conjunto de criterios y "
"enriquezca los datos de la empresa de sus oportunidades."

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "Compras dentro de la aplicación"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "Cuenta para compras dentro de la aplicación"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "Cuentas para compras dentro de la aplicación"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "API de enriquecimiento de leads para compras dentro de la aplicación"

#. module: iap
#: model:ir.model,name:iap.model_iap_service
msgid "IAP Service"
msgstr "Servicio de compras dentro de la aplicación"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
#: model:ir.model.fields,field_description:iap.field_iap_service__id
msgid "ID"
msgstr "ID"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Insufficient credit to perform this service."
msgstr "Crédito insuficiente para ejecutar este servicio."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__integer_balance
msgid "Integer Balance"
msgstr "Saldo integro"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
#: model:ir.model.fields,field_description:iap.field_iap_service__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
msgid "Manage Service & Buy Credits"
msgstr "Gestionar servicio y comprar créditos"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__name
#: model:ir.model.fields,field_description:iap.field_iap_service__name
msgid "Name"
msgstr "Nombre"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Compras dentro de la aplicación de Odoo"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid ""
"One of the email alert recipients doesn't have an email address set. Users: "
"%s"
msgstr ""
"Uno de los destinatarios de la alerta de correo electrónico no tiene una "
"dirección de correo establecida. Usuarios: %s"

#. module: iap
#: model:ir.model.constraint,message:iap.constraint_iap_service_unique_technical_name
msgid "Only one service can exist with a specific technical_name"
msgstr "Solo puede existir un servicio con un nombre técnico específico"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid "Please set a positive email alert threshold."
msgstr "Configure un umbral positivo para la alerta"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__registered
msgid "Registered"
msgstr "Registrado"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_id
msgid "Service"
msgstr "Servicio"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_locked
msgid "Service Locked"
msgstr "Servicio bloqueado"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Start a Trial at Odoo"
msgstr "Comenzar una prueba en Odoo"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__state
msgid "State"
msgstr "Estado"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
#: model:ir.model.fields,field_description:iap.field_iap_service__technical_name
msgid "Technical Name"
msgstr "Nombre técnico"

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"The request to the service timed out. Please contact the author of the app. "
"The URL it tried to contact was %s"
msgstr ""
"Se agotó el tiempo de espera de la solicitud al servicio, contacte al autor "
"de la aplicación. La URL a la que intentó conectarse fue %s"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__unit_name
msgid "Unit Name"
msgstr "Nombre de la unidad"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__unregistered
msgid "Unregistered"
msgstr "Sin registrar"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View My Services"
msgstr "Ver mis servicios"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr ""
"Vea sus servicios de compras dentro de la aplicación y recargue sus créditos"
