# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Buy Credit"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Acheter du crédit"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "Informations du compte"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "Jeton du compte"

#. module: iap
#: model:ir.model.fields,help:iap.field_iap_account__account_token
msgid ""
"Account token is your authentication key for this service. Do not share it."
msgstr ""
"Le jeton du compte est votre clé d'authentification pour ce service. Ne le "
"partagez pas."

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"An error occurred while reaching %s. Please contact Odoo support if this "
"error persists."
msgstr ""
"Une erreur s'est produite en atteignant %s. Veuillez contacter l'assistance "
"Odoo si cette erreur persiste."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__balance
msgid "Balance"
msgstr "Solde"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__banned
msgid "Banned"
msgstr "Banni"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Buy credits"
msgstr "Acheter des crédits"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Cancel"
msgstr "Annuler"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "Société"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
#: model:ir.model.fields,field_description:iap.field_iap_service__create_date
msgid "Created on"
msgstr "Créé le"

#. module: iap
#: model:iap.service,unit_name:iap.iap_service_reveal
msgid "Credits"
msgstr "Crédits"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__description
#: model:ir.model.fields,field_description:iap.field_iap_service__description
msgid "Description"
msgstr "Description"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
#: model:ir.model.fields,field_description:iap.field_iap_service__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_user_ids
msgid "Email Alert Recipients"
msgstr "Destinataires des alertes e-mail"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_threshold
msgid "Email Alert Threshold"
msgstr "Seuil d'alerte e-mail"

#. module: iap
#: model:iap.service,description:iap.iap_service_reveal
msgid ""
"Get quality leads and opportunities: convert your website visitors into "
"leads, generate leads based on a set of criteria and enrich the company data"
" of your opportunities."
msgstr ""
"Obtenez des pistes et des opportunités de qualité : convertissez les "
"visiteurs de votre site web en pistes, générez des pistes sur la base d'un "
"ensemble de critères et enrichissez les données d'entreprise de vos "
"opportunités."

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "Compte IAP"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "Comptes IAP"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "API d'IAP Enrichissement de pistes"

#. module: iap
#: model:ir.model,name:iap.model_iap_service
msgid "IAP Service"
msgstr "Service IAP"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
#: model:ir.model.fields,field_description:iap.field_iap_service__id
msgid "ID"
msgstr "ID"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Insufficient credit to perform this service."
msgstr "Crédit insuffisant pour exécuter ce service."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__integer_balance
msgid "Integer Balance"
msgstr "Solde entiers"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
#: model:ir.model.fields,field_description:iap.field_iap_service__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
msgid "Manage Service & Buy Credits"
msgstr "Gérer les services et acheter des crédits"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__name
#: model:ir.model.fields,field_description:iap.field_iap_service__name
msgid "Name"
msgstr "Nom"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid ""
"One of the email alert recipients doesn't have an email address set. Users: "
"%s"
msgstr ""
"L'un des destinataires de l'alerte e-mail n'a pas d'adresse e-mail définie. "
"Utilisateurs :%s"

#. module: iap
#: model:ir.model.constraint,message:iap.constraint_iap_service_unique_technical_name
msgid "Only one service can exist with a specific technical_name"
msgstr ""
"Il ne peut y avoir qu'un seul service avec un nom technique spécifique"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid "Please set a positive email alert threshold."
msgstr "Veuillez fixer un seuil d'alerte positif par e-mail."

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__registered
msgid "Registered"
msgstr "Inscrit"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_id
msgid "Service"
msgstr "Service"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_locked
msgid "Service Locked"
msgstr "Service verrouillé"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Start a Trial at Odoo"
msgstr "Démarrer un essai avec Odoo"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__state
msgid "State"
msgstr "Statut"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
#: model:ir.model.fields,field_description:iap.field_iap_service__technical_name
msgid "Technical Name"
msgstr "Nom technique"

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"The request to the service timed out. Please contact the author of the app. "
"The URL it tried to contact was %s"
msgstr ""
"La demande de service a expiré. Veuillez contacter l'auteur de "
"l'application. L'URL qu'il a tenté de contacter était %s"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__unit_name
msgid "Unit Name"
msgstr "Nom de l'unité"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__unregistered
msgid "Unregistered"
msgstr "Non enregistré"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View My Services"
msgstr "Voir mes services"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "Consulter vos Services IAP et recharger vos crédits"
