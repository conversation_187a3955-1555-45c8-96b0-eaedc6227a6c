# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * iap
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-26 21:55+0000\n"
"PO-Revision-Date: 2017-10-10 11:35+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Spanish (Chile) (https://www.transifex.com/odoo/teams/41243/"
"es_CL/)\n"
"Language: es_CL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_info_id
msgid "Account Info"
msgstr ""

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
#: model:ir.model.fields,field_description:iap.field_iap_account_info__account_token
msgid "Account Token"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_info__account_uuid_hashed
msgid "Account UUID"
msgstr ""

#. module: iap
#: model:ir.model.fields,help:iap.field_iap_account__account_token
msgid ""
"Account token is your authentication key for this service. Do not share it."
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_info_ids
msgid "Accounts from IAP"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__balance
#: model:ir.model.fields,field_description:iap.field_iap_account_info__balance
msgid "Balance"
msgstr ""

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Buy Credit"
msgstr ""

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Buy credits"
msgstr ""

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Cancel"
msgstr "Cancelar"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "Compañía"

#. module: iap
#: model:ir.model,name:iap.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
#: model:ir.model.fields,field_description:iap.field_iap_account_info__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
#: model:ir.model.fields,field_description:iap.field_iap_account_info__create_date
msgid "Created on"
msgstr "Creado en"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__description
#: model:ir.model.fields,field_description:iap.field_iap_account_info__description
msgid "Description"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
#: model:ir.model.fields,field_description:iap.field_iap_account_info__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Hide Token"
msgstr ""

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr ""

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model:ir.model.fields,field_description:iap.field_iap_account_info__account_id
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr ""

#. module: iap
#: model:ir.model,name:iap.model_iap_account_info
msgid "IAP Account Info"
msgstr ""

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr ""

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
#: model:ir.model.fields,field_description:iap.field_iap_account_info__id
msgid "ID"
msgstr "ID (identificación)"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Insufficient credit to perform this service."
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
#: model:ir.model.fields,field_description:iap.field_iap_account_info__write_uid
msgid "Last Updated by"
msgstr "Última actualización de"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
#: model:ir.model.fields,field_description:iap.field_iap_account_info__write_date
msgid "Last Updated on"
msgstr "Última actualización en"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__name
msgid "Name"
msgstr ""

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr ""

#. module: iap
#: model:ir.actions.server,name:iap.open_iap_account
msgid "Open IAP Account"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_info__service_name
msgid "Related Service"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
msgid "Service Name"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__show_token
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Show Token"
msgstr ""

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Start a Trial at Odoo"
msgstr ""

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"The url that this service requested returned an error. Please contact the "
"author of the app. The url it tried to contact was %s"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_threshold
#: model:ir.model.fields,field_description:iap.field_iap_account_info__warning_threshold
msgid "Threshold"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account_info__unit_name
msgid "Unit Name"
msgstr ""

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View My Services"
msgstr ""

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warn_me
#: model:ir.model.fields,field_description:iap.field_iap_account_info__warn_me
msgid "Warn me"
msgstr ""

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_email
#: model:ir.model.fields,field_description:iap.field_iap_account_info__warning_email
msgid "Warning Email"
msgstr ""

#. module: iap
#: model:ir.model.fields,help:iap.field_iap_account__warn_me
msgid "We will send you an email when your balance gets below that threshold"
msgstr ""
