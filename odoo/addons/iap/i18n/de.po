# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Buy Credit"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Guthaben kaufen"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "Kontoinformationen"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "Kontentoken"

#. module: iap
#: model:ir.model.fields,help:iap.field_iap_account__account_token
msgid ""
"Account token is your authentication key for this service. Do not share it."
msgstr ""
"Das Kontotoken ist Ihr Authentifizierungsschlüssel für diesen Dienst. Geben "
"Sie ihn nicht weiter."

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"An error occurred while reaching %s. Please contact Odoo support if this "
"error persists."
msgstr ""
"Ein Fehler ist beim Aufrufen von %s aufgetreten. Bitte kontaktieren Sie den "
"Odoo-Support, wenn der Fehler weiterhin besteht."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__balance
msgid "Balance"
msgstr "Saldo"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__banned
msgid "Banned"
msgstr "Gesperrt"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Buy credits"
msgstr "Guthaben kaufen"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Cancel"
msgstr "Abbrechen"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "Unternehmen"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
#: model:ir.model.fields,field_description:iap.field_iap_service__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: iap
#: model:iap.service,unit_name:iap.iap_service_reveal
msgid "Credits"
msgstr "Guthaben"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__description
#: model:ir.model.fields,field_description:iap.field_iap_service__description
msgid "Description"
msgstr "Beschreibung"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
#: model:ir.model.fields,field_description:iap.field_iap_service__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_user_ids
msgid "Email Alert Recipients"
msgstr "Empfänger der E-Mail-Benachrichtigung"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_threshold
msgid "Email Alert Threshold"
msgstr "Grenze für E-Mail-Benachrichtigungen"

#. module: iap
#: model:iap.service,description:iap.iap_service_reveal
msgid ""
"Get quality leads and opportunities: convert your website visitors into "
"leads, generate leads based on a set of criteria and enrich the company data"
" of your opportunities."
msgstr ""
"Erhalten Sie hochwertige Leads und Verkaufschancen: Verwandeln Sie die "
"Besucher Ihrer Website in Leads, generieren Sie Leads anhand einer Reihe von"
" Kriterien und reichern Sie die Unternehmensdaten Ihrer Verkaufschancen an."

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "IAP-Konto"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "IAP-Konten"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "IAP-API für Lead-Anreicherung"

#. module: iap
#: model:ir.model,name:iap.model_iap_service
msgid "IAP Service"
msgstr "IAP-Service"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
#: model:ir.model.fields,field_description:iap.field_iap_service__id
msgid "ID"
msgstr "ID"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Insufficient credit to perform this service."
msgstr "Kein ausreichendes Guthaben für diesen Service"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__integer_balance
msgid "Integer Balance"
msgstr "Ganzzahlsaldo"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
#: model:ir.model.fields,field_description:iap.field_iap_service__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
msgid "Manage Service & Buy Credits"
msgstr "Service verwalten & Guthaben kaufen"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__name
#: model:ir.model.fields,field_description:iap.field_iap_service__name
msgid "Name"
msgstr "Name"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid ""
"One of the email alert recipients doesn't have an email address set. Users: "
"%s"
msgstr ""
"Einer der Empfänger de E-Mail-Benachrichtigungen hat keine E-Mail-Adresse "
"angegeben. Benutzer: %s"

#. module: iap
#: model:ir.model.constraint,message:iap.constraint_iap_service_unique_technical_name
msgid "Only one service can exist with a specific technical_name"
msgstr ""
"Es kann nur ein Service mit einem bestimmten technischen Namen existieren"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid "Please set a positive email alert threshold."
msgstr ""
"Bitte geben Sie eine positive Höchstanzahl für E-Mail-Benachrichtigungen "
"ein."

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__registered
msgid "Registered"
msgstr "Registriert"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_id
msgid "Service"
msgstr "Service"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_locked
msgid "Service Locked"
msgstr "Service gesperrt"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Start a Trial at Odoo"
msgstr "Starten Sie eine Testversion bei Odoo"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__state
msgid "State"
msgstr "Status"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
#: model:ir.model.fields,field_description:iap.field_iap_service__technical_name
msgid "Technical Name"
msgstr "Technische Bezeichnung"

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"The request to the service timed out. Please contact the author of the app. "
"The URL it tried to contact was %s"
msgstr ""
"Die Anfrage dieses Dienstes ist abgelaufen. Bitte kontaktieren Sie den Autor"
" der App. Die Url, die er zu kontaktieren versuchte, war %s."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__unit_name
msgid "Unit Name"
msgstr "Name der Einheit"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__unregistered
msgid "Unregistered"
msgstr "Nichtregistriert"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View My Services"
msgstr "Meine Services anzeigen"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "Zeigen Sie Ihre IAP-Services an und laden Sie Guthaben auf"
