# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* iap
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ka<PERSON><PERSON><PERSON><PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: O<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Buy Credit"
msgstr ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                    Osta krediittejä"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "Account Information"
msgstr "Tilitiedot"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__account_token
msgid "Account Token"
msgstr "Tilin tunniste"

#. module: iap
#: model:ir.model.fields,help:iap.field_iap_account__account_token
msgid ""
"Account token is your authentication key for this service. Do not share it."
msgstr ""
"Tilin pääsytunniste on tunnistusavaimesi tätä palvelua varten. Älä jaa sitä."

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"An error occurred while reaching %s. Please contact Odoo support if this "
"error persists."
msgstr ""
"Tavoitettaessa %s tapahtui virhe. Ota yhteyttä Odoo-tukeen, jos tämä virhe "
"jatkuu."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__balance
msgid "Balance"
msgstr "Saldo"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__banned
msgid "Banned"
msgstr "Estetty"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Buy credits"
msgstr "Osta krediittejä"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Cancel"
msgstr "Peruuta"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__company_ids
msgid "Company"
msgstr "Yritys"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__create_date
#: model:ir.model.fields,field_description:iap.field_iap_service__create_date
msgid "Created on"
msgstr "Luotu"

#. module: iap
#: model:iap.service,unit_name:iap.iap_service_reveal
msgid "Credits"
msgstr "Tekijät"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__description
#: model:ir.model.fields,field_description:iap.field_iap_service__description
msgid "Description"
msgstr "Kuvaus"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__display_name
#: model:ir.model.fields,field_description:iap.field_iap_service__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_user_ids
msgid "Email Alert Recipients"
msgstr "Sähköpostihälytyksen vastaanottajat"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__warning_threshold
msgid "Email Alert Threshold"
msgstr "Sähköpostihälytyksen kynnysarvo"

#. module: iap
#: model:iap.service,description:iap.iap_service_reveal
msgid ""
"Get quality leads and opportunities: convert your website visitors into "
"leads, generate leads based on a set of criteria and enrich the company data"
" of your opportunities."
msgstr ""
"Hanki laadukkaita liidejä ja myyntimahdollisuuksia: muunna verkkosivuston "
"kävijät liideiksi, luo liidejä tiettyjen kriteerien perusteella ja rikasta "
"tilaisuuksiesi yritystietoja."

#. module: iap
#: model:ir.ui.menu,name:iap.iap_root_menu
msgid "IAP"
msgstr "IAP"

#. module: iap
#: model:ir.actions.act_window,name:iap.iap_account_action
#: model:ir.model,name:iap.model_iap_account
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_form
msgid "IAP Account"
msgstr "IAP Tili"

#. module: iap
#: model:ir.ui.menu,name:iap.iap_account_menu
#: model_terms:ir.ui.view,arch_db:iap.iap_account_view_tree
msgid "IAP Accounts"
msgstr "IAP-tilit"

#. module: iap
#: model:ir.model,name:iap.model_iap_enrich_api
msgid "IAP Lead Enrichment API"
msgstr "IAP liidin rikastus API"

#. module: iap
#: model:ir.model,name:iap.model_iap_service
msgid "IAP Service"
msgstr "IAP-palvelu"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__id
#: model:ir.model.fields,field_description:iap.field_iap_service__id
msgid "ID"
msgstr "ID"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/xml/iap_templates.xml:0
msgid "Insufficient credit to perform this service."
msgstr "Krediitit eivät riitä tämän palvelun suorittamiseen."

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__integer_balance
msgid "Integer Balance"
msgstr "Kokonaisluku saldo"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_uid
#: model:ir.model.fields,field_description:iap.field_iap_service__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__write_date
#: model:ir.model.fields,field_description:iap.field_iap_service__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
msgid "Manage Service & Buy Credits"
msgstr "Hallitse palvelua ja osta krediittejä"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__name
#: model:ir.model.fields,field_description:iap.field_iap_service__name
msgid "Name"
msgstr "Nimi"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "Odoo IAP"
msgstr "Odoo IAP"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid ""
"One of the email alert recipients doesn't have an email address set. Users: "
"%s"
msgstr ""
"Yhdellä sähköpostihälytyksen vastaanottajista ei ole määritetty "
"sähköpostiosoitetta. Käyttäjät: %s"

#. module: iap
#: model:ir.model.constraint,message:iap.constraint_iap_service_unique_technical_name
msgid "Only one service can exist with a specific technical_name"
msgstr "Vain yksi palvelu voi olla olemassa tietyllä technical_name-nimellä"

#. module: iap
#. odoo-python
#: code:addons/iap/models/iap_account.py:0
msgid "Please set a positive email alert threshold."
msgstr "Aseta positiivinen sähköpostihälytyskynnys."

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__registered
msgid "Registered"
msgstr "Rekisteröitynyt"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_id
msgid "Service"
msgstr "Palvelu"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_locked
msgid "Service Locked"
msgstr "Palvelu lukittu"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/js/insufficient_credit_error_handler.js:0
msgid "Start a Trial at Odoo"
msgstr "Aloita kokeilu Odoo:ssa"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__state
msgid "State"
msgstr "Alue"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_account__service_name
#: model:ir.model.fields,field_description:iap.field_iap_service__technical_name
msgid "Technical Name"
msgstr "Tekninen nimi"

#. module: iap
#. odoo-python
#: code:addons/iap/tools/iap_tools.py:0
msgid ""
"The request to the service timed out. Please contact the author of the app. "
"The URL it tried to contact was %s"
msgstr ""
"Pyyntö palveluun päättyi. Ota yhteyttä sovelluksen tekijään. URL-osoite, "
"johon se yritti ottaa yhteyttä, oli %s"

#. module: iap
#: model:ir.model.fields,field_description:iap.field_iap_service__unit_name
msgid "Unit Name"
msgstr "Yksikön nimi"

#. module: iap
#: model:ir.model.fields.selection,name:iap.selection__iap_account__state__unregistered
msgid "Unregistered"
msgstr "Rekisteröimätön"

#. module: iap
#. odoo-javascript
#: code:addons/iap/static/src/action_buttons_widget/action_buttons_widget.xml:0
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View My Services"
msgstr "Näytä omat palveluni"

#. module: iap
#: model_terms:ir.ui.view,arch_db:iap.res_config_settings_view_form
msgid "View your IAP Services and recharge your credits"
msgstr "Tarkastele omia IAP-palveluja ja lataa krediittejä"
