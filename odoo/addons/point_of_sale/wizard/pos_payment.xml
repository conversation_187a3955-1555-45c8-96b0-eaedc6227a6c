<?xml version="1.0" encoding="utf-8"?>
<odoo>
        <record id="view_pos_payment" model="ir.ui.view">
            <field name="name">pos.make.payment.form</field>
            <field name="model">pos.make.payment</field>
            <field name="arch" type="xml">
            <form string="Pay Order">
                <group>
                    <field name="config_id" invisible="1" />
                    <field name="payment_method_id" domain="[('config_ids', 'in', config_id)]"/>
                    <field name="amount" />
                    <field name="payment_name"/>
                </group>
                <footer>
                    <button name="check" string="Make Payment" type="object" class="btn-primary" data-hotkey="q"/>
                    <button special="cancel" data-hotkey="x" string="Cancel" class="btn-secondary"/>
                </footer>
            </form>
            </field>
        </record>
        <record id="action_pos_payment" model="ir.actions.act_window">
            <field name="name">Payment</field>
            <field name="res_model">pos.make.payment</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
</odoo>
