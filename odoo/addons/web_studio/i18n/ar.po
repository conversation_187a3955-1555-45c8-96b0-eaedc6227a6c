# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_studio
# 
# Translators:
# <PERSON>, 2024
# Wil <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    This is your new action.\n"
"                </p>\n"
"                <p>By default, it contains a list and a form view and possibly\n"
"                    other view types depending on the options you chose for your model.\n"
"                </p>\n"
"                <p>\n"
"                    You can start customizing these screens by clicking on the Studio icon on the\n"
"                    top right corner (you can also customize this help message there).\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    هذا هو إجراؤك الجديد.\n"
"                </p>\n"
"                <p>يحتوي افتراضياً على طريقتي عرض القائمة والاستمارة وربما\n"
"                    أنواع عرض أخرى بناءً على الخيارات التي تقوم بتحديدها لنموذجك.\n"
"                </p>\n"
"                <p>\n"
"                    يمكنك البدء بتخصيص تلك الشاشات عن طريق الضغط على أيقونة الاستوديو في\n"
"                    الزاوية العليا إلى اليسار (بإمكانك أيضاً تخصيص رسالة المساعدة هنا).\n"
"                </p>\n"
"            "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid ""
"\n"
"    There are no many2one fields related to the current model.\n"
"    To create a one2many field on the current model, you must first create its many2one counterpart on the model you want to relate to.\n"
msgstr ""
"\n"
"    لا توجد حقول متعدد إلى واحد في النموذج الحالي.\n"
"    لإنشاء حقل واحد إلى متعدد في النموذج الحالي، عليك أولاً إنشاء نظيره، متعدد إلى واحد، في النموذج الذي ترغب في ربطه به.\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Add a new report\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                إضافة تقرير جديد\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new filter\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                إضافة عامل تصفية جديد\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid " until %s"
msgstr "حتى %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "\" failed."
msgstr "\" فشل. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s delegated approval rights to %(delegate_to)s"
msgstr "%(user_name)s تفويض صلاحيات منح الموافقات إلى %(delegate_to)s "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"%(user_name)s has set approval rights from %(previous_approvers)s to "
"%(next_approvers)s"
msgstr ""
"%(user_name)s قام بتغيير صلاحيات منح الموافقة من %(previous_approvers)s إلى "
"%(next_approvers)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s revoked their delegation to %(revoked_users)s"
msgstr "%(user_name)s قام بإلغاء تفويضه لـ %(revoked_users)s "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "%s Report"
msgstr "%s التقرير"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_export_model.py:0
msgid "%s record(s)"
msgstr "%s سجلات "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "<i role=\"img\" class=\"fa fa-user-times\" title=\"Exclusive approval\"/>"
msgstr "<i role=\"img\" class=\"fa fa-user-times\" title=\"الموافقة الحصرية \"/>"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new access control list\n"
"            </p>\n"
"            "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                إضافة قائمة تحكم بصلاحيات الوصول جديدة\n"
"            </p>\n"
"            "

#. module: web_studio
#: model_terms:web_tour.tour,rainbow_man_message:web_studio.web_studio_new_app_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>عمل رائع!</b> لقد تخطيت كافة مراحل هذه الجولة.</span>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "A field with the same name already exists."
msgstr "يوجد حقل بنفس الاسم."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_entry_uniq_combination
msgid "A rule can only be approved/rejected once per record."
msgstr "يمكن الموافقة على/رفض قاعدة مرة واحدة لكل سجل. "

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_together
msgid "A rule must apply to an action or a method (but not both)."
msgstr "يجب أن تنطبق القاعدة على إجراء أو طريقة (ولكن ليس الاثنين معاً). "

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_not_null
msgid "A rule must apply to an action or a method."
msgstr "يجب أن تنطبق القاعدة على إجراء أو طريقة. "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model__abstract
msgid "Abstract"
msgstr "تجريدي"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Access Control"
msgstr "التحكم في صلاحيات الوصول"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Access Control Lists"
msgstr "قوائم التحكم في الوصول"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_groups
msgid "Access Groups"
msgstr "مجموعات الوصول"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Access records from cell"
msgstr "الوصول إلى السجلات من الخلية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Access records from graph"
msgstr "الوصول إلى السجلات من الرسم البياني "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_mail_activity_type__category
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__action_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Action"
msgstr "إجراء"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window
msgid "Action Window"
msgstr "نافذة الإجراء"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "عرض نافذة الإجراء"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Action to approve:"
msgstr "الإجراء للموافقة عليه: "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Action to run"
msgstr "الإجراء المراد تشغيله "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Action's title"
msgstr "عنوان الإجراء"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Action: %s"
msgstr "الإجراء: %s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_actions
msgid "Actions"
msgstr "الإجراءات"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"قد تؤدي الإجراءات إلى سلوك معين مثل فتح طريقة عرض التقويم أو وضع علامة "
"\"تم\" تلقائياً عند تحميل مستند "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
msgid "Activate View"
msgstr "تفعيل أداة العرض"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__active
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Active"
msgstr "نشط"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#: model:ir.model,name:web_studio.model_mail_activity
msgid "Activity"
msgstr "النشاط"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_activity_type
msgid "Activity Type"
msgstr "نوع النشاط"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Activity view unavailable on this model"
msgstr "نافذة عرض النشاط غير متوفرة في هذا النموذج "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Add"
msgstr "إضافة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/chatter_container.xml:0
msgid "Add Chatter Widget"
msgstr "إضافة أداة الدردشة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add Picture"
msgstr "إضافة صورة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Add a Button"
msgstr "إضافة زر"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Add a button"
msgstr "إضافة زر "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add a pipeline status bar"
msgstr "إضافة شريط حالة مخطط سير العمل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add a priority"
msgstr "إضافة أولوية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Add an approval step"
msgstr "إضافة خطوة الموافقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add an avatar"
msgstr "إضافة صورة رمزية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Add details to your records with an embedded list view"
msgstr "قم بإضافة تفاصيل إلى سجلاتك مع نافذة عرض قائمة مضمنة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Add new value"
msgstr "إضافة قيمة جديدة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record at the bottom"
msgstr "إضافة سجل في الأسفل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record on top"
msgstr "إضافة سجل إلى الأعلى "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add tags"
msgstr "إضافة علامات تصنيف "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_export_data
msgid "Additional Export Data"
msgstr "بيانات التصدير الإضافية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Additional Fields"
msgstr "الحقول الإضافية "

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid "Additional Studio Exports"
msgstr "عمليات التصدير الإضافية للاستوديو "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_models
msgid "Additional models to export"
msgstr "النماذج الإضافية للتصدير "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_wizard__additional_models
msgid ""
"Additional models you may choose to export in addition to the Studio "
"customizations"
msgstr ""
"النماذج الإضافية التي يمكنك اختيار تصديرها بالإضافة إلى تخصيصات الاستوديو "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Aggregate"
msgstr "الإجمالي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "All Day"
msgstr "طوال اليوم"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"All changes done to the report's structure will be discarded and the report "
"will be reset to its factory settings."
msgstr ""
"سيتم إهمال كافة التغييرات التي تم إجراؤها إلي هيكل التقرير وستتم إعادة تعيين"
" التقرير إلى إعدادات المصنع. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"All set? You are just one click away from <b>generating your first app</b>."
msgstr "أأنت جاهز؟ أنت على بعد ضغطة زر من <b>إنشاء تطبيقك الأول</b>. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Allow Resequencing"
msgstr "السماح بإعادة التسلسل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Allow visibility to groups"
msgstr "السماح بالظهور للمجموعات "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "An approval is missing"
msgstr "توجد موافقة مفقودة "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Apply"
msgstr "تطبيق"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approval"
msgstr "الموافقة "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approval Entries"
msgstr "قيود الموافقة "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approval_group_id
msgid "Approval Group"
msgstr "مجموعة الموافقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approval Order"
msgstr "ترتيب الموافقات "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approval_rule_id
msgid "Approval Rule"
msgstr "قاعدة الموافقة "

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_approver
msgid "Approval Rule Approvers Enriched"
msgstr "تم إثراء مانحي الموافقات في قاعدة الموافقة "

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_delegate
msgid "Approval Rule Delegate"
msgstr "تفويض قاعدة منح الموافقات "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
msgid "Approval Rules"
msgstr "قواعد الموافقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approvals"
msgstr "الموافقات"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals %(model_name)s"
msgstr "الموافقات %(model_name)s "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals can only be done on a method or an action, not both."
msgstr "يمكن تطبيق الموافقات فقط على طريقة أو إجراء، وليس الاثنين معاً. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals missing"
msgstr "الموافقات مفقودة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approve"
msgstr "موافقة"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__approved
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approved"
msgstr "تمت الموافقة "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Approved <i class=\"fa fa-thumbs-up text-success\"/>"
msgstr "تمت الموافقة <i class=\"fa fa-thumbs-up text-success\"/> "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approved on"
msgstr "الموافقة في "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__user_id
msgid "Approved/rejected by"
msgstr "تمت الموافقة/الرفض من قِبَل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approver Group"
msgstr "مجموعة مانح الموافقات "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_log_ids
msgid "Approver Log"
msgstr "سجل مانح الموافقات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_ids
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approver_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Approvers"
msgstr "المُعتمِدين "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archive deprecated records"
msgstr "أرشفة السجلات المهلَكة "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Archived"
msgstr "مؤرشف"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archiving"
msgstr "الأرشفة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Are you sure you want to remove the selection values?"
msgstr "هل أنت متأكد من أنك ترغب في إزالة قيم الاختيار؟ "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
msgid "Are you sure you want to remove this %s from the view?"
msgstr "هل أنت متأكد من أنك تريد حذف %s من أداة العرض؟"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Are you sure you want to reset the background image?"
msgstr "هل أنت متأكد أنك تريد إعادة تعيين صورة الخلفية؟"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/edition_flow.js:0
msgid ""
"Are you sure you want to restore the default view?\r\n"
"All customization done with studio on this view will be lost."
msgstr ""
"هل أنت متأكد من أنك ترغب في استعادة طريقة العرض الافتراضية\r\n"
"ستخسر كافة التخصيصات التي قمت بها في طريقة العرض هذه بواسطة الاستوديو. "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__is_demo_data
msgid "As Demo"
msgstr "كعرض توضيحي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Ascending"
msgstr "تصاعدي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign a responsible to each record"
msgstr "تعيين مسؤول لكل سجل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign dates and visualize records in a calendar"
msgstr "قم بتعيين التواريخ وتصور السجلات في التقويم "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Attach a picture to a record"
msgstr "قم بإرفاق صورة بالسجل "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__include_attachment
msgid "Attachments"
msgstr "المرفقات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Autocompletion Fields"
msgstr "حقول الإكمال التلقائي"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_automation
msgid "Automation Rule"
msgstr "قاعدة الأتمتة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Automations"
msgstr "الأتمتة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Average"
msgstr "متوسط"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Average of %s"
msgstr "متوسط %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Awaiting approval"
msgstr "بانتظار الموافقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Backwards"
msgstr "للخلف"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Bar"
msgstr "الشريط "

#. module: web_studio
#: model:ir.model,name:web_studio.model_base
msgid "Base"
msgstr "قاعدة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Blank"
msgstr "فارغ"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Blocked"
msgstr "محجوب"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Bold"
msgstr "عريض"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Business header/footer"
msgstr "رأس/تذييل الأعمال"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.js:0
msgid "Button"
msgstr "زر"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Buttons Properties"
msgstr "خصائص الأزرار "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/aside_properties/aside_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/div_properties/div_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/footer_properties/footer_properties.xml:0
msgid "CSS style"
msgstr "شكل CSS "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Calendar"
msgstr "التقويم"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Call a method"
msgstr "Call a method"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Create"
msgstr "يمكنه إنشاء"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Delete"
msgstr "يمكنه حذف"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Edit"
msgstr "بإمكانه التحرير "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__can_validate
msgid "Can be approved"
msgstr "يمكن الموافقة عليه "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch 'create', 'write' and 'unlink'."
msgstr "Can't patch 'create', 'write' and 'unlink'."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch private methods."
msgstr "Can't patch private methods."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Cancel"
msgstr "إلغاء"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Categorize records with custom tags"
msgstr "قم بوضع السجلات في فئات باستخدام علامات التصنيف المخصصة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Change Background"
msgstr "تغيير الخلفية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Change Image"
msgstr "تغيير الصورة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Chatter"
msgstr "الدردشة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "CheckBox"
msgstr "صندوق الاختيار "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model_data__studio
msgid "Checked if it has been edited with Studio."
msgstr "يكون محددًا إذا تم تعديله من خلال الاستديو."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Choose an app name"
msgstr "اختر اسمًا للتطبيق"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Choose the name of the menu"
msgstr "اختر اسم القائمة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Churn"
msgstr "توقف العميل عن استخدام الخدمات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.xml:0
msgid "Class"
msgstr "التصنيف "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Click to edit messaging features on your model."
msgstr "اضغط هنا لتحرير خصائص الرسائل في نموذجك. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Click to see all approval rules."
msgstr "اضغط هنا لرؤية كافة قواعد الموافقة. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/navbar/navbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Close"
msgstr "إغلاق"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Cohort"
msgstr "جماعي "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Color"
msgstr "اللون"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Color Picker"
msgstr "محدد الألوان "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Column"
msgstr "العمود"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Column grouping"
msgstr "تجميع العمود "

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_company
msgid "Companies"
msgstr "الشركات"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Company"
msgstr "الشركة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_structures.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Components"
msgstr "المكونات"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Conditional"
msgstr "شرطي"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__conditional
msgid "Conditional Rule"
msgstr "قاعدة مشروطة "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Configuration"
msgstr "التهيئة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Configure Model"
msgstr "تهيئة النموذج "

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid ""
"Configure additional models to export with Studio, such as records that hold"
" configuration information or demo data."
msgstr ""
"قم بتهيئة نماذج إضافية للتصدير باستخدام الاستوديو، مثل السجلات التي تحتوي "
"على معلومات التهيئة أو البيانات التجريبية. "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Configure data and demo data to export"
msgstr "تهيئة البيانات والبيانات التجريبية لتصديرها "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Configure model"
msgstr "تهيئة النموذج "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Confirm"
msgstr "تأكيد"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Confirmation"
msgstr "التأكيد "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Contact"
msgstr "جهة الاتصال"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Contact Field"
msgstr "حقل جهة الاتصال "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid "Contact Field Required"
msgstr "حقل جهة الاتصال مطلوب "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Contact details"
msgstr "تفاصيل الاتصال "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Context"
msgstr "السياق "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Continue to configure some typical behaviors for your new type of object."
msgstr "الاستمرار بتهيئة بعض التصرفات التقليدية لنوع الكائن الجديد لديك. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Could not change the background"
msgstr "تعذر تغيير الخلفية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Create Menu"
msgstr "إنشاء القائمة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
msgid "Create Model"
msgstr "إنشاء النموذج "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Create a new Model"
msgstr "إنشاء نموذج جديد "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Create your <b>selection values</b> (e.g.: Romance, Polar, Fantasy, etc.)"
msgstr "إنشاء <b>قيمك الاختيارية</b> (مثلًا: رومانسية، تناقض، خيال، إلخ.)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your App"
msgstr "إنشاء تطبيقك"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your app"
msgstr "إنشاء تطبيقك"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your first menu"
msgstr "أنشئ قائمتك الأولى "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Create your menu"
msgstr "أنشئ قائمتك "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Creating this type of view is not currently supported in Studio."
msgstr "إنشاء هذا النوع من أدوات العرض غير مدعوم حاليًا في الاستديو."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Currency"
msgstr "العملة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Current model:"
msgstr "النموذج الحالي: "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Custom Configuration"
msgstr "التهيئة المخصصة "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_fields
msgid "Custom Fields"
msgstr "الحقول المخصصة "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_models
msgid "Custom Models"
msgstr "النماذج المخصصة "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_reports
msgid "Custom Reports"
msgstr "القارير المخصصة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Custom Sorting"
msgstr "الفرز المخصص "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_views
msgid "Custom Views"
msgstr "طرق العرض المخصصة "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Custom field names cannot contain double underscores."
msgstr "لا يمكن أن تحتوي أسماء الحقول المخصصة على شرطات سفلية مزدوجة. "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom fields:"
msgstr "الحقول المخصصة: "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom models:"
msgstr "النماذج المخصصة: "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom reports:"
msgstr "التقارير المخصصة: "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom views:"
msgstr "طرق العرض المخصصة: "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Customization made with Studio will be permanently lost"
msgstr "ستضيع التخصيصات التي يتم تنفيذها بواسطة الاستوديو بشكل دائم "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "DIFF"
msgstr "DIFF"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Date"
msgstr "التاريخ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date & Calendar"
msgstr "التاريخ والتقويم "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__date_to
msgid "Date To"
msgstr "التاريخ إلى"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date range & Gantt"
msgstr "مجال التاريخ وغانت "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Datetime"
msgstr "التاريخ والوقت"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Day"
msgstr "اليوم"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Day Precision"
msgstr "دقة اليوم "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Decimal"
msgstr "عشري "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Default Display Mode"
msgstr "طريقة العرض الافتراضية"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__default_export_data
msgid "Default Export Data"
msgstr "بيانات التصدير الافتراضية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Default Group By"
msgstr "خاصية التجميع حسب الافتراضية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Group by"
msgstr "تجميع افتراضي حسب"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Scale"
msgstr "ميزان افتراضي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: model:ir.model,name:web_studio.model_ir_default
msgid "Default Values"
msgstr "القيم الافتراضية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Default value"
msgstr "القيمة الافتراضية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Default view"
msgstr "أداة العرض الافتراضية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Define start/end dates and visualize records in a Gantt chart"
msgstr "قم بتحديد تواريخ البدء/الانتهاء وتصور السجلات في مخطط غانت "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__updatable
msgid "Defines if the records would be updated during a module update."
msgstr "يحدد ما إذا كان سيتم تحديث السجلات أثناء تحديث التطبيق أم لا. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__notification_order
msgid "Defines the sequential order in which the approvals are requested."
msgstr "يحدد الترتيب التسلسلي الذي يتم فيه طلب الموافقات. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Delay Field"
msgstr "حقل التأخير"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Delegate"
msgstr "التفويض "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Delegate to"
msgstr "التفويض إلى "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Delete"
msgstr "حذف"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__is_demo_data
msgid "Demo"
msgstr "النسخة التجريبية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Dense"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Descending"
msgstr "تنازلي "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message
msgid "Description"
msgstr "الوصف"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "Design your Icon"
msgstr "صمم أيقونتك"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Disable View"
msgstr "تعطيل نافذة العرض "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Discard"
msgstr "إهمال "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Discard changes"
msgstr "إهمال التغييرات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Mode"
msgstr "طريقة العرض "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Total row"
msgstr "عرض صف المجموع "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Unavailability"
msgstr "عرض عدم التوافر "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Displays a textual hint that helps the user when the field is empty."
msgstr "يقوم بعرض تلميح نصي يساعد المستخدم عندما يكون الحقل فارغاً. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "Do you want to add a dropdown with colors?"
msgstr "أتود إضافة قائمة منسدلة ملونة؟"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__domain
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__domain
msgid "Domain"
msgstr "النطاق"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Done"
msgstr "منتهي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Drag & drop <b>another field</b>. Let's try with a <i>selection field</i>."
msgstr "قم بسحب وإفلات <b>حقل آخر</b>. فلنجرب مع <i>حقل اختيار</i>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Drag a menu to the right to create a sub-menu"
msgstr "اسحب القائمة إلى اليسار لإنشاء قائمة فرعية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.js:0
msgid "Dropdown"
msgstr "منسدلة"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Duplicate"
msgstr "إنشاء نسخة مطابقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Dynamic Table"
msgstr "جدول ديناميكي "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Edit"
msgstr "تحرير"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_content_overlay.js:0
msgid "Edit %s view"
msgstr "تحرير نافذة عرض %s "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
msgid "Edit Application Icon"
msgstr "تحرير أيقونة التطبيق "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/menu_properties/menu_properties.xml:0
msgid "Edit Color Picker"
msgstr "تحرير محدد الألوان "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit Menu"
msgstr "تحرير القائمة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Edit Values"
msgstr "تحرير القيم "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit selection"
msgstr "تحرير الاختيار "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Edit sources"
msgstr "تحرير المصادر "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.js:0
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""
"لا يحبذ تحرير ملف موجود كجزء من أداة التحرير هذه، حيث أن ذلك سيمنعه من أن "
"يتم تحديثه عند تحديثات التطبيق المستقبلية. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Editing item:"
msgstr "تحرير العنصر: "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Email Alias"
msgstr "لقب البريد الإلكتروني"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_template
msgid "Email Templates"
msgstr "قوالب البريد الإلكتروني "

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_thread
msgid "Email Thread"
msgstr "المحادثة البريدية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Empty List Message"
msgstr "رسالة قائمة فارغة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Enable Mass Editing"
msgstr "تمكين التحرير الجماعي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Enable Routing"
msgstr "تمكين المسارات "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entry_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Entries"
msgstr "القيود"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Error"
msgstr "خطأ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error message:"
msgstr "رسالة الخطأ: "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error name:"
msgstr "اسم الخطأ: "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__exclusive_user
msgid "Exclusive Approval"
msgstr "الموافقة الحصرية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Existing Fields"
msgstr "الحقول الموجودة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Existing Model"
msgstr "النموذج الموجود "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Export"
msgstr "تصدير"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "External"
msgstr "خارجي"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_xmlid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__xmlid
msgid "External ID"
msgstr "معرف خارجي"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Fadeout Speed"
msgstr "سرعة التلاشي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Fast"
msgstr "سريع"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Favourites"
msgstr "المُفضلة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Field"
msgstr "حقل"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Field Properties"
msgstr "خصائص الحقل"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.js:0
msgid "Field properties: %s"
msgstr "خصائص الحق: %s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_fields
msgid "Fields"
msgstr "الحقول"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__excluded_fields
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
msgid "Fields to exclude"
msgstr "الحقول المراد استثناؤها "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "File"
msgstr "الملف"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Filename for %s"
msgstr "اسم الملف لـ%s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Filter"
msgstr "عامل التصفية "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Filter Rules"
msgstr "قواعد عوامل التصفية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Filter label"
msgstr "عنوان عامل التصفية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: model:ir.model,name:web_studio.model_ir_filters
msgid "Filters"
msgstr "عوامل التصفية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "First Status"
msgstr "الحالة الأولى"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "First dimension"
msgstr "البعد الأول "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Footer"
msgstr "التذييل"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"For compatibility purpose with base_automation,approvals on 'create', "
"'write' and 'unlink' methods are forbidden."
msgstr ""
"في سبيل التوافق مع base_automation، لا يُسمح بالموافقة على طرق 'create'و  "
"'write' و 'unlink'. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Forbid visibility to groups"
msgstr "منع الظهور للمجموعات "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Forever"
msgstr "للأبد"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Form"
msgstr "الاستمارة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Format"
msgstr "التنسيق "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Forward"
msgstr "للأمام"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Gantt"
msgstr "جانت"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "General views"
msgstr "نوافذ العرض العامة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.js:0
msgid "Generate %s View"
msgstr "إنشاء أداة عرض %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Get contact, phone and email fields on records"
msgstr ""
"احصل على حقول لجهات الاتصال، رقم الهاتف، والبريد الإلكتروني في السجلات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Go back"
msgstr "العودة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Go on, you are almost done!"
msgstr "استمر، أنت على وشك الانتهاء! "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Good job! To add more <b>fields</b>, come back to the <i>Add tab</i>."
msgstr "أحسنت! لإضافة <b>حقول</b> أكثر، عد لاختيار <i>إضافة تبويب</i>."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: model:ir.model.fields.selection,name:web_studio.selection__mail_activity_type__category__grant_approval
#: model:mail.activity.type,name:web_studio.mail_activity_data_approve
msgid "Grant Approval"
msgstr "منح الموافقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Graph"
msgstr "الرسم البياني"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Great !"
msgstr "رائع! "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_renderer.xml:0
msgid "Group"
msgstr "المجموعة"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Group By"
msgstr "تجميع حسب"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Group by"
msgstr "التجميع حسب "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Grouping is not applied while in Studio to allow editing."
msgstr "لا يتم تطبيق التجميع بينما تستخدم الاستوديو للسماح بالتحرير. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "HTML"
msgstr "HTML"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_http
msgid "HTTP Routing"
msgstr "مسار HTTP"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Day"
msgstr "نصف يوم"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Hour"
msgstr "نصف ساعة "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"Header and footer are shared with other reports which may be impacted by the"
" reset."
msgstr "التذييل والترويسة مشتركتان مع تقارير أخرى قد تتأثر بإعادة التعيين. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Help Tooltip"
msgstr "تلميحة للمساعدة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Here, you can <b>name</b> your field (e.g. Book reference, ISBN, Internal "
"Note, etc.)."
msgstr ""
"من هنا يمكنك <b>تسمية</b> حقلك (مثال: الرقم المرجعي للكتاب، الرقم الدولي "
"المعياري للكتاب ، ملاحظة داخلية، إلخ). "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Address"
msgstr "إخفاء العنوان "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Name"
msgstr "إخفاء الاسم "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Hide by default"
msgstr "الإخفاء افتراضياً "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "High Priority"
msgstr "أولوية عالية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Highlight Color Field"
msgstr "إبراز حقل اللون "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_res_company__background_image
msgid "Home Menu Background Image"
msgstr "صورة خلفية القائمة الرئيسية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Hour"
msgstr "ساعة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to <b>name</b> your app? Library, Academy, …?"
msgstr "بم تريد <b>تسمية</b> تطبيقك؟ المكتبة، الأكاديمية، ...؟"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to name your first <b>menu</b>? My books, My courses?"
msgstr "بم تريد تسمية <b>قائمتك</b> الأولى؟ كتبي، دوراتي؟"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"I bet you can <b>build an app</b> in 5 minutes. Ready for the challenge?"
msgstr "أراهن أنه بإمكانك <b>إنشاء تطبيق</b> في 5 دقائق. جاهز للتحدي؟ "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__id
msgid "ID"
msgstr "المُعرف"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "IDs"
msgstr "المعرّفات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Icon"
msgstr "الأيقونة"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__include_attachment
msgid ""
"If set, the attachments related to the exported records will be included in "
"the export."
msgstr ""
"إذا تم تعيينها، سيتم تضمين المرفقات المتعلقة بالسجلات التي تم تصديرها في "
"عملية التصدير. "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__is_demo_data
msgid ""
"If set, the exported records will be considered as demo data during the "
"import."
msgstr ""
"إذا تم تعيينها، سيتم اعتبار السجلات المصدرة كبيانات تجريبية أثناء عملية "
"الاستيراد. "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__domain
msgid "If set, the rule will only apply on records that match the domain."
msgstr "إذا كان محدداً، ستنطبق القاعدة فقط على السجلات التي تطابق النطاق. "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__exclusive_user
msgid ""
"If set, the user who approves this rule will not be able to approve other "
"rules for the same record"
msgstr ""
"إذا كان محدداً، لن يتمكن المستخدم الذي يوافق على هذه القاعدة على الموافقة "
"على القواعد الأخرى لنفس السجل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"إذا قمت بتجاهل التعديلات الحالية، سوف تضيع كافة التغييرات غير المحفوظة. "
"بإمكانك إلغاء الإجراء للعودة إلى وضع التحرير. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"If you don't want to create a new model, an existing model should be "
"selected."
msgstr "إذا كنت لا ترغب في إنشاء نموذج جديد، يجب تحديد نموذج جديد. "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Image"
msgstr "صورة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Import"
msgstr "استيراد"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "In Progress"
msgstr "قيد التنفيذ"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_additional_data
msgid "Include Data"
msgstr "تضمين البيانات "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_demo_data
msgid "Include Demo Data"
msgstr "تضمين البيانات التجريبية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Include header and footer"
msgstr "تضمين الترويسة والتذييل "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_ui_menu__is_studio_configuration
msgid ""
"Indicates that this menu was created by Studio to hold configuration sub-"
"menus"
msgstr ""
"يشير إلى أن هذه القائمة قد تم إنشاؤها بواسطة الاستوديو لتحمل القوائم الفرعية"
" للتهيئة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field"
msgstr "إدراج حقل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field..."
msgstr "إدراج حقل... "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a table based on a relational field."
msgstr "قم بإدراج جدول بناءً على حقل العلاقة. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert left"
msgstr "إدراج في اليسار "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert right"
msgstr "إدراج في اليمين "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Integer"
msgstr "عدد صحيح"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Internal"
msgstr "داخلي"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Interval"
msgstr "الفترة"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Invalid studio_approval %s in button"
msgstr "studio_approval %sغير صالح  في الزر "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Invisible"
msgstr "مخفي"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__is_delegation
msgid "Is Delegation"
msgstr "تفويض "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__is_studio
msgid "Is Studio"
msgstr "الاستوديو "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Is default view"
msgstr "هي أداة العرض الافتراضية"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "It lacks a method to check."
msgstr "يفتقر إلى طريقة للتحقق. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Kanban"
msgstr "كانبان"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__kanban_color
msgid "Kanban Color"
msgstr "لون كانبان "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Kanban State"
msgstr "حالة كانبان"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/group_properties/group_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/page_properties/page_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Label"
msgstr "بطاقة عنوان"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Label of the button"
msgstr "عنوان الزر "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "Leave DIFF"
msgstr "Leave DIFF"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_leave_with
msgid "Leave Studio with another action"
msgstr "مغادرة الاستوديو مع إجراء آخر "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Let's check the result. Close Odoo Studio to get an <b>overview of your "
"app</b>."
msgstr "فلنرَ النتيجة. أغلق استديو أودو لإلقاء <b>نظرة عامة على تطبيقك</b>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Limit visibility to groups"
msgstr "قصر الظهور للمجموعات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Line"
msgstr "البند "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Lines"
msgstr "البنود"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__mail_activity_id
msgid "Linked Activity"
msgstr "النشاط المرتبط"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "List"
msgstr "القائمة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Manually sort records in the list view"
msgstr "فرز السجلات في عرض القائمة يدوياً "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2Many"
msgstr "علاقة متعدد لمتعدد"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2One"
msgstr "علاقة متعدد لواحد"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Map"
msgstr "الخريطة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid ""
"Map views are based on the address of a linked Contact. You need to have a "
"Many2one field linked to the res.partner model in order to create a map "
"view."
msgstr ""
"نوافذ عرض الخريطة مبنية على عنوان جهة الاتصال المرتبطة. يجب أن يكون لديك حقل"
" Many2one مرتبط بنموذج res.partner حتى تتمكن من إنشاء نافذة عرض للخريطة. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Measure"
msgstr "المقياس"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Measure Field"
msgstr "حقل القياس"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Measures"
msgstr "المقاييس"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Medium"
msgstr "متوسط "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_menu
msgid "Menu"
msgstr "القائمة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Message"
msgstr "الرسالة"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__method
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__method
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Method"
msgstr "الطريقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Method to run"
msgstr "الطريقة التي يجب استخدامها "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Method: %s"
msgstr "الطريقة: %s "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Minimal header/footer"
msgstr "أقل ترويسة/تذييل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Mode"
msgstr "الوضع"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Model"
msgstr "النموذج "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/wizard/studio_export_wizard.py:0
msgid "Model '%s' should not contain records with the same ID."
msgstr "يجب ألا يحتوي النموذج '%s' على سجلات لها نفس المعرف."

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_access
msgid "Model Access"
msgstr "الوصول للنموذج "

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_data
msgid "Model Data"
msgstr "بيانات النموذج "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model_name
msgid "Model Description"
msgstr "وصف النموذج "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__model
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_name
msgid "Model Name"
msgstr "اسم النموذج "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Model name"
msgstr "اسم الموديل "

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model
msgid "Models"
msgstr "النماذج"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Modified by:"
msgstr "تم التعديل بواسطة: "

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_module_module
msgid "Module"
msgstr "التطبيق "

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "إلغاء تثبيث التطبيق "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Monetary"
msgstr "نقدي"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Monetary value"
msgstr "قيمة نقدية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month"
msgstr "الشهر"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month (expanded)"
msgstr "الشهر (مع التفاصيل) "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Month Precision"
msgstr "دقة الشهر "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "More"
msgstr "المزيد "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Multine Text"
msgstr "نص متعدد الأسطر"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Multiple records views"
msgstr "نوافذ عرض للسجلات المتعددة "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "My %s"
msgstr "%s الخاص بي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "My Button"
msgstr "زري"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "My entries"
msgstr "قيودي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "N/A"
msgstr "غير منطبق "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__name
msgid "Name"
msgstr "الاسم"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New"
msgstr "جديد"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "New %s"
msgstr "جديد %s "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
msgid "New App"
msgstr "تطبيق جديد "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "New Approval Entry"
msgstr "قيد موافقة جديد "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "New Field"
msgstr "حقل جديد"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "New Fields"
msgstr "حقول جديدة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "New Filter"
msgstr "عامل تصفية جديد "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New Lines"
msgstr "أسطر جديدة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "New Menu"
msgstr "قائمة جديدة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "New Model"
msgstr "نموذج جديد "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "New Page"
msgstr "صفحة جديدة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "New button"
msgstr "زر جديد "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Next"
msgstr "التالي"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Nicely done! Let's build your screen now; <b>drag</b> a <i>text field</i> "
"and <b>drop</b> it in your view, on the right."
msgstr ""
"أحسنت! فلنقم ببناء شاشتك الآن؛ قم <b>بسحب</b> <i>حقلٍ نصي</i> و<b>إفلاته</b>"
" في أداة العرض إلى اليسار. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "No aggregation"
msgstr "بلا تجميع "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "No approval found for this rule, record and user combination."
msgstr ""
"لم يتم العثور على موافقة لهذه القاعدة، أو المزيج بين السجل والمستخدم. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "No header/footer"
msgstr "لا توجد ترويسة/تذييل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "No related many2one fields found"
msgstr "لم يتم العثور على حقول متعدد إلى واحد ذات صلة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "None"
msgstr "لا شيء"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Notes"
msgstr "الملاحظات"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__users_to_notify
msgid "Notify to"
msgstr "إخطار "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now you're on your own. Enjoy your <b>super power</b>."
msgstr "الآن ستعمل وحدك. استمتع بـ<b>قوتك الخارقة</b>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now, customize your icon. Make it yours."
msgstr "الآن، قم بتخصيص أيقونتك. شكلها حسب شخصيتك. "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entries_count
msgid "Number of Entries"
msgstr "عدد القيود"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Odoo Studio"
msgstr "استديو أودو"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "On view (ir.ui.view):"
msgstr "في نافذة العرض (ir.ui.view): "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "One2Many"
msgstr "علاقة واحد لمتعدد"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"Only groups with an external ID can be used here. Please choose another "
"group or assign manually an external ID to this group."
msgstr ""
"فقط المجموعات التي لها معرف خارجي يمكن استخدامها هنا. يرجى اختيار مجموعة "
"أخرى أو تعيين معرف خارجي لهذه المجموعة يدوياً. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Open Record On Click"
msgstr " فتح السجل عند الضغط "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Open form view"
msgstr "فتح نافذة عرض الاستمارة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Open kanban view of approvals"
msgstr "فتح نافذة عرض كانبان للموافقات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Optional"
msgstr "اختياري "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Order"
msgstr "الطلب"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "PDF file"
msgstr "ملف PDF"

#. module: web_studio
#: model:ir.model,name:web_studio.model_report_paperformat
msgid "Paper Format Config"
msgstr "تهيئة تنسيق الورقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Paper format"
msgstr "تنسيق الورقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Parent Menu"
msgstr "القائمة الأساسية  "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Parent View (inherit_id)"
msgstr "نافذة العرض الرئيسية (inherit_id) "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Partner"
msgstr "الشريك"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Phone"
msgstr "رقم الهاتف"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Picture"
msgstr "صورة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Pie"
msgstr "مخطط دائري "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Pipeline stages"
msgstr "مراحل مخطط سير العمل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Pipeline status bar"
msgstr "شريط حالة مخطط سير العمل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Pivot"
msgstr "محور"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Placeholder"
msgstr "العنصر النائب "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Please specify a field."
msgstr "يرجى تحديد حقل. "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__post
msgid "Post"
msgstr "منشور "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__pre
msgid "Pre"
msgstr "قبل "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Preset"
msgstr "معد مسبقاً "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.xml:0
msgid "Preview is not available"
msgstr "المعاينة غير متاحة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Previous"
msgstr "السابق"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Print preview"
msgstr "معاينة الطباعة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Priority"
msgstr "الأولوية"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Private methods cannot be restricted (since they cannot be called remotely, "
"this would be useless)."
msgstr ""
"لا يمكن تقييد الطرق الخاصة (بما أنه لا يمكن استدعاؤها عن بعد، سيكون ذلك بلا "
"فائدة). "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Properties"
msgstr "الخصائص "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Quarter Hour"
msgstr "ربع ساعة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
msgid "Quick Create"
msgstr "إنشاء سريع"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Quick Create Field"
msgstr "حقل إنشاء سريع "

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Effect"
msgstr "تأثير قوس قزح"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Man"
msgstr "رجل قوس قزح"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Readonly"
msgstr "للقراءة فقط"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Ready"
msgstr "جاهز"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Record"
msgstr "السجل"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__res_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__res_id
msgid "Record ID"
msgstr "معرف السجل"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__name
msgid "Record Name"
msgstr "اسم السجل "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__records_count
msgid "Records"
msgstr "السجلات"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Redo"
msgstr "إعادة تنفيذ "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__reference
msgid "Reference"
msgstr "الرقم المرجعي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Reject"
msgstr "رفض"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Rejected"
msgstr "تم الرفض "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Rejected <i class=\"fa fa-thumbs-down text-danger\"/>"
msgstr "تم الرفض <i class=\"fa fa-thumbs-down text-danger\"/> "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Rejected on"
msgstr "تم الرفض في "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Related Field"
msgstr "الحقل ذو الصلة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reload from attachment"
msgstr "إعادة التحميل من المرفقات"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
msgid "Remove from View"
msgstr "إزالة من العرض "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Remove rule"
msgstr "إزالة القاعدة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Remove selection"
msgstr "إزالة الاختيار "

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_report
msgid "Report Action"
msgstr "إجراء التقرير"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report Tools"
msgstr "أدوات إعداد التقارير "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Report edition failed"
msgstr "فشلت عملية تحرير التقرير "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Report name"
msgstr "اسم التقرير "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report preview not available"
msgstr "معاينة التقرير غير متاحة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Reporting views"
msgstr "نوافذ عرض إعداد التقارير "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Reports"
msgstr "التقارير"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Required"
msgstr "مطلوب"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__res_id
msgid "Res"
msgstr "Res"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Reset Default Background"
msgstr "إعادة تعيين الخلفية الافتراضية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Reset Image"
msgstr "إعادة تعيين الصورة"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.reset_view_arch_wizard_view
msgid "Reset View"
msgstr "إعادة تعيين العرض "

#. module: web_studio
#: model:ir.model,name:web_studio.model_reset_view_arch_wizard
msgid "Reset View Architecture Wizard"
msgstr "معالج إعادة تعيين بنية العرض "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reset report"
msgstr "إعادة تعيين التقرير "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Responsible"
msgstr "المسؤول "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Restore Default View"
msgstr "استعادة طريقة العرض الافتراضية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Restrict a record to a specific company"
msgstr "تقييد سجل لشركة محددة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Retention"
msgstr "الاحتفاظ "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Revoke"
msgstr "إلغاء "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Ribbon"
msgstr "شريط "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - First level"
msgstr "تجميع الصفوف - المستوى الأول "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - Second level"
msgstr "تجميع الصفوف - المستوى الثاني "

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_rule
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__rule_id
msgid "Rule"
msgstr "القاعدة "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be deleted since it would delete existing"
" approval entries. You should archive the rule instead."
msgstr ""
"لا يمكن حذف القواعد مع القيود الموجودة بالفعل حيث أن ذلك سيؤدي إلى حذف قيود "
"الموافقة الموجودة بالفعل. قم بأرشفة القاعدة عوضاً عن ذلك. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be modified since it would break existing"
" approval entries. You should archive the rule and create a new one instead."
msgstr ""
"لا يمكن تعديل القواعد مع القيود الموجودة بالفعل حيث أن ذلك سيؤدي إلى كسر "
"قيود الموافقة الموجودة بالفعل. قم بأرشفة القاعدة وإنشاء قاعدة جديدة عوضاً عن"
" ذلك. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Run a Server Action"
msgstr "تشغيل إجراء الخادم "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Save"
msgstr "حفظ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Save."
msgstr "حفظ."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saved"
msgstr "تم الحفظ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saving"
msgstr "جاري الحفظ "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Saving both some report's parts and full xml is not permitted."
msgstr "لا يُسمح بحفظ بعض أجزاء التقرير وملف xml كامل في الوقت ذاته. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Saving the report \""
msgstr "حفظ التقرير \""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Search"
msgstr "بحث"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Search..."
msgstr "بحث..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Second Status"
msgstr "الحالة الثانية"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Second dimension"
msgstr "البعد الثاني "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "See what changes have been made to this view"
msgstr "ألقِ نظرة على التغييرات التي قد تم إجراؤها على نافذة العرض هذه "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "Select a Field"
msgstr "اختر حقلًا"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select a group..."
msgstr "اختر مجموعة... "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Select a related field"
msgstr "اختر حقلًا مرتبطًا"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "Select a related field."
msgstr "اختر حقلًا مرتبطًا."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Select group"
msgstr "اختر مجموعة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
msgid ""
"Select the contact field to use to get the coordinates of your records."
msgstr "اختر حقل جهة الاتصال لاستخدامه للحصول على إحداثيات سجلاتك. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the model in relation to this one"
msgstr "قم بتحديد النموذج المتعلق بهذا "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the reciprocal ManyToOne field"
msgstr "قم بتحديد حقل متعدد إلى واحد المتجاوب "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select users..."
msgstr "اختر المستخدمين... "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Selection"
msgstr "قائمة خيارات"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Send a"
msgstr "إرسال"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Send messages, log notes and schedule activities"
msgstr "قم بإرسال الرسائل، تسجيل الملاحظات، وجدولة الأنشطة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Separator"
msgstr "الفاصل"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_server
msgid "Server Action"
msgstr "إجراء الخادم "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Set As Default"
msgstr "التعيين كافتراضي "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.xml:0
msgid "Set Cover Image"
msgstr "تعيين صورة غلاف"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Set a price or cost on records"
msgstr "قم بتعيين سعر أو تكلفة في السجلات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Set an <b>email alias</b>. Then, try to send an email to this address; it "
"will create a document automatically for you. Pretty cool, huh?"
msgstr ""
"عيّن <b>لقباً للبريد الإلكتروني</b>. ثم جرب إرسال بريد إلكتروني لهذا "
"العنوان؛ سيقوم ذلك بإنشاء مستند تلقائياً. رائع، أليس كذلك؟ "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Show Invisible Elements"
msgstr "إظهار العناصر الخفية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Show Traceback"
msgstr "إظهار تقرير تتبع الكود "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Show by default"
msgstr "الإظهار افتراضياً "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Show in print menu"
msgstr "إظهار في قائمة الطباعة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Show link to record"
msgstr "إظهار رابط السجل "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Side panel"
msgstr "اللوحة الجانبية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Signature"
msgstr "التوقيع"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Slow"
msgstr "بطيء"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "Some approvals are missing"
msgstr "بعض الموافقات غير موجودة "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Some records were skipped because approvals were missing to"
"                                    proceed with your request: "
msgstr "تم تخطي بعض السجلات لعدم وجود الموافقات للاستمرار في طلبك: "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Sort By"
msgstr "الفرز حسب "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Sort by"
msgstr "الفرز حسب "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Sorting"
msgstr "الفرز "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Sparse"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Specify all possible values"
msgstr "تحديد كافة القيم الممكنة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Stacked graph"
msgstr "رسم بياني مكدس "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Stage"
msgstr "المرحلة"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Stage Name"
msgstr "اسم المرحلة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Stage and visualize records in a custom pipeline"
msgstr "قم باختبار وتصور السجلات في مخطط سير عمل مخصص "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Start Date"
msgstr "تاريخ البدء "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Start Date Field"
msgstr "حقل تاريخ البداية"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__notification_order
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Step"
msgstr "خطوة"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__1
msgid "Step 1"
msgstr "خطوة 1 "

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__2
msgid "Step 2"
msgstr "خطوة 2 "

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__3
msgid "Step 3"
msgstr "خطوة 3 "

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__4
msgid "Step 4"
msgstr "خطوة 4 "

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__5
msgid "Step 5"
msgstr "خطوة 5 "

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__6
msgid "Step 6"
msgstr "خطوة 6 "

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__7
msgid "Step 7"
msgstr "خطوة 7 "

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__8
msgid "Step 8"
msgstr "خطوة 8 "

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__9
msgid "Step 9"
msgstr "خطوة 9 "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Stop Date Field"
msgstr "حقل تاريخ النهاية"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model_data__studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__studio
msgid "Studio"
msgstr "الاستوديو"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_app_creator
msgid "Studio App Creator"
msgstr "منشئ تطبيق الاستوديو "

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_entry_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_entry
msgid "Studio Approval Entries"
msgstr "قيود موافقة الاستوديو "

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_entry
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Studio Approval Entry"
msgstr "قيد موافقة الاستوديو "

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_request
msgid "Studio Approval Request"
msgstr "طلب الموافقة على الاستوديو"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Studio Approval Rule"
msgstr "قاعدة موافقة الاستوديو "

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_rule_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_rule
msgid "Studio Approval Rules"
msgstr "قواعد موافقات الاستوديو "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_ui_menu__is_studio_configuration
msgid "Studio Configuration Menu"
msgstr "قائمة تهيئة الاستوديو "

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_customizations_filter
msgid "Studio Customizations"
msgstr "تخصيصات الاستوديو"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Studio Customizations will be exported"
msgstr "سيتم تصدير تخصيصات الاستوديو "

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.action_models_to_export
#: model:ir.actions.act_window,name:web_studio.action_studio_export_wizard
#: model:ir.ui.menu,name:web_studio.menu_models_to_export
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Studio Export"
msgstr "تصدير الاستوديو "

#. module: web_studio
#: model:ir.actions.client,name:web_studio.studio_export_action
msgid "Studio Export Action"
msgstr "إجراء التصدير في الاستوديو "

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard_data
msgid "Studio Export Data"
msgstr "بيانات تصدير الاستوديو "

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_model
msgid "Studio Export Models"
msgstr "نماذج تصدير الاستوديو "

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard
msgid "Studio Export Wizard"
msgstr "معالج تصدير الاستوديو "

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_mixin
msgid "Studio Mixin"
msgstr "مجموعة مخصصات الاستوديو "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Suggested features for your new model"
msgstr "الخصائص المقترحة لنموذجك الجديد "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Sum"
msgstr "المجموع"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Sum of %s"
msgstr "مجموع %s "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Tabs"
msgstr "علامات التبويب "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Tags"
msgstr "علامات التصنيف "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Technical Name"
msgstr "الاسم التقني"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Template '%s' not found"
msgstr "القالب '%s' غير موجود"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Text"
msgstr "النص"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "The fastest way to create a web application."
msgstr "أسرع طريقة لإنشاء تطبيق ويب."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The field %s does not exist."
msgstr "هذا الحقل %s غير موجود."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "The following fields are currently not in the view."
msgstr "الحقول التالية ليست في نافذة العرض حالياً. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon has not a correct format"
msgstr "تنسيق الأيقونة غير صحيح"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon is not linked to an attachment"
msgstr "الأيقونة غير مرتبطة بمرفق"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The method %(method)s does not exist on the model %(model)s."
msgstr "الطريقة %(method)s غير موجودة في النموذج %(model)s. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.js:0
msgid "The method %s is private."
msgstr "الطريقة %s خاصة. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s does not exist."
msgstr "النموذج %s غير موجود. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't exist."
msgstr "النموذج %s غير موجود. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't support adding fields."
msgstr "لا يدعم النموذج %s إضافة الحقول. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The operation  type \"%s\" is not supported"
msgstr "نوع العملية \"%s\" غير مدعوم "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The related field of a button has to be a many2one to %s."
msgstr "يجب أن تكون علاقة الحقل المرتبط بزر بـ%s علاقة متعدد لواحد. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"The report could not be loaded as some error occured. Usually it means that "
"some view inherits from another but targets a node that doesn't exist. It "
"might be due to the mutations of the base views during the upgrade process."
msgstr ""
"تعذر تحميل التقرير  بسبب وقوع خطأ ما. عادةً ما يعني ذلك أن إحدى نوافذ العرض "
"ترث من نافذة عرض أخرى ولكن تستهدف عقدة غير موجودة. قد يكون ذلك بسبب طفرات "
"نوافذ العرض الأساسية أثناء عملية الترقية. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "The report is in error. Only editing the XML sources is permitted"
msgstr "التقرير به خطأ. يُسمح فقط بتحرير مصادر XML "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
msgid ""
"The requested change caused an error in the view. It could be because a "
"field was deleted, but still used somewhere else."
msgstr ""
"تسبب التغيير المطلوب في إحداث خطأ في أداة العرض. قد يكون ذلك بسبب حذف حقل لا"
" يزال مستخدماً في مكان آخر. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message
msgid ""
"The step description will be displayed on the button on which an approval is"
" requested."
msgstr "سيتم عرض وصف الخطوة على الزر الذي يتم طلب الموافقة عليه."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid ""
"The user who approves this step will not be able to approve other steps for "
"the same record."
msgstr ""
"لن يتمكن المستخدم الذي يوافق على هذه الخطوة على الموافقة على الخطوات الأخرى "
"لنفس السجل. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approval_group_id
msgid "The users in this group are able to approve or reject the step."
msgstr "يمكن للمستخدمين في هذه المجموعة الموافقة على الخطوة أو رفضها. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"There are %s records using selection values not listed in those you are trying to save.\n"
"Are you sure you want to remove the selection values of those records?"
msgstr ""
"توجد %s سجلات تستخدم قيم اختيار غير مدرجة بين القيم التي تحاول حفظها. \n"
"هل أنت متأكد من أنك ترغب في إزالة قيم الاختيار لتلك السجلات؟ "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"There is no method %(method)s on the model %(model_name)s (%(model_id)s)"
msgstr ""
"لا توجد طريقة محددة %(method)s في النموذج %(model_name)s (%(model_id)s) "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"There is no record on which this report can be previewed. Create at least "
"one record to preview the report."
msgstr ""
"لا يوجد سجل على أساسه يمكن معاينة هذا التقرير. قم بإنشاء سجل واحد على الأقل "
"لمعاينة التقرير. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "There is no record to preview"
msgstr "لا يوجد سجل لمعاينته "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approver_ids
msgid ""
"These users are able to approve or reject the step and will be assigned to "
"an activity when their approval is requested."
msgstr ""
"يمكن لهؤلاء المستخدمين الموافقة على الخطوة أو رفضها وسيتم تعيينهم إلى نشاط "
"ما عند طلب موافقتهم. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__users_to_notify
msgid ""
"These users will receive a notification via internal note when the step is "
"approved or rejected"
msgstr ""
"سيتلقى هؤلاء المستخدمون إشعاراً عبر ملاحظة داخلية عند الموافقة على الخطوة أو"
" رفضها "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Third Status"
msgstr "الحالة الثالثة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/navbar.js:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "This action is not editable by Studio"
msgstr "لا يمكن تحرير هذا الإجراء من خلال الاستديو "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"This approval or the one you already submitted limits you to a single approval on this action.\n"
"Another user is required to further approve this action."
msgstr ""
"هذه الموافقة أو الموافقة التي قمت بإرسالها بالفعل تمنعك من إرسال أكثر من موافقة واحدة في هذا الإجراء. \n"
"على مستخدم آخر الموافقة على هذا الإجراء. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"This could also be due to the absence of a real record to render the report "
"with."
msgstr ""
"يمكن أن يكون ذلك أيضاً بسبب عدم وجود سجل حقيقي لتكوين التقرير باستخدامه. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "This may be due to an incorrect syntax in the edited parts."
msgstr "قد يكون ذلك بسبب سياق غير صحيح في الأجزاء التي تم تحريرها. "

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_export_model_unique_model
msgid "This model is already being exported."
msgstr "يتم تصدير هذا النموذج بالفعل. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "This operation caused an error, probably because a xpath was broken"
msgstr "سببت هذه العملية خطأ، ربما يكون هذا بسبب مشكلة في xpath ما"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "This rule has already been approved/rejected."
msgstr "لقد تمت الموافقة على/رفض هذه القاعدة بالفعل. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "This rule limits this user to a single approval for this action."
msgstr "تمنع هذه القاعدة المستخدم من منح أكثر من موافقة واحدة لهذا الإجراء. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Timeline"
msgstr "المخطط الزمني "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Timeline views"
msgstr "أدوات عرض المخطط الزمني "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "To <b>customize a field</b>, click on its <i>label</i>."
msgstr "<b>لتخصيص حقل،</b> اضغط على <i>بطاقة عنوانه</i>. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
msgid "Toggle Studio"
msgstr "تبديل الاستوديو "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Total"
msgstr "الإجمالي"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Type"
msgstr "النوع"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Type down your notes here..."
msgstr "قم بكتابة ملاحظاتك هنا... "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Undo"
msgstr "تراجع"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Unsupported operator '%s' to search action_xmlid"
msgstr "المشغل غير مدعوم '%s' للبحث عن action_xmlid "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__date_to
msgid "Until"
msgstr "حتى"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Until %s"
msgstr "حتى %s "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__updatable
msgid "Updatable"
msgstr "قابل للتحديث"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.js:0
msgid ""
"Use Bootstrap or any other custom classes to customize the style and the "
"display of the element."
msgstr ""
"استخدم Bootstrap أو أي فئات مخصصة أخرى لتخصيص الشكل وطريقة عرض العنصر. "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__user_id
msgid "User"
msgstr "المستخدم"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "User assignment"
msgstr "تعيين المستخدم "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "User avatar placeholder"
msgstr "العنصر النائب لصورة المستخدم الرمزية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__users_to_notify
msgid "Users to Notify"
msgstr "المستخدمون الذين يجب أن يتم إخطارهم "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Uses:"
msgstr "الاستخدامات: "

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Value"
msgstr "القيمة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Value of list"
msgstr "قيمة القائمة"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_view
msgid "View"
msgstr "أداة العرض"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor.js:0
msgid "View Editor"
msgstr "عرض المحرّر "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "View in Error:"
msgstr "نافذة العرض التي بها خطأ: "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Views"
msgstr "أدوات العرض"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Waiting for approval"
msgstr "بانتظار الموافقة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Want more fun? Let's create more <b>views</b>."
msgstr "أتريد الاستمتاع أكثر؟ فلننشئ <b>أدوات عرض</b> أخرى."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Webhook Automations"
msgstr "أتمتة ويب هوك "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Webhooks"
msgstr "ويب هوكس "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week"
msgstr "الأسبوع"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week (expanded)"
msgstr "الأسبوع (مع التفاصيل) "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Week Precision"
msgstr "دقة منخفضة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Welcome to"
msgstr "مرحبًا بك في"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "What about a <b>Kanban view</b>?"
msgstr "ما رأيك في استخدام <b>عرض كانبان</b>؟ "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "What should the button do"
msgstr "ما الذي يجب أن يقوم به الزر "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "When Creating Record"
msgstr "عند إنشاء السجل "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__can_validate
msgid "Whether the rule can be approved by the current user"
msgstr "ما إذا كانت القاعدة يمكن الموافقة عليها بواسطة المستخدم الحالي أم لا "

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model__abstract
msgid "Whether this model is abstract"
msgstr "ما إذا كان هذا النموذج مجرداً أم لا "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.xml:0
msgid "Which type of report do you want to create?"
msgstr "ما نوع التقرير الذي ترغب في إنشائه؟ "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_widget_properties.xml:0
msgid "Widget"
msgstr "أداة ذكية "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Wow, nice! And I'm sure you can make it even better! Use this icon to open "
"<b>Odoo Studio</b> and customize any screen."
msgstr ""
"واو، أحسنت! وأثق بأنك قادر على جعله أفضل حتى! استخدم هذه الأيقونة لفتح "
"<b>استديو أودو</b> وتخصيص شاشتك. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Write additional notes or comments"
msgstr "قم بكتابة ملاحظات إضافية أو تعليقات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "XML"
msgstr "XML"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Year"
msgstr "السنة"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You can not approve this rule."
msgstr "لا يمكنك الموافقة على هذه القاعدة. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"You cannot cancel an approval you didn't set yourself or you don't belong to"
" an higher level rule's approvers."
msgstr ""
"لا يمكنك إلغاء موافقة لم تقم بتعيينها بنفسك أو إذا كنت لا تنتمي إلى فئة أعلى"
" من مانحي الموافقات في قاعدة الموافقات. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "You cannot deactivate this view as it is the last one active."
msgstr "لا يمكنك إلغاء تفعيل أداة العرض هذه حيث أنها آخر أداة عرض فعالة. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "You first need to create a many2many field in the form view."
msgstr ""
"تحتاج أولاً إلى إنشاء حقل علاقة متعدد إلى متعدد في أداة عرض الاستمارة. "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You just like to break things, don't you?"
msgstr "أنت تحب فقط كسر الأشياء، أليس كذلك؟ "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "action"
msgstr "إجراء "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "delegates to %s."
msgstr "تفوض لـ %s. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "domain not defined"
msgstr "لم يتم تحديد النطاق "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "e.g. Properties"
msgstr "مثال: الخصائص "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "e.g. Real Estate"
msgstr "مثال: العقارات "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "for company"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "no one"
msgstr "لا أحد "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "object"
msgstr "الكائن "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "or"
msgstr "أو"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "studio_approval attribute can only be set in form views"
msgstr "يمكن تعيين خاصية studio_approval فقط في أداة عرض الاستمارة "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "test email"
msgstr "بريد إلكتروني تجريبي "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "to %s."
msgstr "إلى %s. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload it"
msgstr "قم برفعها "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload one"
msgstr "رفع واحد"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "x_studio_"
msgstr "x_studio_"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "{{ item.isInEdition ? 'Add selection' : 'Edit selection' }}"
msgstr "{{ item.isInEdition ? 'Add selection' : 'Edit selection' }}"
