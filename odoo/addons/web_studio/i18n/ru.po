# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_studio
# 
# Translators:
# <PERSON> <yeli<PERSON><EMAIL>>, 2024
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    This is your new action.\n"
"                </p>\n"
"                <p>By default, it contains a list and a form view and possibly\n"
"                    other view types depending on the options you chose for your model.\n"
"                </p>\n"
"                <p>\n"
"                    You can start customizing these screens by clicking on the Studio icon on the\n"
"                    top right corner (you can also customize this help message there).\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Это ваше новое действие.\n"
"                </p>\n"
"                <p>По умолчанию оно содержит список и представление формы, а также, возможно\n"
"                    другие типы представлений в зависимости от опций, которые вы выбрали для своей модели.\n"
"                </p>\n"
"                <p>\n"
"                    Вы можете начать настраивать эти экраны, нажав на значок Studio в\n"
"                    в правом верхнем углу (там же можно настроить это сообщение справки).\n"
"                </p>\n"
"            "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid ""
"\n"
"    There are no many2one fields related to the current model.\n"
"    To create a one2many field on the current model, you must first create its many2one counterpart on the model you want to relate to.\n"
msgstr ""
"\n"
"    В текущей модели нет полей many2one, связанных с ней.\n"
"    Чтобы создать поле one2many в текущей модели, необходимо сначала создать его аналог many2one в модели, к которой вы хотите привязаться.\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Add a new report\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Добавить новый отчет\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new filter\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Добавьте новый фильтр\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid " until %s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "\" failed."
msgstr "\" не удалось."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s delegated approval rights to %(delegate_to)s"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"%(user_name)s has set approval rights from %(previous_approvers)s to "
"%(next_approvers)s"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s revoked their delegation to %(revoked_users)s"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "%s Report"
msgstr "Отчет %s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_export_model.py:0
msgid "%s record(s)"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "<i role=\"img\" class=\"fa fa-user-times\" title=\"Exclusive approval\"/>"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new access control list\n"
"            </p>\n"
"            "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Добавьте новый список управления доступом\n"
"            </p>\n"
"            "

#. module: web_studio
#: model_terms:web_tour.tour,rainbow_man_message:web_studio.web_studio_new_app_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "A field with the same name already exists."
msgstr "Поле с таким же именем уже существует."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_entry_uniq_combination
msgid "A rule can only be approved/rejected once per record."
msgstr ""
"Правило может быть одобрено/отклонено только один раз для каждой записи."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_together
msgid "A rule must apply to an action or a method (but not both)."
msgstr "Правило должно применяться к действию или методу (но не к обоим)."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_not_null
msgid "A rule must apply to an action or a method."
msgstr "Правило должно применяться к действию или методу."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model__abstract
msgid "Abstract"
msgstr "Абстрактные"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Access Control"
msgstr "Контроль доступа"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Access Control Lists"
msgstr "Списки контроля доступа"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_groups
msgid "Access Groups"
msgstr "Группы доступа"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Access records from cell"
msgstr "Доступ к записям из ячейки"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Access records from graph"
msgstr "Доступ к записям из графика"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_mail_activity_type__category
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__action_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Action"
msgstr "Действие"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction
msgid "Action Needed"
msgstr "Требуются действия"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window
msgid "Action Window"
msgstr "Активное окно"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Вид окна действий"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Action to approve:"
msgstr "Решение об утверждении:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Action to run"
msgstr "Действия для выполнения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Action's title"
msgstr "Название действия"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Action: %s"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_actions
msgid "Actions"
msgstr "Действия"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Действия могут вызывать определенные изменения, такие как открытие просмотра"
" календаря или автоматическая пометка, как это было сделано при загрузке "
"документа"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
msgid "Activate View"
msgstr "Активировать просмотр"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__active
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Active"
msgstr "Активный"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#: model:ir.model,name:web_studio.model_mail_activity
msgid "Activity"
msgstr "Активность"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_activity_type
msgid "Activity Type"
msgstr "Тип активности"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Activity view unavailable on this model"
msgstr "Представление деятельности недоступно для этой модели"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Add"
msgstr "Добавить"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/chatter_container.xml:0
msgid "Add Chatter Widget"
msgstr "Добавить виджет Chatter"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add Picture"
msgstr "Добавить изображения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Add a Button"
msgstr "Добавить кнопку"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Add a button"
msgstr "Добавить кнопку"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add a pipeline status bar"
msgstr "Добавьте строку статуса Воронки"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add a priority"
msgstr "Добавить приоритет"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Add an approval step"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add an avatar"
msgstr "Добавить аватар"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Add details to your records with an embedded list view"
msgstr ""
"Добавляйте подробности к записям с помощью встроенного представления списка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Add new value"
msgstr "Добавить новое значение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record at the bottom"
msgstr "Добавить запись внизу"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record on top"
msgstr "Добавьте запись сверху"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add tags"
msgstr "Добавить теги"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_export_data
msgid "Additional Export Data"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Additional Fields"
msgstr "Дополнительные поля"

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid "Additional Studio Exports"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_models
msgid "Additional models to export"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_wizard__additional_models
msgid ""
"Additional models you may choose to export in addition to the Studio "
"customizations"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Aggregate"
msgstr "Совокупный"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "All Day"
msgstr "Весь день"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"All changes done to the report's structure will be discarded and the report "
"will be reset to its factory settings."
msgstr ""
"Все изменения, внесенные в структуру отчета, будут отменены, и отчет будет "
"возвращен к заводским настройкам."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"All set? You are just one click away from <b>generating your first app</b>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Allow Resequencing"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Allow visibility to groups"
msgstr ""

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "An approval is missing"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Apply"
msgstr "Применить"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approval"
msgstr "Одобрение"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approval Entries"
msgstr "Утвержденные записи"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approval_group_id
msgid "Approval Group"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approval Order"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approval_rule_id
msgid "Approval Rule"
msgstr "Правило утверждения"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_approver
msgid "Approval Rule Approvers Enriched"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_delegate
msgid "Approval Rule Delegate"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
msgid "Approval Rules"
msgstr "Утвержденные правила"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approvals"
msgstr "Одобрения"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals %(model_name)s"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals can only be done on a method or an action, not both."
msgstr ""
"Утверждения могут быть сделаны только для метода или действия, но не для "
"обоих."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals missing"
msgstr "Отсутствие разрешений"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approve"
msgstr "Одобрить"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__approved
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approved"
msgstr "Одобрено"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Approved <i class=\"fa fa-thumbs-up text-success\"/>"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approved on"
msgstr "Одобрено"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__user_id
msgid "Approved/rejected by"
msgstr "Одобрено/отклонено"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approver Group"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_log_ids
msgid "Approver Log"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_ids
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approver_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Approvers"
msgstr "Утвердители"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archive deprecated records"
msgstr "Архивируйте устаревшие записи"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Archived"
msgstr "Архивировано"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archiving"
msgstr "Архивация"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Are you sure you want to remove the selection values?"
msgstr "Вы уверены, что хотите удалить значения выбора?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
msgid "Are you sure you want to remove this %s from the view?"
msgstr "Вы уверены, что хотите удалить этот %s из представления?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Are you sure you want to reset the background image?"
msgstr "Вы уверены, что хотите сбросить фоновое изображение?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/edition_flow.js:0
msgid ""
"Are you sure you want to restore the default view?\r\n"
"All customization done with studio on this view will be lost."
msgstr ""
"Вы уверены, что хотите восстановить вид по умолчанию?\r\n"
"Все настройки, выполненные в студии для этого вида, будут потеряны."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__is_demo_data
msgid "As Demo"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Ascending"
msgstr "По возрастанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign a responsible to each record"
msgstr "Назначьте ответственного за каждую запись"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign dates and visualize records in a calendar"
msgstr "Назначайте даты и визуализируйте записи в календаре"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Attach a picture to a record"
msgstr "Прикрепить изображение к записи"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_attachment_count
msgid "Attachment Count"
msgstr "Количество вложений"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__include_attachment
msgid "Attachments"
msgstr "Вложения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Autocompletion Fields"
msgstr "Поля автозаполнения"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_automation
msgid "Automation Rule"
msgstr "Правило автоматизации"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Automations"
msgstr "Автоматизация"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Average"
msgstr "Средне"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Average of %s"
msgstr "Среднее из %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Awaiting approval"
msgstr "Ожидает утверждения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Backwards"
msgstr "Назад"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Bar"
msgstr "Бар"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base
msgid "Base"
msgstr "База"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Blank"
msgstr "Пустой"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Blocked"
msgstr "Заблокировано"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Bold"
msgstr "Жирный"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Business header/footer"
msgstr "Заголовки и колонтитулы"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.js:0
msgid "Button"
msgstr "Кнопка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Buttons Properties"
msgstr "Свойства кнопок"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/aside_properties/aside_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/div_properties/div_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/footer_properties/footer_properties.xml:0
msgid "CSS style"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Calendar"
msgstr "Календарь"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Call a method"
msgstr "Вызвать метод"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Create"
msgstr "Можно создать"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Delete"
msgstr "Можно удалить"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Edit"
msgstr "Можно редактировать"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__can_validate
msgid "Can be approved"
msgstr "Может быть одобрен"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch 'create', 'write' and 'unlink'."
msgstr "Невозможно установить патчи 'create', 'write' и 'unlink'."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch private methods."
msgstr "Невозможно исправлять приватные методы."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Cancel"
msgstr "Отменить"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Categorize records with custom tags"
msgstr "Категоризация записей с помощью пользовательских тегов"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Change Background"
msgstr "Изменить фон"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Change Image"
msgstr "Изменить изображение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Chatter"
msgstr "Болтовня"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "CheckBox"
msgstr "Флажок"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model_data__studio
msgid "Checked if it has been edited with Studio."
msgstr "Проверено, было ли оно отредактировано с помощью Студии."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Choose an app name"
msgstr "Выберите имя приложения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Choose the name of the menu"
msgstr "Выберите название меню"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Churn"
msgstr "Churn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.xml:0
msgid "Class"
msgstr "Класс"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Click to edit messaging features on your model."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Click to see all approval rules."
msgstr "Нажмите, чтобы просмотреть все правила утверждения."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/navbar/navbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Close"
msgstr "Закрыть"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Cohort"
msgstr "Когорта"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Color"
msgstr "Цвет"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Color Picker"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Column"
msgstr "Колонка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Column grouping"
msgstr "Группировка столбцов"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_company
msgid "Companies"
msgstr "Компании"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Company"
msgstr "Компания"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_structures.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Components"
msgstr "Компоненты"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Conditional"
msgstr "Условный"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__conditional
msgid "Conditional Rule"
msgstr "Условное правило"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Configuration"
msgstr "Конфигурация"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Configure Model"
msgstr "Настроить модель"

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid ""
"Configure additional models to export with Studio, such as records that hold"
" configuration information or demo data."
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Configure data and demo data to export"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Configure model"
msgstr "Настройте модель"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Confirm"
msgstr "Подтвердить"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Confirmation"
msgstr "Подтверждение"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Contact"
msgstr "Контакты"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Contact Field"
msgstr "Контактное поле"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid "Contact Field Required"
msgstr "Поле \"Контакт\" Обязательно"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Contact details"
msgstr "Контактная информация"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Context"
msgstr "Контекст"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Continue to configure some typical behaviors for your new type of object."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Could not change the background"
msgstr "Не удалось изменить фон"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Create Menu"
msgstr "Создать меню"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
msgid "Create Model"
msgstr "Создать модель"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Create a new Model"
msgstr "Создайте новую модель"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Create your <b>selection values</b> (e.g.: Romance, Polar, Fantasy, etc.)"
msgstr ""
"Создайте ваши <b>значение выбора</b> (eg: Романтика, Полярный, Фэнтези, и "
"т.д ..)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your App"
msgstr "Создать свой App"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your app"
msgstr "Создайте свое приложение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your first menu"
msgstr "Создайте свое первое меню"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Create your menu"
msgstr "Создайте свое меню"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_uid
msgid "Created by"
msgstr "Создано"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_date
msgid "Created on"
msgstr "Создано"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Creating this type of view is not currently supported in Studio."
msgstr ""
"Создание этого типа представления в настоящее время не поддерживается в "
"Studio."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Currency"
msgstr "Валюта"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Current model:"
msgstr "Текущая модель:"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Custom Configuration"
msgstr "Расширенная конфигурация"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_fields
msgid "Custom Fields"
msgstr "Настраиваемые поля"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_models
msgid "Custom Models"
msgstr "Пользовательские модели"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_reports
msgid "Custom Reports"
msgstr "Пользовательские отчеты"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Custom Sorting"
msgstr "Пользовательская сортировка"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_views
msgid "Custom Views"
msgstr "Пользовательские представления"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Custom field names cannot contain double underscores."
msgstr ""
"Имена пользовательских полей не могут содержать двойных подчеркиваний."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom fields:"
msgstr "Произвольные поля:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom models:"
msgstr "Пользовательские модели:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom reports:"
msgstr "Пользовательские отчеты:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom views:"
msgstr "Пользовательские виды:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Customization made with Studio will be permanently lost"
msgstr "Настройки, сделанные с помощью Studio, будут безвозвратно утеряны"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "DIFF"
msgstr "DIFF"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Date"
msgstr "Дата"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date & Calendar"
msgstr "Дата и календарь"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__date_to
msgid "Date To"
msgstr "Дата до"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date range & Gantt"
msgstr "Диапазон дат и Гантт"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Datetime"
msgstr "Время и дата"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Day"
msgstr "День"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Day Precision"
msgstr "Точность дня"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Decimal"
msgstr "Десятичный"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Default Display Mode"
msgstr "Режим отображения по умолчанию"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__default_export_data
msgid "Default Export Data"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Default Group By"
msgstr "По умолчанию Группа по"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Group by"
msgstr "По умолчанию Группировать по"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Scale"
msgstr "Масштаб по умолчанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: model:ir.model,name:web_studio.model_ir_default
msgid "Default Values"
msgstr "Значения по умолчанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Default value"
msgstr "Значение по умолчанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Default view"
msgstr "Вид по умолчанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Define start/end dates and visualize records in a Gantt chart"
msgstr ""
"Определите даты начала/окончания и визуализируйте записи на диаграмме Ганта"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__updatable
msgid "Defines if the records would be updated during a module update."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__notification_order
msgid "Defines the sequential order in which the approvals are requested."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Delay Field"
msgstr "Поле задержки"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Delegate"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Delegate to"
msgstr ""

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Delete"
msgstr "Удалить"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__is_demo_data
msgid "Demo"
msgstr "Демо"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Dense"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Descending"
msgstr "По убыванию"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message
msgid "Description"
msgstr "Описание"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "Design your Icon"
msgstr "Создайте свой значок"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Disable View"
msgstr "Отключить просмотр"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Discard"
msgstr "Отменить"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Discard changes"
msgstr "Отменить изменения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Mode"
msgstr "Режим отображения"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Total row"
msgstr "Отображение общего ряда"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Unavailability"
msgstr "Недоступность дисплея"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Displays a textual hint that helps the user when the field is empty."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "Do you want to add a dropdown with colors?"
msgstr "Хотите добавить выпадающий список с цветами?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__domain
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__domain
msgid "Domain"
msgstr "Домен"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Done"
msgstr "Готово"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Drag & drop <b>another field</b>. Let's try with a <i>selection field</i>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Drag a menu to the right to create a sub-menu"
msgstr "Перетащите меню вправо, чтобы создать подменю"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.js:0
msgid "Dropdown"
msgstr "Выпадающий список"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Duplicate"
msgstr "Дублировать"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Dynamic Table"
msgstr "Динамический стол"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Edit"
msgstr "Редактировать"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_content_overlay.js:0
msgid "Edit %s view"
msgstr "Редактировать %s просмотр"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
msgid "Edit Application Icon"
msgstr "Редактирование значка приложения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/menu_properties/menu_properties.xml:0
msgid "Edit Color Picker"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit Menu"
msgstr "Изменить Меню"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Edit Values"
msgstr "Изменить значения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit selection"
msgstr "Изменить выбранное"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Edit sources"
msgstr "Редактировать источники"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.js:0
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""
"Редактирование встроенного файла через этот редактор не рекомендуется, так "
"как это не позволит обновить его при последующих обновлениях приложения."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Editing item:"
msgstr "Элемент редактирования:"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Email"
msgstr "Email"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Email Alias"
msgstr "Псевдоним почты"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_template
msgid "Email Templates"
msgstr "Шаблоны для электронной почты"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_thread
msgid "Email Thread"
msgstr "Цепочка Email"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Empty List Message"
msgstr "Пустой список сообщения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Enable Mass Editing"
msgstr "Включить массовое редактирование"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Enable Routing"
msgstr "Включить маршрутизацию"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "End Date"
msgstr "Дата окончания"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entry_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Entries"
msgstr "Записи"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Error"
msgstr "Ошибка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error message:"
msgstr "Сообщение об ошибке:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error name:"
msgstr "Имя ошибки:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__exclusive_user
msgid "Exclusive Approval"
msgstr "Эксклюзивное одобрение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Existing Fields"
msgstr "Существующие поля"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Existing Model"
msgstr "Существующая модель"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Export"
msgstr "Экспорт"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "External"
msgstr "Внешний"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_xmlid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__xmlid
msgid "External ID"
msgstr "Внешний ID"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Fadeout Speed"
msgstr "Скорость затухания"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Fast"
msgstr "Быстро"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Favourites"
msgstr "Избранные"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Field"
msgstr "Поле"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Field Properties"
msgstr "Свойства поля"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.js:0
msgid "Field properties: %s"
msgstr "Свойства полей: %s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_fields
msgid "Fields"
msgstr "Поля"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__excluded_fields
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
msgid "Fields to exclude"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "File"
msgstr "Файл"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Filename for %s"
msgstr "Имя файла для %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Filter"
msgstr "Фильтр"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Filter Rules"
msgstr "Фильтр правил"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Filter label"
msgstr "Метка фильтра"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: model:ir.model,name:web_studio.model_ir_filters
msgid "Filters"
msgstr "Фильтры"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "First Status"
msgstr "Первый статус"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "First dimension"
msgstr "Первое измерение"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_follower_ids
msgid "Followers"
msgstr "Подписчики"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_partner_ids
msgid "Followers (Partners)"
msgstr "Подписчики"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Footer"
msgstr "Футер"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"For compatibility purpose with base_automation,approvals on 'create', "
"'write' and 'unlink' methods are forbidden."
msgstr ""
"В целях совместимости с base_automation, утверждения методов 'create', "
"'write' и 'unlink' запрещены."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Forbid visibility to groups"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Forever"
msgstr "Навсегда"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Form"
msgstr "Форма"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Format"
msgstr "Формат"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Forward"
msgstr "Вперёд"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Gantt"
msgstr "Гантт"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "General views"
msgstr "Общие взгляды"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.js:0
msgid "Generate %s View"
msgstr "Генерировать %s Просмотр"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Get contact, phone and email fields on records"
msgstr "Получение полей контактов, телефона и электронной почты в записях"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Go back"
msgstr "Назад"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Go on, you are almost done!"
msgstr "Продолжайте, вы почти закончили!"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Good job! To add more <b>fields</b>, come back to the <i>Add tab</i>."
msgstr ""
"Хорошая работа! Для добавления <b>полей,</b> вернитесь на <i>вкладку "
"Добавить.</i>"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: model:ir.model.fields.selection,name:web_studio.selection__mail_activity_type__category__grant_approval
#: model:mail.activity.type,name:web_studio.mail_activity_data_approve
msgid "Grant Approval"
msgstr "Утверждение гранта"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Graph"
msgstr "График"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Great !"
msgstr "Отлично!"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_renderer.xml:0
msgid "Group"
msgstr "Группа"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Group By"
msgstr "Группировать по"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Group by"
msgstr "Сортировать"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Grouping is not applied while in Studio to allow editing."
msgstr "Группировка не применяется в Studio для редактирования."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "HTML"
msgstr "HTML"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_http
msgid "HTTP Routing"
msgstr "Маршрутизация HTTP"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Day"
msgstr "Часть дня"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Hour"
msgstr "Полчаса"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__has_message
msgid "Has Message"
msgstr "Есть сообщение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"Header and footer are shared with other reports which may be impacted by the"
" reset."
msgstr ""
"Верхний и нижний колонтитулы используются совместно с другими отчетами, на "
"которые может повлиять сброс."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Help Tooltip"
msgstr "Помощь подсказка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Here, you can <b>name</b> your field (e.g. Book reference, ISBN, Internal "
"Note, etc.)."
msgstr ""
"Здесь вы можете <b>назвать</b> ваше поле (например, ссылки книги, ISBN, "
"внутренняя примечание пр.)."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Address"
msgstr "Скрыть адрес"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Name"
msgstr "Скрыть имя"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Hide by default"
msgstr "Скрыть по умолчанию"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "High Priority"
msgstr "Высокий приоритет"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Highlight Color Field"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_res_company__background_image
msgid "Home Menu Background Image"
msgstr "Фоновое изображение главного меню"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Hour"
msgstr "Час"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to <b>name</b> your app? Library, Academy, …?"
msgstr ""
"Как вы хотите <b>назвать</b> ваше приложение? Библиотека, Академия ...?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to name your first <b>menu</b>? My books, My courses?"
msgstr "Как вы хотите назвать ваше первое <b>меню?</b> Мои книги, мои курсы?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"I bet you can <b>build an app</b> in 5 minutes. Ready for the challenge?"
msgstr ""
"Мы уверены, что вы сможете <b>создать приложение</b> за 5 минут. Готовы к "
"испытанию?"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__id
msgid "ID"
msgstr "ID"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "IDs"
msgstr "IDs"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Icon"
msgstr "Иконка"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Если флажок установлен, значит, новые сообщения требуют вашего внимания."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Если отмечено, некоторые сообщения имеют ошибку доставки."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__include_attachment
msgid ""
"If set, the attachments related to the exported records will be included in "
"the export."
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__is_demo_data
msgid ""
"If set, the exported records will be considered as demo data during the "
"import."
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__domain
msgid "If set, the rule will only apply on records that match the domain."
msgstr ""
"Если установлено, правило будет применяться только к записям, "
"соответствующим домену."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__exclusive_user
msgid ""
"If set, the user who approves this rule will not be able to approve other "
"rules for the same record"
msgstr ""
"Если установлено, пользователь, утвердивший это правило, не сможет "
"утверждать другие правила для той же записи"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Если отменить текущее редактирование, все несохраненные изменения будут "
"потеряны. Чтобы вернуться в режим редактирования, можно отменить действие."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"If you don't want to create a new model, an existing model should be "
"selected."
msgstr ""
"Если вы не хотите создавать новую модель, следует выбрать существующую."

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Image"
msgstr "Изображение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Import"
msgstr "Импорт"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "In Progress"
msgstr "В процессе"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_additional_data
msgid "Include Data"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_demo_data
msgid "Include Demo Data"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Include header and footer"
msgstr "Включите верхний и нижний колонтитулы"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_ui_menu__is_studio_configuration
msgid ""
"Indicates that this menu was created by Studio to hold configuration sub-"
"menus"
msgstr ""
"Указывает, что это меню было создано Studio для размещения подменю "
"конфигурации"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field"
msgstr "Вставить поле"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field..."
msgstr "Вставьте поле..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a table based on a relational field."
msgstr "Вставка таблицы на основе реляционного поля."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert left"
msgstr "Вставка слева"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert right"
msgstr "Вставить справа"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Integer"
msgstr "Целое число"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Internal"
msgstr "Внутренний"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Interval"
msgstr "Интервал"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Invalid studio_approval %s in button"
msgstr "Недопустимое значение studio_approval %s в кнопке"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Invisible"
msgstr "Невидимый"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__is_delegation
msgid "Is Delegation"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_is_follower
msgid "Is Follower"
msgstr "Является подписчиком"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__is_studio
msgid "Is Studio"
msgstr "Is Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Is default view"
msgstr "Вид по умолчанию"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "It lacks a method to check."
msgstr "В нем отсутствует метод проверки."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Kanban"
msgstr "Канбан"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__kanban_color
msgid "Kanban Color"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Kanban State"
msgstr "Статус канбан"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/group_properties/group_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/page_properties/page_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Label"
msgstr "Метка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Label of the button"
msgstr "Ярлык кнопки"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "Leave DIFF"
msgstr "Оставить DIFF"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_leave_with
msgid "Leave Studio with another action"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Let's check the result. Close Odoo Studio to get an <b>overview of your "
"app</b>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Limit visibility to groups"
msgstr "Ограничить видимость для групп"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Line"
msgstr "Линия"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Lines"
msgstr "Линии"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__mail_activity_id
msgid "Linked Activity"
msgstr "Связанная деятельность"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "List"
msgstr "Список"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Manually sort records in the list view"
msgstr "Ручная сортировка записей в представлении списка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2Many"
msgstr "Many2Many"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2One"
msgstr "Many2One"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Map"
msgstr "Карта"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid ""
"Map views are based on the address of a linked Contact. You need to have a "
"Many2one field linked to the res.partner model in order to create a map "
"view."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Measure"
msgstr "Измерить растояние"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Measure Field"
msgstr "Поле измерений"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Measures"
msgstr "Меры"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Medium"
msgstr "Канал"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_menu
msgid "Menu"
msgstr "Меню"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Message"
msgstr "Сообщение"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error
msgid "Message Delivery error"
msgstr "Ошибка доставки сообщения"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_ids
msgid "Messages"
msgstr "Сообщения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__method
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__method
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Method"
msgstr "Метод"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Method to run"
msgstr "Метод выполнения"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Method: %s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Minimal header/footer"
msgstr "Минимальный хедер/футер"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Mode"
msgstr "Режим"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Model"
msgstr "Модель"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/wizard/studio_export_wizard.py:0
msgid "Model '%s' should not contain records with the same ID."
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_access
msgid "Model Access"
msgstr "Доступ к модели"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_data
msgid "Model Data"
msgstr "Данные модели"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model_name
msgid "Model Description"
msgstr "Описание модели"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__model
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_name
msgid "Model Name"
msgstr "Название модели"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Model name"
msgstr "Название модели"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model
msgid "Models"
msgstr "Модели"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Modified by:"
msgstr "Изменено:"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_module_module
msgid "Module"
msgstr "Модуль"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Деинсталляция модуля"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Monetary"
msgstr "Денежные средства"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Monetary value"
msgstr "Денежная стоимость"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month"
msgstr "Месяц"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month (expanded)"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Month Precision"
msgstr "Месяц Точность"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "More"
msgstr "Больше"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Multine Text"
msgstr "Мультилайн Текст"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Multiple records views"
msgstr "Просмотр нескольких записей"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "My %s"
msgstr "Мои %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "My Button"
msgstr "Моя кнопка"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "My entries"
msgstr "Мои записи"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "N/A"
msgstr "Н/A"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__name
msgid "Name"
msgstr "Имя"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New"
msgstr "Новый"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "New %s"
msgstr "Новый %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
msgid "New App"
msgstr "Новое приложение"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "New Approval Entry"
msgstr "Новая запись об утверждении"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "New Field"
msgstr "Новое поле"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "New Fields"
msgstr "Новые поля"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "New Filter"
msgstr "Новый фильтр"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New Lines"
msgstr "Новые строки"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "New Menu"
msgstr "Новое меню"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "New Model"
msgstr "Новая модель"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "New Page"
msgstr "Новая страница"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "New button"
msgstr "Новая кнопка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Next"
msgstr "Следующий"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Nicely done! Let's build your screen now; <b>drag</b> a <i>text field</i> "
"and <b>drop</b> it in your view, on the right."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "No aggregation"
msgstr "Без агрегации"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "No approval found for this rule, record and user combination."
msgstr ""
"Не найдено одобрения для этой комбинации правил, записей и пользователей."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "No header/footer"
msgstr "Отсутствие верхнего/нижнего колонтитула"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "No related many2one fields found"
msgstr "Связанных полей many2one не найдено"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "None"
msgstr "Нет"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Notes"
msgstr "Заметки"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__users_to_notify
msgid "Notify to"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now you're on your own. Enjoy your <b>super power</b>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now, customize your icon. Make it yours."
msgstr "Теперь настройте свою иконку. Сделайте это самостоятельно."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of Actions"
msgstr "Число действий"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entries_count
msgid "Number of Entries"
msgstr "Число записей"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of errors"
msgstr "Число ошибок"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Количество сообщений, требующих принятия мер"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Количество недоставленных сообщений"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Odoo Studio"
msgstr "Odoo Студия"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "On view (ir.ui.view):"
msgstr "При просмотре (ir.ui.view):"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "One2Many"
msgstr "One2Many"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"Only groups with an external ID can be used here. Please choose another "
"group or assign manually an external ID to this group."
msgstr ""
"Здесь можно использовать только группы с внешним идентификатором. "
"Пожалуйста, выберите другую группу или назначьте внешний ID этой группе "
"вручную."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Open Record On Click"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Open form view"
msgstr "Открыть вид формы"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Open kanban view of approvals"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Optional"
msgstr "Необязательно"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Order"
msgstr "Заказ"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "PDF file"
msgstr "PDF файл"

#. module: web_studio
#: model:ir.model,name:web_studio.model_report_paperformat
msgid "Paper Format Config"
msgstr "Настройка формата бумаги"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Paper format"
msgstr "Формат бумаги"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Parent Menu"
msgstr "Главное меню"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Parent View (inherit_id)"
msgstr "Родительский вид (inherit_id)"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Partner"
msgstr "Партнер"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Phone"
msgstr "Телефон"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Picture"
msgstr "Изображение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Pie"
msgstr "Круговая"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Pipeline stages"
msgstr "Стадии Воронки"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Pipeline status bar"
msgstr "Строка статуса Воронки"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Pivot"
msgstr "Сводная таблица"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Placeholder"
msgstr "Заполнитель"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Please specify a field."
msgstr "Пожалуйста, укажите поле."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__post
msgid "Post"
msgstr "Запись"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__pre
msgid "Pre"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Preset"
msgstr "Пресет"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.xml:0
msgid "Preview is not available"
msgstr "Предварительный просмотр недоступен"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Previous"
msgstr "Предыдущий"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Print preview"
msgstr "Предварительный просмотр печати"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Priority"
msgstr "Приоритет"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Private methods cannot be restricted (since they cannot be called remotely, "
"this would be useless)."
msgstr ""
"Приватные методы нельзя ограничить (поскольку их нельзя вызвать удаленно, "
"это было бы бесполезно)."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Properties"
msgstr "Свойства"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Quarter Hour"
msgstr "Четверть часа"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
msgid "Quick Create"
msgstr "Быстрое создание"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Quick Create Field"
msgstr "Поле быстрого создания"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Effect"
msgstr "Эффект радуги"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Man"
msgstr "Человек-радуга"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__rating_ids
msgid "Ratings"
msgstr "Рейтинги"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Readonly"
msgstr "Только для чтения"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Ready"
msgstr "Подтверждение"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Record"
msgstr "Запись"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__res_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__res_id
msgid "Record ID"
msgstr "ID записи"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__name
msgid "Record Name"
msgstr "Имя Записи"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__records_count
msgid "Records"
msgstr "Записи"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Redo"
msgstr "Повторить"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__reference
msgid "Reference"
msgstr "Справка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Reject"
msgstr "Отклонить"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Rejected"
msgstr "Отклонено"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Rejected <i class=\"fa fa-thumbs-down text-danger\"/>"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Rejected on"
msgstr "Отклонено по"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Related Field"
msgstr "Смежное поле"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reload from attachment"
msgstr "Перезагрузка из вложения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
msgid "Remove from View"
msgstr "Удалить из просмотра"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Remove rule"
msgstr "Удалить правило"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Remove selection"
msgstr "Удалить выбор"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_report
msgid "Report Action"
msgstr "Отчет о действиях"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report Tools"
msgstr "Инструменты для создания отчетов"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Report edition failed"
msgstr "Не удалось отредактировать отчет"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Report name"
msgstr "Название отчета"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report preview not available"
msgstr "Предварительный просмотр отчета недоступен"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Reporting views"
msgstr "Виды отчетов"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Reports"
msgstr "Отчеты"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Required"
msgstr "Обязательно"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__res_id
msgid "Res"
msgstr "Ресет"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Reset Default Background"
msgstr "Сброс фона по умолчанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Reset Image"
msgstr "Сброс Изображения"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.reset_view_arch_wizard_view
msgid "Reset View"
msgstr "Сбросить Вид"

#. module: web_studio
#: model:ir.model,name:web_studio.model_reset_view_arch_wizard
msgid "Reset View Architecture Wizard"
msgstr "Мастер сброса вида архитектуры"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reset report"
msgstr "Отчет о сбросе"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Responsible"
msgstr "Ответственный"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Restore Default View"
msgstr "Восстановление вида по умолчанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Restrict a record to a specific company"
msgstr "Ограничить запись для определенной компании"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Retention"
msgstr "Удержание"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Revoke"
msgstr "Аннулировать"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Ribbon"
msgstr "Лента"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - First level"
msgstr "Группировка рядов - первый уровень"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - Second level"
msgstr "Группировка рядов - второй уровень"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_rule
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__rule_id
msgid "Rule"
msgstr "Правило"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be deleted since it would delete existing"
" approval entries. You should archive the rule instead."
msgstr ""
"Правила с существующими записями нельзя удалять, поскольку это приведет к "
"удалению существующих записей одобрения. Вместо этого правило следует "
"заархивировать."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be modified since it would break existing"
" approval entries. You should archive the rule and create a new one instead."
msgstr ""
"Правила с существующими записями не могут быть изменены, поскольку это "
"приведет к нарушению существующих записей одобрения. Вам следует "
"заархивировать правило и создать новое."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Run a Server Action"
msgstr "Запуск действия сервера"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Ошибка доставки SMS"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Save"
msgstr "Сохранить"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Save."
msgstr "Сохранить."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saved"
msgstr "Сохранено"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saving"
msgstr "Сохранение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Saving both some report's parts and full xml is not permitted."
msgstr "Сохранение как части отчета, так и полного xml не допускается."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Saving the report \""
msgstr "Сохранение отчета \""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Search"
msgstr "Поиск"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Search..."
msgstr "Поиск..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Second Status"
msgstr "Второй статус"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Second dimension"
msgstr "Второе измерение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "See what changes have been made to this view"
msgstr "Просмотрите изменения, внесённые в это представление"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "Select a Field"
msgstr "выбрать поле"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select a group..."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Select a related field"
msgstr "Выберите смежную область"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "Select a related field."
msgstr "Выберите связанное поле."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Select group"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
msgid ""
"Select the contact field to use to get the coordinates of your records."
msgstr "Выберите поле контакта для получения координат записей."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the model in relation to this one"
msgstr "Выберите модель по отношению к этой"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the reciprocal ManyToOne field"
msgstr "Выберите взаимное поле ManyToOne"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select users..."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Selection"
msgstr "Выбор"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Send a"
msgstr "Отправить"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Send messages, log notes and schedule activities"
msgstr "Отправляйте сообщения, записывайте заметки и планируйте действия"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Separator"
msgstr "Разделитель"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_server
msgid "Server Action"
msgstr "Server Action"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Set As Default"
msgstr "Установить по умолчанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.xml:0
msgid "Set Cover Image"
msgstr "Установить изображение обложки"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Set a price or cost on records"
msgstr "Установите цену или стоимость на записи"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Set an <b>email alias</b>. Then, try to send an email to this address; it "
"will create a document automatically for you. Pretty cool, huh?"
msgstr ""
"Установить <b>псевдоним электронной почты.</b> Затем попробуйте отправить "
"сообщение на этот адрес; он автоматически создаст документ для вас. Круто "
"так?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Show Invisible Elements"
msgstr "Показать невидимые элементы"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Show Traceback"
msgstr "Показать трассировку"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Show by default"
msgstr "Показать по умолчанию"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Show in print menu"
msgstr "Показать в меню печати"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Show link to record"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Side panel"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Signature"
msgstr "Подпись"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Slow"
msgstr "Медленно"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "Some approvals are missing"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Some records were skipped because approvals were missing to"
"                                    proceed with your request: "
msgstr ""
"Некоторые записи были пропущены, потому что не было одобрения для"
"                                     выполнения вашего запроса: "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Sort By"
msgstr "Сортировать по"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Sort by"
msgstr "Сортировать по"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Sorting"
msgstr "Сортировка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Sparse"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Specify all possible values"
msgstr "Укажите все возможные значения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Stacked graph"
msgstr "Сложенный график"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Stage"
msgstr "Этап"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Stage Name"
msgstr "Название этапа"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Stage and visualize records in a custom pipeline"
msgstr "Постановка и визуализация записей в пользовательской воронке"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Start Date"
msgstr "Дата начала"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Start Date Field"
msgstr "Поле даты начала"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__notification_order
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Step"
msgstr "Шаг"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__1
msgid "Step 1"
msgstr ""

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__2
msgid "Step 2"
msgstr ""

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__3
msgid "Step 3"
msgstr ""

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__4
msgid "Step 4"
msgstr ""

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__5
msgid "Step 5"
msgstr ""

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__6
msgid "Step 6"
msgstr ""

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__7
msgid "Step 7"
msgstr ""

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__8
msgid "Step 8"
msgstr ""

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__9
msgid "Step 9"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Stop Date Field"
msgstr "Поле даты остановки"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model_data__studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__studio
msgid "Studio"
msgstr "Студия"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_app_creator
msgid "Studio App Creator"
msgstr "Студия создатель приложений"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_entry_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_entry
msgid "Studio Approval Entries"
msgstr "Записи, одобренные студией"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_entry
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Studio Approval Entry"
msgstr "Запись на студию"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_request
msgid "Studio Approval Request"
msgstr "Запрос на утверждение студии"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Studio Approval Rule"
msgstr "Правило утверждения студии"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_rule_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_rule
msgid "Studio Approval Rules"
msgstr "Правила утверждения студии"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_ui_menu__is_studio_configuration
msgid "Studio Configuration Menu"
msgstr "Меню конфигурации студии"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_customizations_filter
msgid "Studio Customizations"
msgstr "Настройки Студии"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Studio Customizations will be exported"
msgstr ""

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.action_models_to_export
#: model:ir.actions.act_window,name:web_studio.action_studio_export_wizard
#: model:ir.ui.menu,name:web_studio.menu_models_to_export
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Studio Export"
msgstr ""

#. module: web_studio
#: model:ir.actions.client,name:web_studio.studio_export_action
msgid "Studio Export Action"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard_data
msgid "Studio Export Data"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_model
msgid "Studio Export Models"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard
msgid "Studio Export Wizard"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_mixin
msgid "Studio Mixin"
msgstr "Studio Mixin"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Suggested features for your new model"
msgstr "Предлагаемые характеристики для вашей новой модели"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Sum"
msgstr "Сумма"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Sum of %s"
msgstr "Сумма %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Tabs"
msgstr "Вкладки"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Tags"
msgstr "Теги"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Technical Name"
msgstr "Техническое название"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Template '%s' not found"
msgstr "Шаблон '%s' не найден"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Text"
msgstr "Текст"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "The fastest way to create a web application."
msgstr "Самый быстрый способ создания веб-приложения."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The field %s does not exist."
msgstr "Поле %s не существует."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "The following fields are currently not in the view."
msgstr "В настоящее время в представлении отсутствуют следующие поля."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon has not a correct format"
msgstr "Значок имеет неправильный формат"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon is not linked to an attachment"
msgstr "Значок не связан с вложением"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The method %(method)s does not exist on the model %(model)s."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.js:0
msgid "The method %s is private."
msgstr "Метод %s является частным."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s does not exist."
msgstr "Модель %s не существует."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't exist."
msgstr "Модель %s не существует."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't support adding fields."
msgstr "Модель %s не поддерживает добавление полей."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The operation  type \"%s\" is not supported"
msgstr "Тип операции \"%s\" не поддерживается"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The related field of a button has to be a many2one to %s."
msgstr "Связанное поле кнопки должно быть many2one к %s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"The report could not be loaded as some error occured. Usually it means that "
"some view inherits from another but targets a node that doesn't exist. It "
"might be due to the mutations of the base views during the upgrade process."
msgstr ""
"Отчет не удалось загрузить, так как произошла какая-то ошибка. Обычно это "
"означает, что какое-то представление наследуется от другого, но нацелено на "
"несуществующий узел. Это может быть связано с мутациями базовых "
"представлений в процессе обновления."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "The report is in error. Only editing the XML sources is permitted"
msgstr ""
"В отчете допущена ошибка. Разрешено только редактирование источников XML"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
msgid ""
"The requested change caused an error in the view. It could be because a "
"field was deleted, but still used somewhere else."
msgstr ""
"Запрошенное изменение вызвало ошибку в представлении. Это может быть связано"
" с тем, что поле было удалено, но по-прежнему используется в другом месте."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message
msgid ""
"The step description will be displayed on the button on which an approval is"
" requested."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid ""
"The user who approves this step will not be able to approve other steps for "
"the same record."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approval_group_id
msgid "The users in this group are able to approve or reject the step."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"There are %s records using selection values not listed in those you are trying to save.\n"
"Are you sure you want to remove the selection values of those records?"
msgstr ""
"Есть %s записей, использующих значения отбора, не перечисленные в тех, которые вы пытаетесь сохранить.\n"
"Вы уверены, что хотите удалить значения выбора из этих записей?"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"There is no method %(method)s on the model %(model_name)s (%(model_id)s)"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"There is no record on which this report can be previewed. Create at least "
"one record to preview the report."
msgstr ""
"Не существует записи, для которой можно предварительно просмотреть этот "
"отчет. Создайте хотя бы одну запись для предварительного просмотра отчета."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "There is no record to preview"
msgstr "Нет записи для предварительного просмотра"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approver_ids
msgid ""
"These users are able to approve or reject the step and will be assigned to "
"an activity when their approval is requested."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__users_to_notify
msgid ""
"These users will receive a notification via internal note when the step is "
"approved or rejected"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Third Status"
msgstr "Третий статус"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/navbar.js:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "This action is not editable by Studio"
msgstr "Это действие не редактируется в Studio"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"This approval or the one you already submitted limits you to a single approval on this action.\n"
"Another user is required to further approve this action."
msgstr ""
"Это одобрение или уже представленное вами одобрение ограничивает вас одним одобрением этого действия.\n"
"Для дальнейшего утверждения этого действия требуется другой пользователь."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"This could also be due to the absence of a real record to render the report "
"with."
msgstr ""
"Это также может быть связано с отсутствием реальной записи для создания "
"отчета."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "This may be due to an incorrect syntax in the edited parts."
msgstr ""
"Это может быть связано с неправильным синтаксисом в редактируемых частях."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_export_model_unique_model
msgid "This model is already being exported."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "This operation caused an error, probably because a xpath was broken"
msgstr "Эта операция вызвала ошибку, возможно потому, что xpath был сломан"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "This rule has already been approved/rejected."
msgstr "Это правило уже было одобрено/отклонено."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "This rule limits this user to a single approval for this action."
msgstr ""
"Это правило ограничивает данного пользователя одним одобрением этого "
"действия."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Timeline"
msgstr "Временная шкала"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Timeline views"
msgstr "Виды временной шкалы"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "To <b>customize a field</b>, click on its <i>label</i>."
msgstr "Чтобы <b>настроить поле,</b> нажмите на эту <i>иконку.</i>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
msgid "Toggle Studio"
msgstr "Toggle Studio"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Total"
msgstr "Всего"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Type"
msgstr "Тип"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Type down your notes here..."
msgstr "Заметки."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Undo"
msgstr "Отменить"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Unsupported operator '%s' to search action_xmlid"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__date_to
msgid "Until"
msgstr "Окончание"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Until %s"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__updatable
msgid "Updatable"
msgstr "Обновляемый"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.js:0
msgid ""
"Use Bootstrap or any other custom classes to customize the style and the "
"display of the element."
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__user_id
msgid "User"
msgstr "Пользователь"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "User assignment"
msgstr "Назначение пользователя"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "User avatar placeholder"
msgstr "Место для аватара пользователя"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__users_to_notify
msgid "Users to Notify"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Uses:"
msgstr "Использование:"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Value"
msgstr "Значение"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Value of list"
msgstr "Значение списка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_view
msgid "View"
msgstr "Просмотр"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor.js:0
msgid "View Editor"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "View in Error:"
msgstr "Просмотр в ошибке:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Views"
msgstr "Просмотры"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Waiting for approval"
msgstr "Ожидание подтверждения"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Want more fun? Let's create more <b>views</b>."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Webhook Automations"
msgstr "Автоматизация с помощью веб-крючков"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Webhooks"
msgstr "Вебзацепы"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website Messages"
msgstr "Веб-сайт сообщения"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website communication history"
msgstr "История общений с сайта"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week"
msgstr "Неделя"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week (expanded)"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Week Precision"
msgstr "Недельная точность"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Welcome to"
msgstr "Добро пожаловать в"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "What about a <b>Kanban view</b>?"
msgstr "Как насчет <b>просмотра канбан?</b>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "What should the button do"
msgstr "Что должна делать кнопка"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "When Creating Record"
msgstr "При создании записи"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__can_validate
msgid "Whether the rule can be approved by the current user"
msgstr "Может ли правило быть одобрено текущим пользователем"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model__abstract
msgid "Whether this model is abstract"
msgstr "Является ли эта модель абстрактной"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.xml:0
msgid "Which type of report do you want to create?"
msgstr "Тип отчета нужно создать?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_widget_properties.xml:0
msgid "Widget"
msgstr "Виджет"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Wow, nice! And I'm sure you can make it even better! Use this icon to open "
"<b>Odoo Studio</b> and customize any screen."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Write additional notes or comments"
msgstr "Напишите дополнительные заметки или комментарии"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "XML"
msgstr "XML"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Year"
msgstr "Год"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You can not approve this rule."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"You cannot cancel an approval you didn't set yourself or you don't belong to"
" an higher level rule's approvers."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "You cannot deactivate this view as it is the last one active."
msgstr ""
"Вы не можете деактивировать этот вид, так как он является последним "
"активным."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "You first need to create a many2many field in the form view."
msgstr "Сначала нужно создать поле many2many в представлении формы."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You just like to break things, don't you?"
msgstr "Тебе просто нравится все ломать, да?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "action"
msgstr "действие"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "delegates to %s."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "domain not defined"
msgstr "домен не определен"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "e.g. Properties"
msgstr "например, свойства"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "e.g. Real Estate"
msgstr "например, недвижимость"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "for company"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "no one"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "object"
msgstr "объект"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "or"
msgstr "или"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "studio_approval attribute can only be set in form views"
msgstr ""
"атрибут studio_approval может быть установлен только в представлениях форм"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "test email"
msgstr "тестовая почта"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "to %s."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload it"
msgstr "загрузить его"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload one"
msgstr "загрузить один"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "x_studio_"
msgstr "x_studio_"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "{{ item.isInEdition ? 'Add selection' : 'Edit selection' }}"
msgstr "{{ item.isInEdition ? 'Добавить выбор' : 'Редактировать выбор' }}"
