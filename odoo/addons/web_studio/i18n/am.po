# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_studio
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:01+0000\n"
"PO-Revision-Date: 2022-09-22 05:49+0000\n"
"Language-Team: Amharic (https://app.transifex.com/odoo/teams/41243/am/)\n"
"Language: am\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    This is your new action.\n"
"                </p>\n"
"                <p>By default, it contains a list and a form view and possibly\n"
"                    other view types depending on the options you chose for your model.\n"
"                </p>\n"
"                <p>\n"
"                    You can start customizing these screens by clicking on the Studio icon on the\n"
"                    top right corner (you can also customize this help message there).\n"
"                </p>\n"
"            "
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid ""
"\n"
"    There are no many2one fields related to the current model.\n"
"    To create a one2many field on the current model, you must first create its many2one counterpart on the model you want to relate to.\n"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Add a new report\n"
"            </p>\n"
"            "
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new automated action\n"
"            </p>\n"
"            "
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new filter\n"
"            </p>\n"
"            "
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "%s Report"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "'My model'"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "(t-if=\""
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new access control list\n"
"            </p>\n"
"            "
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "A field with the same name already exists."
msgstr ""

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_entry_uniq_combination
msgid "A rule can only be approved/rejected once per record."
msgstr ""

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_together
msgid "A rule must apply to an action or a method (but not both)."
msgstr ""

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_not_null
msgid "A rule must apply to an action or a method."
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model__abstract
msgid "Abstract"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Access Control"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Access Control Lists"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_groups
msgid "Access Groups"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Access records from cell"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Access records from graph"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_mail_activity_type__category
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__action_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Action"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window
msgid "Action Window"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Action to approve:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Action's title"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_mail_activity_type__category
msgid "Actions may trigger specific behavior like opening calendar view or automatically mark as done when a document is uploaded"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
msgid "Activate View"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__active
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Active"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#: model:ir.model,name:web_studio.model_mail_activity
msgid "Activity"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_activity_type
msgid "Activity Type"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Activity view unavailable on this model"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Add"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/chatter_container.xml:0
msgid "Add Chatter Widget"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
msgid "Add Picture"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "Add a Button"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Add a description..."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
msgid "Add a pipeline status bar"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_compiler.js:0
msgid "Add a priority"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Add an approval rule"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_compiler.js:0
msgid "Add an avatar"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Add details to your records with an embedded list view"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Add in the print menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Add new value"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record at the bottom"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record on top"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Add selection"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_compiler.js:0
msgid "Add tags"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Additional Fields"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Address Block"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
msgid "Aggregate"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Alignment"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "All Day"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "All set? You are just one click away from <b>generating your first app</b>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Amount total"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Amount untaxed"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approval"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approval Entries"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approval Group"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__rule_id
msgid "Approval Rule"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Approval Rules"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approvals"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals can only be done on a method or an action, not both."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approve"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__approved
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approved"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Approved as"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approved on"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__user_id
msgid "Approved/rejected by"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archive deprecated records"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Archived"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archiving"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Are you sure you want to remove the selection values?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
msgid "Are you sure you want to remove this %s from the view?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Are you sure you want to reset the background image?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/edition_flow.js:0
msgid ""
"Are you sure you want to restore the default view?\r\n"
"All customization done with studio on this view will be lost."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "As expression"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Ascending"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign a responsible to each record"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign dates and visualize records in a calendar"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Attach a picture to a record"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Autocompletion Fields"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_automation
msgid "Automated Action"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Automated Actions"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Automations"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
msgid "Average"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Average of %s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Awaiting approval"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Backwards"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Bar"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_base
msgid "Base"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Blank"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks_registry.js:0
msgid "Block"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Blocked"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Bold"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Bordered"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Borderless"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Business header/footer"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/report_editor_sidebar.js:0
msgid "By default: "
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Calendar"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Create"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Delete"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Edit"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__can_validate
msgid "Can be approved"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/legacy/js/common/new_field_dialog.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.simplified_form_view_import_module
msgid "Cancel"
msgstr "መሰረዝ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Categorize records with custom tags"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Change Background"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Change Image"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Change the <b>background</b>, make it yours."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Chatter"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "CheckBox"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model_data__studio
msgid "Checked if it has been edited with Studio."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Choose an app name"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Churn"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Class"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Clear"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Click here."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Click to edit messaging features on your model."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Click to see all approval rules."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/navbar/navbar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/xml_editor/xml_editor.xml:0
#: code:addons/web_studio/static/src/legacy/js/common/new_field_dialog.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.simplified_form_view_import_module
msgid "Close"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Cohort"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Color"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Colors"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks_registry.js:0
msgid "Column"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Column grouping"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Columns"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Compact"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_company
msgid "Companies"
msgstr "ድርጅት"

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Company"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Components"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/property/property.xml:0
msgid "Conditional"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__conditional
msgid "Conditional Rule"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Configuration"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Configure Model"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/legacy/js/common/new_field_dialog.js:0
msgid "Confirm"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Confirmation"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Contact"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Contact Field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid "Contact Field Required"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Contact details"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
msgid "Context"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Continue to configure some typical behaviors for your new type of object."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Could not change the background"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Create Menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
msgid "Create Model"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
msgid "Create a new Model"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Create your <b>selection values</b> (e.g.: Romance, Polar, Fantasy, etc.)"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your App"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your app"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your first menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Create your menu"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_uid
msgid "Created by"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_date
msgid "Created on"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Creating this type of view is not currently supported in Studio."
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Currency"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Custom Configuration"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_fields
msgid "Custom Fields"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_models
msgid "Custom Models"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_reports
msgid "Custom Reports"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Custom Sorting"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_views
msgid "Custom Views"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Custom color"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Custom field names cannot contain double underscores."
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom fields:"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom models:"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom reports:"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom views:"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Customization made with Studio will be permanently lost"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Customizations"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Data table"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Date"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date & Calendar"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date range & Gantt"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Datetime"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Day"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Day Precision"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Decimal"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Default Display Mode"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Default Group By"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Group by"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Scale"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: model:ir.model,name:web_studio.model_ir_default
msgid "Default Values"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Default value"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Default view"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Define start/end dates and visualize records in a Gantt chart"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Delay Field"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Delete"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Descending"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Description"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "Design your Icon"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Disable View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Discard changes"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__display_name
msgid "Display Name"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Total row"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Unavailability"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.js:0
msgid "Do you want to add a dropdown with colors?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__domain
msgid "Domain"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Done"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Drag & drop <b>another field</b>. Let’s try with a <i>selection field</i>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Drag a menu to the right to create a sub-menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/report_editor.js:0
msgid "Drag building block here"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Dropdown Menu"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Duplicate"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Edit"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_content_overlay.js:0
msgid "Edit %s view"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.js:0
msgid "Edit Application Icon"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.js:0
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit Menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/common/new_field_dialog.js:0
msgid "Edit Value"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Edit Values"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Edit selection"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/xml_editor/xml_editor.js:0
msgid "Editing a built-in file through this editor is not advised, as it will prevent it from being updated during future App upgrades."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Editing item:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Else"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Email"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Email Alias"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_template
msgid "Email Templates"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_thread
msgid "Email Thread"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Empty List Message"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Enable Mass Editing"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Enable Routing"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "End Date"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entry_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Entries"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#: code:addons/web_studio/static/src/legacy/js/common/abstract_editor_manager.js:0
msgid "Error"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Esc expression"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.xml:0
msgid "Existing Fields"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Existing Model"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Export"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "External"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Fadeout Speed"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Fast"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Favourites"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Field & Label"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Field Column"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.js:0
#: code:addons/web_studio/static/src/legacy/js/common/new_field_dialog.js:0
msgid "Field Properties"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Field expression"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Field in Cell"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.js:0
msgid "Field properties: %s"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_fields
msgid "Fields"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "File"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Filename for %s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Filter"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Filter Rules"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Filter label"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: model:ir.model,name:web_studio.model_ir_filters
msgid "Filters"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "First Status"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "First dimension"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Font style"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Foreach expression"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Form"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/xml_editor/xml_editor.xml:0
msgid "Format"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Forward"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Gantt"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "General views"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.js:0
msgid "Generate %s View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Get contact, phone and email fields on records"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Go on, you are almost done!"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Good job! To add more <b>fields</b>, come back to the <i>Add tab</i>."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: model:ir.model.fields.selection,name:web_studio.selection__mail_activity_type__category__grant_approval
#: model:mail.activity.type,name:web_studio.mail_activity_data_approve
msgid "Grant Approval"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Graph"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Graph View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Great !"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Grid View"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__group_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__group_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Group"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Group By"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Group by"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Grouping is not applied while in Studio to allow editing."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Groups used in approval rules must have an external identifier."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "HTML"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Day"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Hour"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Heading1"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Heading2"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Heading3"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Heading4"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Heading5"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Heading6"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Help Tooltip"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Here, you can <b>name</b> your field (e.g. Book reference, ISBN, Internal Note, etc.)."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Address"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Name"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Hide by default"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "High Priority"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_res_company__background_image
msgid "Home Menu Background Image"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Hour"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "How do you want to <b>name</b> your app? Library, Academy, …?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "How do you want to name your first <b>menu</b>? My books, My courses?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "I bet you can <b>build an app</b> in 5 minutes. Ready for the challenge?"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__id
msgid "ID"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Icon"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__domain
msgid "If set, the rule will only apply on records that match the domain."
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__exclusive_user
msgid "If set, the user who approves this rule will not be able to approve other rules for the same record"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "If you don't want to create a new model, an existing model should be selected."
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Image"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.simplified_form_view_import_module
msgid "Import"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "In Progress"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.js:0
msgid "In order to use a monetary field, you need a currency field on the model. Do you want to create a currency field first? You can make this field invisible afterwards."
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_ui_menu__is_studio_configuration
msgid "Indicates that this menu was created by Studio to hold configuration sub-menus"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks_registry.js:0
msgid "Inline"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Integer"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Internal"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Interval"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Invalid studio_approval %s in button"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Invisible"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__is_studio
msgid "Is Studio"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Is default view"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Italic"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Kanban"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Kanban State"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/group_properties/group_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/page_properties/page_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Label"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_uid
msgid "Last Updated by"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_date
msgid "Last Updated on"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Let’s check the result. Close Odoo Studio to get an <b>overview of your app</b>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__exclusive_user
msgid "Limit approver to this rule"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
#: code:addons/web_studio/static/src/legacy/xml/sidebar_common.xml:0
msgid "Limit visibility to groups"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Line"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Lines"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__mail_activity_id
msgid "Linked Activity"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "List"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "List View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor.xml:0
msgid "Loading..."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Manually sort records in the list view"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Many2Many"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Many2One"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Map"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Margins"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Measure"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Measure Field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Measures"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Medium"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message
msgid "Message"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__method
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__method
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Method"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Minimal header/footer"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Mode"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Model"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_access
msgid "Model Access"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_data
msgid "Model Data"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__model
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_name
msgid "Model Name"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Model name"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model
msgid "Models"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_module_module
msgid "Module"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_module_uninstall
msgid "Module Uninstall"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.simplified_form_view_import_module
msgid "Module file (.zip)"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Monetary"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Monetary value"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Month"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Month Precision"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: code:addons/web_studio/static/src/legacy/xml/sidebar_common.xml:0
msgid "More"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Multine Text"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Multiple records views"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "My %s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "My Button"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "My entries"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__name
msgid "Name"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "New %s"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "New Approval Entry"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "New Field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.xml:0
msgid "New Fields"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "New Filter"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New Lines"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "New Menu"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "New Model"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "New Page"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "New button"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Next"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Nicely done! Let’s build your screen now; <b>drag</b> a <i>text field</i> and <b>drop</b> it in your view, on the right."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
msgid "No aggregation"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "No approval found for this rule, record and user combination."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "No header/footer"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "No related many2one fields found"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "No view found for the given report!"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "None"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Normal"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Notes"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Now you’re on your own. Enjoy your <b>super power</b>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Now, customize your icon. Make it yours."
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entries_count
msgid "Number of Entries"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Odoo Studio"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
msgid "Odoo Studio Icon"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Offset"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "One2Many"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Only %s members can approve this rule."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Only groups with an external ID can be used here. Please choose another group or assign manually an external ID to this group."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Open form view"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Optional"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Options"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Order"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "PDF file"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_report_paperformat
msgid "Paper Format Config"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Paper format"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Parent Menu"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Partner"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Phone"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Picture"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Pie"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Pipeline stages"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Pipeline status bar"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Pivot"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
msgid "Placeholder"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Please specify a field name for the selected model."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Please specify a field."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.xml:0
msgid "Preview is not available"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Previous"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_manager.xml:0
msgid "Print"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Printed Report Name"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Priority"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Private methods cannot be restricted (since they cannot be called remotely, this would be useless)."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Properties"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Quarter Hour"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Quick Create"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Quick Create Field"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_qweb
msgid "Qweb"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Rainbow Effect"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Rainbow Man"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Readonly"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Ready"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Record"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__res_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__res_id
msgid "Record ID"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.xml:0
msgid "Redo"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__reference
msgid "Reference"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Reject"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Rejected"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Rejected as"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Rejected on"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Related Field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Related expression"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Relation:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Reload from attachment"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#: code:addons/web_studio/static/src/legacy/xml/sidebar_common.xml:0
msgid "Remove from View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Remove selection"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Report"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_report
msgid "Report Action"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Reporting views"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Reports"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Required"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Reset Default Background"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Reset Image"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__responsible_id
msgid "Responsible"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Restore Default View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Restrict a record to a specific company"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Retention"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Revoke"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - First level"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - Second level"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_rule
msgid "Rule"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Rules with existing entries cannot be deleted since it would delete existing approval entries. You should archive the rule instead."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Rules with existing entries cannot be modified since it would break existing approval entries. You should archive the rule and create a new one instead."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/xml_editor/xml_editor.xml:0
msgid "Save"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Save."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.xml:0
msgid "Saved"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.xml:0
msgid "Saving"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Search"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.xml:0
msgid "Search..."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Second Status"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Second dimension"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Select a related field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "Select a related field."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Select a related field:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
msgid "Select the contact field to use to get the coordinates of your records."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the model in relation to this one"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the reciprocal ManyToOne field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Selection"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Send a"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Send messages, log notes and schedule activities"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Separator"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Sequence"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_server
msgid "Server Action"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Set As Default"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_cover_properties/kanban_cover_properties.xml:0
msgid "Set Cover Image"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Set a price or cost on records"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Set an <b>email alias</b>. Then, try to send an email to this address; it will create a document automatically for you. Pretty cool, huh?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Set approval rules"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Set expression"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Show Invisible Elements"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Show by default"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Signature"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Size"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Slow"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Small"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Sort By"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Sort by"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Sorting"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Source"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Specify all possible values"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Stacked graph"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Stage"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Stage Name"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Stage and visualize records in a custom pipeline"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Start Date"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Start Date Field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Stop Date Field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Striped"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model_data__studio
msgid "Studio"
msgstr ""

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_app_creator
msgid "Studio App Creator"
msgstr ""

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_entry_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_entry
msgid "Studio Approval Entries"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_entry
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Studio Approval Entry"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_request
msgid "Studio Approval Request"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Studio Approval Rule"
msgstr ""

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_rule_action
msgid "Studio Approvals"
msgstr ""

#. module: web_studio
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_rule
msgid "Studio Approvals Rules"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_ui_menu__is_studio_configuration
msgid "Studio Configuration Menu"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_customizations_filter
msgid "Studio Customizations"
msgstr ""

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_mixin
msgid "Studio Mixin"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Subtotal & Total"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Suggested features for your new model"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_type_properties.js:0
msgid "Sum"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Sum of %s"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks_registry.js:0
msgid "Table"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Table style"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Tabs"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
msgid "Tags"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Taxes amount by groups"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Technical Name"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Template '%s' not found"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.js:0
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Text"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Text decoration"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Text in Cell"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "The fastest way to create a web application."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The field %s does not exist."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "The following approvals are missing:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_structures/view_structures.xml:0
msgid "The following fields are currently not in the view."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon has not a correct format"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon is not linked to an attachment"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s does not exist."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't support adding fields."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The operation  type \"%s\" is not supported"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "The record field name is missing"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The related field of a button has to be a many2one to %s."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#: code:addons/web_studio/static/src/legacy/js/common/abstract_editor_manager.js:0
msgid "The requested change caused an error in the view. It could be because a field was deleted, but still used somewhere else."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"There are %s records using selection values not listed in those you are trying to save.\n"
"Are you sure you want to remove the selection values of those records?"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "There is no method %s on the model %s (%s)"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Third Status"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/navbar.js:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
msgid "This action is not editable by Studio"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"This approval or the one you already submitted limits you to a single approval on this action.\n"
"Another user is required to further approve this action."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/sidebar_common.xml:0
msgid "This field is placed after another element that is only displayed under certain conditions."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#: code:addons/web_studio/static/src/legacy/js/common/abstract_editor_manager.js:0
msgid "This operation caused an error, probably because a xpath was broken"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/report_editor.js:0
msgid "This report could not be previewed or edited because it could not be rendered with this message: %s. This could be happening because this is a custom report type that needs custom data to be rendered and so is not editable by studio."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "This rule has already been approved/rejected."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "This rule limits this user to a single approval for this action."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Three Columns"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Timeline"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Timeline views"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Title Block"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "To <b>customize a field</b>, click on its <i>label</i>."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "To edit this document please create a record first"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
msgid "Toggle Studio"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Total"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "Two Columns"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Type"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Type down your notes here..."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Underline"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.xml:0
msgid "Undo"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "User"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "User assignment"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "User avatar placeholder"
msgstr ""

#. module: web_studio
#. odoo-python
#. odoo-javascript
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Value"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/legacy/xml/new_field_dialog.xml:0
msgid "Value of list"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_view
msgid "View"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Views"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Visible for"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Visible if"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Waiting for approval"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Want more fun? Let’s create more <b>views</b>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Want to customize the background? Let’s activate <b>Odoo Studio</b>."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Week"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Week Precision"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Welcome to"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "What about a <b>Kanban view</b>?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "When Creating Record"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__can_validate
msgid "Whether the rule can be approved by the current user"
msgstr ""

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model__abstract
msgid "Whether this model is abstract"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/type_widget_properties.xml:0
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Widget"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "Width"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/tours/web_studio.js:0
msgid "Wow, nice! And I’m sure you can make it even better! Use this icon to open <b>Odoo Studio</b> and customize any screen."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Write additional notes or comments"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: code:addons/web_studio/static/src/legacy/xml/sidebar_common.xml:0
msgid "XML"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Year"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "You can only display a user or a partner"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You cannot cancel an approval you didn't set yourself."
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "You cannot deactivate this view as it is the last one active."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "You cannot modify this view, it is part of the generic layout"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.js:0
msgid "You first need to create a many2many field in the form view."
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You just like to break things, don't you?"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/common/new_field_dialog.js:0
msgid "You must have at least one option set"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/common/new_field_dialog.js:0
msgid "You must select a related field"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/common/new_field_dialog.js:0
msgid "You must select a relation"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/sidebar_common.xml:0
msgid "You need to be part of these groups to see this field:"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/js/reports/sidebar_components/report_building_blocks.js:0
msgid "You need to use a many2many or one2many field to display a list of items"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "bottom"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "domain not defined"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
msgid "dropdown"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "e.g. Real Estate"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "left"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "or"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "px"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "reset color"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "right"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "studio_approval attribute can only be set in form views"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.xml:0
msgid "tabsDisplay.new.title"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.xml:0
msgid "tabsDisplay.properties.title"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.xml:0
msgid "tabsDisplay.view.title"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "test email"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/legacy/xml/report_editor_sidebar.xml:0
msgid "top"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload it"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload one"
msgstr ""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "x_studio_"
msgstr ""
