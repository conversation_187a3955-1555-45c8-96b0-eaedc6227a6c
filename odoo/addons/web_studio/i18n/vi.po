# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_studio
# 
# Translators:
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: Vietnamese (https://app.transifex.com/odoo/teams/41243/vi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: vi\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    This is your new action.\n"
"                </p>\n"
"                <p>By default, it contains a list and a form view and possibly\n"
"                    other view types depending on the options you chose for your model.\n"
"                </p>\n"
"                <p>\n"
"                    You can start customizing these screens by clicking on the Studio icon on the\n"
"                    top right corner (you can also customize this help message there).\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Đây là tác vụ mới của bạn.\n"
"                </p>\n"
"                <p>Theo mặc định, tác vụ này chứa một chế độ xem biểu mẫu và danh sách\n"
"                    và có thể là các loại chế độ xem khác tùy thuộc vào các tùy chọn bạn đã chọn cho mô hình của mình.\n"
"                </p>\n"
"                <p>\n"
"                    Bạn có thể bắt đầu tùy chỉnh các màn hình này bằng cách nhấp vào biểu tượng\n"
"                    Studio ở góc trên bên phải (bạn cũng có thể tùy chỉnh thông báo trợ giúp này ở đó).\n"
"                </p>\n"
"            "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid ""
"\n"
"    There are no many2one fields related to the current model.\n"
"    To create a one2many field on the current model, you must first create its many2one counterpart on the model you want to relate to.\n"
msgstr ""
"\n"
"    Không có trường many2one nào liên quan đến mô hình hiện tại.\n"
"    Để tạo trường one2many trên mô hình hiện tại, trước tiên bạn phải tạo trường many2one đối ứng trên mô hình mà bạn muốn liên kết.\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Add a new report\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Thêm một báo cáo mới\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new filter\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Thêm bộ lọc mới\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid " until %s"
msgstr " cho đến %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "\" failed."
msgstr "\" không thành công."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s delegated approval rights to %(delegate_to)s"
msgstr "%(user_name)s ủy quyền phê duyệt cho %(delegate_to)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"%(user_name)s has set approval rights from %(previous_approvers)s to "
"%(next_approvers)s"
msgstr ""
"%(user_name)s đã đặt quyền phê duyệt từ %(previous_approvers)s thành "
"%(next_approvers)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s revoked their delegation to %(revoked_users)s"
msgstr "%(user_name)s thu hồi uỷ quyền cho %(revoked_users)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "%s Report"
msgstr "%s Báo cáo"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_export_model.py:0
msgid "%s record(s)"
msgstr "%s bản ghi"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "<i role=\"img\" class=\"fa fa-user-times\" title=\"Exclusive approval\"/>"
msgstr "<i role=\"img\" class=\"fa fa-user-times\" title=\"Phê duyệt độc quyền\"/>"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new access control list\n"
"            </p>\n"
"            "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Thêm danh sách kiểm soát truy cập mới\n"
"            </p>\n"
"            "

#. module: web_studio
#: model_terms:web_tour.tour,rainbow_man_message:web_studio.web_studio_new_app_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr ""
"<span><b>Tốt lắm!</b> Bạn đã xem qua tất cả các bước của tour hướng dẫn "
"này.</span>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "A field with the same name already exists."
msgstr "Trường có cùng tên đã tồn tại."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_entry_uniq_combination
msgid "A rule can only be approved/rejected once per record."
msgstr "Quy tắc chỉ có thể được phê duyệt/từ chối một lần cho mỗi bản ghi."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_together
msgid "A rule must apply to an action or a method (but not both)."
msgstr ""
"Quy tắc phải áp dụng cho một tác vụ hoặc một phương pháp (không phải cả "
"hai)."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_not_null
msgid "A rule must apply to an action or a method."
msgstr "Quy tắc phải áp dụng cho một tác vụ hoặc một phương pháp."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model__abstract
msgid "Abstract"
msgstr "Trừu tượng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Access Control"
msgstr "Kiểm soát quyền truy cập"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Access Control Lists"
msgstr "Danh sách kiểm soát truy cập"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_groups
msgid "Access Groups"
msgstr "Nhóm truy cập"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Access records from cell"
msgstr "Truy cập bản ghi từ ô"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Access records from graph"
msgstr "Truy cập bản ghi từ biểu đồ"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_mail_activity_type__category
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__action_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Action"
msgstr "Tác vụ"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction
msgid "Action Needed"
msgstr "Tác vụ cần thiết"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window
msgid "Action Window"
msgstr "Cửa sổ tác vụ"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Chế độ xem cửa sổ tác vụ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Action to approve:"
msgstr "Tác vụ cần phê duyệt:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Action to run"
msgstr "Tác vụ cần chạy"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Action's title"
msgstr "Tiêu đề tác vụ"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Action: %s"
msgstr "Tác vụ: %s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_actions
msgid "Actions"
msgstr "Tác vụ"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Tác vụ có thể kích hoạt thao tác cụ thể như mở chế độ xem lịch hoặc tự động "
"đánh dấu là hoàn thành khi tài liệu được tải lên"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
msgid "Activate View"
msgstr "Kích hoạt chế độ xem"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__active
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Active"
msgstr "Đang hoạt động"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#: model:ir.model,name:web_studio.model_mail_activity
msgid "Activity"
msgstr "Hoạt động"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_activity_type
msgid "Activity Type"
msgstr "Loại hoạt động"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Activity view unavailable on this model"
msgstr "Chế độ xem hoạt động không khả dụng với mô hình này"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Add"
msgstr "Thêm"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/chatter_container.xml:0
msgid "Add Chatter Widget"
msgstr "Thêm tiện ích trò chatter"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add Picture"
msgstr "Thêm ảnh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Add a Button"
msgstr "Thêm một nút"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Add a button"
msgstr "Thêm một nút"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add a pipeline status bar"
msgstr "Thêm thanh trạng thái chu trình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add a priority"
msgstr "Thêm mức độ ưu tiên"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Add an approval step"
msgstr "Thêm bước phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add an avatar"
msgstr "Thêm hình đại diện"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Add details to your records with an embedded list view"
msgstr "Thêm chi tiết vào bản ghi của bạn với chế độ xem danh sách được nhúng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Add new value"
msgstr "Thêm giá trị mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record at the bottom"
msgstr "Thêm bản ghi ở dưới cùng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record on top"
msgstr "Thêm bản ghi ở trên cùng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add tags"
msgstr "Thêm thẻ"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_export_data
msgid "Additional Export Data"
msgstr "Xuất dữ liệu bổ sung"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Additional Fields"
msgstr "Trường bổ sung"

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid "Additional Studio Exports"
msgstr "Xuất studio bổ sung"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_models
msgid "Additional models to export"
msgstr "Mô hình bổ sung cần xuất"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_wizard__additional_models
msgid ""
"Additional models you may choose to export in addition to the Studio "
"customizations"
msgstr ""
"Các mô hình bổ sung bạn có thể chọn để xuất ngoài các tùy chỉnh Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Aggregate"
msgstr "Tổng hợp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "All Day"
msgstr "Cả ngày"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"All changes done to the report's structure will be discarded and the report "
"will be reset to its factory settings."
msgstr ""
"Mọi thay đổi được thực hiện đối với cấu trúc của báo cáo sẽ bị hủy và báo "
"cáo sẽ được đặt lại về cài đặt gốc."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"All set? You are just one click away from <b>generating your first app</b>."
msgstr ""
"Tất cả đã được thiết lập xong? Bạn chỉ cần một cú nhấp chuột để <b>tạo ứng "
"dụng đầu tiên</b>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Allow Resequencing"
msgstr "Cho phép sắp xếp lại thứ tự"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Allow visibility to groups"
msgstr "Cho phép hiển thị cho các nhóm"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "An approval is missing"
msgstr "Thiếu phê duyệt"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Apply"
msgstr "Áp dụng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approval"
msgstr "Phê duyệt"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approval Entries"
msgstr "Các mục phê duyệt"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approval_group_id
msgid "Approval Group"
msgstr "Nhóm phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approval Order"
msgstr "Thứ tự phê duyệt"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approval_rule_id
msgid "Approval Rule"
msgstr "Quy tắc phê duyệt"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_approver
msgid "Approval Rule Approvers Enriched"
msgstr "Approval Rule Approvers Enriched"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_delegate
msgid "Approval Rule Delegate"
msgstr "Approval Rule Delegate"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
msgid "Approval Rules"
msgstr "Quy tắc phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approvals"
msgstr "Phê duyệt"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals %(model_name)s"
msgstr "Phê duyệt %(model_name)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals can only be done on a method or an action, not both."
msgstr ""
"Phê duyệt chỉ có thể được thực hiện trên một phương pháp hoặc một tác vụ, "
"không phải cả hai."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals missing"
msgstr "Thiếu phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approve"
msgstr "Phê duyệt"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__approved
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approved"
msgstr "Đã phê duyệt"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Approved <i class=\"fa fa-thumbs-up text-success\"/>"
msgstr "Đã phê duyệt <i class=\"fa fa-thumbs-up text-success\"/>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approved on"
msgstr "Đã phê duyệt vào"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__user_id
msgid "Approved/rejected by"
msgstr "Được phê duyệt/từ chối bởi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approver Group"
msgstr "Nhóm người phê duyệt"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_log_ids
msgid "Approver Log"
msgstr "Nhật ký người phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_ids
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approver_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Approvers"
msgstr "Người phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archive deprecated records"
msgstr "Lưu trữ các bản ghi không dùng nữa"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Archived"
msgstr "Đã lưu trữ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archiving"
msgstr "Đang lưu trữ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Are you sure you want to remove the selection values?"
msgstr "Bạn có chắc chắn muốn xóa các giá trị lựa chọn không?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
msgid "Are you sure you want to remove this %s from the view?"
msgstr "Bạn có chắc muốn xóa %s khỏi chế độ xem?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Are you sure you want to reset the background image?"
msgstr "Bạn có chắc chắn muốn cài đặt lại hình nền không?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/edition_flow.js:0
msgid ""
"Are you sure you want to restore the default view?\r\n"
"All customization done with studio on this view will be lost."
msgstr ""
"Bạn có chắc chắn muốn khôi phục chế độ xem mặc định không?\r\n"
"Tất cả các tùy chỉnh được thực hiện với studio trên chế độ xem này sẽ bị mất."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__is_demo_data
msgid "As Demo"
msgstr "Demo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Ascending"
msgstr "Tăng dần"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign a responsible to each record"
msgstr "Phân công người chịu trách nhiệm cho từng bản ghi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign dates and visualize records in a calendar"
msgstr "Gán ngày tháng và trực quan hóa các bản ghi trong lịch"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Attach a picture to a record"
msgstr "Đính kèm ảnh vào bản ghi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_attachment_count
msgid "Attachment Count"
msgstr "Số tệp đính kèm"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__include_attachment
msgid "Attachments"
msgstr "Tệp đính kèm"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Autocompletion Fields"
msgstr "Trường tự động hoàn thành"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_automation
msgid "Automation Rule"
msgstr "Quy tắc tự động"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Automations"
msgstr "Tự động hóa"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Average"
msgstr "Trung bình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Average of %s"
msgstr "Trung bình của %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Awaiting approval"
msgstr "Chờ phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Backwards"
msgstr "Lùi lại"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Bar"
msgstr "Cột"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base
msgid "Base"
msgstr "Cơ sở"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Blank"
msgstr "Trống"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Blocked"
msgstr "Đã bị chặn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Bold"
msgstr "In đậm"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Business header/footer"
msgstr "Header/footer kinh doanh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.js:0
msgid "Button"
msgstr "Nút"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Buttons Properties"
msgstr "Thuộc tính nút"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/aside_properties/aside_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/div_properties/div_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/footer_properties/footer_properties.xml:0
msgid "CSS style"
msgstr "Kiểu CSS"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Calendar"
msgstr "Lịch"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Call a method"
msgstr "Gọi một phương thức"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Create"
msgstr "Có thể tạo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Delete"
msgstr "Có thể xóa"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Edit"
msgstr "Có thể sửa"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__can_validate
msgid "Can be approved"
msgstr "Có thể được phê duyệt"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch 'create', 'write' and 'unlink'."
msgstr "Không thể vá 'tạo', 'ghi' và 'hủy liên kết'."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch private methods."
msgstr "Không thể vá các phương thức riêng tư."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Cancel"
msgstr "Hủy"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Categorize records with custom tags"
msgstr "Phân loại bản ghi bằng các thẻ tùy chỉnh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Change Background"
msgstr "Đổi hình nền"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Change Image"
msgstr "Đổi ảnh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Chatter"
msgstr "Chatter"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "CheckBox"
msgstr "Hộp kiểm"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model_data__studio
msgid "Checked if it has been edited with Studio."
msgstr "Đánh dấu nếu đã được chỉnh sửa với Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Choose an app name"
msgstr "Chọn tên ứng dụng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Choose the name of the menu"
msgstr "Chọn tên của menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Churn"
msgstr "Đảo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.xml:0
msgid "Class"
msgstr "Lớp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Click to edit messaging features on your model."
msgstr "Nhấp để chỉnh sửa các tính năng nhắn tin trên model của bạn."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Click to see all approval rules."
msgstr "Nhấp để xem tất cả các quy tắc phê duyệt."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/navbar/navbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Close"
msgstr "Đóng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Cohort"
msgstr "Cohort"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Color"
msgstr "Màu sắc"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Color Picker"
msgstr "Bộ chọn màu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Column"
msgstr "Cột"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Column grouping"
msgstr "Nhóm cột"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_company
msgid "Companies"
msgstr "Công ty"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Company"
msgstr "Công ty"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_structures.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Components"
msgstr "Nguyên liệu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Conditional"
msgstr "Điều kiện"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__conditional
msgid "Conditional Rule"
msgstr "Quy tắc có điều kiện"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Configuration"
msgstr "Cấu hình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Configure Model"
msgstr "Cấu hình mô hình"

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid ""
"Configure additional models to export with Studio, such as records that hold"
" configuration information or demo data."
msgstr ""
"Cấu hình các mô hình bổ sung để xuất bằng Studio, chẳng hạn như các bản ghi "
"chứa thông tin cấu hình hoặc dữ liệu demo."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Configure data and demo data to export"
msgstr "Cấu hình dữ liệu và dữ liệu demo để xuất"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Configure model"
msgstr "Cấu hình mô hình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Confirm"
msgstr "Xác nhận"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Confirmation"
msgstr "Xác nhận"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Contact"
msgstr "Liên hệ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Contact Field"
msgstr "Trường liên hệ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid "Contact Field Required"
msgstr "Trường liên hệ bắt buộc"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Contact details"
msgstr "Chi tiết liên hệ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Context"
msgstr "Ngữ cảnh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Continue to configure some typical behaviors for your new type of object."
msgstr ""
"Tiếp tục cấu hình một số hành vi điển hình cho loại đối tượng mới của bạn."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Could not change the background"
msgstr "Không thể thay đổi hình nền"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Create Menu"
msgstr "Tạo menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
msgid "Create Model"
msgstr "Tạo mô hình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Create a new Model"
msgstr "Tạo một mô hình mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Create your <b>selection values</b> (e.g.: Romance, Polar, Fantasy, etc.)"
msgstr ""
"Tạo <b>giá trị lựa chọn</b> của bạn (ví dụ: Lãng mạn, Cực, Giả tưởng,...)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your App"
msgstr "Tạo ứng dụng của bạn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your app"
msgstr "Tạo ứng dụng của bạn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your first menu"
msgstr "Tạo menu đầu tiên của bạn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Create your menu"
msgstr "Tạo menu của bạn"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_uid
msgid "Created by"
msgstr "Được tạo bởi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_date
msgid "Created on"
msgstr "Được tạo vào"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Creating this type of view is not currently supported in Studio."
msgstr "Việc tạo chế độ xem này hiện không được hỗ trợ trong Studio."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Currency"
msgstr "Tiền tệ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Current model:"
msgstr "Mô hình hiện tại:"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Custom Configuration"
msgstr "Cấu hình tùy chỉnh"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_fields
msgid "Custom Fields"
msgstr "Trường tùy chỉnh"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_models
msgid "Custom Models"
msgstr "Mô hình tùy chỉnh"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_reports
msgid "Custom Reports"
msgstr "Báo cáo tùy chỉnh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Custom Sorting"
msgstr "Sắp xếp tùy chỉnh"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_views
msgid "Custom Views"
msgstr "Chế độ xem tùy chỉnh"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Custom field names cannot contain double underscores."
msgstr "Tên trường tùy chỉnh không thể chứa dấu gạch dưới kép."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom fields:"
msgstr "Trường tùy chỉnh:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom models:"
msgstr "Mô hình tùy chỉnh:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom reports:"
msgstr "Báo cáo tùy chỉnh:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom views:"
msgstr "Chế độ xem tùy chỉnh:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Customization made with Studio will be permanently lost"
msgstr "Tùy chỉnh được thực hiện với Studio sẽ vĩnh viễn bị mất"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "DIFF"
msgstr "DIFF"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Date"
msgstr "Ngày"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date & Calendar"
msgstr "Ngày & lịch"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__date_to
msgid "Date To"
msgstr "Đến ngày"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date range & Gantt"
msgstr "Phạm vi ngày & gantt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Datetime"
msgstr "Ngày giờ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Day"
msgstr "Ngày"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Day Precision"
msgstr "Độ chính xác theo ngày"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Decimal"
msgstr "Số thập phân"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Default Display Mode"
msgstr "Chế độ hiển thị mặc định"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__default_export_data
msgid "Default Export Data"
msgstr "Xuất dữ liệu mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Default Group By"
msgstr "Nhóm theo mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Group by"
msgstr "Nhóm theo mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Scale"
msgstr "Quy mô mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: model:ir.model,name:web_studio.model_ir_default
msgid "Default Values"
msgstr "Giá trị Mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Default value"
msgstr "Giá trị mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Default view"
msgstr "Chế độ xem mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Define start/end dates and visualize records in a Gantt chart"
msgstr ""
"Xác định ngày bắt đầu/ngày kết thúc và trực quan hóa các bản ghi trong biểu "
"đồ Gantt"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__updatable
msgid "Defines if the records would be updated during a module update."
msgstr ""
"Xác định xem các bản ghi có được cập nhật trong quá trình cập nhật phân hệ "
"hay không."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__notification_order
msgid "Defines the sequential order in which the approvals are requested."
msgstr "Xác định thứ tự tuần tự mà các yêu cầu phê duyệt được yêu cầu."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Delay Field"
msgstr "Trường trễ"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Delegate"
msgstr "Uỷ quyền"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Delegate to"
msgstr "Uỷ quyền cho"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Delete"
msgstr "Xoá"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__is_demo_data
msgid "Demo"
msgstr "Demo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Dense"
msgstr "Dense"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Descending"
msgstr "Giảm dần"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message
msgid "Description"
msgstr "Mô tả"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "Design your Icon"
msgstr "Thiết kế biểu tượng của bạn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Disable View"
msgstr "Tắt chế độ xem"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Discard"
msgstr "Huỷ bỏ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Discard changes"
msgstr "Loại bỏ những thay đổi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Mode"
msgstr "Chế độ hiển thị"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__display_name
msgid "Display Name"
msgstr "Tên hiển thị"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Total row"
msgstr "Hiển thị tổng hàng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Unavailability"
msgstr "Hiển thị không khả dụng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Displays a textual hint that helps the user when the field is empty."
msgstr "Hiển thị gợi ý bằng văn bản để trợ giúp người dùng khi trường trống."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "Do you want to add a dropdown with colors?"
msgstr "Bạn có muốn thêm menu thả xuống có màu không?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__domain
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__domain
msgid "Domain"
msgstr "Miền"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Done"
msgstr "Hoàn tất"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Drag & drop <b>another field</b>. Let's try with a <i>selection field</i>."
msgstr ""
"Kéo và thả <b>một trường khác</b>. Hãy thử với một <i>trường lựa chọn</i>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Drag a menu to the right to create a sub-menu"
msgstr "Kéo một menu về bên phải để tạo menu phụ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.js:0
msgid "Dropdown"
msgstr "Thả xuống"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Duplicate"
msgstr "Nhân bản"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Dynamic Table"
msgstr "Bảng động"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Edit"
msgstr "Chỉnh sửa"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_content_overlay.js:0
msgid "Edit %s view"
msgstr "Chỉnh sửa %s chế độ xem"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
msgid "Edit Application Icon"
msgstr "Chỉnh sửa biểu tượng ứng dụng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/menu_properties/menu_properties.xml:0
msgid "Edit Color Picker"
msgstr "Chỉnh sửa bộ chọn màu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit Menu"
msgstr "Chỉnh sửa menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Edit Values"
msgstr "Chỉnh sửa giá trị"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit selection"
msgstr "Chỉnh sửa lựa chọn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Edit sources"
msgstr "Chỉnh sửa nguồn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.js:0
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""
"Không nên chỉnh sửa tệp tích hợp thông qua trình chỉnh sửa này vì sẽ không "
"cập nhật được tệp đó trong các lần nâng cấp Ứng dụng trong tương lai."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Editing item:"
msgstr "Mục chỉnh sửa:"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Email"
msgstr "Email"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Email Alias"
msgstr "Bí danh email"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_template
msgid "Email Templates"
msgstr "Mẫu email"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_thread
msgid "Email Thread"
msgstr "Luồng email"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Empty List Message"
msgstr "Thông báo danh sách trống"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Enable Mass Editing"
msgstr "Bật chỉnh sửa hàng loạt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Enable Routing"
msgstr "Bật định tuyến"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "End Date"
msgstr "Ngày kết thúc"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entry_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Entries"
msgstr "Mục"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Error"
msgstr "Lỗi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error message:"
msgstr "Thông báo lỗi:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error name:"
msgstr "Tên lỗi:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__exclusive_user
msgid "Exclusive Approval"
msgstr "Phê duyệt độc quyền"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Existing Fields"
msgstr "Trường tồn tại"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Existing Model"
msgstr "Mô hình hiện có"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Export"
msgstr "Xuất"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "External"
msgstr "Bên ngoài"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_xmlid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__xmlid
msgid "External ID"
msgstr "ID bên ngoài"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Fadeout Speed"
msgstr "Tốc độ xuất ra"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Fast"
msgstr "Nhanh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Favourites"
msgstr "Ưa thích"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Field"
msgstr "Trường"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Field Properties"
msgstr "Thuộc tính trường"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.js:0
msgid "Field properties: %s"
msgstr "Thuộc tính trường: %s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_fields
msgid "Fields"
msgstr "Trường "

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__excluded_fields
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
msgid "Fields to exclude"
msgstr "Trường cần loại trừ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "File"
msgstr "Tệp"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Filename for %s"
msgstr "Tên tệp cho %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Filter"
msgstr "Bộ lọc"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Filter Rules"
msgstr "Quy tắc bộ lọc"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Filter label"
msgstr "Nhãn bộ lọc"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: model:ir.model,name:web_studio.model_ir_filters
msgid "Filters"
msgstr "Bộ lọc"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "First Status"
msgstr "Trạng thái đầu tiên"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "First dimension"
msgstr "Chiều đầu tiên"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_follower_ids
msgid "Followers"
msgstr "Người theo dõi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_partner_ids
msgid "Followers (Partners)"
msgstr "Người theo dõi (Đối tác)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Footer"
msgstr "Footer"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"For compatibility purpose with base_automation,approvals on 'create', "
"'write' and 'unlink' methods are forbidden."
msgstr ""
"Để tương thích với base_automation, phê duyệt các phương thức 'tạo', 'ghi' "
"và 'hủy liên kết' đều bị cấm."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Forbid visibility to groups"
msgstr "Cấm hiển thị cho các nhóm"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Forever"
msgstr "Mãi mãi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Form"
msgstr "Biểu mẫu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Format"
msgstr "Định dạng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Forward"
msgstr "Chuyển tiếp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Gantt"
msgstr "Gantt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "General views"
msgstr "Chế độ xem chung"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.js:0
msgid "Generate %s View"
msgstr "Tạo %s chế độ xem"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Get contact, phone and email fields on records"
msgstr "Nhận các trường liên hệ, điện thoại và email trong bản ghi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Go back"
msgstr "Trở lại"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Go on, you are almost done!"
msgstr "Tiếp tục, bạn sắp hoàn tất rồi!"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Good job! To add more <b>fields</b>, come back to the <i>Add tab</i>."
msgstr ""
"Làm tốt lắm! Để thêm các <b>trường</b> khác, hãy quay lại <i>Thêm tab</i>."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: model:ir.model.fields.selection,name:web_studio.selection__mail_activity_type__category__grant_approval
#: model:mail.activity.type,name:web_studio.mail_activity_data_approve
msgid "Grant Approval"
msgstr "Phê duyệt grant"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Graph"
msgstr "Biểu đồ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Great !"
msgstr "Thật tuyệt vời!"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_renderer.xml:0
msgid "Group"
msgstr "Nhóm"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Group By"
msgstr "Nhóm theo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Group by"
msgstr "Nhóm theo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Grouping is not applied while in Studio to allow editing."
msgstr ""
"Khi ở trong Studio, việc nhóm không được áp dụng để cho phép chỉnh sửa."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "HTML"
msgstr "HTML"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_http
msgid "HTTP Routing"
msgstr "Định tuyến HTTP"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Day"
msgstr "Nửa ngày"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Hour"
msgstr "Nửa giờ"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__has_message
msgid "Has Message"
msgstr "Có tin nhắn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"Header and footer are shared with other reports which may be impacted by the"
" reset."
msgstr ""
"Việc đặt lại có thể ảnh hưởng đến header và footer được chia sẻ với các báo "
"cáo khác."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Help Tooltip"
msgstr "Tooltip trợ giúp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Here, you can <b>name</b> your field (e.g. Book reference, ISBN, Internal "
"Note, etc.)."
msgstr ""
"Tại đây, bạn có thể <b>đặt tên</b> cho trường của mình (ví dụ: Tham chiếu "
"sách, ISBN, Ghi chú nội bộ,...)."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Address"
msgstr "Ẩn địa chỉ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Name"
msgstr "Ẩn tên"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Hide by default"
msgstr "Ẩn theo mặc định"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "High Priority"
msgstr "Mức độ ưu tiên cao"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Highlight Color Field"
msgstr "Trường màu tô màu"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_res_company__background_image
msgid "Home Menu Background Image"
msgstr "Ảnh hình nền menu màn hình chính"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Hour"
msgstr "Giờ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to <b>name</b> your app? Library, Academy, …?"
msgstr ""
"Bạn muốn <b>đặt tên</b> ứng dụng của mình như thế nào? Thư viện, Học viện,…?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to name your first <b>menu</b>? My books, My courses?"
msgstr ""
"Bạn muốn đặt tên cho <b>menu</b> đầu tiên của mình như thế nào? Sách của "
"tôi, khóa học của tôi?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"I bet you can <b>build an app</b> in 5 minutes. Ready for the challenge?"
msgstr ""
"Cá là bạn có thể <b>tạo một ứng dụng</b> trong vòng 5 phút. Bạn đã sẵn sàng "
"cho thử thách này chưa?"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__id
msgid "ID"
msgstr "ID"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "IDs"
msgstr "ID"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Icon"
msgstr "Biểu tượng"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Nếu chọn, bạn cần chú ý tới các tin nhắn mới."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Nếu chọn, một số tin nhắn sẽ có lỗi gửi."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__include_attachment
msgid ""
"If set, the attachments related to the exported records will be included in "
"the export."
msgstr ""
"Nếu được thiết lập, các tệp đính kèm liên quan đến bản ghi đã xuất sẽ được "
"bao gồm trong bản xuất."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__is_demo_data
msgid ""
"If set, the exported records will be considered as demo data during the "
"import."
msgstr ""
"Nếu được thiết lập, các bản ghi đã xuất sẽ được coi là dữ liệu demo trong "
"quá trình nhập."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__domain
msgid "If set, the rule will only apply on records that match the domain."
msgstr "Nếu được đặt, quy tắc sẽ chỉ áp dụng cho các bản ghi khớp với miền."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__exclusive_user
msgid ""
"If set, the user who approves this rule will not be able to approve other "
"rules for the same record"
msgstr ""
"Nếu được đặt, người dùng phê duyệt quy tắc này sẽ không thể phê duyệt các "
"quy tắc khác cho cùng một bản ghi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Nếu bạn hủy các chỉnh sửa hiện tại, tất cả các thay đổi chưa được lưu sẽ bị "
"mất. Bạn có thể hủy để quay lại chế độ chỉnh sửa."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"If you don't want to create a new model, an existing model should be "
"selected."
msgstr ""
"Nếu bạn không muốn tạo một mô hình mới, bạn nên chọn một mô hình có sẵn."

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Image"
msgstr "Hình ảnh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Import"
msgstr "Nhập"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "In Progress"
msgstr "Đang thực hiện"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_additional_data
msgid "Include Data"
msgstr "Bao gồm dữ liệu"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_demo_data
msgid "Include Demo Data"
msgstr "Bao gồm dữ liệu demo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Include header and footer"
msgstr "Bao gồm header và footer"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_ui_menu__is_studio_configuration
msgid ""
"Indicates that this menu was created by Studio to hold configuration sub-"
"menus"
msgstr ""
"Cho biết rằng menu này được tạo bởi Studio để giữ các menu cấu hình phụ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field"
msgstr "Chèn một trường"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field..."
msgstr "Chèn một trường..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a table based on a relational field."
msgstr "Chèn bảng dựa trên trường quan hệ."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert left"
msgstr "Chèn bên trái"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert right"
msgstr "Chèn bên phải"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Integer"
msgstr "Integer"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Internal"
msgstr "Nội bộ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Interval"
msgstr "Khoảng thời gian"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Invalid studio_approval %s in button"
msgstr "Studio_approval %s không hợp lệ trong nút"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Invisible"
msgstr "Không hiển thị"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__is_delegation
msgid "Is Delegation"
msgstr "Là uỷ quyền"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_is_follower
msgid "Is Follower"
msgstr "Là người theo dõi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__is_studio
msgid "Is Studio"
msgstr "Là Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Is default view"
msgstr "Là chế độ xem mặc định"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "It lacks a method to check."
msgstr "Thiếu một phương thức để kiểm tra."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Kanban"
msgstr "Kanban"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__kanban_color
msgid "Kanban Color"
msgstr "Màu kanban"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Kanban State"
msgstr "Trạng thái kanban"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/group_properties/group_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/page_properties/page_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Label"
msgstr "Nhãn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Label of the button"
msgstr "Nhãn của nút"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_uid
msgid "Last Updated by"
msgstr "Cập nhật lần cuối bởi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_date
msgid "Last Updated on"
msgstr "Cập nhật lần cuối vào"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "Leave DIFF"
msgstr "Leave DIFF"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_leave_with
msgid "Leave Studio with another action"
msgstr "Rời khỏi Studio với một hành động khác"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Let's check the result. Close Odoo Studio to get an <b>overview of your "
"app</b>."
msgstr ""
"Hãy kiểm tra kết quả. Đóng Odoo Studio để xem <b>tổng quan ứng dụng của "
"bạn</b>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Limit visibility to groups"
msgstr "Giới hạn hiển thị cho các nhóm"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Line"
msgstr "Dòng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Lines"
msgstr "Chi tiết"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__mail_activity_id
msgid "Linked Activity"
msgstr "Hoạt động liên kết"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "List"
msgstr "Danh sách"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Manually sort records in the list view"
msgstr "Sắp xếp bản ghi theo cách thủ công trong chế độ xem danh sách"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2Many"
msgstr "Many2Many"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2One"
msgstr "Many2One"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Map"
msgstr "Bản đồ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid ""
"Map views are based on the address of a linked Contact. You need to have a "
"Many2one field linked to the res.partner model in order to create a map "
"view."
msgstr ""
"Chế độ xem bản đồ dựa trên địa chỉ của Liên hệ liên kết. Bạn cần liên kết "
"trường Many2one với mô hình res.partner để tạo chế độ xem bản đồ."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Measure"
msgstr "Thước đo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Measure Field"
msgstr "Trường Thước đo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Measures"
msgstr "Thước đo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Medium"
msgstr "Phương tiện"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Message"
msgstr "Tin nhắn"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error
msgid "Message Delivery error"
msgstr "Lỗi gửi tin nhắn"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_ids
msgid "Messages"
msgstr "Tin nhắn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__method
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__method
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Method"
msgstr "Phương thức"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Method to run"
msgstr "Phương thức cần chạy"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Method: %s"
msgstr "Phương pháp: %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Minimal header/footer"
msgstr "Header/footer tối thiểu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Mode"
msgstr "Chế độ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Model"
msgstr "Mô hình"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/wizard/studio_export_wizard.py:0
msgid "Model '%s' should not contain records with the same ID."
msgstr "Mô hình '%s' không được chứa các bản ghi có cùng ID."

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_access
msgid "Model Access"
msgstr "Truy cập mô hình"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_data
msgid "Model Data"
msgstr "Dữ liệu mô hình"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model_name
msgid "Model Description"
msgstr "Mô tả đối tượng"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__model
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_name
msgid "Model Name"
msgstr "Tên mô hình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Model name"
msgstr "Tên mẫu phương tiện"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model
msgid "Models"
msgstr "Mô hình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Modified by:"
msgstr "Được sửa đổi bởi:"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_module_module
msgid "Module"
msgstr "Phân hệ"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Gỡ cài đặt phân hệ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Monetary"
msgstr "Tiền tệ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Monetary value"
msgstr "Giá trị tiền tệ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month"
msgstr "Tháng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month (expanded)"
msgstr "Tháng (mở rộng)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Month Precision"
msgstr "Độ chính xác theo tháng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "More"
msgstr "Thêm"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Multine Text"
msgstr "Văn bản nhiều dòng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Multiple records views"
msgstr "Chế độ xem đa bản ghi"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "My %s"
msgstr "%s của tôi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "My Button"
msgstr "Nút của tôi"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "My entries"
msgstr "Mục của tôi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "N/A"
msgstr "N/A"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__name
msgid "Name"
msgstr "Tên"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New"
msgstr "Mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "New %s"
msgstr "Mới %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
msgid "New App"
msgstr "Ứng dụng mới"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "New Approval Entry"
msgstr "Mục phê duyệt mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "New Field"
msgstr "Trường mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "New Fields"
msgstr "Trường mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "New Filter"
msgstr "Bộ lọc mới"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New Lines"
msgstr "Dòng mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "New Menu"
msgstr "Menu mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "New Model"
msgstr "Mô hình mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "New Page"
msgstr "Trang mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "New button"
msgstr "Nút mới"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Next"
msgstr "Tiếp theo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Nicely done! Let's build your screen now; <b>drag</b> a <i>text field</i> "
"and <b>drop</b> it in your view, on the right."
msgstr ""
"Tuyệt vời! Bây giờ, hãy tạo màn hình của bạn; <b>kéo</b> một <i>trường văn "
"bản</i> và <b>thả</b> trường này trong chế độ xem, ở bên phải."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "No aggregation"
msgstr "Không có tổng hợp"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "No approval found for this rule, record and user combination."
msgstr ""
"Không tìm thấy phê duyệt nào cho quy tắc, bản ghi và kết hợp người dùng này."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "No header/footer"
msgstr "Không có header/footer"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "No related many2one fields found"
msgstr "Không tìm thấy trường many2one liên quan nào"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "None"
msgstr "Không"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Notes"
msgstr "Ghi chú"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__users_to_notify
msgid "Notify to"
msgstr "Thông báo cho"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now you're on your own. Enjoy your <b>super power</b>."
msgstr ""
"Bây giờ bạn đang ở không gian của riêng bạn. Hãy tận hưởng <b>sức mạnh siêu "
"việt</b> của mình."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now, customize your icon. Make it yours."
msgstr "Bây giờ, hãy tùy chỉnh biểu tượng và biến nó thành của bạn."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of Actions"
msgstr "Số lượng tác vụ"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entries_count
msgid "Number of Entries"
msgstr "Số mục"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of errors"
msgstr "Số lượng lỗi"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Số tin nhắn cần xử lý"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Số tin nhắn bị gửi lỗi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Odoo Studio"
msgstr "Odoo Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "On view (ir.ui.view):"
msgstr "Trên chế độ xem (ir.ui.view):"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "One2Many"
msgstr "One2Many"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"Only groups with an external ID can be used here. Please choose another "
"group or assign manually an external ID to this group."
msgstr ""
"Chỉ những nhóm có ID bên ngoài mới có thể được sử dụng ở đây. Vui lòng chọn "
"một nhóm khác hoặc gán ID bên ngoài cho nhóm này theo cách thủ công."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Open Record On Click"
msgstr "Mở bản ghi khi nhấp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Open form view"
msgstr "Mở chế độ xem biểu mẫu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Open kanban view of approvals"
msgstr "Mở chế độ xem kanban của các phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Optional"
msgstr "Tuỳ chọn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Order"
msgstr "Đơn hàng"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "PDF file"
msgstr "Tệp PDF"

#. module: web_studio
#: model:ir.model,name:web_studio.model_report_paperformat
msgid "Paper Format Config"
msgstr "Cấu hình định dạng trang giấy"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Paper format"
msgstr "Định dạng trang giấy"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Parent Menu"
msgstr "Menu chính"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Parent View (inherit_id)"
msgstr "Chế độ xem chính (inherit_id)"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Partner"
msgstr "Đối tác"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Phone"
msgstr "Điện thoại"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Picture"
msgstr "Hình ảnh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Pie"
msgstr "Tròn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Pipeline stages"
msgstr "Giai đoạn chu trình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Pipeline status bar"
msgstr "Thanh trạng thái chu trình"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Pivot"
msgstr "Pivot"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Placeholder"
msgstr "Phần giữ chỗ"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Please specify a field."
msgstr "Vui lòng chỉ định một trường."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__post
msgid "Post"
msgstr "Bài viết"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__pre
msgid "Pre"
msgstr "Trước"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Preset"
msgstr "Kiểu định trước"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.xml:0
msgid "Preview is not available"
msgstr "Xem trước không khả dụng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Previous"
msgstr "Trước đó"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Print preview"
msgstr "Xem trước bản in"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Priority"
msgstr "Mức độ ưu tiên"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Private methods cannot be restricted (since they cannot be called remotely, "
"this would be useless)."
msgstr ""
"Không thể hạn chế các phương thức riêng (vì chúng không thể được gọi từ xa, "
"điều này sẽ vô ích)."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Properties"
msgstr "Thuộc tính"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Quarter Hour"
msgstr "Một phần tư giờ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
msgid "Quick Create"
msgstr "Tạo nhanh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Quick Create Field"
msgstr "Tạo trường nhanh"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Effect"
msgstr "Hiệu ứng cầu vồng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Man"
msgstr "Người cầu vồng"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__rating_ids
msgid "Ratings"
msgstr "Đánh giá"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Readonly"
msgstr "Chỉ đọc"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Ready"
msgstr "Sẵn sàng"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Record"
msgstr "Bản ghi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__res_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__res_id
msgid "Record ID"
msgstr "ID bản ghi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__name
msgid "Record Name"
msgstr "Tên bản ghi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__records_count
msgid "Records"
msgstr "Bản ghi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Redo"
msgstr "Làm lại"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__reference
msgid "Reference"
msgstr "Tham chiếu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Reject"
msgstr "Từ chối"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Rejected"
msgstr "Bị từ chối"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Rejected <i class=\"fa fa-thumbs-down text-danger\"/>"
msgstr "Bị từ chối <i class=\"fa fa-thumbs-down text-danger\"/>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Rejected on"
msgstr "Bị từ chối vào"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Related Field"
msgstr "Trường liên quan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reload from attachment"
msgstr "Tải lại từ tệp đính kèm"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
msgid "Remove from View"
msgstr "Xóa khỏi chế độ xem"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Remove rule"
msgstr "Gỡ bỏ quy tắc"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Remove selection"
msgstr "Gỡ bỏ lựa chọn"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_report
msgid "Report Action"
msgstr "Báo cáo tác vụ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report Tools"
msgstr "Công cụ báo cáo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Report edition failed"
msgstr "Chỉnh sửa báo cáo không thành công"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Report name"
msgstr "Tên báo cáo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report preview not available"
msgstr "Xem trước báo cáo không khả dụng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Reporting views"
msgstr "Chế độ xem báo cáo"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Reports"
msgstr "Báo cáo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Required"
msgstr "Bắt buộc"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__res_id
msgid "Res"
msgstr "Res"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Reset Default Background"
msgstr "Đặt lại hình nền mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Reset Image"
msgstr "Đặt lại hình ảnh"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.reset_view_arch_wizard_view
msgid "Reset View"
msgstr "Đặt lại hiển thị"

#. module: web_studio
#: model:ir.model,name:web_studio.model_reset_view_arch_wizard
msgid "Reset View Architecture Wizard"
msgstr "Trình hỗ trợ Đặt lại Kiến trúc Chế độ xem"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reset report"
msgstr "Đặt lại báo cáo"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Responsible"
msgstr "Người phụ trách"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Restore Default View"
msgstr "Khôi phục chế độ xem mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Restrict a record to a specific company"
msgstr "Giới hạn bản ghi cho một công ty cụ thể"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Retention"
msgstr "Tỷ lệ giữ chân"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Revoke"
msgstr "Thu hồi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Ribbon"
msgstr "Ruy-băng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - First level"
msgstr "Nhóm hàng - Mức đầu tiên"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - Second level"
msgstr "Nhóm hàng - Mức thứ hai"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_rule
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__rule_id
msgid "Rule"
msgstr "Quy tắc"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be deleted since it would delete existing"
" approval entries. You should archive the rule instead."
msgstr ""
"Không thể xóa quy tắc liên kết với các mục hiện có vì điều này sẽ xóa các "
"mục phê duyệt hiện có. Thay vào đó, bạn nên lưu trữ quy tắc này."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be modified since it would break existing"
" approval entries. You should archive the rule and create a new one instead."
msgstr ""
"Không thể sửa đổi quy tắc liên kết với các mục hiện có vì điều này sẽ ảnh "
"hưởng tới các mục phê duyệt hiện có. Bạn nên lưu trữ quy tắc này và tạo một "
"quy tắc mới thay thế."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Run a Server Action"
msgstr "Chạy một tác vụ máy chủ"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Lỗi gửi SMS"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Save"
msgstr "Lưu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Save."
msgstr "Lưu."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saved"
msgstr "Đã lưu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saving"
msgstr "Đang lưu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Saving both some report's parts and full xml is not permitted."
msgstr "Không được phép lưu cả một số phần của báo cáo và toàn bộ xml."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Saving the report \""
msgstr "Lưu báo cáo \""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Search"
msgstr "Tìm kiếm"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Search..."
msgstr "Tìm kiếm..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Second Status"
msgstr "Trạng thái thứ hai"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Second dimension"
msgstr "Chiều thứ hai"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "See what changes have been made to this view"
msgstr "Xem những thay đổi nào đã được thực hiện với chế độ xem này"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "Select a Field"
msgstr "Chọn một trường"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select a group..."
msgstr "Chọn một nhóm..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Select a related field"
msgstr "Chọn một trường liên quan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "Select a related field."
msgstr "Chọn một trường liên quan."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Select group"
msgstr "Chọn nhóm"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
msgid ""
"Select the contact field to use to get the coordinates of your records."
msgstr ""
"Chọn trường liên hệ cần sử dụng để có được cách sắp xếp các bản ghi của bạn."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the model in relation to this one"
msgstr "Chọn mô hình liên quan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the reciprocal ManyToOne field"
msgstr "Chọn trường ManyToOne đối ứng"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select users..."
msgstr "Chọn người dùng..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Selection"
msgstr "Lựa chọn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Send a"
msgstr "Gửi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Send messages, log notes and schedule activities"
msgstr "Gửi tin nhắn, lưu ghi chú và lên lịch hoạt động"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Separator"
msgstr "Phân cách"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__sequence
msgid "Sequence"
msgstr "Trình tự"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_server
msgid "Server Action"
msgstr "Tác vụ phía máy chủ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Set As Default"
msgstr "Đặt làm mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.xml:0
msgid "Set Cover Image"
msgstr "Đặt ảnh bìa"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Set a price or cost on records"
msgstr "Thiết lập giá hoặc chi phí trên bản ghi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Set an <b>email alias</b>. Then, try to send an email to this address; it "
"will create a document automatically for you. Pretty cool, huh?"
msgstr ""
"Thiết lập một <b>bí danh email</b>. Khi đó, thử gửi một email đến địa chỉ "
"này; điều đó sẽ tự động tạo một tài liệu cho bạn. Thật tuyệt, đúng không?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Show Invisible Elements"
msgstr "Hiển thị các thành phần ẩn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Show Traceback"
msgstr "Hiển thị truy xuất"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Show by default"
msgstr "Hiển thị theo mặc định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Show in print menu"
msgstr "Hiển thị trong menu in"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Show link to record"
msgstr "Show link to record"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Side panel"
msgstr "Bảng điều khiển bên"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Signature"
msgstr "Chữ ký"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Slow"
msgstr "Chậm"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "Some approvals are missing"
msgstr "Thiếu một số phê duyệt"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Some records were skipped because approvals were missing to"
"                                    proceed with your request: "
msgstr ""
"Một số bản ghi đã bị bỏ qua vì thiếu phê duyệt để tiếp tục yêu cầu của bạn:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Sort By"
msgstr "Sắp xếp theo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Sort by"
msgstr "Sắp xếp theo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Sorting"
msgstr "Sắp xếp"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Sparse"
msgstr "Sparse"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Specify all possible values"
msgstr "Chỉ định tất cả giá trị có thể"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Stacked graph"
msgstr "Biểu đồ cột chồng"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Stage"
msgstr "Giai đoạn"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Stage Name"
msgstr "Tên giai đoạn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Stage and visualize records in a custom pipeline"
msgstr "Sắp xếp và trực quan hóa bản ghi trong một chu trình tùy chỉnh"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Start Date"
msgstr "Ngày bắt đầu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Start Date Field"
msgstr "Trường ngày bắt đầu"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__notification_order
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Step"
msgstr "Bước"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__1
msgid "Step 1"
msgstr "Bước 1"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__2
msgid "Step 2"
msgstr "Bước 2"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__3
msgid "Step 3"
msgstr "Bước 3"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__4
msgid "Step 4"
msgstr "Bước 4"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__5
msgid "Step 5"
msgstr "Bước 5"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__6
msgid "Step 6"
msgstr "Bước 6"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__7
msgid "Step 7"
msgstr "Bước 7"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__8
msgid "Step 8"
msgstr "Bước 8"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__9
msgid "Step 9"
msgstr "Bước 9"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Stop Date Field"
msgstr "Trường ngày kết thúc"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model_data__studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__studio
msgid "Studio"
msgstr "Studio"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_app_creator
msgid "Studio App Creator"
msgstr "Trình tạo ứng dụng Studio"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_entry_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_entry
msgid "Studio Approval Entries"
msgstr "Mục phê duyệt Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_entry
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Studio Approval Entry"
msgstr "Mục phê duyệt Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_request
msgid "Studio Approval Request"
msgstr "Yêu cầu phê duyệt Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Studio Approval Rule"
msgstr "Quy tắc phê duyệt Studio"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_rule_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_rule
msgid "Studio Approval Rules"
msgstr "Quy tắc phê duyệt studio"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_ui_menu__is_studio_configuration
msgid "Studio Configuration Menu"
msgstr "Menu cấu hình Studio"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_customizations_filter
msgid "Studio Customizations"
msgstr "Tùy chỉnh Studio"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Studio Customizations will be exported"
msgstr "Tùy chỉnh studio sẽ được xuất"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.action_models_to_export
#: model:ir.actions.act_window,name:web_studio.action_studio_export_wizard
#: model:ir.ui.menu,name:web_studio.menu_models_to_export
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Studio Export"
msgstr "Xuất Studio"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.studio_export_action
msgid "Studio Export Action"
msgstr "Tác vụ xuất"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard_data
msgid "Studio Export Data"
msgstr "Xuất dữ liệu Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_model
msgid "Studio Export Models"
msgstr "Mô hình xuất Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard
msgid "Studio Export Wizard"
msgstr "Công cụ xuất Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_mixin
msgid "Studio Mixin"
msgstr "Studio Mixin"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Suggested features for your new model"
msgstr "Các tính năng được đề xuất cho mô hình mới của bạn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Sum"
msgstr "Tổng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Sum of %s"
msgstr "Tổng của %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Tabs"
msgstr "Tab"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Tags"
msgstr "Thẻ"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Technical Name"
msgstr "Tên kỹ thuật"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Template '%s' not found"
msgstr "Không tìm thấy mẫu '%s' "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Text"
msgstr "Văn bản"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "The fastest way to create a web application."
msgstr "Cách nhanh nhất để tạo một ứng dụng web."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The field %s does not exist."
msgstr "Trường %s không tồn tại."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "The following fields are currently not in the view."
msgstr "Các trường sau đây hiện không có trong chế độ xem."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon has not a correct format"
msgstr "Biểu tượng không đúng định dạng"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon is not linked to an attachment"
msgstr "Biểu tượng không được liên kết với tệp đính kèm"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The method %(method)s does not exist on the model %(model)s."
msgstr "Phương thức %(method)s không tồn tại trong mô hình %(model)s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.js:0
msgid "The method %s is private."
msgstr "Phương thức %s là phương thức riêng tư."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s does not exist."
msgstr "Mô hình %s không tồn tại."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't exist."
msgstr "Mô hình %s không tồn tại."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't support adding fields."
msgstr "Mô hình %s không hỗ trợ thêm trường."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The operation  type \"%s\" is not supported"
msgstr "Loại hoạt động \"%s\" không được hỗ trợ"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The related field of a button has to be a many2one to %s."
msgstr "Trường liên quan của một nút phải có liên kết many2one với %s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"The report could not be loaded as some error occured. Usually it means that "
"some view inherits from another but targets a node that doesn't exist. It "
"might be due to the mutations of the base views during the upgrade process."
msgstr ""
"Không thể tải báo cáo vì đã xảy ra một số lỗi. Thông thường, điều đó có "
"nghĩa là một số chế độ xem kế thừa từ một chế độ xem khác nhưng nhắm mục "
"tiêu đến một nút không tồn tại. Nguyên nhân có thể là do sự biến đổi của các"
" chế độ xem cơ sở trong quá trình nâng cấp."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "The report is in error. Only editing the XML sources is permitted"
msgstr "Báo cáo có lỗi. Chỉ được phép chỉnh sửa các nguồn XML"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
msgid ""
"The requested change caused an error in the view. It could be because a "
"field was deleted, but still used somewhere else."
msgstr ""
"Thay đổi được yêu cầu đã gây ra lỗi trong chế độ xem. Có thể là do một "
"trường đã bị xóa nhưng vẫn được sử dụng ở một vị trí khác."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message
msgid ""
"The step description will be displayed on the button on which an approval is"
" requested."
msgstr "Mô tả bước sẽ được hiển thị trên nút yêu cầu phê duyệt."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid ""
"The user who approves this step will not be able to approve other steps for "
"the same record."
msgstr ""
"Người dùng phê duyệt bước này sẽ không thể phê duyệt các bước khác cho cùng "
"một bản ghi."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approval_group_id
msgid "The users in this group are able to approve or reject the step."
msgstr "Người dùng trong nhóm này có thể phê duyệt hoặc từ chối bước này."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"There are %s records using selection values not listed in those you are trying to save.\n"
"Are you sure you want to remove the selection values of those records?"
msgstr ""
"Có %s bản ghi sử dụng giá trị lựa chọn không được liệt kê trong những bản ghi bạn đang cố gắng lưu.\n"
"Bạn có chắc chắn muốn xóa các giá trị lựa chọn của những bản ghi đó không?"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"There is no method %(method)s on the model %(model_name)s (%(model_id)s)"
msgstr ""
"Không có phương thức %(method)s trên mô hình %(model_name)s (%(model_id)s)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"There is no record on which this report can be previewed. Create at least "
"one record to preview the report."
msgstr ""
"Không thể xem trước báo cáo này trên bản ghi nào. Tạo ít nhất một bản ghi để"
" xem trước báo cáo."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "There is no record to preview"
msgstr "Không có bản ghi nào để xem trước"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approver_ids
msgid ""
"These users are able to approve or reject the step and will be assigned to "
"an activity when their approval is requested."
msgstr ""
"Những người dùng này có thể phê duyệt hoặc từ chối bước này và sẽ được phân "
"công cho một hoạt động khi được yêu cầu phê duyệt."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__users_to_notify
msgid ""
"These users will receive a notification via internal note when the step is "
"approved or rejected"
msgstr ""
"Những người dùng này sẽ nhận được thông báo qua ghi chú nội bộ khi bước này "
"được phê duyệt hoặc từ chối"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Third Status"
msgstr "Trạng thái thứ ba"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/navbar.js:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "This action is not editable by Studio"
msgstr "Không thể chỉnh sửa tác vụ này bằng Studio"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"This approval or the one you already submitted limits you to a single approval on this action.\n"
"Another user is required to further approve this action."
msgstr ""
"Phê duyệt này hoặc phê duyệt bạn đã gửi giới hạn bạn trong một phê duyệt duy nhất đối với tác vụ này.\n"
"Một người dùng khác được yêu cầu phê duyệt thêm tác vụ này."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"This could also be due to the absence of a real record to render the report "
"with."
msgstr ""
"Điều này cũng có thể là do không có bản ghi thực tế để kết xuất báo cáo."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "This may be due to an incorrect syntax in the edited parts."
msgstr "Điều này có thể là do sai cú pháp trong các phần đã chỉnh sửa."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_export_model_unique_model
msgid "This model is already being exported."
msgstr "Mô hình này hiện đang được xuất."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "This operation caused an error, probably because a xpath was broken"
msgstr "Thao tác này đã gây ra lỗi, có thể do một đường dẫn XPath bị hỏng"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "This rule has already been approved/rejected."
msgstr "Quy tắc này đã được chấp thuận/bị từ chối."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "This rule limits this user to a single approval for this action."
msgstr ""
"Quy tắc này giới hạn người dùng này ở một phê duyệt duy nhất cho tác vụ này."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Timeline"
msgstr "Dòng thời gian"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Timeline views"
msgstr "Chế độ xem dòng thời gian"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "To <b>customize a field</b>, click on its <i>label</i>."
msgstr "Để <b>tùy chỉnh một trường</b>, bấm vào <i>nhãn</i> của trường đó."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
msgid "Toggle Studio"
msgstr "Chuyển đổi Studio"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Total"
msgstr "Tổng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Type"
msgstr "Loại"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Type down your notes here..."
msgstr "Nhập ghi chú của bạn ở đây..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Undo"
msgstr "Hoàn tác"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Unsupported operator '%s' to search action_xmlid"
msgstr "Toán tử '%s' không được hỗ trợ để tìm kiếm action_xmlid"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__date_to
msgid "Until"
msgstr "Cho đến"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Until %s"
msgstr "Cho đến %s"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__updatable
msgid "Updatable"
msgstr "Có thể cập nhật được"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.js:0
msgid ""
"Use Bootstrap or any other custom classes to customize the style and the "
"display of the element."
msgstr ""
"Sử dụng Bootstrap hoặc bất kỳ lớp tùy chỉnh nào khác để tùy chỉnh kiểu và "
"cách hiển thị phần tử."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__user_id
msgid "User"
msgstr "Người dùng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "User assignment"
msgstr "Phân công người dùng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "User avatar placeholder"
msgstr "Phần giữ chỗ ảnh đại diện người dùng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__users_to_notify
msgid "Users to Notify"
msgstr "Người dùng cần thông báo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Uses:"
msgstr "Sử dụng:"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Value"
msgstr "Giá trị"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Value of list"
msgstr "Giá trị của danh sách"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_view
msgid "View"
msgstr "Chế độ xem"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor.js:0
msgid "View Editor"
msgstr "Trình chỉnh sửa chế độ xem"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "View in Error:"
msgstr "Chế độ xem bị lỗi:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Views"
msgstr "Lượt xem"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Waiting for approval"
msgstr "Chờ phê duyệt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Want more fun? Let's create more <b>views</b>."
msgstr "Bạn muốn tăng sự thú vị? Hãy tạo nhiều <b>chế độ xem</b> hơn."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Webhook Automations"
msgstr "Tự động hóa Webhook"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Webhooks"
msgstr "Webhook"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website Messages"
msgstr "Thông báo trên trang web"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website communication history"
msgstr "Lịch sử trao đổi qua trang web"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week"
msgstr "Tuần"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week (expanded)"
msgstr "Tuần (mở rộng)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Week Precision"
msgstr "Độ chính xác theo tuần"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Welcome to"
msgstr "Chào mừng bạn đến"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "What about a <b>Kanban view</b>?"
msgstr "<b>Chế độ xem Kanban</b> thì sao?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "What should the button do"
msgstr "Nút này nên làm gì"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "When Creating Record"
msgstr "Khi tạo bản ghi"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__can_validate
msgid "Whether the rule can be approved by the current user"
msgstr "Người dùng hiện tại có thể phê duyệt quy tắc hay không"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model__abstract
msgid "Whether this model is abstract"
msgstr "Dù mô hình này là trừu tượng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.xml:0
msgid "Which type of report do you want to create?"
msgstr "Bạn muốn tạo loại báo cáo nào?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_widget_properties.xml:0
msgid "Widget"
msgstr "Tiện ích"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Wow, nice! And I'm sure you can make it even better! Use this icon to open "
"<b>Odoo Studio</b> and customize any screen."
msgstr ""
"Wow, tuyệt vời! Chắc chắn rằng bạn có thể làm tốt hơn nữa! Sử dụng biểu "
"tượng này để mở <b>Odoo Studio</b> và tùy chỉnh bất kỳ màn hình nào."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Write additional notes or comments"
msgstr "Viết thêm ghi chú hoặc nhận xét"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "XML"
msgstr "XML"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Year"
msgstr "Năm"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You can not approve this rule."
msgstr "Bạn không thể phê duyệt quy tắc này."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"You cannot cancel an approval you didn't set yourself or you don't belong to"
" an higher level rule's approvers."
msgstr ""
"Bạn không thể hủy bỏ phê duyệt mà bạn không tự thiết lập hoặc bạn không "
"thuộc nhóm người phê duyệt của quy tắc cấp cao hơn."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "You cannot deactivate this view as it is the last one active."
msgstr ""
"Bạn không thể hủy kích hoạt chế độ xem này vì đây là chế độ xem cuối cùng "
"đang hoạt động."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "You first need to create a many2many field in the form view."
msgstr ""
"Trước hết, bạn cần tạo một trường many2many trong chế độ xem biểu mẫu này."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You just like to break things, don't you?"
msgstr "Bạn thích phá vỡ mọi thứ, phải không?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "action"
msgstr "tác vụ"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "delegates to %s."
msgstr "uỷ quyền cho %s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "domain not defined"
msgstr "miền chưa được xác định"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "e.g. Properties"
msgstr "VD: Thuộc tính"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "e.g. Real Estate"
msgstr "VD: Bất động sản"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "for company"
msgstr "dành cho công ty"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "no one"
msgstr "không có ai"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "object"
msgstr "đối tượng"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "or"
msgstr "hay"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "studio_approval attribute can only be set in form views"
msgstr ""
"thuộc tính studio_approval chỉ có thể được thiết lập trong chế độ xem biểu "
"mẫu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "test email"
msgstr "kiểm thử email"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "to %s."
msgstr "đến %s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload it"
msgstr "tải lên"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload one"
msgstr "tải lên một"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "x_studio_"
msgstr "x_studio_"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "{{ item.isInEdition ? 'Add selection' : 'Edit selection' }}"
msgstr "{{ item.isInEdition ? 'Thêm lựa chọn' : 'Chỉnh sửa lựa chọn' }}"
