# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* web_studio
# 
# Translators:
# Abe Manyo, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:54+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Indonesian (https://app.transifex.com/odoo/teams/41243/id/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: id\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    This is your new action.\n"
"                </p>\n"
"                <p>By default, it contains a list and a form view and possibly\n"
"                    other view types depending on the options you chose for your model.\n"
"                </p>\n"
"                <p>\n"
"                    You can start customizing these screens by clicking on the Studio icon on the\n"
"                    top right corner (you can also customize this help message there).\n"
"                </p>\n"
"            "
msgstr ""
"\n"
"                <p class=\"o_view_nocontent_smiling_face\">\n"
"                    Ini adalah tindakan baru Anda.\n"
"                </p>\n"
"                <p>Secara default, tindakan ini memiliki daftar dan tampilan formulir dan mungkin\n"
"                    beberapa jenis tampilan lainnya untuk opsi-opsi yang Anda pilih untuk model Anda.\n"
"                </p>\n"
"                <p>\n"
"                    Anda dapat mulai kustomisasi layar ini dengan emngeklik ikon Studio pada\n"
"                    pojok kanan atas (Anda juga dapat kustomisasi pesan bantuan ini di sana).\n"
"                </p>\n"
"            "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid ""
"\n"
"    There are no many2one fields related to the current model.\n"
"    To create a one2many field on the current model, you must first create its many2one counterpart on the model you want to relate to.\n"
msgstr ""
"\n"
"    Tidak ada field many2one yang terkait model saat ini.\n"
"    Untuk membuat field one2many pada model, Anda harus pertama membuat counteraprt many2one counterpart pada model yang Anda ingin kaitkan.\n"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Add a new report\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_empty_report\">\n"
"                Tambahkan laporan baru\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new filter\n"
"            </p>\n"
"            "
msgstr ""
" <p class=\"o_view_nocontent_smiling_face\">\n"
"                Tambahkan filter baru\n"
"            </p>\n"
"            "

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid " until %s"
msgstr "sampai %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "\" failed."
msgstr "\" gagal."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s delegated approval rights to %(delegate_to)s"
msgstr "%(user_name)s mendelegasikan hak penyetuju ke %(delegate_to)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"%(user_name)s has set approval rights from %(previous_approvers)s to "
"%(next_approvers)s"
msgstr ""
"%(user_name)s memiliki hak penyetuju dari %(previous_approvers)s sampai "
"%(next_approvers)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "%(user_name)s revoked their delegation to %(revoked_users)s"
msgstr "%(user_name)s mencabut delegasi mereka ke %(revoked_users)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/report.py:0
msgid "%s Report"
msgstr "%s Laporan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_export_model.py:0
msgid "%s record(s)"
msgstr "%s record"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "<i role=\"img\" class=\"fa fa-user-times\" title=\"Exclusive approval\"/>"
msgstr "<i role=\"img\" class=\"fa fa-user-times\" title=\"Persetujuan eksklusif\"/>"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Add a new access control list\n"
"            </p>\n"
"            "
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Tambahkan daftar kontrol akses baru\n"
"            </p>\n"
"            "

#. module: web_studio
#: model_terms:web_tour.tour,rainbow_man_message:web_studio.web_studio_new_app_tour
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>Good job!</b> Anda melewati semua langkah di tur ini.</span>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "A field with the same name already exists."
msgstr "Field dengan nama yang sama sudah ada."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_entry_uniq_combination
msgid "A rule can only be approved/rejected once per record."
msgstr "Peraturan hanya dapat disetujui/ditolak sekali untuk setiap record."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_together
msgid "A rule must apply to an action or a method (but not both)."
msgstr ""
"Peraturan harus menerapkan tindakan atau metode (tapi tidak keduanya)."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_approval_rule_method_or_action_not_null
msgid "A rule must apply to an action or a method."
msgstr "Peraturan harus menerapkan tindakan atau metode."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model__abstract
msgid "Abstract"
msgstr "Abstrak"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Access Control"
msgstr "Kontrol Akses"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Access Control Lists"
msgstr "Daftar Kontrol Akses"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_groups
msgid "Access Groups"
msgstr "Kelompok Akses"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Access records from cell"
msgstr "Akses record dari cell"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Access records from graph"
msgstr "Akses record dari grafik"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_mail_activity_type__category
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__action_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_id
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Action"
msgstr "Tindakan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction
msgid "Action Needed"
msgstr "Action Dibutuhkan"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window
msgid "Action Window"
msgstr "Jendela Tindakan"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Jendela Tindakan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Action to approve:"
msgstr "Tindakan untuk disetujui:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Action to run"
msgstr "Action untuk dijalankan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Action's title"
msgstr "Judul tindakan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Action: %s"
msgstr "Action: %s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_actions
msgid "Actions"
msgstr "Action"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Tindakan dapat memicu perilaku spesifik seperti membuka tampilan kalender "
"atau secara otomatis ditandai sebagai selesai saat dokumen diunggah"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
msgid "Activate View"
msgstr "Aktifkan Tampilan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__active
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Active"
msgstr "Aktif"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
#: model:ir.model,name:web_studio.model_mail_activity
msgid "Activity"
msgstr "Aktivitas"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_activity_type
msgid "Activity Type"
msgstr "Tipe Aktivitas"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Activity view unavailable on this model"
msgstr "Tampilan aktivitas tidak tersedia untuk model ini"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Add"
msgstr "Tambah"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/chatter_container.xml:0
msgid "Add Chatter Widget"
msgstr "Tambahkan Widget Obrolan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add Picture"
msgstr "Tambahkan Gambar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Add a Button"
msgstr "Tambahkan Tombol"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Add a button"
msgstr "Tambahkan tombol"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Add a pipeline status bar"
msgstr "Tambahkan status bar pipeline "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add a priority"
msgstr "Tambahkan prioritas"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Add an approval step"
msgstr "Tambahkan langkah penyetuju"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add an avatar"
msgstr "Tambahkan avatar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Add details to your records with an embedded list view"
msgstr "Tambahkan detail ke record Anda dengan tampilan daftar yang embedded"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Add new value"
msgstr "Tambah nilai baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record at the bottom"
msgstr "Tambahkan record di bawah"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Add record on top"
msgstr "Tambahkan record di atas"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_compiler_legacy.js:0
msgid "Add tags"
msgstr "Tambahkan tag"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_export_data
msgid "Additional Export Data"
msgstr "Data Ekspor Tambahan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Additional Fields"
msgstr "Tambahkan Field"

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid "Additional Studio Exports"
msgstr "Ekspor Studio Tambahan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__additional_models
msgid "Additional models to export"
msgstr "Model tambahan untuk diekspor"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_wizard__additional_models
msgid ""
"Additional models you may choose to export in addition to the Studio "
"customizations"
msgstr ""
"Model tambahan yang Anda mungkin dapat pilih untuk diekspor sebagai tambahan"
" ke kustomisasi Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Aggregate"
msgstr "Agregat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "All Day"
msgstr "Sepanjang hari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"All changes done to the report's structure will be discarded and the report "
"will be reset to its factory settings."
msgstr ""
"Semua perubahan yang dilakukan ke struktur laporan akan dibuang dan laporan "
"akan direset ke pengaturan pabrik."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"All set? You are just one click away from <b>generating your first app</b>."
msgstr ""
"Sudah siap? Anda hanya satu klik dari <b>membuat aplikasi pertama Anda</b>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Allow Resequencing"
msgstr "Izinkan Pengurutan ulang"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Allow visibility to groups"
msgstr ""

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "An approval is missing"
msgstr "Kurang penyetuju"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Apply"
msgstr "Terapkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approval"
msgstr "Persetujuan"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approval Entries"
msgstr "Entri Persetujuan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approval_group_id
msgid "Approval Group"
msgstr "Kelompok Persetujuan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approval Order"
msgstr "Pesanan Penyetuju"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__rule_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approval_rule_id
msgid "Approval Rule"
msgstr "Peraturan Persetujuan"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_approver
msgid "Approval Rule Approvers Enriched"
msgstr "Peraturan Penyetuju Pemberi Persetujuan Diperkaya"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule_delegate
msgid "Approval Rule Delegate"
msgstr "Peraturan Penyetuju Delegasi"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
msgid "Approval Rules"
msgstr "Peraturan-Peraturan Persetujuan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approvals"
msgstr "Persetujuan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals %(model_name)s"
msgstr "Approval %(model_name)s"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals can only be done on a method or an action, not both."
msgstr ""
"Persetujuan hanya dapat dilakukan pada metode atau tindak, tidak untuk "
"keduanya."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Approvals missing"
msgstr "Approval hilang"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approve"
msgstr "Setujui"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__approved
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Approved"
msgstr "Disetujui"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Approved <i class=\"fa fa-thumbs-up text-success\"/>"
msgstr "Disetuju <i class=\"fa fa-thumbs-up text-success\"/>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Approved on"
msgstr "Disetujui pada"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__user_id
msgid "Approved/rejected by"
msgstr "Disetujui/Ditolak oleh"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Approver Group"
msgstr "Kelompok Penyetuju"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_log_ids
msgid "Approver Log"
msgstr "Log Penyetuju"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__approver_ids
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__approver_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Approvers"
msgstr "Approver"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archive deprecated records"
msgstr "Arsipkan record yang terdepresiasi"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Archived"
msgstr "Diarsipkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Archiving"
msgstr "Mengarsipkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Are you sure you want to remove the selection values?"
msgstr "Apakah Anda yakin ingin menghapus selection values?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.js:0
msgid "Are you sure you want to remove this %s from the view?"
msgstr "Apakah Anda yakin ingin menghapus %s ini dari tampilan?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Are you sure you want to reset the background image?"
msgstr "Apakah Anda yakin ingin reset gambar latar belakang?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/edition_flow.js:0
msgid ""
"Are you sure you want to restore the default view?\r\n"
"All customization done with studio on this view will be lost."
msgstr ""
"Apakah Anda yakin ingin memulihkan tampilan default?\r\n"
"Semua kustomisasi yang dilakukan di Studio pada tampilan ini akan hilang."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__is_demo_data
msgid "As Demo"
msgstr "Sebagai Demo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Ascending"
msgstr "Urutan Teratas"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign a responsible to each record"
msgstr "Tetapkan yang bertanggung jawab untuk setiap record"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Assign dates and visualize records in a calendar"
msgstr "Tetapkan tanggal dan visualisasikan record pada kalender"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Attach a picture to a record"
msgstr "Lampirkan gambar ke record"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_attachment_count
msgid "Attachment Count"
msgstr "Jumlah Lampiran"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__include_attachment
msgid "Attachments"
msgstr "Lampiran"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Autocompletion Fields"
msgstr "Field lengkapi otomatis"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_automation
msgid "Automation Rule"
msgstr "Peraturan Otomatisasi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Automations"
msgstr "Automation"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Average"
msgstr "Rata-rata"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Average of %s"
msgstr "Rata-Rata %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Awaiting approval"
msgstr "Menunggu persetujuan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Backwards"
msgstr "Terbalik"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Bar"
msgstr "Bar"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base
msgid "Base"
msgstr "Base"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Blank"
msgstr "Kosong"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Blocked"
msgstr "Diblokir"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Bold"
msgstr "Bold"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Business header/footer"
msgstr "Header/Footer Bisnis"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.js:0
msgid "Button"
msgstr "Tombol"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Buttons Properties"
msgstr "Properti Tombol"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/aside_properties/aside_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/div_properties/div_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/footer_properties/footer_properties.xml:0
msgid "CSS style"
msgstr "Style CSS"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Calendar"
msgstr "Kalender"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Call a method"
msgstr "Panggil metode"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Create"
msgstr "Dapat Membuat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Delete"
msgstr "Dapat Menghapus"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Can Edit"
msgstr "Dapatkah Edit"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__can_validate
msgid "Can be approved"
msgstr "Dapat disetujui"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch 'create', 'write' and 'unlink'."
msgstr "Tidak dapat patch 'buat'm 'tulis' dan 'unlink'."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Can't patch private methods."
msgstr "Tidak daapt patch metode pribadi."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Cancel"
msgstr "Batal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Categorize records with custom tags"
msgstr "Kategorikan record dengan tag kustom"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Change Background"
msgstr "Ganti Background"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Change Image"
msgstr "Ganti Gambar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Chatter"
msgstr "Obrolan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "CheckBox"
msgstr "CheckBox"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model_data__studio
msgid "Checked if it has been edited with Studio."
msgstr "Centa bila sudah diedit dengan Studio."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Choose an app name"
msgstr "Pilih nama aplikasi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Choose the name of the menu"
msgstr "Pilih nama menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Churn"
msgstr "Churn"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.xml:0
msgid "Class"
msgstr "Class"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Click to edit messaging features on your model."
msgstr "Klik untuk edit fitur pesan pada model Anda."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Click to see all approval rules."
msgstr "Klik untuk melilhat semua peraturan persetujuan."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/navbar/navbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Close"
msgstr "Tutup"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Cohort"
msgstr "Cohort"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Color"
msgstr "Warna"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Color Picker"
msgstr "Pemilih Warna"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Column"
msgstr "Kolom"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Column grouping"
msgstr "Kelompok Kolom"

#. module: web_studio
#: model:ir.model,name:web_studio.model_res_company
msgid "Companies"
msgstr "Perusahaan"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Company"
msgstr "Perusahaan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_structures.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Components"
msgstr "Komponen"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Conditional"
msgstr "Bersyarat"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__conditional
msgid "Conditional Rule"
msgstr "Syarat Aturan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Configuration"
msgstr "Konfigurasi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Configure Model"
msgstr "Konfigurasikan Model"

#. module: web_studio
#: model_terms:ir.actions.act_window,help:web_studio.action_models_to_export
msgid ""
"Configure additional models to export with Studio, such as records that hold"
" configuration information or demo data."
msgstr ""
"Konfigurasikan model-model tambahan untuk diekspor dengan Studio, seperti "
"record yang memiliki informasi konfigurasi atau data demo."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Configure data and demo data to export"
msgstr "Konfigurasi data dan data demo untuk diekspor"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Configure model"
msgstr "Konfigurasikan model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Confirm"
msgstr "Konfirmasi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Confirmation"
msgstr "Konfirmasi"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Contact"
msgstr "Kontak"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Contact Field"
msgstr "Field Kontak"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid "Contact Field Required"
msgstr "Field Kontak Dibutuhkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Contact details"
msgstr "Detail kontak"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Context"
msgstr "Konteks"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Continue to configure some typical behaviors for your new type of object."
msgstr ""
"Lanjutkan untuk konfigurasi beberapa perilaku standar untuk jenis baru "
"object Anda."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.js:0
msgid "Could not change the background"
msgstr "Tidak dapat mengganti latar belakang"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Create Menu"
msgstr "Buat Menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.js:0
msgid "Create Model"
msgstr "Buat Model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Create a new Model"
msgstr "Buat Model baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Create your <b>selection values</b> (e.g.: Romance, Polar, Fantasy, etc.)"
msgstr ""
"Buat <b>selection values</b> Anda (e.g.: Romance, Polar, Fantasy, etc.)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your App"
msgstr "Buat Aplikasi Anda"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your app"
msgstr "Buat aplikasi Anda"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Create your first menu"
msgstr "Buat menu pertama Anda"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "Create your menu"
msgstr "Buat menu Anda"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_uid
msgid "Created by"
msgstr "Dibuat oleh"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__create_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__create_date
msgid "Created on"
msgstr "Dibuat pada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Creating this type of view is not currently supported in Studio."
msgstr "Membuat jenis tampilan ini saat ini tidak didukung di Studio."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Currency"
msgstr "Mata Uang"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Current model:"
msgstr "Model saat ini:"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_menu.py:0
msgid "Custom Configuration"
msgstr "Konfigurasi Kustom"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_fields
msgid "Custom Fields"
msgstr "Field Kustom"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_models
msgid "Custom Models"
msgstr "Model Kustom"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_reports
msgid "Custom Reports"
msgstr "Laporan Kustom"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Custom Sorting"
msgstr "Sorting Kustom"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__custom_views
msgid "Custom Views"
msgstr "Tampilan Kustom"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Custom field names cannot contain double underscores."
msgstr "Nama field kustom tidak boleh memiliki dua garis bawah."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom fields:"
msgstr "Field kustom:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom models:"
msgstr "Model kustom:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom reports:"
msgstr "Laporan kustom:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Custom views:"
msgstr "Tampilan kustom:"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_base_module_uninstall_studio
msgid "Customization made with Studio will be permanently lost"
msgstr "Kustomisasi yang dibuat di Studio akan hilang selamanya"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "DIFF"
msgstr "DIFF"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Date"
msgstr "Tanggal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date & Calendar"
msgstr "Tanggal & Kalender"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__date_to
msgid "Date To"
msgstr "Sampai Tanggal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Date range & Gantt"
msgstr "Rentang tanggal & Gantt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Datetime"
msgstr "Datetime"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Day"
msgstr "Hari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Day Precision"
msgstr "Tingkat Ketelitian Hari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Decimal"
msgstr "Desimal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Default Display Mode"
msgstr "Mode Tampilan Default"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__default_export_data
msgid "Default Export Data"
msgstr "Data Ekspor Default"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Default Group By"
msgstr "Kelompokkan Berdasarkan Default"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Group by"
msgstr "Kelompokkan Berdasarkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Default Scale"
msgstr "Scale Default"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
#: model:ir.model,name:web_studio.model_ir_default
msgid "Default Values"
msgstr "Nilai Baku"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Default value"
msgstr "Value Default"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Default view"
msgstr "Tampilan Default"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Define start/end dates and visualize records in a Gantt chart"
msgstr ""
"Definisikan tanggal awal/akhir dan visualisasikan record dalam bagan Gantt"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__updatable
msgid "Defines if the records would be updated during a module update."
msgstr "Mendefinisikan bila record akan diupdate selama update module."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__notification_order
msgid "Defines the sequential order in which the approvals are requested."
msgstr "Mendefinisikan sequential order di mana approval akan diminta."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Delay Field"
msgstr "Delay Field"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Delegate"
msgstr "Delegasikan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Delegate to"
msgstr "Delegasikan ke"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Delete"
msgstr "Hapus"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__is_demo_data
msgid "Demo"
msgstr "Demo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Dense"
msgstr "Dense"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Descending"
msgstr "Urutan kebawah"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message
msgid "Description"
msgstr "Deskripsi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "Design your Icon"
msgstr "Desain Ikon Anda"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Disable View"
msgstr "Nonaktifkan Tampilan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Discard"
msgstr "Buang"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Discard changes"
msgstr "Buang perubahan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Mode"
msgstr "Mode Tampilan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__display_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__display_name
msgid "Display Name"
msgstr "Nama Tampilan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Total row"
msgstr "Tampilkan Total baris"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Display Unavailability"
msgstr "Tampilkan yang Tidak Tersedia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Displays a textual hint that helps the user when the field is empty."
msgstr "Tampilkan hint teks yang membantu user saat field kosong."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "Do you want to add a dropdown with colors?"
msgstr "Apakah Anda ingin menambahkan dropdown dengan warna?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__domain
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__domain
msgid "Domain"
msgstr "Ruang Lingkup"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Done"
msgstr "Selesai"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Drag & drop <b>another field</b>. Let's try with a <i>selection field</i>."
msgstr ""
"Tarik & lepas <b>field lain</b>. Mari kita coba dengan <i>field "
"selection</i>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Drag a menu to the right to create a sub-menu"
msgstr "Tarik menu ke kanan untuk membuat sub-menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.js:0
msgid "Dropdown"
msgstr "Dropdown"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "Duplicate"
msgstr "Gandakan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Dynamic Table"
msgstr "Tabel Dinamik"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Edit"
msgstr "Edit"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_content_overlay.js:0
msgid "Edit %s view"
msgstr "Edit %s tampilan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/icon_creator_dialog/icon_creator_dialog.xml:0
msgid "Edit Application Icon"
msgstr "Edit Ikon Aplikasi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/menu_properties/menu_properties.xml:0
msgid "Edit Color Picker"
msgstr "Edit Pemilih Warna"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit Menu"
msgstr "Menu Edit"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Edit Values"
msgstr "Edit Value"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Edit selection"
msgstr "Edit seleksi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Edit sources"
msgstr "Edit sumber"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.js:0
msgid ""
"Editing a built-in file through this editor is not advised, as it will "
"prevent it from being updated during future App upgrades."
msgstr ""
"Mengedit file built-in melalui editor ini tidak disarankan, karena akan "
"membuatnya tidak dapat diupdate saat App di-upgrade di masa depan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Editing item:"
msgstr "Mengedit item:"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Email"
msgstr "Email"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Email Alias"
msgstr "Email Alias"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_template
msgid "Email Templates"
msgstr "Templat Email"

#. module: web_studio
#: model:ir.model,name:web_studio.model_mail_thread
msgid "Email Thread"
msgstr "Thread email"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Empty List Message"
msgstr "Kosongkan Daftar Pesan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Enable Mass Editing"
msgstr "Aktifkan Editing Massal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Enable Routing"
msgstr "Aktifkan Routing"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "End Date"
msgstr "Tanggal Berakhir"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entry_ids
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Entries"
msgstr "Entri-entri"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Error"
msgstr "Error!"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error message:"
msgstr "Pesan error:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Error name:"
msgstr "Nama error:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__exclusive_user
msgid "Exclusive Approval"
msgstr "Approval Eksklusif"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Existing Fields"
msgstr "Field yang Tersedia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Existing Model"
msgstr "Model yang Tersedia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Export"
msgstr "Ekspor"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "External"
msgstr "Eksternal"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__action_xmlid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__xmlid
msgid "External ID"
msgstr "ID Eksternal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Fadeout Speed"
msgstr "Kecepatan Fadeout"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Fast"
msgstr "Cepat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Favourites"
msgstr "Favorit"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Field"
msgstr "Kolom"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Field Properties"
msgstr "Properti Field"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.js:0
msgid "Field properties: %s"
msgstr "Properti-properti field: %s"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_fields
msgid "Fields"
msgstr "Kolom"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__excluded_fields
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
msgid "Fields to exclude"
msgstr "Field untuk dikecualikan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "File"
msgstr "File"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Filename for %s"
msgstr "Nama file untuk %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Filter"
msgstr "Saring"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Filter Rules"
msgstr "Peraturan Filter"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Filter label"
msgstr "Label filter"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: model:ir.model,name:web_studio.model_ir_filters
msgid "Filters"
msgstr "Penyaring"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "First Status"
msgstr "Status Pertama"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "First dimension"
msgstr "Dimensi pertama"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_follower_ids
msgid "Followers"
msgstr "Pengikut-Pengikut"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pengikut (Partner)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Footer"
msgstr "Catatan Kaki"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"For compatibility purpose with base_automation,approvals on 'create', "
"'write' and 'unlink' methods are forbidden."
msgstr ""
"Untuk tujuan kompatibilitas dengan base_automation, metode approval pada "
"'buat', 'tulis' dan 'unlink' dilarang."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/limit_group_visibility/limit_group_visibility.xml:0
msgid "Forbid visibility to groups"
msgstr ""

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_delegate_approvers
msgid "Forever"
msgstr "Selamanya"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Form"
msgstr "Formulir"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Format"
msgstr "Format"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Forward"
msgstr "Forward"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Gantt"
msgstr "Gantt"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "General views"
msgstr "Tampilan umum"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.js:0
msgid "Generate %s View"
msgstr "Generate %s View"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Get contact, phone and email fields on records"
msgstr "Dapatkan field kontak, telepon dan email pada record"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Go back"
msgstr "Kembali"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Go on, you are almost done!"
msgstr "Ayo, Anda nyaris selesai!"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Good job! To add more <b>fields</b>, come back to the <i>Add tab</i>."
msgstr ""
"Kerja bagus! Untuk menambahkan lebih banyak <b>field</b>, kembali ke "
"<i>Tambahkan tab</i>."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: model:ir.model.fields.selection,name:web_studio.selection__mail_activity_type__category__grant_approval
#: model:mail.activity.type,name:web_studio.mail_activity_data_approve
msgid "Grant Approval"
msgstr "Berikan Persetujuan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Graph"
msgstr "Grafik"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Great !"
msgstr "Bagus !"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_renderer.xml:0
msgid "Group"
msgstr "Grup"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Group By"
msgstr "Dikelompokkan berdasarkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
msgid "Group by"
msgstr "Dikelompokkan menurut"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Grouping is not applied while in Studio to allow editing."
msgstr ""
"Pengelompokkan tidak diterapkan selama di Studio untuk mengizinkan editing."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "HTML"
msgstr "HTML"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP routing"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Day"
msgstr "Setengah Hari"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Half Hour"
msgstr "Setengah Jam"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__has_message
msgid "Has Message"
msgstr "Memiliki Pesan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid ""
"Header and footer are shared with other reports which may be impacted by the"
" reset."
msgstr ""
"Header dan footer dibagi dengan laporan lain yang mungkin terdampak oleh "
"reset."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Help Tooltip"
msgstr "Tooltip Help"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Here, you can <b>name</b> your field (e.g. Book reference, ISBN, Internal "
"Note, etc.)."
msgstr ""
"Di sini, Anda dapat <b>memberikan nama</b> ke field Anda (misalnya referensi"
" buku, ISBN, Catatan Internal, dsb.)."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Address"
msgstr "Sembunyikan Alamat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Hide Name"
msgstr "Sembunyikan Nama"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Hide by default"
msgstr "Secara default sembunyikan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "High Priority"
msgstr "Prioritas Tinggi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Highlight Color Field"
msgstr "Field Warna Highlight"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_res_company__background_image
msgid "Home Menu Background Image"
msgstr "Gambar Latar Belakang Beranda"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Hour"
msgstr "Jam"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to <b>name</b> your app? Library, Academy, …?"
msgstr ""
"Apa nama yang Anda ingin <b>berikan</b> untuk aplikasi Anda? Perpustakaan, "
"Akademi, ...?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "How do you want to name your first <b>menu</b>? My books, My courses?"
msgstr "Apa nama untuk <b>menu</b> pertama Anda? Buku saya, kursus saya?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"I bet you can <b>build an app</b> in 5 minutes. Ready for the challenge?"
msgstr ""
"Saya yakin Anda <b>bisa membuat aplikasi</b> dalam 5 menit. Siap untuk "
"mencoba?"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__id
msgid "ID"
msgstr "ID"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "IDs"
msgstr "ID"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Icon"
msgstr "Ikon"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Bila dicentang, pesan baru memerlukan perhatian Anda."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Bila dicentang, beberapa pesan mungkin memiliki kesalahan pengiriman."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__include_attachment
msgid ""
"If set, the attachments related to the exported records will be included in "
"the export."
msgstr ""
"Bila ditetapkan, lampiran terkait ke record yang diekspor akan dimasukkan di"
" ekspor."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_export_model__is_demo_data
msgid ""
"If set, the exported records will be considered as demo data during the "
"import."
msgstr ""
"Bila ditetapkan, record yang diekspor akan dianggap sebagai data demo selama"
" impor."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__domain
msgid "If set, the rule will only apply on records that match the domain."
msgstr ""
"Jika ditetapkan, peraturan akan menerapkan record yang cocok dengan domain."

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__exclusive_user
msgid ""
"If set, the user who approves this rule will not be able to approve other "
"rules for the same record"
msgstr ""
"Jika ditetapkan, user yang setuju dengan peraturan ini tidak dapat "
"menyetujui peraturan lain untuk record yang sama"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"If you discard the current edits, all unsaved changes will be lost. You can "
"cancel to return to edit mode."
msgstr ""
"Jika Anda membuang edit saat ini, semua perubahan yang belum disimpan akan "
"hilang. Anda dapat membatalkan ini untuk kembali ke mode edit."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"If you don't want to create a new model, an existing model should be "
"selected."
msgstr ""
"Jika Anda tidak dapat membuat model baru, model yang tersedia saat ini "
"seharusnya akan dipilih."

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Image"
msgstr "Gambar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Import"
msgstr "Impor"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "In Progress"
msgstr "Dalam Proses"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_additional_data
msgid "Include Data"
msgstr "Termasuk Data"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__include_demo_data
msgid "Include Demo Data"
msgstr "Termasuk Data Demo"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Include header and footer"
msgstr "Termasuk header dan footer"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_ui_menu__is_studio_configuration
msgid ""
"Indicates that this menu was created by Studio to hold configuration sub-"
"menus"
msgstr ""
"Indikasikan bahwa menu ini dibuat oleh Studio untuk menahan konfigurasi sub-"
"menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field"
msgstr "Masukkan field"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a field..."
msgstr "Masukkan field..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Insert a table based on a relational field."
msgstr "Masukkan tabel berdasarkan field relational."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert left"
msgstr "Masukkan di kiri"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/qweb_table_plugin.js:0
msgid "Insert right"
msgstr "Masukkan di kanan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Integer"
msgstr "Bilangan Bulat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Internal"
msgstr "Internal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Interval"
msgstr "Interval"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Invalid studio_approval %s in button"
msgstr "studio_approval %s invalid di tombol"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Invisible"
msgstr "Sembunyikan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__is_delegation
msgid "Is Delegation"
msgstr "Apakah Delegasi"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_is_follower
msgid "Is Follower"
msgstr "Apakah Pengikut"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_base_module_uninstall__is_studio
msgid "Is Studio"
msgstr "Is Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Is default view"
msgstr "Merupakan tampilan default"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "It lacks a method to check."
msgstr "Kurang metode untuk diperiksa."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Kanban"
msgstr "Kanban"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__kanban_color
msgid "Kanban Color"
msgstr "Warna Kanban"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Kanban State"
msgstr "Status Kanban"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/group_properties/group_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/page_properties/page_properties.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Label"
msgstr "Label"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Label of the button"
msgstr "Label tombo"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_uid
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_uid
msgid "Last Updated by"
msgstr "Terakhir Diperbarui oleh"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard__write_date
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__write_date
msgid "Last Updated on"
msgstr "Terakhir Diperbarui pada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "Leave DIFF"
msgstr "Tinggalkan DIFF"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_leave_with
msgid "Leave Studio with another action"
msgstr "Tinggalkan Studio dengan action lain"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Let's check the result. Close Odoo Studio to get an <b>overview of your "
"app</b>."
msgstr ""
"Mari kita periksa hasilnya. Tutup Odoo Studio untuk mendapatkan <b>gambaran "
"umum aplikasi Anda</b>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Limit visibility to groups"
msgstr "Batasi visibilitas ke kelompok"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Line"
msgstr "Baris"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Lines"
msgstr "Baris"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__mail_activity_id
msgid "Linked Activity"
msgstr "Linked Activity"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "List"
msgstr "Daftar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Manually sort records in the list view"
msgstr "Secara manual sortir record dalam tampilan daftar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2Many"
msgstr "Many2Many"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Many2One"
msgstr "Many2One"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Map"
msgstr "Peta"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.js:0
msgid ""
"Map views are based on the address of a linked Contact. You need to have a "
"Many2one field linked to the res.partner model in order to create a map "
"view."
msgstr ""
"Tampilan peta didasarkan pada alamat dari Kontak terhubung. Anda harus "
"memiliki banyak field Man2one yang terhubung ke model res.partner untuk "
"dapat membuat tampilan peta."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Measure"
msgstr "Ukur"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Measure Field"
msgstr "Field Ukur"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Measures"
msgstr "Pengukuran"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Medium"
msgstr "Media"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Message"
msgstr "Pesan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error
msgid "Message Delivery error"
msgstr "Error Pengiriman Pesan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_ids
msgid "Messages"
msgstr "Pesan-Pesan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/properties/kanban_button_properties/kanban_button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__method
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__method
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Method"
msgstr "Metode"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Method to run"
msgstr "Metode untuk dijalankan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Method: %s"
msgstr "Metode: %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "Minimal header/footer"
msgstr "Header/footer minimal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Mode"
msgstr "Mode"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_id
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_search_view
msgid "Model"
msgstr "Model"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/wizard/studio_export_wizard.py:0
msgid "Model '%s' should not contain records with the same ID."
msgstr "Model '%s' harusnya tidak memiliki record dengan ID yang sama."

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_access
msgid "Model Access"
msgstr "Akses Model"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model_data
msgid "Model Data"
msgstr "Model Data"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__model_name
msgid "Model Description"
msgstr "Keterangan Model"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__model
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__model_name
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__model_name
msgid "Model Name"
msgstr "Nama Model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
msgid "Model name"
msgstr "Contoh"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_model
msgid "Models"
msgstr "Model"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Modified by:"
msgstr "Dimodifikasi oleh:"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_module_module
msgid "Module"
msgstr "Modul"

#. module: web_studio
#: model:ir.model,name:web_studio.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Module Uninstal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Monetary"
msgstr "Kebijakan moneter"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Monetary value"
msgstr "Nilai moneter"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month"
msgstr "Bulan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Month (expanded)"
msgstr "Bulan (diperluas)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Month Precision"
msgstr "Tingkat Ketelitian Bulan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "More"
msgstr "Selengkapnya"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Multine Text"
msgstr "Teks Multine"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Multiple records views"
msgstr "Lebih dari satu record view"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "My %s"
msgstr "%s Saya"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "My Button"
msgstr "Tombol Saya"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "My entries"
msgstr "Entri Saya"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "N/A"
msgstr "N/A"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__name
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__name
msgid "Name"
msgstr "Nama"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New"
msgstr "Baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "New %s"
msgstr "Baru %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
msgid "New App"
msgstr "App Baru"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "New Approval Entry"
msgstr "Entri Persetujuan Baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "New Field"
msgstr "Field Baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "New Fields"
msgstr "Field-Field Baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "New Filter"
msgstr "Fitler Baru"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "New Lines"
msgstr "Line Baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "New Menu"
msgstr "Menu Baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_model_item/new_model_item.xml:0
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "New Model"
msgstr "Model Baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "New Page"
msgstr "Halaman Baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "New button"
msgstr "Tombol baru"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Next"
msgstr "Next"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Nicely done! Let's build your screen now; <b>drag</b> a <i>text field</i> "
"and <b>drop</b> it in your view, on the right."
msgstr ""
"Kejra bagus! Mari buat layar Anda sekarang; <b>tarik</b><i>field teks</i> "
"dan <b>lepas</b> di tampilan Anda, di sebelah kanan."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "No aggregation"
msgstr "Tidak ada aggregation"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "No approval found for this rule, record and user combination."
msgstr ""
"Tidak ada persetujuan yang ditemukan untuk kombinasi peraturan, record dan "
"user ini."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.js:0
msgid "No header/footer"
msgstr "Tidak ada header/footer"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor.js:0
msgid "No related many2one fields found"
msgstr "Tidak ada field many2one terkait yang ditemukan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "None"
msgstr "Tidak Ada"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Notes"
msgstr "Catatan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__users_to_notify
msgid "Notify to"
msgstr "Notifikasi "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now you're on your own. Enjoy your <b>super power</b>."
msgstr ""
"Sekarang Anda dapat melakukannya sendiri. Nikmati <b>super power</b> Anda."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Now, customize your icon. Make it yours."
msgstr "Sekarang, kustomisasi ikon Anda. Sesuai keinginan Anda."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of Actions"
msgstr "Jumlah Action"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__entries_count
msgid "Number of Entries"
msgstr "Jumlah Entri"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of errors"
msgstr "Jumlah error"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Jumlah pesan yang membutuhkan action"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Jumlah pesan dengan error pengiriman"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Odoo Studio"
msgstr "Odoo Studio"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "On view (ir.ui.view):"
msgstr "Pada tampilan (ir.ui.view):"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "One2Many"
msgstr "One2Many"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"Only groups with an external ID can be used here. Please choose another "
"group or assign manually an external ID to this group."
msgstr ""
"Hanya kelompok-kelompok dengan ID eksternal dapat digunakan di sini. Silakan"
" memilih kelompok lain atau secara manual berikan ID eksternal ke grup ini."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
msgid "Open Record On Click"
msgstr "Buka Catatan Pada Klik"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Open form view"
msgstr "Buka tampilan formulir"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Open kanban view of approvals"
msgstr "Buka tampilan kanban approval"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Optional"
msgstr "Opsional"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Order"
msgstr "Order"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.act_report_xml_view_kanban
msgid "PDF file"
msgstr "File PDF"

#. module: web_studio
#: model:ir.model,name:web_studio.model_report_paperformat
msgid "Paper Format Config"
msgstr "Konfigurasi Format Kertas"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Paper format"
msgstr "Format kertas"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.js:0
msgid "Parent Menu"
msgstr "Menu Induk"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Parent View (inherit_id)"
msgstr "Parent View (inherit_id)"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Partner"
msgstr "Rekanan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Phone"
msgstr "Telepon"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Picture"
msgstr "Gambar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor.js:0
msgid "Pie"
msgstr "Pie"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Pipeline stages"
msgstr "Tahapan pipeline"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Pipeline status bar"
msgstr "Status bar pipeline"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Pivot"
msgstr "Poros"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Placeholder"
msgstr "Placeholder"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Please specify a field."
msgstr "Mohon tetapkan field."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__post
msgid "Post"
msgstr "Post"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__pre
msgid "Pre"
msgstr "Pra"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Preset"
msgstr "Preset"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_record.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.xml:0
msgid "Preview is not available"
msgstr "Pratinjau tidak tersedia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Previous"
msgstr "Sebelum"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Print preview"
msgstr "Cetak pratinjau"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Priority"
msgstr "Prioritas"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Private methods cannot be restricted (since they cannot be called remotely, "
"this would be useless)."
msgstr ""
"Metode pribadi tidak dapat dibatasi (karena mereka tidak dapat dibuat secara"
" remote, batasan ini akan tidak berguna)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
msgid "Properties"
msgstr "Properti"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Quarter Hour"
msgstr "Seperempat Jam"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
msgid "Quick Create"
msgstr "Buat Cepat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
msgid "Quick Create Field"
msgstr "Field Buat Cepat"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Effect"
msgstr "Efek Pelangi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Rainbow Man"
msgstr "Rainbow Man"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__rating_ids
msgid "Ratings"
msgstr "Rating"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Readonly"
msgstr "Readonly"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Ready"
msgstr "Siap"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Record"
msgstr "Catatan"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__res_id
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_request__res_id
msgid "Record ID"
msgstr "Record ID"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__name
msgid "Record Name"
msgstr "Nama Record"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__records_count
msgid "Records"
msgstr "Record-Record"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Redo"
msgstr "Redo"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_entry__reference
msgid "Reference"
msgstr "Referensi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Reject"
msgstr "Tolak"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_search_view
msgid "Rejected"
msgstr "Ditolak"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.notify_approval
msgid "Rejected <i class=\"fa fa-thumbs-down text-danger\"/>"
msgstr "Ditolak <i class=\"fa fa-thumbs-down text-danger\"/>"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Rejected on"
msgstr "Ditolak pada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Related Field"
msgstr "Field Terkait"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reload from attachment"
msgstr "Muat ulang dari lampiran"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/sidebar_properties_toolbox/sidebar_properties_toolbox.xml:0
msgid "Remove from View"
msgstr "Hapus dari Tampilan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid "Remove rule"
msgstr "Hapus peraturan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Remove selection"
msgstr "Hapus seleksi"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_report
msgid "Report Action"
msgstr "Report Action"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report Tools"
msgstr "Tool Laporan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Report edition failed"
msgstr "Mengedit laporan gagal"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Report name"
msgstr "Nama laporan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid "Report preview not available"
msgstr "Pratinjau laporan tidak tersedia"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Reporting views"
msgstr "Melaporkan tampilan"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Reports"
msgstr "Laporan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/modifiers/modifiers_properties.xml:0
msgid "Required"
msgstr "Wajib"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__res_id
msgid "Res"
msgstr "Res"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/home_menu_customizer/home_menu_customizer.xml:0
msgid "Reset Default Background"
msgstr "Reset Latar Belakang Default"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.xml:0
msgid "Reset Image"
msgstr "Reset Gambar"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.reset_view_arch_wizard_view
msgid "Reset View"
msgstr "Reset Tampilan"

#. module: web_studio
#: model:ir.model,name:web_studio.model_reset_view_arch_wizard
msgid "Reset View Architecture Wizard"
msgstr "Wizard Reset Tampilan Arsitektur"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Reset report"
msgstr "Reset laporan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Responsible"
msgstr "Penanggung Jawab"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Restore Default View"
msgstr "Pulihkan Tampilan Default"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Restrict a record to a specific company"
msgstr "Batasi record ke perusahaan tertentu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
msgid "Retention"
msgstr "Retention"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "Revoke"
msgstr "Cabut"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Ribbon"
msgstr "Pita"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - First level"
msgstr "Pengelompokkan baris - Tingkat pertama"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/pivot/pivot_editor_sidebar.xml:0
msgid "Row grouping - Second level"
msgstr "Pengelompokkan baris - Tingkat kedua"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_rule
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__rule_id
msgid "Rule"
msgstr "Peraturan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be deleted since it would delete existing"
" approval entries. You should archive the rule instead."
msgstr ""
"Peraturan dengan entri yang sudah ada tidak dapat dihapus karena akan juga "
"menghapus entri persetujuan. Anda harus mengarsipkan peraturan tersebut."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Rules with existing entries cannot be modified since it would break existing"
" approval entries. You should archive the rule and create a new one instead."
msgstr ""
"Peraturan dengan entri yang sudah ada tidak dapat dimodifikasi karena ini "
"akan juga merusak entri persetujuan yang sudah ada. Anda harus mengarsipkan "
"peraturan tersebut dan membuat peraturan baru."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "Run a Server Action"
msgstr "Jalankan Server Action"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error Pengiriman SMS"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_snackbar.xml:0
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Save"
msgstr "Simpan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Save."
msgstr "Simpan."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saved"
msgstr "Disimpan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Saving"
msgstr "Menyimpan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "Saving both some report's parts and full xml is not permitted."
msgstr "Menyimpan beberapa bagian laporan dan xml penuh tidak diizinkan."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Saving the report \""
msgstr "Menyimpan laporan \""

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "Search"
msgstr "Pencarian"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "Search..."
msgstr "Cari ..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Second Status"
msgstr "Status Kedua"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Second dimension"
msgstr "Dimensi kedua"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_xml/report_editor_xml.xml:0
msgid "See what changes have been made to this view"
msgstr "Lihat perubahan apa yang dilakukan ke tampilan ini"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/field_selector_dialog.xml:0
msgid "Select a Field"
msgstr "Pilih Field"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select a group..."
msgstr "Pilih kelompok..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.xml:0
msgid "Select a related field"
msgstr "Pilih field yang terkait"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/new_button_box_dialog.js:0
msgid "Select a related field."
msgstr "Pilih field yang terkait."

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Select group"
msgstr "Pilih kelompok"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/map_new_view_dialog.xml:0
msgid ""
"Select the contact field to use to get the coordinates of your records."
msgstr ""
"Pilih field kontak untuk digunakan untuk mendapatkan koordinat records Anda."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the model in relation to this one"
msgstr "Pilih model yang terhubung dengan yang ini"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/field_configuration.xml:0
msgid "Select the reciprocal ManyToOne field"
msgstr "Pilih reciprocal ManyToOne field"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view_quick_create
msgid "Select users..."
msgstr "Pilih user..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Selection"
msgstr "Seleksi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "Send a"
msgstr "Kirimkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Send messages, log notes and schedule activities"
msgstr "Kirimkan pesan, catatan log dan jadwalkan aktivitas"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/search/search_editor.js:0
msgid "Separator"
msgstr "Pembatas"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__sequence
msgid "Sequence"
msgstr "Urutan"

#. module: web_studio
#: model:ir.model,name:web_studio.model_ir_actions_server
msgid "Server Action"
msgstr "Tindakan Server"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.xml:0
msgid "Set As Default"
msgstr "Tetapkan Sebagai Default"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/properties/kanban_cover_properties/kanban_cover_properties.xml:0
msgid "Set Cover Image"
msgstr "Foto Cover"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Set a price or cost on records"
msgstr "Tetapkan harga atau biaya pada record"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Set an <b>email alias</b>. Then, try to send an email to this address; it "
"will create a document automatically for you. Pretty cool, huh?"
msgstr ""
"Tetapkan <b>alias email</b>. Lalu, coba kirimkan email ke alamt ini; ini "
"akan membuat secara otomatis dokumen untuk Anda. Keren, kan?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "Show Invisible Elements"
msgstr "Tetapkan Element yang Tidak Terlihat"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "Show Traceback"
msgstr "Tunjukkan Traceback"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.js:0
msgid "Show by default"
msgstr "Secara default tunjukkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "Show in print menu"
msgstr "Tunjukkan di menu cetak"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Show link to record"
msgstr "Tunjukkan link ke record"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.js:0
msgid "Side panel"
msgstr "Panel samping"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Signature"
msgstr "Tanda Tangan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/rainbow_effect.js:0
msgid "Slow"
msgstr "Lambat"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
#: code:addons/web_studio/static/src/approval/approval_hook.js:0
msgid "Some approvals are missing"
msgstr "Kurang beberapa approval"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"Some records were skipped because approvals were missing to"
"                                    proceed with your request: "
msgstr ""
"Beberapa record dilewati karena approval tidak ada untuk melanjutkan dengan "
"permintaan Anda:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban/kanban_editor_sidebar/kanban_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_sidebar_legacy/kanban_editor_sidebar_legacy.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "Sort By"
msgstr "Sortir Berdasarkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/map/map_editor_sidebar.xml:0
msgid "Sort by"
msgstr "Susun menurut"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Sorting"
msgstr "Penyortiran"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Sparse"
msgstr "Sparse"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Specify all possible values"
msgstr "Tentukan semua possible value"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Stacked graph"
msgstr "Grafik bertumpuk"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Stage"
msgstr "Tahapan"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
msgid "Stage Name"
msgstr "Nama Tahap"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Stage and visualize records in a custom pipeline"
msgstr "Stage dan visualisasikan record pada pipeline kustom"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Start Date"
msgstr "Tanggal Mulai"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Start Date Field"
msgstr "Field Tanggal Mulai"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__notification_order
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_button_configuration_search_view
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_kanban_view
msgid "Step"
msgstr "Langkah"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__1
msgid "Step 1"
msgstr "Langkah 1"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__2
msgid "Step 2"
msgstr "Langkah 2"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__3
msgid "Step 3"
msgstr "Langkah 3"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__4
msgid "Step 4"
msgstr "Langkah 4"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__5
msgid "Step 5"
msgstr "Langkah 5"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__6
msgid "Step 6"
msgstr "Langkah 6"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__7
msgid "Step 7"
msgstr "Langkah 7"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__8
msgid "Step 8"
msgstr "Langkah 8"

#. module: web_studio
#: model:ir.model.fields.selection,name:web_studio.selection__studio_approval_rule__notification_order__9
msgid "Step 9"
msgstr "Langkah 9"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/new_view_dialogs/new_view_dialog.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/calendar/calendar_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Stop Date Field"
msgstr "Field Tanggal Berakhir"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_model_data__studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_wizard_data__studio
msgid "Studio"
msgstr "Studio"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.action_web_studio_app_creator
msgid "Studio App Creator"
msgstr "Studio App Creator"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_entry_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_entry
msgid "Studio Approval Entries"
msgstr "Entri Persetujuan Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_entry
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_entry_form_view
msgid "Studio Approval Entry"
msgstr "Entry Persetujuan Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_request
msgid "Studio Approval Request"
msgstr "Permintaan Persetujuan Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_approval_rule
#: model_terms:ir.ui.view,arch_db:web_studio.studio_approval_rule_form_view
msgid "Studio Approval Rule"
msgstr "Peraturan Persetujuan Studio"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.studio_approval_rule_action
#: model:ir.ui.menu,name:web_studio.menu_studio_approval_rule
msgid "Studio Approval Rules"
msgstr "Peraturan Approval Studio"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_ir_ui_menu__is_studio_configuration
msgid "Studio Configuration Menu"
msgstr "Menu Konfigurasi Studio"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.studio_customizations_filter
msgid "Studio Customizations"
msgstr "Kustomisasi Studio"

#. module: web_studio
#: model_terms:ir.ui.view,arch_db:web_studio.view_studio_export_wizard
msgid "Studio Customizations will be exported"
msgstr "Kustomisasi Studio akan diekspor"

#. module: web_studio
#: model:ir.actions.act_window,name:web_studio.action_models_to_export
#: model:ir.actions.act_window,name:web_studio.action_studio_export_wizard
#: model:ir.ui.menu,name:web_studio.menu_models_to_export
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_form_view
#: model_terms:ir.ui.view,arch_db:web_studio.models_to_export_list
msgid "Studio Export"
msgstr "Ekspor Studio"

#. module: web_studio
#: model:ir.actions.client,name:web_studio.studio_export_action
msgid "Studio Export Action"
msgstr "Action Ekspor Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard_data
msgid "Studio Export Data"
msgstr "Data Ekspor Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_model
msgid "Studio Export Models"
msgstr "Model-Model Ekspor Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_export_wizard
msgid "Studio Export Wizard"
msgstr "Wizard Ekspor Studio"

#. module: web_studio
#: model:ir.model,name:web_studio.model_studio_mixin
msgid "Studio Mixin"
msgstr "Studio Mixin"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.xml:0
msgid "Suggested features for your new model"
msgstr "Fitur yang disarankan untuk model baru Anda"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_specific_and_computed_properties.js:0
msgid "Sum"
msgstr "Jumlah"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.js:0
msgid "Sum of %s"
msgstr "Jumlah dari %s"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/form_editor_sidebar.js:0
msgid "Tabs"
msgstr "Tab"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Tags"
msgstr "Label"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "Technical Name"
msgstr "Nama Teknis"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Template '%s' not found"
msgstr "Templat '%s' tidak ditemukan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.js:0
msgid "Text"
msgstr "Teks"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "The fastest way to create a web application."
msgstr "Cara tercepat untuk membuat aplikasi web."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The field %s does not exist."
msgstr "Field %s tidak ada."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/components/view_fields.xml:0
msgid "The following fields are currently not in the view."
msgstr "FIeld berikut saat ini tidak ada di tampilan."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon has not a correct format"
msgstr "Ikon tidak dalam format yang tepat"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The icon is not linked to an attachment"
msgstr "Ikon tidak ter-link ke lampiran"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The method %(method)s does not exist on the model %(model)s."
msgstr "Metode %(method)s tidak tersedia di model %(model)s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.js:0
msgid "The method %s is private."
msgstr "Metode %s bersifat pribadi."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s does not exist."
msgstr "Model %s tidak ada."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't exist."
msgstr "Model %s tidak tersedia."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The model %s doesn't support adding fields."
msgstr "Model %s tidak mendukung menambahkan field."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The operation  type \"%s\" is not supported"
msgstr "Operation jenis \"%s\" tidak didukung"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "The related field of a button has to be a many2one to %s."
msgstr "Field yang terkait tombol harus many2one ke %s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"The report could not be loaded as some error occured. Usually it means that "
"some view inherits from another but targets a node that doesn't exist. It "
"might be due to the mutations of the base views during the upgrade process."
msgstr ""
"Laporan tidak dapat dimuat karena ada beberapa error. Biasanya ini berarti "
"beberapa tampilan mewarisi dari tampilan lain tapi menarget node yang tidak "
"tersedia. Ini mungkin terjadi oleh karena mutasi tampilan dasar pada proses "
"upgrade."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_model.js:0
msgid "The report is in error. Only editing the XML sources is permitted"
msgstr "Laporan error. Hanya mengedit sumber XML yang diizinkan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/studio_view.js:0
msgid ""
"The requested change caused an error in the view. It could be because a "
"field was deleted, but still used somewhere else."
msgstr ""
"Perubahan yang diminta menghasilkan error di tampilan. Ini bisa terjadi "
"karena field dihapus, tapi masih digunakan di tempat lain."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__message
msgid ""
"The step description will be displayed on the button on which an approval is"
" requested."
msgstr ""
"Keterangan langkah akan ditampilkan pada tombol pada mana approval diminta."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
msgid ""
"The user who approves this step will not be able to approve other steps for "
"the same record."
msgstr ""
"User yang menyetujui langkah ini tidak dapat menyetujui langkah-langkah "
"lainnya untuk record yang sama."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approval_group_id
msgid "The users in this group are able to approve or reject the step."
msgstr "User-user di kelompok ini dapat menyetujui atau menolak langkah."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid ""
"There are %s records using selection values not listed in those you are trying to save.\n"
"Are you sure you want to remove the selection values of those records?"
msgstr ""
"Terdapat %s record yang menggunakan selection values yang tidak didaftarkan di record yang Anda ingin simpan.\n"
"Apakah Anda yakin ingin menghapus selection values record-record tersebut?"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"There is no method %(method)s on the model %(model_name)s (%(model_id)s)"
msgstr "Tidak ada metode %(method)s pada model %(model_name)s (%(model_id)s)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.js:0
msgid ""
"There is no record on which this report can be previewed. Create at least "
"one record to preview the report."
msgstr ""
"Tidak ada record pada mana laporan ini dapat dipratinjau. Buat setidaknya "
"satu record untuk pratinjau laporan."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
msgid "There is no record to preview"
msgstr "Tidak ada record untuk dipratinjau"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__approver_ids
msgid ""
"These users are able to approve or reject the step and will be assigned to "
"an activity when their approval is requested."
msgstr ""
"User-user ini akan dapat menyetujui atau menolak langkah dan akan ditugaskan"
" ke kegiatan saat persetujuan mereka diminta."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__users_to_notify
msgid ""
"These users will receive a notification via internal note when the step is "
"approved or rejected"
msgstr ""
"User-user ini akan menerima notifikasi melalui catatan internal saat langkah"
" disetujui atau ditolak"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_renderer/form_editor_renderer_components.js:0
msgid "Third Status"
msgstr "Status Ketiga"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/navbar/navbar.js:0
#: code:addons/web_studio/static/src/client_action/studio_home_menu/studio_home_menu.js:0
#: code:addons/web_studio/static/src/studio_service.js:0
msgid "This action is not editable by Studio"
msgstr "Tindakan ini tidak dapat diedit oleh Studio"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"This approval or the one you already submitted limits you to a single approval on this action.\n"
"Another user is required to further approve this action."
msgstr ""
"Persetujuan ini atau yang Anda sudah kirimkan membatasi Anda ke satu persetujuan untuk tindakan ini.\n"
"User lain dibutuhkan untuk menyetujui lebih lanjut tindakan ini."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid ""
"This could also be due to the absence of a real record to render the report "
"with."
msgstr "Ini bisa terjadi karena tidak ada record asli untuk merender laporan."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "This may be due to an incorrect syntax in the edited parts."
msgstr ""
"Ini bisa terjadi karena syntax yang tidak tepat di bagian yang diedit."

#. module: web_studio
#: model:ir.model.constraint,message:web_studio.constraint_studio_export_model_unique_model
msgid "This model is already being exported."
msgstr "Model ini sudah diekspor."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_model.js:0
msgid "This operation caused an error, probably because a xpath was broken"
msgstr "Operation ini menghasilkan error, mungkin karena xpath rusak"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "This rule has already been approved/rejected."
msgstr "Peraturan ini sudah disetujui/ditolak."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/approval_infos.xml:0
msgid "This rule limits this user to a single approval for this action."
msgstr ""
"Peraturan ini membatasi user ini untuk memiliki hanya satu persetujuan untuk"
" tindakan ini."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor_sidebar.xml:0
msgid "Timeline"
msgstr "Timeline"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "Timeline views"
msgstr "Timeline views"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "To <b>customize a field</b>, click on its <i>label</i>."
msgstr "Untuk <b>mengkustomisasi field</b>, klik pada <i>labelnya</i>."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/systray_item/systray_item.xml:0
msgid "Toggle Studio"
msgstr "Toggle Studio"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Total"
msgstr "Total"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/graph/graph_editor_sidebar.xml:0
msgid "Type"
msgstr "Jenis"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "Type down your notes here..."
msgstr "Ketik catatan Anda di sini..."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor_wysiwyg/report_editor_wysiwyg.xml:0
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor_snackbar.xml:0
msgid "Undo"
msgstr "Undo"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Unsupported operator '%s' to search action_xmlid"
msgstr "Operator tidak didukung '%s' untuk mencari action_xmlid"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_delegate__date_to
msgid "Until"
msgstr "Hingga"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "Until %s"
msgstr "Sampai %s"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_export_model__updatable
msgid "Updatable"
msgstr "Dapat Diupdate"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/class_attribute/class_attribute.js:0
msgid ""
"Use Bootstrap or any other custom classes to customize the style and the "
"display of the element."
msgstr ""
"Gunakan Bootstrap atau custom class apa pun yang lain untuk "
"mengustomisasikan style dan tampilan element."

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule_approver__user_id
msgid "User"
msgstr "Pengguna"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "User assignment"
msgstr "Tugas user"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "User avatar placeholder"
msgstr "Placeholder avatar user"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/button_properties/button_properties.xml:0
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__users_to_notify
msgid "Users to Notify"
msgstr "User untuk Dinotifikasi"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/xml_resource_editor/xml_resource_editor.xml:0
msgid "Uses:"
msgstr "Penggunaan:"

#. module: web_studio
#. odoo-javascript
#. odoo-python
#: code:addons/web_studio/models/ir_model.py:0
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "Value"
msgstr "Value"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/app_menu_editor/app_menu_editor.xml:0
msgid "Value of list"
msgstr "Value dari daftar"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/interactive_editor_sidebar.js:0
#: model:ir.model,name:web_studio.model_ir_ui_view
msgid "View"
msgstr "Tampilan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/view_editor.js:0
msgid "View Editor"
msgstr "Lihat Editor"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/report_editor/report_editor.xml:0
msgid "View in Error:"
msgstr "Tampilan di Error:"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Views"
msgstr "Tampilan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/approval/studio_approval.xml:0
msgid "Waiting for approval"
msgstr "Menunggu persetujuan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "Want more fun? Let's create more <b>views</b>."
msgstr "Want more fun? Mari buat lebih banyak <b>tampilan</b>."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/controllers/main.py:0
msgid "Webhook Automations"
msgstr "Otomatisasi Webhook"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/editor/editor_menu/editor_menu.js:0
msgid "Webhooks"
msgstr "Webhook"

#. module: web_studio
#: model:ir.model.fields,field_description:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website Messages"
msgstr "Pesan situs"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__website_message_ids
msgid "Website communication history"
msgstr "Sejarah komunikasi situs"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week"
msgstr "Pekan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Week (expanded)"
msgstr "Minggu (diperluas)"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.xml:0
msgid "Week Precision"
msgstr "Tingkat Ketelitian Minggu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "Welcome to"
msgstr "Selamat datang di"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid "What about a <b>Kanban view</b>?"
msgstr "Bagaimana dengan <b>tampilan Kanban</b>?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "What should the button do"
msgstr "Apa yang tombol akan lakukan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/list/list_editor_sidebar/list_editor_sidebar.xml:0
msgid "When Creating Record"
msgstr "Saat Membuat Record"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_studio_approval_rule__can_validate
msgid "Whether the rule can be approved by the current user"
msgstr "Apakah peraturan dapat disetujui oleh user saat ini"

#. module: web_studio
#: model:ir.model.fields,help:web_studio.field_ir_model__abstract
msgid "Whether this model is abstract"
msgstr "Apakah model ini abstrak"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/views/kanban_report/new_report_dialog.xml:0
msgid "Which type of report do you want to create?"
msgstr "Jenis laporan apa yang ingin Anda buat?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/type_widget_properties/type_widget_properties.xml:0
msgid "Widget"
msgstr "Widget"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/js/tours/web_studio_tour.js:0
msgid ""
"Wow, nice! And I'm sure you can make it even better! Use this icon to open "
"<b>Odoo Studio</b> and customize any screen."
msgstr ""
"Wow, nice! Dan saya akin Anda dapat membuatnya lebih baik lagi! Gunakan ikon"
" ini untuk membuka <b>Odoo Studio</b> dan kustomisasi layar apa pun. "

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/model_configurator/model_configurator.js:0
msgid "Write additional notes or comments"
msgstr "Tuliskan catatan atau komentar tambahan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/sidebar_view_toolbox/sidebar_view_toolbox.xml:0
msgid "XML"
msgstr "XML"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/cohort/cohort_editor.js:0
#: code:addons/web_studio/static/src/client_action/view_editor/editors/gantt/gantt_editor_sidebar.js:0
msgid "Year"
msgstr "Tahun"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You can not approve this rule."
msgstr "Anda tidak dapat menyetujui peraturan ini."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid ""
"You cannot cancel an approval you didn't set yourself or you don't belong to"
" an higher level rule's approvers."
msgstr ""
"Anda tidak dapat membatalkan persetujuan yang Anda tidak tetapkan sendiri "
"atau Anda tidak dalam tingkat peraturan penyetuju yang lebih tinggi."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/action_editor/action_editor.js:0
msgid "You cannot deactivate this view as it is the last one active."
msgstr ""
"Anda tidak dapat menonaktifkan tampilan ini karena ini adalah tampilan "
"terakhit yang aktif."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/kanban_legacy/kanban_editor_record_legacy.js:0
msgid "You first need to create a many2many field in the form view."
msgstr ""
"Anda harus terlebih dahulu membuat field many2many di tampilan formulir."

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "You just like to break things, don't you?"
msgstr "Anda suka merusak segala sesuatu, ya?"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "action"
msgstr "action"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "delegates to %s."
msgstr "didelegasi ke %s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "domain not defined"
msgstr "domain tidak dedifinisikan"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/menu_creator/menu_creator.xml:0
msgid "e.g. Properties"
msgstr "contohnya Properti"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/app_creator/app_creator.xml:0
msgid "e.g. Real Estate"
msgstr "contohnya Real Estate"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "for company"
msgstr ""

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "no one"
msgstr "tidak ada"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/action_button/action_button.xml:0
msgid "object"
msgstr "object"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "or"
msgstr "atau"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/ir_ui_view.py:0
msgid "studio_approval attribute can only be set in form views"
msgstr "Attribut studio_approval hanya dapat ditetapkan di tampilan formulir"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/editors/form/form_editor_sidebar/properties/chatter_properties/chatter_properties.xml:0
msgid "test email"
msgstr "test email"

#. module: web_studio
#. odoo-python
#: code:addons/web_studio/models/studio_approval.py:0
msgid "to %s."
msgstr "ke %s."

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload it"
msgstr "unggah"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/icon_creator/icon_creator.xml:0
msgid "upload one"
msgstr "unggah satu"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/properties/field_properties/field_properties.xml:0
msgid "x_studio_"
msgstr "x_studio_"

#. module: web_studio
#. odoo-javascript
#: code:addons/web_studio/static/src/client_action/view_editor/interactive_editor/field_configuration/selection_content_dialog.xml:0
msgid "{{ item.isInEdition ? 'Add selection' : 'Edit selection' }}"
msgstr "{{ item.isInEdition ? 'Add selection' : 'Edit selection' }}"
