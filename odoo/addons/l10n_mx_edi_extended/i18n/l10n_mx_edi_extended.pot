# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mx_edi_extended
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-07 08:04+0000\n"
"PO-Revision-Date: 2023-09-07 08:04+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "<br/>Customs:"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid ""
"<span>\n"
"                                <strong>External Trade</strong>\n"
"                            </span>"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "<span> - </span>"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_001
msgid "Abalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_24
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_001
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_001
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_001
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_001
msgid "Abasolo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_001
msgid "Abejones"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_001
msgid "Acacoyagua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_001
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_001
msgid "Acajete"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_25
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_002
msgid "Acala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_001
msgid "Acambay de Ruíz Castañeda"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_002
msgid "Acanceh"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_003
msgid "Acapetahua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_001
msgid "Acaponeta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_001
msgid "Acapulco de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_002
msgid "Acateno"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_076
msgid "Acatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_001
msgid "Acatic"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_001
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_003
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_002
msgid "Acatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_55
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_002
msgid "Acatlán de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_10
msgid "Acatlán de Osorio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_002
msgid "Acatlán de Pérez Figueroa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_004
msgid "Acatzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_002
msgid "Acaxochitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_003
msgid "Acayucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_002
msgid "Acolman"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_001
msgid "Aconchi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_005
msgid "Acteopan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__active
msgid "Active"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_003
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_004
msgid "Actopan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_022
msgid "Acuamanala de Miguel Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_001
msgid "Acuitzio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_005
msgid "Acula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_003
msgid "Aculco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_006
msgid "Acultzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_002
msgid "Acuña"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_002
msgid "Acámbaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_journal__l10n_mx_address_issued_id
msgid "Address Issued"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_004
msgid "Agua Blanca de Iturbide"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_204
msgid "Agua Dulce"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_002
msgid "Agua Prieta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_05
msgid "Agua dulce"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_002
msgid "Agualeguas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_20
msgid "Aguaruto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_001
msgid "Aguascalientes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_002
msgid "Aguililla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_001
msgid "Ahome"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_002
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_006
msgid "Ahuacatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_002
msgid "Ahuacuotzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_001
msgid "Ahualulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_24
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_003
msgid "Ahualulco de Mercado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_007
msgid "Ahuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_008
msgid "Ahuazotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_009
msgid "Ahuehuetitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_001
msgid "Ahumada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_005
msgid "Ajacuba"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_010
msgid "Ajalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_47
msgid "Ajijic"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_003
msgid "Ajuchitlán del Progreso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_003
msgid "Akil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_003
msgid "Alamos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_002
msgid "Alaquines"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_011
msgid "Albino Zertuche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_004
msgid "Alcozauca de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_002
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_113
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_002
msgid "Aldama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_006
msgid "Alfajayucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_012
msgid "Aljojuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_24
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_003
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_003
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_004
msgid "Allende"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_007
msgid "Almoloya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_004
msgid "Almoloya de Alquisiras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_39
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_005
msgid "Almoloya de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_006
msgid "Almoloya del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_008
msgid "Alpatláhuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_005
msgid "Alpoyeca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_003
msgid "Altamira"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_004
msgid "Altamirano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_004
msgid "Altar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_013
msgid "Altepexi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_009
msgid "Alto Lucero de Gutiérrez Barrios"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_39
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_010
msgid "Altotonga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_34
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_011
msgid "Alvarado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_004
msgid "Amacueca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_001
msgid "Amacuzac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_007
msgid "Amanalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_006
msgid "Amatenango de la Frontera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_007
msgid "Amatenango del Valle"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_24
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_008
msgid "Amatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_012
msgid "Amatitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_005
msgid "Amatitán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_003
msgid "Amatlán de Cañas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_014
msgid "Amatlán de los Reyes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_005
msgid "Amatán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_001
msgid "Amaxac de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_001
msgid "Amealco de Bonfil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_006
msgid "Ameca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_009
msgid "Amecameca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_014
msgid "Amixtlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_015
msgid "Amozoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_004
msgid "Angamacutiro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_005
msgid "Angangueo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_008
msgid "Angel Albino Corzo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_015
msgid "Angel R. Cabada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_002
msgid "Angostura"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_004
msgid "Antiguo Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_20
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_005
msgid "Anáhuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_008
msgid "Apan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_33
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_004
msgid "Apaseo el Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_28
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_005
msgid "Apaseo el Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_006
msgid "Apatzingán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_01
msgid "Apatzingán de la Constitución"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_010
msgid "Apaxco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_006
msgid "Apaxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_017
msgid "Apazapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_002
msgid "Apetatitlán de Antonio Carvajal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tla_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_005
msgid "Apizaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_006
msgid "Apodaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_007
msgid "Aporo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_001
msgid "Apozol"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_002
msgid "Apulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_008
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_018
msgid "Aquila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_004
msgid "Aquiles Serdán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_003
msgid "Aquismón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_016
msgid "Aquixtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_007
msgid "Aramberri"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_20
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_008
msgid "Arandas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_007
msgid "Arcelia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_009
msgid "Ario"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_005
msgid "Arivechi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_006
msgid "Arizpe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_004
msgid "Armadillo de los Infante"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_001
msgid "Armería"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_009
msgid "Arriaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_003
msgid "Arroyo Seco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_23
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_004
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_010
msgid "Arteaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_005
msgid "Ascensión"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_002
msgid "Asientos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_019
msgid "Astacinga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_003
msgid "Asunción Cacalotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_004
msgid "Asunción Cuyotepeji"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_005
msgid "Asunción Ixtaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_52
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_006
msgid "Asunción Nochixtlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_007
msgid "Asunción Ocotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_008
msgid "Asunción Tlacolulita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_006
msgid "Atarjea"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_010
msgid "Atemajac de Brizuela"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_017
msgid "Atempan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_008
msgid "Atenango del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_011
msgid "Atenco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_011
msgid "Atengo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_012
msgid "Atenguillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_018
msgid "Atexcal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_007
msgid "Atil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_010
msgid "Atitalaquia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_012
msgid "Atizapán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_013
msgid "Atizapán de Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_014
msgid "Atlacomulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_020
msgid "Atlahuilco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_009
msgid "Atlamajalcingo del Monte"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_003
msgid "Atlangatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_011
msgid "Atlapexco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_002
msgid "Atlatlahucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_015
msgid "Atlautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_080
msgid "Atlequizayan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_019
msgid "Atlixco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_010
msgid "Atlixtac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_004
msgid "Atltzayanca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_003
msgid "Atolinga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_013
msgid "Atotonilco de Tula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_013
msgid "Atotonilco el Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_012
msgid "Atotonilco el Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_56
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_014
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_021
msgid "Atoyac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_011
msgid "Atoyac de Álvarez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_020
msgid "Atoyatempan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_022
msgid "Atzacan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_021
msgid "Atzala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_023
msgid "Atzalan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_022
msgid "Atzitzihuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_023
msgid "Atzitzintla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_25
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_015
msgid "Autlán de Navarro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_016
msgid "Axapusco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_003
msgid "Axochiapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_053
msgid "Axtla de Terrazas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_024
msgid "Axutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_025
msgid "Ayahualulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_004
msgid "Ayala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_017
msgid "Ayapango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_398
msgid "Ayoquezco de Aldama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_016
msgid "Ayotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_025
msgid "Ayotoxco de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_009
msgid "Ayotzintepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_017
msgid "Ayutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_012
msgid "Ayutla de los Libres"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_002
msgid "Azcapotzalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_30
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_013
msgid "Azoyú"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_004
msgid "Baca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_008
msgid "Bacadéhuachi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_roo_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_010
msgid "Bacalar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_009
msgid "Bacanora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_010
msgid "Bacerac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_006
msgid "Bachíniva"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_011
msgid "Bacoachi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_003
msgid "Badiraguato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_13
msgid "Bahias de Huatulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_020
msgid "Bahía de Banderas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_001
msgid "Balancán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_007
msgid "Balleza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_36
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_026
msgid "Banderilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_013
msgid "Banámichi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_008
msgid "Batopilas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_015
msgid "Bavispe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_014
msgid "Baviácora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_010
msgid "Bejucal de Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_011
msgid "Bella Vista"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_114
msgid "Benemérito de las Américas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_014
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_014
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_005
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_071
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_045
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_027
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_004
msgid "Benito Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_016
msgid "Benjamín Hill"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_012
msgid "Berriozábal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_03
msgid "Boca del RÍo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_028
msgid "Boca del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_013
msgid "Bochil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_009
msgid "Bocoyna"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_005
msgid "Bokobá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_019
msgid "Bolaños"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_011
msgid "Briseñas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_12
msgid "Bucerías"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_006
msgid "Buctzotz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_010
msgid "Buenaventura"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_012
msgid "Buenavista"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_12
msgid "Buenavista de Cuellar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_015
msgid "Buenavista de Cuéllar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_005
msgid "Burgos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_008
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_006
msgid "Bustamante"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_012
msgid "Bácum"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_curp
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_curp
msgid "CURP"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_020
msgid "Cabo Corrientes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_03
msgid "Cabo San Lucas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_017
msgid "Caborca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_015
msgid "Cacahoatán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_007
msgid "Cacalchén"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_009
msgid "Cadereyta Jiménez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_004
msgid "Cadereyta de Montes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_018
msgid "Cajeme"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_010
msgid "Calakmul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_029
msgid "Calcahualco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_005
msgid "Calera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_011
msgid "Calihualá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_018
msgid "Calimaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_03
msgid "Calkini"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_001
msgid "Calkiní"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_014
msgid "Calnali"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_008
msgid "Calotmul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_026
msgid "Calpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tla_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_006
msgid "Calpulalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_027
msgid "Caltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_003
msgid "Calvillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_011
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_007
msgid "Camargo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_007
msgid "Camarón de Tejeda"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_030
msgid "Camerino Z. Mendoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_028
msgid "Camocuautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_002
msgid "Campeche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_019
msgid "Cananea"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_001
msgid "Canatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_roo_01
msgid "Cancún"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_005
msgid "Candela"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_011
msgid "Candelaria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_012
msgid "Candelaria Loxicha"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_002
msgid "Canelas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_009
msgid "Cansahcab"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_010
msgid "Cantamayec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_28
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_019
msgid "Capulhuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_247
msgid "Capulálpam de Méndez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_020
msgid "Carbó"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_015
msgid "Cardonal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_012
msgid "Carichí"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_20
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_208
msgid "Carlos A. Carrillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_003
msgid "Carmen"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_031
msgid "Carrillo Puerto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_013
msgid "Carácuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_008
msgid "Casas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_013
msgid "Casas Grandes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_021
msgid "Casimiro Castillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_006
msgid "Castaños"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_157
msgid "Castillo de Teayo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_016
msgid "Catazajá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_45
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_032
msgid "Catemaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_006
msgid "Catorce"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_029
msgid "Caxhuacan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_58
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_033
msgid "Cazones de Herrera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_099
msgid "Cañada Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_117
msgid "Cañadas de Obregón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_006
msgid "Cañitas de Felipe Pescador"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_04
msgid "Cd. de Fray Bernardino de Sahagún"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_007
msgid "Cedral"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_007
msgid "Celaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_011
msgid "Celestún"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_012
msgid "Cenotillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_003
msgid "Centla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_004
msgid "Centro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_011
msgid "Cerralvo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_008
msgid "Cerritos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_61
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_034
msgid "Cerro Azul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_009
msgid "Cerro de San Pedro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Certificate Key"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Certificate Source"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_054
msgid "Chacaltianguis"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_016
msgid "Chacsinkín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_30
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_025
msgid "Chahuites"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_026
msgid "Chalcatongo de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_045
msgid "Chalchicomula de Sesma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_009
msgid "Chalchihuites"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_022
msgid "Chalchihuitán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_025
msgid "Chalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_23
msgid "Chalco de Díaz Covarrubias"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_055
msgid "Chalma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_004
msgid "Champotón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_023
msgid "Chamula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_024
msgid "Chanal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_017
msgid "Chankom"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_026
msgid "Chapa de Mota"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_018
msgid "Chapab"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_46
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_030
msgid "Chapala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_017
msgid "Chapantongo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_046
msgid "Chapulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_018
msgid "Chapulhuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_025
msgid "Chapultenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_027
msgid "Chapultepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_021
msgid "Charapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_015
msgid "Charcas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_022
msgid "Charo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_023
msgid "Chavinda"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_019
msgid "Chemax"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_026
msgid "Chenalhó"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_024
msgid "Cherán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_roo_04
msgid "Chetumal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_027
msgid "Chiapa de Corzo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_028
msgid "Chiapilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tla_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_010
msgid "Chiautempan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_028
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_047
msgid "Chiautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_048
msgid "Chiautzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_021
msgid "Chichimilá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_050
msgid "Chichiquila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_029
msgid "Chicoasén"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_029
msgid "Chicoloapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_030
msgid "Chicomuselo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_056
msgid "Chiconamel"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_36
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_030
msgid "Chiconcuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_049
msgid "Chiconcuautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_057
msgid "Chiconquiaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_058
msgid "Chicontepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_020
msgid "Chicxulub Pueblo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_051
msgid "Chietla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_052
msgid "Chigmecatitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_053
msgid "Chignahuapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_054
msgid "Chignautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_019
msgid "Chihuahua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_022
msgid "Chikindzonot"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_055
msgid "Chila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_056
msgid "Chila de la Sal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_33
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_028
msgid "Chilapa de Álvarez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_025
msgid "Chilchota"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_058
msgid "Chilchotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_019
msgid "Chilcuautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_029
msgid "Chilpancingo de los Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_031
msgid "Chilón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_031
msgid "Chimalhuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_031
msgid "Chimaltitán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_013
msgid "China"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_059
msgid "Chinameca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_060
msgid "Chinampa de Gorostiza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_059
msgid "Chinantla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_026
msgid "Chinicuila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_027
msgid "Chiquihuitlán de Benito Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_032
msgid "Chiquilistlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_062
msgid "Chocamán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_023
msgid "Chocholá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_007
msgid "Choix"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_063
msgid "Chontla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_027
msgid "Chucándiro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_064
msgid "Chumatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_024
msgid "Chumayel"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_028
msgid "Churintzio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_029
msgid "Churumuco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_020
msgid "Chínipas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_36
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_022
msgid "Cihuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_017
msgid "Cintalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_18
msgid "Cintalapa de Figueroa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_035
msgid "Citlaltépetl"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_res_city
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "City"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_01
msgid "Ciudad Acuña"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_01
msgid "Ciudad Adolfo López Mateos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_11
msgid "Ciudad Altamirano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_10
msgid "Ciudad Apaxtla de Castrejón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_01
msgid "Ciudad Apodaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_22
msgid "Ciudad Benito Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_02
msgid "Ciudad Camargo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_01
msgid "Ciudad Constitución"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_06
msgid "Ciudad Cuauhtémoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_011
msgid "Ciudad Fernández"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_03
msgid "Ciudad General Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_14
msgid "Ciudad Gustavo Díaz Ordaz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_02
msgid "Ciudad Guzmán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_03
msgid "Ciudad Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_49
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_014
msgid "Ciudad Ixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_03
msgid "Ciudad Lerdo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_21
msgid "Ciudad Lázaro Cárdenas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_009
msgid "Ciudad Madero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_04
msgid "Ciudad Mante"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_34
msgid "Ciudad Manuel Doblado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_20
msgid "Ciudad Melchor Múzquiz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_17
msgid "Ciudad Miguel Alemán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_10
msgid "Ciudad Nezahualcoyotl"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_04
msgid "Ciudad Obregón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_08
msgid "Ciudad Río Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_08
msgid "Ciudad Sabinas Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_10
msgid "Ciudad Santa Catarina"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_16
msgid "Ciudad Serdán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_19
msgid "Ciudad Tula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_013
msgid "Ciudad Valles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_11
msgid "Ciudad Victoria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_col_05
msgid "Ciudad de Armería"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_02
msgid "Ciudad de México (Azcapotzalco)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_03
msgid "Ciudad de México (Benito Juárez)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_04
msgid "Ciudad de México (Coyoacán)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_05
msgid "Ciudad de México (Cuajimalpa de Morelos)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_06
msgid "Ciudad de México (Cuauhtémoc)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_07
msgid "Ciudad de México (Gustavo A. Madero)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_08
msgid "Ciudad de México (Iztacalco)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_09
msgid "Ciudad de México (Iztapalapa)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_10
msgid "Ciudad de México (Magdalena Contreras)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_11
msgid "Ciudad de México (Miguel Hidalgo)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_12
msgid "Ciudad de México (Milpa Alta)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_14
msgid "Ciudad de México (Tlalpan)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_13
msgid "Ciudad de México (Tláhuac)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_15
msgid "Ciudad de México (Venustiano Carranza)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_16
msgid "Ciudad de México (Xochimilco)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cmx_01
msgid "Ciudad de México (Álvaro Obregón)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_col_04
msgid "Ciudad de Villa de Álvarez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_02
msgid "Ciudad del Carmen"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_010
msgid "Ciudad del Maíz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_012
msgid "Ciénega de Flores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_013
msgid "Ciénega de Zimatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_03
msgid "Coacalco de Berriozabal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_020
msgid "Coacalco de Berriozábal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_036
msgid "Coacoatzintla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_014
msgid "Coahuayana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_016
msgid "Coahuayutla de José María Izazaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_037
msgid "Coahuitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_015
msgid "Coalcomán de Vázquez Pallares"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_018
msgid "Coapilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_015
msgid "Coatecas Altas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_030
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_038
msgid "Coatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_021
msgid "Coatepec Harinas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_005
msgid "Coatlán del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_039
msgid "Coatzacoalcos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_031
msgid "Coatzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_49
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_040
msgid "Coatzintla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_078
msgid "Cochoapa el Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_022
msgid "Cocotitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_28
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_017
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_024
msgid "Cocula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__code
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__code
msgid "Code"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_city__l10n_mx_edi_code
msgid "Code MX"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__code
msgid "Code defined in the SAT to this record."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_city__l10n_mx_edi_code
msgid ""
"Code to use in the CFDI with external trade complement. It is based on the "
"SAT catalog."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_016
msgid "Coeneo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_041
msgid "Coetzala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_032
msgid "Cohetzala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_033
msgid "Cohuecan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_016
msgid "Coicoyán de las Flores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_074
msgid "Cojumatlán de Régules"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_col_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_002
msgid "Colima"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_042
msgid "Colipa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_10
msgid "Colonia Anáhuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_company__l10n_mx_edi_colony_code
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_colony_code
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_colony_code
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.res_company_form_inherit_l10n_mx_edi_extended
msgid "Colony Code"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_company__l10n_mx_edi_colony_code
msgid ""
"Colony Code configured for this company. It is used in the external trade "
"complement to define the colony where the domicile is located."
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "Colony Code..."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_colony
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_colony
msgid "Colony Name"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.res_company_form_inherit_l10n_mx_edi_extended
msgid "Colony..."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_35
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_025
msgid "Colotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_005
msgid "Colón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_003
msgid "Comala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_005
msgid "Comalcalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_043
msgid "Comapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_019
msgid "Comitán de Domínguez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcs_001
msgid "Comondú"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_009
msgid "Comonfort"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_004
msgid "Compostela"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_018
msgid "Concepción Buenavista"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_019
msgid "Concepción Pápalo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_026
msgid "Concepción de Buenos Aires"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_007
msgid "Concepción del Oro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_004
msgid "Concordia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_003
msgid "Coneto de Comonfort"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.res_config_settings_view_form_inherit_l10n_mx_edi_extended
msgid "Configure data to external trade."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_013
msgid "Conkal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_020
msgid "Constancia del Rosario"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_017
msgid "Contepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_018
msgid "Contla de Juan Cuamatzi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_021
msgid "Copainalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_40
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_018
msgid "Copala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_019
msgid "Copalillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_020
msgid "Copanatoyac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_018
msgid "Copándaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_004
msgid "Coquimatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_014
msgid "Coronado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_034
msgid "Coronango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_010
msgid "Coroneo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_006
msgid "Corregidora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_011
msgid "Cortazar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_005
msgid "Cosalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_19
msgid "Cosamaloapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_045
msgid "Cosamaloapan de Carpio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_046
msgid "Cosautlán de Carvajal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_047
msgid "Coscomatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_021
msgid "Cosolapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_53
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_048
msgid "Cosoleacaque"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_022
msgid "Cosoltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_004
msgid "Cosío"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_049
msgid "Cotaxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_019
msgid "Cotija"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_25
msgid "Cotija de la Paz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__country_id
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "Country"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_035
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_014
msgid "Coxcatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_050
msgid "Coxquihui"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_015
msgid "Coyame del Sotol"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_003
msgid "Coyoacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_036
msgid "Coyomeapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_023
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_037
msgid "Coyotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_021
msgid "Coyuca de Benítez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_022
msgid "Coyuca de Catalán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_051
msgid "Coyutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_roo_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_001
msgid "Cozumel"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__create_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__create_date
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__create_date
msgid "Created on"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_010
msgid "Cruillas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_14
msgid "Cruz Azul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_38
msgid "Cruz Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_004
msgid "Cuajimalpa de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_36
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_023
msgid "Cuajinicuilapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_024
msgid "Cualác"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_008
msgid "Cuapiaxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_038
msgid "Cuapiaxtla de Madero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_007
msgid "Cuatro Ciénegas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_14
msgid "Cuatro Ciénegas de Carranza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_017
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_015
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_005
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_008
msgid "Cuauhtémoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_039
msgid "Cuautempan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_025
msgid "Cuautepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_016
msgid "Cuautepec de Hinojosa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_040
msgid "Cuautinchán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_024
msgid "Cuautitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_121
msgid "Cuautitlán Izcalli"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_027
msgid "Cuautitlán de García Barragán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_028
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_006
msgid "Cuautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mor_01
msgid "Cuautla (Cuautla de Morelos)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_041
msgid "Cuautlancingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_009
msgid "Cuaxomulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_042
msgid "Cuayuca de Andrade"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_022
msgid "Cucurpe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_004
msgid "Cuencamé"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mor_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_007
msgid "Cuernavaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_20
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_012
msgid "Cuerámaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_026
msgid "Cuetzala del Progreso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_043
msgid "Cuetzalan del Progreso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_41
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_052
msgid "Cuichapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_56
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_023
msgid "Cuilápam de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_32
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_053
msgid "Cuitláhuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_020
msgid "Cuitzeo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_27
msgid "Cuitzeo del Porvenir"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_006
msgid "Culiacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_02
msgid "Culiacán Rosales"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_023
msgid "Cumpas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_014
msgid "Cuncunul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_006
msgid "Cunduacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_029
msgid "Cuquío"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_018
msgid "Cusihuiriachi"
msgstr ""

#. module: l10n_mx_edi_extended
#. odoo-python
#: code:addons/l10n_mx_edi_extended/models/account_move.py:0
msgid ""
"Custom numbers set on invoice lines are invalid and should have a pattern like: 15  48  3009  0001234:\n"
"%(invalid_message)s"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Customs Qty"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Customs Unit"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_uom_uom__l10n_mx_edi_code_aduana
msgid "Customs code"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_move_line__l10n_mx_edi_customs_number
msgid "Customs number"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_027
msgid "Cutzamala de Pinzón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_024
msgid "Cuyamecalco Villa de Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_044
msgid "Cuyoaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_015
msgid "Cuzamá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_09
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_005
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_002
msgid "Cárdenas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_044
msgid "Córdoba"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields.selection,name:l10n_mx_edi_extended.selection__account_move__l10n_mx_edi_external_trade_type__02
#: model:ir.model.fields.selection,name:l10n_mx_edi_extended.selection__res_partner__l10n_mx_edi_external_trade_type__02
msgid "Definitive"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_033
msgid "Degollado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_009
msgid "Del Nayar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_021
msgid "Delicias"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__display_name
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_024
msgid "Divisaderos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_014
msgid "Doctor Arroyo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_015
msgid "Doctor Coss"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_016
msgid "Doctor González"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_38
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_013
msgid "Doctor Mora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_39
msgid "Dolores Hgo. Cuna de la Indep. Nal."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_014
msgid "Dolores Hidalgo Cuna de la Independencia Nacional"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_060
msgid "Domingo Arenas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_032
msgid "Donato Guerra"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "Door #"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_022
msgid "Dr. Belisario Domínguez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_005
msgid "Durango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_026
msgid "Dzemul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_027
msgid "Dzidzantún"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_029
msgid "Dzilam González"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_028
msgid "Dzilam de Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_030
msgid "Dzitás"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_031
msgid "Dzoncauich"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_025
msgid "Dzán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_016
msgid "Ebano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_033
msgid "Ecatepec de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_034
msgid "Ecatzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_030
msgid "Ecuandureo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_075
msgid "Eduardo Neri"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_034
msgid "Ejutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_009
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_009
msgid "El Arenal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_010
msgid "El Barrio de la Soledad"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_014
msgid "El Bosque"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_27
msgid "El Camarón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_010
msgid "El Carmen"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_007
msgid "El Carmen Tequexquitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_030
msgid "El Espinal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_010
msgid "El Fuerte"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_29
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_037
msgid "El Grullo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_67
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_205
msgid "El Higo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_054
msgid "El Limón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_010
msgid "El Llano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_021
msgid "El Mante"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_011
msgid "El Marqués"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_20
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_058
msgid "El Naranjo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_018
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_064
msgid "El Oro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_015
msgid "El Plateado de Joaquín Amaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_070
msgid "El Porvenir"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_que_04
msgid "El Pueblito"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_42
msgid "El Quince (San José el Quince)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_57
msgid "El Rosario"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_070
msgid "El Salto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_041
msgid "El Salvador"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_064
msgid "El Tule"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_18
msgid "El cercado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_18
msgid "El rosario"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_008
msgid "Elota"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_020
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_061
msgid "Eloxochitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_029
msgid "Eloxochitlán de Flores Magón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_021
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_008
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_007
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_046
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_065
msgid "Emiliano Zapata"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_025
msgid "Empalme"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_21
msgid "Empalme Escobedo (Escobedo)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_035
msgid "Encarnación de Díaz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcn_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcn_001
msgid "Ensenada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_062
msgid "Epatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_022
msgid "Epazoyucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_031
msgid "Epitacio Huerta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_032
msgid "Erongarícuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_008
msgid "Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_009
msgid "Escuinapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_03
msgid "Escuinapa de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_032
msgid "Escuintla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_009
msgid "Escárcega"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_012
msgid "Españita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_063
msgid "Esperanza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_066
msgid "Espinal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_032
msgid "Espita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_15
msgid "Estación Manuel (Úrsulo Galván)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_19
msgid "Estación Naranjo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_026
msgid "Etchojoa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_22
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_036
msgid "Etzatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_bank_statement_line__l10n_mx_edi_external_trade_type
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_move__l10n_mx_edi_external_trade_type
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_payment__l10n_mx_edi_external_trade_type
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_external_trade_type
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_external_trade_type
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.res_config_settings_view_form_inherit_l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.view_l10n_mx_edi_invoice_form_inherit
msgid "External Trade"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_007
msgid "Ezequiel Montes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_roo_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_002
msgid "Felipe Carrillo Puerto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_067
msgid "Filomeno Mata"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_030
msgid "Florencio Villarreal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_068
msgid "Fortín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_33
msgid "Fortín de las Flores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_17
msgid "Fracción el Refugio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_009
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_023
msgid "Francisco I. Madero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_13
msgid "Francisco I. Madero (Chávez)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_07
msgid "Francisco I. Madero (Puga)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_033
msgid "Francisco León"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_064
msgid "Francisco Z. Mena"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_010
msgid "Fresnillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_032
msgid "Fresnillo de Trujano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_02
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_010
msgid "Frontera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_034
msgid "Frontera Comalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_035
msgid "Frontera Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_027
msgid "Fronteras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_033
msgid "Gabriel Zamora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mor_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_023
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_017
msgid "Galeana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_018
msgid "García"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_012
msgid "Genaro Codina"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_020
msgid "General Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_031
msgid "General Canuto A. Neri"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_011
msgid "General Cepeda"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_013
msgid "General Enrique Estrada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_021
msgid "General Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_065
msgid "General Felipe Ángeles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_014
msgid "General Francisco R. Murguía"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_032
msgid "General Heliodoro Castillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_48
msgid "General Miguel Alemán (Potrero Nuevo)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_070
msgid "General Plutarco Elías Calles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_016
msgid "General Pánfilo Natera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_006
msgid "General Simón Bolívar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_022
msgid "General Terán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_023
msgid "General Treviño"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_024
msgid "General Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_025
msgid "General Zuazua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_012
msgid "González"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_026
msgid "Gran Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_028
msgid "Granados"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_038
msgid "Guachinango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_027
msgid "Guachochi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_039
msgid "Guadalajara"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_017
msgid "Guadalcázar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_04
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_028
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_026
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_066
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_017
msgid "Guadalupe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_033
msgid "Guadalupe Etla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_008
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_067
msgid "Guadalupe Victoria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_034
msgid "Guadalupe de Ramírez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_029
msgid "Guadalupe y Calvo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_06
msgid "Guamúchil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_009
msgid "Guanaceví"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_015
msgid "Guanajuato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_011
msgid "Guasave"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_029
msgid "Guaymas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_030
msgid "Guazapares"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_035
msgid "Guelatao de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_031
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_012
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_014
msgid "Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_11
msgid "Guerrero Negro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_036
msgid "Guevea de Humboldt"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_005
msgid "Gustavo A. Madero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_015
msgid "Gustavo Díaz Ordaz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_25
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_069
msgid "Gutiérrez Zamora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_025
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_079
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_011
msgid "Gómez Farías"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_007
msgid "Gómez Palacio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_013
msgid "Güémez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_033
msgid "Halachó"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_005
msgid "Hecelchakán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_068
msgid "Hermenegildo Galeana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_030
msgid "Hermosillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_03
msgid "Heroica Caborca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_12
msgid "Heroica Ciudad de Cananea"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_31
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_028
msgid "Heroica Ciudad de Ejutla de Crespo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_039
msgid "Heroica Ciudad de Huajuapan de León"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_043
msgid "Heroica Ciudad de Juchitán de Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_46
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_397
msgid "Heroica Ciudad de Tlaxiaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_06
msgid "Heroica Guaymas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_05
msgid "Heroica Matamoros"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_08
msgid "Heroica Mulegé"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_10
msgid "Heroica Nogales"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_12
msgid "Heroica Zitácuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_013
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_010
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_034
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_047
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_016
msgid "Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_032
msgid "Hidalgo del Parral"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_070
msgid "Hidalgotitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_10
msgid "Higuera de Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_028
msgid "Higueras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_034
msgid "Hocabá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_035
msgid "Hoctún"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_036
msgid "Homún"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_057
msgid "Honey"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_006
msgid "Hopelchén"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_040
msgid "Hostotipaquillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "House #"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_031
msgid "Huachinera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_005
msgid "Huajicori"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_029
msgid "Hualahuises"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tla_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_013
msgid "Huamantla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_37
msgid "Huamuxtitlan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_033
msgid "Huamuxtitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_036
msgid "Huandacareo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_037
msgid "Huaniqueo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_018
msgid "Huanusco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_016
msgid "Huanímaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_069
msgid "Huaquechula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_024
msgid "Huasca de Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_033
msgid "Huatabampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_070
msgid "Huatlatlauca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_071
msgid "Huatusco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_28
msgid "Huatusco de Chicuellar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_071
msgid "Huauchinango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_040
msgid "Huautepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_025
msgid "Huautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_041
msgid "Huautla de Jiménez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_43
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_072
msgid "Huayacocotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_026
msgid "Huazalingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_027
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_072
msgid "Huehuetla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_018
msgid "Huehuetlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_073
msgid "Huehuetlán el Chico"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_150
msgid "Huehuetlán el Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_035
msgid "Huehuetoca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_037
msgid "Huehuetán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_033
msgid "Huejotitán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_074
msgid "Huejotzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_50
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_042
msgid "Huejuquilla el Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_028
msgid "Huejutla de Reyes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_041
msgid "Huejúcar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_038
msgid "Huetamo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_19
msgid "Huetamo de Núñez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_075
msgid "Hueyapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_073
msgid "Hueyapan de Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_014
msgid "Hueyotlipan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_036
msgid "Hueypoxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_076
msgid "Hueytamalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_077
msgid "Hueytlalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_037
msgid "Huhí"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_029
msgid "Huichapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_57
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_074
msgid "Huiloapan de Cuauhtémoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_008
msgid "Huimanguillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_008
msgid "Huimilpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_039
msgid "Huiramba"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_039
msgid "Huitiupán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_009
msgid "Huitzilac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_078
msgid "Huitzilan de Serdán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_079
msgid "Huitziltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_26
msgid "Huitzuco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_034
msgid "Huitzuco de los Figueroa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_037
msgid "Huixquilucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_06
msgid "Huixquilucan de Degollado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_23
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_040
msgid "Huixtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_038
msgid "Huixtán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_038
msgid "Hunucmá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_032
msgid "Huásabas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_034
msgid "Huépac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__id
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__id
msgid "ID"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__active
msgid ""
"If the tariff fraction has expired it could be disabled to do not allow "
"select the record."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_bank_statement_line__l10n_mx_edi_external_trade_type
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_move__l10n_mx_edi_external_trade_type
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_payment__l10n_mx_edi_external_trade_type
msgid "If this field is 02, the CFDI will include the complement."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_bank_statement_line__l10n_mx_edi_external_trade
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_move__l10n_mx_edi_external_trade
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_payment__l10n_mx_edi_external_trade
msgid ""
"If this field is active, the CFDI that generates this invoice will include "
"the complement 'External Trade'."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_034
msgid "Ignacio Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_075
msgid "Ignacio de la Llave"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_035
msgid "Iguala de la Independencia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_036
msgid "Igualapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_076
msgid "Ilamatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_081
msgid "Iliatenco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_035
msgid "Imuris"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_curp
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_curp
msgid ""
"In Mexico, the Single Code of Population Registration (CURP) is a unique "
"alphanumeric code of 18 characters used to officially identify both "
"residents and Mexican citizens throughout the country."
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Incoterm"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_040
msgid "Indaparapeo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_company__l10n_mx_edi_num_exporter
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_config_settings__l10n_mx_edi_num_exporter
msgid ""
"Indicates the number of reliable exporter in accordance with Article 22 of "
"Annex 1 of the Free Trade Agreement with the European Association and the "
"Decision of the European Community. Used in External Trade in the attribute "
"\"NumeroExportadorConfiable\"."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_011
msgid "Indé"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_017
msgid "Irapuato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_041
msgid "Irimbo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_038
msgid "Isidro Fabela"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_31
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_077
msgid "Isla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_roo_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_003
msgid "Isla Mujeres"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_product_product__l10n_mx_edi_tariff_fraction_id
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_product_template__l10n_mx_edi_tariff_fraction_id
msgid ""
"It is used to express the key of the tariff fraction corresponding to the "
"description of the product to export."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_030
msgid "Iturbide"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_081
msgid "Ixcamilpa de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_082
msgid "Ixcaquixtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_037
msgid "Ixcateopan de Cuauhtémoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_078
msgid "Ixcatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_079
msgid "Ixhuacán de los Reyes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_081
msgid "Ixhuatlancillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_083
msgid "Ixhuatlán de Madero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_080
msgid "Ixhuatlán del Café"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_082
msgid "Ixhuatlán del Sureste"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_042
msgid "Ixhuatán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_039
msgid "Ixil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_084
msgid "Ixmatlahuacan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_030
msgid "Ixmiquilpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_065
msgid "Ixpantepec Nieves"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_083
msgid "Ixtacamaxtitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_043
msgid "Ixtacomitán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_015
msgid "Ixtacuixtla de Mariano Matamoros"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_29
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_085
msgid "Ixtaczoquitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_044
msgid "Ixtapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_039
msgid "Ixtapaluca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_040
msgid "Ixtapan de la Sal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_041
msgid "Ixtapan del Oro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_045
msgid "Ixtapangajoya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_016
msgid "Ixtenco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_084
msgid "Ixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_042
msgid "Ixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_006
msgid "Ixtlahuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_044
msgid "Ixtlahuacán de los Membrillos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_045
msgid "Ixtlahuacán del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_042
msgid "Ixtlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_042
msgid "Ixtlán de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_006
msgid "Ixtlán del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_040
msgid "Izamal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_006
msgid "Iztacalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_007
msgid "Iztapalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_085
msgid "Izúcar de Matamoros"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_031
msgid "Jacala de Ledezma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_043
msgid "Jacona"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_04
msgid "Jacona de Plancarte"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_007
msgid "Jala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_086
msgid "Jalacingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_009
msgid "Jalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_088
msgid "Jalcomulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_046
msgid "Jalostotitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_019
msgid "Jalpa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_010
msgid "Jalpa de Méndez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_086
msgid "Jalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_009
msgid "Jalpan de Serra"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_044
msgid "Jaltenco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_032
msgid "Jaltocán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_090
msgid "Jamapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_33
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_047
msgid "Jamay"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_035
msgid "Janos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_010
msgid "Jantetelco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_35
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_018
msgid "Jaral del Progreso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_017
msgid "Jaumave"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_020
msgid "Jerez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_02
msgid "Jerez de García Salinas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_019
msgid "Jerécuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_091
msgid "Jesús Carranza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_005
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_048
msgid "Jesús María"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_045
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_093
msgid "Jilotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_049
msgid "Jilotlán de los Dolores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_046
msgid "Jilotzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_036
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_014
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_044
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_018
msgid "Jiménez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_021
msgid "Jiménez del Teul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_045
msgid "Jiquilpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_23
msgid "Jiquilpan de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_046
msgid "Jiquipilas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_047
msgid "Jiquipilco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_047
msgid "Jitotol"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_011
msgid "Jiutepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_44
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_050
msgid "Jocotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_048
msgid "Jocotitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mor_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_012
msgid "Jojutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_087
msgid "Jolalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_013
msgid "Jonacatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_088
msgid "Jonotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_011
msgid "Jonuta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_089
msgid "Jopala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_049
msgid "Joquicingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_169
msgid "José Azueta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_35
msgid "José Cardel"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_079
msgid "José Joaquín de Herrera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_12
msgid "José Mariano Jiménez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_006
msgid "José María Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_113
msgid "José Sixto Verduzco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_11
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_19
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_022
msgid "Juan Aldama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_090
msgid "Juan C. Bonilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_40
msgid "Juan Díaz Covarrubias"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_091
msgid "Juan Galindo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_092
msgid "Juan N. Méndez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_039
msgid "Juan R. Escudero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_27
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_094
msgid "Juan Rodríguez Clara"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_051
msgid "Juanacatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_023
msgid "Juchipila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_095
msgid "Juchique de Ferrer"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_050
msgid "Juchitepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_29
msgid "Juchitepec de Mariano Riva Palacio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_052
msgid "Juchitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_080
msgid "Juchitán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_01
msgid "Juchitán (Juchitán de Zaragoza)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_038
msgid "Julimes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_047
msgid "Jungapeo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_037
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_048
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_015
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_046
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_031
msgid "Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_033
msgid "Juárez Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_089
msgid "Jáltipan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_09
msgid "Jáltipan de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_yuc_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_041
msgid "Kanasín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_042
msgid "Kantunil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_roo_06
msgid "Kantunilkín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_043
msgid "Kaua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_044
msgid "Kinchil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_045
msgid "Kopomá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_company__l10n_mx_edi_colony
msgid "L10N Mx Edi Colony"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_company__l10n_mx_edi_locality
msgid "L10N Mx Edi Locality"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_016
msgid "La Antigua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_32
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_018
msgid "La Barca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_021
msgid "La Colorada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_017
msgid "La Compañía"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_020
msgid "La Concordia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_016
msgid "La Cruz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_036
msgid "La Grandeza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_035
msgid "La Huacana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_043
msgid "La Huerta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_041
msgid "La Independencia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_050
msgid "La Libertad"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_008
msgid "La Magdalena Contreras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_048
msgid "La Magdalena Tlaltelulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_095
msgid "La Magdalena Tlatlauquitepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_057
msgid "La Manzanilla de la Paz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_040
msgid "La Misión"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcs_003
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_070
msgid "La Paz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_069
msgid "La Pe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_127
msgid "La Perla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_069
msgid "La Piedad"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_076
msgid "La Reforma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_16
msgid "La Resolana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_556
msgid "La Trinidad Vista Hermosa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_099
msgid "La Trinitaria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_22
msgid "La Unión"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_068
msgid "La Unión de Isidoro Montes de Oca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_019
msgid "La Yesca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_16
msgid "La peñita de Jaltemba"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_05
msgid "La piedad de Cabadas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_093
msgid "Lafragua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_053
msgid "Lagos de Moreno"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_48
msgid "Lagunas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_048
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_019
msgid "Lagunillas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_016
msgid "Lamadrid"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_032
msgid "Lampazos de Naranjo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_010
msgid "Landa de Matamoros"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_096
msgid "Landero y Coss"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_049
msgid "Larráinzar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_47
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_061
msgid "Las Choapas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_22
msgid "Las Guacamayas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_052
msgid "Las Margaritas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_107
msgid "Las Minas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_53
msgid "Las Pintitas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_075
msgid "Las Rosas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_13
msgid "Las Varas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_132
msgid "Las Vigas de Ramírez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__write_uid
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__write_date
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_040
msgid "Leonardo Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_012
msgid "Lerdo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_54
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_097
msgid "Lerdo de Tejada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_051
msgid "Lerma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_020
msgid "León"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_07
msgid "León de los Aldama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_094
msgid "Libres"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_24
msgid "Lic. Benito Juárez (Campo Gobierno)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_033
msgid "Linares"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_019
msgid "Llera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_l10n_mx_edi_res_locality
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_company__l10n_mx_edi_locality_id
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_locality_id
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_locality_id
msgid "Locality"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_locality
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_locality
msgid "Locality Name"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.res_company_form_inherit_l10n_mx_edi_extended
msgid "Locality..."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_034
msgid "Lolotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_044
msgid "Loma Bonita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_05
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcs_009
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_024
msgid "Loreto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_003
msgid "Los Aldamas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcs_008
msgid "Los Cabos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_027
msgid "Los Herreras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_01
msgid "Los Mochis"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_042
msgid "Los Ramones"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_075
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_137
msgid "Los Reyes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_07
msgid "Los Reyes Acaquilpan (La Paz)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_118
msgid "Los Reyes de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_02
msgid "Los Reyes de Salgado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_025
msgid "Luis Moya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_123
msgid "Luvianos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_052
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_007
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_047
msgid "Lázaro Cárdenas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_039
msgid "López"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_012
msgid "Macuspana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_040
msgid "Madera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_049
msgid "Madero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_26
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_055
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_036
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_098
msgid "Magdalena"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_045
msgid "Magdalena Apasco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_046
msgid "Magdalena Jaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_048
msgid "Magdalena Mixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_049
msgid "Magdalena Ocotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_050
msgid "Magdalena Peñasco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_051
msgid "Magdalena Teitipac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_052
msgid "Magdalena Tequisistlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_053
msgid "Magdalena Tlacotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_562
msgid "Magdalena Yodocono de Porfirio Díaz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_054
msgid "Magdalena Zahuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_14
msgid "Magdalena de Kino"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_041
msgid "Maguarichi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_020
msgid "Mainero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_052
msgid "Malinalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_041
msgid "Malinaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_099
msgid "Maltrata"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_046
msgid "Mama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_100
msgid "Manlio Fabio Altamirano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_042
msgid "Manuel Benavides"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_008
msgid "Manuel Doblado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_13
msgid "Manuel Ojinaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_col_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_007
msgid "Manzanillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_047
msgid "Maní"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_051
msgid "Mapastepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_013
msgid "Mapimí"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_050
msgid "Maravatío"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_15
msgid "Maravatío de Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_115
msgid "Maravilla Tenejapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_051
msgid "Marcos Castellanos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_31
msgid "Marfil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_101
msgid "Mariano Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_42
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_055
msgid "Mariscala de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_077
msgid "Marquelia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_116
msgid "Marqués de Comillas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_102
msgid "Martínez de la Torre"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_034
msgid "Marín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_058
msgid "Mascota"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_043
msgid "Matachí"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_044
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_017
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_022
msgid "Matamoros"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_020
msgid "Matehuala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_057
msgid "Matlapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_057
msgid "Matías Romero Avendaño"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_048
msgid "Maxcanú"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_049
msgid "Mayapán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_059
msgid "Mazamitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_053
msgid "Mazapa de Madero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_026
msgid "Mazapil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_096
msgid "Mazapiltepec de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_017
msgid "Mazatecochco de José María Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_014
msgid "Mazatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_012
msgid "Mazatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_058
msgid "Mazatlán Villa de Flores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_054
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_037
msgid "Mazatán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_103
msgid "Mecatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_104
msgid "Mecayapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_105
msgid "Medellín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_26
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_053
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_035
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_027
msgid "Melchor Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_045
msgid "Meoqui"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_037
msgid "Mesones Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_055
msgid "Metapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_035
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_054
msgid "Metepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_043
msgid "Metlatónoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_037
msgid "Metztitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcn_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcn_002
msgid "Mexicali"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_055
msgid "Mexicaltzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_l10n_mx_edi_tariff_fraction
msgid "Mexican EDI Tariff Fraction"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_l10n_mx_edi_document
msgid "Mexican documents that needs to transit outside of Odoo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_external_trade_type
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_external_trade_type
msgid ""
"Mexico: Indicates whether the partner is foreign and if an External Trade "
"complement is required.01 - Not Set: No Complement.02 - Definitive: Adds the"
" External Trade complement to CFDI.03 - Temporal: Used when exporting goods "
"for a temporary period."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_021
msgid "Mexquitic de Carmona"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_060
msgid "Mexticacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_014
msgid "Mezquital"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_028
msgid "Mezquital del Oro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_061
msgid "Mezquitic"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_015
msgid "Miacatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_106
msgid "Miahuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_25
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_059
msgid "Miahuatlán de Porfirio Díaz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_024
msgid "Mier"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_036
msgid "Mier y Noriega"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_025
msgid "Miguel Alemán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_029
msgid "Miguel Auza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_016
msgid "Miguel Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_009
msgid "Milpa Alta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_037
msgid "Mina"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_008
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_108
msgid "Minatitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_051
msgid "Mineral de la Reforma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_038
msgid "Mineral del Chico"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_039
msgid "Mineral del Monte"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_026
msgid "Miquihuana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_109
msgid "Misantla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_056
msgid "Mitontic"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_060
msgid "Mixistlán de la Reforma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_041
msgid "Mixquiahuala de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_097
msgid "Mixtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_110
msgid "Mixtla de Altamirano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_062
msgid "Mixtlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_044
msgid "Mochitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_051
msgid "Mocochá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_013
msgid "Mocorito"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_022
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_038
msgid "Moctezuma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_042
msgid "Molango de Escamilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_098
msgid "Molcaxac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_111
msgid "Moloacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_030
msgid "Momax"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_018
msgid "Monclova"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_061
msgid "Monjas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_031
msgid "Monte Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_117
msgid "Montecristo de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_038
msgid "Montemorelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_039
msgid "Monterrey"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_053
msgid "Morelia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_22
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_046
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_019
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_056
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_054
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_032
msgid "Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_047
msgid "Moris"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_021
msgid "Moroleón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_057
msgid "Motozintla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_24
msgid "Motozintla de Mendoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_052
msgid "Motul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_yuc_04
msgid "Motul de Carrillo Puerto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_033
msgid "Moyahua de Estrada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcs_002
msgid "Mulegé"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_053
msgid "Muna"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_company__l10n_mx_edi_locality_id
msgid "Municipality configured for this company"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_054
msgid "Muxupip"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_011
msgid "Muñoz de Domingo Arenas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_042
msgid "Mártir de Cuilapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_056
msgid "Mártires de Tacubaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_023
msgid "Méndez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_yuc_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_050
msgid "Mérida"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_055
msgid "Múgica"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_020
msgid "Múzquiz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_013
msgid "Nacajuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_039
msgid "Naco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_041
msgid "Nacozari de García"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_021
msgid "Nadadores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_056
msgid "Nahuatzen"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__name
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__name
msgid "Name"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__name
msgid "Name defined in the SAT catalog to this record."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_048
msgid "Namiquipa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_021
msgid "Nanacamilpa de Mariano Arista"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_206
msgid "Nanchital de Lázaro Cárdenas del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_112
msgid "Naolinco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_113
msgid "Naranjal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_02
msgid "Naranjos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_013
msgid "Naranjos Amatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_062
msgid "Natividad"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_023
msgid "Natívitas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_057
msgid "Naucalpan de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_100
msgid "Naupan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_114
msgid "Nautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_101
msgid "Nauzontla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_022
msgid "Nava"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_042
msgid "Navojoa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_018
msgid "Navolato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_063
msgid "Nazareno Etla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_015
msgid "Nazas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_102
msgid "Nealtican"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_bank_statement_line__l10n_mx_edi_external_trade
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_move__l10n_mx_edi_external_trade
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_payment__l10n_mx_edi_external_trade
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_external_trade
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_external_trade
msgid "Need external trade?"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_064
msgid "Nejapa de Madero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_059
msgid "Nextlalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_058
msgid "Nezahualcóyotl"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_103
msgid "Nicolás Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_043
msgid "Nicolás Flores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_060
msgid "Nicolás Romero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_058
msgid "Nicolás Ruíz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_034
msgid "Nochistlán de Mejía"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_057
msgid "Nocupétaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_46
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_043
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_115
msgid "Nogales"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_016
msgid "Nombre de Dios"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_049
msgid "Nonoava"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_044
msgid "Nopala de Villagrán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_061
msgid "Nopaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_104
msgid "Nopalucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_035
msgid "Noria de Ángeles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_colony_code
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_colony_code
msgid ""
"Note: Only use this field if this partner is the company address or if it is a branch office.\n"
"Colony code that will be used in the CFDI with the external trade as Emitter colony. It must be a code from the SAT catalog."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_20
msgid "Nueva Ciudad Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_26
msgid "Nueva Italia de Ruiz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_09
msgid "Nueva Rosita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_050
msgid "Nuevo Casas Grandes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_039
msgid "Nuevo Ideal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_027
msgid "Nuevo Laredo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_028
msgid "Nuevo Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_058
msgid "Nuevo Parangaricutiro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_059
msgid "Nuevo Urecho"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_504
msgid "Nuevo Zoquiápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_060
msgid "Numarán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid ""
"Number\n"
"                                        Certificate Source"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_company__l10n_mx_edi_num_exporter
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_res_config_settings__l10n_mx_edi_num_exporter
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Number of Reliable Exporter"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_040
msgid "Nácori Chico"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_067
msgid "Oaxaca de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_051
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_023
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_017
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_022
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_061
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_029
msgid "Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_059
msgid "Ocosingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_060
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_105
msgid "Ocotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_39
msgid "Ocotito"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_063
msgid "Ocotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_23
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_068
msgid "Ocotlán de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_40
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_062
msgid "Ocoyoacac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_106
msgid "Ocoyucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_061
msgid "Ocozocoautla de Espinosa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_063
msgid "Ocuilan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_016
msgid "Ocuituco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_052
msgid "Ojinaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_036
msgid "Ojocaliente"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_064
msgid "Ojuelos de Jalisco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_045
msgid "Olinalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_107
msgid "Olintla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_116
msgid "Oluta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_117
msgid "Omealca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_046
msgid "Ometepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_045
msgid "Omitlán de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_044
msgid "Onavas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Operation Type"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_055
msgid "Opichén"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_045
msgid "Opodepe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_locality_id
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_locality_id
msgid ""
"Optional attribute used in the XML that serves to define the locality where "
"the domicile is located."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_move_line__l10n_mx_edi_customs_number
msgid ""
"Optional field for entering the customs information in the case of first-hand sales of imported goods or in the case of foreign trade operations with goods or services.\n"
"The format must be:\n"
" - 2 digits of the year of validation followed by two spaces.\n"
" - 2 digits of customs clearance followed by two spaces.\n"
" - 4 digits of the serial number followed by two spaces.\n"
" - 1 digit corresponding to the last digit of the current year, except in case of a consolidated customs initiated in the previous year of the original request for a rectification.\n"
" - 6 digits of the progressive numbering of the custom."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_046
msgid "Oquitoa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_108
msgid "Oriental"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_118
msgid "Orizaba"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_062
msgid "Ostuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_063
msgid "Osumacinta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_119
msgid "Otatitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_120
msgid "Oteapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_004
msgid "Othón P. Blanco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_065
msgid "Otumba"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_066
msgid "Otzoloapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_067
msgid "Otzolotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_019
msgid "Otáez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_064
msgid "Oxchuc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_056
msgid "Oxkutzcab"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_121
msgid "Ozuluama de Mascareñas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_068
msgid "Ozumba"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_006
msgid "Pabellón de Arteaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_048
msgid "Pachuca de Soto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_047
msgid "Pacula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_030
msgid "Padilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_109
msgid "Pahuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_062
msgid "Pajacuarán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_122
msgid "Pajapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_065
msgid "Palenque"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_007
msgid "Palizada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_110
msgid "Palmar de Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_031
msgid "Palmillas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_057
msgid "Panabá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_063
msgid "Panindícuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_024
msgid "Panotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_066
msgid "Pantelhó"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_067
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_111
msgid "Pantepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_069
msgid "Papalotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_041
msgid "Papalotla de Xicohténcatl"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_124
msgid "Papantla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_13
msgid "Papantla de Olarte"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_065
msgid "Paracho"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_13
msgid "Paracho de Verduzco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_37
msgid "Paraje Nuevo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_014
msgid "Paraíso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_024
msgid "Parras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_05
msgid "Parras de la Fuente"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_064
msgid "Parácuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_040
msgid "Parás"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_44
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_126
msgid "Paso de Ovejas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_68
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_125
msgid "Paso del Macho"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_047
msgid "Pedro Ascencio Alquisiras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_012
msgid "Pedro Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_067
msgid "Penjamillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_068
msgid "Peribán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_128
msgid "Perote"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_041
msgid "Pesquería"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_048
msgid "Petatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_112
msgid "Petlalcingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_058
msgid "Peto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_013
msgid "Peñamiller"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_021
msgid "Peñón Blanco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_113
msgid "Piaxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_19
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_068
msgid "Pichucalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_025
msgid "Piedras Negras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_065
msgid "Pihuamo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_069
msgid "Pijijiapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_049
msgid "Pilcaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_002
msgid "Pinal de Amoles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_038
msgid "Pinos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_070
msgid "Pinotepa de Don Luis"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_049
msgid "Pisaflores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_047
msgid "Pitiquito"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_26
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_129
msgid "Platón Sánchez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_38
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_130
msgid "Playa Vicente"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_roo_05
msgid "Playa del Carmen"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcn_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcn_005
msgid "Playas de Rosarito"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_071
msgid "Pluma Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_022
msgid "Poanas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_071
msgid "Polotitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_11
msgid "Pomuch"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_19
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_066
msgid "Poncitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_131
msgid "Poza Rica de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_053
msgid "Praxedis G. Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_product_template
msgid "Product"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model,name:l10n_mx_edi_extended.model_uom_uom
msgid "Product Unit of Measure"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_026
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_059
msgid "Progreso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_050
msgid "Progreso de Obregón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_114
msgid "Puebla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_03
msgid "Puebla (Heroica Puebla)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_023
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_024
msgid "Pueblo Nuevo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_072
msgid "Pueblo Nuevo Solistahuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_133
msgid "Pueblo Viejo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_134
msgid "Puente Nacional"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mor_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_017
msgid "Puente de Ixtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_06
msgid "Puerto Adolfo López Mateos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_08
msgid "Puerto Escondido"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_20
msgid "Puerto Madero (San Benito)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_011
msgid "Puerto Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_048
msgid "Puerto Peñasco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_067
msgid "Puerto Vallarta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_050
msgid "Pungarabato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_071
msgid "Puruándiro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_070
msgid "Purépero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_29
msgid "Purísima de Bustos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_025
msgid "Purísima del Rincón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_073
msgid "Putla Villa de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_123
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_037
msgid "Pánuco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_020
msgid "Pánuco de Coronado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_066
msgid "Pátzcuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_19
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_023
msgid "Pénjamo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_move_line__l10n_mx_edi_qty_umt
msgid "Qty UMT"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_move_line__l10n_mx_edi_qty_umt
msgid ""
"Quantity expressed in the UMT from product. It is used in the attribute "
"'CantidadAduana' in the CFDI"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_115
msgid "Quecholac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_051
msgid "Quechultenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_072
msgid "Queréndaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_que_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_014
msgid "Querétaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_08
msgid "Quila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_116
msgid "Quimixtlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_060
msgid "Quintana Roo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_049
msgid "Quiriego"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_073
msgid "Quiroga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_069
msgid "Quitupan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_135
msgid "Rafael Delgado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_117
msgid "Rafael Lara Grajales"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_136
msgid "Rafael Lucio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_027
msgid "Ramos Arizpe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Rate USD"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_043
msgid "Rayones"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_073
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_072
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_023
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_050
msgid "Rayón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_22
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_074
msgid "Reforma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_075
msgid "Reforma de Pineda"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_077
msgid "Reyes Etla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_032
msgid "Reynosa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_007
msgid "Rincón de Romos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_25
msgid "Rincón de Tamayo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_024
msgid "Rioverde"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_054
msgid "Riva Palacio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_024
msgid "Rodeo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcn_06
msgid "Rodolfo Sánchez T. (Maneadero)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_078
msgid "Rojas de Cuauhtémoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_026
msgid "Romita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_055
msgid "Rosales"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_010
msgid "Rosamorada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_056
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_014
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_051
msgid "Rosario"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_09
msgid "Ruiz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_011
msgid "Ruíz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_30
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_138
msgid "Río Blanco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_033
msgid "Río Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_039
msgid "Río Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_09
msgid "Río Grande o Piedra Parada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_061
msgid "Río Lagartos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_06
msgid "Sabancuy"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_076
msgid "Sabanilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_028
msgid "Sabinas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_044
msgid "Sabinas Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_062
msgid "Sacalum"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_029
msgid "Sacramento"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_052
msgid "Sahuaripa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_076
msgid "Sahuayo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_08
msgid "Sahuayo de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_040
msgid "Sain Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_027
msgid "Salamanca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_079
msgid "Salina Cruz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_025
msgid "Salinas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_045
msgid "Salinas Victoria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_08
msgid "Salinas de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_139
msgid "Saltabarranca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_030
msgid "Saltillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_077
msgid "Salto de Agua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_015
msgid "Salvador Alvarado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_079
msgid "Salvador Escalante"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_30
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_028
msgid "Salvatierra"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_063
msgid "Samahil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_080
msgid "San Agustín Amatengo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_081
msgid "San Agustín Atenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_082
msgid "San Agustín Chayuco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_084
msgid "San Agustín Etla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_085
msgid "San Agustín Loxicha"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_036
msgid "San Agustín Metzquititlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_086
msgid "San Agustín Tlacotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_052
msgid "San Agustín Tlaxiaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_087
msgid "San Agustín Yatareni"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_083
msgid "San Agustín de las Juntas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_088
msgid "San Andrés Cabecera Nueva"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_119
msgid "San Andrés Cholula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_089
msgid "San Andrés Dinicuiti"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_118
msgid "San Andrés Duraznal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_090
msgid "San Andrés Huaxpaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_091
msgid "San Andrés Huayápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_092
msgid "San Andrés Ixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_093
msgid "San Andrés Lagunas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_094
msgid "San Andrés Nuxiño"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_095
msgid "San Andrés Paxtlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_096
msgid "San Andrés Sinaxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_097
msgid "San Andrés Solaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_140
msgid "San Andrés Tenejapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_098
msgid "San Andrés Teotilálpam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_099
msgid "San Andrés Tepetlapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_141
msgid "San Andrés Tuxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_100
msgid "San Andrés Yaá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_101
msgid "San Andrés Zabache"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_102
msgid "San Andrés Zautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_103
msgid "San Antonino Castillo Velasco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_105
msgid "San Antonino Monte Verde"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_104
msgid "San Antonino el Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_026
msgid "San Antonio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_106
msgid "San Antonio Acutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_120
msgid "San Antonio Cañada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_108
msgid "San Antonio Huitepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_109
msgid "San Antonio Nanahuatípam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_110
msgid "San Antonio Sinicahua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_111
msgid "San Antonio Tepetlapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_59
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_107
msgid "San Antonio de la Cal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_073
msgid "San Antonio la Isla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_112
msgid "San Baltazar Chichicápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_113
msgid "San Baltazar Loxicha"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_114
msgid "San Baltazar Yatzachi el Bajo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_115
msgid "San Bartolo Coyotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_121
msgid "San Bartolo Soyaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_053
msgid "San Bartolo Tutotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_122
msgid "San Bartolo Yautepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_116
msgid "San Bartolomé Ayautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_117
msgid "San Bartolomé Loxicha"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_118
msgid "San Bartolomé Quialana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_119
msgid "San Bartolomé Yucuañe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_120
msgid "San Bartolomé Zoogocho"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_025
msgid "San Bernardo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_123
msgid "San Bernardo Mixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_10
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_012
msgid "San Blas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_54
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_124
msgid "San Blas Atempa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_19
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_031
msgid "San Buenaventura"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_034
msgid "San Carlos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_125
msgid "San Carlos Yautepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_027
msgid "San Ciro de Acosta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_126
msgid "San Cristóbal Amatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_127
msgid "San Cristóbal Amoltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_128
msgid "San Cristóbal Lachirioag"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_129
msgid "San Cristóbal Suchixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_071
msgid "San Cristóbal de la Barranca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_078
msgid "San Cristóbal de las Casas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_049
msgid "San Damián Texóloc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_072
msgid "San Diego de Alejandría"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_36
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_029
msgid "San Diego de la Unión"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_121
msgid "San Diego la Mesa Tochimiltzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_026
msgid "San Dimas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_131
msgid "San Dionisio Ocotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_132
msgid "San Dionisio Ocotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_130
msgid "San Dionisio del Mar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_133
msgid "San Esteban Atatlahuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcn_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_030
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_065
msgid "San Felipe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_134
msgid "San Felipe Jalapa de Díaz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_046
msgid "San Felipe Orizatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_135
msgid "San Felipe Tejalápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_122
msgid "San Felipe Teotlalcingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_123
msgid "San Felipe Tepatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_136
msgid "San Felipe Usila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_053
msgid "San Felipe de Jesús"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_074
msgid "San Felipe del Progreso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_079
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_035
msgid "San Fernando"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_137
msgid "San Francisco Cahuacuá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_138
msgid "San Francisco Cajonos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_139
msgid "San Francisco Chapulapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_140
msgid "San Francisco Chindúa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_142
msgid "San Francisco Huehuetlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_53
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_143
msgid "San Francisco Ixhuatán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_144
msgid "San Francisco Jaltepetongo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_145
msgid "San Francisco Lachigoló"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_146
msgid "San Francisco Logueche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_147
msgid "San Francisco Nuxaño"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_148
msgid "San Francisco Ozolotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_149
msgid "San Francisco Sola"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_41
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_150
msgid "San Francisco Telixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_151
msgid "San Francisco Teopan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_050
msgid "San Francisco Tetlanohcan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_152
msgid "San Francisco Tlapancingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_057
msgid "San Francisco de Borja"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_cam_01
msgid "San Francisco de Campeche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_058
msgid "San Francisco de Conchos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_011
msgid "San Francisco de los Romo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_141
msgid "San Francisco del Mar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_059
msgid "San Francisco del Oro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_031
msgid "San Francisco del Rincón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_113
msgid "San Gabriel"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_124
msgid "San Gabriel Chilac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_153
msgid "San Gabriel Mixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_125
msgid "San Gregorio Atzompa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_10
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_22
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_016
msgid "San Ignacio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_48
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_125
msgid "San Ignacio Cerro Gordo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_072
msgid "San Ignacio Río Muerto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_154
msgid "San Ildefonso Amatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_155
msgid "San Ildefonso Sola"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_156
msgid "San Ildefonso Villa Alta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_157
msgid "San Jacinto Amilpas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_158
msgid "San Jacinto Tlacotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_054
msgid "San Javier"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_159
msgid "San Jerónimo Coatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_160
msgid "San Jerónimo Silacayoapilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_161
msgid "San Jerónimo Sosola"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_162
msgid "San Jerónimo Taviche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_126
msgid "San Jerónimo Tecuanipan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_163
msgid "San Jerónimo Tecóatl"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_550
msgid "San Jerónimo Tlacochahuaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_127
msgid "San Jerónimo Xayacatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_051
msgid "San Jerónimo Zacualpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_09
msgid "San Jerónimo de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_015
msgid "San Joaquín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_164
msgid "San Jorge Nuchita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_165
msgid "San José Ayuquila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_128
msgid "San José Chiapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_166
msgid "San José Chiltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_168
msgid "San José Estancia Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_169
msgid "San José Independencia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_32
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_032
msgid "San José Iturbide"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_170
msgid "San José Lachiguiri"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_129
msgid "San José Miahuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_052
msgid "San José Teacalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_171
msgid "San José Tenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_008
msgid "San José de Gracia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_04
msgid "San José del Cabo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_167
msgid "San José del Peñasco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_072
msgid "San José del Progreso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_124
msgid "San José del Rincón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_43
msgid "San José el Verde (El Verde)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_172
msgid "San Juan Achiutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_130
msgid "San Juan Atenco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_173
msgid "San Juan Atepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_131
msgid "San Juan Atzompa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_175
msgid "San Juan Bautista Atatlahuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_176
msgid "San Juan Bautista Coixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_177
msgid "San Juan Bautista Cuicatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_178
msgid "San Juan Bautista Guelache"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_179
msgid "San Juan Bautista Jayacatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_180
msgid "San Juan Bautista Lo de Soto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_181
msgid "San Juan Bautista Suchitepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_183
msgid "San Juan Bautista Tlachichilco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_182
msgid "San Juan Bautista Tlacoatzintepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_184
msgid "San Juan Bautista Tuxtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_47
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_559
msgid "San Juan Bautista Valle Nacional"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_35
msgid "San Juan Bautista lo de Soto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_36
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_185
msgid "San Juan Cacahuatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_112
msgid "San Juan Cancuc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_191
msgid "San Juan Chicomezúchil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_192
msgid "San Juan Chilateca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_186
msgid "San Juan Cieneguilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_187
msgid "San Juan Coatzóspam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_188
msgid "San Juan Colorado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_189
msgid "San Juan Comaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_190
msgid "San Juan Cotzocón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_195
msgid "San Juan Diuxi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_142
msgid "San Juan Evangelista"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_196
msgid "San Juan Evangelista Analco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_197
msgid "San Juan Guelavía"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_198
msgid "San Juan Guichicovi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_053
msgid "San Juan Huactzinco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_199
msgid "San Juan Ihualtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_200
msgid "San Juan Juquila Mixes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_201
msgid "San Juan Juquila Vijanos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_202
msgid "San Juan Lachao"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_203
msgid "San Juan Lachigalla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_204
msgid "San Juan Lajarcia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_205
msgid "San Juan Lalana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_207
msgid "San Juan Mazatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_208
msgid "San Juan Mixtepec -Dto. 08 - "
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_209
msgid "San Juan Mixtepec -Dto. 26 - "
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_211
msgid "San Juan Ozolotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_212
msgid "San Juan Petlapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_213
msgid "San Juan Quiahije"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_214
msgid "San Juan Quiotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_215
msgid "San Juan Sayultepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_216
msgid "San Juan Tabaá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_217
msgid "San Juan Tamazola"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_218
msgid "San Juan Teita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_219
msgid "San Juan Teitipac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_220
msgid "San Juan Tepeuxila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_221
msgid "San Juan Teposcolula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_222
msgid "San Juan Yaeé"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_223
msgid "San Juan Yatzona"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_224
msgid "San Juan Yucuita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_027
msgid "San Juan de Guadalupe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_032
msgid "San Juan de Sabinas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_206
msgid "San Juan de los Cués"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_073
msgid "San Juan de los Lagos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_193
msgid "San Juan del Estado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_que_02
msgid "San Juan del Rio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_028
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_194
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_016
msgid "San Juan del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_06
msgid "San Juan del Río del Centauro del Norte"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_210
msgid "San Juan Ñumí"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_007
msgid "San Juanito de Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_27
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_074
msgid "San Julián"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_225
msgid "San Lorenzo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_226
msgid "San Lorenzo Albarradas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_054
msgid "San Lorenzo Axocomanitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_227
msgid "San Lorenzo Cacaotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_228
msgid "San Lorenzo Cuaunecuiltitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_229
msgid "San Lorenzo Texmelúcan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_230
msgid "San Lorenzo Victoria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_110
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_077
msgid "San Lucas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_231
msgid "San Lucas Camotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_232
msgid "San Lucas Ojitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_233
msgid "San Lucas Quiaviní"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_055
msgid "San Lucas Tecopilco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_234
msgid "San Lucas Zoquiápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_32
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_052
msgid "San Luis Acatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_235
msgid "San Luis Amatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_028
msgid "San Luis Potosí"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_055
msgid "San Luis Río Colorado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_23
msgid "San Luis San Pedro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_20
msgid "San Luis de la Loma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_22
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_033
msgid "San Luis de la Paz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_029
msgid "San Luis del Cordero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_236
msgid "San Marcial Ozolotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_29
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_053
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_075
msgid "San Marcos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_237
msgid "San Marcos Arteaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_029
msgid "San Martín Chalchicuautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_077
msgid "San Martín Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_239
msgid "San Martín Huamelúlpam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_240
msgid "San Martín Itunyoso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_241
msgid "San Martín Lachilá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_242
msgid "San Martín Peras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_132
msgid "San Martín Texmelucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_04
msgid "San Martín Texmelucan de Labastida"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_243
msgid "San Martín Tilcajete"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_133
msgid "San Martín Totoltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_244
msgid "San Martín Toxpalan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_245
msgid "San Martín Zacatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_076
msgid "San Martín de Bolaños"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_075
msgid "San Martín de las Pirámides"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_238
msgid "San Martín de los Cansecos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_32
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_076
msgid "San Mateo Atenco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_246
msgid "San Mateo Cajonos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_250
msgid "San Mateo Etlatongo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_251
msgid "San Mateo Nejápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_252
msgid "San Mateo Peñasco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_253
msgid "San Mateo Piñas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_254
msgid "San Mateo Río Hondo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_255
msgid "San Mateo Sindihui"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_256
msgid "San Mateo Tlapiltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_249
msgid "San Mateo Yoloxochitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_566
msgid "San Mateo Yucutindoo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_248
msgid "San Mateo del Mar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_134
msgid "San Matías Tlalancaleca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_257
msgid "San Melchor Betaza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_258
msgid "San Miguel Achiutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_259
msgid "San Miguel Ahuehuetitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_260
msgid "San Miguel Aloápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_261
msgid "San Miguel Amatitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_262
msgid "San Miguel Amatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_264
msgid "San Miguel Chicahua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_265
msgid "San Miguel Chimalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_263
msgid "San Miguel Coatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_268
msgid "San Miguel Ejutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_270
msgid "San Miguel Huautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_135
msgid "San Miguel Ixitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_271
msgid "San Miguel Mixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_272
msgid "San Miguel Panixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_273
msgid "San Miguel Peras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_274
msgid "San Miguel Piedras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_275
msgid "San Miguel Quetzaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_276
msgid "San Miguel Santa Flor"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_278
msgid "San Miguel Soyaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_279
msgid "San Miguel Suchixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_281
msgid "San Miguel Tecomatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_282
msgid "San Miguel Tenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_283
msgid "San Miguel Tequixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_284
msgid "San Miguel Tilquiápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_285
msgid "San Miguel Tlacamama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_286
msgid "San Miguel Tlacotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_054
msgid "San Miguel Totolapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_287
msgid "San Miguel Tulancingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_136
msgid "San Miguel Xoxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_288
msgid "San Miguel Yotao"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_003
msgid "San Miguel de Allende"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_056
msgid "San Miguel de Horcasitas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_266
msgid "San Miguel del Puerto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_267
msgid "San Miguel del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_30
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_078
msgid "San Miguel el Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_38
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_269
msgid "San Miguel el Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_289
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_036
msgid "San Nicolás"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_137
msgid "San Nicolás Buenos Aires"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_290
msgid "San Nicolás Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_030
msgid "San Nicolás Tolentino"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_046
msgid "San Nicolás de los Garza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_138
msgid "San Nicolás de los Ranchos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_139
msgid "San Pablo Anicano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_291
msgid "San Pablo Coatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_292
msgid "San Pablo Cuatro Venados"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_293
msgid "San Pablo Etla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_40
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_294
msgid "San Pablo Huitzo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_295
msgid "San Pablo Huixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_296
msgid "San Pablo Macuiltianguis"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_297
msgid "San Pablo Tijaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_298
msgid "San Pablo Villa de Mitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_299
msgid "San Pablo Yaganiza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_025
msgid "San Pablo del Monte"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_033
msgid "San Pedro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_300
msgid "San Pedro Amuzgos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_301
msgid "San Pedro Apóstol"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_302
msgid "San Pedro Atoyac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_303
msgid "San Pedro Cajonos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_140
msgid "San Pedro Cholula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_305
msgid "San Pedro Comitancillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_304
msgid "San Pedro Coxcaltepec Cántaros"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_019
msgid "San Pedro Garza García"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_307
msgid "San Pedro Huamelula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_308
msgid "San Pedro Huilotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_309
msgid "San Pedro Ixcatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_310
msgid "San Pedro Ixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_311
msgid "San Pedro Jaltepetongo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_312
msgid "San Pedro Jicayán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_313
msgid "San Pedro Jocotipac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_314
msgid "San Pedro Juchatengo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_013
msgid "San Pedro Lagunillas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_318
msgid "San Pedro Mixtepec -Dto. 22 - "
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_28
msgid "San Pedro Mixtepec -Dto. 22-"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_319
msgid "San Pedro Mixtepec -Dto. 26 - "
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_320
msgid "San Pedro Molinos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_315
msgid "San Pedro Mártir"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_316
msgid "San Pedro Mártir Quiechapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_317
msgid "San Pedro Mártir Yucuxaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_321
msgid "San Pedro Nopala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_322
msgid "San Pedro Ocopetatillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_323
msgid "San Pedro Ocotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_45
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_324
msgid "San Pedro Pochutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_325
msgid "San Pedro Quiatoni"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_326
msgid "San Pedro Sochiápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_32
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_327
msgid "San Pedro Tapanatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_328
msgid "San Pedro Taviche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_329
msgid "San Pedro Teozacoalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_330
msgid "San Pedro Teutila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_331
msgid "San Pedro Tidaá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_098
msgid "San Pedro Tlaquepaque"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_332
msgid "San Pedro Topiltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_37
msgid "San Pedro Totolapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_333
msgid "San Pedro Totolápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_335
msgid "San Pedro Yaneri"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_141
msgid "San Pedro Yeloixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_341
msgid "San Pedro Yucunama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_336
msgid "San Pedro Yólox"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_057
msgid "San Pedro de la Cueva"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_030
msgid "San Pedro del Gallo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_306
msgid "San Pedro el Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_337
msgid "San Pedro y San Pablo Ayutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_339
msgid "San Pedro y San Pablo Teposcolula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_340
msgid "San Pedro y San Pablo Tequixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_51
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_211
msgid "San Rafael"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_342
msgid "San Raymundo Jalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_054
msgid "San Salvador"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_144
msgid "San Salvador Huixcolotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_142
msgid "San Salvador el Seco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_143
msgid "San Salvador el Verde"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_343
msgid "San Sebastián Abasolo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_344
msgid "San Sebastián Coatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_345
msgid "San Sebastián Ixcapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_346
msgid "San Sebastián Nicananduta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_347
msgid "San Sebastián Río Hondo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_51
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_348
msgid "San Sebastián Tecomaxtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_349
msgid "San Sebastián Teitipac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_145
msgid "San Sebastián Tlacotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_350
msgid "San Sebastián Tutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_080
msgid "San Sebastián del Oeste"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_351
msgid "San Simón Almolongas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_352
msgid "San Simón Zahuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_077
msgid "San Simón de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_27
msgid "San Vicente Chicoloapan de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_534
msgid "San Vicente Coatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_535
msgid "San Vicente Lachixío"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_536
msgid "San Vicente Nuñú"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_034
msgid "San Vicente Tancuayalab"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_02
msgid "San miguel de Allende"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_15
msgid "San pedro Lagunillas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_064
msgid "Sanahcat"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_020
msgid "Sanctórum de Lázaro Cárdenas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_353
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_058
msgid "Santa Ana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_354
msgid "Santa Ana Ateixtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_355
msgid "Santa Ana Cuauhtémoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_078
msgid "Santa Ana Maya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_056
msgid "Santa Ana Nopalucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_357
msgid "Santa Ana Tavela"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_358
msgid "Santa Ana Tlapacoyan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_359
msgid "Santa Ana Yareni"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_360
msgid "Santa Ana Zegache"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_356
msgid "Santa Ana del Valle"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_057
msgid "Santa Apolonia Teacalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_060
msgid "Santa Bárbara"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_361
msgid "Santa Catalina Quierí"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_034
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_048
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_031
msgid "Santa Catarina"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_058
msgid "Santa Catarina Ayometla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_362
msgid "Santa Catarina Cuixtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_363
msgid "Santa Catarina Ixtepeji"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_364
msgid "Santa Catarina Juquila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_365
msgid "Santa Catarina Lachatao"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_366
msgid "Santa Catarina Loxicha"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_367
msgid "Santa Catarina Mechoacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_368
msgid "Santa Catarina Minas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_369
msgid "Santa Catarina Quiané"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_074
msgid "Santa Catarina Quioquitani"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_370
msgid "Santa Catarina Tayata"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_371
msgid "Santa Catarina Ticuá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_146
msgid "Santa Catarina Tlaltempan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_372
msgid "Santa Catarina Yosonotú"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_373
msgid "Santa Catarina Zapoquila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_031
msgid "Santa Clara"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_059
msgid "Santa Cruz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_374
msgid "Santa Cruz Acatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_375
msgid "Santa Cruz Amilpas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_29
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_377
msgid "Santa Cruz Itundujia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_37
msgid "Santa Cruz Juventino Rosas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_378
msgid "Santa Cruz Mixtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_379
msgid "Santa Cruz Nundaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_380
msgid "Santa Cruz Papalutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_059
msgid "Santa Cruz Quilehtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_381
msgid "Santa Cruz Tacache de Mina"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_382
msgid "Santa Cruz Tacahua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_383
msgid "Santa Cruz Tayata"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_026
msgid "Santa Cruz Tlaxcala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_384
msgid "Santa Cruz Xitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_385
msgid "Santa Cruz Xoxocotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_386
msgid "Santa Cruz Zenzontepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_376
msgid "Santa Cruz de Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_035
msgid "Santa Cruz de Juventino Rosas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_066
msgid "Santa Elena"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_387
msgid "Santa Gertrudis"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_147
msgid "Santa Inés Ahuatempan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_389
msgid "Santa Inés Yatzeche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_569
msgid "Santa Inés de Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_388
msgid "Santa Inés del Monte"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_024
msgid "Santa Isabel"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_148
msgid "Santa Isabel Cholula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_060
msgid "Santa Isabel Xiloxoxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_58
msgid "Santa Lucia del Camino"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_391
msgid "Santa Lucía Miahuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_392
msgid "Santa Lucía Monteverde"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_393
msgid "Santa Lucía Ocotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_390
msgid "Santa Lucía del Camino"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_047
msgid "Santa Magdalena Jicotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_15
msgid "Santa Maria Tultepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_394
msgid "Santa María Alotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_395
msgid "Santa María Apazco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_399
msgid "Santa María Atzompa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_400
msgid "Santa María Camotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_404
msgid "Santa María Chachoápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_406
msgid "Santa María Chilchotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_407
msgid "Santa María Chimalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_401
msgid "Santa María Colotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_402
msgid "Santa María Cortijo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_403
msgid "Santa María Coyotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_410
msgid "Santa María Ecatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_411
msgid "Santa María Guelacé"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_412
msgid "Santa María Guienagati"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_20
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_413
msgid "Santa María Huatulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_414
msgid "Santa María Huazolotitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_415
msgid "Santa María Ipalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_416
msgid "Santa María Ixcatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_417
msgid "Santa María Jacatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_418
msgid "Santa María Jalapa del Marqués"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_419
msgid "Santa María Jaltianguis"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_420
msgid "Santa María Lachixío"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_421
msgid "Santa María Mixtequilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_422
msgid "Santa María Nativitas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_423
msgid "Santa María Nduayaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_424
msgid "Santa María Ozolotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_427
msgid "Santa María Petapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_426
msgid "Santa María Peñoles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_425
msgid "Santa María Pápalo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_428
msgid "Santa María Quiegolani"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_429
msgid "Santa María Sola"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_430
msgid "Santa María Tataltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_431
msgid "Santa María Tecomavaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_432
msgid "Santa María Temaxcalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_433
msgid "Santa María Temaxcaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_434
msgid "Santa María Teopoxco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_435
msgid "Santa María Tepantlali"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_436
msgid "Santa María Texcatitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_437
msgid "Santa María Tlahuitoltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_438
msgid "Santa María Tlalixtac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_439
msgid "Santa María Tonameca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_440
msgid "Santa María Totolapilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_441
msgid "Santa María Xadani"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_442
msgid "Santa María Yalina"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_443
msgid "Santa María Yavesía"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_444
msgid "Santa María Yolotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_445
msgid "Santa María Yosoyúa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_446
msgid "Santa María Yucuhiti"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_447
msgid "Santa María Zacatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_448
msgid "Santa María Zaniza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_449
msgid "Santa María Zoquitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_058
msgid "Santa María de la Paz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_081
msgid "Santa María de los Ángeles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_056
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_014
msgid "Santa María del Oro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_408
msgid "Santa María del Rosario"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_19
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_032
msgid "Santa María del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_409
msgid "Santa María del Tule"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_396
msgid "Santa María la Asunción"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mor_06
msgid "Santa Rosa Treinta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_12
msgid "Santa Rosalía"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_01
msgid "Santa Rosalía de Camargo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nle_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_049
msgid "Santiago"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_450
msgid "Santiago Amoltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_451
msgid "Santiago Apoala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_452
msgid "Santiago Apóstol"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_453
msgid "Santiago Astata"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_454
msgid "Santiago Atitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_455
msgid "Santiago Ayuquililla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_456
msgid "Santiago Cacaloxtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_457
msgid "Santiago Camotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_459
msgid "Santiago Chazumba"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_460
msgid "Santiago Choápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_458
msgid "Santiago Comaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_462
msgid "Santiago Huajolotitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_463
msgid "Santiago Huauclilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_464
msgid "Santiago Ihuitlán Plumas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_465
msgid "Santiago Ixcuintepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_01
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_015
msgid "Santiago Ixcuintla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_466
msgid "Santiago Ixtayutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_44
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_467
msgid "Santiago Jamiltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_468
msgid "Santiago Jocotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_50
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_469
msgid "Santiago Juxtlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_470
msgid "Santiago Lachiguiri"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_471
msgid "Santiago Lalopa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_472
msgid "Santiago Laollaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_473
msgid "Santiago Laxopa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_474
msgid "Santiago Llano Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_036
msgid "Santiago Maravatío"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_475
msgid "Santiago Matatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_149
msgid "Santiago Miahuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_476
msgid "Santiago Miltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_477
msgid "Santiago Minas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_478
msgid "Santiago Nacaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_479
msgid "Santiago Nejapilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_066
msgid "Santiago Niltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_480
msgid "Santiago Nundiche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_481
msgid "Santiago Nuyoó"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_032
msgid "Santiago Papasquiaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_43
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_482
msgid "Santiago Pinotepa Nacional"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_212
msgid "Santiago Sochiapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_483
msgid "Santiago Suchilquitongo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_484
msgid "Santiago Tamazola"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_485
msgid "Santiago Tapextla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_487
msgid "Santiago Tenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_488
msgid "Santiago Tepetlapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_489
msgid "Santiago Tetepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_490
msgid "Santiago Texcalcingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_491
msgid "Santiago Textitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_492
msgid "Santiago Tilantongo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_493
msgid "Santiago Tillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_494
msgid "Santiago Tlazoyaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_11
msgid "Santiago Tulantepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_056
msgid "Santiago Tulantepec de Lugo Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_42
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_143
msgid "Santiago Tuxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_495
msgid "Santiago Xanica"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_496
msgid "Santiago Xiacuí"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_497
msgid "Santiago Yaitepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_498
msgid "Santiago Yaveo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_499
msgid "Santiago Yolomécatl"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_500
msgid "Santiago Yosondúa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_501
msgid "Santiago Yucuyachi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_502
msgid "Santiago Zacatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_503
msgid "Santiago Zoochila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_055
msgid "Santiago de Anaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_461
msgid "Santiago del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_119
msgid "Santiago el Pinar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_033
msgid "Santo Domingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_506
msgid "Santo Domingo Albarradas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_507
msgid "Santo Domingo Armenta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_508
msgid "Santo Domingo Chihuitán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_505
msgid "Santo Domingo Ingenio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_510
msgid "Santo Domingo Ixcatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_511
msgid "Santo Domingo Nuxaá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_512
msgid "Santo Domingo Ozolotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_513
msgid "Santo Domingo Petapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_514
msgid "Santo Domingo Roayaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_55
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_515
msgid "Santo Domingo Tehuantepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_516
msgid "Santo Domingo Teojomulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_517
msgid "Santo Domingo Tepuxtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_518
msgid "Santo Domingo Tlatayápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_519
msgid "Santo Domingo Tomaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_521
msgid "Santo Domingo Tonaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_520
msgid "Santo Domingo Tonalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_522
msgid "Santo Domingo Xagacía"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_523
msgid "Santo Domingo Yanhuitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_524
msgid "Santo Domingo Yodohino"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_525
msgid "Santo Domingo Zanatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_509
msgid "Santo Domingo de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_078
msgid "Santo Tomás"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_151
msgid "Santo Tomás Hueyotlipan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_530
msgid "Santo Tomás Jalieza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_531
msgid "Santo Tomás Mazaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_532
msgid "Santo Tomás Ocotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_533
msgid "Santo Tomás Tamazulapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_526
msgid "Santos Reyes Nopala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_527
msgid "Santos Reyes Pápalo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_528
msgid "Santos Reyes Tepejillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_529
msgid "Santos Reyes Yucuná"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_061
msgid "Satevó"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chh_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_062
msgid "Saucillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_23
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_082
msgid "Sayula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_144
msgid "Sayula de Alemán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_080
msgid "Senguio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_067
msgid "Seyé"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_034
msgid "Sierra Mojada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_66
msgid "Sihuapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_537
msgid "Silacayoápam"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_11
msgid "Silao"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_037
msgid "Silao de la Victoria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_080
msgid "Siltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_081
msgid "Simojovel"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_sin_017
msgid "Sinaloa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_13
msgid "Sinaloa de Leyva"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_068
msgid "Sinanché"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_057
msgid "Singuilucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_082
msgid "Sitalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_538
msgid "Sitio de Xitlapehua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_146
msgid "Sochiapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_083
msgid "Socoltenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_145
msgid "Soconusco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_147
msgid "Soledad Atzompa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_539
msgid "Soledad Etla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_60
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_148
msgid "Soledad de Doblado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_06
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_035
msgid "Soledad de Graciano Sánchez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_008
msgid "Solidaridad"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_084
msgid "Solosuchiapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_152
msgid "Soltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_042
msgid "Sombrerete"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_son_13
msgid "Sonoita"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_149
msgid "Soteapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_037
msgid "Soto la Marina"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_069
msgid "Sotuta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_085
msgid "Soyaló"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_079
msgid "Soyaniquilpan de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_061
msgid "Soyopa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_res_locality__state_id
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "State"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "Street"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "Street 2..."
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "Street..."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_062
msgid "Suaqui Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Subdivision"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_086
msgid "Suchiapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_087
msgid "Suchiate"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_070
msgid "Sucilá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_071
msgid "Sudzal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_080
msgid "Sultepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_072
msgid "Suma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_088
msgid "Sunuapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_043
msgid "Susticacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_081
msgid "Susupuato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_060
msgid "Sáric"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_033
msgid "Súchil"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_044
msgid "Tabasco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_015
msgid "Tacotalpa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_082
msgid "Tacámbaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_20
msgid "Tacámbaro de Codallos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_073
msgid "Tahdziú"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_074
msgid "Tahmek"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_31
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_083
msgid "Tala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_084
msgid "Talpa de Allende"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_150
msgid "Tamalín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_036
msgid "Tamasopo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_034
msgid "Tamazula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_54
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_085
msgid "Tamazula de Gordiano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_031
msgid "Tamazulápam del Espíritu Santo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_037
msgid "Tamazunchale"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_151
msgid "Tamiahua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_038
msgid "Tampacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_039
msgid "Tampamolón Corona"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_038
msgid "Tampico"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_22
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_152
msgid "Tampico Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_11
msgid "Tamuin"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_040
msgid "Tamuín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_012
msgid "Tancanhuitz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_153
msgid "Tancoco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_083
msgid "Tancítaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_541
msgid "Tanetze de Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_084
msgid "Tangamandapio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_085
msgid "Tangancícuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_14
msgid "Tangancícuaro de Arista"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_086
msgid "Tanhuato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_542
msgid "Taniche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_041
msgid "Tanlajás"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_042
msgid "Tanquián de Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_154
msgid "Tantima"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_24
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_155
msgid "Tantoyuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_089
msgid "Tapachula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_03
msgid "Tapachula de Córdova y Ordóñez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_090
msgid "Tapalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_086
msgid "Tapalpa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_091
msgid "Tapilula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_038
msgid "Tarandacuao"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_087
msgid "Taretan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_product_product__l10n_mx_edi_tariff_fraction_id
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_product_template__l10n_mx_edi_tariff_fraction_id
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.l10n_mx_edi_tariff_fraction_tree_view
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Tariff Fraction"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_039
msgid "Tarimoro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_088
msgid "Tarímbaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_058
msgid "Tasquillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_209
msgid "Tatahuicapan de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_543
msgid "Tataltepec de Valdés"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_156
msgid "Tatatila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_055
msgid "Taxco de Alarcón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_075
msgid "Teabo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_016
msgid "Teapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_153
msgid "Tecali de Herrera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_45
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_087
msgid "Tecalitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_12
msgid "Tecamac de Felipe Villanueva"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_154
msgid "Tecamachalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcn_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcn_003
msgid "Tecate"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_089
msgid "Techaluta de Montenegro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_056
msgid "Tecoanapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_076
msgid "Tecoh"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_088
msgid "Tecolotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_158
msgid "Tecolutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_col_03
msgid "Tecoman"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_155
msgid "Tecomatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_009
msgid "Tecomán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_059
msgid "Tecozautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_092
msgid "Tecpatán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_016
msgid "Tecuala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_081
msgid "Tecámac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_05
msgid "Tehuacan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_156
msgid "Tehuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_159
msgid "Tehuipango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_157
msgid "Tehuitzingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_082
msgid "Tejupilco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_22
msgid "Tejupilco de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_077
msgid "Tekal de Venegas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_078
msgid "Tekantó"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_079
msgid "Tekax"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_080
msgid "Tekit"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_081
msgid "Tekom"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_082
msgid "Telchac Pueblo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_083
msgid "Telchac Puerto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_24
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_058
msgid "Teloloapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_083
msgid "Temamatla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_084
msgid "Temascalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_085
msgid "Temascalcingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_086
msgid "Temascaltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_084
msgid "Temax"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_018
msgid "Temixco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_033
msgid "Temoac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_087
msgid "Temoaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_085
msgid "Temozón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_161
msgid "Tempoal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_23
msgid "Tempoal de Sánchez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields.selection,name:l10n_mx_edi_extended.selection__account_move__l10n_mx_edi_external_trade_type__03
#: model:ir.model.fields.selection,name:l10n_mx_edi_extended.selection__res_partner__l10n_mx_edi_external_trade_type__03
msgid "Temporary"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_063
msgid "Temósachic"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cam_008
msgid "Tenabo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_090
msgid "Tenamaxtlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_162
msgid "Tenampa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_158
msgid "Tenampulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_088
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_027
msgid "Tenancingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_060
msgid "Tenango de Doria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_089
msgid "Tenango del Aire"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_090
msgid "Tenango del Valle"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_093
msgid "Tenejapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_163
msgid "Tenochtitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tab_017
msgid "Tenosique"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_08
msgid "Tenosique de Pino Suárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_39
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_091
msgid "Teocaltiche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_164
msgid "Teocelo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_544
msgid "Teococuilco de Marcos Pérez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_092
msgid "Teocuitatlán de Corona"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_028
msgid "Teolocholco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_091
msgid "Teoloyucan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_159
msgid "Teopantlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_094
msgid "Teopisca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_092
msgid "Teotihuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_19
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_545
msgid "Teotitlán de Flores Magón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_546
msgid "Teotitlán del Valle"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_160
msgid "Teotlalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_547
msgid "Teotongo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_063
msgid "Tepache"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_086
msgid "Tepakán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_089
msgid "Tepalcatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_019
msgid "Tepalcingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_161
msgid "Tepanco de López"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_162
msgid "Tepango de Rodríguez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_093
msgid "Tepatitlán de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_165
msgid "Tepatlaxco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_163
msgid "Tepatlaxco de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_164
msgid "Tepeaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_061
msgid "Tepeapulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_045
msgid "Tepechitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_28
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_059
msgid "Tepecoacuilco de Trujano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_062
msgid "Tepehuacán de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_035
msgid "Tepehuanes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_13
msgid "Tepeji del Rio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_063
msgid "Tepeji del Río de Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_548
msgid "Tepelmeme Villa de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_165
msgid "Tepemaxalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_166
msgid "Tepeojuma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_019
msgid "Tepetitla de Lardizábal"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_064
msgid "Tepetitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_093
msgid "Tepetlaoxtoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_094
msgid "Tepetlixpa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_166
msgid "Tepetlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_046
msgid "Tepetongo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_167
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_167
msgid "Tepetzintla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_168
msgid "Tepexco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_169
msgid "Tepexi de Rodríguez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_170
msgid "Tepeyahualco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_171
msgid "Tepeyahualco de Cuauhtémoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_029
msgid "Tepeyanco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_agu_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_agu_009
msgid "Tepezalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_017
msgid "Tepic"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_095
msgid "Tepotzotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_020
msgid "Tepoztlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_41
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_094
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_168
msgid "Tequila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_017
msgid "Tequisquiapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_30
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_096
msgid "Tequixquiac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_030
msgid "Terrenate"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_021
msgid "Tetecala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_172
msgid "Tetela de Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_022
msgid "Tetela del Volcán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_173
msgid "Teteles de Avila Castillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_065
msgid "Tetepango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_060
msgid "Tetipac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_087
msgid "Tetiz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_031
msgid "Tetla de la Solidaridad"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_032
msgid "Tetlatlahuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_095
msgid "Teuchitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_097
msgid "Texcaltitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_098
msgid "Texcalyacac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_170
msgid "Texcatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_099
msgid "Texcoco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_19
msgid "Texcoco de Mora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_171
msgid "Texhuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_172
msgid "Texistepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_088
msgid "Teya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_06
msgid "Teziutlan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_174
msgid "Teziutlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_549
msgid "Tezoatlán de Segura y Luna"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_62
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_173
msgid "Tezonapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_067
msgid "Tezontepec de Aldama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_100
msgid "Tezoyuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_047
msgid "Teúl de González Ortega"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.res_config_settings_view_form_inherit_l10n_mx_edi_extended
msgid "The parameters to configure the External Trade complement."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_175
msgid "Tianguismanalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_101
msgid "Tianguistenco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_068
msgid "Tianguistengo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_yuc_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_089
msgid "Ticul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_040
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_174
msgid "Tierra Blanca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_15
msgid "Tierra Colorada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_043
msgid "Tierra Nueva"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_55
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_175
msgid "Tihuatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcn_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_bcn_004
msgid "Tijuana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_096
msgid "Tila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_176
msgid "Tilapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_102
msgid "Timilpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_090
msgid "Timucuy"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_090
msgid "Tingambato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_091
msgid "Tingüindín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_091
msgid "Tinum"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_092
msgid "Tiquicheo de Nicolás Romero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_092
msgid "Tixcacalcupul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_093
msgid "Tixkokob"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_094
msgid "Tixmehuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_095
msgid "Tixpéhual"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_27
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_061
msgid "Tixtla de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_096
msgid "Tizapán el Alto"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_069
msgid "Tizayuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_yuc_02
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_096
msgid "Tizimín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_180
msgid "Tlachichilco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_179
msgid "Tlachichuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_062
msgid "Tlacoachistlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_063
msgid "Tlacoapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_52
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_176
msgid "Tlacojalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_551
msgid "Tlacolula de Matamoros"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_177
msgid "Tlacolulan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_178
msgid "Tlacotalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_552
msgid "Tlacotepec Plumas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_177
msgid "Tlacotepec de Benito Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_179
msgid "Tlacotepec de Mejía"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_178
msgid "Tlacuilotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_036
msgid "Tlahualilo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_180
msgid "Tlahuapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_070
msgid "Tlahuelilpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_071
msgid "Tlahuiltepa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_097
msgid "Tlajomulco de Zúñiga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_064
msgid "Tlalchapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_181
msgid "Tlalixcoyan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_553
msgid "Tlalixtac de Cabrera"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_35
msgid "Tlalixtaquilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_065
msgid "Tlalixtaquilla de Maldonado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_103
msgid "Tlalmanalco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_182
msgid "Tlalnelhuayocan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_023
msgid "Tlalnepantla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_104
msgid "Tlalnepantla de Baz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_012
msgid "Tlalpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_093
msgid "Tlalpujahua"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_181
msgid "Tlaltenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_048
msgid "Tlaltenango de Sánchez Román"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_024
msgid "Tlaltetela"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_024
msgid "Tlaltizapán de Zapata"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_072
msgid "Tlanalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_073
msgid "Tlanchinol"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_182
msgid "Tlanepantla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_183
msgid "Tlaola"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_34
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_066
msgid "Tlapa de Comonfort"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_184
msgid "Tlapacoya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_69
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_183
msgid "Tlapacoyan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_185
msgid "Tlapanalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_31
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_067
msgid "Tlapehuala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_09
msgid "Tlaquepaque"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_184
msgid "Tlaquilpa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mor_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_025
msgid "Tlaquiltenango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_186
msgid "Tlatlauquitepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_105
msgid "Tlatlaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_033
msgid "Tlaxcala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tla_03
msgid "Tlaxcala (Tlaxcala de Xicotencatl)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_187
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_034
msgid "Tlaxco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_074
msgid "Tlaxcoapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_026
msgid "Tlayacapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_094
msgid "Tlazazalca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_185
msgid "Tlilapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_011
msgid "Tláhuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_035
msgid "Tocatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_188
msgid "Tochimilco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_189
msgid "Tochtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_095
msgid "Tocumbo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_07
msgid "Todos Santos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_075
msgid "Tolcayuca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_099
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_que_018
msgid "Tolimán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_106
msgid "Toluca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_20
msgid "Toluca de Lerdo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_100
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_186
msgid "Tomatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_12
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_097
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_101
msgid "Tonalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_125
msgid "Tonanitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_107
msgid "Tonatico"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_102
msgid "Tonaya"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_187
msgid "Tonayán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_103
msgid "Tonila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_037
msgid "Topia"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_23
msgid "Topolobampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_11
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_035
msgid "Torreón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.report_invoice_document
msgid "Total USD"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_104
msgid "Totatiche"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_036
msgid "Totolac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_098
msgid "Totolapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_027
msgid "Totolapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_190
msgid "Totoltepec de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_554
msgid "Totontepec Villa de Morelos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_105
msgid "Tototlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_188
msgid "Totutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_057
msgid "Trancoso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_207
msgid "Tres Valles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_064
msgid "Trincheras"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_011
msgid "Trinidad García de la Cadena"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_555
msgid "Trinidad Zaachila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_065
msgid "Tubutama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_039
msgid "Tula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_076
msgid "Tula de Allende"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_06
msgid "Tulancingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_077
msgid "Tulancingo de Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_191
msgid "Tulcingo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_108
msgid "Tultepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_109
msgid "Tultitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_16
msgid "Tultitlán de Mariano Escobedo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_roo_009
msgid "Tulum"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_100
msgid "Tumbalá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_096
msgid "Tumbiscatío"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_097
msgid "Tunkás"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_097
msgid "Turicato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_106
msgid "Tuxcacuesco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_107
msgid "Tuxcueca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_13
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_24
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_108
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_098
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_018
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_189
msgid "Tuxpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_190
msgid "Tuxtilla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_102
msgid "Tuxtla Chico"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_04
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_101
msgid "Tuxtla Gutiérrez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_192
msgid "Tuzamapan de Galeana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_099
msgid "Tuzantla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_103
msgid "Tuzantán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_193
msgid "Tzicatlacoyan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_104
msgid "Tzimol"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_100
msgid "Tzintzuntzan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_101
msgid "Tzitzio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_038
msgid "Tzompantepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_098
msgid "Tzucacab"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_25
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_057
msgid "Técpan de Galeana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_16
msgid "Túxpam de Rodríguez Cano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_move_line__l10n_mx_edi_umt_aduana_id
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_product_product__l10n_mx_edi_umt_aduana_id
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_product_template__l10n_mx_edi_umt_aduana_id
msgid "UMT Aduana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_099
msgid "Uayma"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_100
msgid "Ucú"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_101
msgid "Umán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_account_move_line__l10n_mx_edi_price_unit_umt
msgid "Unit Value UMT"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_move_line__l10n_mx_edi_price_unit_umt
msgid ""
"Unit value expressed in the UMT from product. It is used in the attribute "
"'ValorUnitarioAduana' in the CFDI"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_26
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_557
msgid "Unión Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_105
msgid "Unión Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_52
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_109
msgid "Unión de San Antonio"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_110
msgid "Unión de Tula"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__uom_code
msgid ""
"UoM code related with this tariff fraction. This value is defined in the SAT"
" catalog and will be assigned in the attribute 'UnidadAduana' in the "
"merchandise."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,field_description:l10n_mx_edi_extended.field_l10n_mx_edi_tariff_fraction__uom_code
msgid "Uom Code"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_066
msgid "Ures"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_041
msgid "Uriangato"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_065
msgid "Urique"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_191
msgid "Ursulo Galván"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_066
msgid "Uruachi"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_102
msgid "Uruapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_move_line__l10n_mx_edi_umt_aduana_id
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_product_product__l10n_mx_edi_umt_aduana_id
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_product_template__l10n_mx_edi_umt_aduana_id
msgid ""
"Used in complement 'Comercio Exterior' to indicate in the products the TIGIE"
" Units of Measurement. It is based in the SAT catalog."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_account_journal__l10n_mx_address_issued_id
msgid ""
"Used in multiple-offices environments to fill, with the given address, the "
"node 'ExpedidoEn' in the XML for invoices of this journal. If empty, the "
"node won't be added."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_uom_uom__l10n_mx_edi_code_aduana
msgid ""
"Used in the complement of 'Comercio Exterior' to indicate in the products "
"the UoM. It is based in the SAT catalog."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_210
msgid "Uxpanapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_558
msgid "Valerio Trujano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_yuc_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_102
msgid "Valladolid"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_040
msgid "Valle Hermoso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_110
msgid "Valle de Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_122
msgid "Valle de Chalco Solidaridad"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_56
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_111
msgid "Valle de Guadalupe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_112
msgid "Valle de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_23
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_042
msgid "Valle de Santiago"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chh_067
msgid "Valle de Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_050
msgid "Vallecillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_13
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_049
msgid "Valparaíso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_044
msgid "Vanegas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_192
msgid "Vega de Alatorre"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_045
msgid "Venado"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_05
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_106
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_017
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_103
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_194
msgid "Venustiano Carranza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_17
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_193
msgid "Veracruz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_050
msgid "Vetagrande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_33
msgid "Vicente Camalote"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_dur_038
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_195
msgid "Vicente Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_043
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_041
msgid "Victoria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_dur_01
msgid "Victoria de Durango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_21
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_036
msgid "Viesca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_bcs_09
msgid "Villa Alberto Andrés Alvarado Arámburo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_194
msgid "Villa Aldama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_071
msgid "Villa Comaltitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_38
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_114
msgid "Villa Corona"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_107
msgid "Villa Corzo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_560
msgid "Villa Díaz Ordaz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_052
msgid "Villa García"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_053
msgid "Villa González Ortega"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_115
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_113
msgid "Villa Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_51
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_08
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_116
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_038
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_051
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_067
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_054
msgid "Villa Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_08
msgid "Villa Hidalgo (El Nuevo)"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_052
msgid "Villa Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_11
msgid "Villa Nicolás Romero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_068
msgid "Villa Pesqueira"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_068
msgid "Villa Purificación"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_22
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_277
msgid "Villa Sola de Vega"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_280
msgid "Villa Talea de Castro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_486
msgid "Villa Tejúpam de la Unión"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_sin_12
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_037
msgid "Villa Unión"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tla_02
msgid "Villa Vicente Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_114
msgid "Villa Victoria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_111
msgid "Villa de Allende"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_056
msgid "Villa de Arista"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_046
msgid "Villa de Arriaga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_405
msgid "Villa de Chilapa de Díaz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_051
msgid "Villa de Cos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_338
msgid "Villa de Etla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_047
msgid "Villa de Guadalupe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_049
msgid "Villa de Ramos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_050
msgid "Villa de Reyes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_34
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_540
msgid "Villa de Tamazulápam del Progreso"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_066
msgid "Villa de Tezontepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_334
msgid "Villa de Tututepec de Melchor Ocampo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_24
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_565
msgid "Villa de Zaachila"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_048
msgid "Villa de la Paz"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_col_010
msgid "Villa de Álvarez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_112
msgid "Villa del Carbón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_chp_07
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_108
msgid "Villaflores"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_26
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_044
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_042
msgid "Villagrán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tab_02
msgid "Villahermosa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nle_051
msgid "Villaldama"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_104
msgid "Villamar"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_09
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_055
msgid "Villanueva"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_105
msgid "Vista Hermosa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_12
msgid "Víctor Rosales"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_087
msgid "Xalapa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_10
msgid "Xalapa-Enríquez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_043
msgid "Xalatlaco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_nay_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_nay_008
msgid "Xalisco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_039
msgid "Xaloztoc"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_069
msgid "Xalpatláhuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_040
msgid "Xaltocan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_196
msgid "Xayacatlán de Bravo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_045
msgid "Xichú"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_092
msgid "Xico"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_042
msgid "Xicohtzinco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_15
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_197
msgid "Xicotepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_198
msgid "Xicotlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_tam_16
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tam_043
msgid "Xicoténcatl"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_054
msgid "Xilitla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_199
msgid "Xiutetelco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_103
msgid "Xocchel"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_200
msgid "Xochiapulco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_078
msgid "Xochiatipan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_079
msgid "Xochicoatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_070
msgid "Xochihuehuetlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_201
msgid "Xochiltepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_013
msgid "Xochimilco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_071
msgid "Xochistlahuaca"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_028
msgid "Xochitepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_203
msgid "Xochitlán Todos Santos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_202
msgid "Xochitlán de Vicente Suárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_31
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_115
msgid "Xonacatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_195
msgid "Xoxocotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_080
msgid "Yahualica"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_34
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_118
msgid "Yahualica de González Gallo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_109
msgid "Yajalón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_196
msgid "Yanga"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_204
msgid "Yaonáhuac"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_043
msgid "Yauhquemehcan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_029
msgid "Yautepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_104
msgid "Yaxcabá"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_561
msgid "Yaxe"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_105
msgid "Yaxkukul"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_030
msgid "Yecapixtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_59
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_197
msgid "Yecuatla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_205
msgid "Yehualtepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_yuc_106
msgid "Yobaín"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_563
msgid "Yogana"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gua_27
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gua_046
msgid "Yuriria"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_106
msgid "Yurécuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_564
msgid "Yutanduchi de Guerrero"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_son_069
msgid "Yécora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model_terms:ir.ui.view,arch_db:l10n_mx_edi_extended.mx_partner_address_form
msgid "ZIP"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_206
msgid "Zacapala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_207
msgid "Zacapoaxtla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_107
msgid "Zacapu"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_zac_03
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_zac_056
msgid "Zacatecas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_044
msgid "Zacatelco"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_031
msgid "Zacatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mor_07
msgid "Zacatepec de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_pue_14
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_208
msgid "Zacatlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_116
msgid "Zacazonapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_49
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_119
msgid "Zacoalco de Torres"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_117
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mor_032
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_198
msgid "Zacualpan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_081
msgid "Zacualtipán de Ángeles"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_108
msgid "Zamora"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_11
msgid "Zamora de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_10
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_120
msgid "Zapopan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_jal_37
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_121
msgid "Zapotiltic"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_209
msgid "Zapotitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_567
msgid "Zapotitlán Lagunas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_568
msgid "Zapotitlán Palmas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_072
msgid "Zapotitlán Tablas"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_210
msgid "Zapotitlán de Méndez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_122
msgid "Zapotitlán de Vadillo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_124
msgid "Zapotlanejo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_082
msgid "Zapotlán de Juárez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_123
msgid "Zapotlán del Rey"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_jal_023
msgid "Zapotlán el Grande"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_coa_18
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_coa_038
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_211
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_slp_055
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_199
msgid "Zaragoza"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_212
msgid "Zautla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_083
msgid "Zempoala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_200
msgid "Zentla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_41
msgid "Zihuatanejo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_038
msgid "Zihuatanejo de Azueta"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_213
msgid "Zihuateutla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_tla_037
msgid "Ziltlaltépec de Trinidad Sánchez Santos"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_hid_07
msgid "Zimapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_hid_084
msgid "Zimapán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_oax_39
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_570
msgid "Zimatlán de Álvarez"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_118
msgid "Zinacantepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_chp_111
msgid "Zinacantán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_214
msgid "Zinacatepec"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_110
msgid "Zinapécuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mic_16
msgid "Zinapécuaro de Figueroa"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_109
msgid "Zináparo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_111
msgid "Ziracuaretiro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_073
msgid "Zirándaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_gro_074
msgid "Zitlala"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_112
msgid "Zitácuaro"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_201
msgid "Zongolica"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_215
msgid "Zongozotla"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_202
msgid "Zontecomatlán de López y Fuentes"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_216
msgid "Zoquiapan"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_pue_217
msgid "Zoquitlán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_203
msgid "Zozocolco de Hidalgo"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_119
msgid "Zumpahuacán"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_mex_41
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mex_120
msgid "Zumpango"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_gro_19
msgid "Zumpango del Río"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_partner__l10n_mx_edi_external_trade
#: model:ir.model.fields,help:l10n_mx_edi_extended.field_res_users__l10n_mx_edi_external_trade
msgid ""
"check this box to add by default the external trade complement in invoices "
"for this customer."
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_ver_160
msgid "Álamo Temapache"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_cmx_010
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_mic_003
msgid "Álvaro Obregón"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_ver_50
msgid "Ángel R. Cabada"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:res.city,name:l10n_mx_edi_extended.res_city_mx_oax_174
msgid "Ánimas Trujano"
msgstr ""

#. module: l10n_mx_edi_extended
#: model:l10n_mx_edi.res.locality,name:l10n_mx_edi_extended.res_locality_mx_slp_02
msgid "Ébano"
msgstr ""
