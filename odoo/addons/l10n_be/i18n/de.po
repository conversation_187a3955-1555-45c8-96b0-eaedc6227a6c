# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be
#
# Translators:
# <PERSON>, 2023
# <PERSON><PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.3alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-26 17:03+0000\n"
"PO-Revision-Date: 2023-04-14 14:22+0200\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: \n"
"X-Generator: Poedit 3.2.2\n"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_00
msgid "00 - Operations subject to a special regulation"
msgstr "00 - <PERSON><PERSON><PERSON><PERSON>, die einer Sonderregelung unterliegen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_01
msgid "01 - Operations subject to 6% VAT"
msgstr "01 - <PERSON><PERSON><PERSON><PERSON>, die einer MwSt. von 6 % unterliegen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_02
msgid "02 - Operations subject to 12% VAT"
msgstr "02 - Umsätze, die einer MwSt. von 12 % unterliegen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_03
msgid "03 - Operations subject to 21% VAT"
msgstr "03 - Umsätze, die einer MwSt. von 21 % unterliegen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_44
msgid "44 - Intra-Community services"
msgstr "44 - Innergemeinschaftliche Dienstleistungen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_45
msgid "45 - Operations subject to VAT due by the co-contractor"
msgstr "45 - Umsätze, die der MwSt. unterliegen und die vom Vertragspartner zu zahlen sind"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations_sortie_46
msgid "46 - Exempted intra-Community deliveries and ABC sales"
msgstr "46 - Steuerfreie innergemeinschaftliche Lieferungen und ABC-Geschäfte"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_46L
msgid "46L - Exempted intra-Community deliveries"
msgstr "46L - Steuerfreie innergemeinschaftliche Lieferungen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_46T
msgid "46T - ABC sales"
msgstr "46T - ABC-Geschäfte"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_47
msgid "47 - Other exempted operations and operations carried out abroad"
msgstr "47 - Andere steuerfreie Umsätze und andere im Ausland bewirkte Umsätze"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations_sortie_48
msgid "48 - Credit notes for operations in grids [44] and [46]"
msgstr "48 - Gutschriften, die sich auf Umsätze in den Rastern [44] und [46] beziehen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_48s44
msgid "48s44 - Credit notes for operations in grid [44]"
msgstr "48s44 - Gutschriften, die sich auf Umsätze in Raster [44] beziehen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_48s46L
msgid "48s46L - Credit notes for operations in grid [46L]"
msgstr "48s46L - Gutschriften, die sich auf Umsätze in Raster [46L] beziehen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_48s46T
msgid "48s46T - Credit notes for operations in grid [46T]"
msgstr "48s46T - Gutschriften, die sich auf Umsätze in Raster [46T] beziehen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_49
msgid "49 - Credit notes for other operations in part II"
msgstr "49 - Gutschriften, die sich auf die übrigen Umsätze aus Rahmen II beziehen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_54
msgid "54 - VAT on operations in grids [01], [02] and [03]"
msgstr "54 - MwSt. auf Umsätze in den Rastern [01], [02] und [03]"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_55
msgid "55 - VAT on operations in grids [86] and [88]"
msgstr "55 - MwSt. auf Umsätze in den Rastern [86] und [88]"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_56
msgid ""
"56 - VAT on operations in grid [87], with the exception of imports with "
"reverse charge"
msgstr ""
"56 - MwSt. auf Umsätze in Raster [87], ausschließlich der Einfuhren "
"mit Umkehrung der Steuerschuldnerschaft"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_57
msgid "57 - VAT on import with reverse charge"
msgstr "57 - MwSt. auf Einfuhren mit Umkehrung der Steuerschuldnerschaft"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_59
msgid "59 - Deductible VAT"
msgstr "59 - Abzugsfähige MwSt."

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_61
msgid "61 - Various VAT regularizations in favor of the State"
msgstr "61 - Verschiedene MwSt.-Regelungen zugunsten des Staates"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_62
msgid "62 - Various VAT regularizations in favor of the declarant"
msgstr "62 - Verschiedene MwSt.-Regelungen zugunsten des Anmeldepflichtigen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_63
msgid "63 - VAT to be paid back on credit notes received"
msgstr "63 - Zurückzuzahlende MwSt., die auf erhaltenen Gutschriften angegeben ist"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_64
msgid "64 - VAT to be recovered on credit notes issued"
msgstr "64 - Zurückzuerhaltende MwSt., die auf ausgestellten Gutschriften angegeben ist"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_71
msgid "71 - Taxes due to the State"
msgstr "71 - Dem Staat geschuldete Steuer"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_72
msgid "72 - Amount owed by the State"
msgstr "72 - Vom Staat geschuldeter Betrag"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_81
msgid "81 - Trade goods, raw materials and consumables"
msgstr "81 - Handelsgüter, Roh- und Hilfsstoffe"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_82
msgid "82 - Services and miscellaneous goods"
msgstr "82 - Leistungen und verschiedene Güter"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_83
msgid "83 - Investment goods"
msgstr "83 - Investitionsgüter"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_84
msgid "84 - Credit notes for operations in grids [86] and [88]"
msgstr "84 - Gutschriften, die sich auf Umsätze in Raster [86] und [88] beziehen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_85
msgid "85 - Credit notes received relating to other operations in part III"
msgstr "85 - Gutschriften, die sich auf die übrigen Umsätze aus Rahmen III beziehen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_86
msgid "86 - Intra-Community acquisitions and ABC sales"
msgstr "86 - Innergemeinschaftliche Erwerbe und ABC-Geschäfte"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_87
msgid "87 - Other operations subject to VAT"
msgstr "87 - Sonstige Umsätze, die der MwSt. unterliegen"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_88
msgid "88 - Intra-Community services with reverse charge"
msgstr "88 - Innergemeinschaftliche Leistungen mit Umkehrung der Steuerschuldnerschaft"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_chart_template
msgid "Account Chart Template"
msgstr "Vorlage für Kontenplan"

#. module: l10n_be
#. odoo-python
#: code:addons/l10n_be/models/template_be_asso.py:0
msgid "Associations and Foundations"
msgstr "Vereinigungen und Stiftungen"

#. module: l10n_be
#: model:account.report.column,name:l10n_be.tax_report_vat_balance
msgid "Balance"
msgstr "Saldo"

#. module: l10n_be
#. odoo-python
#: code:addons/l10n_be/models/template_be.py:0
msgid "Base"
msgstr "Basis"

#. module: l10n_be
#: model:ir.model.fields.selection,name:l10n_be.selection__account_journal__invoice_reference_model__be
#: model:ir.ui.menu,name:l10n_be.account_reports_be_statements_menu
msgid "Belgium"
msgstr "Belgien"

#. module: l10n_be
#: model:ir.model.fields,field_description:l10n_be.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr "Mittelungsstandard"

#. module: l10n_be
#. odoo-python
#: code:addons/l10n_be/models/template_be_comp.py:0
msgid "Companies"
msgstr "Unternehmen"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations_sortie
msgid "II Outgoing"
msgstr "II Ausgänge"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations_entree
msgid "III Incoming"
msgstr "III Eingänge"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_taxes_dues
msgid "IV Due"
msgstr "IV Geschuldet"

#. module: l10n_be
#: model:ir.model.fields.selection,name:l10n_be.selection__account_tax__tax_scope__invest
msgid "Investment"
msgstr "Investition"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_journal
msgid "Journal"
msgstr "Journal"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_move
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: l10n_be
#: model:ir.model.fields.selection,name:l10n_be.selection__account_tax__tax_scope__merch
msgid "Merchandise"
msgstr "Waren"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations
msgid "Operations"
msgstr "Umsätze"

#. module: l10n_be
#: model:ir.model.fields,help:l10n_be.field_account_tax__tax_scope
msgid "Restrict the use of taxes to a type of product."
msgstr "Verwendung von Steuern auf eine Art von Produkt beschränken"

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_tax
msgid "Tax"
msgstr "Steuer"

#. module: l10n_be
#: model:ir.model.fields,field_description:l10n_be.field_account_tax__tax_scope
msgid "Tax Scope"
msgstr "Steuergültigkeit"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_taxes
msgid "Taxes"
msgstr "Steuern"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_taxes_deductibles
msgid "V Deductible"
msgstr "V Abzugsfähig"

#. module: l10n_be
#: model:account.report,name:l10n_be.tax_report_vat
msgid "VAT Return"
msgstr "MwSt.-Erklärung"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_taxes_soldes
msgid "VI Balance"
msgstr "VI Saldo"

#. module: l10n_be
#: model:ir.model.fields,help:l10n_be.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""
"Sie können für jede Art von Referenz verschiedene Modelle auswählen. Die "
"Standardeinstellung ist die Odoo-Referenz."
