# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~17.4+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-26 17:03+0000\n"
"PO-Revision-Date: 2024-12-26 17:03+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_00
msgid "00 - Operations subject to a special regulation"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_01
msgid "01 - Operations subject to 6% VAT"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_02
msgid "02 - Operations subject to 12% VAT"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_03
msgid "03 - Operations subject to 21% VAT"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_44
msgid "44 - Intra-Community services"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_45
msgid "45 - Operations subject to VAT due by the co-contractor"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations_sortie_46
msgid "46 - Exempted intra-Community deliveries and ABC sales"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_46L
msgid "46L - Exempted intra-Community deliveries"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_46T
msgid "46T - ABC sales"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_47
msgid "47 - Other exempted operations and operations carried out abroad"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations_sortie_48
msgid "48 - Credit notes for operations in grids [44] and [46]"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_48s44
msgid "48s44 - Credit notes for operations in grid [44]"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_48s46L
msgid "48s46L - Credit notes for operations in grid [46L]"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_48s46T
msgid "48s46T - Credit notes for operations in grid [46T]"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_49
msgid "49 - Credit notes for other operations in part II"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_54
msgid "54 - VAT on operations in grids [01], [02] and [03]"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_55
msgid "55 - VAT on operations in grids [86] and [88]"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_56
msgid ""
"56 - VAT on operations in grid [87], with the exception of imports with "
"reverse charge"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_57
msgid "57 - VAT on import with reverse charge"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_59
msgid "59 - Deductible VAT"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_61
msgid "61 - Various VAT regularizations in favor of the State"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_62
msgid "62 - Various VAT regularizations in favor of the declarant"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_63
msgid "63 - VAT to be paid back on credit notes received"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_64
msgid "64 - VAT to be recovered on credit notes issued"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_71
msgid "71 - Taxes due to the State"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_72
msgid "72 - Amount owed by the State"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_81
msgid "81 - Trade goods, raw materials and consumables"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_82
msgid "82 - Services and miscellaneous goods"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_83
msgid "83 - Investment goods"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_84
msgid "84 - Credit notes for operations in grids [86] and [88]"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_85
msgid "85 - Credit notes received relating to other operations in part III"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_86
msgid "86 - Intra-Community acquisitions and ABC sales"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_87
msgid "87 - Other operations subject to VAT"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_line_88
msgid "88 - Intra-Community services with reverse charge"
msgstr ""

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_be
#. odoo-python
#: code:addons/l10n_be/models/template_be_asso.py:0
msgid "Associations and Foundations"
msgstr ""

#. module: l10n_be
#: model:account.report.column,name:l10n_be.tax_report_vat_balance
msgid "Balance"
msgstr ""

#. module: l10n_be
#. odoo-python
#: code:addons/l10n_be/models/template_be.py:0
msgid "Base"
msgstr ""

#. module: l10n_be
#: model:ir.model.fields.selection,name:l10n_be.selection__account_journal__invoice_reference_model__be
#: model:ir.ui.menu,name:l10n_be.account_reports_be_statements_menu
msgid "Belgium"
msgstr ""

#. module: l10n_be
#: model:ir.model.fields,field_description:l10n_be.field_account_journal__invoice_reference_model
msgid "Communication Standard"
msgstr ""

#. module: l10n_be
#. odoo-python
#: code:addons/l10n_be/models/template_be_comp.py:0
msgid "Companies"
msgstr ""

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations_sortie
msgid "II Outgoing"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations_entree
msgid "III Incoming"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_taxes_dues
msgid "IV Due"
msgstr ""

#. module: l10n_be
#: model:ir.model.fields.selection,name:l10n_be.selection__account_tax__tax_scope__invest
msgid "Investment"
msgstr ""

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_be
#: model:ir.model.fields.selection,name:l10n_be.selection__account_tax__tax_scope__merch
msgid "Merchandise"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_operations
msgid "Operations"
msgstr ""

#. module: l10n_be
#: model:ir.model.fields,help:l10n_be.field_account_tax__tax_scope
msgid "Restrict the use of taxes to a type of product."
msgstr ""

#. module: l10n_be
#: model:ir.model,name:l10n_be.model_account_tax
msgid "Tax"
msgstr ""

#. module: l10n_be
#: model:ir.model.fields,field_description:l10n_be.field_account_tax__tax_scope
msgid "Tax Scope"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_taxes
msgid "Taxes"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_taxes_deductibles
msgid "V Deductible"
msgstr ""

#. module: l10n_be
#: model:account.report,name:l10n_be.tax_report_vat
msgid "VAT Return"
msgstr ""

#. module: l10n_be
#: model:account.report.line,name:l10n_be.tax_report_title_taxes_soldes
msgid "VI Balance"
msgstr ""

#. module: l10n_be
#: model:ir.model.fields,help:l10n_be.field_account_journal__invoice_reference_model
msgid ""
"You can choose different models for each type of reference. The default one "
"is the Odoo reference."
msgstr ""
