<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="account_financial_report_profitandloss_lt" model="account.report">
        <field name="name">Profit and Loss</field>
        <field name="root_report_id" ref="account_reports.profit_and_loss"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="country_id" ref="base.lt"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_financial_report_profitandloss_lt_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_financial_html_report_line_pnl_lt_1" model="account.report.line">
                <field name="name">Net turnover</field>
                <field name="code">PNL_1_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">1</field>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_1_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-tag(l10n_lt.account_account_tag_1_net_turnover)</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_2" model="account.report.line">
                <field name="name">Cost of sales</field>
                <field name="code">PNL_2_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">1</field>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_2_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">tag(l10n_lt.account_account_tag_2_cost_of_sales)</field>
                        <field name="green_on_positive" eval="False"/>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_3" model="account.report.line">
                <field name="name">Fair value adjustments of the biological assets</field>
                <field name="code">PNL_3_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_3_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-tag(l10n_lt.account_account_tag_3_adjustments_of_biological_assets)</field>
                        <field name="subformula" eval="False"/>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_4" model="account.report.line">
                <field name="name">GROSS PROFIT (LOSS)</field>
                <field name="code">PNL_4_</field>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_4_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">PNL_1_.balance - PNL_2_.balance + PNL_3_.balance</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_5" model="account.report.line">
                <field name="name">Selling expenses</field>
                <field name="code">PNL_5_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_5_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">tag(l10n_lt.account_account_tag_4_selling_expenses)</field>
                        <field name="green_on_positive" eval="False"/>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_6" model="account.report.line">
                <field name="name">General and administrative expenses</field>
                <field name="code">PNL_6_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_6_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">tag(l10n_lt.account_account_tag_5_general_administrative_expenses)</field>
                        <field name="green_on_positive" eval="False"/>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_7" model="account.report.line">
                <field name="name">Other operating results</field>
                <field name="code">PNL_7_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_7_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-tag(l10n_lt.account_account_tag_6_other_operating_results)</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_8" model="account.report.line">
                <field name="name">Income from investments in the shares of parent, subsidiaries and associated entities</field>
                <field name="code">PNL_8_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_8_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-tag(l10n_lt.account_account_tag_7_income_investments_parent)</field>
                        <field name="subformula" eval="False"/>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_9" model="account.report.line">
                <field name="name">Income from other long-term investments and loans</field>
                <field name="code">PNL_9_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_9_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-tag(l10n_lt.account_account_tag_8_income_other_longterm_investments_loans)</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_10" model="account.report.line">
                <field name="name">Other interest and similar income</field>
                <field name="code">PNL_10_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_10_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-tag(l10n_lt.account_account_tag_9_other_interest_similar_income)</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_11" model="account.report.line">
                <field name="name">The impairment of the financial assets and short-term investments</field>
                <field name="code">PNL_11_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_11_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">tag(l10n_lt.account_account_tag_10_impaired_fin_assets_short_investments)</field>
                        <field name="green_on_positive" eval="False"/>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_12" model="account.report.line">
                <field name="name">Interest and other similar expenses</field>
                <field name="code">PNL_12_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_12_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">tag(l10n_lt.account_account_tag_11_interest_other_similar_expenses)</field>
                        <field name="green_on_positive" eval="False"/>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_13" model="account.report.line">
                <field name="name">PROFIT (LOSS) BEFORE TAXATION</field>
                <field name="code">PNL_13_</field>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_13_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">PNL_4_.balance - PNL_5_.balance - PNL_6_.balance + PNL_7_.balance + PNL_8_.balance + PNL_9_.balance + PNL_10_.balance - PNL_11_.balance - PNL_12_.balance</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_14" model="account.report.line">
                <field name="name">Tax on profit</field>
                <field name="code">PNL_14_</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_14_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">tag(l10n_lt.account_account_tag_12_tax_on_profit)</field>
                        <field name="green_on_positive" eval="False"/>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_pnl_lt_15" model="account.report.line">
                <field name="name">NET PROFIT (LOSS)</field>
                <field name="code">PNL_15_</field>
                <field name="expression_ids">
                    <record id="account_financial_html_report_line_pnl_lt_15_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">PNL_13_.balance - PNL_14_.balance</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
    <record id="account_financial_report_l10n_lt_pl_action" model="ir.actions.client">
        <field name="name">Profit and Loss</field>
        <field name="context" eval="{'model': 'account.report', 'report_id': ref('account_financial_report_profitandloss_lt')}"/>
        <field name="tag">account_report</field>
    </record>
    <record id="account_financial_report_balancesheet_lt" model="account.report">
        <field name="name">Balance Sheet</field>
        <field name="root_report_id" ref="account_reports.balance_sheet"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_date_range" eval="False"/>
        <field name="country_id" ref="base.lt"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_financial_report_balancesheet_lt_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_financial_html_report_line_bs_lt_debit" model="account.report.line">
                <field name="name">TOTAL ASSETS</field>
                <field name="code">BS_DEBIT</field>
                <field name="hierarchy_level">0</field>
                <field name="aggregation_formula">BS_A_.balance + BS_B_.balance + BS_C_.balance</field>
                <field name="children_ids">
                    <record id="account_financial_html_report_line_bs_lt_a" model="account.report.line">
                        <field name="name">FIXED ASSETS</field>
                        <field name="code">BS_A_</field>
                        <field name="foldable" eval="False"/>
                        <field name="aggregation_formula">BS_A_1_.balance + BS_A_2_.balance + BS_A_3_.balance + BS_A_4_.balance</field>
                        <field name="hierarchy_level">3</field>
                        <field name="children_ids">
                            <record id="account_financial_html_report_line_bs_lt_a_1" model="account.report.line">
                                <field name="name">INTANGIBLE ASSETS</field>
                                <field name="code">BS_A_1_</field>
                                <field name="aggregation_formula">BS_A_1_1_.balance + BS_A_1_2_.balance + BS_A_1_3_.balance + BS_A_1_4_.balance + BS_A_1_5_.balance + BS_A_1_6_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_a_1_1" model="account.report.line">
                                        <field name="name">Assets arising from development</field>
                                        <field name="code">BS_A_1_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_1_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_1_2" model="account.report.line">
                                        <field name="name">Goodwill</field>
                                        <field name="code">BS_A_1_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_1_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_1_3" model="account.report.line">
                                        <field name="name">Software</field>
                                        <field name="code">BS_A_1_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_1_3'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_1_4" model="account.report.line">
                                        <field name="name">Concessions, patents, licences, trade marks and similar rights</field>
                                        <field name="code">BS_A_1_4_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_1_4'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_1_5" model="account.report.line">
                                        <field name="name">Other intangible assets</field>
                                        <field name="code">BS_A_1_5_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_1_5'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_1_6" model="account.report.line">
                                        <field name="name">Advance payments</field>
                                        <field name="code">BS_A_1_6_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_1_6'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_a_2" model="account.report.line">
                                <field name="name">TANGIBLE ASSETS</field>
                                <field name="code">BS_A_2_</field>
                                <field name="aggregation_formula">BS_A_2_1_.balance + BS_A_2_2_.balance + BS_A_2_3_.balance + BS_A_2_4_.balance + BS_A_2_5_.balance + BS_A_2_6_.balance + BS_A_2_7_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_a_2_1" model="account.report.line">
                                        <field name="name">Land</field>
                                        <field name="code">BS_A_2_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_2_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_2_2" model="account.report.line">
                                        <field name="name">Buildings and structures</field>
                                        <field name="code">BS_A_2_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_2_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_2_3" model="account.report.line">
                                        <field name="name">Machinery and plant</field>
                                        <field name="code">BS_A_2_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_2_3'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_2_4" model="account.report.line">
                                        <field name="name">Vehicles</field>
                                        <field name="code">BS_A_2_4_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_2_4'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_2_5" model="account.report.line">
                                        <field name="name">Other equipment, fittings and tools</field>
                                        <field name="code">BS_A_2_5_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_2_5'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_2_6" model="account.report.line">
                                        <field name="name">Investment property</field>
                                        <field name="code">BS_A_2_6_</field>
                                        <field name="aggregation_formula">BS_A_2_6_1_.balance + BS_A_2_6_2_.balance</field>
                                        <field name="children_ids">
                                            <record id="account_financial_html_report_line_bs_lt_a_2_6_1" model="account.report.line">
                                                <field name="name">Land</field>
                                                <field name="code">BS_A_2_6_1_</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_2_6_1'))])</field>
                                            </record>
                                            <record id="account_financial_html_report_line_bs_lt_a_2_6_2" model="account.report.line">
                                                <field name="name">Buildings</field>
                                                <field name="code">BS_A_2_6_2_</field>
                                                <field name="groupby">account_id</field>
                                                <field name="foldable" eval="True"/>
                                                <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_2_6_2'))])</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_2_7" model="account.report.line">
                                        <field name="name">Advance payments and tangible assets under construction (production)</field>
                                        <field name="code">BS_A_2_7_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_2_7'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_a_3" model="account.report.line">
                                <field name="name">FINANCIAL ASSETS</field>
                                <field name="code">BS_A_3_</field>
                                <field name="aggregation_formula">BS_A_3_1_.balance + BS_A_3_2_.balance + BS_A_3_3_.balance + BS_A_3_4_.balance + BS_A_3_5_.balance + BS_A_3_6_.balance + BS_A_3_7_.balance + BS_A_3_8_.balance + BS_A_3_9_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_a_3_1" model="account.report.line">
                                        <field name="name">Shares in entities of the entities group</field>
                                        <field name="code">BS_A_3_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_3_2" model="account.report.line">
                                        <field name="name">Loans to entities of the entities group</field>
                                        <field name="code">BS_A_3_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_3_3" model="account.report.line">
                                        <field name="name">Amounts receivable from entities of the entities group</field>
                                        <field name="code">BS_A_3_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_3'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_3_4" model="account.report.line">
                                        <field name="name">Shares in associated entities</field>
                                        <field name="code">BS_A_3_4_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_4'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_3_5" model="account.report.line">
                                        <field name="name">Loans to associated entities</field>
                                        <field name="code">BS_A_3_5_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_5'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_3_6" model="account.report.line">
                                        <field name="name">Amounts receivable from the associated entities</field>
                                        <field name="code">BS_A_3_6_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_6'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_3_7" model="account.report.line">
                                        <field name="name">Long-term investments</field>
                                        <field name="code">BS_A_3_7_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_7'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_3_8" model="account.report.line">
                                        <field name="name">Amounts receivable after one year</field>
                                        <field name="code">BS_A_3_8_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_8'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_3_9" model="account.report.line">
                                        <field name="name">Other financial assets</field>
                                        <field name="code">BS_A_3_9_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_3_9'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_a_4" model="account.report.line">
                                <field name="name">OTHER FIXED ASSETS</field>
                                <field name="code">BS_A_4_</field>
                                <field name="aggregation_formula">BS_A_4_1_.balance + BS_A_4_2_.balance + BS_A_4_3_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_a_4_1" model="account.report.line">
                                        <field name="name">Assets of the deferred tax on profit</field>
                                        <field name="code">BS_A_4_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_4_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_4_2" model="account.report.line">
                                        <field name="name">Biological assets</field>
                                        <field name="code">BS_A_4_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_4_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_a_4_3" model="account.report.line">
                                        <field name="name">Other assets</field>
                                        <field name="code">BS_A_4_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_a_4_3'))])</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_html_report_line_bs_lt_b" model="account.report.line">
                        <field name="name">CURRENT ASSETS</field>
                        <field name="code">BS_B_</field>
                        <field name="foldable" eval="False"/>
                        <field name="aggregation_formula">BS_B_1_.balance + BS_B_2_.balance + BS_B_3_.balance + BS_B_4_.balance</field>
                        <field name="children_ids">
                            <record id="account_financial_html_report_line_bs_lt_b_1" model="account.report.line">
                                <field name="name">STOCKS</field>
                                <field name="code">BS_B_1_</field>
                                <field name="aggregation_formula">BS_B_1_1_.balance + BS_B_1_2_.balance + BS_B_1_3_.balance + BS_B_1_4_.balance + BS_B_1_5_.balance + BS_B_1_6_.balance + BS_B_1_7_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_b_1_1" model="account.report.line">
                                        <field name="name">Raw materials, materials ir consumables</field>
                                        <field name="code">BS_B_1_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_1_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_1_2" model="account.report.line">
                                        <field name="name">Production and work in progress</field>
                                        <field name="code">BS_B_1_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_1_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_1_3" model="account.report.line">
                                        <field name="name">Finished goods</field>
                                        <field name="code">BS_B_1_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_1_3'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_1_4" model="account.report.line">
                                        <field name="name">Goods for resale</field>
                                        <field name="code">BS_B_1_4_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_1_4'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_1_5" model="account.report.line">
                                        <field name="name">Biological assets</field>
                                        <field name="code">BS_B_1_5_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_1_5'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_1_6" model="account.report.line">
                                        <field name="name">Fixed tangible assets held for sale</field>
                                        <field name="code">BS_B_1_6_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_1_6'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_1_7" model="account.report.line">
                                        <field name="name">Advance payments</field>
                                        <field name="code">BS_B_1_7_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_1_7'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_b_2" model="account.report.line">
                                <field name="name">AMOUNTS RECEIVABLE WITHIN ONE YEAR</field>
                                <field name="code">BS_B_2_</field>
                                <field name="aggregation_formula">BS_B_2_1_.balance + BS_B_2_2_.balance + BS_B_2_3_.balance + BS_B_2_4_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_b_2_1" model="account.report.line">
                                        <field name="name">Trade debtors</field>
                                        <field name="code">BS_B_2_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_2_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_2_2" model="account.report.line">
                                        <field name="name">Amounts owed by entities of the entities group</field>
                                        <field name="code">BS_B_2_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_2_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_2_3" model="account.report.line">
                                        <field name="name">Amounts owed by associates entities</field>
                                        <field name="code">BS_B_2_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_2_3'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_2_4" model="account.report.line">
                                        <field name="name">Other debtors</field>
                                        <field name="code">BS_B_2_4_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_2_4'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_b_3" model="account.report.line">
                                <field name="name">SHORT-TERM INVESTMENTS</field>
                                <field name="code">BS_B_3_</field>
                                <field name="aggregation_formula">BS_B_3_1_.balance + BS_B_3_2_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_b_3_1" model="account.report.line">
                                        <field name="name">Shares in entities of the entities group</field>
                                        <field name="code">BS_B_3_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_3_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_b_3_2" model="account.report.line">
                                        <field name="name">Other investments</field>
                                        <field name="code">BS_B_3_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_3_2'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_b_4" model="account.report.line">
                                <field name="name">CASH AND CASH EQUIVALENTS</field>
                                <field name="code">BS_B_4_</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_b_4'))])</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_html_report_line_bs_lt_c" model="account.report.line">
                        <field name="name">PREPAYMENTS AND ACCRUED INCOME</field>
                        <field name="code">BS_C_</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_c_prepayments_accrued_income'))])</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_html_report_line_bs_lt_credit" model="account.report.line">
                <field name="name">TOTAL EQUITY AND LIABILITIES</field>
                <field name="code">BS_CREDIT</field>
                <field name="hierarchy_level">0</field>
                <field name="foldable" eval="False"/>
                <field name="aggregation_formula">BS_D_.balance + BS_E_.balance + BS_F_.balance + BS_G_.balance + BS_H_.balance</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_financial_html_report_line_bs_lt_d" model="account.report.line">
                        <field name="name">EQUITY </field>
                        <field name="code">BS_D_</field>
                        <field name="aggregation_formula">BS_D_1_.balance + BS_D_2_.balance + BS_D_3_.balance + BS_D_4_.balance + BS_D_5_.balance + BS_D_6_.balance</field>
                        <field name="children_ids">
                            <record id="account_financial_html_report_line_bs_lt_d_1" model="account.report.line">
                                <field name="name">CAPITAL</field>
                                <field name="code">BS_D_1_</field>
                                <field name="aggregation_formula">BS_D_1_1_.balance - BS_D_1_2_.balance - BS_D_1_3_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_d_1_1" model="account.report.line">
                                        <field name="name">Authorized (subscribed) or primary capital</field>
                                        <field name="code">BS_D_1_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_d_1_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_d_1_2" model="account.report.line">
                                        <field name="name">Subscribed capital unpaid (–)</field>
                                        <field name="code">BS_D_1_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_d_1_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_d_1_3" model="account.report.line">
                                        <field name="name">Own shares (–)</field>
                                        <field name="code">BS_D_1_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_d_1_3'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_d_2" model="account.report.line">
                                <field name="name">SHARE PREMIUM ACCOUNT</field>
                                <field name="code">BS_D_2_</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_d_2_share_premium'))])</field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_d_3" model="account.report.line">
                                <field name="name">REVALUATION RESERVE</field>
                                <field name="code">BS_D_3_</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_d_3_revaluation_reserve'))])</field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_d_4" model="account.report.line">
                                <field name="name">RESERVES</field>
                                <field name="code">BS_D_4_</field>
                                <field name="aggregation_formula">BS_D_4_1_.balance + BS_D_4_2_.balance + BS_D_4_3_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_d_4_1" model="account.report.line">
                                        <field name="name">Compulsory reserve or emergency (reserve) capital</field>
                                        <field name="code">BS_D_4_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_d_4_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_d_4_2" model="account.report.line">
                                        <field name="name">Reserve for acquiring own shares</field>
                                        <field name="code">BS_D_4_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_d_4_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_d_4_3" model="account.report.line">
                                        <field name="name">Other reserves</field>
                                        <field name="code">BS_D_4_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_d_4_3'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_d_5" model="account.report.line">
                                <field name="name">RETAINED PROFIT (LOSS)</field>
                                <field name="code">BS_D_5_</field>
                                <field name="aggregation_formula">BS_D_5_1_.balance + BS_D_5_2_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_d_5_1" model="account.report.line">
                                        <field name="name">Profit (loss) for the reporting year </field>
                                        <field name="code">BS_D_5_1_</field>
                                        <field name="action_id" ref="account_financial_report_l10n_lt_pl_action"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_html_report_line_bs_lt_d_5_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">PNL_15_.balance</field>
                                                <field name="subformula">cross_report</field>
                                                <field name="date_scope">from_fiscalyear</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_d_5_2" model="account.report.line">
                                        <field name="name">Profit (loss) brought forward</field>
                                        <field name="code">BS_D_5_2_</field>
                                        <field name="expression_ids">
                                            <record id="account_financial_html_report_line_bs_lt_d_5_2_balance_account_codes" model="account.report.expression">
                                                <field name="label">balance_account_codes</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-tag(l10n_lt.account_account_tag_d_5_1) - tag(l10n_lt.account_account_tag_d_5_2)</field>
                                            </record>
                                            <record id="account_financial_html_report_line_bs_lt_d_5_2_bs_ppl_balance" model="account.report.expression">
                                                <field name="label">bs_pl_balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">PNL_15_.balance</field>
                                                <field name="subformula">cross_report</field>
                                                <field name="date_scope">to_beginning_of_fiscalyear</field>
                                            </record>
                                            <record id="account_financial_html_report_line_bs_lt_d_5_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">BS_D_5_2_.balance_account_codes + BS_D_5_2_.bs_pl_balance</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_d_6" model="account.report.line">
                                <field name="name">COMMON SUMMARY OF ACCOUNTS</field>
                                <field name="code">BS_D_6_</field>
                                <field name="account_codes_formula">-tag(l10n_lt.account_account_tag_d_6)</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_html_report_line_bs_lt_e" model="account.report.line">
                        <field name="name">GRANTS, SUBSIDIES</field>
                        <field name="code">BS_E_</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_e_grants_subsidies'))])</field>
                    </record>
                    <record id="account_financial_html_report_line_bs_lt_f" model="account.report.line">
                        <field name="name">PROVISIONS</field>
                        <field name="code">BS_F_</field>
                        <field name="aggregation_formula">BS_F_1_.balance + BS_F_2_.balance + BS_F_3_.balance</field>
                        <field name="children_ids">
                            <record id="account_financial_html_report_line_bs_lt_f_1" model="account.report.line">
                                <field name="name">Provisions for pensions and similar obligations</field>
                                <field name="code">BS_F_1_</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_f_1'))])</field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_f_2" model="account.report.line">
                                <field name="name">Provisions for taxation</field>
                                <field name="code">BS_F_2_</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_f_2'))])</field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_f_3" model="account.report.line">
                                <field name="name">Other provisions</field>
                                <field name="code">BS_F_3_</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_f_3'))])</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_html_report_line_bs_lt_g" model="account.report.line">
                        <field name="name">AMOUNTS PAYABLE AND OTHER LIABILITIES</field>
                        <field name="code">BS_G_</field>
                        <field name="aggregation_formula">BS_G_1_.balance + BS_G_2_.balance</field>
                        <field name="children_ids">
                            <record id="account_financial_html_report_line_bs_lt_g_1" model="account.report.line">
                                <field name="name">AMOUNTS PAYABLE AFTER ONE YEAR AND OTHER LONG-TERM LIABILITIES</field>
                                <field name="code">BS_G_1_</field>
                                <field name="aggregation_formula">BS_G_1_1_.balance + BS_G_1_2_.balance + BS_G_1_3_.balance + BS_G_1_4_.balance + BS_G_1_5_.balance + BS_G_1_6_.balance + BS_G_1_7_.balance + BS_G_1_8_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_g_1_1" model="account.report.line">
                                        <field name="name">Debenture loans</field>
                                        <field name="code">BS_G_1_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_1_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_1_2" model="account.report.line">
                                        <field name="name">Amounts owed to credit institutions</field>
                                        <field name="code">BS_G_1_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_1_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_1_3" model="account.report.line">
                                        <field name="name">Payments received on account</field>
                                        <field name="code">BS_G_1_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_1_3'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_1_4" model="account.report.line">
                                        <field name="name">Trade creditors</field>
                                        <field name="code">BS_G_1_4_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_1_4'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_1_5" model="account.report.line">
                                        <field name="name">Amounts payable under the bills and checks </field>
                                        <field name="code">BS_G_1_5_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_1_5'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_1_6" model="account.report.line">
                                        <field name="name">Amounts payable to the entities of the entities group</field>
                                        <field name="code">BS_G_1_6_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_1_6'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_1_7" model="account.report.line">
                                        <field name="name">Amounts payable to the associated entities</field>
                                        <field name="code">BS_G_1_7_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_1_7'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_1_8" model="account.report.line">
                                        <field name="name">Other amounts payable and long-term liabilities</field>
                                        <field name="code">BS_G_1_8_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_1_8'))])</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_html_report_line_bs_lt_g_2" model="account.report.line">
                                <field name="name">PAYABLES AND OTHER CURRENT LIABILITIES DUE WITHIN ONE YEAR</field>
                                <field name="code">BS_G_2_</field>
                                <field name="aggregation_formula">BS_G_2_1_.balance + BS_G_2_2_.balance + BS_G_2_3_.balance + BS_G_2_4_.balance + BS_G_2_5_.balance + BS_G_2_6_.balance + BS_G_2_7_.balance + BS_G_2_8_.balance + BS_G_2_9_.balance + BS_G_2_10_.balance</field>
                                <field name="children_ids">
                                    <record id="account_financial_html_report_line_bs_lt_g_2_1" model="account.report.line">
                                        <field name="name">Debenture loans</field>
                                        <field name="code">BS_G_2_1_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_1'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_2" model="account.report.line">
                                        <field name="name">Amounts owed to credit institutions</field>
                                        <field name="code">BS_G_2_2_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_2'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_3" model="account.report.line">
                                        <field name="name">Payments received on account</field>
                                        <field name="code">BS_G_2_3_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_3'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_4" model="account.report.line">
                                        <field name="name">Trade creditors</field>
                                        <field name="code">BS_G_2_4_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_4'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_5" model="account.report.line">
                                        <field name="name">Amounts payable under the bills and checks </field>
                                        <field name="code">BS_G_2_5_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_5'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_6" model="account.report.line">
                                        <field name="name">Amounts payable to the entities of the entities group</field>
                                        <field name="code">BS_G_2_6_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_6'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_7" model="account.report.line">
                                        <field name="name">Amounts payable to the associated entities</field>
                                        <field name="code">BS_G_2_7_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_7'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_8" model="account.report.line">
                                        <field name="name">Liabilities of tax on profit</field>
                                        <field name="code">BS_G_2_8_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_8'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_9" model="account.report.line">
                                        <field name="name">Liabilities related to employment relations</field>
                                        <field name="code">BS_G_2_9_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_9'))])</field>
                                    </record>
                                    <record id="account_financial_html_report_line_bs_lt_g_2_10" model="account.report.line">
                                        <field name="name">Other amounts payable and short-term liabilities</field>
                                        <field name="code">BS_G_2_10_</field>
                                        <field name="groupby">account_id</field>
                                        <field name="foldable" eval="True"/>
                                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_g_2_10'))])</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_html_report_line_bs_lt_h" model="account.report.line">
                        <field name="name">ACCRUALS AND DEFERRED INCOME</field>
                        <field name="code">BS_H_</field>
                        <field name="groupby">account_id</field>
                        <field name="foldable" eval="True"/>
                        <field name="domain_formula">-sum([('account_id.tag_ids', '=', ref('l10n_lt.account_account_tag_h_accruals_deferred_income'))])</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
