<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_pa" model="res.partner" forcecreate="1">
        <field name="name">PA Company</field>
        <field name="vat">PA770023598</field>
        <field name="street">Entrada Urbanización El Bosque</field>
        <field name="city">San Miguelito</field>
        <field name="country_id" ref="base.pa"/>
        <field name="zip">84365</field>
        <field name="phone">+************-9</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.paexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_pa" model="res.company" forcecreate="1">
        <field name="name">PA Company</field>
        <field name="partner_id" ref="base.partner_demo_company_pa"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_pa')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_pa'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>pa</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_pa')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
