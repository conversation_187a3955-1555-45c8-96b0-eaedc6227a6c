<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_sk" model="res.partner" forcecreate="1">
        <field name="name">SK Company</field>
        <field name="vat">SK2022749619</field>
        <field name="street">Prievozská</field>
        <field name="city">Bratislava</field>
        <field name="country_id" ref="base.sk"/>
        <field name="zip">821 09</field>
        <field name="phone">+421 5 12 34 56 78</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.skexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_sk" model="res.company" forcecreate="1">
        <field name="name">SK Company</field>
        <field name="partner_id" ref="base.partner_demo_company_sk"/>
    </record>

    <record id="base.demo_bank_sk" model="res.partner.bank" forcecreate="1">
        <field name="acc_number">SK0265215925971839248596</field>
        <field name="partner_id" ref="base.partner_demo_company_sk"/>
        <field name="company_id" ref="base.demo_company_sk"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_sk')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_sk'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>sk</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_sk')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
