<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_preparation_display.Stages">
        <section class="d-flex justify-content-center flex-grow-1 border-bottom overflow-hidden">
            <div class="d-flex align-items-center justify-content-start gap-3 py-2 overflow-auto">
                <button
                    class="btn btn-lg d-flex align-items-center px-3"
                    t-att-class="{ 'bg-200': this.preparationDisplay.selectedStageId === 0 }"
                    t-on-click="() => this.preparationDisplay.selectStage(0)"
                >
                    All
                </button>
                <button
                    t-foreach="[...this.props.stages.values()]"
                    t-as="stage"
                    t-key="stage.id"
                    class="o_pdis_navbar_stage btn btn-lg d-flex align-items-center bg-opacity-50"
                    t-on-click="() => this.preparationDisplay.selectStage(stage.id)"
                    t-attf-style="{{ this.preparationDisplay.selectedStageId === stage.id ? 'background-color:' + (stage.color ? (stage.color === '#ffffff' ? '#00000010' : stage.color + '50') : '#00000010') + ';' : '' }}"
                >
                    <t t-esc="stage.name"/>
                    <div t-att-style="`background-color: ${stage.color} !important; color: ${this.getFontColor(stage.color)}`" class="ms-2 px-2 fw-bold rounded" t-esc="stage.orderCount"/>
                </button>
            </div>
        </section>
    </t>
</templates>
