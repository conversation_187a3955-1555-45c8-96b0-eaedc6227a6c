<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_preparation_display.Order">
        <section
            class="o_pdis_order_card position-relative w-100 mx-auto mb-auto overflow-hidden rounded"
            t-attf-class="{{this.props.order.changeStageTimeout ? 'o_pdis_stage--animation' : ''}} {{this.cardColor}}">
            <div class="o_pdis_order_card_header" t-on-click="clickOrder">
                <div
                    class="o_pdis_order_card_header_top d-flex flex-nowrap align-items-center justify-content-between px-2 pb-1 fw-bold"
                    t-att-class="this.cardColor">
                    <div class="o_pdis_tracking_number d-flex align-items-center justify-content-start flex-shrink-0 pe-3 flex-grow-1">
                        <div class="o_pdis_tracker">
                            #<t t-esc="this.props.order.tracking_number"/>
                        </div>
                    </div>
                    <div t-if="this.props.order.responsible" class="o_pdis_staff w-100 text-truncate flex-shrink-1">
                        <i class="fa fa-user-circle-o pe-1" aria-hidden="true"/>
                        <span t-esc="this.props.order.responsible"/>
                    </div>
                </div>
                <div class="o_pdis_order_card_header_bottom d-grid p-2 bg-100">
                    <div class="o_pdis_table-info d-flex justify-content-between align-items-center">
                        <div t-esc="this.stage.name" class=" o_pdis_stage py-1 px-3 rounded-pill" t-att-style="`background-color: ${this.stage.color} !important; color: ${this.fondColor}`" />
                        <div class="o_pdis_alert-timer" t-attf-class="{{ this.preparationDisplay.lastStage.id !== this.props.order.stageId ? '' : 'd-none' }}">
                            <div class="rounded-pill py-1 px-3"
                                t-att-class="{ 'bg-white': !this.isAlert, 'o_pdis_is_alert text-bg-danger': this.isAlert, 'o_pdis_archive-order cursor-pointer': this.preparationDisplay.lastStage.id === this.props.order.stageId }">
                                <div t-if="this.preparationDisplay.lastStage.id !== this.props.order.stageId">
                                    <i class="fa fa-clock-o pe-1" aria-hidden="true"></i>
                                    <span t-esc="this.state.duration" />'
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="o_pdis_orderline w-100 bg-white overflow-auto" t-ref="orderlines-container">
                <t t-foreach="this.getSortedOrderlines()" t-as="orderline" t-key="orderline.id">
                    <t t-if="this.preparationDisplay.checkOrderlineVisibility(orderline)">
                        <Orderline orderline="orderline"/>
                    </t>
                </t>
                <div class="product-down-bubble position-absolute bottom-0 start-50 translate-middle d-flex align-items-center justify-content-center badge rounded-pill py-2 px-3 text-bg-dark bg-opacity-75 small " t-if="this.state.productHighlighted.length">
                    <t t-if="this.state.productHighlighted.length === 1">
                        <t t-esc="this.state.productHighlighted[0]" />
                    </t>
                    <t t-else="">
                        <t t-esc="this.state.productHighlighted.length" /> products
                    </t>
                    <i class="ms-2 oi oi-arrow-down" aria-hidden="true"></i>
                </div>
            </div>

            <div class="m-2 text-break" t-if="this.props.order.generalNote?.length > 0" t-on-click="clickOrder">
                <p class="fw-bold mb-2 px-2 fs-6">General Note</p>
                <ul class="small text-muted px-4">
                    <t t-foreach="this.props.order.generalNote.split('\n')" t-as="subNote" t-key="subNote_index">
                        <li><t t-esc="subNote"/></li>
                    </t>
                </ul>
            </div>
            <div class="o_pdis_order_card_footer bg-100">
                <t t-set="o_pdis_last_stage" t-value="this.preparationDisplay.lastStage.id == this.props.order.stageId"/>
                <div t-attf-class="{{ o_pdis_last_stage ? 'd-flex pt-1' : ''}}">
                    <button class="btn btn-lg btn-secondary flex-grow-1 border-end border-white rounded" type="button" t-on-click.stop="() => this.preparationDisplay.changeOrderStage(this.props.order, true)" t-if="o_pdis_last_stage">
                        <i class="fa fa-undo pe-1" aria-hidden="true"></i><span>Reset</span>
                    </button>
                    <button class="btn btn-lg btn-secondary flex-grow-1 border-start border-white rounded" type="button" t-on-click.stop="doneOrder" t-if="o_pdis_last_stage">
                        <span>Done</span>
                    </button>
                </div>
            </div>
        </section>
    </t>
</templates>
