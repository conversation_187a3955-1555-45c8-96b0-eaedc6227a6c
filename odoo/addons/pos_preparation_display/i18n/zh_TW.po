# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_preparation_display
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (Taiwan) (https://app.transifex.com/odoo/teams/41243/zh_TW/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_TW\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "<span>Average time</span>"
msgstr "<span>平均時間</span>"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "<span>In progress</span>"
msgstr "<span>進行中</span>"

#. module: pos_preparation_display
#. odoo-python
#: code:addons/pos_preparation_display/models/preparation_display.py:0
msgid "A preparation display must have a minimum of one step."
msgstr "準備工作顯示必須最少有一個步驟。"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__access_token
msgid "Access Token"
msgstr "存取權杖(token)"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__alert_timer
msgid "Alert timer (min)"
msgstr "警報計時器（分鐘）"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/stages/stages.xml:0
msgid "All"
msgstr "所有"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
msgid "All PoS"
msgstr "所有PoS"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
msgid "All categories"
msgstr "所有類別"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_order__order_stage_ids
msgid "All the stage ids in which the order is placed"
msgstr "訂單所在的所有階段識別碼"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_reset_wizard
msgid ""
"Archive all preparation display's orders for a fresh start. This will not "
"affect the PoS order history."
msgstr "封存所有準備工作顯示的訂單，以重新開始。這不會影響 PoS 訂單歷史記錄。"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_display__average_time
msgid "Average time of all order that not in a done stage."
msgstr "所有並非在已完成階段的訂單的平均時間。"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_reset_wizard
msgid "Check products"
msgstr "檢查產品"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Clear All Filters"
msgstr "清除所有篩選器"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Close"
msgstr "關閉"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__color
msgid "Color"
msgstr "顏色"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__company_id
msgid "Company"
msgstr "公司"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "Configure"
msgstr "設定"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__create_uid
msgid "Created by"
msgstr "建立人員"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__create_date
msgid "Created on"
msgstr "建立於"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_order__pdis_general_note
msgid "Current general-note displayed on preparation display"
msgstr "準備工作顯示屏上，目前顯示的一般備註"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_order__displayed
msgid ""
"Determines whether the order should be displayed on the preparation screen"
msgstr "決定是否在準備畫面顯示該訂單"

#. module: pos_preparation_display
#: model_terms:ir.actions.act_window,help:pos_preparation_display.action_preparation_display
msgid ""
"Different products and categories are sent to different tablets and screens."
msgstr "不同的產品及類別會發送至不同的平板電腦及螢幕。"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_reset_wizard
msgid "Discard"
msgstr "捨棄"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__display_name
msgid "Display Name"
msgstr "顯示名稱"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/order/order.xml:0
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Done"
msgstr "完成"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Explore demo data"
msgstr "探索模擬數據"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/override/point_of_sale/pos_store.js:0
msgid "Failed in sending the changes to preparation display"
msgstr "未能向準備工作顯示屏發送變更"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Filters"
msgstr "篩選"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/order/order.xml:0
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__pdis_general_note
msgid "General Note"
msgstr "一般備註"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__id
msgid "ID"
msgstr "識別號"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_order__pos_order_id
msgid "ID of the original PoS order"
msgstr "原始銷售點訂單識別碼"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__internal_note
msgid "Internal Note"
msgstr "內部備註"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_orderline__internal_note
msgid "Internal notes written at the time of the order"
msgstr "訂單下單時的內部備註"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__contains_bar_restaurant
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_search
msgid "Is a Bar/Restaurant"
msgstr "為酒吧/餐廳"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__done
msgid "Is the order done"
msgstr "訂單是否已完成"

#. module: pos_preparation_display
#: model:ir.actions.server,name:pos_preparation_display.action_pos_preparation_display_kitchen_display
#: model:ir.ui.menu,name:pos_preparation_display.menu_point_kitchen_display_root
msgid "Kitchen Display"
msgstr "廚房顯示屏"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__write_uid
msgid "Last Updated by"
msgstr "最後更新者"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__write_date
msgid "Last Updated on"
msgstr "最後更新於"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Loading..."
msgstr "載入中⋯"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__name
msgid "Name"
msgstr "名稱"

#. module: pos_preparation_display
#: model_terms:ir.actions.act_window,help:pos_preparation_display.action_preparation_display
msgid "Need a touchscreen interface to manage order ?"
msgstr "需要觸控螢幕介面去管理訂單嗎？"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "No data available."
msgstr "無可用資料."

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
msgid "Offer"
msgstr "聘用邀約"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__order_id
msgid "Order"
msgstr "訂單"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__preparation_display_order_line_ids
msgid "Order Lines"
msgstr "訂單明細"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__order_stage_ids
msgid "Order Stage"
msgstr "訂單階段"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__average_time
msgid "Order average time"
msgstr "訂單平均時間"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__order_count
msgid "Order count"
msgstr "訂單數目"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__displayed
msgid "Order is displayed"
msgstr "訂單會顯示"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_orderline__pos_order_line_uuid
msgid "Original pos order line UUID"
msgstr "原始銷售點訂單資料行唯一識別碼"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__pos_config_ids
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__pos_config_id
msgid "Point of Sale"
msgstr "POS營業點"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_order
msgid "Point of Sale Orders"
msgstr "POS訂單"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_session
msgid "Point of Sale Session"
msgstr "POS營業點"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_orderline
msgid "Point of Sale preparation order line"
msgstr "銷售點準備工作單資料行"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_stage
msgid "Point of Sale preparation stage"
msgstr "銷售點準備工作階段"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__pos_order_id
msgid "Pos Order"
msgstr "POS訂單"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__pos_order_line_uuid
msgid "Pos Order Line Uuid"
msgstr "銷售點訂單資料行唯一識別碼"

#. module: pos_preparation_display
#: model:ir.actions.act_url,name:pos_preparation_display.action_pos_preparation_display_bar_restaurant_filter_link
#: model:ir.actions.act_window,name:pos_preparation_display.action_preparation_display
#: model:ir.actions.act_window,name:pos_preparation_display.action_preparation_display_bar_restaurant_filter
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__preparation_display_id
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_search
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_tree
msgid "Preparation Display"
msgstr "準備工作顯示"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__preparation_display_order_id
msgid "Preparation Display Order"
msgstr "準備工作顯示順序"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "Preparation Screen"
msgstr "準備工作畫面"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__preparation_display_id
msgid "Preparation display"
msgstr "準備工作顯示"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_order
msgid "Preparation orders"
msgstr "準備工作單"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__product_id
msgid "Product ID"
msgstr "產品識別碼"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__category_ids
msgid "Product categories"
msgstr "產品類別"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_display__category_ids
msgid "Product categories that will be displayed on this screen."
msgstr "此螢幕將會顯示的產品類別。"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__product_cancelled
msgid "Quantity of cancelled product"
msgstr "已取消產品數量"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__product_quantity
msgid "Quantity of ordered product"
msgstr "訂單產品數量"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Recall"
msgstr "召回"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/order/order.xml:0
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_reset_wizard
msgid "Reset"
msgstr "重設"

#. module: pos_preparation_display
#. odoo-python
#: code:addons/pos_preparation_display/models/preparation_display.py:0
msgid "Reset Preparation Display"
msgstr "重設準備工作顯示"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_reset_wizard
msgid "Reset all current order in a preparation display"
msgstr "重設準備工作顯示中目前所有訂單"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "Reset all orders"
msgstr "重設所有訂單"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__attribute_value_ids
msgid "Selected Attributes"
msgstr "已選取屬性"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/override/point_of_sale/pos_store.js:0
msgid "Send failed"
msgstr "發送失敗"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__sequence
msgid "Sequence"
msgstr "序列號"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__stage_id
msgid "Stage"
msgstr "階段"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_order_stage
msgid "Stage of orders by preparation display"
msgstr "按準備工作顯示的訂單階段"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__stage_ids
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
msgid "Stages"
msgstr "階段"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__todo
msgid "Status of the orderline"
msgstr "訂單資料行狀態"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_orderline__todo
msgid "The status of a command line, todo or not"
msgstr "指令行的狀態，是否待辦事項"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_stage__alert_timer
msgid "Timer after which the order will be highlighted"
msgstr "計時器時間，之後訂單會突出標示"

#. module: pos_preparation_display
#: model:ir.actions.server,name:pos_preparation_display.preparation_display_delete_cron_ir_actions_server
msgid "delete_preparation_display_order"
msgstr "delete_preparation_display_order"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "or simply wait for orders to be sent for preparation."
msgstr "或只等待需要發送至準備工作的訂單。"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/order/order.xml:0
msgid "products"
msgstr "產品"
