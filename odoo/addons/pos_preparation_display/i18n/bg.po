# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_preparation_display
# 
# Translators:
# <PERSON><PERSON>, 2024
# Ивай<PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <albena_viche<PERSON>@abv.bg>, 2024
# <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# ale<PERSON><PERSON><PERSON>, 2024
# KeyVillage, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Bulgarian (https://app.transifex.com/odoo/teams/41243/bg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: bg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "<span>Average time</span>"
msgstr ""

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "<span>In progress</span>"
msgstr ""

#. module: pos_preparation_display
#. odoo-python
#: code:addons/pos_preparation_display/models/preparation_display.py:0
msgid "A preparation display must have a minimum of one step."
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__access_token
msgid "Access Token"
msgstr "Токен за достъп"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__alert_timer
msgid "Alert timer (min)"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/stages/stages.xml:0
msgid "All"
msgstr "Всичко"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
msgid "All PoS"
msgstr ""

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
msgid "All categories"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_order__order_stage_ids
msgid "All the stage ids in which the order is placed"
msgstr ""

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_reset_wizard
msgid ""
"Archive all preparation display's orders for a fresh start. This will not "
"affect the PoS order history."
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_display__average_time
msgid "Average time of all order that not in a done stage."
msgstr ""

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_reset_wizard
msgid "Check products"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Clear All Filters"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Close"
msgstr "Затвори"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__color
msgid "Color"
msgstr "Цвят"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__company_id
msgid "Company"
msgstr "Фирма"

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "Configure"
msgstr "Конфигурирайте"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__create_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__create_uid
msgid "Created by"
msgstr "Създаден от"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__create_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__create_date
msgid "Created on"
msgstr "Създадено на"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_order__pdis_general_note
msgid "Current general-note displayed on preparation display"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_order__displayed
msgid ""
"Determines whether the order should be displayed on the preparation screen"
msgstr ""

#. module: pos_preparation_display
#: model_terms:ir.actions.act_window,help:pos_preparation_display.action_preparation_display
msgid ""
"Different products and categories are sent to different tablets and screens."
msgstr ""

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_reset_wizard
msgid "Discard"
msgstr "Отхвърлете"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__display_name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__display_name
msgid "Display Name"
msgstr "Име за показване"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/order/order.xml:0
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Done"
msgstr "Извършен"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Explore demo data"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/override/point_of_sale/pos_store.js:0
msgid "Failed in sending the changes to preparation display"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Filters"
msgstr "Филтри"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/order/order.xml:0
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__pdis_general_note
msgid "General Note"
msgstr "Обща бележка"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__id
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__id
msgid "ID"
msgstr "ID"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_order__pos_order_id
msgid "ID of the original PoS order"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__internal_note
msgid "Internal Note"
msgstr "Вътрешна бележка"

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_orderline__internal_note
msgid "Internal notes written at the time of the order"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__contains_bar_restaurant
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_search
msgid "Is a Bar/Restaurant"
msgstr "Е бар/ресторант"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__done
msgid "Is the order done"
msgstr ""

#. module: pos_preparation_display
#: model:ir.actions.server,name:pos_preparation_display.action_pos_preparation_display_kitchen_display
#: model:ir.ui.menu,name:pos_preparation_display.menu_point_kitchen_display_root
msgid "Kitchen Display"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__write_uid
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__write_uid
msgid "Last Updated by"
msgstr "Последно актуализирано от"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_reset_wizard__write_date
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__write_date
msgid "Last Updated on"
msgstr "Последно актуализирано на"

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Loading..."
msgstr "Зареждане..."

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__name
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__name
msgid "Name"
msgstr "Име"

#. module: pos_preparation_display
#: model_terms:ir.actions.act_window,help:pos_preparation_display.action_preparation_display
msgid "Need a touchscreen interface to manage order ?"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "No data available."
msgstr "Няма налични данни."

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
msgid "Offer"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__order_id
msgid "Order"
msgstr "Поръчка"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__preparation_display_order_line_ids
msgid "Order Lines"
msgstr "Редове на поръчка"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__order_stage_ids
msgid "Order Stage"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__average_time
msgid "Order average time"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__order_count
msgid "Order count"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__displayed
msgid "Order is displayed"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_orderline__pos_order_line_uuid
msgid "Original pos order line UUID"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__pos_config_ids
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__pos_config_id
msgid "Point of Sale"
msgstr "Център за продажби"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_order
msgid "Point of Sale Orders"
msgstr "Поръчки на центъра за продажби"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_session
msgid "Point of Sale Session"
msgstr "Сесия на център за продажби"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_orderline
msgid "Point of Sale preparation order line"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_stage
msgid "Point of Sale preparation stage"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order__pos_order_id
msgid "Pos Order"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__pos_order_line_uuid
msgid "Pos Order Line Uuid"
msgstr ""

#. module: pos_preparation_display
#: model:ir.actions.act_url,name:pos_preparation_display.action_pos_preparation_display_bar_restaurant_filter_link
#: model:ir.actions.act_window,name:pos_preparation_display.action_preparation_display
#: model:ir.actions.act_window,name:pos_preparation_display.action_preparation_display_bar_restaurant_filter
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__preparation_display_id
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_search
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_tree
msgid "Preparation Display"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__preparation_display_order_id
msgid "Preparation Display Order"
msgstr ""

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "Preparation Screen"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__preparation_display_id
msgid "Preparation display"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_order
msgid "Preparation orders"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__product_id
msgid "Product ID"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__category_ids
msgid "Product categories"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_display__category_ids
msgid "Product categories that will be displayed on this screen."
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__product_cancelled
msgid "Quantity of cancelled product"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__product_quantity
msgid "Quantity of ordered product"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "Recall"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/order/order.xml:0
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_reset_wizard
msgid "Reset"
msgstr "Нулиране"

#. module: pos_preparation_display
#. odoo-python
#: code:addons/pos_preparation_display/models/preparation_display.py:0
msgid "Reset Preparation Display"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_reset_wizard
msgid "Reset all current order in a preparation display"
msgstr ""

#. module: pos_preparation_display
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_kanban
msgid "Reset all orders"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__attribute_value_ids
msgid "Selected Attributes"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/override/point_of_sale/pos_store.js:0
msgid "Send failed"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_stage__sequence
msgid "Sequence"
msgstr "Последователност"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_order_stage__stage_id
msgid "Stage"
msgstr "Стадий"

#. module: pos_preparation_display
#: model:ir.model,name:pos_preparation_display.model_pos_preparation_display_order_stage
msgid "Stage of orders by preparation display"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_display__stage_ids
#: model_terms:ir.ui.view,arch_db:pos_preparation_display.preparation_display_view_form
msgid "Stages"
msgstr "Стадии"

#. module: pos_preparation_display
#: model:ir.model.fields,field_description:pos_preparation_display.field_pos_preparation_display_orderline__todo
msgid "Status of the orderline"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_orderline__todo
msgid "The status of a command line, todo or not"
msgstr ""

#. module: pos_preparation_display
#: model:ir.model.fields,help:pos_preparation_display.field_pos_preparation_display_stage__alert_timer
msgid "Timer after which the order will be highlighted"
msgstr ""

#. module: pos_preparation_display
#: model:ir.actions.server,name:pos_preparation_display.preparation_display_delete_cron_ir_actions_server
msgid "delete_preparation_display_order"
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/preparation_display/preparation_display.xml:0
msgid "or simply wait for orders to be sent for preparation."
msgstr ""

#. module: pos_preparation_display
#. odoo-javascript
#: code:addons/pos_preparation_display/static/src/app/components/order/order.xml:0
msgid "products"
msgstr ""
