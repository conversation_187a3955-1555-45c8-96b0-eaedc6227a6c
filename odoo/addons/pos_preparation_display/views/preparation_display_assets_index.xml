<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="pos_preparation_display.index" name="Preparation Display Index">
        &lt;!DOCTYPE html&gt;
        <html>
            <head>
                <title>Odoo Preparation Display</title>

                <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
                <meta http-equiv="content-type" content="text/html, charset=utf-8" />

                <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no" />
                <meta name="apple-mobile-web-app-capable" content="yes" />
                <meta name="mobile-web-app-capable" content="yes" />

                <link rel="shortcut icon" sizes="196x196" href="/point_of_sale/static/src/img/touch-icon-196.png" />
                <link rel="shortcut icon" sizes="128x128" href="/point_of_sale/static/src/img/touch-icon-128.png" />
                <link rel="apple-touch-icon" href="/point_of_sale/static/src/img/touch-icon-iphone.png" />
                <link rel="apple-touch-icon" sizes="76x76" href="/point_of_sale/static/src/img/touch-icon-ipad.png" />
                <link rel="apple-touch-icon" sizes="120x120" href="/point_of_sale/static/src/img/touch-icon-iphone-retina.png" />
                <link rel="apple-touch-icon" sizes="152x152" href="/point_of_sale/static/src/img/touch-icon-ipad-retina.png" />

                <style> body { margin:0;padding:0 } </style>

                <link rel="shortcut icon" href="/point_of_sale/static/src/img/favicon.ico" type="image/x-icon" />
                <link rel="icon" href="/point_of_sale/static/src/img/favicon.ico" type="image/x-icon" />

                <script type="text/javascript">
                    var odoo =
                    <t t-out="json.dumps({
                        'csrf_token': request.csrf_token(None),
                        'preparation_display': session_info['preparation_display'],
                        '__session_info__': session_info,
                        'debug': debug,
                    })" />;

                    document.title = odoo.preparation_display.name;
                </script>

                <t t-call-assets="pos_preparation_display.assets" />
                <t t-if="'tests' in debug or test_mode_enabled">
                    <t t-call-assets="pos_preparation_display.assets_tour_tests" defer_load="True" />
                </t>
            </head>
            <body></body>
        </html>
    </template>
</odoo>
