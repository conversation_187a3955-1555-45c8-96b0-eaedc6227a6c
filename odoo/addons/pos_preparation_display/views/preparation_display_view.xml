<odoo>
    <record id="action_preparation_display" model="ir.actions.act_window">
        <field name="name">Preparation Display</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">pos_preparation_display.display</field>
        <field name="view_mode">kanban,list,form</field><field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Need a touchscreen interface to manage order ?
            </p>
            <p>
                Different products and categories are sent to different tablets and screens.
            </p>
        </field>
    </record>

    <record id="action_pos_preparation_display_kitchen_display" model="ir.actions.server">
        <field name="name">Kitchen Display</field>
        <field name="model_id" ref="model_pos_preparation_display_display"/>
        <field name="state">code</field>
        <field name="code">
            action = env.ref('pos_preparation_display.action_pos_preparation_display_bar_restaurant_filter_link').sudo().read()[0]
        </field>
    </record>

    <record id="action_preparation_display_bar_restaurant_filter"
        model="ir.actions.act_window">
        <field name="name">Preparation Display</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">pos_preparation_display.display</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{'search_default_filter_bar_restaurant': True}</field>
    </record>

    <!-- Link used to open Preparation Display in the point_of_sale module -->
    <record id="action_pos_preparation_display_bar_restaurant_filter_link" model="ir.actions.act_url">
        <field name="name">Preparation Display</field>
        <field name="target">self</field>
        <field name="url" eval="'/odoo/action-pos_preparation_display.action_preparation_display_bar_restaurant_filter?menu_id=%d' % (ref('point_of_sale.menu_point_root'))"/>
    </record>

    <record id="preparation_display_view_form" model="ir.ui.view">
        <field name="name">preparation.display.form.view</field>
        <field name="model">pos_preparation_display.display</field>
        <field name="arch" type="xml">
            <form string="Preparation Display">
                <sheet>
                    <div class="oe_title mb-32">
                        <label for="name" />
                        <h1>
                            <field name="name" class="oe_inline" />
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="pos_config_ids" widget="many2many_tags" options="{'create':false}" placeholder="All PoS"/>
                            <field name="category_ids" widget="many2many_tags" options="{'create':false}" placeholder="All categories"/>
                        </group>
                    </group>
                    <notebook>
                        <page name="stages" string="Stages">
                            <field name="stage_ids">
                                <list string="Offer" editable="bottom">
                                    <field name="sequence" widget="handle"/>
                                    <field name="name" />
                                    <field name="color" widget="color" />
                                    <field name="alert_timer" />
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="preparation_display_view_tree" model="ir.ui.view">
        <field name="name">preparation.display.list.view</field>
        <field name="model">pos_preparation_display.display</field>
        <field name="arch" type="xml">
            <list string="Preparation Display" create="1" delete="1">
                <field name="name" />
                <field name="pos_config_ids" widget="many2many_tags" />
                <field name="category_ids" widget="many2many_tags" />
                <field name="stage_ids" widget="many2many_tags" options="{'color_field':'color'}" />
            </list>
        </field>
    </record>

    <record id="preparation_display_view_search" model="ir.ui.view">
        <field name="name">preparation.display.search.view</field>
        <field name="model">pos_preparation_display.display</field>
        <field name="arch" type="xml">
            <search string="Preparation Display">
                <field name="name" />
                <field name="pos_config_ids" />
                <filter string="Is a Bar/Restaurant" name="filter_bar_restaurant"
                    domain="[('contains_bar_restaurant', '=', True)]" />
            </search>
        </field>
    </record>

    <record id="preparation_display_view_kanban" model="ir.ui.view">
        <field name="name">preparation.display.kanban.view</field>
        <field name="model">pos_preparation_display.display</field>
        <field name="arch" type="xml">
            <kanban can_open="0" class="o_pos_kanban">
                <templates>
                    <t t-name="menu">
                        <div class="container">
                            <div class="row">
                                <div class="col-4">
                                    <div groups="point_of_sale.group_pos_manager">
                                        <a type="open">Configure</a>
                                    </div>
                                    <div groups="base.group_no_one">
                                        <a name="open_reset_wizard" type="object">Reset all orders</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                    <t t-name="card">
                        <field name="name" class="fw-bold fs-4 ms-2"/>
                        <field name="stage_ids" widget="many2many_tags" class="ms-2"/>
                        <div class="row pb-4 mt-4">
                            <div name="card_left" class="col-12 col-sm-7 d-flex flex-row">
                                <button class="btn btn-primary ms-1 me-1" name="open_ui" type="object">
                                    Preparation Screen
                                </button>
                            </div>
                            <div class="col-12 col-sm-5">
                                <div class="row g-0 text-end pe-1">
                                    <div class="col-10">
                                        <span>In progress</span>
                                    </div>
                                    <div class="col-2">
                                        <field name="order_count"/>
                                    </div>
                                </div>
                                <div class="row g-0 text-end pe-1">
                                    <div class="col-10">
                                        <span>Average time</span>
                                    </div>
                                    <div class="col-2">
                                        <span><field name="average_time" />'</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <menuitem
        name="Preparation Display"
        id="point_of_sale.menu_pos_preparation_display"
        parent="point_of_sale.menu_point_of_sale"
        action="action_preparation_display"
        sequence="99" />

    <menuitem
        id="menu_point_kitchen_display_root"
        name="Kitchen Display"
        groups="point_of_sale.group_pos_manager"
        web_icon="pos_preparation_display,static/description/icon.png"
        sequence="51"
        active="False"
        action="action_pos_preparation_display_kitchen_display" />

</odoo>
