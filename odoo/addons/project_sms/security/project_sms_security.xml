<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <record id="ir_rule_sms_template_project_manager" model="ir.rule">
        <field name="name">SMS Template: project manager CUD on project/task</field>
        <field name="model_id" ref="sms.model_sms_template"/>
        <field name="groups" eval="[(4, ref('project.group_project_manager'))]"/>
        <field name="domain_force">[('model_id.model', 'in', ('project.task.type', 'project.project.stage'))]</field>
        <field name="perm_read" eval="False"/>
    </record>
</odoo>
