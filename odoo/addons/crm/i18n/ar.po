# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* crm
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_all_assigned_month_count
msgid "# Leads/Opps assigned this month"
msgstr "عدد العملاء المهتمين/الفرص التي تم تعيينها هذا الشهر "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__number_of_months
msgid "# Months"
msgstr "عدد الشهور "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_count
msgid "# Opportunities"
msgstr "عدد الفرص "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_overdue_count
msgid "# Overdue Opportunities"
msgstr "عدد الفرص المتأخرة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_unassigned_count
msgid "# Unassigned Leads"
msgstr "عدد العملاء المهتمين غير المعينين "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "%(assigned)s leads allocated among %(team_count)s teams."
msgstr "%(assigned)s عميلاً مهتماً تم تقسيمهم بين %(team_count)s فريق/فرق. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "%(assigned)s leads allocated to %(team_name)s team."
msgstr "%(assigned)s عميلاً مهتماً تم إسنادهم إلى %(team_name)s فريق/فرق. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "%(attach_name)s (from %(lead_name)s)"
msgstr "%(attach_name)s (من %(lead_name)s) "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "%(duplicates)s duplicates leads have been merged."
msgstr "تم دمج %(duplicates)s عميل مهتم مطابق. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"%(members_assigned)s leads assigned among %(member_count)s salespersons."
msgstr ""
"تم تعيين %(members_assigned)s عميلاً مهتماً بين %(member_count)s مندوب "
"مبيعات. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "%s's opportunity"
msgstr "فرصة %s "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "<b>Create your first opportunity.</b>"
msgstr "<b>أنشئ فرصتك الأولى.</b> "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid ""
"<b>Drag &amp; drop opportunities</b> between columns as you progress in your"
" sales cycle."
msgstr ""
"قم بنقل الفرص ما بين الأعمدة المختلفة باستخدام <b>السحب والإفلات</b> بينما "
"تعمل في دورة مبيعاتك. "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "<b>Write a few letters</b> to look for a company, or create a new one."
msgstr "<b>قم بكتابة بضع أحرف</b> للبحث عن شركة أو إنشاء شركة جديدة. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<i class=\"fa fa-gear\" role=\"img\" title=\"Switch to automatic "
"probability\" aria-label=\"Switch to automatic probability\"/>"
msgstr ""
"<i class=\"fa fa-gear\" role=\"img\" title=\"الانتقال إلى الاحتمال التلقائي "
"\" aria-label=\"Switch to automatic probability\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "<i class=\"fa fa-info-circle me-2\" title=\"Assigned Lead Count\"/>"
msgstr ""
"<i class=\"fa fa-info-circle me-2\" title=\"عدد العملاء المهتمين المسندين "
"\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_partner_kanban_view
msgid ""
"<i class=\"fa fa-star me-1\" aria-label=\"Opportunities\" role=\"img\" "
"title=\"Opportunities\"/>"
msgstr ""
"<i class=\"fa fa-star me-1\" aria-label=\"Opportunities\" role=\"img\" "
"title=\"الفرص \"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<i class=\"oi oi-arrow-right\"/>\n"
"                                Install Extension"
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<i title=\"Update now\" role=\"img\" aria-label=\"Update now\" class=\"fa "
"fa-fw fa-refresh\"/>"
msgstr ""
"<i title=\"التحديث الآن \" role=\"img\" aria-label=\"Update now\" class=\"fa"
" fa-fw fa-refresh\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer email will also be updated.\" "
"invisible=\"not partner_email_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"عندما تقوم بحفظ هذا التغيير، سيتم تحديث بريد العميل الإلكتروني "
"أيضاً. \" invisible=\"not partner_email_update\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"By saving this change, the customer phone number will also be "
"updated.\" invisible=\"not partner_phone_update\"/>"
msgstr ""
"<span class=\"fa fa-exclamation-triangle text-warning oe_edit_only\" "
"title=\"عندما تقوم بحفظ هذا التغيير، سيتم تحديث رقم هاتف العميل أيضاً. \" "
"invisible=\"not partner_email_update\"/>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"duplicate_lead_count &lt; 2\">Similar Leads</span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"duplicate_lead_count &gt; 1\">Similar Lead</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"duplicate_lead_count &lt; 2\">عملاء مهتمين مشابهين</span>\n"
"                                    <span class=\"o_stat_text\" invisible=\"duplicate_lead_count &gt; 1\">عميل مهتم مشابه</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_form
msgid ""
"<span class=\"o_stat_text\" invisible=\"not use_leads\">Leads</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"use_leads\">Opportunities</span>"
msgstr ""
"<span class=\"o_stat_text\" invisible=\"not use_leads\">العملاء المهتمون</span>\n"
"                        <span class=\"o_stat_text\" invisible=\"use_leads\">الفرص</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "<span class=\"o_stat_text\"> Leads</span>"
msgstr "<span class=\"o_stat_text\"> العملاء المهتمين</span> "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey p-2 text-nowrap\"> at </span>"
msgstr "<span class=\"oe_grey p-2 text-nowrap\"> في </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"<span class=\"oe_grey p-2\" groups=\"crm.group_use_recurring_revenues\"> + </span>\n"
"                                        <span class=\"oe_grey p-2\" groups=\"!crm.group_use_recurring_revenues\"> at </span>"
msgstr ""
"<span class=\"oe_grey p-2\" groups=\"crm.group_use_recurring_revenues\"> + </span>\n"
"                                        <span class=\"oe_grey p-2\" groups=\"!crm.group_use_recurring_revenues\">في </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "<span class=\"oe_grey p-2\"> %</span>"
msgstr "<span class=\"oe_grey p-2\"> %</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
msgid "<span class=\"oe_inline\"> (max) </span>"
msgstr "<span class=\"oe_inline\"> (الحد الأقصى) </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
msgid "<span class=\"oe_inline\"> / </span>"
msgstr "<span class=\"oe_inline\"> / </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid ""
"<span> leads assigned this month\n"
"                            on a maximum of </span>"
msgstr ""
"<span> العملاء المهتمون الذين تم إسنادهم هذا الشهر\n"
"                            كحد أقصى </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>Expected Revenues:</span>"
msgstr "<span>المتوقع الإيرادات:</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>Merged the Lead/Opportunity</span>"
msgstr "<span>دمج العميل المحتمل / الفرصة</span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"<span>Periodically assign leads based on rules</span><br/>\n"
"                                <span invisible=\"not crm_use_auto_assignment\">\n"
"                                    All sales teams will use this setting by default unless\n"
"                                    specified otherwise.\n"
"                                </span>"
msgstr ""
"<span>قم بتعيين العملاء المهتمين دورياً حسب القواعد</span><br/>\n"
"                                <span invisible=\"not crm_use_auto_assignment\">\n"
"                                    سوف تستخدم كافة فرق المبيعات هذا الإعداد اقتراضياً إلا إذا\n"
"                                    تم تحديد خلاف ذلك.\n"
"                                </span>"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "<span>into this one.</span>"
msgstr "<span>في هذا.</span>"

#. module: crm
#: model:mail.template,body_html:crm.mail_template_demo_crm_lead
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 24px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: white; padding: 0; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your Lead/Opportunity</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">Interest in your products</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: 48px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hi <t t-out=\"object.partner_id and object.partner_id.name or ''\">Deco Addict</t>,<br/><br/>\n"
"                            Welcome to <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>.\n"
"                            It's great to meet you! Now that you're on board, you'll discover what <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t> has to offer. My name is <t t-out=\"object.user_id.name or ''\">Marc Demo</t> and I'll help you get the most out of Odoo. Could we plan a quick demo soon?<br/>\n"
"                            Feel free to reach out at any time!<br/><br/>\n"
"                            Best,<br/>\n"
"                            <t t-if=\"object.user_id\">\n"
"                                <b><t t-out=\"object.user_id.name or ''\">Marc Demo</t></b>\n"
"                                <br/>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t>\n"
"                                <br/>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <t t-out=\"object.company_id.name or ''\">My Company (San Francisco)</t>\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px; padding: 0 8px 0 8px; font-size:11px;\">\n"
"            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;\"/>\n"
"            <b t-out=\"object.company_id.name or ''\">My Company (San Francisco)</b><br/>\n"
"            <div style=\"color: #999999;\">\n"
"                <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                <t t-if=\"object.company_id.email\">\n"
"                    | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                </t>\n"
"                <t t-if=\"object.company_id.website\">\n"
"                    | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=email\" style=\"color: #875A7B;\">Odoo</a>\n"
"</td></tr>\n"
"</table>\n"
"        "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 24px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: white; padding: 0; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">العميل المهتم/الفرصة الخاصة بك</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.name or ''\">الاهتمام بمنتجاتك</span>\n"
"                </td><td valign=\"middle\" align=\"right\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ object.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: 48px;\" t-att-alt=\"object.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:4px 0px 32px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            أهلاً <t t-out=\"object.partner_id and object.partner_id.name or ''\">ديكو آديكت</t>،<br/><br/>\n"
"                            مرحباً بك في <t t-out=\"object.company_id.name or ''\">شركتي (سان فرانسيسكو)</t>.\n"
"                            من الرائع ملاقاتك! بما أنك على متن رحلتنا، سوف تكتشف ما بوسع <t t-out=\"object.company_id.name or ''\"> شركتي (سان فرانسيسكو)</t> القيام به وتقديمه. اسمي هو <t t-out=\"object.user_id.name or ''\">مارك ديمو</t> وسوف أساعدك في تحقيق الفائدة القصوى من أودو. أيسعنا تنظيم جلسة تجريبية قريباً؟<br/>\n"
"                            تفضل بالتواصل معنا وقتما شئت!<br/><br/>\n"
"                            مع أطيب التحيات،<br/>\n"
"                            <t t-if=\"object.user_id\">\n"
"                                <b><t t-out=\"object.user_id.name or ''\">مارك ديمو</t></b>\n"
"                                <br/>البريد الإلكتروني: <t t-out=\"object.user_id.email or ''\"><EMAIL></t>\n"
"                                <br/>رقم الهاتف: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                <t t-out=\"object.company_id.name or ''\">شركتي (سان فرانسيسكو)</t>\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px; padding: 0 8px 0 8px; font-size:11px;\">\n"
"            <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 4px 0px;\"/>\n"
"            <b t-out=\"object.company_id.name or ''\">شركتي (سان فرانسيسكو)</b><br/>\n"
"            <div style=\"color: #999999;\">\n"
"                <t t-out=\"object.company_id.phone or ''\">******-123-4567</t>\n"
"                <t t-if=\"object.company_id.email\">\n"
"                    | <a t-attf-href=\"'mailto:%s' % {{ object.company_id.email }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.email or ''\"><EMAIL></a>\n"
"                </t>\n"
"                <t t-if=\"object.company_id.website\">\n"
"                    | <a t-attf-href=\"'%s' % {{ object.company_id.website }}\" style=\"text-decoration:none; color: #999999;\" t-out=\"object.company_id.website or ''\">http://www.example.com</a>\n"
"                </t>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    مشغل بواسطة <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=email\" style=\"color: #875A7B;\">أودو</a>\n"
"</td></tr>\n"
"</table>\n"
"        "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"قاموس بايثون الذي سيتم تقييمه لتوفير قيم افتراضية عند إنشاء سجلات جديدة لهذا"
" اللقب. "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_3
msgid ""
"A great tip to boost sales efficiency is to always define a next step on "
"each opportunity. To manage ongoing activities, click on any status of the "
"progress bar to filter opportunities based on their next activities' status."
" Click on the grey area of the progress bar to see all opportunities that "
"have no next activity."
msgstr ""
"نصيحة رائعة لتعزيز كفاءة المبيعات هي أن تقوم دائماً بتحديد الخطوة التالية "
"لكل فرصة. لإدارة الأنشطة الجارية، قم بالضغط على أي حالة في شريط التقدم "
"لتصفية الفرص بناءً على حالة أنشطتها التالية. اضغط على المنطقة الرمادية من "
"شريط التقدم لرؤية كافة الفرص التي ليس لديها نشاط تالٍ. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "A new lead has been created and is not assigned to any team."
msgstr "لقد تم إنشاء عميل مهتم جديد ولم يتم إسناده إلى أي فريق. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "A new lead has been created for the team \"%(team_name)s\"."
msgstr "تم إنشاء عميل مهتم جديد لفريق \"%(team_name)s\" "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Accept Emails From"
msgstr "قبول رسائل البريد من"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction
msgid "Action Needed"
msgstr "إجراء مطلوب"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__active
#: model:ir.model.fields,field_description:crm.field_crm_lead__active
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__active
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__active
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Active"
msgstr "نشط"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__lead_tomerge_ids
msgid "Active Leads"
msgstr "الهملاء المهتمين النشطين "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_ids
#: model:ir.ui.menu,name:crm.crm_activity_report_menu
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activities
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Activities"
msgstr "الأنشطة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activities Analysis"
msgstr "تحليل الأنشطة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_done
msgid "Activities Done Target"
msgstr "هدف الأنشطة المكتملة"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action_team
msgid ""
"Activities marked as Done on Leads will appear here, providing an overview "
"of lead interactions."
msgstr ""
"ستظهر الأنشطة التي تم تعيينها كمنتهية لدى العملاء المهتمين هنا، مما يمنحك "
"نظرة عامة على تفاعلات العملاء المعتمين. "

#. module: crm
#: model:ir.model,name:crm.model_mail_activity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity"
msgstr "النشاط"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__body
msgid "Activity Description"
msgstr "وصف النشاط"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "زخرفة استثناء النشاط"

#. module: crm
#: model:ir.ui.menu,name:crm.mail_activity_plan_menu_config_lead
msgid "Activity Plans"
msgstr "خطط النشاط "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_state
msgid "Activity State"
msgstr "حالة النشاط"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__mail_activity_type_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Activity Type"
msgstr "نوع النشاط"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_icon
msgid "Activity Type Icon"
msgstr "أيقونة نوع النشاط"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_menu_config_activity_types
msgid "Activity Types"
msgstr "أنواع الأنشطة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Activity by"
msgstr "الأنشطة حسب "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.mail_activity_plan_action_lead
msgid ""
"Activity plans are used to assign a list of activities in just a few clicks\n"
"                    (e.g. \"Demo Preparation Process\", \"Qualification\", ...)"
msgstr ""
"تُستخدَم خطط الأنشطة لإسناد قائمة من الأنشطة ببضع نقرات فقط\n"
"                    (مثال: \"عملية تجهيز العرض التوضيحي\"، \"التأهيل\"، ...) "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Add a description..."
msgstr "إضافة وصف..."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Add a qualification step before the creation of an opportunity"
msgstr "أضف خطوة تأهيل قبل إنشاء فرصة "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/views/forecast_kanban/forecast_kanban_column_quick_create.js:0
msgid "Add next %s"
msgstr "إضافة %s التالي "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__assignment_domain
msgid ""
"Additional filter domain when fetching unassigned leads to allocate to the "
"team."
msgstr ""
"نطاق عامل تصفية جديد عند جلب العملاء المهتمين غير المعيّنين لتحيينهم للفريق."
" "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Address"
msgstr "العنوان"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Address:"
msgstr "العنوان: "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_id
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_tree
msgid "Alias"
msgstr "لقب"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_contact
msgid "Alias Contact Security"
msgstr "أمان ألقاب جهات الاتصال "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_domain_id
msgid "Alias Domain"
msgstr "نطاق اللقب "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_domain
msgid "Alias Domain Name"
msgstr "اسم نطاق اللقب "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_full_name
msgid "Alias Email"
msgstr "لقب البريد الإلكتروني "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_name
msgid "Alias Name"
msgstr "اسم اللقب"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_status
msgid "Alias Status"
msgstr "حالة اللقب "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_status
msgid "Alias status assessed on the last message received."
msgstr "حالة اللقب مقيّمة في آخر رسالة مستلمة. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_model_id
msgid "Aliased Model"
msgstr "النموذج الملقب "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "All set. Let’s <b>Schedule</b> it."
msgstr "أصبح كل شيء معداً. <b>فلنقم بجدولته</b>. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Analysis"
msgstr "التحليل"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Apply deduplication"
msgstr "تطبيق إلغاء التكرار"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_recurring_plan_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Archived"
msgstr "مؤرشف"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"As you are a member of no Sales Team, you are showed the Pipeline of the <b>first team by default.</b>\n"
"                                        To work with the CRM, you should <a name=\"%d\" type=\"action\" tabindex=\"-1\">join a team.</a>"
msgstr ""
"بما أنك لست عضواً في أي فريق مبيعات، سيتم إظهار مخطط سير عمل <b>أول فريق مبيعات افتراضياً.</b>\n"
"                                        لتعمل ياستخدام تطبيق إدارة علاقات العملاء، عليك <a name=\"%d\" type=\"action\" tabindex=\"-1\">الانضمام إلى فريق.</a> "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"As you are a member of no Sales Team, you are showed the Pipeline of the <b>first team by default.</b>\n"
"                                        To work with the CRM, you should join a team."
msgstr ""
"بما أنك لست عضواً في أي فريق مبيعات، سيتم إظهار مخطط سير عمل <b>أول فريق مبيعات افتراضياً.</b>\n"
"                                        لتعمل ياستخدام تطبيق إدارة علاقات العملاء، عليك الانضمام إلى فريق. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assign Leads"
msgstr "تعيين العملاء المهتمين "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Assign opportunities to"
msgstr "إسناد الفرص إلى "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Assign salespersons into multiple Sales Teams."
msgstr "تعيين مندوب المبيعات في عدة فرق مبيعات. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Assign these opportunities to"
msgstr "إسناد هذه الفرص إلى "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Assign this opportunity to"
msgstr "إسناد هذه الفرصة إلى "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__author_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Assigned To"
msgstr "مُسنَد إلى "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_open
msgid "Assignment Date"
msgstr "تاريخ الإسناد "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_domain
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_domain
msgid "Assignment Domain"
msgstr "نطاق التعيين "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Assignment Rules"
msgstr "قواعد التعيين "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "Assignment domain for team %(team)s is incorrectly formatted"
msgstr "لم تتم صياغة نطاق التعيين للفريق %(team)s بشكل صحيح "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__lead_id
msgid "Associated Lead"
msgstr "العميل المهتم المرتبط "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_attachment_count
msgid "Attachment Count"
msgstr "عدد المرفقات"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_auto_enabled
msgid "Auto Assignment"
msgstr "التعيين التلقائي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_action
msgid "Auto Assignment Action"
msgstr "إجراء التعيين التلقائي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_interval_type
msgid "Auto Assignment Interval Unit"
msgstr "وحدة الفترة الزمنية الفاصلة للتعيين التلقائي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_run_datetime
msgid "Auto Assignment Next Execution Date"
msgstr "تاريخ التنفيذ التالي للتعيين التلقائي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__automated_probability
msgid "Automated Probability"
msgstr "الاحتمالية المؤتمتة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_max
msgid "Average Leads Capacity (on 30 days)"
msgstr "السعة المتوسطة للعملاء المهتمين (في 30 يوماً) "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_blacklisted
msgid "Blacklist"
msgstr "القائمة السوداء"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile_blacklisted
msgid "Blacklisted Phone Is Mobile"
msgstr "الهاتف الذي تم إدراجه في القائمة السوداء هو هاتف محمول "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_blacklisted
msgid "Blacklisted Phone is Phone"
msgstr "الهاتف الذي تم إدراجه في القائمة السوداء هو هاتف "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Boom! Team record for the past 30 days."
msgstr "بوم! رقم قياسي للفريق خلال الـ30 يوم الماضية. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_bounce
msgid "Bounce"
msgstr "الارتداد "

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_root
#: model_terms:ir.ui.view,arch_db:crm.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "CRM"
msgstr "إدارة علاقات العملاء "

#. module: crm
#: model:ir.model,name:crm.model_crm_activity_report
msgid "CRM Activity Analysis"
msgstr "تحليل أنشطة إدارة علاقات العملاء "

#. module: crm
#: model:ir.model,name:crm.model_crm_recurring_plan
msgid "CRM Recurring revenue plans"
msgstr "خطط الإيرادات المكررة لإدارة علاقات العملاء "

#. module: crm
#: model:ir.model,name:crm.model_crm_stage
msgid "CRM Stages"
msgstr "مراحل إدارة علاقات العملاء "

#. module: crm
#: model:ir.actions.server,name:crm.ir_cron_crm_lead_assign_ir_actions_server
msgid "CRM: Lead Assignment"
msgstr "إدارة علاقات العملاء: تعيين العملاء المهتمين "

#. module: crm
#: model:ir.model,name:crm.model_calendar_event
msgid "Calendar Event"
msgstr "فعالية التقويم "

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_call_demo
msgid "Call for Demo"
msgstr "طلب نسخة تجريبية"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__campaign_id
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Campaign"
msgstr "الحملة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Campaign:"
msgstr "حملة:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Cancel"
msgstr "إلغاء"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_tree
msgid "Channel"
msgstr "القناة"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_6
msgid ""
"Check the Stage Tracker of Leads to identify bottlenecks in your Sales "
"process."
msgstr ""
"تحقق من متتبع مراحل العملاء المهتمين لتحديد نقاط الضعف في عملية المبيعات "
"الخاصة بك. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_leads
msgid ""
"Check this box to filter and qualify incoming requests as leads before "
"converting them into opportunities and assigning them to a salesperson."
msgstr ""
"قم بتحديد هذا المربع لتصفية وتأهيل الطلبات الواردة كعملاء مهتمين قبل تحويلهم"
" لفرص وإسنادهم إلى أحد موظفي المبيعات. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__use_opportunities
msgid "Check this box to manage a presales process with opportunities."
msgstr "قم بتحديد هذا المربع لإدارة عملية ما قبل المبيعات بها فرص. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__city
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "City"
msgstr "المدينة"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report__tag_ids
#: model:ir.model.fields,help:crm.field_crm_lead__tag_ids
msgid ""
"Classify and analyze your lead/opportunity categories like: Training, "
"Service"
msgstr ""
"قم بتصنيف وتحليل العملاء المهتمين/الفرص في فئات، مثل: التدريب، الخدمات "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid ""
"Click on the breadcrumb to go back to your Pipeline. Odoo will save all "
"modifications as you navigate."
msgstr ""
"اضغط على آثار التتبع للعودة إلى مخطط سير عملك. سيقوم أودو بحفظ كافة "
"التعديلات بينما تتنقل. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_closed
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_closed
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Closed Date"
msgstr "التاريخ المقفل "

#. module: crm
#. odoo-python
#: code:addons/crm/wizard/crm_lead_to_opportunity.py:0
msgid "Closed/Dead leads cannot be converted into opportunities."
msgstr "لا يمكن تحويل بيانات العملاء المهتمين المغلقة/الميتة إلى فرص. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_feedback
msgid "Closing Note"
msgstr "ملاحظة الإغلاق "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__color
msgid "Color Index"
msgstr "مؤشر اللون "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__company_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Company"
msgstr "الشركة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_name
msgid "Company Name"
msgstr "اسم الشركة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Company Name:"
msgstr "اسم الشركة: "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Company:"
msgstr "المؤسسة:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Completion Date"
msgstr "تاريخ الإكمال "

#. module: crm
#: model:ir.model,name:crm.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_config
msgid "Configuration"
msgstr "التهيئة "

#. module: crm
#: model_terms:web_tour.tour,rainbow_man_message:crm.crm_tour
msgid "Congrats, best of luck catching such big fish! :)"
msgstr "تهانينا، وحظاً موفقاً في الإمساك بسمكة بهذا الحجم! :) "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Consider leads created as of the:"
msgstr "اعتبار العملاء المهتمين الذين تم إنشاؤهم في "

#. module: crm
#: model:ir.model,name:crm.model_res_partner
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Contact"
msgstr "جهة الاتصال"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Contact Details:"
msgstr "بيانات المتصل:"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Contact Information"
msgstr "معلومات التواصل"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__contact_name
msgid "Contact Name"
msgstr "اسم جهة الاتصال"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Contact:"
msgstr "اتصال:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__name
msgid "Conversion Action"
msgstr "إجراء التحويل"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_conversion
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_conversion
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Conversion Date"
msgstr "تاريخ التحويل"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Conversion Date from Lead to Opportunity"
msgstr "تاريخ التحويل من عميل مهتم إلى فرصة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Conversion Options"
msgstr "خيارات التحويل"

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner_mass
msgid "Convert Lead to Opportunity (in mass)"
msgstr "تحويل العميل المهتم إلى فرصة (بشكل جماعي) "

#. module: crm
#: model:ir.model,name:crm.model_crm_lead2opportunity_partner
msgid "Convert Lead to Opportunity (not in mass)"
msgstr "تحويل العميل المهتم إلى فرصة (بشكل فردي) "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunities"
msgstr "التحويل إلى فرص "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Convert to Opportunity"
msgstr "تحويل إلى فرصة "

#. module: crm
#: model:ir.actions.act_window,name:crm.action_crm_send_mass_convert
msgid "Convert to opportunities"
msgstr "التحويل إلى فرص "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.actions.act_window,name:crm.action_crm_lead2opportunity_partner
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__convert
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__convert
msgid "Convert to opportunity"
msgstr "التحويل إلى فرصة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Convert visitors of your website into leads and perform data enrichment "
"based on their IP address"
msgstr ""
"قم بتحويل زوار صفحة الويب الخاص بك إلى عملاء مهتمين وقم بإثراء البيانات "
"بناءً على عناوين IP الخاصة بهم "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__correct
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__correct
msgid "Correct"
msgstr "صحيحة "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "عدد رسائل البريد الإلكتروني المرتدة لجهة الاتصال هذه"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__country_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__country_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Country"
msgstr "الدولة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_website_crm_iap_reveal
msgid "Create Leads/Opportunities from your website's traffic"
msgstr "قم بإنشاء عملاء مهتمين أو فرص من زيارات موقعك الإلكتروني "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Create Opportunity"
msgstr "إنشاء فرصة"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid "Create a Lead"
msgstr "إنشاء عميل مهتم "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.mail_activity_plan_action_lead
msgid "Create a Lead Activity Plan"
msgstr "إنشاء خطة نشاط للعميل المهتم "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid "Create a Lost Reason"
msgstr "إنشاء سبب للضياع "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_recurring_plan_action
msgid "Create a Recurring Plan"
msgstr "إنشاء خطة متكررة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_mining_in_pipeline
msgid "Create a lead mining request directly from the opportunity pipeline."
msgstr "إنشاء طلب لاستقطاب العملاء المهتمين مباشرة من مخطط سير عمل الفرص. "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__create
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__create
msgid "Create a new customer"
msgstr "إنشاء عميل جديد"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid "Create a new lead"
msgstr "إنشاء عميل مهتم جديد "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "Create an Opportunity"
msgstr "إنشاء فرصة"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "Create an opportunity to start playing with your pipeline."
msgstr "أنشئ فرصة حتى تبدأ باستخدام مخطط سير عملك. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__create_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__create_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__create_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__create_date
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__create_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Created on:"
msgstr "أنشئ في: "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_create_date
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Creation Date"
msgstr "تاريخ الإنشاء"

#. module: crm
#: model:ir.actions.server,name:crm.action_opportunity_forecast
msgid "Crm: Forecast"
msgstr "إدارة علاقات العملاء: التنبؤات "

#. module: crm
#: model:ir.actions.server,name:crm.action_your_pipeline
msgid "Crm: My Pipeline"
msgstr "إدارة علاقات العملاء: مخطط سير عملي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__company_currency
msgid "Currency"
msgstr "العملة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "الرسالة المرتدة المخصصة"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__partner_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_id
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
msgid "Customer"
msgstr "العميل"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Customer Email"
msgstr "بريد العميل الإلكتروني "

#. module: crm
#: model:ir.ui.menu,name:crm.res_partner_menu_customer
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Customers"
msgstr "العملاء"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Date Closed"
msgstr "تاريخ الإغلاق"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__days
msgid "Days"
msgstr "الأيام"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_open
msgid "Days to Assign"
msgstr "أيام حتى التعيين "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__day_close
msgid "Days to Close"
msgstr "أيام الإقفال "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Deadline: %s"
msgstr "الموعد النهائي: %s "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_defaults
msgid "Default Values"
msgstr "القيم الافتراضية"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Define recurring plans and revenues on Opportunities"
msgstr "قم بتحديد الخطط الدورية والإيرادات في الفرص "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Delete"
msgstr "حذف"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__name
msgid "Description"
msgstr "الوصف"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Did you know emails sent to"
msgstr "هل تعلم أن رسائل البريد الإلكتروني المرسلة إلى"

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid ""
"Did you know emails sent to a Sales Team alias generate opportunities in "
"your pipeline?"
msgstr ""
"هل تعلم أن رسائل البريد الإلكتروني المرسلة إلى ألقاب فرق المبيعات تُنشئ "
"فرصاً في مخطط سير عملك؟ "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_4
msgid ""
"Did you know you can search a company by name or VAT number to instantly "
"fill in all its data? Odoo autocompletes everything for you: logo, address, "
"company size, business information, social media accounts, etc."
msgstr ""
"هل تعلم أنه بوسعك البحث عن شركة عن طريق الاسم أو الرقم الضريبي لملء كافة "
"بياناتها فورياً؟ يقوم أودو بإكمال كل شيء تلقائياً من أجلك: شعارك، عنوانك، "
"حجم شركتك، معلومات عملك، حسابات مواقع التواصل الاجتماعي، وما إلى ذلك. "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_5
msgid ""
"Did you know you can turn a list of opportunities into a map view, using the"
" top-right map icon? A lot of screens in Odoo can be turned into a map: "
"tasks, contacts, delivery orders, etc."
msgstr ""
"هل تعلم أنه باستطاعتك تحويل قائمة من العملاء المهتمين إلى عرض خريطة باستخدام"
" أيقونة الخريطة في أعلى يسار الصفحة؟ يمكن تحويل العديد من الشاشات في أودو "
"إلى خريطة: المهام، جهات الاتصال، أوامر التوصيل، وما إلى ذلك. "

#. module: crm
#: model:ir.model,name:crm.model_digest_digest
msgid "Digest"
msgstr "الموجز "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__display_name
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__display_name
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__display_name
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__display_name
#: model:ir.model.fields,field_description:crm.field_crm_stage__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: crm
#. odoo-python
#: code:addons/crm/models/digest.py:0
msgid "Do not have access, skip this data for user's digest email"
msgstr "لا تملك صلاحيات الوصول. تخط هذه البيانات لبريد الملخص للمستخدم. "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__nothing
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__nothing
msgid "Do not link to a customer"
msgstr "عدم الربط بعميل "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_member_view_form
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Domain"
msgstr "النطاق"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Drag your opportunity to <b>Won</b> when you get the deal. Congrats!"
msgstr "قم بسحب الفرصة إلى <b>رابحة</b> عندما تحصل على الصفقة. تهانينا! "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Duration: %s"
msgstr "المدة: %s "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_forecast
msgid "Easily set expected closing dates and overview your revenue streams."
msgstr ""
"قم بتعيين تواريخ الإقفال المتوقعة بكل سهولة وألقِ نظرة عامة على تدفقات "
"إيراداتك. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "Edit"
msgstr "تحرير"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_from
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_email
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "Email Alias"
msgstr "لقب البريد الإلكتروني"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_domain_criterion
msgid "Email Domain Criterion"
msgstr "فئة نطاق البريد الإلكتروني "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_state
msgid "Email Quality"
msgstr "جودة البريد الإلكتروني"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_cc
msgid "Email cc"
msgstr "نسخة البريد الإلكتروني "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Email cc:"
msgstr "نسخة البريد الإلكتروني: "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "نطاق البريد الإلكتروني مثال: 'example.com' في '<EMAIL>' "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Email:"
msgstr "البريد الإلكتروني:"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_type_demo_email_with_template
msgid "Email: Welcome Demo"
msgstr "البريد الإلكتروني: التجربة المجانية الترحيبية "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__auto
msgid "Enrich all leads automatically"
msgstr "قم بإثراء كافة العملاء المهتمين تلقائياً "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__lead_enrich_auto
msgid "Enrich lead automatically"
msgstr "إثراء العميل المهتم تلقائياً "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__lead_enrich_auto__manual
msgid "Enrich leads on demand only"
msgstr "إثراء العملاء المهتمين عند الطلب فقط "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_enrich
msgid ""
"Enrich your leads automatically with company data based on their email "
"address."
msgstr ""
"قم بإثراء عملائك المهتمين تلقائياً ببيانات الشركة بناءً على عناوين بريدهم "
"الإلكتروني. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Enrich your leads with company data based on their email addresses"
msgstr ""
"قم بإثراء عملائك المهتمين باستخدام بيانات الشركة بناءً على عناوين بريدهم "
"الإلكتروني. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__requirements
msgid ""
"Enter here the internal requirements for this stage (ex: Offer sent to "
"customer). It will appear as a tooltip over the stage's name."
msgstr ""
"أدخل هنا المتطلبات الداخلية لهذه المرحلة (مثلًا: أن يُرسل العرض للعميل). "
"ستظهر هذه المتطلبات كتلميح فوق اسم المرحلة."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__date_deadline
msgid "Estimate of the date on which the opportunity will be won."
msgstr "التاريخ المتوقع لربح هذه الفرصة."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_all_assigned_month_exceeded
msgid "Exceed monthly lead assignement"
msgstr "تخطَّ الحد الأقصى الشهري لإسناد العملاء المهتمين "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__date_deadline
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_deadline
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_search_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Expected Closing"
msgstr "إقفال متوقع"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Expected Closing:"
msgstr "الإغلاق المتوقع: "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_monthly
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected MRR"
msgstr "الإيرادات الشهرية المتكررة المتوقعة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__expected_revenue
msgid "Expected Revenue"
msgstr "الإيرادات المتوقعة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Expected Revenues"
msgstr "الإيرادات المتوقعة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Extended Filters"
msgstr "عوامل التصفية التفصيلية "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Info"
msgstr "معلومات إضافية"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Extra Information"
msgstr "معلومات إضافية"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Extra fields..."
msgstr "حقول إضافية... "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__field_id
msgid "Field"
msgstr "حقل"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__name
msgid "Field Label"
msgstr "بطاقة عنوان الحقل "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_sanitized
msgid ""
"Field used to store sanitized phone number. Helps speeding up searches and "
"comparisons."
msgstr ""
"حقل مُستخدَم لتخزين أرقام الهواتف السليمة، مما يساعد على تسريع عمليات البحث "
"والمقارنات. "

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency_field
msgid "Fields that can be used for predictive lead scoring computation"
msgstr "حقول يمكن استخدامها لاحتساب تصنيف العملاء المهتمين التوقعي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__fold
msgid "Folded in Pipeline"
msgstr "مطوي في دورة المبيعات"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_followup_quote
msgid "Follow-up Quote"
msgstr "عرض السعر المتابع "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_follower_ids
msgid "Followers"
msgstr "المتابعين"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_partner_ids
msgid "Followers (Partners)"
msgstr "المتابعين (الشركاء) "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "أيقونة من Font awesome مثال: fa-tasks "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_1
msgid ""
"For a sales team, there is nothing worse than being dry on leads. "
"Fortunately, in just a few clicks, you can generate leads specifically "
"targeted to your needs: company size, industry, etc. To help you test the "
"feature, we offer you 200 credits for free."
msgstr ""
"بالنسبة لفريق المبيعات، لا يوجد أسوأ من أن لا تتمكن من إبرام أي صفقات. لحسن "
"الحظ، بإمكانك إنشاء عملاء مهتمين يلائمون احتياجاتك بشكل خاص وببضع نقرات فقط:"
" حجم الشركة، والمجال، وما إلى ذلك. لنساعدَك في اختبار هذه الخاصيو، إليك 200 "
"رصيد مجاناً. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__force_assignment
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__force_assignment
msgid "Force assignment"
msgstr "فرض التعيين "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_forecast
#: model:ir.ui.menu,name:crm.crm_menu_forecast
msgid "Forecast"
msgstr "المتوقع "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot_forecast
msgid "Forecast Analysis"
msgstr "تحليل التنبؤ "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "From %(source_name)s"
msgstr "من %(source_name)s "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "From %(source_name)s: %(source_subject)s"
msgstr "من %(source_name)s: %(source_subject)s"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Future Activities"
msgstr "الأنشطة المستقبلية"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__module_crm_iap_mine
msgid "Generate new leads based on their country, industries, size, etc."
msgstr ""
"قم بإنشاء عملاء مهتمين جدد بناءً على إلى دولهم ومجالاتهم وأحجامهم وما إلى "
"ذلك. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Generate new leads based on their country, industry, size, etc."
msgstr ""
"قم بإنشاء العملاء المهتمين بناءً على دولهم وصناعتهم وحجمهم وما إلى ذلك. "

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_lost
msgid "Get Lost Reason"
msgstr "معرفة سبب الضياع "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Give your team the requirements to move an opportunity to this stage."
msgstr "امنح فريقك متطلبات نقل فرصة لهذه المرحلة."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Go, go, go! Congrats for your first deal."
msgstr "مرحى، مرحى، مرحى! تهانينا على صفقتك الأولى. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Group By"
msgstr "تجميع حسب"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__has_message
msgid "Has Message"
msgstr "يحتوي على رسالة "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__2
msgid "High"
msgstr "مرتفع"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__hours
msgid "Hours"
msgstr "ساعات "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__id
#: model:ir.model.fields,field_description:crm.field_crm_lead__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__id
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__id
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__id
#: model:ir.model.fields,field_description:crm.field_crm_stage__id
msgid "ID"
msgstr "المُعرف"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"معرف السجل الأصل الذي يحتوي على اللقب (مثال: المشروع الذي يحتوي على اللقب "
"لإنشاء المهمة) "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_exception_icon
msgid "Icon"
msgstr "الأيقونة"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "الأيقونة للإشارة إلى النشاط المستثنى. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner__force_assignment
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__force_assignment
msgid ""
"If checked, forces salesman to be updated on updated opportunities even if "
"already set."
msgstr ""
"إذا كان محدداً، فإنه يفرض تحديث مندوب المبيعات في الفرص المحدّثة، حتى إذا "
"كان قد تم تعيينها بالفعل. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction
msgid "If checked, new messages require your attention."
msgstr "إذا كان محددًا، فهناك رسائل جديدة عليك رؤيتها. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "إذا كان محددًا، فقد حدث خطأ في تسليم بعض الرسائل."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"إذا كان محدداً، سوف يتم إرسال هذا المحتوى تلقائياً إلى المستخدمين غير المصرح"
" لهم عوضاً عن الرسالة الافتراضية. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__is_blacklisted
#: model:ir.model.fields,help:crm.field_crm_lead__partner_is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"إذا كان البريد الإلكتروني في القائمة السوداء، لن يستقبل صاحبه أي مراسلات "
"جماعية من أي قائمة"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_sanitized_blacklisted
msgid ""
"If the sanitized phone number is on the blacklist, the contact won't receive"
" mass mailing sms anymore, from any list"
msgstr ""
"إذا كان رقم الهاتف السليم في القائمة السوداء، لن تستلم جهة الاتصال الرسائل "
"النصية القصيرة الجماعية من أي قائمة بعد الآن "

#. module: crm
#: model:ir.ui.menu,name:crm.menu_import_crm
msgid "Import & Synchronize"
msgstr "استيراد ومزامنة "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Import Template for Leads & Opportunities"
msgstr "استيراد قالب للعملاء المهتمين والفرص "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Inactive"
msgstr "غير نشط "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__email_state__incorrect
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__phone_state__incorrect
msgid "Incorrect"
msgstr "غير صحيح"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__mobile_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a mobile number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"يشير إلى ما إذا كان رقم الهاتف السليم المدرج في القائمة السوداء أم لا.  "
"يساعد في تمييز أي الأرقام تم إدراجها في القائمة السوداء عندما يمون هناك "
"حقلان لرقم الهاتف والهاتف المحمول في إحدى النماذج. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__phone_blacklisted
msgid ""
"Indicates if a blacklisted sanitized phone number is a phone number. Helps "
"distinguish which number is blacklisted             when there is both a "
"mobile and phone field in a model."
msgstr ""
"يشير إلى ما إذا كان رقم الهاتف السليم رقم هاتف أم لا. يساعد في تمييز أي "
"الأرقام تم إدراجها في القائمة السوداء عندما يكون هناك حقلان لرقم الهاتف "
"والهاتف المحمول في أحد النماذج. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Internal Notes"
msgstr "ملاحظات داخلية"

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_interval_type
msgid "Interval type between each cron run (e.g. each 2 days or each 2 hours)"
msgstr "نوع الفترة الزمنية بين كل تشغيل cron (مثال: كل يومين 2 لكل ساعتين 2) "

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
msgid ""
"Invalid repeat frequency. Consider changing frequency type instead of using "
"large numbers."
msgstr ""
"تواتر التكرار غير صالح. جرب تغيير نوع التواتر عوضاً عن استخدام أرقام كبيرة. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_is_follower
msgid "Is Follower"
msgstr "متابع"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_partner_visible
msgid "Is Partner Visible"
msgstr "هل الشريك مرئي"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__is_won
msgid "Is Won Stage?"
msgstr "مرحلة ربح الصفقة؟ "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__is_automated_probability
msgid "Is automated probability?"
msgstr "احتمال آلي؟"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""
"JSON يقوم بتخطيط المعرفات من حقل متعدد إلى واحد إلى الثواني المستغرقة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__function
msgid "Job Position"
msgstr "المنصب الوظيفي"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Job Position:"
msgstr "المنصب الوظيفي:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created_value
msgid "Kpi Crm Lead Created Value"
msgstr ""
"قيمة العميل المهتم المنشأ في مؤشر الأداء الرئيسي لإدارة علاقات العملاء "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won_value
msgid "Kpi Crm Opportunities Won Value"
msgstr "قيمة الفرص المحسومة في مؤشر الأداء الرئيسي لإدارة علاقات العملاء "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_active_count
msgid "Lang Active Count"
msgstr "عدد اللغات النشطة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_id
msgid "Language"
msgstr "اللغة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Language:"
msgstr "اللغة: "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_automation_last
msgid "Last Action"
msgstr "الإجراء الأخير "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Last Automation:"
msgstr "آخر عملية أتمتة: "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Last Meeting"
msgstr "آخر اجتماع "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__date_last_stage_update
msgid "Last Stage Update"
msgstr "آخر تحديث للمرحلة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__write_uid
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency_field__write_date
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__write_date
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__write_date
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__write_date
#: model:ir.model.fields,field_description:crm.field_crm_stage__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Late Activities"
msgstr "الأنشطة المتأخرة"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__lead
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__lead
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Lead"
msgstr "عميل مهتم "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_enabled
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_enabled
msgid "Lead Assign"
msgstr "تعيين العميل المهتم "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "Lead Assignment requested by %(user_name)s"
msgstr "تم طلب تعيين العميل المهتم من قِبَل %(user_name)s "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_max
msgid "Lead Average Capacity"
msgstr "متوسط سعة العميل المهتم "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Enrichment"
msgstr "إثراء العميل المهتم "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Generation"
msgstr "إنشاء العملاء المهتمين "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Lead Mining"
msgstr "استقطاب العملاء المهتمين "

#. module: crm
#: model:ir.actions.act_window,name:crm.mail_activity_plan_action_lead
msgid "Lead Plans"
msgstr "خطط العملاء المهتمين "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__lead_properties_definition
msgid "Lead Properties"
msgstr "خصائص العملاء المهتمين "

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_scoring_frequency
msgid "Lead Scoring Frequency"
msgstr "تواتر تصنيف العملاء المهتمين "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields
msgid "Lead Scoring Frequency Fields"
msgstr " حقول تواتر تصنيف العملاء المهتمين "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_fields_str
msgid "Lead Scoring Frequency Fields in String"
msgstr " حقول تواتر تصنيف العملاء المهتمين في السلسلة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date
msgid "Lead Scoring Starting Date"
msgstr "تاريخ بداية تصنيف العملاء المهتمين "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_start_date_str
msgid "Lead Scoring Starting Date in String"
msgstr "تاريخ بداية تصنيف العملاء المهتمين في السلسلة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_tree
msgid "Lead Tags"
msgstr "علامات تصنيف العميل المهتم "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_member__lead_day_count
msgid "Lead assigned to this member this last day (lost one excluded)"
msgstr ""
"العملاء المهتمون الذين تم إسنادهم إلى هذا العضو خلال الأيام الماضية "
"(باستثناء الفرص الضائعة) "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team_member__lead_month_count
msgid "Lead assigned to this member those last 30 days"
msgstr "العميل المهتم المعيّن لهذا العضو خلال الـ 30 يوماً الماضية "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Lead or Opportunity"
msgstr "عميل مهتم أو فرصة "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"Lead/Opportunities automatic assignment is limited to managers or "
"administrators"
msgstr ""
"يقتصر التعيين التلقائي للعملاء المهتمين/الفرص على المدراء أو المسؤولين "

#. module: crm
#: model:ir.model,name:crm.model_crm_lead
msgid "Lead/Opportunity"
msgstr "عميل مهتم/فرصة "

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_create
msgid "Lead/Opportunity created"
msgstr "تم إنشاء عميل مهتم/فرصة "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lost_reason.py:0
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_lead
#: model:ir.actions.act_window,name:crm.crm_lead_all_leads
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lead_ids
#: model:ir.model.fields,field_description:crm.field_crm_team__use_leads
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_lead
#: model:ir.ui.menu,name:crm.crm_menu_leads
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_kanban
msgid "Leads"
msgstr "العملاء المهتمين "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__lead_month_count
msgid "Leads (30 days)"
msgstr "العملاء المهتمين (30 يوماً) "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team_member__lead_day_count
msgid "Leads (last 24h)"
msgstr "العملاء المهتمون (آخر 24 ساعة) "

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_lead_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph_lead
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot_lead
msgid "Leads Analysis"
msgstr "تحليل العملاء المهتمين "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_lead_salesteam
msgid ""
"Leads Analysis allows you to check different CRM related information like "
"the treatment delays or number of leads per state. You can sort out your "
"leads analysis by different groups to get accurate grained analysis."
msgstr ""
"يتيح لك تحليل العملاء المهتمين متابعة معلومات مختلفة ذات صلة بإدارة علاقات "
"العملاء، مثل تأخيرات المعالجة أو عدد العملاء المهتمين في كل مرحلة. يمكنك فرز"
" تحليلات عملائك المهتمين إلى مجموعات مختلفة من الفلاتر للوصول إلى مستوى "
"التحليل المناسب لك. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "Leads Assigned"
msgstr "العملاء المهتمين المعينين "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lost_reason__leads_count
msgid "Leads Count"
msgstr "عدد العملاء المهتمين "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_calendar_view_leads
msgid "Leads Generation"
msgstr "إنشاء العملاء المهتمين "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_all_leads
msgid ""
"Leads are the qualification step before the creation of an opportunity."
msgstr "يعد العملاء المهتمون الخطوة المُؤهِّلة قبل إنشاء فرصة. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_activity
msgid "Leads or Opportunities"
msgstr "العملاء المهتمين أو الفرص "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are assigned to me"
msgstr "العملاء المهتمون الذين تم إسنادهم إلي "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Leads that are not assigned"
msgstr "العملاء المهتمون الذين لم يتم تعيينهم "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid ""
"Leads that you selected that have duplicates. If the list is empty, it means"
" that no duplicates were found"
msgstr ""
"العملاء المهتمين ذوي البيانات المكررة. إذا كانت القائمة فارغة، فهذا يعني أنه"
" لا توجد أي بيانات مكررة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner_mass
msgid "Leads with existing duplicates (for information)"
msgstr "العملاء المهتمين ذوي البيانات المكررة (لأغراض المعلومات) "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__opportunity_ids
msgid "Leads/Opportunities"
msgstr "العملاء المهتمين/الفرص "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__crm_lead_count
msgid "Leads/Opportunities count"
msgstr "عدد العملاء المهتمين/الفرص "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Let's <b>Schedule an Activity.</b>"
msgstr "فلنقم <b>بجدولة نشاط.</b> "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_activity_report_action_team
msgid "Let's get to work!"
msgstr "فلبدأ بالعمل! "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Let’s have a look at an Opportunity."
msgstr "فلنقم بإلقاء نظرة على إحدى الفرص. "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__action__exist
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__exist
msgid "Link to an existing customer"
msgstr "الربط بعميل موجود بالفعل "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_id
msgid ""
"Linked partner (optional). Usually created when converting the lead. You can"
" find a partner by its Name, TIN, Email or Internal Reference."
msgstr ""
"الشريك المرتبط (اختياري). يُنشأ عادةً عند تحويل العميل المهتم. باستطاعتكم "
"إيجاد الشريك عن طريق الاسم أو الرقم التعريفي لدافع الضرائب، أو البريد "
"الإلكتروني، أو عن طريق مرجع داخلي. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "الكشف عن الوارد على أساس الجزء المحلي"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lang_code
msgid "Locale Code"
msgstr "الرمز المحلي"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_my_activities
msgid "Looks like nothing is planned."
msgstr "يبدو أنه ليس لديك أي خطط. "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid ""
"Looks like nothing is planned. :(<br><br><i>Tip: Schedule activities to keep"
" track of everything you have to do!</i>"
msgstr ""
"يبدو أنه ليس لديك أي خطط. :(<br><br><i>نصيحة: قم بجدولة الأنشطة لتتمكن من "
"متابعة كافة المهام التي عليك القيام بها!</i> "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_kanban_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost"
msgstr "ضائع "

#. module: crm
#. odoo-python
#: code:addons/crm/wizard/crm_lead_lost.py:0
msgid "Lost Comment"
msgstr "تعليق بشأن الخسارة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__lost_count
msgid "Lost Count"
msgstr "عدد الفرص الضائعة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Lost Lead"
msgstr "فرصة عميل مهتم ضائعة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lost_reason_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_lost__lost_reason_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Lost Reason"
msgstr "سبب الضياع "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Lost Reason:"
msgstr "سبب الضياع: "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lost_reason_action
#: model:ir.ui.menu,name:crm.menu_crm_lost_reason
msgid "Lost Reasons"
msgstr "أسباب الضياع "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__0
msgid "Low"
msgstr "منخفض"

#. module: crm
#: model:mail.activity.type,name:crm.mail_activity_demo_make_quote
msgid "Make Quote"
msgstr "إنشاء عرض سعر"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Make and receive calls from Odoo with Ringover's dialer. Track calls, SMS "
"messages, and get AI-powered transcripts of your conversations."
msgstr ""

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Manage Recurring Plans"
msgstr "إدارة الخطط المتكررة "

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_action
msgid ""
"Manual assign allow to trigger assignment from team form view using an "
"action button. Automatic configures a cron running repeatedly assignment in "
"all teams."
msgstr ""
"يتيح التعيين اليدوي تشغيل التعيين من طريقة عرض الاستمارة الخاصة بالفريق باستخدام زر الإجراء. \n"
"يقوم التعيين الآلي بتهيئة تشغيل cron باستمرار للتعيين في كافة الفِرَق. "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_action__manual
msgid "Manually"
msgstr "يدويًا"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
#: model:ir.actions.act_window,name:crm.crm_lead_lost_action
msgid "Mark Lost"
msgstr "تعيين كخسارة"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Mark Won"
msgstr "تعيين كفوز"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "Mark as Lost"
msgstr "تعيين كمفقود"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as lost"
msgstr "التعيين كضائعة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mark as won"
msgstr "التعيين كرابحة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Marketing"
msgstr "التسويق"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Marketing:"
msgstr "تسويق:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__medium_id
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__1
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Medium"
msgstr "متوسط "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Medium:"
msgstr "متوسط:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__meeting_display_date
msgid "Meeting Display Date"
msgstr "تاريخ عرض الإجتماع "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__meeting_display_label
msgid "Meeting Display Label"
msgstr "عنوان شاشة عرض الاجتماع "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Meeting scheduled at %s"
msgstr "تمت جدولة الاجتماع في %s "

#. module: crm
#: model:ir.actions.act_window,name:crm.act_crm_opportunity_calendar_event_new
#: model:ir.model.fields,field_description:crm.field_crm_lead__calendar_event_ids
msgid "Meetings"
msgstr "الاجتماعات"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team_member.py:0
msgid ""
"Member assignment domain for user %(user)s and team %(team)s is incorrectly "
"formatted"
msgstr ""
"لم تتم صياغة نطاق تعيين العضو للمستخدم %(user)s والفريق %(team)s بشكل صحيح "

#. module: crm
#: model:ir.actions.act_window,name:crm.action_merge_opportunities
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge"
msgstr "دمج"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.merge_opportunity_form
msgid "Merge Leads/Opportunities"
msgstr "دمج العملاء المهتمين/الفرص "

#. module: crm
#: model:ir.model,name:crm.model_crm_merge_opportunity
msgid "Merge Opportunities"
msgstr "دمج الفرص"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead2opportunity_partner_mass__deduplicate
msgid "Merge with existing leads/opportunities of each partner"
msgstr "دمج مع العملاء المهتمين/الفرص الموجودة بالفعل لكل شريك "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner__name__merge
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__name__merge
msgid "Merge with existing opportunities"
msgstr "دمج مع فرص موجودة بالفعل "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error
msgid "Message Delivery error"
msgstr "خطأ في تسليم الرسائل"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_ids
msgid "Messages"
msgstr "الرسائل"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__minutes
msgid "Minutes"
msgstr "الدقائق"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__mobile
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Mobile"
msgstr "الهاتف المحمول"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Mobile:"
msgstr "الجوال:"

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_monthly
msgid "Monthly"
msgstr "شهرياً"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__assignment_max
msgid "Monthly average leads capacity for all salesmen belonging to the team"
msgstr ""
"متوسط السعة الشهرية للعملاء المهتمين لكافة مندوبي المنتمين التابعين للفريق "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__is_membership_multi
msgid "Multi Teams"
msgstr "فِرَق متعددة "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_my_activities
#: model:ir.ui.menu,name:crm.crm_lead_menu_my_activities
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
msgid "My Activities"
msgstr "أنشطتي"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "الموعد النهائي لنشاطاتي "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "My Deadline"
msgstr "مواعيدي النهائية "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "My Leads"
msgstr "عملائي المهتمين "

#. module: crm
#: model:ir.ui.menu,name:crm.menu_crm_opportunities
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "My Pipeline"
msgstr "مخطط سير عملي "

#. module: crm
#: model:crm.stage,name:crm.stage_lead1
msgid "New"
msgstr "جديد"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_open_lead_form
msgid "New Lead"
msgstr "عميل مهتم جديد "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_lead_created
msgid "New Leads"
msgstr "العملاء المهتمون الجدد "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid "New Opportunities"
msgstr "الفرص الجديدة"

#. module: crm
#: model:ir.actions.act_window,name:crm.action_opportunity_form
msgid "New Opportunity"
msgstr "فرصة جديدة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "الفعالية التالية في تقويم الأنشطة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "الموعد النهائي للنشاط التالي"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_summary
msgid "Next Activity Summary"
msgstr "ملخص النشاط التالي"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_type_id
msgid "Next Activity Type"
msgstr "نوع النشاط التالي"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Next Meeting"
msgstr "الاجتماع التالي "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Next Run"
msgstr "التشغيل التالي"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "No"
msgstr "لا"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "No Meeting"
msgstr "لا يوجد اجتماع "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "No Subject"
msgstr "بلا موضوع"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No allocated leads to %(team_name)s team and its salespersons because no "
"unassigned lead matches its domain."
msgstr ""
"لا يوجد عملاء مهتمون مسندون إلى الفريق %(team_name)s ومندوبي مبيعاته لأنه لا"
" يوجد عميل مهتم غير مسند مطابق لنطاقه. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No allocated leads to %(team_name)s team because it has no capacity. Add "
"capacity to its salespersons."
msgstr ""
"لا يوجد عملاء مهتمون مسندون إلى الفريق %(team_name)s لعدم وجود سعة. قم "
"بإضافة سعة لمندوبي المبيعات. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No allocated leads to any team or salesperson. Check your Sales Teams and "
"Salespersons configuration as well as unassigned leads."
msgstr ""
"لا يوجد عملاء مهتمون مسندون إلى أي فريق أو مندوب مبيعات. تحقق من تهيئة فريق "
"مبيعاتك ومندوبي مبيعاتك بالإضافة إلى العملاء المهتمين غير المسندين. "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "No data found!"
msgstr "لم يتم العثور على أي بيانات! "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No lead assigned to salespersons because no unassigned lead matches their "
"domains."
msgstr ""
"لم يتم إسناد أي عملاء مهتمين إلى مندوبي المبيعات لأنه لا يوجد أي عميل مهتم "
"غير مسند يطابق نطاقاتهم. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No new lead allocated to %(team_name)s team because no unassigned lead "
"matches its domain."
msgstr ""
"لا يوجد أي عملاء مهتمين جدد مسندين للفريق %(team_name)s لعدم وجود عميل مهتم "
"غير مسند مطابق لهذا النطاق. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
msgid ""
"No new lead allocated to the teams because no lead match their domains."
msgstr ""
"لا يوجد أي عملاء مهتمين جدد مسندين إلى الفِرَق لعدم وجود عميل مهتم يطابق "
"نطاقاتهم. "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_forecast
msgid "No opportunity to display!"
msgstr "لا توجد فرصة لعرضها! "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "No salesperson"
msgstr "لا يوجد مندوب مبيعات "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__email_normalized
msgid "Normalized Email"
msgstr "البريد الإلكتروني الطبيعي"

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_3
msgid "Not enough stock"
msgstr "لا توجد بضاعة كافية في المخزون "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__description
msgid "Notes"
msgstr "الملاحظات"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Notes:"
msgstr "الملاحظات: "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Now, <b>add your Opportunity</b> to your Pipeline."
msgstr "الآن، <b>قم بإضافة الفرصة</b> إلى مخطط سير عملك. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_needaction_counter
msgid "Number of Actions"
msgstr "عدد الإجراءات"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_error_counter
msgid "Number of errors"
msgstr "عدد الأخطاء "

#. module: crm
#: model:ir.model.fields,help:crm.field_res_config_settings__crm_auto_assignment_interval_number
msgid ""
"Number of interval type between each cron run (e.g. each 2 days or each 4 "
"days)"
msgstr ""
"عدد أنواع الفترات الزمنية بين كل تشغيل cron (مثال: كل يومين 2 أو كل 4 أيام) "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__lead_all_assigned_month_count
msgid "Number of leads and opportunities assigned this last month."
msgstr "عدد العملاء المهتمين والفرص التي تم تعيينها في الشهر الماضي. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "عدد الرسائل التي تتطلب اتخاذ إجراء"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "عدد الرسائل الحادث بها خطأ في التسليم"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"Odoo helps you keep track of your sales pipeline to follow\n"
"                    up potential sales and better forecast your future revenues."
msgstr ""
"يساعدك أودو على مراقبة مخطط سير عملك لتتمكن من تتبع\n"
"                    مبيعاتك المحتملة والتنبؤ بإيراداتك المستقبلية بدقة أكثر. "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_2
msgid ""
"Odoo's artificial intelligence engine predicts the success rate of each "
"opportunity based on your history. You can always update the success rate "
"manually, but if you let Odoo do the job the score is updated while the "
"opportunity moves forward in your sales cycle."
msgstr ""
"يتوقع محرك الذكاء الاصطناعي لدى أودو نسبة النجاح لكل فرصة بناءً على سجلك. "
"بإمكانك تحديث نسبة النجاح يدوياً متى شئت، ولكن إذا تركت أودو يتولى هذه "
"المهمة، فسوف يقوم بتحديثها بينما تمضي الفرصة قدماً في دورة مبيعاتك. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Ongoing"
msgstr "جاري"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Open Opportunities"
msgstr "فرص مفتوحة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Open Opportunity"
msgstr "فرصة مفتوحة"

#. module: crm
#: model:ir.model,name:crm.model_crm_lost_reason
msgid "Opp. Lost Reason"
msgstr "سبب ضياع الفرصة "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_case_form_view_salesteams_opportunity
#: model:ir.actions.act_window,name:crm.crm_lead_opportunities
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__duplicated_lead_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__duplicated_lead_ids
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_ids
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_ids
#: model:ir.ui.menu,name:crm.menu_crm_config_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:crm.view_crm_lead2opportunity_partner
#: model_terms:ir.ui.view,arch_db:crm.view_partners_form_crm1
msgid "Opportunities"
msgstr "الفرص"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Opportunities Analysis"
msgstr "تحليل الفرص"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.action_report_crm_opportunity_salesteam
msgid ""
"Opportunities Analysis gives you an instant access to your opportunities "
"with information such as the expected revenue, planned cost, missed "
"deadlines or the number of interactions per opportunity. This report is "
"mainly used by the sales manager in order to do the periodic review with the"
" channels of the sales pipeline."
msgstr ""
"يمنحك تحليل الفرص إمكانية الوصول الفوري لفرصك مع إتاحة معلومات كالإيرادات "
"المتوقعة، والتكلفة المخطط لها، والمواعيد النهائية الفائتة أو عدد تفاعلات كل "
"فرصة. يُستخدم هذا التقرير بشكل أساسي من قِبَل مدير المبيعات لإجراء المراجعة "
"الدورية مع قنوات مخطط سير العمل الخاص بالمبيعات. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_graph_forecast
msgid "Opportunities Forecast"
msgstr "تنبؤات الفرص "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_amount
msgid "Opportunities Revenues"
msgstr "إيرادات الفرص "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_digest_digest__kpi_crm_opportunities_won
msgid "Opportunities Won"
msgstr "الفرص الرابحة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunities that are assigned to me"
msgstr "الفرص التي تم إسنادها إلي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_calendar_event__opportunity_id
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__name
#: model:ir.model.fields.selection,name:crm.selection__crm_activity_report__lead_type__opportunity
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__type__opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Opportunity"
msgstr "الفرصة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_partner__opportunity_count
#: model:ir.model.fields,field_description:crm.field_res_users__opportunity_count
msgid "Opportunity Count"
msgstr "عدد الفرص "

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_create
#: model:mail.message.subtype,name:crm.mt_salesteam_lead
msgid "Opportunity Created"
msgstr "تم إنشاء فرصة"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_lost
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_lost
msgid "Opportunity Lost"
msgstr "ضاعت الفرصة "

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_restored
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_restored
msgid "Opportunity Restored"
msgstr "تمت استعادة الفرصة"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_stage
msgid "Opportunity Stage Changed"
msgstr "تم تغيير مرحلة الفرصة"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_won
#: model:mail.message.subtype,name:crm.mt_salesteam_lead_won
msgid "Opportunity Won"
msgstr "تم الفوز بالفرصة"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_lost
msgid "Opportunity lost"
msgstr "ضاعت الفرصة "

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_restored
msgid "Opportunity restored"
msgstr "تمت استعادة الفرصة"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_won
msgid "Opportunity won"
msgstr "تم الفوز بالفرصة"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"معرف اختياري لمناقشة (سجل) سيتم إرفاق كافة رسائل البريد الإلكتروني الواردة "
"فيه، حتى لو لم يتم الرد عليها. إذا تم تعيين قيمة له، سيعطل هذا إنشاء السجلات"
" الجديدة بالكامل. "

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_over_3_years
msgid "Over 3 years"
msgstr "خلال 3 سنوات "

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_over_5_years
msgid "Over 5 years "
msgstr "خلال 5 سنوات "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_action_team_overdue_opportunity
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Overdue Opportunities"
msgstr "الفرص المتأخرة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__opportunities_overdue_amount
msgid "Overdue Opportunities Revenues"
msgstr "عائدات الفرص المتأخرة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Overdue Opportunity"
msgstr "فرصة متأخرة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_parent_model_id
msgid "Parent Model"
msgstr "النموذج الأصلي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "معرف مناقشة السجل الرئيسي "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"النموذج الرئيسي الذي يحتفظ بلقب البريد الإلكتروني. ليس بالضرورة أن يكون "
"النموذج الذي يحتفظ بمرجع لقب البريد الإلكتروني هو النموذج المحدد في الحقل "
"alias_model_id (مثال: المشروع (parent_model) والمهمة (model))"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_email_update
msgid "Partner Email will Update"
msgstr "سوف يتم تحديث البريد الإلكتروني للشريك "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_phone_update
msgid "Partner Phone will Update"
msgstr "سوف يتم تحديث رقم هاتف الشريك "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__partner_is_blacklisted
msgid "Partner is blacklisted"
msgstr "تمت إضافة الشريك للقائمة السوداء"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "Phone"
msgstr "رقم الهاتف"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_sanitized_blacklisted
msgid "Phone Blacklisted"
msgstr "تم إدراج رقم الهاتف في القائمة السوداء "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_state
msgid "Phone Quality"
msgstr "جودة الهاتف"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_mobile_search
msgid "Phone/Mobile"
msgstr "الهاتف/الهاتف المتحرك "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Phone:"
msgstr "الهاتف:"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_team.py:0
#: model:ir.actions.act_window,name:crm.crm_lead_action_pipeline
#: model:ir.model.fields,field_description:crm.field_crm_team__use_opportunities
#: model:ir.ui.menu,name:crm.crm_opportunity_report_menu
#: model:ir.ui.menu,name:crm.menu_crm_config_lead
msgid "Pipeline"
msgstr "مخطط سير العمل "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_activity_report_action_team
msgid "Pipeline Activities"
msgstr "نشاطات مخطط سير العمل "

#. module: crm
#: model:ir.actions.act_window,name:crm.action_report_crm_opportunity_salesteam
#: model:ir.actions.act_window,name:crm.crm_opportunity_report_action
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_pivot
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_graph
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_pivot
msgid "Pipeline Analysis"
msgstr "تحليل مخطط سير العمل "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__name
msgid "Plan Name"
msgstr "اسم الخطة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__pls_fields
msgid "Pls Fields"
msgstr "حقول تصنيف العملاء المهتمين التوقعي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_pls_update__pls_start_date
msgid "Pls Start Date"
msgstr "تاريخ بداية تصنيف العملاء المهتمين التوقعي "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"سياسة لنشر رسالة في المستند باستخدام بوابة البريد الإلكتروني.\n"
"- الجميع: يمكن للجميع النشر\n"
"- الشركاء: الشركاء المعتمدون فقط\n"
"- المتابعون: فقط متابعو المستند ذي الصلة أو أعضاء القنوات التالية.\n"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duplicate_lead_ids
msgid "Potential Duplicate Lead"
msgstr "نسخة مطابقة محتملة لبيانات عميل مهتم "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duplicate_lead_count
msgid "Potential Duplicate Lead Count"
msgstr "عدد النسخ المطابقة المحتملة لبيانات عميل مهتم  "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Predictive Lead Scoring"
msgstr "تصنيف العملاء المهتمين التوقعي "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__predictive_lead_scoring_field_labels
msgid "Predictive Lead Scoring Field Labels"
msgstr "عناوين حقل تصنيف العملاء المهتمين التوقعي "

#. module: crm
#: model:ir.actions.server,name:crm.website_crm_score_cron_ir_actions_server
msgid "Predictive Lead Scoring: Recompute Automated Probabilities"
msgstr "تصنيف العملاء المهتمين التوقعي: إعادة احتساب الاحتمالات المؤتمتة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__priority
msgid "Priority"
msgstr "الأولوية"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Priority:"
msgstr "الأولوية:"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__probability
msgid "Probability"
msgstr "الاحتمالية "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_leads
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Probability (%)"
msgstr "الاحتمالية (%)"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Probability:"
msgstr "الاحتمالية: "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__lead_properties
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Properties"
msgstr "الخصائص "

#. module: crm
#: model:crm.stage,name:crm.stage_lead3
msgid "Proposition"
msgstr "مقترح "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_monthly_prorated
msgid "Prorated MRR"
msgstr "الإيرادات الشهرية المتكررة التناسبية "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue_prorated
msgid "Prorated Recurring Revenues"
msgstr "الإيرادات المتكررة التناسبية "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__prorated_revenue
msgid "Prorated Revenue"
msgstr "الإيراد التناسبي "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_tree_forecast
msgid "Prorated Revenues"
msgstr "الإيرادات التناسبية "

#. module: crm
#: model:crm.stage,name:crm.stage_lead2
msgid "Qualified"
msgstr "مؤهل"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__rating_ids
msgid "Ratings"
msgstr "التقييمات "

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "Ready to boost your sales? Let's have a look at your <b>Pipeline</b>."
msgstr "هل أنت جاهز لتعزيز مبيعاتك؟ فلنلقِ نظرة على <b>مخطط سير عملك</b>. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__alias_force_thread_id
msgid "Record Thread ID"
msgstr "معرف مناقشة السجل"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_plan
msgid "Recurring Plan"
msgstr "خطة متكررة "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_recurring_plan_action
#: model:ir.ui.menu,name:crm.crm_recurring_plan_menu_config
msgid "Recurring Plans"
msgstr "الخطط المتكررة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Recurring Revenue"
msgstr "الإيرادات المتكررة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__recurring_revenue
#: model:ir.model.fields,field_description:crm.field_res_config_settings__group_use_recurring_revenues
msgid "Recurring Revenues"
msgstr "الإيرادات المتكررة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__referred
msgid "Referred By"
msgstr "محال من قِبَل "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Referred By:"
msgstr "تمت الإحالة من قِبَل: "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__action
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__action
msgid "Related Customer"
msgstr "العميل ذو الصلة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_auto_assignment_interval_number
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Repeat every"
msgstr "التكرار كل "

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
msgid "Repeat frequency should be positive."
msgstr "يجب أن يكون تواتر التكرار قيمة إيجابية "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_action__auto
msgid "Repeatedly"
msgstr "بشكل متكرر "

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_report
msgid "Reporting"
msgstr "إعداد التقارير "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__requirements
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "Requirements"
msgstr "المتطلبات"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Reschedule"
msgstr "إعادة جدولة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__activity_user_id
msgid "Responsible User"
msgstr "المستخدم المسؤول"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Restore"
msgstr "استعادة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Ringover VOIP Phone"
msgstr ""

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_config_settings__crm_use_auto_assignment
msgid "Rule-Based Assignment"
msgstr "التعيين المبني على القواعد "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Running"
msgstr "جاري"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__message_has_sms_error
msgid "SMS Delivery error"
msgstr "خطأ في تسليم الرسائل النصية القصيرة "

#. module: crm
#: model:ir.ui.menu,name:crm.crm_menu_sales
msgid "Sales"
msgstr "المبيعات"

#. module: crm
#: model:ir.model,name:crm.model_crm_team
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__team_id
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__team_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__team_id
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Sales Team"
msgstr "فريق المبيعات"

#. module: crm
#: model:ir.model,name:crm.model_crm_team_member
msgid "Sales Team Member"
msgstr "عضو فريق المبيعات "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Sales Team:"
msgstr "فريق المبيعات:"

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_config
msgid "Sales Teams"
msgstr "فرق المبيعات"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_id
#: model:ir.model.fields,field_description:crm.field_crm_merge_opportunity__user_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Salesperson"
msgstr "مندوب المبيعات "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Salesperson:"
msgstr "مندوب المبيعات: "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead2opportunity_partner_mass__user_ids
msgid "Salespersons"
msgstr "مندوبي المبيعات "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__phone_sanitized
msgid "Sanitized Number"
msgstr "رقم هاتف سليم "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lead_action_my_activities
msgid "Schedule activities to keep track of everything you have to do."
msgstr "قم بجدولة الفعاليات حتى تتمكن من متابعة كل ما عليك فعله. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
msgid "Search Leads"
msgstr "البحث في العملاء المهتمين "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Search Opportunities"
msgstr "البحث في الفرص"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Select at least two Leads/Opportunities from the list to merge them."
msgstr "قم بتحديد عميلين مهتمين/فرصتين من القائمة على الأقل لدمجهما. "

#. module: crm
#: model:ir.actions.act_window,name:crm.action_lead_mail_compose
#: model:ir.actions.act_window,name:crm.action_lead_mass_mail
msgid "Send email"
msgstr "إرسال بريد إلكتروني "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_recurring_plan__sequence
#: model:ir.model.fields,field_description:crm.field_crm_stage__sequence
msgid "Sequence"
msgstr "تسلسل "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_recurring_plan_action
msgid ""
"Set Recurring Plans on Opportunities to display the contracts' renewal "
"periodicity<br>(e.g: Monthly, Yearly)."
msgstr ""
"قم بوضع خطط دورية للفرص لعرض وتيرة تجديد العقود<br>(مثال: شهرياً، سنوياً). "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid "Set a new stage in your opportunity pipeline"
msgstr "تعيين مرحلة جديدة في مخطط سير العمل الخاص بفرصتك "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_config_settings_action
#: model:ir.ui.menu,name:crm.crm_config_settings_menu
msgid "Settings"
msgstr "الإعدادات"

#. module: crm
#: model:res.groups,name:crm.group_use_lead
msgid "Show Lead Menu"
msgstr "إظهار قائمة العملاء المهتمين "

#. module: crm
#: model:res.groups,name:crm.group_use_recurring_revenues
msgid "Show Recurring Revenues Menu"
msgstr "إظهار قائمة الإيرادات المتكررة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Show all opportunities for which the next action date is before today"
msgstr "عرض كافة الفرص المُعين لها تاريخ إجراء تالي يسبق تاريخ اليوم الجاري"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Show only lead"
msgstr "إظهار العملاء المهتمين فقط "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only leads"
msgstr "إظهار العملاء المهتمين فقط "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
msgid "Show only opportunities"
msgstr "عرض الفرص فقط"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Show only opportunity"
msgstr "إظهار الفرص فقط"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_team__assignment_optout
#: model:ir.model.fields,field_description:crm.field_crm_team_member__assignment_optout
msgid "Skip auto assignment"
msgstr "تخطي التعيين التلقائي "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_tree_view_oppor
msgid "Snooze 7d"
msgstr "تأجيل 7 أيام "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__source_id
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Source"
msgstr "المصدر"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Source:"
msgstr "مصدر:"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__team_id
msgid ""
"Specific team that uses this stage. Other teams will not be able to see or "
"use this stage."
msgstr ""
"الفريق المحدد الذي يستخدم هذه المرحلة. لن تتمكن الفرق الأخرى من رؤية أو "
"استخدام هذه المرحلة."

#. module: crm
#. odoo-python
#: code:addons/crm/models/res_config_settings.py:0
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__stage_id
#: model:ir.model.fields,field_description:crm.field_crm_lead__stage_id
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Stage"
msgstr "المرحلة"

#. module: crm
#: model:mail.message.subtype,name:crm.mt_lead_stage
msgid "Stage Changed"
msgstr "تم تغيير المرحلة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__name
msgid "Stage Name"
msgstr "اسم المرحلة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_stage_search
msgid "Stage Search"
msgstr "البحث في المراحل"

#. module: crm
#: model:mail.message.subtype,description:crm.mt_lead_stage
msgid "Stage changed"
msgstr "تم تغيير المرحلة"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Stage:"
msgstr "المرحلة: "

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_stage_action
#: model:ir.ui.menu,name:crm.menu_crm_lead_stage_act
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_tree
msgid "Stages"
msgstr "المراحل"

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_stage_action
msgid ""
"Stages allow salespersons to easily track how a specific opportunity\n"
"            is positioned in the sales cycle."
msgstr ""
"تتيح المراحل لمندوبي المبيعات تتبع وضع فرصة معينة\n"
"            في دورة المبيعات بكل سهولة. "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__state_id
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "State"
msgstr "الحالة "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"الأنشطة المعتمدة على الحالة\n"
"المتأخرة: تاريخ الاستحقاق مر\n"
"اليوم: تاريخ النشاط هو اليوم\n"
"المخطط: الأنشطة المستقبلية."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__duration_tracking
msgid "Status time"
msgstr "وقت الحالة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street
msgid "Street"
msgstr "الشارع"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street 2..."
msgstr "الشارع 2..."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Street..."
msgstr "الشارع..."

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__street2
msgid "Street2"
msgstr "الشارع 2"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Subject: "
msgstr "الموضوع: "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__subtype_id
msgid "Subtype"
msgstr "النوع الفرعي"

#. module: crm
#: model:ir.model,name:crm.model_ir_config_parameter
msgid "System Parameter"
msgstr "معيار النظام "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Tag"
msgstr "علامة تصنيف "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__tag_ids
#: model:ir.model.fields,field_description:crm.field_crm_lead__tag_ids
#: model:ir.ui.menu,name:crm.menu_crm_lead_categ
msgid "Tags"
msgstr "علامات التصنيف "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Tags:"
msgstr "علامات التصنيف: "

#. module: crm
#: model:ir.ui.menu,name:crm.sales_team_menu_team_pipeline
msgid "Teams"
msgstr "الفِرَق "

#. module: crm
#: model:ir.ui.menu,name:crm.crm_team_member_config
msgid "Teams Members"
msgstr "أعضاء الفِرَق "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid ""
"The contacts below have been added as followers of this lead\n"
"                        because they have been contacted less than 30 days ago on"
msgstr ""
"لقد تمت إضافة جهات الاتصال أدناه كمتابعين لهذا العميل المهتم\n"
"                        لأنه قد تم التواصل معهم قبل أقل من 30 يوم على "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_id
msgid ""
"The email address associated with this channel. New emails received will "
"automatically create new leads assigned to the channel."
msgstr ""
"البريد الإلكتروني المرتبط بهذه القناة. سوف يتم إنشاء عملاء مهتمين جدد "
"تلقائياً مسندون إلى هذه القناة من رسائل البريد الإلكتروني الواردة الجديدة. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"النموذج (مستندات أودو) الذي يقترن به هذا اللقب. أي رسالة واردة لا ترد على "
"سجل موجود ستقوم بإنشاء سجل جديد من نفس نوع هذا النموذج (مثلًا: مهمة مشروع) "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"اسم لقب البريد الإلكتروني، مثلًا: 'وظائف' إذا كنت ترغب في جمع الرسائل "
"المرسلة لـ<<EMAIL>> "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__partner_name
msgid ""
"The name of the future partner company that will be created while converting"
" the lead into opportunity"
msgstr ""
"اسم شركة الشريك المستقبلي التي سوف يتم إنشاؤها أثناء تحويل العميل المهتم إلى"
" فرصة "

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_recurring_plan_check_number_of_months
msgid "The number of month can't be negative."
msgstr "لا يمكن أن يكون عدد الأشهر رقماً سالباً. "

#. module: crm
#: model:ir.model.constraint,message:crm.constraint_crm_lead_check_probability
msgid "The probability of closing the deal should be between 0% and 100%!"
msgstr "يجب أن تكون احتمالية عقد صفقة بين 0٪ و 100٪! "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "The success rate is computed based on"
msgstr "يتم احتساب نسبة النجاح بناءً على "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid ""
"The success rate is computed based on the stage, but you can add more fields"
" in the statistical analysis."
msgstr ""
"يتم احتساب نسبة النجاح بناءً على المرحلة، ولكن بمقدورك إضافة المزيد من "
"الحقول في التحليل الإحصائي. "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action_lead
msgid "This analysis shows you how many leads have been created per month."
msgstr "يظهر التحليل عدد العملاء المهتمين الذين قد تم إنشاؤهم لكل شهر. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid ""
"This bar allows to filter the opportunities based on scheduled activities."
msgstr "يتيح لك هذا الشريط تصفية الفرص حسب الأنشطة المُجدولة. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"This can be used to automatically assign leads to sales persons based on "
"rules"
msgstr ""
"يمكن استخدام ذلك لتعيين العملاء المهتمين تلقائياً لمندوبي المبيعات بناءً على"
" القواعد "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "This can be used to compute statistical probability to close a lead"
msgstr "يمكن استخدام ذلك لحساب الاحتمالية الإحصائية لإغلاق العميل المهتم "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"هذا البريد الإلكتروني مدرج على القائمة السوداء للرسائل البريدية الجماعية. "
"انقر لإلغاء إدراجه بتلك القائمة."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"يستخدم هذا الحقل للبحث في عنوان البريد الإلكتروني حيث يمكن أن يحتوي حقل "
"البريد الإلكتروني الأساسي على أكثر من عنوان بريد إلكتروني بدقة."

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__lang_code
msgid "This field is used to set/get locales for user"
msgstr "يستخدم هذا الحقل لضبط مناطق البيانات للمستخدم أو للحصول عليها"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"يساعدك هذا الاسم على تتبع جهود حملاتك المختلفة، مثال: fall_drive "
"،christmas_special "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""
"هذه هي طريقة التوصيل، مثال: بطاقة بريدية، أو البريد الإلكتروني أو لافتة "
"إعلانية "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"هذا هو مصدر الرابط، مثلًا: محرك بحث، أو نطاق آخر، أو اسم في قائمة البريد "
"الإلكتروني"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid ""
"This phone number is blacklisted for SMS Marketing. Click to unblacklist."
msgstr ""
"لقد تم إدراج هذا الرقم في القائمة السوداء للتسويق عبر الرسائل النصية "
"القصيرة. انقر لإزالته من القائمة السوداء. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"يتم طي هذه المرحلة عند استخدام طريقة كانبان للعرض عندما لا توجد سجلات يمكن "
"عرضها في هذه المرحلة."

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "This will assign leads to all members. Do you want to proceed?"
msgstr ""
"سوف يقوم ذلك بتعيين العملاء المهتمين لكافة الأعضاء. هل ترغب بالاستمرار؟ "

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_0
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Tip: Convert incoming emails into opportunities"
msgstr "نصيحة: قم بتحويل رسائل البريد الإلكتروني الواردة إلى فرص "

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_1
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_1
msgid "Tip: Did you know Odoo has built-in lead mining?"
msgstr "نصيحة: هل تعلم أن أودو مجهز بنظام ضمنياً لاستقطاب العملاء المهتمين؟ "

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_4
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_4
msgid "Tip: Do not waste time recording customers' data"
msgstr "نصيحة: لا تهدر الوقت في تسجيل بيانات العملاء "

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_6
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_6
msgid "Tip: Identify Bottlenecks at a glance"
msgstr "نصيحة: تمكن من تحديد نقاط الضعف بسهولة "

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_3
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_3
msgid "Tip: Manage your pipeline"
msgstr "نصيحة: قم بإدارة مخطط سير عملك "

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_2
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_2
msgid "Tip: Opportunity win rate is predicted with AI"
msgstr "نصيحة: يتم توقع نسبة نجاح الفرصة عن طريق الذكاء الاصطناعي "

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_7
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_7
msgid "Tip: Turn Sales Forecasting into Child's Play"
msgstr "نصيحة: اجعل توقع مبيعاتك أسهل من أي وقت مضى "

#. module: crm
#: model:digest.tip,name:crm.digest_tip_crm_5
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_5
msgid "Tip: Turn a selection of opportunities into a map"
msgstr "نصيحة: قم بتحويل مجموعة من الفرص إلى خريطة "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__title
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Title"
msgstr "اللقب "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid ""
"To prevent data loss, Leads and Opportunities can only be merged by groups "
"of %(max_length)s."
msgstr ""
"لمنع ضياع البيانات، يمكن دمج العملاء المهتمين والفرص فقط في مجموعات مكونة من"
" %(max_length)s. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_my_activities_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Today Activities"
msgstr "أنشطة اليوم "

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_1
msgid "Too expensive"
msgstr "غالٍ للغاية "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "Tracking"
msgstr "التتبع"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Trailing 12 months"
msgstr "متأخر لمدة 12 شهراً "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_team__lead_all_assigned_month_exceeded
msgid ""
"True if the monthly lead assignment count is greater than the maximum "
"assignment limit, false otherwise."
msgstr ""
"تكون القيمة صحيحة إذا كان عدد العملاء المهتمين الذين تم إسنادهم أكبر من الحد"
" الأقصى المسموح به للإسناد، وإلا فستكون القيمة خاطئة. "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "Try sending an email"
msgstr "جرب إرسال بريد إلكتروني "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_activity_report__lead_type
#: model:ir.model.fields,field_description:crm.field_crm_lead__type
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
msgid "Type"
msgstr "النوع"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_activity_report__lead_type
msgid "Type is used to separate Leads and Opportunities"
msgstr "يستخدم النوع للفصل بين العملاء المهتمين والفرص "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "نوع النشاط المستثنى في السجل. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Type:"
msgstr "النوع:"

#. module: crm
#: model:ir.model,name:crm.model_utm_campaign
msgid "UTM Campaign"
msgstr "حملة UTM"

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__user_company_ids
msgid "UX: Limit to lead company or all if no company"
msgstr ""
"تجربة المستخدم (UX): التقييد لشركة العميل المهتم أو الجميع إذا لم تكن هناك "
"شركة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_leads_filter
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unassigned"
msgstr "غير مسند "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Unassigned Lead"
msgstr "عميل مهتم غير معيّن "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_team_view_kanban_dashboard
msgid "Unassigned Leads"
msgstr "عملاء مهتمين غير معينين "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Unread Messages"
msgstr "الرسائل غير المقروءة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_search_forecast
msgid "Upcoming Closings"
msgstr "الإقفالات المقبلة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_pls_update_view_form
msgid "Update"
msgstr "تحديث"

#. module: crm
#: model:ir.actions.act_window,name:crm.crm_lead_pls_update_action
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Update Probabilities"
msgstr "تحديث الاحتمالات "

#. module: crm
#: model:ir.model,name:crm.model_crm_lead_pls_update
msgid "Update the probabilities"
msgstr "تحديث الاحتمالات "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_utm_campaign__use_leads
msgid "Use Leads"
msgstr "استخدم بيانات العملاء المهتمين "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_lost_reason_action
msgid ""
"Use Lost Reasons to report on why opportunities are lost (e.g.\"Undercut by "
"competitors\")."
msgstr ""
"استخدم أسباب الضياع لإعداد تقرير حول سبب ضياع الفرص (مثال: \"تلقوا سعراً أقل"
" من قِبَل منافسينا\"). "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead2opportunity_partner_mass__action__each_exist_or_create
msgid "Use existing partner or create"
msgstr "استخدام شريك موجود بالفعل أو إنشاء واحد "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_lead
msgid ""
"Use leads if you need a qualification step before creating an\n"
"                    opportunity or a customer. It can be a business card you received,\n"
"                    a contact form filled in your website, or a file of unqualified\n"
"                    prospects you import, etc."
msgstr ""
"استخدم العملاء المهتمين إذا كنت بحاجة لخطوة تأهيلية قبل إنشاء\n"
"                    فرصة أو عميل. يمكن أن تكون بيانات العميل المهتم في بطاقة عمل استلمتها،\n"
"                    أو استمارة اتصال تمت تعبئتها على موقعك الإلكتروني، أو ملف قمت باستيراده\n"
"                    للعملاء المحتملين غير المؤهلين، إلخ. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid ""
"Use leads if you need a qualification step before creating an opportunity or"
" a customer. It can be a business card you received, a contact form filled "
"in your website, or a file of unqualified prospects you import, etc. Once "
"qualified, the lead can be converted into a business opportunity and/or a "
"new customer in your address book."
msgstr ""
"استخدم العملاء المهتمين إذا كنت بحاجة لخطوة تأهيلية قبل إنشاء فرصة أو عميل. "
"يمكن أن تكون بيانات العميل المهتم في بطاقة عمل استلمتها، أو استمارة اتصال "
"تمت تعبئتها على موقعك الإلكتروني، أو ملف قمت باستيراده للعملاء المحتملين غير"
" المؤهلين، إلخ. بمجرد تأهيلهم، يكون بالإمكان تحويل العميل المهتم إلى فرصة "
"عمل و/أو عميل جديد في دفتر عناوينك. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "Use the <i>New</i> button, or send an email to"
msgstr "استخدم زر <i>جديد</i>، أو قم بإرسال بريد إلكتروني إلى "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid ""
"Use the <i>New</i> button, or send an email to %(email_link)s to test the "
"email gateway."
msgstr ""
"استخدم زر <i>جديد</i> أو أرسل بريداً إلكترونياً إلى %(email_link)s لاختبار "
"بوابة البريد الإلكتروني. "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_7
msgid ""
"Use the CRM Forecast Kanban to easily define when Opportunities are expected"
" to be won."
msgstr ""
"استخدم أداة كانبان لتوقعات إدارة علاقات العملاء حتى تتمكن من تحديد الوقت "
"المتوقع الفوز فيه بالفرص بكل سهولة. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid ""
"Use the New button, or configure an email alias to test the email gateway."
msgstr ""
"استخدم زر \"جديد\" لتهيئة لقب البريد الإلكتروني لاختبار بوابة البريد "
"لإلكتروني. "

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_opportunity_report_action
msgid "Use this menu to have an overview of your Pipeline."
msgstr "استخدم هذه القائمة للحصول على نظرة عامة على مخطط سير عملك. "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_stage__sequence
msgid "Used to order stages. Lower is better."
msgstr "يستخدم في ترتيب المراحل، العدد الأقل له الأسبقية."

#. module: crm
#: model:ir.model,name:crm.model_res_users
msgid "User"
msgstr "المستخدم"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__user_company_ids
msgid "User Company"
msgstr "شركة المستخدم "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__value
msgid "Value"
msgstr "القيمة"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__variable
msgid "Variable"
msgstr "متغير"

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__crm_lead__priority__3
msgid "Very High"
msgstr "عالٍ جداً "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "Visits to Leads"
msgstr "من زيارات إلى عملاء مهتمين "

#. module: crm
#: model:crm.lost.reason,name:crm.lost_reason_2
msgid "We don't have people/skills"
msgstr "لا نملك الأشخاص/المهارات"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website
msgid "Website"
msgstr "الموقع الإلكتروني"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__website_message_ids
msgid "Website Messages"
msgstr "رسائل الموقع الإلكتروني "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website_message_ids
msgid "Website communication history"
msgstr "سجل تواصل الموقع الإلكتروني "

#. module: crm
#: model:ir.model.fields,help:crm.field_crm_lead__website
msgid "Website of the contact"
msgstr "الموقع الإلكتروني لجهة الاتصال"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_merge_summary
msgid "Website:"
msgstr "الموقع الإلكتروني: "

#. module: crm
#: model:ir.model.fields.selection,name:crm.selection__res_config_settings__crm_auto_assignment_interval_type__weeks
msgid "Weeks"
msgstr "أسابيع"

#. module: crm
#: model:mail.template,name:crm.mail_template_demo_crm_lead
msgid "Welcome Demo"
msgstr "التجربة المجانية الترحيبية "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_lost_view_form
msgid "What went wrong?"
msgstr "ما الذي حدث؟ "

#. module: crm
#: model:crm.stage,name:crm.stage_lead4
#: model_terms:ir.ui.view,arch_db:crm.crm_activity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_kanban_forecast
#: model_terms:ir.ui.view,arch_db:crm.crm_opportunity_report_view_search
#: model_terms:ir.ui.view,arch_db:crm.view_crm_case_opportunities_filter
msgid "Won"
msgstr "تم الفوز بها "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead_scoring_frequency__won_count
msgid "Won Count"
msgstr "عدد مرات الربح "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_res_users__target_sales_won
msgid "Won in Opportunities Target"
msgstr "الربح المستهدف في الفرص"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Yeah! Deal of the last 7 days for the team."
msgstr "أجل! أفضل صفقة في الـ7 أيام الماضية للفريق. "

#. module: crm
#: model:crm.recurring.plan,name:crm.crm_recurring_plan_yearly
msgid "Yearly"
msgstr "سنويًا"

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "Yes"
msgstr "نعم"

#. module: crm
#. odoo-javascript
#: code:addons/crm/static/src/js/tours/crm.js:0
msgid "You can make your opportunity advance through your pipeline from here."
msgstr "يمكنك مساعدة فرصتك على المضي قدماً في مخطط سير عملك من هنا. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "You don't have the access needed to run this cron."
msgstr "لا تملك حقوق الوصول المطلوبة لتشغيل هذا الـ cron. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "You just beat your personal record for the past 30 days."
msgstr "لقد حطمت للتو رقمك القياسي خلال الـ30 يوم الماضي."

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "You just beat your personal record for the past 7 days."
msgstr "لقد حطمت للتو رقمك القياسي خلال الـ7 أيام الماضية."

#. module: crm
#: model_terms:ir.actions.act_window,help:crm.crm_case_form_view_salesteams_opportunity
msgid ""
"You will be able to plan meetings and phone calls from\n"
"                    opportunities, convert them into quotations, attach related\n"
"                    documents, track all discussions, and much more."
msgstr ""
"سوف تكون قادراً على التخطيط للاجتماعات والمكالمات الهاتفية من\n"
"                الفرص، وتحويلها إلى عروض أسعار، وإرفاق المستندات\n"
"                ذات الصلة، وتتبع كافة المناقشات، وأكثر من ذلك بكثير. "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "ZIP"
msgstr "الرمز البريدي"

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_lead__zip
msgid "Zip"
msgstr "الرمز البريدي"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "alias"
msgstr "اللقب "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. \"0123456789\""
msgstr "مثال: \"0123456789\" "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. \"Monthly\""
msgstr "مثال: \"شهري\" "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. \"<EMAIL>\""
msgstr "مثال: \"<EMAIL>\" "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_stage_form
msgid "e.g. Negotiation"
msgstr "مثال: المفاوضة "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
#: model_terms:ir.ui.view,arch_db:crm.quick_create_opportunity_form
msgid "e.g. Product Pricing"
msgstr "مثلًا: تسعير المنتج"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lost_reason_view_form
msgid "e.g. Too expensive"
msgstr "مثال: غالٍ جداً "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_lead_view_form
msgid "e.g. https://www.odoo.com"
msgstr "مثل https://www.odoo.com"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.sales_team_form_view_in_crm
msgid "e.g. mycompany.com"
msgstr "مثال: mycompany.com "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.res_config_settings_view_form
msgid "for the leads created as of the"
msgstr "للعملاء المهتمين الذين تم إنشاؤهم في "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "generate opportunities in your pipeline?<br>"
msgstr "إنشاء الفرص في مخطط سير عملك؟<br> "

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_case_kanban_view_leads
msgid "lost"
msgstr "ضائع "

#. module: crm
#: model:ir.model.fields,field_description:crm.field_crm_stage__team_count
msgid "team_count"
msgstr "team_count"

#. module: crm
#: model_terms:ir.ui.view,arch_db:crm.crm_action_helper
msgid "to test the email gateway."
msgstr "لاختبار بوابة البريد الإلكتروني. "

#. module: crm
#: model_terms:digest.tip,tip_description:crm.digest_tip_crm_0
msgid "to your CRM. This email address is configurable by sales team members."
msgstr ""
"لإدارة علاقات العملاء. يمكن تهيئة هذا البريد الإلكتروني من قِبَل أعضاء فريق "
"المبيعات. "

#. module: crm
#. odoo-python
#: code:addons/crm/models/crm_lead.py:0
msgid "unknown"
msgstr "مجهول "
