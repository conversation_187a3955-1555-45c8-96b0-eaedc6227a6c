# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * mrp_account
#
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.saas~18+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:02+0000\n"
"PO-Revision-Date: 2017-09-20 11:33+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Faroese (https://www.transifex.com/odoo/teams/41243/fo/)\n"
"Language: fo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_account_enterprise
#: model:ir.actions.report,print_report_name:mrp_account_enterprise.action_cost_struct_mrp_production
msgid "'Cost Analysis - %s ' % object.name"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.actions.report,print_report_name:mrp_account_enterprise.action_cost_struct_product_template
msgid "'Cost Structure Analysis - %s' % object.name"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid ", from"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_production_form_inherit_view6
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.product_product_inherit_form_view_cost_structure
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.product_template_inherit_form_view_cost_structure
msgid "<span class=\"o_stat_text\">Cost Analysis</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Cost of Components Per Unit</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Cost of Operations Per Unit</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Total Cost Per Unit</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Components</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Cost/hour</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Operation</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Product</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Quantity</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Resource</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Scraps</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Total Cost</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Unit Cost</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Unit of Measure</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Working Time</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Components</strong>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Operations</strong>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Scraps</strong>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Production Cost</strong>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Component Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Operation Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Total Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "By product(s)"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__byproduct_cost
msgid "By-Products Total Cost"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__company_id
msgid "Company"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_component_cost
msgid "Component Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_component_cost
msgid "Component cost per unit produced (in product UoM) of manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_cost
msgid "Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.actions.report,name:mrp_account_enterprise.action_cost_struct_mrp_production
msgid "Cost Analysis"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost Analysis Report"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost Breakdown of Products"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.actions.report,name:mrp_account_enterprise.action_cost_struct_product_template
msgid "Cost Structure Analysis"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Components"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Components per unit (in"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Operations"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Operations per unit (in"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Scraps"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_cost
msgid "Cost per unit produced (in product UoM) of manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:account.analytic.account,name:mrp_account_enterprise.account_assembly_cycle
msgid "Costing Account For Cycle of Assembly."
msgstr ""

#. module: mrp_account_enterprise
#: model:account.analytic.account,name:mrp_account_enterprise.account_assembly_hours
msgid "Costing Account For Hours of Assembly."
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__currency_id
msgid "Currency"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__display_name
msgid "Display Name"
msgstr "Vís navn"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_duration
msgid "Duration of Operations / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__date_finished
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "End Date"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Group by"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__id
msgid "ID"
msgstr "ID"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__production_id
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Manufacturing Order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_mrp_report
msgid "Manufacturing Report"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.actions.act_window,help:mrp_account_enterprise.mrp_report_dashboard_action
msgid "No data yet!"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_operation_cost
msgid "Operation cost per unit produced (in product UoM) of manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_duration
msgid "Operation duration (minutes) per unit produced of manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Order Overview"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_product_template
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__product_id
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Product"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_report_mrp_account_enterprise_product_template_cost_structure
msgid "Product Template Cost Structure Report"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_product_product
msgid "Product Variant"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.actions.act_window,name:mrp_account_enterprise.mrp_report_dashboard_action
#: model:ir.ui.menu,name:mrp_account_enterprise.mrp_dashboard_menuitem
msgid "Production Analysis"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Quantity Produced"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Some of the Manufacturing Order(s) selected are not done yet"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__component_cost
msgid "Total Component Cost"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__total_cost
msgid "Total Cost"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Total Cost per unit (in"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__duration
msgid "Total Duration of Operations"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__operation_cost
msgid "Total Operation Cost"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_operation_cost
msgid "Total Operation Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__component_cost
msgid "Total cost of components for manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__total_cost
msgid "Total cost of manufacturing order (component + operation costs + subcontracting cost)"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__operation_cost
msgid "Total cost of operations for manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__duration
msgid "Total duration (minutes) of operations for manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__qty_produced
msgid "Total quantity produced in product's UoM"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "hours"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "manufacturing order(s)."
msgstr ""
