# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account_enterprise
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> Ganbat <<EMAIL>>, 2024
# Bayark<PERSON>u Bataa, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# tser<PERSON><PERSON><PERSON> tsogtoo <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Mongolian (https://app.transifex.com/odoo/teams/41243/mn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: mn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_account_enterprise
#: model:ir.actions.report,print_report_name:mrp_account_enterprise.action_cost_struct_mrp_production
msgid "'Cost Analysis - %s ' % object.name"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.actions.report,print_report_name:mrp_account_enterprise.action_cost_struct_product_template
msgid "'Cost Structure Analysis - %s' % object.name"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid ", from"
msgstr ", -с"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.product_product_inherit_form_view_cost_structure
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.product_template_inherit_form_view_cost_structure
msgid "<span class=\"o_stat_text\">Cost Analysis</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Cost of Components Per Unit</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Cost of Operations Per Unit</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Total Cost Per Unit</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Components</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Cost/hour</span>"
msgstr "<span>Өртөг/цаг</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Operation</span>"
msgstr "<span>Үйлдэл</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Product</span>"
msgstr "<span>Бараа</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Quantity</span>"
msgstr "<span>Тоо хэмжээ</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Resource</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Scraps</span>"
msgstr "<span>Хаягдал</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Total Cost</span>"
msgstr "<span>Нийт өртөг</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Unit Cost</span>"
msgstr "<span>Нэгжийн зардал</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Unit of Measure</span>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Working Time</span>"
msgstr "<span>Ажлын цаг</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Components</strong>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Operations</strong>"
msgstr "<strong>Үйл Ажиллагааны Нийт Зардал</strong>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Scraps</strong>"
msgstr "<strong>Хаягдлын Нийт Зардал</strong>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Production Cost</strong>"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Component Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Operation Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Total Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "By product(s)"
msgstr "Бүтээгдэхүүнээр"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__byproduct_cost
msgid "By-Products Total Cost"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__company_id
msgid "Company"
msgstr "Компани"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_component_cost
msgid "Component Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_component_cost
msgid ""
"Component cost per unit produced (in product UoM) of manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_cost
msgid "Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.actions.report,name:mrp_account_enterprise.action_cost_struct_mrp_production
msgid "Cost Analysis"
msgstr "Зардлын Шинжилгээ"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost Analysis Report"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost Breakdown of Products"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.actions.report,name:mrp_account_enterprise.action_cost_struct_product_template
msgid "Cost Structure Analysis"
msgstr "Зардлын Бүтцийн Шинжилгээ"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Components"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Components per unit (in"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Operations"
msgstr "Үйл Ажиллагааны Зардал"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Operations per unit (in"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Scraps"
msgstr "Хаягдлын Зардал"

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_cost
msgid "Cost per unit produced (in product UoM) of manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:account.analytic.account,name:mrp_account_enterprise.account_assembly_cycle
msgid "Costing Account For Cycle of Assembly."
msgstr ""

#. module: mrp_account_enterprise
#: model:account.analytic.account,name:mrp_account_enterprise.account_assembly_hours
msgid "Costing Account For Hours of Assembly."
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__currency_id
msgid "Currency"
msgstr "Валют"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__display_name
msgid "Display Name"
msgstr "Дэлгэрэнгүй нэр"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_duration
msgid "Duration of Operations / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__date_finished
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "End Date"
msgstr "Дуусах огноо"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__expected_component_cost_unit
msgid "Expected Component Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__expected_employee_cost_unit
msgid "Expected Employee Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__expected_operation_cost_unit
msgid "Expected Operation Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__expected_total_cost_unit
msgid "Expected Total Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Group by"
msgstr "Бүлэглэх"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__id
msgid "ID"
msgstr "ID"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr "Үйлдвэрлэлийн өртгийн бүтцийн тайлан"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__production_id
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Manufacturing Order"
msgstr "Үйлдвэрлэлийн Захиалга"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_mrp_report
msgid "Manufacturing Report"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.actions.act_window,help:mrp_account_enterprise.mrp_report_dashboard_action
msgid "No data yet!"
msgstr "Одоогоор мэдээлэл алга!"

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_operation_cost
msgid ""
"Operation cost per unit produced (in product UoM) of manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_duration
msgid "Operation duration (minutes) per unit produced of manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Order Overview"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_product_template
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__product_id
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Product"
msgstr "Бараа"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_report_mrp_account_enterprise_product_template_cost_structure
msgid "Product Template Cost Structure Report"
msgstr "Бүтээгдэхүүний загвар өртгийн бүтцийн тайлан"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_product_product
msgid "Product Variant"
msgstr "Барааны хувилбар"

#. module: mrp_account_enterprise
#: model:ir.actions.act_window,name:mrp_account_enterprise.mrp_report_dashboard_action
#: model:ir.ui.menu,name:mrp_account_enterprise.mrp_dashboard_menuitem
msgid "Production Analysis"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__qty_demanded
msgid "Quantity Demanded"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Quantity Produced"
msgstr "Үйлдвэрлэсэн Тоо хэмжээ"

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__yield_rate
msgid "Ratio of quantity produced over quantity demanded"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Some of the Manufacturing Order(s) selected are not done yet"
msgstr "Сонгогдсон Захиалга(ууд)-с зарим нь үйлдвэрлэгдээгүй байна"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__component_cost
msgid "Total Component Cost"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__total_cost
msgid "Total Cost"
msgstr "Нийт Өртөг"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Total Cost per unit (in"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__duration
msgid "Total Duration of Operations"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__operation_cost
msgid "Total Operation Cost"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_operation_cost
msgid "Total Operation Cost / Unit"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__component_cost
msgid "Total cost of components for manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__total_cost
msgid ""
"Total cost of manufacturing order (component + operation costs + "
"subcontracting cost)"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__operation_cost
msgid "Total cost of operations for manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__duration
msgid "Total duration (minutes) of operations for manufacturing order"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__qty_demanded
msgid "Total quantity demanded in product's UoM"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__qty_produced
msgid "Total quantity produced in product's UoM"
msgstr ""

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__yield_rate
msgid "Yield Percentage(%)"
msgstr ""

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "hours"
msgstr "цаг"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "manufacturing order(s)."
msgstr "үйлдвэрлэлийн захиалга(ууд)"
