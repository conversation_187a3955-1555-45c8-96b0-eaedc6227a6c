# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_account_enterprise
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: mrp_account_enterprise
#: model:ir.actions.report,print_report_name:mrp_account_enterprise.action_cost_struct_mrp_production
msgid "'Cost Analysis - %s ' % object.name"
msgstr "'تحليل التكاليف - %s ' % object.name "

#. module: mrp_account_enterprise
#: model:ir.actions.report,print_report_name:mrp_account_enterprise.action_cost_struct_product_template
msgid "'Cost Structure Analysis - %s' % object.name"
msgstr "'تحليل بنية التكاليف - %s' % object.name "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid ", from"
msgstr "، من"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.product_product_inherit_form_view_cost_structure
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.product_template_inherit_form_view_cost_structure
msgid "<span class=\"o_stat_text\">Cost Analysis</span>"
msgstr "<span class=\"o_stat_text\">تحليل التكاليف</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Cost of Components Per Unit</span>"
msgstr "<span>متوسط تكلفة المكونات لكل وحدة</span> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Cost of Operations Per Unit</span>"
msgstr "<span>متوسط تكلفة العمليات لكل وحدة</span> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Avg Total Cost Per Unit</span>"
msgstr "<span>متوسط إجمالي التكلفة لكل وحدة</span> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Components</span>"
msgstr "<span>المكونات</span> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Cost/hour</span>"
msgstr "<span>التكلفة في الساعة</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Operation</span>"
msgstr "<span>العملية</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Product</span>"
msgstr "<span>المنتج</span> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Quantity</span>"
msgstr "<span>الكمية</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Resource</span>"
msgstr "<span>المَورِد</span> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Scraps</span>"
msgstr "<span>مخلفات التصنيع</span> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Total Cost</span>"
msgstr "<span>التكلفة الكلية</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Unit Cost</span>"
msgstr "<span>سعر الوحدة</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Unit of Measure</span>"
msgstr "<span>وحدة القياس</span> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<span>Working Time</span>"
msgstr "<span>وقت العمل</span>"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Components</strong>"
msgstr "<strong>إجمالي تكلفة المكونات</strong> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Operations</strong>"
msgstr "<strong>إجمالي تكلفة العمليات</strong> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Cost of Scraps</strong>"
msgstr "<strong>إجمالي تكلفة مخلفات التصنيع</strong> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "<strong>Total Production Cost</strong>"
msgstr "<strong>إجمالي تكلفة الإنتاج</strong> "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Component Cost / Unit"
msgstr "متوسط تكلفة المكون لكل وحدة "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Operation Cost / Unit"
msgstr "متوسط تكلفة العملية لكل وحدة "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Average Total Cost / Unit"
msgstr "متوسط إجمالي التكلفة لكل وحدة "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "By product(s)"
msgstr "المنتج (المنتجات) الثانوية "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__byproduct_cost
msgid "By-Products Total Cost"
msgstr "التكلفة الكلية للمنتجات الثانوية "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__company_id
msgid "Company"
msgstr "الشركة "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_component_cost
msgid "Component Cost / Unit"
msgstr "تكلفة المكونات لكل وحدة "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_component_cost
msgid ""
"Component cost per unit produced (in product UoM) of manufacturing order"
msgstr "تكلفة المكونات المنتَجة لكل وحدة (في قائمة مواد المنتج) لأمر التصنيع "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_cost
msgid "Cost / Unit"
msgstr "التكلفة لكل وحدة "

#. module: mrp_account_enterprise
#: model:ir.actions.report,name:mrp_account_enterprise.action_cost_struct_mrp_production
msgid "Cost Analysis"
msgstr "تحليل التكلفة"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost Analysis Report"
msgstr "تقري تحليل التكلفة "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost Breakdown of Products"
msgstr "تفاصيل تكاليف المنتجات "

#. module: mrp_account_enterprise
#: model:ir.actions.report,name:mrp_account_enterprise.action_cost_struct_product_template
msgid "Cost Structure Analysis"
msgstr "تحليل بنية التكلفة"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Components"
msgstr "تكلفة المكونات "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Components per unit (in"
msgstr "تكلفة العمليات لكل وحدة (في "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Operations"
msgstr "تكلفة العمليات"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Operations per unit (in"
msgstr "تكلفة العمليات لكل وحدة (في "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Cost of Scraps"
msgstr "تكلفة مخلفات التصنيع "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_cost
msgid "Cost per unit produced (in product UoM) of manufacturing order"
msgstr "التكلفة لكل وحدة يتم إنتاجها (في قائمة مواد المنتج) لأمر التصنيع "

#. module: mrp_account_enterprise
#: model:account.analytic.account,name:mrp_account_enterprise.account_assembly_cycle
msgid "Costing Account For Cycle of Assembly."
msgstr "حساب التكاليف لدورة تركيب المنتج "

#. module: mrp_account_enterprise
#: model:account.analytic.account,name:mrp_account_enterprise.account_assembly_hours
msgid "Costing Account For Hours of Assembly."
msgstr "حساب التكاليف لساعات تركيب المنتج "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__currency_id
msgid "Currency"
msgstr "العملة"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__display_name
msgid "Display Name"
msgstr "اسم العرض "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_duration
msgid "Duration of Operations / Unit"
msgstr "مدة العمليات لكل وحدة "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__date_finished
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "End Date"
msgstr "تاريخ الانتهاء"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__expected_component_cost_unit
msgid "Expected Component Cost / Unit"
msgstr "تكلفة المكون المتوقعة لكل وحدة "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__expected_employee_cost_unit
msgid "Expected Employee Cost / Unit"
msgstr "التكلفة المتوقعة للموظف / الوحدة "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__expected_operation_cost_unit
msgid "Expected Operation Cost / Unit"
msgstr "التكلفة المتوقعة للعمليات لكل وحدة "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__expected_total_cost_unit
msgid "Expected Total Cost / Unit"
msgstr "إجمالي التكلفة المتوقعة / الوحدة "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Group by"
msgstr "التجميع حسب "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__id
msgid "ID"
msgstr "المُعرف"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_report_mrp_account_enterprise_mrp_cost_structure
msgid "MRP Cost Structure Report"
msgstr "تقرير بنية التكلفة في نظام تخطيط متطلبات المواد"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__production_id
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Manufacturing Order"
msgstr "أمر التصنيع"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_mrp_report
msgid "Manufacturing Report"
msgstr "تقرير التصنيع "

#. module: mrp_account_enterprise
#: model_terms:ir.actions.act_window,help:mrp_account_enterprise.mrp_report_dashboard_action
msgid "No data yet!"
msgstr "لا توجد أي بيانات بعد! "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_operation_cost
msgid ""
"Operation cost per unit produced (in product UoM) of manufacturing order"
msgstr "تكلفة العمليات لكل وحدة منتَجة (في قائمة مواد المنتج) لأمر التصنيع "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__unit_duration
msgid "Operation duration (minutes) per unit produced of manufacturing order"
msgstr "مدة العمليات (بالدقائق) لكل وحدة منتَجة لأمر التصنيع "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Order Overview"
msgstr "نظرة عامة على الأمر "

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_product_template
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__product_id
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_search_view
msgid "Product"
msgstr "المنتج"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_report_mrp_account_enterprise_product_template_cost_structure
msgid "Product Template Cost Structure Report"
msgstr "تقرير بنية تكلفة قالب المنتج"

#. module: mrp_account_enterprise
#: model:ir.model,name:mrp_account_enterprise.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: mrp_account_enterprise
#: model:ir.actions.act_window,name:mrp_account_enterprise.mrp_report_dashboard_action
#: model:ir.ui.menu,name:mrp_account_enterprise.mrp_dashboard_menuitem
msgid "Production Analysis"
msgstr "تحليل الإنتاج "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__qty_demanded
msgid "Quantity Demanded"
msgstr "الكمية المطلوبة "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__qty_produced
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_report_pivot_view
msgid "Quantity Produced"
msgstr "الكمية المنتجة"

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__yield_rate
msgid "Ratio of quantity produced over quantity demanded"
msgstr "نسبة الكمية المنتجة إلى الكمية المطلوبة "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Some of the Manufacturing Order(s) selected are not done yet"
msgstr "بعض أوامر التصنيع غير منتهية بعد"

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__component_cost
msgid "Total Component Cost"
msgstr "إجمالي تكلفة المكونات "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__total_cost
msgid "Total Cost"
msgstr "إجمالي التكلفة"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "Total Cost per unit (in"
msgstr "إجمالي التكلفة لكل وحدة (في "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__duration
msgid "Total Duration of Operations"
msgstr "المدة الكلية للعمليات "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__operation_cost
msgid "Total Operation Cost"
msgstr "التكلفة الكلية للعمليات "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__unit_operation_cost
msgid "Total Operation Cost / Unit"
msgstr "التكلفة الكلية للعمليات لكل وحدة "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__component_cost
msgid "Total cost of components for manufacturing order"
msgstr "التكلفة الكلية للمكونات لأمر التصنيع "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__total_cost
msgid ""
"Total cost of manufacturing order (component + operation costs + "
"subcontracting cost)"
msgstr ""
"إجمالي تكاليف أمر التصنيع (المكون + تكاليف العمليات + تكاليف التعاقد من "
"الباطن) "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__operation_cost
msgid "Total cost of operations for manufacturing order"
msgstr "التكلفة الكلية للعمليات لأمر التصنيع "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__duration
msgid "Total duration (minutes) of operations for manufacturing order"
msgstr "المدة الكلية للعمليات (بالدقائق) لأمر التصنيع "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__qty_demanded
msgid "Total quantity demanded in product's UoM"
msgstr "إجمالي الكمية المطلوبة بوحدة قياس المنتج "

#. module: mrp_account_enterprise
#: model:ir.model.fields,help:mrp_account_enterprise.field_mrp_report__qty_produced
msgid "Total quantity produced in product's UoM"
msgstr "إجمالي الكمية المنتجة بوحدة قياس المنتج "

#. module: mrp_account_enterprise
#: model:ir.model.fields,field_description:mrp_account_enterprise.field_mrp_report__yield_rate
msgid "Yield Percentage(%)"
msgstr "نسبة المردود (%) "

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "hours"
msgstr "ساعات"

#. module: mrp_account_enterprise
#: model_terms:ir.ui.view,arch_db:mrp_account_enterprise.mrp_cost_structure
msgid "manufacturing order(s)."
msgstr "أوامر التصنيع."
