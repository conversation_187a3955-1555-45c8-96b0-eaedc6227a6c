# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_account
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"\n"
"Affected valuation layers: %s"
msgstr ""
"\n"
"Capas de valoración perjudicadas: %s"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid " Product cost updated from %(previous)s to %(new_cost)s."
msgstr " Se actualizó el costo del producto de %(previous)s a %(new_cost)s."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_quant.py:0
msgid " [Accounted on %s]"
msgstr " [Contabilizado en %s]"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid " lot/serial number cost updated from %(previous)s to %(new_cost)s."
msgstr ""
"Se actualizó el costo del número de serie o lote de %(previous)s a "
"%(new_cost)s."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid "%(user)s changed cost from %(previous)s to %(new_price)s - %(record)s"
msgstr "%(user)s cambió el costo de %(previous)s a %(new_price)s - %(record)s"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"%(user)s changed stock valuation from  %(previous)s to %(new_value)s - %(product)s\n"
"%(reason)s"
msgstr ""
"%(user)s cambió la valoración de existencias de %(previous)s a %(new_value)s - %(product)s\n"
"%(reason)s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid ""
")\n"
"                            <small class=\"mx-2 fst-italic\">Use a negative added value to record a decrease in the product value</small>"
msgstr ""
")\n"
"                            <small class=\"mx-2 fst-italic\">Utilice un valor agregado negativo para registrar una reducción en el valor del producto</small>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "6.00"
msgstr "6.00"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "<b>Set other input/output accounts on specific </b>"
msgstr "<b>Configure otras cuentas de entrada/salida</b>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_picking
#: model_terms:ir.ui.view,arch_db:stock_account.view_production_lot_form_stock_account
msgid "<span class=\"o_stat_text\">Valuation</span>"
msgstr "<span class=\"o_stat_text\">Valoración</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Product</span>"
msgstr "<span>Producto</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>Quantity</span>"
msgstr "<span>Cantidad</span>"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "<span>SN/LN</span>"
msgstr "<span>NS/NL</span>"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de plan de cuentas"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__account_move_ids
msgid "Account Move"
msgstr "Movimiento de cuenta"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "Account Stock Properties"
msgstr "Propiedades de la cuenta de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_request_count__accounting_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__date
msgid "Accounting Date"
msgstr "Fecha contable"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_move_form_inherit
msgid "Accounting Entries"
msgstr "Asientos contables"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_location_form_inherit
msgid "Accounting Information"
msgstr "Información contable"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Add Manual Valuation"
msgstr "Agregar valoración manual"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Add additional cost (transport, customs, ...) in the value of the product."
msgstr ""
"Agregue el costo adicional (transporte, aduanal, entre otros) al valor del "
"producto."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Added Value"
msgstr "Valor agregado"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__added_value
msgid "Added value"
msgstr "Valor agregado"

#. module: stock_account
#: model:ir.actions.act_window,name:stock_account.action_revalue_layers
msgid "Adjust Valuation"
msgstr "Ajustar valoración"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_res_config_settings__module_stock_landed_costs
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid ""
"Affect landed costs on reception operations and split them among products to"
" update their cost price."
msgstr ""
"Afecte los costos en destino en las operaciones de recepción y divídalos "
"entre productos para actualizar su precio de costo."

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_analytic_account
msgid "Analytic Account"
msgstr "Cuenta analítica"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__analytic_account_line_ids
msgid "Analytic Account Line"
msgstr "Línea de cuenta analítica"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_analytic_plan
msgid "Analytic Plans"
msgstr "Planes analíticos"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__real_time
msgid "Automated"
msgstr "Automatizado"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_stock_accounting_automatic
msgid "Automatic Stock Accounting"
msgstr "Contabilidad automática de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__avg_cost
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__avg_cost
msgid "Average Cost"
msgstr "Costo promedio"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__average
msgid "Average Cost (AVCO)"
msgstr "Costo promedio (AVCO)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "BC46282798"
msgstr "BC46282798"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "Bacon"
msgstr "Tocino"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Cancel"
msgstr "Cancelar"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Cannot find a stock input account for the product %s. You must define one on"
" the product category, or on the location, before processing this operation."
msgstr ""
"No se pudo encontrar una cuenta de entrada de existencias para el producto "
"%s. Debe definir una en la categoría de producto o en la ubicación antes de "
"procesar esta operación."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Cannot find a stock output account for the product %s. You must define one "
"on the product category, or on the location, before processing this "
"operation."
msgstr ""
"No se pudo encontrar una cuenta de salida de existencias para el producto "
"%s. Debe definir una en la categoría de producto o en la ubicación antes de "
"procesar esta operación."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Changing your cost method is an important change that will impact your "
"inventory valuation. Are you sure you want to make that change?"
msgstr ""
"Modificar el método de costo es un cambio importante que influirá en su "
"valoración de inventario. ¿Está seguro de que desea realizar el cambio?"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history_inherit_stock_account
msgid "Choose a date to get the valuation at that date"
msgstr "Elija la fecha en la que se realizará la valoración"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__cogs_origin_id
msgid "Cogs Origin"
msgstr "Origen del costo de los bienes vendidos"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_company
msgid "Companies"
msgstr "Empresas"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__company_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__company_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Company"
msgstr "Empresa"

#. module: stock_account
#: model:ir.model,name:stock_account.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"Configuration error. Please configure the price difference account on the "
"product or its category to process this operation."
msgstr ""
"Error de configuración. Configure la cuenta de diferencias de precio en el "
"producto o su categoría para procesar esta operación."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid "Correction of %s (modification of past move)"
msgstr "Corrección de %s (modificación del movimiento anterior)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__standard_price
msgid "Cost"
msgstr "Costo"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_product__cost_method
#: model:ir.model.fields,field_description:stock_account.field_product_template__cost_method
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__cost_method
msgid "Costing Method"
msgstr "Método de costo"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Costing method change for product category %(category)s: from %(old_method)s"
" to %(new_method)s."
msgstr ""
"Cambio de método de costo para la categoría de producto %(category)s: de "
"%(old_method)s a %(new_method)s."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_id
msgid "Counterpart Account"
msgstr "Cuenta de contrapartida"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_input_categ_id
msgid ""
"Counterpart journal items for all incoming stock moves will be posted in this account, unless there is a specific valuation account\n"
"                set on the source location. This is the default value for all products in this category. It can also directly be set on each product."
msgstr ""
"Los apuntes contables de contrapartida para todos los movimientos de existencias entrantes se publicarán en esta cuenta, a menos que haya una cuenta de valoración específica\n"
"                establecida en la ubicación de origen. Este es el valor predeterminado para todos los productos de esta categoría. También se puede configurar directamente en cada producto."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_picking__country_code
msgid "Country Code"
msgstr "Código de país"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__create_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__create_date
msgid "Created on"
msgstr "Creado el"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__currency_id
msgid "Currency"
msgstr "Moneda"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_quantity_svl
msgid "Current Quantity"
msgstr "Cantidad actual"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__current_value_svl
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Current Value"
msgstr "Valor actual"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Date"
msgstr "Fecha"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_quant__accounting_date
msgid ""
"Date at which the accounting entries will be created in case of automated "
"inventory valuation. If empty, the inventory date will be used."
msgstr ""
"Fecha en la que se crearán los asientos contables en caso de valoración de "
"inventario automatizada. Si está vacío, se usará la fecha de inventario."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer__uom_id
msgid "Default unit of measure used for all stock operations."
msgstr ""
"Unidad de medida de uso predeterminado para todas las operaciones de "
"existencias."

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__description
msgid "Description"
msgstr "Descripción"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__group_lot_on_invoice
msgid "Display Lots & Serial Numbers on Invoices"
msgstr "Mostrar números de lote y de serie en las facturas"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__display_name
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__display_name
msgid "Display Name"
msgstr "Mostrar nombre"

#. module: stock_account
#: model:res.groups,name:stock_account.group_lot_on_invoice
msgid "Display Serial & Lot Number on Invoices"
msgstr "Mostrar el número de serie y lote en las facturas"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Due to a change of product category (from %(old_category)s to "
"%(new_category)s), the costing method has changed for product %(product)s: "
"from %(old_method)s to %(new_method)s."
msgstr ""
"Debido a un cambio en la categoría del producto (de %(old_category)s a "
"%(new_category)s), el método de costo cambió en la plantilla del producto "
"%(product)s: de %(old_method)s a %(new_method)s."

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__fifo
msgid "First In First Out (FIFO)"
msgstr "Primeras entradas, primeras salidas (PEPS)"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Group by..."
msgstr "Agrupar por..."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Has Remaining Qty"
msgstr "Tiene cantidad restante"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__id
msgid "ID"
msgstr "ID"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__lot_valuated
#: model:ir.model.fields,help:stock_account.field_product_template__lot_valuated
msgid "If checked, the valuation will be specific by Lot/Serial number."
msgstr ""
"Si se encuentra seleccionado, la valoración será específica para cada número"
" de serie o lote."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Incoming"
msgstr "Entrante"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_location
msgid "Inventory Locations"
msgstr "Ubicaciones de inventario"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/account_chart_template.py:0
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,field_description:stock_account.field_product_product__valuation
#: model:ir.model.fields,field_description:stock_account.field_product_template__valuation
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__property_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form_stock
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Inventory Valuation"
msgstr "Valuación de inventario"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_line_id
msgid "Invoice Line"
msgstr "Línea de factura"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__price_diff_value
msgid "Invoice value correction with invoice currency"
msgstr "Corrección de valor de factura en la moneda de la factura"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__account_journal_id
msgid "Journal"
msgstr "Diario"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__account_move_id
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: stock_account
#: model:ir.model,name:stock_account.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_config_settings__module_stock_landed_costs
msgid "Landed Costs"
msgstr "Costos en destino"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_uid
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__write_date
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_id
msgid "Linked To"
msgstr "Vinculado a"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Lot %(lot)s has a negative quantity in stock. Correct this"
"                         quantity before enabling lot valuation"
msgstr ""
"El lote %(lot)s tiene una cantidad negativa en inventario. Corríjala"
"                         antes de habilitar la valoración de lotes."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Lot value manually modified (from %(old)s to %(new)s)"
msgstr "Valor del lote modificado manualmente (de %(old)s a %(new)s)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_lot
msgid "Lot/Serial"
msgstr "Número de serie/lote"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__lot_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Lot/Serial Number"
msgstr "Número de serie/lote"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Lot/Serial number Revaluation"
msgstr "Revalorización de números de serie o lote"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move_line.py:0
msgid "Lot/Serial number is mandatory for product valuated by lot"
msgstr ""
"El número de lote o serie es obligatorio para los productos valorados por "
"lote"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
msgid "Lots & Serial numbers will appear on the invoice"
msgstr "Los número de lote y de serie aparecerán en la factura "

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_valuation__manual_periodic
msgid "Manual"
msgstr "Manual"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "Manual Stock Valuation: %s."
msgstr "Valoración manual de existencias: %s."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_valuation
#: model:ir.model.fields,help:stock_account.field_product_product__valuation
#: model:ir.model.fields,help:stock_account.field_product_template__valuation
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__property_valuation
msgid ""
"Manual: The accounting entries to value the inventory are not posted automatically.\n"
"        Automated: An accounting entry is automatically created to value the inventory when a product enters or leaves the company.\n"
"        "
msgstr ""
"Manual: los asientos contables de valoración del inventario no se registran en automático.\n"
"        Automatizado: un asiento contable se crea en automático para evaluar el inventario cuando un producto entra o sale de la empresa.\n"
"        "

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value
msgid "New value"
msgstr "Nuevo valor"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__new_value_by_qty
msgid "New value by quantity"
msgstr "Nuevo valor por cantidad"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "No Reason Given"
msgstr "No proporcionó un motivo"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
msgid "Other Info"
msgstr "Otra información"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Outgoing"
msgstr "Saliente"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_template
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product"
msgstr "Producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_category
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__categ_id
#: model_terms:ir.ui.view,arch_db:stock_account.view_inventory_valuation_search
msgid "Product Category"
msgstr "Categoría del producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimientos de producto (línea de movimiento de stock)"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Product Revaluation"
msgstr "Revaloración de producto"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__product_tmpl_id
msgid "Product Template"
msgstr "Plantilla del producto"

#. module: stock_account
#: model:ir.model,name:stock_account.model_product_product
msgid "Product Variant"
msgstr "Variante del producto"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Product value manually modified (from %(original_price)s to %(new_price)s)"
msgstr ""
"Valor del producto modificado de forma manual (de %(original_price)s a "
"%(new_price)s)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_company__account_production_wip_account_id
msgid "Production WIP Account"
msgstr "Cuenta de producción en curso"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_res_company__account_production_wip_overhead_account_id
msgid "Production WIP Overhead Account"
msgstr "Cuenta de gastos generales de producción en curso"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__quantity
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Quantity"
msgstr "Cantidad"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__quantity_svl
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__quantity_svl
msgid "Quantity Svl"
msgstr "Cantidad de la capa de valoración de existencias"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quant
msgid "Quants"
msgstr "Cantidades"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason"
msgstr "Motivo"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__reason
msgid "Reason of the revaluation"
msgstr "Motivo de la revaluación"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__warehouse_id
msgid "Receipt WH"
msgstr "Almacén de recepción"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__reference
msgid "Reference"
msgstr "Referencia"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__lot_id
msgid "Related lot/serial number"
msgstr "Número de serie/lote relacionado"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_id
msgid "Related product"
msgstr "Producto relacionado"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_qty
msgid "Remaining Qty"
msgstr "Cant. restante"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__remaining_value
msgid "Remaining Value"
msgstr "Valor restante"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_return_picking_line
msgid "Return Picking Line"
msgstr "Línea de recolección de devolución"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "Revaluation of %s"
msgstr "Revaloración de %s"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "Revalue"
msgstr "Revalorar"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_lot.py:0
msgid "Select an existing lot/serial number to be reevaluated"
msgstr ""
"Seleccione un número de lote o serie existente para volver a evaluarlo"

#. module: stock_account
#: model:ir.model.fields.selection,name:stock_account.selection__product_category__property_cost_method__standard
msgid "Standard Price"
msgstr "Precio estándar"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_cost_method
#: model:ir.model.fields,help:stock_account.field_product_product__cost_method
#: model:ir.model.fields,help:stock_account.field_product_template__cost_method
#: model:ir.model.fields,help:stock_account.field_stock_quant__cost_method
msgid ""
"Standard Price: The products are valued at their standard cost defined on the product.\n"
"        Average Cost (AVCO): The products are valued at weighted average cost.\n"
"        First In First Out (FIFO): The products are valued supposing those that enter the company first will also leave it first.\n"
"        "
msgstr ""
"Precio estándar: los productos se valoran según su costo estándar definido en el producto.\n"
"        Costo promedio (AVCO): los productos se valoran según su costo promedio ponderado\n"
"        Primeras entradas, primeras salidas (PEPS): los productos se valoran dando por hecho que los primeros en entrar en la empresa son también los primeros en salir.\n"
"        "

#. module: stock_account
#: model:res.groups,name:stock_account.group_stock_accounting_automatic
msgid "Stock Accounting Automatic"
msgstr "Contabilidad de existencias automática"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_input_categ_id
msgid "Stock Input Account"
msgstr "Cuenta de entrada de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_journal
msgid "Stock Journal"
msgstr "Diario de existencias"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_move
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_move_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_move_id
msgid "Stock Move"
msgstr "Movimiento de stock"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_account_output_categ_id
msgid "Stock Output Account"
msgstr "Cuenta de salida de existencias"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_quantity_history
msgid "Stock Quantity History"
msgstr "Historial de cantidad de existencias "

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_forecasted_product_product
msgid "Stock Replenishment Report"
msgstr "Reporte de reabastecimiento de existencias"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_request_count
msgid "Stock Request an Inventory Count"
msgstr "Solicitar un recuento de inventario"

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/stock_account_forecasted/forecasted_header.js:0
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_action
#: model:ir.actions.act_window,name:stock_account.stock_valuation_layer_report_action
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Stock Valuation"
msgstr "Valoración de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_category__property_stock_valuation_account_id
msgid "Stock Valuation Account"
msgstr "Cuenta de valoración de existencias"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_in_account_id
msgid "Stock Valuation Account (Incoming)"
msgstr "Cuenta de valoración de existencias (entrada)"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_location__valuation_out_account_id
msgid "Stock Valuation Account (Outgoing)"
msgstr "Cuenta de valoración de existencias (salida)"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer
#: model:ir.model.fields,field_description:stock_account.field_account_bank_statement_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_account_move_line__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_product_product__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_move__stock_valuation_layer_ids
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__stock_valuation_layer_ids
msgid "Stock Valuation Layer"
msgstr "Capa de valoración de existencias"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_product__company_currency_id
msgid ""
"Technical field to correctly show the currently selected company's currency "
"that corresponds to the totaled value of the product's valuation layers"
msgstr ""
"Campo técnico para mostrar de forma correcta la moneda seleccionada de la "
"empresa que corresponde al valor total de las capas de valoración del "
"producto"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_picking__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"El código ISO del país en dos caracteres.\n"
"Puede utilizar este campo para realizar una búsqueda rápida."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The Stock Input and/or Output accounts cannot be the same as the Stock "
"Valuation account."
msgstr ""
"Las cuentas de entrada o salida de existencias no pueden ser las mismas que "
"la cuenta de valoración de existencias."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The action leads to the creation of a journal entry, for which you don't "
"have the access rights."
msgstr ""
"La acción ocasiona la creación de un asiento contable del que no tiene "
"permisos de acceso."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "The added value doesn't have any impact on the stock valuation"
msgstr "El valor agregado no influye en la valoración de existencias"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent state: some are entering and other "
"are leaving the company."
msgstr ""
"Las líneas de movimiento no están en un estado consistente: algunas entran y"
" otras salen de la empresa."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent states: they are doing an "
"intercompany in a single step while they should go through the intercompany "
"transit location."
msgstr ""
"Las líneas de movimiento no están en estados consistentes: están llevando a "
"cabo una operación interempresarial en un solo paso cuando deberían pasar "
"por la ubicación de tránsito interno de la empresa."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"The move lines are not in a consistent states: they do not share the same "
"origin or destination company."
msgstr ""
"Las líneas de movimiento no están en estados consistentes: no comparten la "
"misma empresa de origen o de destino."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"The stock accounts should be set in order to use the automatic valuation."
msgstr ""
"Es necesario que configure las cuentas de inventario para usar la valoración"
" automática."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move_line.py:0
msgid ""
"The stock valuation of a move is based on the type of the source and "
"destination locations. As the move is already processed, you cannot modify "
"the locations in a way that changes the valuation logic defined during the "
"initial processing."
msgstr ""
"La valoración de existencias de un movimiento se basa en el tipo de "
"ubicación de origen y destino. El movimiento ya está procesado, así que no "
"puede modificar las ubicaciones para cambiar la lógica de valoración "
"definida durante el procesamiento inicial."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid ""
"The value of a stock valuation layer cannot be negative. Landed cost could "
"be use to correct a specific transfer."
msgstr ""
"El valor de una capa de valoración de existencias no puede ser negativo. Los"
" costos en destino pueden usarse para corregir un traslado específico."

#. module: stock_account
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_action
#: model_terms:ir.actions.act_window,help:stock_account.stock_valuation_layer_report_action
msgid ""
"There are no valuation layers. Valuation layers are created when there are "
"product moves that impact the valuation of the stock."
msgstr ""
"No hay capas de valoración. Estas se crean cuando hay movimientos de "
"productos que influyen en la valoración de las existencias."

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/fields/boolean_confirm.js:0
msgid ""
"This operation might lead in a loss of data. Valuation will be identical for"
" all lots/SN. Do you want to proceed ? "
msgstr ""
"Esta operación puede ocasionar que pierda datos, la valuación será idéntica "
"para todos los números de serie o lotes. ¿Desea continuar?"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"This product is valuated by lot/serial number. Changing the cost will update"
" the cost of every lot/serial number in stock."
msgstr ""
"Este producto se valua por número de serie o lote. Si cambia el costo "
"actualizará el costo de todos los números de serie o lote en sus "
"existencias. "

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Moved Quantity"
msgstr "Cantidad total movida"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_report_tree
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Remaining Quantity"
msgstr "Cantidad restante total"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_report_tree
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Total Remaining Value"
msgstr "Valor total restante"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__total_value
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__total_value
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__value
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Total Value"
msgstr "Valor total"

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_picking
msgid "Transfer"
msgstr "Trasladar"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,help:stock_account.field_stock_return_picking_line__to_refund
msgid ""
"Trigger a decrease of the delivered/received quantity in the associated Sale"
" Order/Purchase Order"
msgstr ""
"Activar una disminución de la cantidad entregada/recibida en la orden de "
"venta/compra asociada"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
msgid "Unit"
msgstr "Unidad"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
msgid "Unit Cost"
msgstr "Costo unitario"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__unit_cost
msgid "Unit Value"
msgstr "Valor unitario"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer__uom_id
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__product_uom_name
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_move__to_refund
#: model:ir.model.fields,field_description:stock_account.field_stock_return_picking_line__to_refund
msgid "Update quantities on SO/PO"
msgstr "Actualizar cantidades en órdenes de venta o compra"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid "Updating lot valuation for product %s."
msgstr "Actualizando la valoración de lotes para el producto %s"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_in_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved from an internal location into this location, instead of the "
"generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Se utiliza para la valoración de inventario en tiempo real. Al establecerla "
"en una ubicación virtual (que no es de tipo interno), esta cuenta se usará "
"para mantener el valor de los productos que se mueven de una ubicación "
"interna a esta ubicación, en lugar de la cuenta de salida de existencias "
"genérica establecida en el producto. No influye en las ubicaciones internas."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_location__valuation_out_account_id
msgid ""
"Used for real-time inventory valuation. When set on a virtual location (non "
"internal type), this account will be used to hold the value of products "
"being moved out of this location and into an internal location, instead of "
"the generic Stock Output Account set on the product. This has no effect for "
"internal locations."
msgstr ""
"Se utiliza para la valoración de inventario en tiempo real. Al establecerla "
"en una ubicación virtual (que no es de tipo interno), esta cuenta se usará "
"para mantener el valor de los productos que se mueven fuera de la ubicación "
"a una ubicación interna, en lugar de la cuenta de salida de existencias "
"genérica establecida en el producto. No influye en las ubicaciones internas."

#. module: stock_account
#: model:ir.ui.menu,name:stock_account.menu_valuation
#: model_terms:ir.ui.view,arch_db:stock_account.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_form
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quant_tree_editable_inherit
msgid "Valuation"
msgstr "Valoración"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__company_currency_id
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__company_currency_id
msgid "Valuation Currency"
msgstr "Moneda de la valoración"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_valuation_layer_revaluation__adjusted_layer_ids
msgid "Valuation Layers"
msgstr "Capa de valoración"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.product_product_stock_tree_inherit_stock_account
msgid "Valuation Report"
msgstr "Reporte de valoración"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_tree
#: model_terms:ir.ui.view,arch_db:stock_account.view_stock_quantity_history_inherit_stock_account
msgid "Valuation at Date"
msgstr "Valoración a la fecha"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__lot_valuated
#: model:ir.model.fields,field_description:stock_account.field_product_template__lot_valuated
msgid "Valuation by Lot/Serial number"
msgstr "Valoración por número de serie o lote"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"Valuation method change for product category %(category)s: from "
"%(old_method)s to %(new_method)s."
msgstr ""
"Cambio de método de valuación para la categoría de producto %(category)s: de"
" %(old_method)s a %(new_method)s."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_valuation_layer_revaluation__adjusted_layer_ids
msgid "Valuations layers being adjusted"
msgstr "Capas de valoración que se están ajustando"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_stock_quant__value
msgid "Value"
msgstr "Valor"

#. module: stock_account
#. odoo-javascript
#: code:addons/stock_account/static/src/stock_account_forecasted/forecasted_header.xml:0
msgid "Value On Hand:"
msgstr "Valor a la mano:"

#. module: stock_account
#: model:ir.model.fields,field_description:stock_account.field_product_product__value_svl
#: model:ir.model.fields,field_description:stock_account.field_stock_lot__value_svl
msgid "Value Svl"
msgstr "Valor de la capa de valoración de existencias"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_stock_lot__standard_price
msgid ""
"Value of the lot (automatically computed in AVCO).\n"
"        Used to value the product when the purchase cost is not known (e.g. inventory adjustment).\n"
"        Used to compute margins on sale orders."
msgstr ""
"El valor del lote (calculado de forma automática con AVCO)\n"
"        Se utiliza para valorar el producto cuando se desconoce el costo de compra (por ejemplo, en un ajuste de inventario).\n"
"         Se utiliza para calcular los márgenes de las órdenes de venta."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid "Warning"
msgstr "Advertencia"

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_valuation_account_id
msgid ""
"When automated inventory valuation is enabled on a product, this account "
"will hold the current value of the products."
msgstr ""
"Cuando la valoración de inventario automatizada está habilitada en un "
"producto, esta cuenta mantendrá el valor actual de los productos."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_account_output_categ_id
msgid ""
"When doing automated inventory valuation, counterpart journal items for all outgoing stock moves will be posted in this account,\n"
"                unless there is a specific valuation account set on the destination location. This is the default value for all products in this category.\n"
"                It can also directly be set on each product."
msgstr ""
"Al realizar una valoración de inventario automatizada, los apuntes contables de contrapartida para todos los movimientos de existencias salientes se registrarán en esta cuenta,\n"
"                a menos que haya una cuenta de valoración específica establecida en la ubicación de destino. Este es el valor predeterminado para todos los productos de esta categoría.\n"
"                También se puede configurar directamente en cada producto."

#. module: stock_account
#: model:ir.model.fields,help:stock_account.field_product_category__property_stock_journal
msgid ""
"When doing automated inventory valuation, this is the Accounting Journal in "
"which entries will be automatically posted when stock moves are processed."
msgstr ""
"Al realizar una valoración de inventario automatizada, las entradas se "
"registrarán en automático en este diario contable al procesar los "
"movimientos de existencias."

#. module: stock_account
#: model:ir.model,name:stock_account.model_stock_valuation_layer_revaluation
msgid "Wizard model to reavaluate a stock inventory for a product"
msgstr ""
"Modelo de asistente para revalorizar un inventario de existencias para un "
"producto"

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot adjust the valuation of a layer with zero quantity"
msgstr "No puede ajustar la valoración de una capa si la cantidad es cero."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot adjust valuation without a product"
msgstr "No puede ajustar la valoración sin un producto."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You cannot change the costing method of product valuated by lot/serial "
"number."
msgstr ""
"No puede cambiar el método de costo de un producto valuado por número de "
"serie o lote."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You cannot change the product category of a product valuated by lot/serial "
"number."
msgstr ""
"No puede cambiar la categoría de un producto valuado por número de serie o "
"lote."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue a product with a standard cost method."
msgstr "No puede revalorizar un producto con un método de costo estándar."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue a product with an empty or negative stock."
msgstr "No puede revalorizar un producto con inventario vacío o negativo."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/wizard/stock_valuation_layer_revaluation.py:0
msgid "You cannot revalue multiple products at once"
msgstr "No puede revalorizar varios productos a la vez."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_lot.py:0
msgid ""
"You cannot update the cost of a product in automated valuation as it leads "
"to the creation of a journal entry, for which you don't have the access "
"rights."
msgstr ""
"No puede actualizar el costo de un producto en la valoración automatizada, "
"ocasionará un asiento contable del que no tendrá permisos de acceso."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any input valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"No tiene ninguna cuenta de valoración de entrada definida en su categoría de"
" producto. Debe definir una antes de procesar esta operación."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any output valuation account defined on your product "
"category. You must define one before processing this operation."
msgstr ""
"No definió ninguna cuenta de valoración de salida en su categoría de "
"producto. Debe definir una antes de procesar esta operación."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
msgid ""
"You don't have any stock input account defined on your product category. You"
" must define one before processing this operation."
msgstr ""
"No tiene ninguna cuenta de entrada de existencias definida en su categoría "
"de producto. Debe definir una antes de procesar esta operación."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_move.py:0
msgid ""
"You don't have any stock journal defined on your product category, check if "
"you have installed a chart of accounts."
msgstr ""
"No tiene ningún diario de existencias definido en su categoría de producto, "
"verifique si instaló un plan de cuentas."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/product.py:0
#: code:addons/stock_account/models/stock_move.py:0
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid ""
"You don't have any stock valuation account defined on your product category."
" You must define one before processing this operation."
msgstr ""
"No tiene ninguna cuenta de valoración de existencias definida en su "
"categoría de producto. Debe definir una antes de procesar esta operación."

#. module: stock_account
#. odoo-python
#: code:addons/stock_account/models/stock_valuation_layer.py:0
msgid "You must set a counterpart account on your product category."
msgstr ""
"Debe establecer una cuenta de contrapartida en su categoría de producto."

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "by"
msgstr "por"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_revaluation_form_view
msgid "for"
msgstr "para"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.view_category_property_form
msgid "locations"
msgstr "ubicaciones"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_account_report_invoice_document
msgid "units"
msgstr "unidades"

#. module: stock_account
#: model_terms:ir.ui.view,arch_db:stock_account.stock_valuation_layer_graph
msgid "valuation graph"
msgstr "gráfico de valoración"
