<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record id="res_config_settings_view_form" model="ir.ui.view">
            <field name="name">res.config.settings.view.form.inherit.stock.account</field>
            <field name="model">res.config.settings</field>
            <field name="inherit_id" ref="stock.res_config_settings_view_form"/>
            <field name="arch" type="xml">
                <block id="production_lot_info" position="after">
                    <block title="Valuation" name="valuation_setting_container">
                        <setting id="additional_cost_setting" title="Affect landed costs on reception operations and split them among products to update their cost price." documentation="/applications/inventory_and_mrp/inventory/management/reporting/integrating_landed_costs.html" help="Add additional cost (transport, customs, ...) in the value of the product.">
                            <field name="module_stock_landed_costs"/>
                            <div class="content-group">
                                <div name="landed_cost_info"/>
                            </div>
                        </setting>
                        <setting invisible="not group_stock_production_lot" id="group_lot_on_invoice" help="Lots &amp; Serial numbers will appear on the invoice">
                            <field name="group_lot_on_invoice"/>
                        </setting>
                    </block>
                </block>
            </field>
        </record>
    </data>
</odoo>
