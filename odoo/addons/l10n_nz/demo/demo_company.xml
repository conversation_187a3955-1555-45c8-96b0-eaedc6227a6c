<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_nz" model="res.partner" forcecreate="1">
        <field name="name">NZ Company</field>
        <field name="vat">49098576</field>
        <field name="company_registry">9429047488083</field>
        <field name="street">Victoria Street</field>
        <field name="city">Hamilton</field>
        <field name="country_id" ref="base.nz"/>
        <field name="state_id" ref="base.state_nz_wtc"/>
        <field name="zip">3247</field>
        <field name="phone">+64 21 123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.nzexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_nz" model="res.company" forcecreate="1">
        <field name="name">NZ Company</field>
        <field name="partner_id" ref="base.partner_demo_company_nz"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_nz')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_nz'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>nz</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_nz')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
