# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_jo
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-28 07:33+0000\n"
"PO-Revision-Date: 2023-03-28 07:33+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_exempt_local_zero
msgid "10. Exempted Local Sales (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_exempt_local_zero_tax
msgid "10. Exempted Local Sales (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_no_deductible_zero
msgid "11. Non-deductible tax (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_no_deductible_zero_tax
msgid "11. Non-deductible tax (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_sixteen
msgid "2a. Purchase Tax 16% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_sixteen_tax
msgid "2a. Purchase Tax 16% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_ten
msgid "2b. Purchase Tax 10% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_ten_tax
msgid "2b. Purchase Tax 10% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_four
msgid "2c. Purchase Tax 4% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_four_tax
msgid "2c. Purchase Tax 4% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_other
msgid "2d. Purchase Other Tax (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_other_tax
msgid "2d. Purchase Other Tax (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_import_sixteen
msgid "3a. Import Tax 16% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_import_sixteen_tax
msgid "3a. Import Tax 16% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_import_ten
msgid "3b. Import Tax 10% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_import_ten_tax
msgid "3b. Import Tax 10% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_import_four
msgid "3c. Import Tax 4% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_import_four_tax
msgid "3c. Import Tax 4% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_import_other
msgid "3d. Import Other Tax (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_import_other_tax
msgid "3d. Import Other Tax (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_deferred_supply
msgid "4. Imports Deferred Supply Tax (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_deferred_supply_tax
msgid "4. Imports Deferred Supply Tax (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_exempt
msgid "5. Exempted Purchases and Imports (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_exempt_tax
msgid "5. Exempted Purchases and Imports (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_sixteen
msgid "6a. Sales Tax 16% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_sixteen_tax
msgid "6a. Sales Tax 16% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_ten
msgid "6b. Sales Tax 10% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_ten_tax
msgid "6b. Sales Tax 10% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_four
msgid "6c. Sales Tax 4% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_four_tax
msgid "6c. Sales Tax 4% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_other
msgid "6d. Sales Other Tax (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_other_tax
msgid "6d. Sales Other Tax (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_zero
msgid "7. Sales Tax 0% (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_zero_tax
msgid "7. Sales Tax 0% (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_export_zero
msgid "8. Exported Sales (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_export_zero_tax
msgid "8. Exported Sales (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_no_tax_zero
msgid "9. Sales that are not subject to tax (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_no_tax_zero_tax
msgid "9. Sales that are not subject to tax (Tax)"
msgstr ""

#. module: l10n_jo
#: model:ir.model,name:l10n_jo.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_jo
#: model:account.report.column,name:l10n_jo.tax_report_vat_return_balance
msgid "Balance"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_net_due
msgid "Net Tax Due"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_return_net
msgid "Net Tax Due (Total)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_base
msgid "Purchase Tax (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_purchase_tax
msgid "Purchase Tax (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_base
msgid "Sales Tax (Base)"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_sales_tax
msgid "Sales Tax (Tax)"
msgstr ""

#. module: l10n_jo
#: model:account.report,name:l10n_jo.tax_report_vat_return
msgid "Tax Report Jordan"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_recoverable
msgid "Total Tax Paid - Recoverable"
msgstr ""

#. module: l10n_jo
#: model:account.report.line,name:l10n_jo.tax_report_vat_payable
msgid "Total Tax Received - To be Forwarded To Authority"
msgstr ""
