# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* helpdesk_sale_loyalty
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: helpdesk_sale_loyalty
#. odoo-python
#: code:addons/helpdesk_sale_loyalty/wizard/helpdesk_sale_coupon_generate.py:0
msgid "Compose Email"
msgstr "编写邮件"

#. module: helpdesk_sale_loyalty
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
msgid "Coupon"
msgstr "优惠券"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__program
msgid "Coupon Program"
msgstr "优惠券方案"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__points_granted
msgid "Coupon Value"
msgstr "优惠券价值"

#. module: helpdesk_sale_loyalty
#. odoo-python
#: code:addons/helpdesk_sale_loyalty/wizard/helpdesk_sale_coupon_generate.py:0
msgid "Coupon created"
msgstr "已创建的优惠券"

#. module: helpdesk_sale_loyalty
#. odoo-python
#: code:addons/helpdesk_sale_loyalty/models/helpdesk_ticket.py:0
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_ticket_view_form_inherit_helpdesk_sale_coupon
msgid "Coupons"
msgstr "优惠券"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__create_uid
msgid "Created by"
msgstr "创建人"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__create_date
msgid "Created on"
msgstr "创建日期"

#. module: helpdesk_sale_loyalty
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_sale_coupon_generate_view_form
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.loyalty_card_view_form_helpdesk_sale_loyalty
msgid "Discard"
msgstr "丢弃"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: helpdesk_sale_loyalty
#: model:ir.model,name:helpdesk_sale_loyalty.model_helpdesk_sale_coupon_generate
msgid "Generate Sales Coupon from Helpdesk"
msgstr "从服务台生成销售优惠券"

#. module: helpdesk_sale_loyalty
#: model:ir.actions.act_window,name:helpdesk_sale_loyalty.helpdesk_sale_coupon_generate_action
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_sale_coupon_generate_view_form
msgid "Generate a Coupon"
msgstr "获取一份优惠券"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_ticket__coupon_ids
msgid "Generated Coupons"
msgstr "生成优惠券"

#. module: helpdesk_sale_loyalty
#: model:ir.model,name:helpdesk_sale_loyalty.model_helpdesk_ticket
msgid "Helpdesk Ticket"
msgstr "服务台工单"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__id
msgid "ID"
msgstr "ID"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: helpdesk_sale_loyalty
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.loyalty_card_view_form_helpdesk_sale_loyalty
msgid "Send"
msgstr "发送"

#. module: helpdesk_sale_loyalty
#: model_terms:ir.ui.view,arch_db:helpdesk_sale_loyalty.helpdesk_sale_coupon_generate_view_form
msgid "Send by Email"
msgstr "通过邮件发送"

#. module: helpdesk_sale_loyalty
#: model:ir.model.fields,field_description:helpdesk_sale_loyalty.field_helpdesk_sale_coupon_generate__valid_until
msgid "Valid Until"
msgstr "有效期至"
