# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 07:48+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Czech (https://app.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_employee.py:0
msgid "%s work entries"
msgstr "%s  pracovn<PERSON>ch z<PERSON>namů"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_employee_view_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                            Work Entries\n"
"                            </span>"
msgstr ""

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "<span>Hours</span>"
msgstr "<span>Hodin</span>"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__active
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__active
msgid "Active"
msgstr "Aktivní"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Archived"
msgstr "Archivováno"

#. module: hr_work_entry
#: model:hr.work.entry.type,name:hr_work_entry.work_entry_type_attendance
msgid "Attendance"
msgstr "Docházka"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "Beginning"
msgstr "Začátek"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__cancelled
msgid "Cancelled"
msgstr "Zrušeno"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry__code
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__code
msgid ""
"Careful, the Code is used in many references, changing it could lead to "
"unwanted changes."
msgstr ""
"Pozor, kód se používá v mnoha odkazech, jeho změna by mohla vést k nechtěným"
" změnám."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__color
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__color
msgid "Color"
msgstr "Barva"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__company_id
msgid "Company"
msgstr "Společnost"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__conflict
msgid "Conflict"
msgstr "Konflikt"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Conflicting"
msgstr "Konfliktní"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__conflict
msgid "Conflicts"
msgstr "Konflikty"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__country_id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__country_id
msgid "Country"
msgstr "Země"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__country_code
msgid "Country Code"
msgstr "Kód země"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_type_action
msgid "Create a new work entry type"
msgstr "Vytvořit nový typ pracovního záznamu"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_uid
msgid "Created by"
msgstr "Vytvořeno uživatelem"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__create_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__create_date
msgid "Created on"
msgstr "Vytvořeno dne"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Current Month"
msgstr "Aktuální měsíc"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Date"
msgstr "Datum"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__department_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Department"
msgstr "Oddělení"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Description"
msgstr "Popis"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__display_name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__display_name
msgid "Display Name"
msgstr "Zobrazovací název"

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__draft
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Draft"
msgstr "Návrh"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__duration
msgid "Duration"
msgstr "Trvání"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_employee
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__employee_id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__employee_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Employee"
msgstr "Zaměstnanec"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_tree
msgid "End"
msgstr "Konec"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__external_code
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__external_code
msgid "External Code"
msgstr ""

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_start
msgid "From"
msgstr "Od"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR pracovní záznam"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR typ pracovního záznamu"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_employee__has_work_entries
msgid "Has Work Entries"
msgstr ""

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__id
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__id
msgid "ID"
msgstr "ID"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__active
msgid ""
"If the active field is set to false, it will allow you to hide the work "
"entry type without removing it."
msgstr ""
"Pokud je aktivní pole nastaveno na false, umožní vám skrýt typ pracovního "
"záznamu bez jeho odstranění."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_uid
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno uživatelem"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__write_date
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__write_date
msgid "Last Updated on"
msgstr "Naposledy upraveno dne"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_user_work_entry_employee__user_id
msgid "Me"
msgstr "Já"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__name
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__name
msgid "Name"
msgstr "Název"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_action
msgid "No data to display"
msgstr "Žádná data k zobrazení"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Note: Validated work entries cannot be modified."
msgstr "Poznámka: Ověřené pracovní záznamy nelze měnit."

#. module: hr_work_entry
#: model:hr.work.entry.type,name:hr_work_entry.overtime_work_entry_type
msgid "Overtime Hours"
msgstr ""

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__code
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__code
msgid "Payroll Code"
msgstr "Mzdový kód"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_leaves
msgid "Resource Time Off Detail"
msgstr "Podrobnosti o volnu zdroje"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Search Work Entry"
msgstr "Hledat záznam práce"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_search
msgid "Search Work Entry Type"
msgstr "Vyhledat typ záznamu práce"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry_type__sequence
msgid "Sequence"
msgstr "Sekvence"

#. module: hr_work_entry
#. odoo-javascript
#: code:addons/hr_work_entry/static/src/xml/work_entry_templates.xml:0
msgid "Solve conflicts first"
msgstr "Nejprve vyřešte konflikty"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Start Date"
msgstr "Počáteční datum"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_start_before_end
msgid "Starting time should be before end time."
msgstr "Počáteční čas by měl být před časem ukončení."

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__state
msgid "State"
msgstr "Stát"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO kód země ve dvou znacích. Toto pole můžete použít pro rychlé "
"vyhledávání."

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "The same code cannot be associated to multiple work entry types."
msgstr "Stejný kód nelze přidružit k více typům záznamů práce."

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "This work entry is validated. You can't delete it."
msgstr ""

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Time Off Options"
msgstr "Možnosti volna"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__date_stop
msgid "To"
msgstr "Na"

#. module: hr_work_entry
#: model_terms:ir.actions.act_window,help:hr_work_entry.hr_work_entry_action
msgid ""
"Try to add some records, or make sure that there is no active filter in the "
"search bar."
msgstr ""
"Zkuste přidat nějaké záznamy, nebo se ujistěte, že není žádný aktivní filtr "
"ve vyhledávací liště."

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Type"
msgstr "Typ"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "Undefined"
msgstr "Nedefinováno"

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "Undefined Type"
msgstr "Nedefinovaný typ"

#. module: hr_work_entry
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry__external_code
#: model:ir.model.fields,help:hr_work_entry.field_hr_work_entry_type__external_code
msgid "Use this code to export your data to a third party"
msgstr ""

#. module: hr_work_entry
#: model:ir.model.fields.selection,name:hr_work_entry.selection__hr_work_entry__state__validated
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_search
msgid "Validated"
msgstr "Potvrzeno"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entries_no_validated_conflict
msgid "Validated work entries cannot overlap"
msgstr "Ověřené pracovní záznamy se nesmí překrývat"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_resource_calendar_attendance
msgid "Work Detail"
msgstr "Podrobnosti práce"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_pivot
msgid "Work Entries"
msgstr "Pracovní záznamy"

#. module: hr_work_entry
#: model:ir.model,name:hr_work_entry.model_hr_user_work_entry_employee
msgid "Work Entries Employees"
msgstr "Pracovní záznamy zaměstnanců"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_action_conflict
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_calendar
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry"
msgstr "Pracovní záznam"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_view_form
msgid "Work Entry Name"
msgstr "Název pracovní položky"

#. module: hr_work_entry
#: model:ir.model.fields,field_description:hr_work_entry.field_hr_work_entry__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_attendance__work_entry_type_id
#: model:ir.model.fields,field_description:hr_work_entry.field_resource_calendar_leaves__work_entry_type_id
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_work_entry.resource_calendar_leaves_view_search_inherit
msgid "Work Entry Type"
msgstr "Typ pracovní položky"

#. module: hr_work_entry
#: model_terms:ir.ui.view,arch_db:hr_work_entry.hr_work_entry_type_view_form
msgid "Work Entry Type Name"
msgstr "Název typu pracovní položky"

#. module: hr_work_entry
#: model:ir.actions.act_window,name:hr_work_entry.hr_work_entry_type_action
msgid "Work Entry Types"
msgstr "Typy pracovních záznamů"

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_work_entry__work_entry_has_end
msgid "Work entry must end. Please define an end date or a duration."
msgstr ""
"Vstup do práce musí skončit. Definujte prosím datum ukončení nebo dobu "
"trvání."

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid ""
"You can't change the Country of this work entry type cause it's currently "
"used by the system. You need to delete related working entries first."
msgstr ""

#. module: hr_work_entry
#. odoo-python
#: code:addons/hr_work_entry/models/hr_work_entry.py:0
msgid "You can't change the country of this specific work entry type."
msgstr ""

#. module: hr_work_entry
#: model:ir.model.constraint,message:hr_work_entry.constraint_hr_user_work_entry_employee_user_id_employee_id_unique
msgid "You cannot have the same employee twice."
msgstr "Nemůžete mít stejného zaměstnance dvakrát."
