# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* frontdesk
# 
# Translators:
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(name)s just checked-in. Requested Drink: %(drink)s."
msgstr ""
"%(name)s только что зарегистрировался. Запрошенный напиток: %(drink)s."

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(station)s Check-In: %(visitor)s"
msgstr ""

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%(station)s Check-In: %(visitor)s to meet %(host)s"
msgstr ""

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_visitor.py:0
msgid "%s just checked-in."
msgstr "%s только что зарегистрировался."

#. module: frontdesk
#: model:ir.actions.report,print_report_name:frontdesk.frontdesk_visitor_print_badge
msgid "'Badge - %s' % (object.name).replace('/', '')"
msgstr "'Значок - %s' % (object.name).replace('/', '')"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "22 Oct 24 14:20:10"
msgstr ""

#. module: frontdesk
#: model:mail.template,body_html:frontdesk.frontdesk_mail_template
msgid ""
"<div>\n"
"                    <p>Hello <t t-if=\"ctx.get('host_name')\"><t t-out=\"ctx.get('host_name')\"/>, </t><b><t t-out=\"object.name\"/></b> <t t-if=\"object.phone\">(<t t-out=\"object.phone\"/>)</t><t t-if=\"object.company\">, coming from <t t-out=\"object.company\"/></t> is asking to meet you at <t t-out=\"object.station_id.name\"/>. Please let them know you'll be there shortly.\n"
"                    </p>\n"
"                </div>\n"
"            "
msgstr ""

#. module: frontdesk
#: model_terms:web_tour.tour,rainbow_man_message:frontdesk.frontdesk_tour
msgid ""
"<span><b>Congratulations!!!</b> You have created your first visitor.\n"
"        </span>"
msgstr ""

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "<strong>Visiting:</strong>"
msgstr ""

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__active
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__active
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__active
msgid "Active"
msgstr "Активный"

#. module: frontdesk
#: model:ir.actions.client,name:frontdesk.frontdesk_visitor_action_configure_properties_field
msgid "Add Properties"
msgstr "Добавить свойства"

#. module: frontdesk
#: model:res.groups,name:frontdesk.frontdesk_group_administrator
msgid "Administrator"
msgstr "Администратор"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Allow visitor to select a drink during registration"
msgstr "Дайте посетителю возможность выбрать напиток во время регистрации"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Allows the visitor to pick the host of the meeting from the list"
msgstr "Позволяет посетителю выбрать хозяина встречи из списка"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Archived"
msgstr "Архивировано"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/quick_check_in/quick_check_in.xml:0
msgid "Are you one of these people?"
msgstr "Вы один из этих людей?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__authenticate_guest
msgid "Authenticate Guest"
msgstr "Аутентификация гостя"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Back"
msgstr "Назад"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "By Responsible"
msgstr "Ответственный"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "Cancel"
msgstr "Отменить"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__canceled
msgid "Cancelled"
msgstr "Отменен"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__check_in
msgid "Check In"
msgstr "Заезд"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__check_out
msgid "Check Out"
msgstr "Выезд"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Check in"
msgstr "Заезд"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Check out"
msgstr "Выезд"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "CheckIn"
msgstr "Регистрация"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "CheckIn Station"
msgstr "Станция регистрации"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__checked_in
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Checked-In"
msgstr "Регистрация"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__checked_out
msgid "Checked-Out"
msgstr "Проверенный вариант"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Checked-out"
msgstr "Выезд"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Checkout"
msgstr "Оформить заказ"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Close"
msgstr "Закрыть"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__company_id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__company_id
msgid "Company"
msgstr "Компания"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Company Logo"
msgstr "Логотип компании"

#. module: frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_config
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Configuration"
msgstr "Конфигурация"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Configure Drinks"
msgstr "Настройка напитков"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/host_page/host_page.xml:0
msgid "Confirm"
msgstr "Подтвердить"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__create_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__create_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__create_uid
msgid "Created by"
msgstr "Создано"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__create_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__create_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__create_date
msgid "Created on"
msgstr "Создано"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__theme__dark
msgid "Dark"
msgstr "Темный"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Date"
msgstr "Дата"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__description
msgid "Description"
msgstr "Описание"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__display_name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__display_name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Do you want something to drink?"
msgstr "Хотите что-нибудь выпить?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_ids
msgid "Drink"
msgstr "Напитки"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__drink_image
msgid "Drink Image"
msgstr "Изображение напитка"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "Drink Name"
msgstr "Название напитка"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__served
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Drink Served"
msgstr "Подаваемый напиток"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "Drink image"
msgstr "Изображение напитка"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Drink to Serve"
msgstr "Напиток для подачи"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_drink
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_drinks_report
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__drink_ids
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_drinks_config
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report_drinks
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_report_view_graph
msgid "Drinks"
msgstr "Напитки"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_drink_to_serve_visitor
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_to_serve
msgid "Drinks to Serve"
msgstr "Напитки для подачи"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Drinks to serve"
msgstr "Напитки для подачи"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__duration
msgid "Duration"
msgstr "Продолжительность"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "E.g. What's your Name"
msgstr "Например, \"Как тебя зовут\""

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_email
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__email
msgid "Email"
msgstr "Email"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__mail_template_id
msgid "Email Template"
msgstr "Шаблон электронной почты"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Enter the visitor's name."
msgstr ""

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Favorite"
msgstr "Избранное"

#. module: frontdesk
#: model:ir.actions.client,name:frontdesk.frontdesk_action_install_kiosk_pwa
#: model:ir.model,name:frontdesk.model_frontdesk_frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_root
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Frontdesk"
msgstr "Frontdesk"

#. module: frontdesk
#: model:ir.model,name:frontdesk.model_frontdesk_drink
msgid "Frontdesk Drink"
msgstr "Напиток для фронтдеска"

#. module: frontdesk
#: model:mail.template,name:frontdesk.frontdesk_mail_template
msgid "Frontdesk Email Template"
msgstr "Шаблон электронной почты Frontdesk"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__name
msgid "Frontdesk Name"
msgstr "Имя сотрудника отдела кадров"

#. module: frontdesk
#: model:sms.template,name:frontdesk.frontdesk_sms_template
msgid "Frontdesk SMS Template"
msgstr "Шаблон SMS для фронтдеска"

#. module: frontdesk
#: model:ir.model,name:frontdesk.model_frontdesk_visitor
msgid "Frontdesk Visitors"
msgstr "Посетители стойки регистрации"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Group By"
msgstr "Группировать по"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_guest_on_site_visitor
msgid "Guest On Site"
msgstr "Гость на территории"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__guest_on_site
msgid "Guests On Site"
msgstr "Гости на территории"

#. module: frontdesk
#: model:sms.template,body:frontdesk.frontdesk_sms_template
msgid ""
"Hello, Your visitor {{ object.name }} {{ '(%s)' % object.phone if "
"object.phone else '' }} {{ '(%s)' % object.company if object.company else ''"
" }} wants to meet you at {{ object.station_id.name }}. Please let them know "
"you'll be there shortly."
msgstr ""
"Здравствуйте, ваш посетитель {{ object.name }} {{ '(%s)' % object.phone if "
"object.phone else '' }} {{ '(%s)' % object.company if object.company else ''"
" }} хочет встретиться с вами на {{ object.station_id.name }}. Пожалуйста, "
"сообщите им, что вы скоро приедете."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Henry"
msgstr ""

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Here, you'll see list of all the visitors."
msgstr ""

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Host"
msgstr "Хост"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__host_ids
msgid "Host Name"
msgstr "Имя Хоста"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__host_selection
msgid "Host Selection"
msgstr "Выбор хозяина"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "How can we delight you?"
msgstr "Как мы можем порадовать вас?"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__id
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__id
msgid "ID"
msgstr "ID"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__image
msgid "Image"
msgstr "Изображение"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Install"
msgstr "Установить"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__is_favorite
msgid "Is Favorite"
msgstr "Любимый"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Karen"
msgstr ""

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Kiosk"
msgstr "Киоск"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__kiosk_url
msgid "Kiosk URL"
msgstr "URL-адрес киоска"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
msgid "Last Check-In: %s hours ago"
msgstr "Последняя регистрация: %s часов назад"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
msgid "Last Check-In: %s minutes ago"
msgstr "Последняя регистрация: %s минут назад"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__write_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__write_uid
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__write_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__write_date
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__latest_check_in
msgid "Latest Check In"
msgstr "Последняя регистрация"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Let's add a new visitor."
msgstr ""

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__theme__light
msgid "Light"
msgstr "Светлый"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/host_page/many2one/many2one.js:0
msgid "Loading..."
msgstr "Загрузка..."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid ""
"Looking for a better way to manage your visitors? \n"
" It begins right here."
msgstr ""

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "MY PVT LTD"
msgstr ""

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__message
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_form
msgid "Message"
msgstr "Сообщение"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "My Station"
msgstr "Моя станция"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__name
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__name
msgid "Name"
msgstr "Имя"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_drink
msgid "No drinks to offer to visitors. Let's add one!"
msgstr ""

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_frontdesk_tree
msgid "No stations found. Let's create one!"
msgstr ""

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_drink_to_serve_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_guest_on_site_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_planned_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_station_visitor
msgid "No visitors yet. Let's add one!"
msgstr ""

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "No, thank you"
msgstr "Нет, спасибо"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__none
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__none
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__none
msgid "None"
msgstr "Нет"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/drink_page/drink_page.xml:0
msgid "Nothing, thanks."
msgstr "Ничего, спасибо."

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_sms
msgid "Notify by SMS"
msgstr "Уведомление по SMS"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_discuss
msgid "Notify by discuss"
msgstr "Уведомить путем обсуждения"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__notify_email
msgid "Notify by email"
msgstr "Уведомление по электронной почте"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Notify the host on guest arrival"
msgstr "Сообщите хозяину о прибытии гостя"

#. module: frontdesk
#: model_terms:ir.actions.act_window,help:frontdesk.action_frontdesk_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_drink_to_serve_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_guest_on_site_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_planned_visitor
#: model_terms:ir.actions.act_window,help:frontdesk.action_open_station_visitor
msgid "Odoo helps you to track all information related to your visitors."
msgstr ""

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__drink_offer
msgid "Offer Drinks"
msgstr "Предлагайте напитки"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "On Site"
msgstr "Сайт %d"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Open Desk"
msgstr "Открытый стол"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Open Kiosk"
msgstr "Открытый киоск"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Open host chat window when guest arrives"
msgstr "Открывайте окно чата хозяина при появлении гостя"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__optional
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__optional
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__optional
msgid "Optional"
msgstr "Необязательно"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Options"
msgstr "Опции"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_company
msgid "Organization"
msgstr "Организация"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__pending
msgid "Pending"
msgstr "В ожидании"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__notify_user_ids
msgid "People to Notify"
msgstr "Люди, которых нужно уведомить"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__ask_phone
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__phone
msgid "Phone"
msgstr "Телефон"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_planned_visitor
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_visitor__state__planned
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Planned"
msgstr "Проектируемый"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Please have a seat."
msgstr "Пожалуйста, присаживайтесь."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_qr_expired
msgid "Please rescan it."
msgstr "Пожалуйста, проведите повторное сканирование."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Print Badge"
msgstr "Печать бейджа"

#. module: frontdesk
#: model:ir.actions.report,name:frontdesk.frontdesk_visitor_print_badge
msgid "Print Visitor Badge"
msgstr ""

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__visitor_properties
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_tree
msgid "Properties"
msgstr "Свойства"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
msgid "QR Code"
msgstr "QR Код"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_qr_expired
msgid "QR Code Expired."
msgstr "Срок действия QR-кода истек."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/quick_check_in/quick_check_in.xml:0
msgid "Quick Check In"
msgstr "Быстрая регистрация"

#. module: frontdesk
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report
msgid "Reporting"
msgstr "Отчет"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Request additional information upon registering"
msgstr "Запросите дополнительную информацию при регистрации"

#. module: frontdesk
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_company__required
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_email__required
#: model:ir.model.fields.selection,name:frontdesk.selection__frontdesk_frontdesk__ask_phone__required
msgid "Required"
msgstr "Обязательно"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Responsible"
msgstr "Ответственный"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__responsible_ids
msgid "Responsibles"
msgstr "Responsibles"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__sms_template_id
msgid "SMS Template"
msgstr "SMS шаблон"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Save the visitor."
msgstr ""

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__access_token
msgid "Security Token"
msgstr "Токен безопасности"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/js/tours/frontdesk.js:0
msgid "Select or create a station on the fly from where the visitor arrived."
msgstr ""

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Select the color of the Desk"
msgstr "Выберите цвет стола"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__self_check_in
msgid "Self Check-In"
msgstr "Самостоятельная регистрация"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Send an SMS to the host on guest arrival"
msgstr "Отправьте SMS хозяину о прибытии гостя"

#. module: frontdesk
#: model:mail.template,description:frontdesk.frontdesk_mail_template
msgid "Sent to hosts on guest arrival"
msgstr "Отправляется хозяевам по прибытии гостей"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_drink__sequence
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "Sequence"
msgstr "Последовательность"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Show a QR code on the welcome screen to check-in from mobile"
msgstr ""
"Показывайте QR-код на экране приветствия для регистрации с мобильных "
"устройств"

#. module: frontdesk
#: model:ir.model.fields,help:frontdesk.field_frontdesk_frontdesk__self_check_in
msgid ""
"Shows a QR code in the interface, for guests to check in from their mobile "
"phone."
msgstr ""
"Показывает QR-код в интерфейсе, чтобы гости могли зарегистрироваться с "
"мобильного телефона."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Side Message"
msgstr "Побочное сообщение"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__station_id
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Station"
msgstr "Станция"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_search
msgid "Station Name"
msgstr "Имя Пикета"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_open_station_visitor
msgid "Station Visitors"
msgstr "Посетители станции"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_frontdesk_tree
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_stations
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_stations_config
msgid "Stations"
msgstr "Станции"

#. module: frontdesk
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_station_report
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
msgid "Statistics"
msgstr "Статистика"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__state
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Status"
msgstr "Статус"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "Thank you for registering!"
msgstr "Спасибо за регистрацию!"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__theme
msgid "Theme"
msgstr "Тема"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
msgid "Today"
msgstr "Сегодня"

#. module: frontdesk
#: model:res.groups,name:frontdesk.frontdesk_group_user
msgid "User"
msgstr "Пользователь"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_search
#: model_terms:ir.ui.view,arch_db:frontdesk.print_visitor_badge
msgid "Visitor"
msgstr "Посетитель"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_visitor__company
msgid "Visitor Company"
msgstr "Компания \"Посетитель"

#. module: frontdesk
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__visitor_properties_definition
msgid "Visitor Properties"
msgstr "Свойства посетителей"

#. module: frontdesk
#. odoo-python
#: code:addons/frontdesk/models/frontdesk_frontdesk.py:0
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_visitor
#: model:ir.actions.act_window,name:frontdesk.action_frontdesk_visitors_report
#: model:ir.model.fields,field_description:frontdesk.field_frontdesk_frontdesk__visitor_ids
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_report_visitors
#: model:ir.ui.menu,name:frontdesk.frontdesk_menu_visitors
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_kanban
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_station_report_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_report_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_graph
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_visitor_view_pivot
msgid "Visitors"
msgstr "Посетители"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
msgid "Welcome"
msgstr "Добро пожаловать"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Who are you visiting?"
msgstr "Кого вы навещаете?"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/navbar/navbar.xml:0
msgid "Who are you?"
msgstr "Кто вы?"

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_frontdesk_view_form
msgid "Write message..."
msgstr "Написать сообщение..."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "Yes, please"
msgstr "Да, пожалуйста"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "You have been registered!"
msgstr "Вы зарегистрированы!"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Company"
msgstr "Ваша компания"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Email"
msgstr "Ваш Email"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Name"
msgstr "Ваше имя"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "Your Phone Number"
msgstr "Ваш телефон"

#. module: frontdesk
#: model:mail.template,subject:frontdesk.frontdesk_mail_template
msgid "Your Visitor {{ object.name }} Requested To Meet You"
msgstr "Ваш посетитель {{ object.name }} Запросил встречу с вами"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "Your drink is on the way."
msgstr "Ваш напиток уже в пути."

#. module: frontdesk
#: model_terms:ir.ui.view,arch_db:frontdesk.frontdesk_drink_view_form
msgid "e.g. Coca-Cola"
msgstr "например, Кока-Кола"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. John Doe"
msgstr "например, Васильев Андрей"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. My Company"
msgstr "например, \"Моя компания\""

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/visitor_form/visitor_form.xml:0
msgid "e.g. <EMAIL>"
msgstr "например, <EMAIL>"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/end_page/end_page.xml:0
msgid "has been informed."
msgstr "был проинформирован."

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/welcome_page/welcome_page.xml:0
msgid "to"
msgstr "в"

#. module: frontdesk
#. odoo-javascript
#: code:addons/frontdesk/static/src/register_page/register_page.xml:0
msgid "will get back to you."
msgstr "свяжется с вами."
