# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_km
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 10:39+0000\n"
"PO-Revision-Date: 2023-11-30 10:39+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_km
#: model:ir.model,name:l10n_km.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_km
#: model:account.report.column,name:l10n_km.account_tax_report_km_balance
msgid "Base"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_credit_reported
msgid "Credit balance from previous return"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_credit_to_report
msgid "Credit balance to report for the next return"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_due
msgid "Due tax for the month"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_operations_exempt
msgid "Exempt"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_operations_export
msgid "Export"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_operations
msgid "Operations"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_prepayment
msgid "Prepayments made"
msgstr ""

#. module: l10n_km
#. odoo-python
#: code:addons/l10n_km/models/template_km_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_km
#. odoo-python
#: code:addons/l10n_km/models/template_km.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_km
#: model:account.report.column,name:l10n_km.account_tax_report_km_tax
msgid "Tax"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_operations_10
msgid "Taxable at 10%"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_operations_25
msgid "Taxable at 25%"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_operations_3
msgid "Taxable at 3%"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_operations_5
msgid "Taxable at 5%"
msgstr ""

#. module: l10n_km
#: model:account.report.line,name:l10n_km.account_tax_report_line_km_operations_7_5
msgid "Taxable at 7.5%"
msgstr ""

#. module: l10n_km
#: model:account.report,name:l10n_km.account_tax_report_km
msgid "VAT Report"
msgstr ""
