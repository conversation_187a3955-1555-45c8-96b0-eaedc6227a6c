<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_ir56b">
        <t t-call="web.basic_layout" t-if="report_data">
            <t t-foreach="docs" t-as="employee">
                <t t-set="date_from" t-value="report_data.get('date_from', datetime.date.today())"/>
                <t t-set="date_to" t-value="report_data.get('date_to', datetime.date.today())"/>
                <t t-set="emp_start_date" t-value="report_data.get('StartDateOfEmp', datetime.date.today())"/>
                <t t-set="emp_end_date" t-value="report_data.get('EndDateOfEmp', datetime.date.today())"/>
                <t t-set="emp_period_str" t-value="'{} - {}'.format(emp_start_date.strftime('%d/%m/%Y'), emp_end_date.strftime('%d/%m/%Y'))"/>
                <div class="page position-relative report-ird ir56b ms-5 me-1">
                    <div class="row">
                        <div class="col text-center text-uppercase fw-bold" style="margin-top: 4rem">
                            Inland Revenue Department <br/>
                            Employer's Return of Remuneration and Pensions <br/>
                            For the year from
                            <span t-esc="date_from" t-options="{'widget': 'date', 'format': 'dd MMMM YYYY'}"/>
                            To
                            <span t-esc="date_to" t-options="{'widget': 'date', 'format': 'dd MMMM YYYY'}"/>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">1.</td>
                                    <td style="width: 150mm;">
                                        Employer's File No. : <span class="ps-2" t-esc="report_data.get('FileNo')" />
                                    </td>
                                    <td class="text-end">
                                        <span>Sheet No.:</span>
                                        <span class="d-inline-block fw-bold fs-5" style="width: 8.5rem" t-esc="report_data.get('ERN', 1)" />
                                        <span class="d-inline-block ps-1">****</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td/>
                                    <td colspan="2">
                                        Name of Employer: <span class="ps-2" t-esc="report_data.get('ErName')" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">2.</td>
                                    <td>
                                        Name of Employee or Pensioner :
                                        <span class="ps-2 text-uppercase" t-esc="', '.join(i for i in [report_data.get('Surname'), report_data.get('GivenName')] if i)" />
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold fs-5" t-esc="report_data.get('Surname')" />
                                        <span class="d-inline-block ps-1">****</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td/>
                                    <td colspan="2">
                                        Full name in Chinese :
                                        <span class="ps-2" t-esc="report_data.get('NameInChinese')" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">3.</td>
                                    <td>(a) H.K. Identity Card Number :</td>
                                    <td class="text-end">
                                        <span class="fw-bold fs-5" t-esc="report_data.get('HKID') or report_data.get('PpNum')" />
                                        <span class="d-inline-block ps-1">****</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td/>
                                    <td colspan="2">(b) Passport Number and place of issue (if Employee has no H.K. Identity Card) :</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">4.</td>
                                    <td>Sex (M = Male, F = Female) :</td>
                                    <td class="text-end">
                                        <span class="fw-bold fs-5" t-esc="report_data.get('Sex')" />
                                        <span class="d-inline-block ps-1">****</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">5.</td>
                                    <td>Marital Status (1 = Single / Widowed / Divorced / Living Apart, 2 = Married) :</td>
                                    <td class="text-end">
                                        <span class="fw-bold fs-5" t-esc="report_data.get('MaritalStatus')" />
                                        <span class="d-inline-block ps-1">****</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">6.</td>
                                    <td>
                                        (a) If married, full name of spouse :
                                        <span class="ps-2" t-esc="report_data.get('SpouseName')" />
                                    </td>
                                </tr>
                                <tr>
                                    <td/>
                                    <td>
                                        (b) Spouse's H.K. Identity Card Number / Passport Number and place of issue (if known) :
                                        <span class="ps-2" t-esc="report_data.get('SpouseHKID') or report_data.get('SpousePpNum')" />
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">7.</td>
                                    <td>
                                        Residential Address :
                                        <span class="ps-2" t-esc="report_data.get('employee_address')" />
                                    </td>
                                </tr>
                                <tr>
                                    <td>8.</td>
                                    <td>Postal Address (if different from item 7 above) :</td>
                                </tr>
                                <tr>
                                    <td>9.</td>
                                    <td colspan="4">Capacity in which employed : <span class="text-uppercase ps-5" t-esc="report_data.get('Capacity')" /></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">10.</td>
                                    <td>
                                        Period of employment for the year from
                                        <t t-esc="date_from" t-options="{'widget': 'date', 'format': 'dd MMMM YYYY'}"/>
                                        to
                                        <t t-esc="date_to" t-options="{'widget': 'date', 'format': 'dd MMMM YYYY'}"/> :
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold fs-5" t-esc="emp_start_date" t-options="{'widget': 'date', 'format': 'dd/MM/YYYY'}" />
                                        <span class="fs-4 px-1">to</span>
                                        <span class="fw-bold fs-5" t-esc="emp_end_date" t-options="{'widget': 'date', 'format': 'dd/MM/YYYY'}" />
                                        <span class="d-inline-block ps-1">****</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">11.</td>
                                    <td>
                                        Particulars of income accruing for the year from
                                        <t t-esc="date_from" t-options="{'widget': 'date', 'format': 'dd MMMM YYYY'}"/>
                                        to
                                        <t t-esc="date_to" t-options="{'widget': 'date', 'format': 'dd MMMM YYYY'}"/> :
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row" style="padding-left: 6.5mm;">
                        <div class="col" >
                            <table style="font-size: 15px;">
                                <tr>
                                    <td style="width: 6.5mm;" />
                                    <td class="text-center text-decoration-underline">Particulars</td>
                                    <td class="text-center text-decoration-underline" style="width: 60mm;">Period</td>
                                    <td class="text-center text-decoration-underline" style="width: 38.5mm;">Amount (HK$)</td>
                                </tr>
                                <tr>
                                    <td />
                                    <td />
                                    <td />
                                    <td class="text-center text-decoration-underline">(EXCLUDE CENTS)</td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(a)</td>
                                    <td>Salary / Wages</td>
                                    <td class="text-center">
                                        <span t-if="report_data.get('AmtOfSalary')" t-esc="emp_period_str" />
                                    </td>
                                    <td class="text-end px-5">
                                        <span t-if="report_data.get('AmtOfSalary')" t-esc="'{:,}'.format(report_data.get('AmtOfSalary'))" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(b)</td>
                                    <td>Leave Pay</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(c)</td>
                                    <td>Director's Fee</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(d)</td>
                                    <td>Commission / Fees</td>
                                    <td class="text-center">
                                        <span t-if="report_data.get('AmtOfCommFee')" t-esc="emp_period_str" />
                                    </td>
                                    <td class="text-end px-5">
                                        <span t-if="report_data.get('AmtOfCommFee')" t-esc="'{:,}'.format(report_data['AmtOfCommFee'])" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(e)</td>
                                    <td>Bonus</td>
                                    <td class="text-center">
                                        <span t-if="report_data.get('AmtOfBonus')" t-esc="emp_period_str" />
                                    </td>
                                    <td class="text-end px-5">
                                        <span t-if="report_data.get('AmtOfBonus')" t-esc="'{:,}'.format(report_data.get('AmtOfBonus'))" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(f)</td>
                                    <td>Back Pay, Payment in Lieu of Notice, Terminal Awards or Gratuities</td>
                                    <td class="text-center">
                                        <span t-if="report_data.get('AmtOfBpEtc')" t-esc="emp_period_str" />
                                    </td>
                                    <td class="text-end px-5">
                                        <span t-if="report_data.get('AmtOfBpEtc')" t-esc="'{:,}'.format(report_data.get('AmtOfBpEtc'))" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(g)</td>
                                    <td>Certain Payments from Retirement Schemes</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(h)</td>
                                    <td>Salaries Tax paid by Employer</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(i)</td>
                                    <td>Education Benefits</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(j)</td>
                                    <td>Gain realized under Share Option Scheme</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(k)</td>
                                    <td colspan="3">Any other Rewards, Allowances or Perquisites</td>
                                </tr>
                                <tr>
                                    <td />
                                    <td class="pt-1">
                                        <span class="d-inline-block" style="width: 6.5mm;">(1)</span>Nature :
                                        <span class="ps-2" t-if="report_data.get('AmtOfOtherRAP1')" t-esc="report_data['NatureOtherRAP1']"/>
                                    </td>
                                    <td class="text-center">
                                        <span t-if="report_data.get('AmtOfOtherRAP1')" t-esc="emp_period_str" />
                                    </td>
                                    <td class="text-end px-5">
                                        <span t-if="report_data.get('AmtOfOtherRAP1')" t-esc="'{:,}'.format(report_data.get('AmtOfOtherRAP1'))" />
                                    </td>
                                </tr>
                                <tr>
                                    <td />
                                    <td class="pt-1"><span class="d-inline-block" style="width: 6.5mm;">(2)</span>Nature :</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                                <tr>
                                    <td/>
                                    <td class="pt-1"><span class="d-inline-block" style="width: 6.5mm;">(3)</span>Nature :</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                                <tr>
                                    <td class="pt-1">(l)</td>
                                    <td>Pensions</td>
                                    <td class="text-center"></td>
                                    <td class="text-end px-5"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col text-end">
                            <span class="pe-1">Total :</span>
                            <span class="d-inline-block text-end pe-2 fw-bold fs-5 border-top border-dark" style="width: 7rem; border-bottom:3px double;"><t t-esc="'{:,}'.format(report_data.get('TotalIncome', 0))" /></span>
                            <span class="d-inline-block ps-1">****</span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">12.</td>
                                    <td>Particulars of Place of Residence provided (0 = Not provided, 1 = Provided) :</td>
                                    <td class="text-end">
                                        <span class="fw-bold fs-5" t-esc="report_data.get('PlaceOfResInd', 0)" />
                                        <span class="d-inline-block ps-1">****</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td />
                                    <td colspan="2">
                                        <span class="d-block overflow-hidden" style="height:1.2rem;">
                                            Address 1 :
                                            <span class="ps-2" t-esc="report_data.get('AddrOfPlace1')"/>
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td />
                                    <td colspan="2">
                                        <span class="d-block overflow-hidden" style="height:1.2rem;">
                                            Address 2 :
                                            <span class="ps-2" t-esc="report_data.get('AddrOfPlace2')"/>
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row" style="padding-left: 6.5mm;">
                        <div class="col">
                            <table>
                                <tr>
                                    <td />
                                    <td class="text-center" style="width: 57mm"><span class="border-bottom border-dark px-2">Place of Residence 1</span></td>
                                    <td class="text-center" style="width: 57mm"><span class="border-bottom border-dark px-2">Place of Residence 2</span></td>
                                </tr>
                                <tr>
                                    <td>Nature :</td>
                                    <td class="text-center"><span t-esc="report_data.get('NatureOfPlace1')"/></td>
                                    <td class="text-center"><span t-esc="report_data.get('NatureOfPlace2')"/></td>
                                </tr>
                                <tr>
                                    <td>Period Provided :</td>
                                    <td class="text-center"><span t-esc="report_data.get('PerOfPlace1')"/></td>
                                    <td class="text-center"><span t-esc="report_data.get('PerOfPlace2')"/></td>
                                </tr>
                                <tr>
                                    <td>Rent Paid to Landlord by Employer :</td>
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                </tr>
                                <tr>
                                    <td>Rent Paid to Landlord by Employee :</td>
                                    <td class="text-center"><span t-if="report_data.get('RentPaidEe1')" t-esc="'{:,}'.format(report_data.get('RentPaidEe1'))"/></td>
                                    <td class="text-center"><span t-if="report_data.get('RentPaidEe2')" t-esc="'{:,}'.format(report_data.get('RentPaidEe2'))"/></td>
                                </tr>
                                <tr>
                                    <td>Rent Refunded to Employee by Employer :</td>
                                    <td class="text-center"><span t-if="report_data.get('RentRefund1')" t-esc="'{:,}'.format(report_data.get('RentRefund1'))"/></td>
                                    <td class="text-center"><span t-if="report_data.get('RentRefund2')" t-esc="'{:,}'.format(report_data.get('RentRefund2'))"/></td>
                                </tr>
                                <tr>
                                    <td>Rent Paid to Employer by Employee :</td>
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">13.</td>
                                    <td colspan="2">Whether the employee was wholly or partly paid either in Hong Kong or elsewhere</td>
                                </tr>
                                <tr>
                                    <td />
                                    <td>by a non-Hong Kong company (0 = No, 1 = Yes) :</td>
                                    <td class="text-end">
                                        <span class="fw-bold fs-5">0</span>
                                        <span class="d-inline-block ps-1">****</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td />
                                    <td colspan="2">If yes, please state :</td>
                                </tr>
                                <tr>
                                    <td />
                                    <td colspan="2">Name of the non-Hong Kong company :</td>
                                </tr>
                                <tr>
                                    <td />
                                    <td colspan="2">Address :</td>
                                </tr>
                                <tr>
                                    <td />
                                    <td colspan="2">Amount (if known) (This amount must also be included in item 11) :</td>
                                </tr>

                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-9">
                            <table>
                                <tr>
                                    <td style="width: 6.5mm;">14.</td>
                                    <td colspan="2">Remarks :
                                        ERMC <span class="px-2" t-esc="'{:,}'.format(abs(report_data.get('AmtOfERMC', 0)))" />
                                        ERVC <span class="px-2" t-esc="'{:,}'.format(abs(report_data.get('AmtOfERVC', 0)))" />
                                        EEMC <span class="px-2" t-esc="'{:,}'.format(abs(report_data.get('AmtOfEEMC', 0)))" />
                                        EEVC <span class="px-2" t-esc="'{:,}'.format(abs(report_data.get('AmtOfEEVC', 0)))" />
                                    </td>
                                </tr>
                                <tr>
                                    <td />
                                    <td style="width: 54mm;">
                                        <div class="border border-dark text-center position-relative mt-1" style="height: 35mm; margin-left: 3.5mm;">
                                            <span class="position-absolute fixed-bottom fst-italic" style="font-size: 0.8rem;">Space for Employer's official chop</span>
                                        </div>
                                    </td>
                                    <td class="align-middle" style="padding-left: 2rem;">
                                        <div>
                                            Signature :<br/><br/>
                                            Name : <span class="ps-2" t-esc="report_data.get('NAME_OF_SIGNER')" /><br/><br/>
                                            Designation : <span class="ps-2" t-esc="report_data.get('Designation')" /><br/><br/>
                                            Date : <span class="ps-2" t-esc="report_data.get('SubDate')" t-options="{'widget': 'date', 'format': 'dd/MM/YYYY'}" />
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="pt-3" colspan="3" style="font-size: 0.9rem;">IR56B</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-3" style="width:22%;">
                            <div class="border border-dark" style="width: 53mm;">
                                <div style="height: 38mm;" />
                                <div class="border-top border-dark text-center py-1" style="font-size: 0.8rem;">For Official Use</div>
                            </div>
                        </div>
                    </div>
                </div>
            </t>
        </t>
    </template>

</odoo>
