# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_slides_survey
# 
# Translators:
# Wil Odoo, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_channel__members_certified_count
msgid "# Certified Attendees"
msgstr "# 已认证的参与者"

#. module: website_slides_survey
#. odoo-python
#: code:addons/website_slides_survey/models/survey_survey.py:0
msgid "- %(certification)s (Courses - %(courses)s)"
msgstr "- %(certification)s（课程：%(courses)s）"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_survey_view_kanban
msgid ""
"<br/>\n"
"                        <span class=\"text-muted\">Courses</span>"
msgstr ""
"<br/>\n"
"                                    <span class=\"text-muted\">已注册</span>"

#. module: website_slides_survey
#: model:mail.template,body_html:website_slides_survey.mail_template_user_input_certification_failed
msgid ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        Dear <t t-out=\"object.partner_id.name or 'participant' or ''\">participant</t><br/><br/>\n"
"        Unfortunately, you have failed the certification and are no longer a member of the course: <t t-out=\"object.slide_partner_id.channel_id.name or ''\">Basics of Gardening</t>.<br/><br/>\n"
"        Don't hesitate to enroll again!\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.slide_partner_id.channel_id.website_url)\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                Enroll now\n"
"            </a>\n"
"        </div>\n"
"        Thank you for your participation.\n"
"    \n"
"</div>\n"
"            "
msgstr ""
"<div style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        亲爱的 <t t-out=\"object.partner_id.name or 'participant' or ''\">参与者</t><br/><br/>\n"
"        很抱歉，您未通过认证，并且不再是该课程的成员: <t t-out=\"object.slide_partner_id.channel_id.name or ''\">园艺基础</t>.<br/><br/>\n"
"        不要犹豫，再次报名！\n"
"        </p><div style=\"margin: 16px 0px 16px 0px;\">\n"
"            <a t-att-href=\"(object.slide_partner_id.channel_id.website_url)\" style=\"background-color: #875A7B; padding: 8px 16px 8px 16px; text-decoration: none; color: #fff; border-radius: 5px; font-size:13px;\">\n"
"                现在报名\n"
"            </a>\n"
"        </div>\n"
"        感谢您的参与。\n"
"    \n"
"</div>\n"
"            "

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "<i class=\"fa fa-arrow-right\"/> Get Certified"
msgstr "<i class=\"fa fa-arrow-right\"/> 获得认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid ""
"<i class=\"fa fa-download\" aria-label=\"Download certification\" "
"title=\"Download Certification\"/>"
msgstr ""
"<i class=\"fa fa-download\" aria-label=\"下载证书\" title=\"Download "
"Certification\"/>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"Download "
"certification\" title=\"Download certification\"/> Download certification"
msgstr ""
"<i class=\"fa fa-fw fa-trophy\" role=\"img\" aria-label=\"下载 certification\""
" title=\"Download certification\"/> 下载证书"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_fill_form_done_inherit_website_slides
msgid ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share certification\" title=\"Share certification\"/>\n"
"                        Share your certification"
msgstr ""
"<i class=\"fa fa-share-alt\" aria-label=\"Share certification\" title=\"分享认证\"/>\n"
"                        分享您的认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "<i class=\"fa fa-share-alt\" aria-label=\"Share\" title=\"Share\"/>"
msgstr "<i class=\"fa fa-share-alt\" aria-label=\"分享\" title=\"分享\"/>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid "<i class=\"oi oi-arrow-right me-1\"/>Add Questions to this Survey"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>添加问题至此调查"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "<i class=\"oi oi-arrow-right me-1\"/>All Certifications"
msgstr "<i class=\"oi oi-arrow-right me-1\"/>所有认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "<i class=\"oi oi-arrow-right\"/> See Certifications"
msgstr "<i class=\"oi oi-arrow-right\"/>查看认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid "<i class=\"text-muted\"> awarded users</i>"
msgstr "<i class=\"text-muted\"> 授予用户</i>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.courses_home_inherit_survey
msgid "<span class=\"ms-1\">Certifications</span>"
msgstr "<span class=\"ms-1\">认证</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_form
msgid "<span class=\"o_stat_text\">Certified</span>"
msgstr "<span class=\"o_stat_text\">已认证</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.all_user_card
msgid "<span class=\"text-muted small fw-bold\">Certifications</span>"
msgstr "<span class=\"text-muted small fw-bold\">认证</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.top3_user_card
msgid "<span class=\"text-muted\">Certifications</span>"
msgstr "<span class=\"text-muted\">证书</span>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.course_main
msgid "<span>Start Now</span><i class=\"oi oi-chevron-right ms-2 align-middle\"/>"
msgstr "<span>现在开始</span><i class=\"oi oi-chevron-right ms-2 align-middle\"/>"

#. module: website_slides_survey
#: model:ir.model.constraint,message:website_slides_survey.constraint_slide_slide_check_survey_id
msgid "A slide of type 'certification' requires a certification."
msgstr "“认证”类型的幻灯片需要认证。"

#. module: website_slides_survey
#: model:ir.model.constraint,message:website_slides_survey.constraint_slide_slide_check_certification_preview
msgid "A slide of type certification cannot be previewed."
msgstr "无法预览类型认证的幻灯片。"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_view_form
msgid "Add Certification"
msgstr "添加认证"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
msgid "Add Questions to this Survey"
msgstr "在问卷中添加问题"

#. module: website_slides_survey
#: model_terms:ir.actions.act_window,help:website_slides_survey.slide_slide_action_certification
msgid "Add a new certification"
msgstr "添加新的认证"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__is_preview
msgid "Allow Preview"
msgstr "允许预览"

#. module: website_slides_survey
#. odoo-python
#: code:addons/website_slides_survey/models/survey_survey.py:0
msgid ""
"Any Survey listed below is currently used as a Course Certification and cannot be deleted:\n"
"%s"
msgstr ""
"以下列出的任何调查目前被用作课程认证，不能删除:\n"
"%s"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_3
msgid "Ash"
msgstr "灰烬"

#. module: website_slides_survey
#: model_terms:ir.actions.act_window,help:website_slides_survey.survey_survey_action_slides
msgid ""
"Assess the level of understanding of your attendees\n"
"                <br>and send them a document if they pass the test."
msgstr ""
"评估与会者的理解程度\n"
"                <br>如果他们通过了测试，就给他们发一份文件。"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "Attempt n°"
msgstr "尝试n°"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_survey_view_tree_slides
msgid "Avg Score (%)"
msgstr "平均分数 (%)"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_5
msgid "Bed"
msgstr "床"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_4
msgid "Beech"
msgstr "榉木"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid "Begin Certification"
msgstr "开始认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.course_main
msgid "Begin your <b>certification</b> today!"
msgstr "今天开始 <b>认证</b>!"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__slide_category
msgid "Category"
msgstr "类别"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.js:0
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__survey_id
#: model:ir.model.fields.selection,name:website_slides_survey.selection__slide_slide__slide_category__certification
#: model:ir.model.fields.selection,name:website_slides_survey.selection__slide_slide__slide_type__certification
msgid "Certification"
msgstr "认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "Certification Attempts"
msgstr "颁发的证书"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid "Certification Badges"
msgstr "认证徽标"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_channel_ids
msgid "Certification Courses"
msgstr "认证课程"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_slide_partner_view_search
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_slide_partner_view_tree
msgid "Certification Passed"
msgstr "通过认证"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_ids
msgid "Certification Slides"
msgstr "认证幻灯片"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide_partner__survey_scoring_success
msgid "Certification Succeeded"
msgstr "认证成功"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_survey_view_tree_slides
msgid "Certification Title"
msgstr "证书名称"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide_partner__user_input_ids
msgid "Certification attempts"
msgstr "认证尝试"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.o_wss_certification_icon
msgid "Certification icon"
msgstr "认证图标"

#. module: website_slides_survey
#. odoo-python
#: code:addons/website_slides_survey/controllers/slides.py:0
msgid "Certification slides are completed when the survey is succeeded."
msgstr "成功完成调查后，认证幻灯片就会完成。"

#. module: website_slides_survey
#: model:ir.actions.act_window,name:website_slides_survey.slide_slide_action_certification
#: model:ir.actions.act_window,name:website_slides_survey.survey_survey_action_slides
#: model:ir.ui.menu,name:website_slides_survey.website_slides_menu_courses_certification
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "Certifications"
msgstr "认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "Certifications are exams that you successfully passed. <br/>"
msgstr "认证是您成功通过的考试。<br/>"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_sidebar_done_button
msgid "Certifications you have passed cannot be marked as not done"
msgstr "您已通过的认证不能被标记为未完成"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_channel_partner__survey_certification_success
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_channel_partner_view_search
msgid "Certified"
msgstr "具有证明的"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_1
msgid "Chair"
msgstr "椅子"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_channel_partner
msgid "Channel / Partners (Members)"
msgstr "频道/合作伙伴（会员）"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid "Congratulations, you passed the Certification!"
msgstr "恭喜你，你通过了认证!"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_channel
msgid "Course"
msgstr "课程"

#. module: website_slides_survey
#. odoo-python
#: code:addons/website_slides_survey/models/survey_survey.py:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_survey_view_form
msgid "Courses"
msgstr "课程"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_survey__slide_channel_count
msgid "Courses Count"
msgstr "课程计数"

#. module: website_slides_survey
#: model_terms:ir.actions.act_window,help:website_slides_survey.survey_survey_action_slides
msgid "Create a Certification"
msgstr "创建一个认证"

#. module: website_slides_survey
#: model:slide.slide,name:website_slides_survey.slide_slide_demo_6_0
msgid "DIY Furniture Certification"
msgstr "DIY家具认证"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_3
msgid "Desk"
msgstr "桌子"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
msgid "Download certification"
msgstr "下载证书"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_1
msgid "Fir"
msgstr "冷杉"

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1
msgid "Furniture"
msgstr "家具"

#. module: website_slides_survey
#: model:slide.slide,name:website_slides_survey.slide_slide_demo_5_4
#: model:survey.survey,title:website_slides_survey.furniture_certification
msgid "Furniture Creation Certification"
msgstr "家具创作认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_fill_form_done_inherit_website_slides
msgid "Go back to course"
msgstr "回到课程"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid "How to upload a certification on your course?"
msgstr "如何上传课程认证？"

#. module: website_slides_survey
#: model_terms:slide.slide,description:website_slides_survey.slide_slide_demo_6_0
msgid "It's time to test your knowledge!"
msgstr "考验您知识的时候到了！"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.res_config_settings_view_form
msgid "Manage Certifications"
msgstr "管理认证"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid "Mark To Do"
msgstr "标记为待办事项"

#. module: website_slides_survey
#. odoo-python
#: code:addons/website_slides_survey/models/slide_channel.py:0
msgid "No Attendee passed this course certification yet!"
msgstr "尚未有学员通过此课程认证！"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "No certification found for the given search term."
msgstr "没有找到给定搜索词的认证。"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "No certifications yet!"
msgstr "还没认证！"

#. module: website_slides_survey
#: model_terms:slide.slide,description:website_slides_survey.slide_slide_demo_5_4
msgid ""
"Now that you have completed the course, it's time to test your knowledge!"
msgstr "现在您已经完成了课程，是时候测试您的知识了!"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_channel__nbr_certification
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_channel_partner__nbr_certification
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__nbr_certification
msgid "Number of Certifications"
msgstr "证书数量"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_1_choice_2
msgid "Oak"
msgstr "橡木"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/js/public/components/slide_upload_dialog/slide_upload_category.xml:0
msgid "Please select a certification."
msgstr "请选择一个认证。"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_user_input__slide_id
msgid "Related course slide"
msgstr "相关课程幻灯片"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.display_certificate
msgid "Score:"
msgstr "分数："

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "Search"
msgstr "搜索"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "Search Attempts..."
msgstr "搜索尝试……"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/js/public/components/slide_upload_dialog/slide_upload_category.js:0
msgid "Select a certification"
msgstr "选择一项认证"

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_2
msgid "Select all the furniture shown in the video"
msgstr "选择视频中显示的所有家具"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/js/public/components/slide_upload_dialog/slide_upload_dialog.xml:0
msgid ""
"Select an existing certification from the list. Creating or editing a "
"certification can be done from the backend."
msgstr "从列表中选择一个现有证书。创建或编辑认证可在后台完成。"

#. module: website_slides_survey
#: model:mail.template,description:website_slides_survey.mail_template_user_input_certification_failed
msgid "Sent to participant if they failed the certification"
msgstr "如果学员未能通过认证，则发送给他们"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_4
msgid "Shelf"
msgstr "架子"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_slide_partner
msgid "Slide / Partner decorated m2m"
msgstr "幻灯片/合作伙伴装饰为 多对多m2m"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__slide_type
msgid "Slide Type"
msgstr "幻灯片类型"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_user_input__slide_partner_id
msgid "Slide membership information for the logged in user"
msgstr "为登录用户滑动成员资格信息"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_slide_slide
msgid "Slides"
msgstr "幻灯片"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_survey_user_input__slide_partner_id
msgid "Subscriber information"
msgstr "订阅者信息"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_slide_slide__slide_type
msgid ""
"Subtype of the slide category, allows more precision on the actual file type"
" / source type."
msgstr "幻灯片类别的子类型，允许对实际文件类型/源类型进行更精确的分析。"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.survey_survey_view_tree_slides
msgid "Success Ratio (%)"
msgstr "成功率(%)"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_survey_survey
msgid "Survey"
msgstr "调查"

#. module: website_slides_survey
#: model:ir.model,name:website_slides_survey.model_survey_user_input
msgid "Survey User Input"
msgstr "调查用户输入"

#. module: website_slides_survey
#: model:mail.template,name:website_slides_survey.mail_template_user_input_certification_failed
msgid "Survey: Certification Failure"
msgstr "调查:认证失败"

#. module: website_slides_survey
#: model:survey.question.answer,value:website_slides_survey.furniture_certification_page_1_question_2_choice_2
msgid "Table"
msgstr "桌台"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid "Take Quiz"
msgstr "接受测试"

#. module: website_slides_survey
#. odoo-javascript
#: code:addons/website_slides_survey/static/src/xml/website_slides_fullscreen.xml:0
#: model_terms:ir.ui.view,arch_db:website_slides_survey.slide_content_detailed
msgid "Test Certification"
msgstr "测试认证"

#. module: website_slides_survey
#: model_terms:survey.question,description:website_slides_survey.furniture_certification_page_1
#: model_terms:survey.survey,description:website_slides_survey.furniture_certification
msgid "Test your furniture knowledge!"
msgstr "测试您的家具知识！"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_slide_slide__is_preview
msgid ""
"The course is accessible by anyone : the users don't need to join the "
"channel to access the content of the course."
msgstr "何人都可以访问该课程：用户无需加入频道即可访问课程内容。"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_survey__slide_channel_ids
msgid ""
"The courses this survey is linked to through the e-learning application"
msgstr "此调查通过在线学习应用程序链接到的课程"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_user_input__slide_id
msgid "The related course slide when there is no membership information"
msgstr "没有会员资料时，相关课程会滑动"

#. module: website_slides_survey
#: model:ir.model.fields,help:website_slides_survey.field_survey_survey__slide_ids
msgid "The slides this survey is linked to through the e-learning application"
msgstr "此调查通过在线学习应用程序链接到的幻灯片"

#. module: website_slides_survey
#: model:ir.model.fields,field_description:website_slides_survey.field_slide_slide__name
msgid "Title"
msgstr "标题"

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_3
msgid "What do you think about the content of the course? (not rated)"
msgstr "您认为这门课的内容怎么样?(未评级)"

#. module: website_slides_survey
#: model:survey.question,title:website_slides_survey.furniture_certification_page_1_question_1
msgid "What type of wood is the best for furniture?"
msgstr "什么样的木头最适合做家具?"

#. module: website_slides_survey
#. odoo-python
#: code:addons/website_slides_survey/controllers/slides.py:0
msgid "You are not allowed to create a survey."
msgstr "您不能创建调查。"

#. module: website_slides_survey
#. odoo-python
#: code:addons/website_slides_survey/controllers/slides.py:0
msgid "You are not allowed to link a certification."
msgstr "您不能链接认证。"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.badge_content
msgid ""
"You can gain badges by passing certifications. Here is a list of all available certification badges.\n"
"                            <br/>Follow the links to reach new heights and skill up!"
msgstr ""
"您可以通过认证获得徽标。这是所有可用认证徽标的列表。\n"
"1按照链接达到新的高度和技能！"

#. module: website_slides_survey
#: model:mail.template,subject:website_slides_survey.mail_template_user_input_certification_failed
msgid ""
"You have failed the course: {{ object.slide_partner_id.channel_id.name }}"
msgstr "您没通过课程: {{ object.slide_partner_id.channel_id.name }}"

#. module: website_slides_survey
#: model_terms:ir.ui.view,arch_db:website_slides_survey.user_profile_content
msgid "You have not taken any certification yet."
msgstr "你还没有取得任何证书。"
