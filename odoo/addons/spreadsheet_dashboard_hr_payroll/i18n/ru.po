# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* spreadsheet_dashboard_hr_payroll
# 
# Translators:
# <PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:15+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "#Payslips"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Absenteeism Rate"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Attendance Rate"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Basic Wage"
msgstr "Средняя базовая заработная плата"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Gross Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Hours per Days of Work"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Average Net Wage"
msgstr "Средняя чистая заработная плата"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Avg Hours/Days"
msgstr "Среднее количество часов/дней"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Companies"
msgstr "Компании"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Current"
msgstr "Текущий"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Days"
msgstr "Дней"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Employees"
msgstr "Сотрудники"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Employees (%)"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "FTE"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "FTE (%)"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Job position"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Paid Time Off"
msgstr "Оплачиваемое отсутствие"

#. module: spreadsheet_dashboard_hr_payroll
#: model:spreadsheet.dashboard,name:spreadsheet_dashboard_hr_payroll.spreadsheet_dashboard_payroll
msgid "Payroll"
msgstr "Зарплата"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Payroll Analysis"
msgstr "Анализ заработной платы"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
msgid "Period"
msgstr "Период"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Previous"
msgstr "Предыдущий"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Basic Wage"
msgstr "Итого Основная заработная плата"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Gross Wage"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Total Net Wage"
msgstr "Итого чистая заработная плата"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Unpaid TIme Off"
msgstr ""

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Days"
msgstr "Рабочие дни"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Entries Analysis"
msgstr "Анализ рабочих записей"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work Hours"
msgstr "Часы работы"

#. module: spreadsheet_dashboard_hr_payroll
#. odoo-javascript
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_dashboard.json:0
#: code:addons/spreadsheet_dashboard_hr_payroll/data/files/payroll_sample_dashboard.json:0
msgid "Work type"
msgstr "Тип работы"
