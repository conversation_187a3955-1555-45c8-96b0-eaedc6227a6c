<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_tax_report_bj" model="account.report">
        <field name="name">VAT Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.bj"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="account_tax_report_bj_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_tax_report_line_bj_turnover" model="account.report.line">
                <field name="name">II. Turnover (Without Tax)</field>
                <field name="code">BJ_TURNOVER</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_line_bj_sales_exempt" model="account.report.line">
                        <field name="name">1. Exempted turnover</field>
                        <field name="code">BJ_EXEMPT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_sales_exempt_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">BJ_1</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_sales_export" model="account.report.line">
                        <field name="name">2. Export of taxable products</field>
                        <field name="code">BJ_EXPORT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_sales_export_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">BJ_2</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_sales_export_non_taxable" model="account.report.line">
                        <field name="name">3. Export of non-taxable products</field>
                        <field name="code">BJ_EXPORT_NON_TAXABLE</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_sales_export_non_taxable_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">BJ_3</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_sales_taxable" model="account.report.line">
                        <field name="name">4. Taxable operations</field>
                        <field name="code">BJ_TAXABLE</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_sales_taxable_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">BJ_4</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_sales_self" model="account.report.line">
                        <field name="name">5. Self Deliveries</field>
                        <field name="code">BJ_SELF</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_sales_self_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">BJ_5</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_sales_total_turnover" model="account.report.line">
                        <field name="name">6. Total turnover without VAT</field>
                        <field name="code">BJ_TOTAL_TURNOVER</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_sales_total_turnover_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">BJ_EXEMPT.balance + BJ_EXPORT.balance + BJ_EXPORT_NON_TAXABLE.balance + BJ_TAXABLE.balance + BJ_SELF.balance</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_bj_deductions" model="account.report.line">
                <field name="name">III. Deductions</field>
                <field name="code">BJ_DEDUCTIONS</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_line_bj_deductible_reported" model="account.report.line">
                        <field name="name">7. Credit reported from last month</field>
                        <field name="code">BJ_REPORTED</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_deductible_reported_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">BJ_REPORTED._applied_carryover_balance</field>
                            </record>
                            <record id="account_tax_report_line_bj_deductible_reported_balance_carryover" model="account.report.expression">
                                <field name="label">_applied_carryover_balance</field>
                                <field name="engine">external</field>
                                <field name="formula">most_recent</field>
                                <field name="date_scope">previous_tax_period</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_deductible_goods_services" model="account.report.line">
                        <field name="name">8. Deduction on goods and services (without assets)</field>
                        <field name="code">BJ_GOODS_SERVICES</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_deductible_goods_services_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">BJ_8</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_deductible_assets" model="account.report.line">
                        <field name="name">9. Deduction on assets</field>
                        <field name="code">BJ_ASSETS</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_deductible_assets_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">BJ_9</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_deductible_additional" model="account.report.line">
                        <field name="name">10. Additional deductions</field>
                        <field name="code">BJ_DEDU_ADDITIONAL</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_deductible_additional_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_deductible_repayment" model="account.report.line">
                        <field name="name">11. Repayment to do</field>
                        <field name="code">BJ_DEDU_REPAYMENT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_deductible_repayment_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_deductible_total" model="account.report.line">
                        <field name="name">12. Total</field>
                        <field name="code">BJ_DEDU_TOTAL</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_deductible_total_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">BJ_REPORTED.balance + BJ_GOODS_SERVICES.balance + BJ_ASSETS.balance + BJ_DEDU_ADDITIONAL.balance + BJ_DEDU_REPAYMENT.balance</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_bj_net" model="account.report.line">
                <field name="name">IV. NET VAT</field>
                <field name="code">BJ_NET</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_line_bj_net_gross" model="account.report.line">
                        <field name="name">13. Gross VAT (18% x l.4 + l.5)</field>
                        <field name="code">BJ_GROSS</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_net_gross_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">BJ_13</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_net_deductible" model="account.report.line">
                        <field name="name">14. VAT deductible</field>
                        <field name="code">BJ_VAT_DEDUCTIBLE</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_net_deductible_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">BJ_DEDU_TOTAL.balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_net_to_pay" model="account.report.line">
                        <field name="name">15. Net VAT to pay</field>
                        <field name="code">BJ_NET_TO_PAY</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_net_to_pay_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">BJ_GROSS.balance - BJ_VAT_DEDUCTIBLE.balance</field>
                                <field name="subformula">if_above(XOF(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_bj_credit_to_report" model="account.report.line">
                        <field name="name">16. Credit to report</field>
                        <field name="code">BJ_TO_REPORT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_bj_credit_to_report_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">BJ_VAT_DEDUCTIBLE.balance - BJ_GROSS.balance</field>
                                <field name="subformula">if_above(XOF(0))</field>
                                <field name="carryover_target" eval="False"/>
                            </record>
                            <record id="account_tax_report_line_bj_credit_to_report_carryover" model="account.report.expression">
                                <field name="label">_carryover_balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">BJ_TO_REPORT.balance</field>
                                <field name="carryover_target">BJ_REPORTED._applied_carryover_balance</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
