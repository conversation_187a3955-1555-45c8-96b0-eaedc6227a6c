# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_paypal
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Client ID"
msgstr "معرف العميل"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Client Secret"
msgstr "سر العميل"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__code
msgid "Code"
msgstr "رمز "

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "Could not establish the connection to the API."
msgstr "تعذر إنشاء الاتصال بالواجهة البرمجية للتطبيق. "

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "Could not generate a new access token."
msgstr "تعذر إنشاء رمز وصول جديد. "

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_email_account
msgid "Email"
msgstr "البريد الإلكتروني"

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Generate your webhook"
msgstr "إنشاء Webhook الخاص بك "

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "How to configure your paypal account?"
msgstr "كيف تقوم بإعداد حسابك على paypal؟"

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/controllers/main.py:0
msgid "Invalid response format, can't normalize."
msgstr ""

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "Missing value for txn_id (%(txn_id)s) or txn_type (%(txn_type)s)."
msgstr "قيمة مفقودة لـ txn_id (%(txn_id)s) أو txn_type (%(txn_type)s). "

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "لم يتم العثور على معاملة تطابق المرجع %s. "

#. module: payment_paypal
#: model:ir.model.fields.selection,name:payment_paypal.selection__payment_provider__code__paypal
msgid "PayPal"
msgstr "PayPal"

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_access_token
msgid "PayPal Access Token"
msgstr "رمز وصول PayPal "

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_access_token_expiry
msgid "PayPal Access Token Expiry"
msgstr "انتهاء صلاحية رمز وصول PayPal "

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_client_id
msgid "PayPal Client ID"
msgstr "معرّف عميل PayPal "

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_client_secret
msgid "PayPal Client Secret"
msgstr "سر عميل PayPal "

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_transaction__paypal_type
msgid "PayPal Transaction Type"
msgstr "نوع معاملة PayPal "

#. module: payment_paypal
#: model:ir.model.fields,field_description:payment_paypal.field_payment_provider__paypal_webhook_id
msgid "PayPal Webhook ID"
msgstr "معرّف PayPal Webhook "

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_provider
msgid "Payment Provider"
msgstr "مزود الدفع "

#. module: payment_paypal
#: model:ir.model,name:payment_paypal.model_payment_transaction
msgid "Payment Transaction"
msgstr "معاملة السداد"

#. module: payment_paypal
#. odoo-javascript
#: code:addons/payment_paypal/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "فشلت معالجة عملية الدفع "

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "Received data with invalid payment status: %s"
msgstr "تم استلام البيانات مع حالة دفع غير صالحة: %s "

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "The communication with the API failed. Details: %s"
msgstr "فشل التواصل مع الواجهة البرمجية. التفاصيل: %s "

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_transaction.py:0
msgid "The customer left the payment page."
msgstr "لقد غادر العميل صفحة الدفع. "

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_access_token_expiry
msgid "The moment at which the access token becomes invalid."
msgstr "اللحظة التي يصبح فيها رمز الوصول غير صالح. "

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_email_account
msgid ""
"The public business email solely used to identify the account with PayPal"
msgstr ""
"عنوان البريد الإلكتروني العام للعمل الذي يُستخدم فقط لتعريف الحساب مع PayPal"
" "

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__paypal_access_token
msgid "The short-lived token used to access Paypal APIs"
msgstr "الرمز قصير الأمد المُستخدَم للوصول إلى الواجهة البرمجية لـ Paypal "

#. module: payment_paypal
#: model:ir.model.fields,help:payment_paypal.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr "الكود التقني لمزود الدفع هذا. "

#. module: payment_paypal
#: model_terms:ir.ui.view,arch_db:payment_paypal.payment_provider_form
msgid "Webhook ID"
msgstr "معرّف Webhook "

#. module: payment_paypal
#. odoo-python
#: code:addons/payment_paypal/models/payment_provider.py:0
msgid "You must have an HTTPS connection to generate a webhook."
msgstr "يجب أن يكون لديك اتصال HTTPS لإنشاء webhook. "
