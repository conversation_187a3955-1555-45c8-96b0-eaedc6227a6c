# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* marketing_automation
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:51+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: erpgo translator <<EMAIL>>, 2024\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "\" <strong>bounced</strong>,"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "\" did <strong>not receive a reply</strong>,"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "\" gets clicked,"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "\" was <strong>not opened</strong>,"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__link_tracker_click_count
msgid "# Clicks"
msgstr "# Kliklər"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_count
msgid "# Favorite Filters"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mass_mailing_count
msgid "# Mailings"
msgstr "# Məktubların göndərilməsi"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__total_participant_count
msgid "# of active and completed participants"
msgstr "# aktiv və tamamlanmış iştirakçıların"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__running_participant_count
msgid "# of active participants"
msgstr "# aktiv iştirakçıların"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__completed_participant_count
msgid "# of completed participants"
msgstr "# tamamlanmış iştirakçıların"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__test_participant_count
msgid "# of test participants"
msgstr "# sınaq iştirakçılarının"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "<br/>This activity will be"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Bounced"
msgstr "<i class=\"fa fa-check-circle\"/> Sıçrayış etdi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Clicked"
msgstr "<i class=\"fa fa-check-circle\"/> Klikləndi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Opened"
msgstr "<i class=\"fa fa-check-circle\"/> Açıqdır"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<i class=\"fa fa-check-circle\"/> Replied"
msgstr "<i class=\"fa fa-check-circle\"/> Cavablandırıldı"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<i class=\"fa fa-clock-o pe-1\" role=\"img\" aria-label=\"Select time\" "
"title=\"Select time\"/>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-envelope-open-o\"/> Not opened within"
msgstr "<i class=\"fa fa-envelope-open-o\"/> Daxilində açılmayıb"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-envelope-open-o\"/> Opened after"
msgstr "<i class=\"fa fa-envelope-open-o\"/> Sonra açılıb"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-exclamation-circle\"/> Bounced after"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Clicked after"
msgstr "<i class=\"fa fa-hand-pointer-o\"/> klikləyin və buraxın"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-hand-pointer-o\"/> Not clicked within"
msgstr "<i class=\"fa fa-hand-pointer-o\"/> kliklənmədi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-pie-chart\"/> Details"
msgstr "<i class=\"fa fa-pie-chart\"/> Detallar"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-plus-circle\"/> Add child activity"
msgstr "<i class=\"fa fa-plus-circle\"/> Uşaq fəaliyyətin əlavə edin "

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-reply\"/> Not replied within"
msgstr "<i class=\"fa fa-reply\"/> bu müddətdə cavab vermədi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<i class=\"fa fa-reply\"/> Replied after"
msgstr "<i class=\"fa fa-reply\"/> sonra cavab verdi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<i data-trigger-type=\"activity\" class=\"fa fa-code-fork fa-rotate-180 fa-"
"flip-vertical o_ma_text_processed o_add_child_activity text-success\" "
"title=\"Add Another Activity\" role=\"img\" aria-label=\"Add Another "
"Activity\"/>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "<span class=\"d-inline-block w-25\">after</span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "<span class=\"o_form_label\">Record</span>"
msgstr "<span class=\"o_form_label\">Qeyd</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"<span role=\"img\" title=\"Graph\" aria-label=\"Graph\" class=\"o_ma_activity_tab active\" data-tab-type=\"graph\">\n"
"                                                            <i class=\"fa fa-pie-chart\"/>\n"
"                                                        </span>\n"
"                                                        <span title=\"Filter\" role=\"img\" aria-label=\"Filter\" class=\"o_ma_activity_tab\" data-tab-type=\"filter\">\n"
"                                                            <i class=\"fa fa-filter\"/>\n"
"                                                        </span>"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Completed</span>"
msgstr "<span>Tamamlanmış</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Running</span>"
msgstr "<span>İcra olunmuş</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_kanban
msgid "<span>Total</span>"
msgstr "<span>Ümumi</span>"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"<strong>Summary</strong><br/>\n"
"            Starting from"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "<strong>The workflow has been modified!</strong>"
msgstr "<strong>İş dövriyyəsi dəyişdirildi!</strong>"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_winner_mailing_id
msgid "A/B Campaign Winner Mailing"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_mailings_count
msgid "A/B Test Mailings #"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_completed
msgid "A/B Testing Campaign Finished"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__trace_ids
msgid "Actions"
msgstr "Hərəkətlər"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__active
msgid "Active"
msgstr "Aktiv"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__marketing_activity_ids
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_tree_marketing_automation
msgid "Activities"
msgstr "Fəaliyyətlər"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__parent_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__activity_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Activity"
msgstr "Fəaliyyət"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_domain
msgid "Activity Filter"
msgstr "Fəaliyyət Filtri"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Activity Name"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_summary
msgid "Activity Summary"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__activity_type
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__activity_type
msgid "Activity Type"
msgstr "Fəaliyyət növü"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__domain
msgid ""
"Activity will only be performed if record satisfies this domain, obtained "
"from the combination of the activity filter and its inherited filter"
msgstr ""
"Fəaliyyət fəaliyyət filtri və onun miras filtrinin birləşməsindən  əldə "
"olunan bu domen qeyd ilə təmin olunduqdan snra icra olunacaq"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Add Hot Category"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Add Tag"
msgstr "Etiket əlavə et"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Add To Confirmed List"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Add new activity"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Add to Templates"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Add to list"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Advanced"
msgstr "Geniş"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "After 7 days"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__allowed_parent_ids
msgid "All activities which can be the parent of this one"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_post_ids
msgid "All related social media posts"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__allowed_parent_ids
msgid "Allowed parents"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__is_auto_campaign
msgid "Allows us to filter relevant Campaigns"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__domain
msgid "Applied Filter"
msgstr "Tətbiq olunan Filtr"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Archived"
msgstr "Arxivləndi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Are you sure you want to create a new participant for each matching record "
"that has not been used yet?"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Attach a file"
msgstr "Fay əlavə edin"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__is_auto_campaign
msgid "Automatically Generated Campaign"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Be aware that participants that had no more activities could be reintroduced"
" into the campaign and new traces could be created for them."
msgstr ""
"Agah olun ki, artıq fəaliyyətlə məşğul olmayan iştirakçılar kampaniyaya "
"yenidən daxil ola bilərlər və onlar üçün yeni izlər yaradıla bilər."

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Blacklist Bounces"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Blacklist record"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Bounced"
msgstr "Sıçrayış etdi"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__bounced_ratio
msgid "Bounced Ratio"
msgstr "Sıçrayış Nisbəti"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__campaign_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Campaign"
msgstr "Kampaniya"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__name
msgid "Campaign Identifier"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__title
msgid "Campaign Name"
msgstr " Kampaniyanın Adı"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_campaign_action
#: model:ir.ui.menu,name:marketing_automation.marketing_campaign_menu
msgid "Campaigns"
msgstr "Kampaniyalar"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Cancel"
msgstr "Ləğv edin"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Cancel after"
msgstr "Sonra ləğv edin"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__canceled
msgid "Cancelled"
msgstr "Ləğv olundu"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Check Bounce Contact"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Check Email Address"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__validity_duration
msgid ""
"Check this to make sure your actions are not executed after a specific "
"amount of time after the scheduled date. (e.g. Time-limited offer, Upcoming "
"event, …)"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__child_ids
msgid "Child Activities"
msgstr "Alt fəaliyyətlər"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Clicked"
msgstr "Klikləndi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Clicks"
msgstr "kliklər"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__color
msgid "Color Index"
msgstr "Rəng İndeksi"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_participants_action_reporting
msgid ""
"Come back later once your campaigns are running to overview your "
"participants."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Commercial prospection"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__completed
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Completed"
msgstr "Tamamlandı"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_menu_configuration
msgid "Configuration"
msgstr "Konfiqurasiya"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Confirmation"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Confirmed contacts"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Create Campaign"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_campaign_action
msgid "Create a Campaign"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Create a Marketing Automation Campaign"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Create one or load a template prepared by our experts."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_campaign_action
msgid ""
"Create one or load a template prepared by our experts.<br>\n"
"                            Then sit down and let Odoo handle the rest!"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__create_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__create_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__ab_testing_schedule_datetime
msgid ""
"Date that will be used to know when to determine and send the winner mailing"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__days
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__days
msgid "Days"
msgstr "günlər"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_type
msgid "Delay Type"
msgstr "Gecikmənin Tipi"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/js/marketing_automation_one2many.js:0
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Delete"
msgstr "Silin"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/js/marketing_automation_one2many.js:0
msgid ""
"Deleting this activity will delete ALL its children activities. Are you "
"sure?"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Design your own marketing campaign from the ground up."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__child_ids
msgid "Direct child traces"
msgstr "Birbaşa alt izləmələri"

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Discard"
msgstr "Ləğv edin"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__display_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__display_name
msgid "Display Name"
msgstr "Göstəriləcək Ad"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__res_id
msgid "Document ID"
msgstr "Sənədin ID-si"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Domain"
msgstr "Domen"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_activity__activity_domain
msgid "Domain that applies to this activity and its child activities"
msgstr "Bu fəaliyyətə və alt fəaliyyətlərinə tətbiq olunan domen "

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Don't update"
msgstr "Yeniləməyin"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Double Opt-in"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__activity_type__email
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__mass_mailing_id_mailing_type__mail
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Email"
msgstr "Elektron poçt"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Email cancelled"
msgstr ""

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Email tərtibi sehrbazı"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Email failed"
msgstr "Email uğursuz oldu"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__error
msgid "Error"
msgstr "Xəta"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__state_msg
msgid "Error message"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Error! You can't create recursive hierarchy of Activity."
msgstr "Xəta! Fəaliyyətin rekursiv iyerarxiyasını yarada bilməzsiniz."

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Exception in mass mailing: %s"
msgstr "Kütləvi məktub göndərmə zamanı istisna: %s"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Exception in server action: %s"
msgstr "Server fəaliyyətində istisna:%s"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Exclude Test"
msgstr "Testi xaric edin"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Expiry Duration"
msgstr "Son İstifadə Müddəti"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_id
msgid "Favorite Filter"
msgstr ""

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.mailing_filter_menu_action_marketing_automaion
msgid "Favorite Filters"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_filter_domain
msgid "Favorite filter domain"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__domain
msgid "Filter"
msgstr "FİLTER"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Generate participants"
msgstr "İştirakçıları Yaradın"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Get 10% OFF"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_trace_action
msgid ""
"Here you will be able to check the results of your mailings from all "
"Marketing Automation Campaigns."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Hot"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__hours
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__hours
msgid "Hours"
msgstr "Saatlar"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__id
msgid "ID"
msgstr "ID"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__is_mailing_campaign_activated
msgid "Is Mailing Campaign Activated"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "It will be generated automatically once you save this record."
msgstr "Bu qeydi yaddaşda saxladıqdan sonra avtomatik olaraq yaradılacaq."

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Join partnership!"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__write_uid
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__write_date
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__last_sync_date
msgid "Last activities synchronization"
msgstr "Fəaliyyətlərin son sinxronizasiyası"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Launch"
msgstr "İşə sal"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/wizard/marketing_campaign_test.py:0
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Launch a Test"
msgstr "Sınağı İşə sal"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_campaign_test_action
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Launch a test"
msgstr "Sınağı İşə sal"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__crm_lead_count
msgid "Leads/Opportunities count"
msgstr "Potensial Müştərilər/Fürsətlər sayı"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.link_tracker_action_marketing_campaign
msgid "Link Statistics"
msgstr "Link Statistikası"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.link_tracker_menu_reporting_marketing_automation
msgid "Link Tracker"
msgstr "Bağlantı İzləyicisi"

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.link_tracker_action_marketing_campaign
msgid ""
"Link Trackers are created when mailings with links are sent to track how "
"many clicks they get."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__links_click_datetime
msgid "Links Click Datetime"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_category__email
msgid "Mail"
msgstr "Poçt"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_automation
msgid "Mail Body"
msgstr "Mail Gövdəsi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Mail Template"
msgstr "Məktub Şablonu"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_bounce
msgid "Mail: bounced"
msgstr "Poçt: geri qaytarılmış"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_click
msgid "Mail: clicked"
msgstr "Poçt: kliklənmiş"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_click
msgid "Mail: not clicked"
msgstr "Poçt: kliklənməmiş"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_open
msgid "Mail: not opened"
msgstr "Poçt: açılmamış"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_not_reply
msgid "Mail: not replied"
msgstr "Poçt: cavabsız qalmış"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_open
msgid "Mail: opened"
msgstr "Poçt: açılmış"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__mail_reply
msgid "Mail: replied"
msgstr "Poçt: cavabı verilmiş"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Mailing"
msgstr "Məktub göndərmə"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_trace
msgid "Mailing Statistics"
msgstr "Məktub göndərmə Statistikası"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__mass_mailing_id_mailing_type
msgid "Mailing Type"
msgstr "Məktub göndərmə Növü"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Mailings"
msgstr "Məktubların göndərilməsi"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/mailing_mailing.py:0
msgid ""
"Mailings %(mailing_names)s are used in marketing campaigns. You should take "
"care of this before unlinking the mailings."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Mails sent and not bounced"
msgstr "Göndərilmiş və geri qayıtmamış məktublar"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Manually"
msgstr "Əllə"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_participant.py:0
msgid "Marked as completed"
msgstr "Tamamlanmış kimi işarələndi"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Marketing"
msgstr "Marketinq"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_mailing__marketing_activity_ids
msgid "Marketing Activities"
msgstr "Marketinq Fəaliyyətləri"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_activity
#: model:ir.model.fields,field_description:marketing_automation.field_mail_compose_message__marketing_activity_id
msgid "Marketing Activity"
msgstr "Marketinq Fəaliyyəti"

#. module: marketing_automation
#: model:ir.module.category,name:marketing_automation.module_marketing_automation_category
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_menu
msgid "Marketing Automation"
msgstr "Marketinqin Avtomatlaşdırılması"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.mail_mass_mailing_action_marketing_automation
msgid "Marketing Automation Mailings"
msgstr "Marketinqin Avtomatlaşdırılması üzrə Məktubların göndərilməsi"

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_cron_campaign_execute_activities_ir_actions_server
msgid "Marketing Automation: execute activities"
msgstr "Marketinqin Avtomatlaşdırılması: hərəkətləri icra et"

#. module: marketing_automation
#: model:ir.actions.server,name:marketing_automation.ir_cron_campaign_sync_participants_ir_actions_server
msgid "Marketing Automation: sync participants"
msgstr "Marketinqin Avtomatlaşdırılması: iştirakçıları sinxronizə et"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_campaign
msgid "Marketing Campaign"
msgstr "Marketinq Kampaniyası"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_campaign_test
msgid "Marketing Campaign: Launch a Test"
msgstr "Marketinq Kampaniyası: Sınağı İşə sal"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_participant
msgid "Marketing Participant"
msgstr "Marketinq İştirakçısı"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__mass_mailing_id
msgid "Marketing Template"
msgstr "Marketinq Şablonu"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_marketing_trace
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_trace__marketing_trace_id
msgid "Marketing Trace"
msgstr "Marketinq Zolağı"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_mailing_mailing__use_in_marketing_automation
msgid ""
"Marketing campaigns use mass mailings with some specific behavior; this "
"field is used to indicate its statistics may be suspicious."
msgstr ""
"Marketinq kampaniyalarında bəzi konkret davranışlı kütləvi məktub göndərmə "
"istifadə olunur; bu sahə onun statistikasının şübhəli ola biləcəyini "
"göstərmək üçün istifadə olunur."

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_mailing
msgid "Mass Mailing"
msgstr "Kütləvi Məktub Göndərmə"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_mailing_trace_report
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_form
msgid "Mass Mailing Statistics"
msgstr "Kütləvi Məktub Göndərmə Statistikası"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_mail_ids
msgid "Mass Mailings"
msgstr "Kütləvi Məktub Göndərmələr"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__mailing_trace_ids
msgid "Mass mailing statistics"
msgstr "Kütləvi Məktub Göndərmə Statistikası"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Message for sales person"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Misc"
msgstr "MÜXTƏLİF"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__model_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__model_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Model"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.ir_model_view_tree_marketing
msgid "Model Description"
msgstr "Modelin Təsviri"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__model_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__model_name
msgid "Model Name"
msgstr "Model Adı"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__months
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__months
msgid "Months"
msgstr "Aylar"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__name
msgid "Name"
msgstr "Ad"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__draft
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "New"
msgstr "Yeni"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Next activity: Check Email Address"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "No activity"
msgstr "Fəaliyyət yoxdur"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "No activity for this campaign."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.actions.act_window,help:marketing_automation.link_tracker_action_marketing_campaign
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_participants_action_reporting
#: model_terms:ir.actions.act_window,help:marketing_automation.marketing_trace_action
msgid "No data yet!"
msgstr "Hələ məlumat yoxdur!"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Clicked"
msgstr "Kliklənməyib"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Opened"
msgstr "Açılmayıb"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Not Replied"
msgstr "Cavablanmayıb"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not bounced yet"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not clicked yet"
msgstr "Hələ kliklənməyib"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not opened yet"
msgstr "Hələ Açılmayıb"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Not replied yet"
msgstr "Hələ Cavablanmayıb"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__mailing_mail_count
msgid "Number of Mass Mailing"
msgstr "Kütləvi Göndərilən Məktubların Sayı"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__click_count
msgid "Number of clicks generated by the campaign"
msgstr "Kampaniya tərəfindən yaradılan klikləmələrin sayı"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_engagement
msgid ""
"Number of interactions (likes, shares, comments ...) with the social posts"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Offer free catalog"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Opened"
msgstr "Açılan"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__opened_ratio
msgid "Opened Ratio"
msgstr "Açılan Nisbət "

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Options"
msgstr "Opsionlar"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Other activity"
msgstr "Digər fəaliyyət "

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__parent_id
msgid "Parent"
msgstr "Əsas"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Parent activity mail bounced"
msgstr "Əsas fəaliyyət emaili geri qaytarıldı"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Parent activity mail clicked"
msgstr "Əsas fəaliyyət emaili klikləndi"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Parent activity mail opened"
msgstr "Əsas fəaliyyət emaili açıldı"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_trace.py:0
msgid "Parent activity mail replied"
msgstr "Əsas fəaliyyət emailinə cavab verildi"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__participant_id
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_form
msgid "Participant"
msgstr "İştirakçı"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
msgid "Participant Name"
msgstr "İştirakçı Adı "

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_graph
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_pivot
msgid "Participant summary"
msgstr "İştirakçı qısa xülasəsi"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_participant_action_campaign
#: model:ir.actions.act_window,name:marketing_automation.marketing_participant_action_campaign_test
#: model:ir.actions.act_window,name:marketing_automation.marketing_participants_action_mail
#: model:ir.actions.act_window,name:marketing_automation.marketing_participants_action_reporting
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__participant_ids
#: model:ir.ui.menu,name:marketing_automation.marketing_participants_menu
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Participants"
msgstr "İştirakçılar "

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Participants of %(activity)s (%(filter)s)"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Pick a Server Action..."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Pick a record..."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Pick or Create a Template..."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "Pick or create a/an"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__processed
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__processed
msgid "Processed"
msgstr "İşlənmişdir"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "REJECTED"
msgstr "İmtina edildi"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__received_ratio
msgid "Received Ratio"
msgstr "Qəbul edilən Proporsiya"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__resource_ref
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__resource_ref
msgid "Record"
msgstr "Qeyd"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__res_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__res_id
msgid "Record ID"
msgstr "ID qeyd et"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_participant.py:0
msgid "Record deleted"
msgstr "Qeyd silinib"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign_test__model_name
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__model_name
msgid "Record model"
msgstr "Modeli qeyd et"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__rejected
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__rejected
msgid "Rejected"
msgstr "İmtina edildi"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Rejected by activity filter or record deleted / archived"
msgstr ""
"Əməliyyat filtri tərəfindən rədd edilib ya da qeydlər silinib/arxivə "
"yerləşdirilib"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Reload a favorite filter"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.mailing_mailing_view_form_marketing_activity
msgid "Remove from Templates"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__unlinked
msgid "Removed"
msgstr "Silindi"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_participant__state
msgid "Removed means the related record does not exist anymore."
msgstr "Silindi, əlaqədar qeydin artıq mövcud olmadığını bildirir."

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Replied"
msgstr "Cavab verilib"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__replied_ratio
msgid "Replied Ratio"
msgstr "Cavablandırılan Nisbət"

#. module: marketing_automation
#: model:ir.ui.menu,name:marketing_automation.marketing_automation_reporting_menu
msgid "Reporting"
msgstr "Hesabatlıq"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__require_sync
msgid "Require trace sync"
msgstr "İz sinxronizasiyasını tələb et"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Resource ID"
msgstr "Resurs ID"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Resource Name"
msgstr "Resurs Adı"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__user_id
msgid "Responsible"
msgstr "Məsul"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Run"
msgstr "İşəsalma"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Run the next scheduled activity for each participant of this campaign?"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__running
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_participant__state__running
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Running"
msgstr "Qüvvədə"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "SUCCESS"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__schedule_date
msgid "Schedule Date"
msgstr "Tarixi planlaşdırın"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_trace__state__scheduled
msgid "Scheduled"
msgstr "Planlaşdırılmış "

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Search Campaign"
msgstr "Kampaniya Axtarışı"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "Search Participant"
msgstr "İştirakçı Axtarışı"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_search
msgid "Search Traces"
msgstr "Tarixçə Axtarışı"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__ab_testing_winner_selection
msgid "Selection to determine the winner mailing that will be sent."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send 10% Welcome Discount"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_schedule_datetime
msgid "Send Final On"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send Welcome Email"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send a free catalog and follow-up according to reactions."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send a welcome email to contacts and tag them if they click in it."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid ""
"Send a welcome email to new subscribers, remove the addresses that bounced."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_number
msgid "Send after"
msgstr "-dan sonra göndərin"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__interval_standardized
msgid "Send after (in hours)"
msgstr "-dan sonra göndərin (saatla)"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Send an email to new recipients to confirm their consent."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Sent"
msgstr "Göndərildi"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__server_action_id
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__activity_type__action
msgid "Server Action"
msgstr "Server Fəaliyyəti"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__social_posts_count
msgid "Social Media Posts"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid ""
"Some participants are already running on this campaign. Click on 'Update' to"
" apply the modifications you've just made."
msgstr ""
"Bəzi iştirakçılar artıq bu kampaniyada işləyirlər. Yenicə həyata "
"keçirdiyiniz dəyişiklikləri tətbiq etmək üçün 'Yeniləmə' düyməsini "
"klikləyin."

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__source_id
msgid "Source"
msgstr "Mənbə"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_mailing_mailing__use_in_marketing_automation
msgid "Specific mailing used in marketing campaign"
msgstr "Marketinq kampaniyasında istifadə olunan xüsusi məktublaşma"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__stage_id
msgid "Stage"
msgstr "Mərhələ"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Start"
msgstr "Başlayın"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Start from scratch"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__state
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__state
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__state
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_search
msgid "State"
msgstr "Dövlət"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "State: #{record.state.raw_value}"
msgstr "Vəziyyət: #{record.state.raw_value}"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__statistics_graph_data
msgid "Statistics Graph Data"
msgstr "Statistikanın Qrafik Datası"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__mailing_trace_status
msgid "Status"
msgstr "Status"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Stop"
msgstr "Dayanın"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_campaign__state__stopped
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
msgid "Stopped"
msgstr "Dayandırılan"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid "Success"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid ""
"Switching Target Model invalidates the existing activities. Either update "
"your activity actions to match the new Target Model or delete them."
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__require_sync
msgid "Sync of participants is required"
msgstr "İştirakçıların sinxronizasiyası tələb olunur"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Tag Hot Contacts"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__tag_ids
msgid "Tags"
msgstr "Etiketlər"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_search
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_tree
msgid "Target"
msgstr "Hədəf "

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_tree
msgid "Target Model"
msgstr "Hədəf Modeli"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Templates"
msgstr "Şablonlar"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Test"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_participant__is_test
msgid "Test Record"
msgstr "Test Qeydi"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__is_test
msgid "Test Trace"
msgstr "Test İzi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Tests"
msgstr "Testlər"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid ""
"The saved filter targets different model and is incompatible with this "
"campaign."
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "Then sit down and let Odoo handle the rest!"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid ""
"To use this feature you should be an administrator or belong to the "
"marketing automation group."
msgstr ""
"Bu xüsusiyyətdən istifadə etmək üçün, siz administrator olmalı və ya "
"marketinq avtomatlaşdırma qrupuna aid olmalısınız."

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_bounce
msgid "Total Bounce"
msgstr "Ümumi Geriqayıtma"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_click
msgid "Total Click"
msgstr "Ümumi Klik Sayı"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_open
msgid "Total Open"
msgstr "Ümumi Açıq"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_reply
msgid "Total Reply"
msgstr "Cəmi Təkrar Sayı"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__total_sent
msgid "Total Sent"
msgstr "Cəmi Göndərilən"

#. module: marketing_automation
#: model:ir.actions.act_window,name:marketing_automation.marketing_trace_action
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trace_ids
#: model:ir.ui.menu,name:marketing_automation.marketing_trace_menu
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_trace_view_tree
msgid "Traces"
msgstr "İzləmələr"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "Trigger"
msgstr "Başlatmaq"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trigger_category
msgid "Trigger Category"
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__trigger_type
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_trace__trigger_type
msgid "Trigger Type"
msgstr "Başlatma Növü"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_utm_campaign
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__utm_campaign_id
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__utm_campaign_id
msgid "UTM Campaign"
msgstr "Kampaniyanın UTM kodu"

#. module: marketing_automation
#: model:ir.model,name:marketing_automation.model_utm_source
msgid "UTM Source"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Unicity based on"
msgstr "Unikallıq əsasında"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__unique_field_id
msgid "Unique Field"
msgstr "Unikal Sahə"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "Update"
msgstr "Yeniləyin"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__use_leads
msgid "Use Leads"
msgstr "Potensial Müştəriləri istifadə edin"

#. module: marketing_automation
#: model:ir.model.fields,help:marketing_automation.field_marketing_campaign__unique_field_id
msgid ""
"Used to avoid duplicates based on model field.\n"
"e.g.\n"
"                For model 'Customers', select email field here if you don't\n"
"                want to process records which have the same email address"
msgstr ""
"Model sahəsinə əsaslanan təkrarlanmaların qarşısını almaq üçün istifadə olunur.\n"
"məs.\n"
"                Model 'Müştərilər' üçün eyni email ünvanına sahib olan qeydləri işləmək istəmirsinizsə, \n"
"                burada email sahəsini seçin"

#. module: marketing_automation
#: model:res.groups,name:marketing_automation.group_marketing_automation_user
msgid "User"
msgstr "İstifadəçi"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration_number
msgid "Valid during"
msgstr "Ərzində etibarlıdır"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration
msgid "Validity Duration"
msgstr "Etibarlılıq müddəti"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_activity__validity_duration_type
msgid "Validity Duration Type"
msgstr "Etibarlılıq Müddəti Növü"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Warning"
msgstr "Xəbərdarlıq"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__interval_type__weeks
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__validity_duration_type__weeks
msgid "Weeks"
msgstr "Həftələr"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Welcome Flow"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "Welcome!"
msgstr "Xoş gəlmisiniz!"

#. module: marketing_automation
#: model:ir.model.fields,field_description:marketing_automation.field_marketing_campaign__ab_testing_winner_selection
msgid "Winner Selection"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Workflow"
msgstr "İş dövriyyəsi"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_participant_view_form
msgid "Workflow Started On"
msgstr "İş dövriyyəsi Başladı"

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_activity.py:0
msgid ""
"You are trying to set the activity \"%(parent_activity)s\" as \"%(parent_type)s\" while its child \"%(activity)s\" has the trigger type \"%(trigger_type)s\"\n"
"Please modify one of those activities before saving."
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/utm_campaign.py:0
msgid ""
"You cannot delete these UTM Campaigns as they are linked to the following marketing campaigns in Marketing Automation:\n"
"%(campaign_names)s"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to the following marketing activities in Marketing Automation:\n"
"%(activities_names)s"
msgstr ""

#. module: marketing_automation
#. odoo-python
#: code:addons/marketing_automation/models/marketing_campaign.py:0
msgid "You must set up at least one activity to start this campaign."
msgstr ""
"Bu kampaniyanı başlamaq üçün, siz ən azı bir fəaliyyət yətin etməlisiniz."

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "after the <strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "after the <strong>beginning of the workflow</strong>,<br/>the"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"after the Participant <strong>clicked</strong>,<br/>on any link included in "
"the <strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"after the Participant <strong>opened</strong> the <strong>Mailing</strong> "
"sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"after the Participant <strong>replied</strong> to the "
"<strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "after the execution of the Activity \""
msgstr ""

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__activity
msgid "another activity"
msgstr "başqa fəaliyyət"

#. module: marketing_automation
#: model:ir.model.fields.selection,name:marketing_automation.selection__marketing_activity__trigger_type__begin
msgid "beginning of workflow"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "cancelled, if"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "e.g. \"Brandon Freeman\""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_view_form
msgid "e.g. eCommerce Offers"
msgstr "məs. e-Ticarət Təklifləri"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_view_form
msgid "e.g. eCommerce Offers Plan"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "have passed since the scheduled date."
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid ""
"if no link included in the <strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "if the <strong>Mailing</strong> sent by the Activity \""
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "run"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "sent"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "the"
msgstr "Bu"

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_campaign_test_view_form
msgid "to generate a Test Participant"
msgstr ""

#. module: marketing_automation
#: model_terms:ir.ui.view,arch_db:marketing_automation.marketing_activity_summary_template
msgid "will be"
msgstr ""

#. module: marketing_automation
#. odoo-javascript
#: code:addons/marketing_automation/static/src/components/campaign_template_picker_dialog/campaign_template_picker_dialog.xml:0
msgid "{{template_value.title}}"
msgstr ""
