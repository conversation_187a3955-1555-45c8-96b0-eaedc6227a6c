# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_sign
# 
# Translators:
# <PERSON><PERSON> <kari.l<PERSON><PERSON>@emsystems.fi>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:14+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: O<PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_applicant__sign_request_count
msgid "# Signatures"
msgstr "# Allekirjoitukset"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
msgid "%(partner)s and %(responsible)s are the signatories."
msgstr "%(partner)s ja %(responsible)s ovat allekirjoittajia."

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_applicant_sign_view_form
msgid "<span class=\"o_stat_text\">Signature Requests</span>"
msgstr "<span class=\"o_stat_text\">Allekirjoituspyynnöt</span>"

#. module: hr_recruitment_sign
#: model:ir.model,name:hr_recruitment_sign.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_id
msgid "Applicant"
msgstr "Hakija"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_role_id
msgid "Applicant Role"
msgstr "Hakijan rooli"

#. module: hr_recruitment_sign
#: model:ir.model.fields,help:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__applicant_role_id
msgid ""
"Applicant's role on the templates to sign. The same role must be present in "
"all the templates"
msgstr ""
"Hakijan rooli allekirjoitettavien mallien osalta. Saman roolin on oltava "
"kaikissa malleissa"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Attach a file"
msgstr "Liitä tiedosto"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__attachment_ids
msgid "Attachment"
msgstr "Liite"

#. module: hr_recruitment_sign
#: model:ir.model,name:hr_recruitment_sign.model_hr_candidate
msgid "Candidate"
msgstr "Ehdokas"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__partner_id
msgid "Contact"
msgstr "Kontakti"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__cc_partner_ids
msgid "Copy to"
msgstr "Kopioi osoitteeseen"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__create_date
msgid "Created on"
msgstr "Luotu"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Discard"
msgstr "Hylkää"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: hr_recruitment_sign
#: model:ir.actions.act_window,name:hr_recruitment_sign.sign_recruitment_wizard_action
msgid "Document Signature"
msgstr "Asiakirjan allekirjoitus"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_ids
msgid "Documents to sign"
msgstr "Allekirjoitettavat asiakirjat"

#. module: hr_recruitment_sign
#: model:ir.model.fields,help:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_ids
msgid ""
"Documents to sign. Only documents with 1 or 2 different responsible are selectable.\n"
"        Documents with 1 responsible will only have to be signed by the applicant while documents with 2 different responsible will have to be signed by both the applicant and the responsible.\n"
"        "
msgstr ""
"Allekirjoitettavat asiakirjat. Vain asiakirjat, joissa on 1 tai 2 eri vastuuhenkilöä, ovat valittavissa.\n"
"        Asiakirjat, joissa on yksi vastuuhenkilö, on vain hakijan allekirjoitettava, kun taas asiakirjat, joissa on kaksi eri vastuuhenkilöä, on sekä hakijan että vastuuhenkilön allekirjoitettava.\n"
"        "

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__has_both_template
msgid "Has Both Template"
msgstr "On molemmat mallit"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Mail Options"
msgstr "Sähköpostivaihtoehdot"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__message
msgid "Message"
msgstr "Viesti"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
msgid ""
"No appropriate template could be found, please make sure you configured them"
" properly."
msgstr ""
"Sopivaa mallia ei löytynyt, varmista, että olet määrittänyt ne oikein."

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "No template available"
msgstr "Mallia ei ole saatavilla"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
msgid "Only %s has to sign."
msgstr "Vain %s on allekirjoitettava."

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Optional Message..."
msgstr "Valinnainen viesti..."

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__partner_name
msgid "Partner Name"
msgstr "Kumppanin nimi"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__possible_template_ids
msgid "Possible Template"
msgstr "Mahdollinen malli"

#. module: hr_recruitment_sign
#: model:ir.actions.server,name:hr_recruitment_sign.action_request_signature
msgid "Request Signature"
msgstr "Pyynnön allekirjoitus"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__responsible_id
msgid "Responsible"
msgstr "Vastuuhenkilö"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Send"
msgstr "Lähetä"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Sign Request Options"
msgstr "Allekirjoituspyynnön valinnat"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__sign_template_responsible_ids
msgid "Sign Template Responsible"
msgstr "Allekirjoitusmallin vastuutaho"

#. module: hr_recruitment_sign
#: model:ir.model,name:hr_recruitment_sign.model_hr_recruitment_sign_document_wizard
msgid "Sign document in recruitment"
msgstr "Allekirjoita asiakirja rekrytoinnissa"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Signature Request"
msgstr "Allekirjoituspyyntö"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/wizard/hr_recruitment_sign_document_wizard.py:0
msgid "Signature Request - %s"
msgstr "Allekirjoituspyyntö - %s"

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/models/hr_applicant.py:0
msgid "Signature Requests"
msgstr "Allekirjoituspyynnöt"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__subject
msgid "Subject"
msgstr "Aihe"

#. module: hr_recruitment_sign
#: model:ir.model.fields,field_description:hr_recruitment_sign.field_hr_recruitment_sign_document_wizard__template_warning
msgid "Template Warning"
msgstr "Mallivaroitus"

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.hr_recruitment_sign_view
msgid "Write email or search contact..."
msgstr "Kirjoita sähköpostia tai etsi yhteystiedot..."

#. module: hr_recruitment_sign
#. odoo-python
#: code:addons/hr_recruitment_sign/models/hr_applicant.py:0
msgid "You must define a Contact Name for this applicant."
msgstr "Hakijalle on määriteltävä yhteyshenkilön nimi."

#. module: hr_recruitment_sign
#: model_terms:ir.ui.view,arch_db:hr_recruitment_sign.message_signature_request
msgid "requested a new signature on the following documents:"
msgstr "pyytänyt uutta allekirjoitusta seuraaviin asiakirjoihin:"
