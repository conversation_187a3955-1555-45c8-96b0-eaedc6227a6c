# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_gn
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 10:22+0000\n"
"PO-Revision-Date: 2023-11-30 10:22+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_gn
#: model:ir.model,name:l10n_gn.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_gn
#: model:account.report.column,name:l10n_gn.account_tax_report_gn_balance
msgid "Base"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_purchase_exempt
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_sales_exempt
msgid "Exempt"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_sales_sales_export
msgid "Export"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_purchase_taxable_goods
msgid "Goods"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_purchase_import
msgid "Import"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_purchase
msgid "Incoming"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_net
msgid "Net VAT"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_sales
msgid "Outgoing"
msgstr ""

#. module: l10n_gn
#. odoo-python
#: code:addons/l10n_gn/models/template_gn_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_gn
#. odoo-python
#: code:addons/l10n_gn/models/template_gn.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_purchase_taxable_service
msgid "Services"
msgstr ""

#. module: l10n_gn
#: model:account.report.column,name:l10n_gn.account_tax_report_gn_tax
msgid "Tax"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_purchase_taxable
msgid "Taxable"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_sales_taxable
msgid "Taxable operations"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_credit
msgid "VAT Credit"
msgstr ""

#. module: l10n_gn
#: model:account.report,name:l10n_gn.account_tax_report_gn
msgid "VAT Report"
msgstr ""

#. module: l10n_gn
#: model:account.report.line,name:l10n_gn.account_tax_report_line_gn_to_pay
msgid "VAT to pay"
msgstr ""
