<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_sg" model="res.partner" forcecreate="1">
        <field name="name">SG Company</field>
        <field name="vat">197401143C</field>
        <field name="l10n_sg_unique_entity_number">201131415A</field>
        <field name="street">Tyersall Avenue</field>
        <field name="city">Central Singapore</field>
        <field name="country_id" ref="base.sg"/>
        <field name="zip">248048</field>
        <field name="phone">+65 9123 4567</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.sg.example.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_sg" model="res.company" forcecreate="1">
        <field name="name">SG Company</field>
        <field name="partner_id" ref="base.partner_demo_company_sg"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_sg')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_sg'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>sg</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_sg')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
