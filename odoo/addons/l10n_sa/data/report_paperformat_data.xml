<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="paperformat_l10n_sa_a4" model="report.paperformat">
            <field name="name">Saudi Arabia A4</field>
            <field name="orientation">Portrait</field>
            <field name="margin_bottom">32</field>
            <field name="header_spacing">52</field>
            <field name="margin_top">52</field>
            <field name="margin_left">0</field>
            <field name="margin_right">0</field>
        </record>
    </data>
    <data>
        <function model="res.company" name="write">
            <value model="res.company" search="[
                ('partner_id.country_id', '=', ref('base.sa'))]"/>
            <value eval="{'paperformat_id': ref('l10n_sa.paperformat_l10n_sa_a4')}"/>
        </function>
    </data>
</odoo>
