# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_sa
#
msgid ""
msgstr ""

#. module: l10n_sa
#: model:account.tax.report,name:l10n_sa.tax_report_vat_filing
msgid "VAT Filing Report"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_vat_all_sales_base
msgid "VAT on Sales and all other Outputs (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_standard_rated_15_base
msgid "1. Standard Rated 15% (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_special_sales_to_locals_base
msgid "2. Special Sales to Locals (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_local_sales_subject_to_0_base
msgid "3. Local Sales Subject to 0% (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_export_sales_base
msgid "4. Export Sales (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_exempt_sales_base
msgid "5. Exempt Sales (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_net_sales_base
msgid "6. Net Sales (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_vat_all_expenses_base
msgid "VAT on Expenses and all other Inputs (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_standard_rated_15_purchases_base
msgid "7. Standard rated 15% Purchases (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_taxable_imports_15_paid_to_customs_base
msgid "8. Taxable Imports 15% Paid to Customs (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_imports_subject_tp_reverse_charge_mechanism_base
msgid "9. Imports subject to reverse charge mechanism (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_zero_rated_purchases_base
msgid "10. Zero Rated Purchases (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_exempt_purchases_base
msgid "11. Exempt Purchases (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_net_purchases_base
msgid "12. Net Purchases (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_vat_all_sales_tax
msgid "VAT on Sales and all other Outputs (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_standard_rated_15_tax
msgid "1. Standard Rated 15% (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_special_sales_to_locals_tax
msgid "2. Special Sales to Locals (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_local_sales_subject_to_0_tax
msgid "3. Local Sales Subject to 0% (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_export_sales_tax
msgid "4. Export Sales (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_exempt_sales_tax
msgid "5. Exempt Sales (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_net_sales_tax
msgid "6. Net Sales (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_vat_all_expenses_tax
msgid "VAT on Expenses and all other Inputs (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_standard_rated_15_purchases_tax
msgid "7. Standard rated 15% Purchases (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_taxable_imports_15_paid_to_customs_tax
msgid "8. Taxable Imports 15% Paid to Customs (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_imports_subject_tp_reverse_charge_mechanism_tax
msgid "9. Imports subject to reverse charge mechanism (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_zero_rated_purchases_tax
msgid "10. Zero Rated Purchases (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_exempt_purchases_tax
msgid "11. Exempt Purchases (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_net_purchases_tax
msgid "12. Net Purchases (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_net_vat_due
msgid "Net VAT Due"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_total_value_of_due_tax_for_the_period
msgid "Total value of due tax for the period"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_net_vat_due_or_reclaimed_for_the_period
msgid "Net VAT due (or reclaimed) for the period"
msgstr ""

#. module: l10n_sa
#: model:account.tax.report,name:l10n_sa.tax_report_withholding_tax
msgid "Withholding Tax Report"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_on_purchased_services_base
msgid "Withholding Tax on Purchased Services (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_rental_base
msgid "Withholding Tax 5% (Rental) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_tickets_or_air_freight_base
msgid "Withholding Tax 5% (Tickets or Air Freight) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_tickets_or_sea_freight_base
msgid "Withholding Tax 5% (Tickets or Sea Freight)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_international_telecommunication_base
msgid "Withholding Tax 5% (International Telecommunication)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_distributed_profits_base
msgid "Withholding Tax 5% (Distributed Profits) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_consulting_and_technical_base
msgid "Withholding Tax 5% (Consulting and Technical) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_return_from_loans_base
msgid "Withholding Tax 5% (Return from Loans) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_insurance_and_reinsurance_base
msgid "Withholding Tax 5% (Insurance & Reinsurance) (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_royalties_base
msgid "Withholding Tax 15% (Royalties)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_paid_services_from_main_branch_base
msgid "Withholding Tax 15% (Paid Services from Main Branch)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_paid_services_from_another_branch_base
msgid "Withholding Tax 15% (Paid Services from another branch)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_others_base
msgid "Withholding Tax 15% (Others)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_20_managerial_base
msgid "Withholding Tax 20% (Managerial)(Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_total_base
msgid "Withholding Tax Total (Base)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_on_purchased_services_tax
msgid "Withholding Tax on Purchased Services (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_rental_tax
msgid "Withholding Tax 5% (Rental) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_tickets_or_air_freight_tax
msgid "Withholding Tax 5% (Tickets or Air Freight) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_tickets_or_sea_freight_tax
msgid "Withholding Tax 5% (Tickets or Sea Freight)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_international_telecommunication_tax
msgid "Withholding Tax 5% (International Telecommunication)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_distributed_profits_tax
msgid "Withholding Tax 5% (Distributed Profits) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_consulting_and_technical_tax
msgid "Withholding Tax 5% (Consulting and Technical) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_return_from_loans_tax
msgid "Withholding Tax 5% (Return from Loans) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_5_insurance_and_reinsurance_tax
msgid "Withholding Tax 5% (Insurance & Reinsurance) (Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_royalties_tax
msgid "Withholding Tax 15% (Royalties)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_paid_services_from_main_branch_tax
msgid "Withholding Tax 15% (Paid Services from Main Branch)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_paid_services_from_another_branch_tax
msgid "Withholding Tax 15% (Paid Services from another branch)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_15_others_tax
msgid "Withholding Tax 15% (Others)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_20_managerial_tax
msgid "Withholding Tax 20% (Managerial)(Tax)"
msgstr ""

#. module: l10n_sa
#: model:account.report.line,name:l10n_sa.tax_report_line_withholding_tax_total_tax
msgid "Withholding Tax Total (Tax)"
msgstr ""

#. module: l10n_sa
#: model:res.country,vat_label:base.sa
msgid "VAT Number"
msgstr ""

#. module: l10n_sa
#: model:res.currency,currency_unit_label:base.SAR
msgid "Riyal"
msgstr ""

#. module: l10n_sa
#: model:res.currency,currency_subunit_label:base.SAR
msgid "Halala"
msgstr ""

#. module: l10n_sa
#: model:account.tax.group,name:l10n_sa.sa_tax_group_taxes_15
msgid "VAT Taxes"
msgstr ""
