# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_tr
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-31 08:31+0000\n"
"PO-Revision-Date: 2024-10-31 08:31+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_tr
#: model:ir.model,name:l10n_tr.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_tr
#: model:account.report.column,name:l10n_tr.tr_base_column
msgid "Base"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_vat_line_0
msgid "Export Sales 0%"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.net_vat
msgid "Net VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_vat_line_0
msgid "Purchases 0% VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_vat_line_1
msgid "Purchases 1% VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_vat_line_10
msgid "Purchases 10% VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_vat_line_20
msgid "Purchases 20% VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_vat
msgid "Purchases VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_wh
msgid "Purchases Withholding"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_wh_line_10
msgid "Purchases Withholding 20% (10/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_wh_line_2
msgid "Purchases Withholding 20% (2/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_wh_line_3
msgid "Purchases Withholding 20% (3/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_wh_line_4
msgid "Purchases Withholding 20% (4/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_wh_line_5
msgid "Purchases Withholding 20% (5/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_wh_line_7
msgid "Purchases Withholding 20% (7/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.purchases_wh_line_9
msgid "Purchases Withholding 20% (9/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_vat_line_1
msgid "Sales 1% VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_vat_line_10
msgid "Sales 10% VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_vat_line_20
msgid "Sales 20% VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_vat
msgid "Sales VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_wh
msgid "Sales Withholding"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_wh_line_10
msgid "Sales Withholding 20% (10/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_wh_line_2
msgid "Sales Withholding 20% (2/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_wh_line_3
msgid "Sales Withholding 20% (3/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_wh_line_4
msgid "Sales Withholding 20% (4/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_wh_line_5
msgid "Sales Withholding 20% (5/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_wh_line_7
msgid "Sales Withholding 20% (7/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.sales_wh_line_9
msgid "Sales Withholding 20% (9/10)"
msgstr ""

#. module: l10n_tr
#: model:account.report.column,name:l10n_tr.tr_tax_column
msgid "Tax"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.net_vat_line_total
msgid "Total Net VAT"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.vat_due_line_p
msgid "Total Purchase Taxes Paid"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.vat_due_line_s
msgid "Total Sales Tax Collected"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.net_vat_line_p
msgid "Total VAT on Purchases"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.wh_vat_line_p
msgid "Total VAT on Purchases Withheld"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.net_vat_line_s
msgid "Total VAT on Sales"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.wh_vat_line_s
msgid "Total VAT on Sales Withheld"
msgstr ""

#. module: l10n_tr
#: model:account.report,name:l10n_tr.turkey_tax_report
msgid "Türkiye Tax Report"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.vat_due
msgid "VAT Due"
msgstr ""

#. module: l10n_tr
#: model:account.report.line,name:l10n_tr.wh_vat
msgid "Withholding Tax Total"
msgstr ""
