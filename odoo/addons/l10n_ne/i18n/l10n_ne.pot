# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ne
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 10:52+0000\n"
"PO-Revision-Date: 2023-11-30 10:52+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_sales_goods
msgid "1. Goods sales"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible_imported_invest
msgid "10. VAT on imported investments"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible_local_invest
msgid "11. VAT on local investments"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible_imported_goods_services
msgid "12. VAT on imported goods and services"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible_local_goods_services
msgid "13. VAT on local goods and services"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible_withheld
msgid "14. VAT withheld by customers"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible_reported
msgid "15. Credit reported (line 21 of last month)"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible_self
msgid "16. VAT on self deliveries"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible_total
msgid "17. Total deductible VAT (10 to 16)"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_net_deductible_addition
msgid "18. - Additional deductible vat"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_net_repay
msgid "19. - VAT to repay"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_sales_service
msgid "2. Service sales"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_net_to_pay
msgid "20. Net VAT to pay (8+19-17-18)"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_credit_to_report
msgid "21. Credit VAT to report (17+18-8-19)"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_sales_self
msgid "3. Self Deliveries"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_sales_imposable
msgid "4. Taxable turnover (1+2+3)"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_sales_export
msgid "5. Export turnover"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_sales_exempt
msgid "6. Other exempted turnover"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_sales_total_turnover
msgid "7. Total turnover (4+5+6)"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_gross_vat
msgid "8. Gross VAT (line 4x19%)"
msgstr ""

#. module: l10n_ne
#: model:ir.model,name:l10n_ne.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ne
#: model:account.report.column,name:l10n_ne.account_tax_report_ne_balance
msgid "Balance"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_turnover
msgid "I. Turnover (Without Tax)"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_gross
msgid "II. Gross VAT "
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_deductible
msgid "III. Deductible VAT"
msgstr ""

#. module: l10n_ne
#: model:account.report.line,name:l10n_ne.account_tax_report_line_ne_net_vat
msgid "IV. Net VAT"
msgstr ""

#. module: l10n_ne
#: model:account.report,name:l10n_ne.account_tax_report_line_ne_net_regularisation
msgid "Regularisation"
msgstr ""

#. module: l10n_ne
#. odoo-python
#: code:addons/l10n_ne/models/template_ne_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_ne
#. odoo-python
#: code:addons/l10n_ne/models/template_ne.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_ne
#: model:account.report,name:l10n_ne.account_tax_report_ne
msgid "VAT Report"
msgstr ""
