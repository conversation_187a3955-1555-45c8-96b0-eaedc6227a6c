<?xml version="1.0" encoding="utf-8"?>
<odoo>

<!-- Page -->
<template id="layout" name="Partners Layout">
    <t t-call="website.layout">
        <t t-set="additional_title">Resellers</t>
        <div id="wrap">
            <t t-set="editor_message">DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL RESELLERS</t>
            <div class="oe_structure oe_empty" id="oe_structure_website_crm_partner_assign_layout_1" t-att-data-editor-message="editor_message"/>
            <div class="container my-4">
                <t t-out="0" />
            </div>
            <div class="oe_structure oe_empty" id="oe_structure_website_crm_partner_assign_layout_2" t-att-data-editor-message="editor_message"/>
        </div>
    </t>
</template>

<template id="index" name="Find Resellers">
    <t t-call="website_crm_partner_assign.layout">
        <div class="o_wcrm_filters_top d-flex d-print-none align-items-center justify-content-end flex-wrap gap-2 w-100">
            <h4 class="my-0 me-auto pe-sm-4">
                Find a reseller <t t-foreach="countries" t-as="country"><span t-if="country['active'] and country['country_id'][0] != 0 and country['country_id']">in <t t-out="country['country_id'][1]"/></span></t>
            </h4>
            <div class="dropdown d-none d-lg-block">
                <a role="button" href="#" data-bs-toggle="dropdown" class="dropdown-toggle btn btn-light" aria-expanded="true" aria-label="Open categories dropdown">
                    <t t-foreach="grades" t-as="grade">
                        <span t-if="grade['active']" t-out="grade['grade_id'][1]"/>
                    </t>
                </a>
                <div class="dropdown-menu">
                    <t t-foreach="grades" t-as="grade">
                        <a t-attf-href="/partners#{ grade['grade_id'][0] and '/grade/%s' % slug(grade['grade_id']) or '' }#{ current_country and '/country/%s' % slug(current_country) or '' }#{ '?' + (search_path or '') + '&amp;' + keep_query('country_all') }"
                        class="dropdown-item" t-out="grade['grade_id'][1]"/>
                    </t>
                </div>
            </div>
            <div class="dropdown d-none d-lg-block">
                <a role="button" href="#" data-bs-toggle="dropdown" class="dropdown-toggle btn btn-light" aria-expanded="true" aria-label="Open countries dropdown">
                    <t t-foreach="countries" t-as="country">
                        <span t-if="country['active']" t-out="country['country_id'][1]"/>
                    </t>
                </a>
                <div class="dropdown-menu">
                    <t t-foreach="countries" t-as="country" t-if="country['country_id']">
                        <a t-attf-href="/partners#{ current_grade and '/grade/%s' % slug(current_grade) or ''}#{country['country_id'][0] and '/country/%s' % slug(country['country_id']) or '' }#{ '?' + (search_path or '') + (country['country_id'][0] == 0 and '&amp;country_all=True' or '')}"
                        class="dropdown-item" t-out="country['country_id'][1]"/>
                    </t>
                </div>
            </div>
            <div class="o_wcrm_search d-flex w-100 w-lg-auto">
                <form class="flex-grow-1" action="" method="get">
                    <input t-if="country_all" type="hidden" name="country_all" value="True" />
                    <div class="input-group" role="search">
                        <input type="text" name="search" class="search-query form-control border-0 bg-light" placeholder="Search" t-att-value="searches.get('search', '')"/>
                        <button type="submit" aria-label="Search" title="Search" class="oe_search_button btn btn-light">
                            <i class="oi oi-search"/>
                        </button>
                    </div>
                </form>
                <button class="btn btn-light position-relative ms-2 d-lg-none"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#o_wcrm_offcanvas">
                    <i class="fa fa-sliders"/>
                </button>
            </div>
        </div>
        <!-- Off canvas filters on mobile-->
        <div id="o_wcrm_offcanvas" class="o_website_offcanvas offcanvas offcanvas-end d-lg-none p-0 overflow-visible">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title">Filters</h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"/>
            </div>
            <div class="offcanvas-body p-0">
                <div class="accordion accordion-flush">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed"
                                type="button"
                                data-bs-toggle="collapse"
                                data-bs-target=".o_wcrm_offcanvas_grade"
                                aria-expanded="false"
                                aria-controls="o_wcrm_offcanvas_grade">
                                Filter by category
                            </button>
                        </h2>
                        <div class="o_wcrm_offcanvas_grade accordion-collapse collapse">
                            <div class="accordion-body pt-0">
                                <ul class="list-group list-group-flush">
                                    <t t-foreach="grades" t-as="grade">
                                        <li class="list-group-item d-flex justify-content-between align-items-center ps-0 pb-0 border-0">
                                            <a t-attf-href="/partners#{ grade['grade_id'][0] and '/grade/%s' % slug(grade['grade_id']) or '' }#{ current_country and '/country/%s' % slug(current_country) or '' }#{ '?' + (search_path or '') + '&amp;' + keep_query('country_all') }"
                                            class="text-reset" aria-label="See categories filters">
                                                <div class="form-check">
                                                    <input class="form-check-input pe-none" type="radio" t-attf-name="#{grade['grade_id'][1]}" t-att-checked="bool(grade['active'])"/>
                                                    <label class="form-check-label" t-attf-for="#{grade['grade_id'][1]}" t-out="grade['grade_id'][1]"/>
                                                </div>
                                            </a>
                                        </li>
                                    </t>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button border-top collapsed"
                                type="button"
                                data-bs-toggle="collapse"
                                data-bs-target=".o_wcrm_offcanvas_country"
                                aria-expanded="false"
                                aria-controls="o_wcrm_offcanvas_country">
                                Filter by country
                            </button>
                        </h2>
                        <div class="o_wcrm_offcanvas_country accordion-collapse collapse">
                            <div class="accordion-body pt-0">
                                <ul class="list-group list-group-flush">
                                    <t t-foreach="countries" t-as="country">
                                        <li t-if="country['country_id']" class="list-group-item d-flex justify-content-between align-items-center ps-0 pb-0 border-0">
                                            <a t-attf-href="/partners#{ current_grade and '/grade/%s' % slug(current_grade) or ''}#{country['country_id'][0] and '/country/%s' % slug(country['country_id']) or '' }#{ '?' + (search_path or '') + (country['country_id'][0] == 0 and '&amp;country_all=True' or '')}"
                                            class="text-reset" aria-label="See countries filters">
                                                <div class="form-check">
                                                    <input class="form-check-input pe-none" type="radio" t-attf-name="#{country['country_id'][1]}" t-att-checked="bool(country['active'])"/>
                                                    <label class="form-check-label" t-attf-for="#{country['country_id'][1]}" t-out="country['country_id'][1]"/>
                                                </div>
                                            </a>
                                        </li>
                                    </t>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="my-5 py-5 text-center" t-if="not partners">
                <h5>No results found for "<span t-out="searches.get('search', '')"/>"</h5>
                <a href="/partners">See all resellers</a>
            </div>
            <div t-elif="fallback_all_countries" class="mt-4 alert alert-primary alert-dismissible fade show" role="alert">
                <i class="fa fa-info-circle me-2"/>
                There are no matching partners found for the selected country. Displaying results across all countries instead.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <t t-set="last_grade" t-value="None"/>
            <t t-foreach="partners" t-as="partner">
                <t t-if="last_grade != partner.grade_id.id">
                    <h5 class="mt-4 mb-2 col-12">
                        <span t-field="partner.grade_id"/> Resellers
                        <t t-call="website.publish_management">
                            <t t-set="object" t-value="partner.grade_id"/>
                            <t t-set="publish_edit" t-value="True"/>
                        </t>
                    </h5>
                    <t t-set="last_grade" t-value="partner.grade_id.id"/>
                </t>
                <div class="col-md-4 col-xl-3 col-12 mb-4">
                    <div class="card h-100 text-decoration-none">
                        <a class="text-decoration-none" t-attf-href="/partners/#{slug(partner)}?#{current_grade and 'grade_id=%s&amp;' % current_grade.id}#{current_country and 'country_id=%s' % current_country.id}" aria-label="Go to reseller">
                            <div t-field="partner.image_1920"
                                class="card-img-top border-bottom"
                                t-options='{"widget": "image", "qweb_img_responsive": False, "class": "img img-fluid h-100 w-100 mw-100", "style": "max-height: 208px; min-height: 208px; object-fit: cover"}'
                                />
                            <div class="card-body">
                                <h5 class="card-title m-0" t-attf-href="/partners/#{slug(partner)}?#{current_grade and 'grade_id=%s&amp;' % current_grade.id}#{current_country and 'country_id=%s' % current_country.id}" t-field="partner.display_name"/>
                                <small id="o_wcrm_partners_address"/>
                                <small class="o_wcrm_short_description text-muted overflow-hidden">
                                    <b t-if="partner.implemented_partner_count">
                                        <t t-if="partner.implemented_partner_count > 1" t-set='reflabel'>references</t>
                                        <t t-else="" t-set='reflabel'>reference</t>
                                        <t t-out="partner.implemented_partner_count"/> <t t-out='reflabel'/> » </b>
                                    <span t-field="partner.website_short_description"/>
                                </small>
                                <small t-if="not partner.website_short_description" class="css_editable_mode_hidden text-muted fst-italic" groups="website.group_website_restricted_editor">
                                    Edit to add a short description
                                </small>
                            </div>
                        </a>
                    </div>
                </div>
            </t>
            <div class="navbar">
                <t t-call="website.pager">
                   <t t-set="classname" t-valuef="mx-auto"/>
                </t>
            </div>
        </div>
    </t>
</template>

<template id="ref_country" inherit_id="website_crm_partner_assign.index" name="World Map">
    <xpath expr="//div[hasclass('o_wcrm_search')]" position="inside">
        <t t-if="google_maps_api_key">
            <!-- modal for large map -->
            <div role="dialog" class="modal fade partner_map_modal" tabindex="-1">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <header class="modal-header">
                        <h4 class="modal-title">World Map</h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"/>
                    </header>
                    <iframe loading="lazy" t-attf-src="/google_map?height=485&amp;dom=website_crm_partner_assign.partners&amp;current_grade=#{ current_grade and current_grade.id }&amp;current_country=#{ current_country and current_country.id }&amp;partner_url=/partners/&amp;limit=1000"
                    style="height:485px;"/>
                </div>
              </div>
            </div>
            <!-- modal end -->
            <div class="btn-group ms-2">
                <button class="btn btn-light border-primary active">
                    <i class="fa fa-th-large"/>
                </button>
                <button class="btn btn-light" data-bs-toggle="modal" data-bs-target=".partner_map_modal">
                    <i class="fa fa-map-marker" role="img" aria-label="Open map" title="Open map"/>
                </button>
            </div>
        </t>
    </xpath>
</template>

<template id="o_wcrm_partner_address" inherit_id="website_crm_partner_assign.index" name="Address">
    <xpath expr="//small[@id='o_wcrm_partners_address']" position="inside">
        <div t-field="partner.self" 
            t-options="{
                'widget': 'contact',
                'fields': ['country_id']
            }"
            class="py-2"
        />
    </xpath>
</template>

<template id="partner" name="Partner Detail">
    <t t-call="website_crm_partner_assign.layout">
        <div t-if="not edit_page" class="mb-3">
            <a t-attf-href="/partners#{current_grade and '/grade/%s' % slug(current_grade)}#{current_country and '/country/%s' % slug(current_country)}" aria-label="Back to resellers list"><i class="fa fa-chevron-left me-2"/>Back to resellers</a>
        </div>
        <t t-call="website_partner.partner_detail">
            <t t-set="right_column">
                <div id="right_column"><t t-call="website_crm_partner_assign.references_block"/></div>
            </t>
        </t>
    </t>
</template>

<template id="grade_in_detail" inherit_id="website_partner.partner_detail">
    <xpath expr="//*[@id='partner_name']" position="before">
        <span class="col-lg-12 text-muted" t-if="partner.grade_id and partner.grade_id.website_published">
            <span t-field="partner.grade_id"/>
        </span>
    </xpath>
</template>

<template id="references_block" name="Partner References Block">
    <t t-if="any(p.website_published for p in partner.implemented_partner_ids)">
        <h4 id="references">References</h4>
        <div t-foreach="partner.implemented_partner_ids" t-if="reference.website_published" t-as="reference" class="card mt-3 border-0">
            <div class="row">
                <div class="col-md-2">
                    <span t-field="reference.avatar_128" class="d-flex justify-content-center" t-options='{"widget": "image", "qweb_img_responsive": False, "class": "img-fluid rounded mw-100"}'/>
                </div>
                <div class="card-body col-md-10">
                    <span t-field="reference.self"/>
                    <div t-field='reference.website_short_description'/>
                </div>
            </div>
        </div>
    </t>
</template>

<!-- Portal -->
    <template id="portal_my_home_menu_lead" name="Portal layout : lead menu entry" inherit_id="portal.portal_breadcrumbs" priority="15">
        <xpath expr="//ol[hasclass('o_portal_submenu')]" position="inside">
            <li t-if="page_name == 'lead' or lead" t-attf-class="breadcrumb-item #{'active ' if not lead else ''}">
                <a t-if="lead" t-attf-href="/my/leads?{{ keep_query() }}">
                    Leads
                </a>
                <t t-else="">
                    Leads
                </t>
            </li>
            <li t-if="lead" class="breadcrumb-item active">
                <span t-field="lead.name"/>
            </li>
            <li t-if="page_name == 'opportunity' or opportunity" t-attf-class="breadcrumb-item #{'active ' if not opportunity else ''}">
                <a t-if="opportunity" t-attf-href="/my/opportunities?{{ keep_query() }}">
                    Opportunities
                </a>
                <t t-else="">
                    Opportunities
                </t>
            </li>
            <li t-if="opportunity" class="breadcrumb-item active">
                <span t-field="opportunity.name"/>
            </li>
        </xpath>
    </template>

    <template id="portal_my_home_lead" name="Leads / Opps" inherit_id="portal.portal_my_home" priority="15">
        <xpath expr="//div[hasclass('o_portal_docs')]" position="before">
            <t t-set="portal_vendor_category_enable" t-value="True"/>
        </xpath>
        <div id="portal_vendor_category" position="inside">
            <t t-call="portal.portal_docs_entry">
                <t t-set="icon" t-value="'/website_crm_partner_assign/static/src/img/leads.svg'"/>
                <t t-set="text">Follow and convert your leads</t>
                <t t-set="title">Leads</t>
                <t t-set="url" t-value="'/my/leads'"/>
                <t t-set="placeholder_count" t-value="'lead_count'"/>
            </t>
            <t t-call="portal.portal_docs_entry">
                <t t-set="icon" t-value="'/website_crm_partner_assign/static/src/img/quotation.svg'"/>
                <t t-set="text">Follow and convert your opportunities</t>
                <t t-set="title">Opportunities</t>
                <t t-set="url" t-value="'/my/opportunities'"/>
                <t t-set="placeholder_count" t-value="'opp_count'"/>
                <t t-set="config_card" t-value="request.env.user.partner_id.grade_id or request.env.user.commercial_partner_id.grade_id"/>
            </t>
        </div>
    </template>

    <template id="portal_my_leads" name="My Leads">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>

            <t t-call="portal.portal_searchbar">
                <t t-set="title">Leads</t>
            </t>
            <div t-if="not leads" class="alert alert-warning" role="alert">
                There are no leads.
            </div>
            <t t-if="leads" t-call="portal.portal_table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th class="w-25">Lead</th>
                        <th>Contact Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                    </tr>
                </thead>
                <tbody>
                    <tr t-foreach="leads" t-as="lead">
                        <td><span t-field="lead.create_date" t-options='{"widget": "date"}' /></td>
                        <td class="text-wrap">
                            <a t-attf-href="/my/lead/#{lead.id}"><span t-field="lead.name"/></a>
                        </td>
                        <td><span t-field="lead.contact_name"/></td>
                        <td><span t-field="lead.email_from"/></td>
                        <td><span t-field="lead.phone"/></td>
                    </tr>
                </tbody>
            </t>
        </t>
    </template>

    <template id="portal_my_opportunities" name="My Opportunities">
        <t t-call="portal.portal_layout">
            <t t-set="breadcrumbs_searchbar" t-value="True"/>

            <t t-call="portal.portal_searchbar">
                <t t-set="title">Opportunities</t>

                <div t-if="request.env.user.partner_id.grade_id or request.env.user.commercial_partner_id.grade_id">
                    <button class="btn btn-primary" name='new_opp' data-bs-toggle="modal" data-bs-target=".modal_new_opp" title="Add an opportunity" aria-label="Add an opportunity">
                        Create Opportunity
                    </button>
                </div>
            </t>

            <div class="modal fade modal_new_opp" role="form">
                <div class="modal-dialog">
                    <form method="POST" class="modal-content js_website_submit_form new_opp_form">
                        <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                        <header class="modal-header">
                            <h4 class="modal-title">New Opportunity</h4>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </header>
                        <main class="modal-body" id="new-opp-dialog">
                            <div class="mb-3">
                                <label class="col-form-label hdd4" for="contact_name">Contact name</label>
                                <input type='text' name="contact_name" class="form-control contact_name"/>
                            </div>
                            <div class="mb-3">
                                <label class="col-form-label h4dd" for="title">Opportunity</label>
                                <input type='text' name="title" class="form-control title"/>
                            </div>
                            <div class="mb-3">
                                <label class="col-form-label hdd4" for="description">Description</label>
                                <textarea rows="3" name="description" class="form-control description"></textarea>
                            </div>
                        </main>
                        <footer class="modal-footer">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                            <button t-attf-class="btn btn-primary new_opp_confirm">Confirm</button>
                        </footer>
                    </form>
                </div>
            </div>
            <t t-if="not opportunities">
                <div class="alert alert-warning" role="alert">
                    There are no opportunities.
                </div>
            </t>
            <t t-if="opportunities" t-call="portal.portal_table">
                <thead>
                    <tr class="active">
                        <th>Date</th>
                        <th class="w-25">Opportunity</th>
                        <th>Contact</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Expected</th>
                        <th>Stage</th>
                    </tr>
                </thead>
                <tbody>
                    <tr t-foreach="opportunities" t-as="opp">
                        <td><span t-field="opp.create_date" t-options='{"widget": "date"}' /></td>
                        <td class="text-wrap">
                            <a t-attf-href="/my/opportunity/#{opp.id}"><span t-field="opp.name"/></a>
                        </td>
                        <td><span t-field="opp.contact_name"/></td>
                        <td><span t-field="opp.email_from"/></td>
                        <td><span t-field="opp.phone"/></td>
                        <td>
                            <span t-if="opp.company_currency" class="text-nowrap" t-esc="opp.expected_revenue" t-options="{'widget': 'monetary', 'display_currency': opp.company_currency}"/>
                            <span t-else="" class="text-nowrap" t-esc="opp.expected_revenue"/>
                            <span> at </span>
                            <span t-field="opp.probability" />%
                        </td>
                        <td>
                            <span class="badge text-bg-info" title="Current stage of the opportunity" t-esc="opp.stage_id.name" />
                        </td>
                    </tr>
                </tbody>
            </t>
        </t>
    </template>

    <template id="portal_my_lead" name="My Lead">
        <t t-call="portal.portal_layout">
            <div class="d-flex align-items-center gap-2 mb-4">
                <h4 class="mb-0">Lead -
                    <span t-field="lead.name"/>
                    <span title="Rating" role="img" t-attf-aria-label="Rating: #{lead.priority} on 3" class="fs-6">
                        <t t-foreach="range(1, 4)" t-as="i">
                            <span t-attf-class="fa fa-lg fa-star#{' text-warning' if i &lt;= int(lead.priority) else '-o'}"/>
                        </t>
                    </span>
                </h4>
            </div>
            <table class="table table-borderless">
                <tbody class="text-nowrap">
                    <tr t-if="lead.partner_name or lead.email_from or lead.partner_id">
                        <th class="ps-0 pb-0">Customer:</th>
                        <td>
                            <address>
                                <div>
                                    <span t-if="lead.partner_name" itemprop="name" t-field="lead.partner_name" />
                                    <span t-if="not lead.partner_name" itemprop="name" t-field="lead.contact_name"/>
                                </div>
                                <div t-if="lead.phone">
                                    <span class="fa fa-phone" role="img" aria-label="Phone" title="Phone"/> <span itemprop="telephone" t-field="lead.phone" />
                                </div>
                                <div t-if="lead.mobile">
                                    <span class="fa fa-mobile" role="img" aria-label="Mobile" title="Mobile"/> <span itemprop="telephone" t-field="lead.mobile" />
                                </div>
                                <div t-if="lead.email_from">
                                    <span class="fa fa-envelope" role="img" aria-label="Email" title="Email"/> <span itemprop="email" t-field="lead.email_from" />
                                </div>
                            </address>
                        </td>
                    </tr>
                    <tr t-if="lead.street or lead.street2 or lead.city or lead.state_id or lead.country_id">
                        <th class="ps-0 pb-0">Address:</th>
                        <td class="w-100 pb-0 text-wrap">
                            <address>
                                <div t-if="lead.street"><span t-field="lead.street"/></div>
                                <div t-if="lead.street2"><span t-field="lead.street2"/></div>
                                <div t-if="lead.city or lead.zip">
                                    <span t-field="lead.city"/> <span t-field="lead.zip"/>
                                </div>
                            </address>
                        </td>
                    </tr>
                    <tr t-if="lead.user_id">
                        <th class="ps-0 pb-0">Salesperson:</th>
                        <td class="w-100 pb-0 text-wrap">
                            <span t-field="lead.user_id"/>
                        </td>
                    </tr>
                    <tr t-if="lead.team_id">
                        <th class="ps-0 pb-0">Sales Team:</th>
                        <td class="w-100 pb-0 text-wrap">
                            <span t-field="lead.team_id"/>
                        </td>
                    </tr>
                    <tr t-if="lead.date_deadline">
                        <th class="ps-0 pb-0">Expected Closing:</th>
                        <td class="w-100 pb-0 text-wrap">
                            <span t-field="lead.date_deadline"/>
                        </td>
                    </tr>
                    <tr groups="!base.group_portal" t-if="lead.tag_ids">
                        <th class="ps-0 pb-0">Tags</th>
                        <td class="w-100 pb-0 text-wrap">
                             <t t-foreach="lead.tag_ids" t-as="tag">
                                <span class="badge text-bg-info" t-esc="tag.name" />
                            </t>
                        </td>
                    </tr>
                    <tr t-if="lead.partner_assigned_id">
                        <th class="ps-0 pb-0">Assigned Partner:</th>
                        <td class="w-100 pb-0 text-wrap">
                            <address t-field="lead.partner_assigned_id" t-options='{"widget": "contact", "fields": ["name", "email", "phone"], "no_marker": True}'/>
                        </td>
                    </tr>
                    <tr t-if="lead.campaign_id">
                        <th class="ps-0 pb-0">Campaign:</th>
                        <td class="w-100 pb-0 text-wrap">
                            <span t-field="lead.campaign_id"/>
                        </td>
                    </tr>
                    <tr t-if="lead.medium_id">
                        <th class="ps-0 pb-0">Medium:</th>
                        <td class="w-100 pb-0 text-wrap">
                            <span t-field="lead.medium_id"/>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class='d-flex justify-content-center gap-2 mt-3'>
                <a role="button" title="I'm interested" href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target=".modal_partner_assign_interested">I'm interested</a>
                <a role="button" title="I'm not interested" href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target=".modal_partner_assign_desinterested"> I'm not interested</a>
                <div class="modal fade modal_partner_assign_interested" role="form">
                    <div class="modal-dialog">
                        <form method="POST" class="js_accept_json modal-content js_website_submit_form interested_partner_assign_form">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            <input type="hidden" name="lead_id" class="assign_lead_id" t-att-value="lead.id"/>
                            <header class="modal-header">
                                <h4 class="modal-title">Lead Feedback</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </header>
                            <main class="modal-body" id="sign-dialog">
                                <div class="mb-3">
                                    <label class="col-form-label" for="comment">What is the next action? When? What is the expected revenue?</label>
                                    <input type="text" name="comment" id="comment" class="form-control comment_interested"/>
                                </div>
                                <div class="mb-3">
                                    <label class="col-form-label" for="customer_contacted">I have contacted the customer</label>
                                    <input type="checkbox" name="customer_contacted" id="customer_contacted" class="contacted_interested"/>
                                </div>
                                <div>
                                    <span class="text-danger error_partner_assign_interested" style="display:none;">You need to fill up the next action and contact the customer before accepting the lead</span>
                                </div>
                            </main>
                            <footer class="modal-footer">
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                                <button t-attf-class="btn btn-primary interested_partner_assign_confirm">Confirm</button>
                            </footer>
                        </form>
                    </div>
                </div>
                <div role="dialog" class="modal fade modal_partner_assign_desinterested">
                    <div class="modal-dialog">
                        <form method="POST" class="js_accept_json modal-content js_website_submit_form desinterested_partner_assign_form">
                            <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                            <input type="hidden" name="lead_id" class="assign_lead_id" t-att-value="lead.id"/>
                            <header class="modal-header">
                                <h4 class="modal-title">Lead Feedback</h4>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </header>
                            <main class="modal-body" id="sign-dialog">
                                <div class="mb-3">
                                    <label class="col-form-label" for="comment">Why aren't you interested in this lead?</label>
                                    <input type="text" name="comment" id="comment" class="form-control comment_desinterested"/>
                                </div>
                                <div class="mb-3">
                                    <label class="col-form-label" for="contacted_desinterested">I have contacted the customer</label>
                                    <input type="checkbox" name="contacted_desinterested" id="contacted_desinterested" class="contacted_desinterested"/>
                                </div>
                                <div class="mb-3">
                                    <label class="col-form-label" for="customer_mark_spam">This lead is a spam</label>
                                    <input type="checkbox" name="customer_mark_spam" id="customer_mark_spam" class="customer_mark_spam"/>
                                </div>
                                <div>
                                    <span class="text-danger error_partner_assign_desinterested" style="display:none;">You need to fill up the next action and contact the customer before accepting the lead</span>
                                </div>
                            </main>
                            <footer class="modal-footer">
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                                <button t-attf-class="btn btn-primary desinterested_partner_assign_confirm">Confirm</button>
                            </footer>
                        </form>
                    </div>
                </div>
            </div>
            <hr/>
            <div>
                <h3>Communication history</h3>
                <div>
                    <t t-call="portal.message_thread">
                        <t t-set="object" t-value="lead"/>
                    </t>
                </div>
            </div>
        </t>
    </template>

    <template id="portal_my_opportunity" name="My Opportunity">
        <t t-call="portal.portal_layout">
            <div class="d-flex justify-content-between flex-wrap mb-3">
                <h4 class="mb-2 mb-md-0">
                    Opportunity -
                    <span t-field="opportunity.name"/>
                </h4>
                <!-- todo: replace by design system wizard -->
                <div class="d-flex justify-content-between align-items-center gap-2">
                    <p class="mb-0 fw-bold">Stage:</p>
                    <div t-foreach="stages[::-1]" t-as="stage" class="d-flex justify-content-between align-items-center gap-2">
                        <i t-if="not stage_first" class="oi oi-chevron-right small" style="opacity: 0.5"/>
                        <button type="button" t-att-data="stage.id" t-att-opp="opportunity.id" t-attf-class="btn btn-sm px-2 opp-stage-button #{'btn-light' if opportunity.stage_id.name != stage.name else 'btn-primary disabled'}">
                            <span t-field="stage.name"/>
                        </button>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-5 mb-4 mb-lg-0">
                    <div class="border-bottom d-flex justify-content-between py-2 mb-3 align-items-center">
                        <h5 class="d-flex align-items-center justify-content-between gap-2 mb-0 ">
                            <span>
                                <t t-if="opportunity.company_currency" t-out="opportunity.expected_revenue"
                                    t-options="{'widget': 'monetary', 'display_currency': opportunity.company_currency}"/>
                                <t t-else="" t-out="opportunity.expected_revenue"/> at  </span>
                            <span class="badge text-bg-info"><span t-field="opportunity.probability"/>%</span>
                        </h5>
                        <button type="button" data-bs-toggle="modal" data-bs-target=".modal_edit_opp" class="btn btn-link btn-sm"><i class="fa fa-pencil me-1"/>Edit</button>
                    </div>
                    <div class="row mb-2">
                        <strong class="col-12 col-sm-4">Expected Closing</strong>
                        <div class="col">
                            <span t-if="opportunity.date_deadline" t-field="opportunity.date_deadline"/>
                            <span t-else="" class="text-muted"> - </span>
                        </div>
                    </div>
                    <div class="row mb-2">
                        <strong class="col-12 col-sm-4">Priority</strong>
                        <div class="col">
                            <span class="" title="Rating" role="img" t-attf-aria-label="Rating: #{opportunity.priority} on 4">
                                <t t-foreach="range(1, 4)" t-as="i">
                                    <span t-attf-class="fa text-warning fa-lg fa-star#{'' if i &lt;= int(opportunity.priority) else '-o'}" role="img" t-att-aria-label="'Star %d of 3' % i"/>
                                </t>
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <strong class="col-12 col-sm-4">Next Activity</strong>
                        <div class="col" t-if="user_activity">
                            <span t-field="user_activity.activity_type_id"/>
                            <span t-if="user_activity.date_deadline">&#160;on&#160;</span>
                            <span t-field="user_activity.date_deadline"/>
                            <em class="d-block" t-field="user_activity.summary"/>
                        </div>
                        <div class="col" t-else="">
                            <span class="text-muted"> - </span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 offset-lg-1 col-xl-5 offset-xl-2">
                    <div class="d-flex justify-content-between py-2 mb-3 border-bottom align-items-center">
                        <h5 class="card-title mb-0">Contact</h5>
                        <button type="button" data-bs-toggle="modal" data-bs-target=".modal_edit_contact" class="btn btn-link btn-sm"><i class="fa fa-pencil me-1"/>Edit</button>
                    </div>
                    <div class="row mb-3" t-if="opportunity.partner_name or opportunity.email_from or opportunity.contact_name">
                        <strong class="col-12 col-sm-3">Customer</strong>
                        <div class="col">
                            <div class="d-flex justify-content-start align-items-center gap-2 mb-2" t-if="opportunity.partner_name or opportunity.contact_name">
                                <img t-if="opportunity.partner_id.sudo().avatar_1024" class="o_avatar o_portal_contact_img rounded"
                                        t-att-src="image_data_uri(opportunity.partner_id.sudo().avatar_512)"
                                alt="Contact" width="50"/>
                                <div class="d-flex flex-column justify-content-center">
                                    <h5 class="mb-0" t-if="opportunity.partner_name" t-out="opportunity.partner_name"/>
                                    <p class="mb-0 text-muted" t-else="" t-out="opportunity.contact_name"/>
                                </div>
                            </div>
                            <div>
                                <div t-field="opportunity.partner_id" t-options='{"widget": "contact", "fields": ["email", "phone", "mobile"]}'/>
                            </div>
                            <address t-if="opportunity.street or opportunity.street2 or opportunity.city or opportunity.zip or opportunity.state_id or opportunity.country_id"
                            class="col d-flex align-items-baseline mb-0 mt-2">
                                <div class="d-flex flex-nowrap gap-3">
                                    <div class="fa fa-map-marker fa-fw text-muted"></div>
                                    <div>
                                        <div t-if="opportunity.street"><span t-field="opportunity.street"/></div>
                                        <span t-else="" class="text-muted"> - </span>
                                        <div t-if="opportunity.street2"><span t-field="opportunity.street2"/></div>
                                        <div t-if="opportunity.city or opportunity.zip">
                                            <span t-field="opportunity.city"/> <span t-field="opportunity.zip"/>
                                        </div>
                                        <div t-if="opportunity.state_id or opportunity.country_id">
                                            <span t-field="opportunity.state_id"/> <span t-field="opportunity.country_id"/>
                                        </div>
                                    </div>
                                </div>
                            </address>
                        </div>
                    </div>
                </div>

                <!-- ==== MODALS ==== -->
                <div>
                    <div role="dialog" class="modal fade modal_edit_opp">
                        <div class="modal-dialog">
                            <form method="POST" class="js_accept_json modal-content js_website_submit_form edit_opp_form">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <input type="hidden" name="opportunity_id" class="opportunity_id" t-att-value="opportunity.id"/>
                                <header class="modal-header">
                                    <h4 class="modal-title">Edit Opportunity</h4>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </header>
                                <main class="modal-body" id="sign-dialog">
                                    <div class="mb-3">
                                        <div class="row align-items-center">
                                            <div class="col-auto flex-grow-1">
                                                <div class="input-group">
                                                    <div class="input-group-text"><span class="text-nowrap" t-esc="opportunity.company_currency.symbol"/></div>
                                                    <input type="text" name="expected_revenue" class="form-control expected_revenue" t-att-value="opportunity.expected_revenue" placeholder="Planned Revenue"/>
                                                </div>
                                            </div>
                                            <div class="col-auto">at</div>
                                            <div class="col-auto">
                                                <div class="input-group">
                                                    <input type="text" name="probability" class="form-control probability" t-att-value="opportunity.probability" placeholder="Probability"/>
                                                    <div class="input-group-text">%</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="row">
                                            <div class="col-md-5 pe-0">
                                                <label>Priority:</label>
                                                <div class="input-group">
                                                    <label class="radio-inline">
                                                        <input type="radio" name="PriorityRadioOptions" value="0" t-att-checked="opportunity.priority not in ['1','2','3']" aria-label="Rating: 0 on 3" title="Rating: 0 on 3"/>
                                                        <i class="ms-1 fa fa-star-o"></i>
                                                    </label>
                                                    <label class="radio-inline ms-2">
                                                        <input type="radio" name="PriorityRadioOptions" value="1" t-att-checked="opportunity.priority == '1'" aria-label="Rating: 1 on 3" title="Rating: 1 on 3"/>
                                                        <i class="ms-1 fa text-warning fa-star"></i>
                                                    </label>
                                                    <label class="radio-inline ms-2">
                                                        <input type="radio" name="PriorityRadioOptions" value="2" t-att-checked="opportunity.priority == '2'" aria-label="Rating: 2 on 3" title="Rating: 2 on 3"/>
                                                        <i class="ms-1 fa text-warning fa-star"></i>
                                                        <i class="ms-1 fa text-warning fa-star"></i>
                                                    </label>
                                                    <label class="radio-inline ms-2">
                                                        <input type="radio" name="PriorityRadioOptions" value="3" t-att-checked="opportunity.priority == '3'" aria-label="Rating: 3 on 3" title="Rating: 3 on 3"/>
                                                        <i class="ms-1 fa text-warning fa-star"></i>
                                                        <i class="ms-1 fa text-warning fa-star"></i>
                                                        <i class="ms-1 fa text-warning fa-star"></i>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-7">
                                                <label>Expected Closing:</label>
                                                <div class="input-group date" id="exp_closing_div">
                                                    <t t-set='date_formatted'><t t-options='{"widget": "date"}' t-esc="opportunity.date_deadline"/></t>
                                                    <input type="text" data-widget="datetime-picker" data-widget-type="date" name="date_deadline" t-att-value="date_formatted" class="datetimepicker-input form-control date_deadline" t-att-name="prefix"/>
                                                    <span class="input-group-text o_input_group_date_icon">
                                                        <span class="fa fa-calendar" role="img" aria-label="Calendar"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <div class="row">
                                            <div class="col-md-5">
                                                <label class="col-form-label" for="next_activity">Next Activity</label>
                                                <select class="form-select next_activity" name="next_activity">
                                                    <t t-foreach="activity_types" t-as="activity_type">
                                                        <option t-att-data="activity_type.id" t-att-selected="activity_type.id == user_activity.activity_type_id.id"
                                                            t-att-name="activity_type.name" t-att-delay_count="activity_type.delay_count"
                                                            t-att-delay_unit="activity_type.delay_unit" t-att-summary="activity_type.summary">
                                                        <t t-esc="activity_type.name"/></option>
                                                    </t>
                                                </select>
                                            </div>
                                            <div class="col-md-7">
                                                <label class="col-form-label" for="activity_date_deadline">Next Activity Date</label>
                                                <div class="input-group date" id="next_activity_div" >
                                                    <t t-set='date_formatted'><t t-options='{"widget": "date"}' t-esc="user_activity.date_deadline"/></t>
                                                    <input type="text" data-widget="datetime-picker" data-widget-type="date" name="activity_date_deadline" t-att-value="date_formatted" class="form-control activity_date_deadline datetimepicker-input" t-att-name="prefix"/>
                                                    <span class="input-group-text o_input_group_date_icon">
                                                        <span class="fa fa-calendar" role="img" aria-label="Calendar" title="Calendar"></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="col-form-label" for="activity_summary">Details Next Activity</label>
                                        <textarea rows="6" name="activity_summary" class="form-control activity_summary"><t t-esc="user_activity.summary"/></textarea>
                                    </div>
                                </main>
                                <footer class="modal-footer">
                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                                    <button t-attf-class="btn btn-primary edit_opp_confirm">Confirm</button>
                                </footer>
                            </form>
                        </div>
                    </div>

                    <div role="dialog" class="modal fade modal_edit_contact">
                        <div class="modal-dialog">
                            <form method="POST" class="js_accept_json modal-content js_website_submit_form edit_contact_form">
                                <input type="hidden" name="csrf_token" t-att-value="request.csrf_token()"/>
                                <input type="hidden" name="opportunity_id" class="opportunity_id" t-att-value="opportunity.id"/>
                                <header class="modal-header">
                                    <h4 class="modal-title">Edit Contact</h4>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </header>
                                <main class="modal-body" id="sign-dialog">
                                    <t t-if="opportunity.partner_name">
                                        <div class="mb-3">
                                            <label class="col-form-label" for="partner_name">Customer Name</label>
                                            <input type="text" name="partner_name" class="form-control partner_name" t-att-value="opportunity.partner_name"/>
                                        </div>
                                    </t>
                                    <t t-if="not opportunity.partner_name">
                                        <div class="mb-3">
                                            <label class="col-form-label" for="partner_name">Customer Name</label>
                                            <input type="text" name="partner_name" class="form-control partner_name" t-att-value="opportunity.contact_name"/>
                                        </div>
                                    </t>
                                    <div class="mb-3">
                                        <label class="col-form-label" for="phone">Phone</label>
                                        <input type="text" name="phone" class="form-control phone" t-att-value="opportunity.phone"/>
                                    </div>
                                    <div class="mb-3">
                                        <label class="col-form-label" for="mobile">Mobile</label>
                                        <input type="text" name="mobile" class="form-control mobile" t-att-value="opportunity.mobile"/>
                                    </div>
                                    <div class="mb-3">
                                        <label class="col-form-label" for="email_from">Email</label>
                                        <input type="text" name="email_from" class="form-control email_from" t-att-value="opportunity.email_from"/>
                                    </div>
                                    <div class="mb-3">
                                        <label class="col-form-label" for="street">Address</label>
                                        <input type="text" name="street" class="form-control street" t-att-value="opportunity.street" placeholder="Street"/>
                                    </div>
                                    <div class="mb-3">
                                        <input type="text" name="street2" class="form-control street2" t-att-value="opportunity.street2" placeholder="Street2"/>
                                    </div>
                                    <div class="mb-3">
                                        <div class="row">
                                            <div class="col-md-5">
                                                <input type="text" name="city" class="form-control city" t-att-value="opportunity.city" placeholder="City"/>
                                            </div>
                                            <div class="col-md-5">
                                                <select name="state_id" class="form-select state_id">
                                                    <option>States...</option>
                                                    <t t-foreach="states or []" t-as="state">
                                                        <option class="state" t-att-value="state.id" t-att-country="state.country_id.id" t-att-selected="state.id == opportunity.state_id.id">
                                                            <t t-esc="state.name"/>
                                                        </option>
                                                    </t>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <input type="text" name="zip" class="form-control zip" t-att-value="opportunity.zip" placeholder="ZIP"/>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <select name="country_id" class="form-select country_id">
                                            <option>Countries...</option>
                                            <t t-foreach="countries or []" t-as="country">
                                                <option t-att-value="country.id" t-att-selected="country.id == opportunity.country_id.id">
                                                    <t t-esc="country.name"/>
                                                </option>
                                            </t>
                                        </select>
                                    </div>
                                </main>
                                <footer class="modal-footer">
                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                                    <button t-attf-class="btn btn-primary edit_contact_confirm">Confirm</button>
                                </footer>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- === / MODALS === -->
            </div>

            <hr/>

            <div>
                <h3>Communication history</h3>
                <div>
                    <t t-call="portal.message_thread">
                        <t t-set="object" t-value="opportunity"/>
                    </t>
                </div>
            </div>
        </t>
    </template>

</odoo>
