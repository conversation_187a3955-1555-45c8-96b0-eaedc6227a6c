# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_crm_partner_assign
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON> <mi<PERSON><PERSON><PERSON>@gmail.com>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ka<PERSON><PERSON>l<PERSON>@emsystems.fi>, 2024
# V<PERSON>k<PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Ossi Mantylahti <<EMAIL>>, 2025\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__nbr_opportunities
msgid "# of Opportunity"
msgstr "# Mahdollisuutta"

#. module: website_crm_partner_assign
#. odoo-javascript
#: code:addons/website_crm_partner_assign/static/src/js/crm_partner_assign.js:0
msgid "%s's Opportunity"
msgstr "%s:n myyntimahdollisuus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_view_kanban
msgid ""
"(<i class=\"fa fa-long-arrow-right me-1\" aria-label=\"Assigned Partner\" "
"title=\"Assigned Partner\"/>"
msgstr ""
"(<i class=\"fa fa-long-arrow-right me-1\" aria-label=\"Vastuutettu "
"partneri\" title=\"Vastuutettu partneri\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "<i class=\"fa fa-chevron-left me-2\"/>Back to resellers"
msgstr "<i class=\"fa fa-chevron-left me-2\"/>Takaisin jälleenmyyjille"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid ""
"<i class=\"fa fa-info-circle me-2\"/>\n"
"                There are no matching partners found for the selected country. Displaying results across all countries instead."
msgstr ""
"<i class=\"fa fa-info-circle me-2\"/>\n"
"                Valitusta maasta ei löytynyt sopivia kumppaneita. Sen sijaan näytetään tulokset kaikista maista."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid ""
"<i class=\"fa fa-map-marker\" role=\"img\" aria-label=\"Open map\" "
"title=\"Open map\"/>"
msgstr ""
"<i class=\"fa fa-map-marker\" role=\"img\" aria-label=\"Avaa kartta\" "
"title=\"Avaa kartta\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Muokkaa"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<option>Countries...</option>"
msgstr "<option>Maat...</option>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<option>States...</option>"
msgstr "<option>Alueet...</option>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<span class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-mobile\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<span class=\"fa fa-mobile\" role=\"img\" aria-label=\"Mobiili\" title=\"Mobiili\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<span class=\"fa fa-phone\" role=\"img\" aria-label=\"Puhelin\" title=\"Puhelin\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"input-group-text o_input_group_date_icon\">\n"
"                                                        <span class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>\n"
"                                                    </span>"
msgstr ""
"<span class=\"input-group-text o_input_group_date_icon\">\n"
"                                                        <span class=\"fa fa-calendar\" role=\"img\" aria-label=\"Kalenteri\" title=\"Kalenteri\"/>\n"
"                                                    </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"input-group-text o_input_group_date_icon\">\n"
"                                                        <span class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\"/>\n"
"                                                    </span>"
msgstr ""
"<span class=\"input-group-text o_input_group_date_icon\">\n"
"                                                        <span class=\"fa fa-calendar\" role=\"img\" aria-label=\"Kalenteri\"/>\n"
"                                                    </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" invisible=\"partner_latitude &lt;= 0\">N </span>\n"
"                                    <span class=\"oe_grey\" invisible=\"partner_latitude &gt;= 0\">S </span>"
msgstr ""
"<span class=\"oe_grey\" invisible=\"partner_latitude &lt;= 0\">P </span>\n"
"                                    <span class=\"oe_grey\" invisible=\"partner_latitude &gt;= 0\">E </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" invisible=\"partner_longitude &lt;= 0\">E </span>\n"
"                                    <span class=\"oe_grey\" invisible=\"partner_longitude &gt;= 0\">W </span>\n"
"                                    <span class=\"oe_grey ps-1\">) </span>"
msgstr ""
"<span class=\"oe_grey\" invisible=\"partner_longitude &lt;= 0\">I </span>\n"
"                                    <span class=\"oe_grey\" invisible=\"partner_longitude &gt;= 0\">L </span>\n"
"                                    <span class=\"oe_grey ps-1\">) </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "<span class=\"oe_grey\">( </span>"
msgstr "<span class=\"oe_grey\">( </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_desinterested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""
"<span class=\"text-danger error_partner_assign_desinterested\" "
"style=\"display:none;\">Sinun on täytettävä seuraava toiminto ja otettava "
"yhteyttä asiakkaaseen ennen liidien hyväksymistä</span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_interested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""
"<span class=\"text-danger error_partner_assign_interested\" "
"style=\"display:none;\">Sinun on täytettävä seuraava toiminto ja otettava "
"yhteyttä asiakkaaseen ennen liidien hyväksymistä</span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<span class=\"text-muted\"> - </span>"
msgstr "<span class=\"text-muted\"> - </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "<span> at </span>"
msgstr "<span> , jossa </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-3\">Customer</strong>"
msgstr "<strong class=\"col-12 col-sm-3\">Asiakas</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Expected Closing</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Odotettu sulkeminen</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Next Activity</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Seuraava aktiviteetti</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Priority</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Prioriteetti</strong>"

#. module: website_crm_partner_assign
#: model:mail.template,body_html:website_crm_partner_assign.email_template_lead_forward_mail
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your leads</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not user.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hello,<br/>\n"
"                            We have been contacted by those prospects that are in your region. Thus, the following leads have been assigned to <t t-out=\"ctx['partner_id'].name or ''\"/>:<br/>\n"
"                            <ol>\n"
"                                <li t-foreach=\"ctx['partner_leads']\" t-as=\"lead\"><a t-att-href=\"lead['lead_link']\" t-out=\"lead['lead_id'].name or 'Subject Undefined'\">Subject Undefined</a>, <t t-out=\"lead['lead_id'].partner_name or lead['lead_id'].contact_name or 'Contact Name Undefined'\">Contact Name Undefined</t>, <t t-out=\"lead['lead_id'].country_id and lead['lead_id'].country_id.name or 'Country Undefined'\">Country Undefined</t>, <t t-out=\"lead['lead_id'].email_from or 'Email Undefined' or ''\">Email Undefined</t>, <t t-out=\"lead['lead_id'].phone or ''\">******-123-4567</t> </li><br/>\n"
"                            </ol>\n"
"                            <t t-if=\"ctx.get('partner_in_portal')\">\n"
"                                Please connect to your <a t-att-href=\"'%s?db=%s' % (object.get_base_url(), object.env.cr.dbname)\">Partner Portal</a> to get details. On each lead are two buttons on the top left corner that you should press after having contacted the lead: \"I'm interested\" &amp; \"I'm not interested\".<br/>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                You do not have yet a portal access to our database. Please contact\n"
"                                <t t-out=\"ctx['partner_id'].user_id and ctx['partner_id'].user_id.email and 'your account manager %s (%s)' % (ctx['partner_id'].user_id.name,ctx['partner_id'].user_id.email) or 'us'\">us</t>.<br/>\n"
"                            </t>\n"
"                            The lead will be sent to another partner if you do not contact the lead before 20 days.<br/><br/>\n"
"                            Thank you,<br/>\n"
"                            <t t-out=\"ctx['partner_id'].user_id and ctx['partner_id'].user_id.signature or ''\"/>\n"
"                            <br/>\n"
"                            <t t-if=\"not ctx['partner_id'].user_id\">\n"
"                                PS: It looks like you do not have an account manager assigned to you, please contact us.\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <a t-if=\"user.company_id.email\" t-att-href=\"'mailto:%s' % user.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <a t-if=\"user.company_id.website\" t-att-href=\"'%s' % user.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Sinun liidisi</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not user.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hei,<br/>\n"
"                            Meihin on otettu yhteyttä alueellanne sijaitsevista potentiaalisista asiakkaista. Näin ollen seuraavat liidit on annettu osoitteeseen <t t-out=\"ctx['partner_id'].name or ''\"/>:<br/>\n"
"                            <ol>\n"
"                                <li t-foreach=\"ctx['partner_leads']\" t-as=\"lead\"><a t-att-href=\"lead['lead_link']\" t-out=\"lead['lead_id'].name or 'Subject Undefined'\">Aihe Määrittelemätön</a>, <t t-out=\"lead['lead_id'].partner_name or lead['lead_id'].contact_name or 'Contact Name Undefined'\">Yhteyshenkilön nimi Määrittelemätön</t>, <t t-out=\"lead['lead_id'].country_id and lead['lead_id'].country_id.name or 'Country Undefined'\">Maa Määrittelemätön</t>, <t t-out=\"lead['lead_id'].email_from or 'Email Undefined' or ''\">Sähköposti Määrittelemätön</t>, <t t-out=\"lead['lead_id'].phone or ''\">******-123-4567</t> </li><br/>\n"
"                            </ol>\n"
"                           <t t-if=\"ctx.get('partner_in_portal')\">\n"
"                                Ota yhteys <a t-att-href=\"'%s?db=%s' % (object.get_base_url(), object.env.cr.dbname)\">kumppaniportaaliin</a> saadaksesi lisätietoja. Jokaisessa liidissä on vasemmassa yläkulmassa kaksi painiketta, joita sinun tulee painaa, kun olet ottanut yhteyttä liidiin: \"Olen kiinnostunut\" ja \"En ole kiinnostunut\".<br/>\n"
"                           </t>\n"
"                           <t t-else=\"\">\n"
"                                Sinulla ei ole vielä portaaliyhteyttä tietokantaamme. Ota yhteyttä\n"
"                               <t t-out=\"ctx['partner_id'].user_id and ctx['partner_id'].user_id.email and 'your account manager %s (%s)' % (ctx['partner_id'].user_id.name,ctx['partner_id'].user_id.email) or 'us'\">meihin</t>.<br/>\n"
"                           </t>\n"
"                            Liidi lähetetään toiselle kumppanille, jos et ota yhteyttä liidiin ennen kuin 20 päivää on kulunut.<br/><br/>\n"
"                            Kiitos,<br/>\n"
"                            <t t-out=\"ctx['partner_id'].user_id and ctx['partner_id'].user_id.signature or ''\"/>\n"
"                            <br/>\n"
"                           <t t-if=\"not ctx['partner_id'].user_id\">\n"
"                                PS: Näyttää siltä, että sinulle ei ole nimetty asiakaspäällikköä, ota meihin yhteyttä.\n"
"                           </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <a t-if=\"user.company_id.email\" t-att-href=\"'mailto:%s' % user.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <a t-if=\"user.company_id.website\" t-att-href=\"'%s' % user.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Voimana <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_tree
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
msgid "Activation"
msgstr "Aktivointi"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__active
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__active
msgid "Active"
msgstr "Aktiivinen"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Add an opportunity"
msgstr "Lisää myyntimahdollisuus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "Address"
msgstr "Osoite"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Address:"
msgstr "Osoite:"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "All Categories"
msgstr "Kaikki ryhmät"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "All Countries"
msgstr "Kaikki maat"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "All fields are required!"
msgstr "Kaikki kentät ovat pakollisia!"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_grade_view_search
msgid "Archived"
msgstr "Arkistoitu"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_assigned_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__partner_assigned_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Assigned Partner"
msgstr "Määritelty kumppani"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Assigned Partner:"
msgstr "Määritelty kumppani:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Automatic Assignment"
msgstr "Automaattinen määritys"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_forward_to_partner__body
msgid "Automatically sanitized HTML contents"
msgstr "Automaattisesti puhdistetut HTML-sisällöt"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "Back to resellers list"
msgstr "Takaisin jälleenmyyjien luetteloon"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_bronze
msgid "Bronze"
msgstr "Pronssi"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_partner_report_assign
msgid "CRM Partnership Analysis"
msgstr "CRM kumppanuusanalyysi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Campaign:"
msgstr "Kampanja:"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__can_publish
msgid "Can Publish"
msgstr "Voi julkaista"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Cancel"
msgstr "Peruuta"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "City"
msgstr "Kaupunki"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "Close"
msgstr "Sulje"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Communication history"
msgstr "Viestintähistoria"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Confirm"
msgstr "Vahvista"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Contact"
msgstr "Kontakti"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "Contact Name"
msgstr "Yhteystiedon nimi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Contact name"
msgstr "Yhteystiedon nimi"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__body
msgid "Contents"
msgstr "Sisältö"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__country_id
msgid "Country"
msgstr "Maa"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Create Opportunity"
msgstr "Luo mahdollisuus"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_activation_act
msgid "Create a Partner Activation"
msgstr "Luo kumppanin aktivointi"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_grade_action
msgid "Create a Partner Level"
msgstr "Luo kumppanuustaso"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__create_uid
msgid "Created by"
msgstr "Luonut"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_own_opp
msgid "Created by Partner"
msgstr "Partnerin luoma"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__create_date
msgid "Created on"
msgstr "Luotu"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Current stage of the opportunity"
msgstr "Myyntimahdollisuuden vaihe"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Customer Name"
msgstr "Asiakkaan nimi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Customer:"
msgstr "Asiakas:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL RESELLERS"
msgstr ""
"PUDOTA RAKENNUSPALIKAT TÄNNE, JOTTA NE OVAT SAATAVILLA KAIKILLA "
"JÄLLEENMYYJILLÄ"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Date"
msgstr "Päivämäärä"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Partnership"
msgstr "Kumppanuuden alkupäivä"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Review"
msgstr "Päivämäärän tarkistus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Description"
msgstr "Kuvaus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Details Next Activity"
msgstr "Seuraavan aktiviteetin yksityiskohdat"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__display_name
msgid "Display Name"
msgstr "Näyttönimi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Contact"
msgstr "Muokkaa yhteystietoa"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Opportunity"
msgstr "Muokkaa myyntimahdollisuutta"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Edit to add a short description"
msgstr "Muokkaa lisätäksesi lyhyen kuvauksen"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Email"
msgstr "Sähköposti"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Email Template"
msgstr "Sähköpostin mallipohja"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Expected"
msgstr "Odotettu"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Expected Closing:"
msgstr "Odotettu sulkeminen:"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Expected Revenue"
msgstr "Odotetut tulot"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by category"
msgstr "Suodata kategorian mukaan"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by country"
msgstr "Suodata maittain"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filters"
msgstr "Suotimet"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Find a reseller"
msgstr "Etsi jälleenmyyjä"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
msgid "Follow and convert your leads"
msgstr "Seuraa ja muunna liidit"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
msgid "Follow and convert your opportunities"
msgstr "Seuraa ja muunna mahdollisuutesi"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__partner_id
msgid "Forward Leads To"
msgstr "Välitä liidit"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__forward_type
msgid "Forward selected leads to"
msgstr "Välitä valitut liidit"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.crm_lead_forward_to_partner_act
msgid "Forward to Partner"
msgstr "Välitä kumppanille"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_crm_send_mass_forward
msgid "Forward to partner"
msgstr "Välitä kumppanille"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Future Activities"
msgstr "Tulevat toimenpiteet"

#. module: website_crm_partner_assign
#: model:mail.template,subject:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Fwd: Lead: {{ ctx['partner_id'].name }}"
msgstr "Fwd: Liidi: {{ ctx['partner_id'].name }}"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_latitude
msgid "Geo Latitude"
msgstr "Maantieteellinen leveys"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_longitude
msgid "Geo Longitude"
msgstr "Maantieteellinen pituus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Geolocation"
msgstr "Geolokaatio"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "Geolocation:"
msgstr "Paikannus:"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade__partner_weight
msgid ""
"Gives the probability to assign a lead to this partner. (0 means no "
"assignment.)"
msgstr ""
"Antaa todennäköisyyden antaa liidi tälle kumppanille. (0 tarkoittaa, että "
"liidiä ei anneta.)"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Go to reseller"
msgstr "Siirry jälleenmyyjälle"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_gold
msgid "Gold"
msgstr "Kulta"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__grade_id
msgid "Grade"
msgstr "Luokitus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Group By"
msgstr "Ryhmittely"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "I am interested by this lead."
msgstr "Olen kiinnostunut tästä liidistä."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "I am not interested by this lead. I contacted the lead."
msgstr "Tämä liidi ei kiinnosta minua. Otin yhteyttä liidiin."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "I am not interested by this lead. I have not contacted the lead."
msgstr "Tämä liidi ei kiinnosta minua. En ole ottanut yhteyttä liidiin."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "I have contacted the customer"
msgstr "Olen ottanut yhteyttä asiakkaaseen"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "I'm interested"
msgstr "Olen kiinnostunut"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "I'm not interested"
msgstr "En ole kiinnostunut"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__id
msgid "ID"
msgstr "ID"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__implemented_partner_ids
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__implemented_partner_ids
msgid "Implementation References"
msgstr "Toteutuksen viitteet"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__implemented_partner_count
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__implemented_partner_count
msgid "Implemented Partner Count"
msgstr "Toteutettu Kumppanien määrä"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__assigned_partner_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__assigned_partner_id
msgid "Implemented by"
msgstr "Toteuttanut"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date
msgid "Invoice Account Date"
msgstr "Laskun tilipäivä"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__is_published
msgid "Is Published"
msgstr "On julkaistu"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__write_uid
msgid "Last Updated by"
msgstr "Viimeksi päivittänyt"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__write_date
msgid "Last Updated on"
msgstr "Viimeksi päivitetty"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead__date_partner_assign
msgid "Last date this case was forwarded/assigned to a partner"
msgstr ""
"Viimeinen päivämäärä, jolloin tämä tapaus siirrettiin/annettiin partnerille"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Late Activities"
msgstr "Myöhässä olevat toimenpiteet"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_review
msgid "Latest Partner Review"
msgstr "Viimeisin kumppanin katselmointi"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "Lead"
msgstr "Liidi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead -"
msgstr "Liidi -"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_assignation
msgid "Lead Assignation"
msgstr "Liidin osoitus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead Feedback"
msgstr "Liidin palaute"

#. module: website_crm_partner_assign
#: model:mail.template,name:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Lead Forward: Send to partner"
msgstr "Liidin edelleenlähetys: Lähetä kumppanille"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_location
msgid "Lead Location"
msgstr "Liidin sijainti"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_forward_to_partner
msgid "Lead forward to partner"
msgstr "Liidin edelleenlähetys kumppanille"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Liidi/mahdollisuus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "Leads"
msgstr "Liidit"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "Level"
msgstr "Taso"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__name
msgid "Level Name"
msgstr "Tason nimi"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__partner_weight
msgid "Level Weight"
msgstr "Tason paino"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_link
msgid "Link to Lead"
msgstr "Linkki liidiin"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Lost"
msgstr "Hävitty"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Medium:"
msgstr "Media:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Mobile"
msgstr "Matkapuhelin"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
msgid "My Assigned Partners"
msgstr "Omat tehtäväksi saaneeet kumppanit"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__name
msgid "Name"
msgstr "Nimi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "New Opportunity"
msgstr "Uusi mahdollisuus"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Newest"
msgstr "Uusimmat"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity"
msgstr "Seuraava toimenpide"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity Date"
msgstr "Toimenpiteen määräaika"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_review_next
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_review_next
msgid "Next Partner Review"
msgstr "Seuraava kumppanin katselmointi"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "No Activities"
msgstr "Ei toimintaa"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.action_report_crm_partner_assign
msgid "No data yet!"
msgstr "Ei vielä tietoja!"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_partner_unavailable
msgid "No more partner available"
msgstr "Ei enää kumppaneita saatavilla"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "No results found for \""
msgstr "Ei hakutuloksia hakusanalla \""

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "Not allowed to update the following field(s): %s."
msgstr "Seuraavien kenttien päivittäminen ei ole sallittua: %s."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Open categories dropdown"
msgstr "Avaa luokkien pudotusvalikko"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Open countries dropdown"
msgstr "Avaa avattavat maat"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Opportunities"
msgstr "Mahdollisuudet"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_graph
msgid "Opportunities Assignment Analysis"
msgstr "Myyntimahdollisuuksien toimeksiannon analyysi"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Opportunity"
msgstr "Mahdollisuus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Opportunity -"
msgstr "Mahdollisuus -"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__partner_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner"
msgstr "Kumppani"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Activation"
msgstr "Kumppanin aktivointi"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_activation_act
#: model:ir.ui.menu,name:website_crm_partner_assign.res_partner_activation_config_mi
msgid "Partner Activations"
msgstr "Kumppanien aktivoinnit"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__forward_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__assignation_lines
msgid "Partner Assignment"
msgstr "Partnerin asetus"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__date_partner_assign
msgid "Partner Assignment Date"
msgstr "Partnerin asetuspäivä"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "Partner Assignment Date:"
msgstr "Partnerin asetuspäivä:"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_grade
msgid "Partner Grade"
msgstr "Kumppanin luokka"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__grade_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__grade_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_tree
msgid "Partner Level"
msgstr "Kumppanuustaso"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_grade_action
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_res_partner_grade_action
msgid "Partner Levels"
msgstr "Kumppanuustasot"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_grade_action
msgid ""
"Partner Levels allow you to rank your Partners based on their performances."
msgstr ""
"Kumppanitasojen avulla voit asettaa kumppanisi paremmuusjärjestykseen heidän"
" suoritustensa perusteella."

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__partner_location
msgid "Partner Location"
msgstr "Kumppanin sijainti"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Review"
msgstr "Kumppanin katselmointi/tarkastus"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner assigned Analysis"
msgstr "Kumppanille osoitettu analyysi"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_declined_ids
msgid "Partner not interested"
msgstr "Kumppani ei ole kiinnostunut"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead__partner_assigned_id
msgid "Partner this case has been forwarded/assigned to."
msgstr "Kumppani jolle tämä tapaus on välitetty/siirretty."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "Partners Page"
msgstr "Yhteistyökumppanien sivu"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_report_crm_partner_assign
msgid "Partnership Analysis"
msgstr "Kumppanuusanalyysi"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_partnership
msgid "Partnership Date"
msgstr "Kumppanuuden alkupäivämäärä"

#. module: website_crm_partner_assign
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_report_crm_partner_assign_tree
msgid "Partnerships"
msgstr "Kumppanuudet"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Phone"
msgstr "Puhelin"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Planned Revenue"
msgstr "Suunniteltu liikevaihto"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Priority:"
msgstr "Prioriteetti:"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Probability"
msgstr "Todennäköisyys"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating"
msgstr "Arvostelu"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Rating: #{lead.priority} on 3"
msgstr "Luokitus: #{lead.priority} kolmessa"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: #{opportunity.priority} on 4"
msgstr "Luokitus: #{opportunity.priority} neljässä"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 0 on 3"
msgstr "Luokitus: 0 / 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 1 on 3"
msgstr "Luokitus: 1 / 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 2 on 3"
msgstr "Arvosana: 2 / 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 3 on 3"
msgstr "Luokitus: 3 / 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.references_block
msgid "References"
msgstr "Viitteet"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/website.py:0
#: model:ir.ui.menu,name:website_crm_partner_assign.crm_menu_resellers
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
msgid "Resellers"
msgstr "Jälleenmyyjät"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Sales Team:"
msgstr "Myyntitiimi:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Salesperson"
msgstr "Myyjä"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Salesperson:"
msgstr "Myyjä:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Search"
msgstr "Hae"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_grade_view_search
msgid "Search Partner Grade"
msgstr "Hae kumppanin luokka"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "See all resellers"
msgstr "Katso kaikki jälleenmyyjät"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "See categories filters"
msgstr "Katso luokkien suodattimet"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "See countries filters"
msgstr "Katso maasuodattimet"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send"
msgstr "Lähetä"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Send Email"
msgstr "Lähetä sähköposti"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send Mail"
msgstr "Lähetä sähköposti"

#. module: website_crm_partner_assign
#: model:mail.template,description:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Sent to partner when a lead has been assigned to him"
msgstr "Lähetetään yhteistyökumppanille, kun hänelle on annettu liidi"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__grade_sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__grade_sequence
msgid "Sequence"
msgstr "Järjestys"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
msgid "Set an email address for the partner %s"
msgstr "Aseta sähköposti kumppanille %s"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
msgid "Set an email address for the partner(s): %s"
msgstr "Aseta sähköposti kumppaneille %s"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "Show Leads / Opps"
msgstr "Näytä liidit tai myyntimahdollisuudet"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_silver
msgid "Silver"
msgstr "Hopea"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_is_spam
msgid "Spam"
msgstr "Roskaposti"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Stage"
msgstr "Vaihe"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Stage:"
msgstr "Vaihe:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street"
msgstr "Katuosoite"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street2"
msgstr "Katuosoite2"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Tags"
msgstr "Tunnisteet"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
msgid "The Forward Email Template is not in the database"
msgstr "Sähköpostin edelleenlähetysmallia ei ole tietokannassa"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade__website_url
msgid "The full URL to access the document through the website."
msgstr "Dokumentin URL-osoite verkkosivustolla."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "There are no leads."
msgstr "Ei liidejä."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "There are no opportunities."
msgstr "Ei myyntimahdollisuuksia."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "There is no country set in addresses for %(lead_names)s."
msgstr "Osoitteissa %(lead_names)s ei ole määritetty maata."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "This lead is a spam"
msgstr "Tämä liidi on roskapostia"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner__partner_weight
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_users__partner_weight
msgid ""
"This should be a numerical value greater than 0 which will decide the "
"contention for this partner to take this lead/opportunity."
msgstr ""
"Tämän tulisi olla numeroarvo, joka on suurempi kuin 0 ja joka ratkaisee, "
"onko tämä kumppani oikeutettu ottamaan tämän liidin tai "
"myyntimahdollisuuden."

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_activation_act
msgid ""
"Those are used to know where your Partners stand in your onboarding process."
msgstr ""
"Niiden avulla tiedät, missä vaiheessa kumppanisi ovat "
"sisäänottoprosessissasi."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Today Activities"
msgstr "Tämän päivän toimenpiteet"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__turnover
msgid "Turnover"
msgstr "Liikevaihto"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__user_id
msgid "User"
msgstr "Käyttäjä"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__website_published
msgid "Visible on current website"
msgstr "Näkyy nykysellä verkkosivulla"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "Warning"
msgstr "Varoitus"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_website
msgid "Website"
msgstr "Verkkosivu"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__website_url
msgid "Website URL"
msgstr "Verkkosivuston osoite"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "What is the next action? When? What is the expected revenue?"
msgstr ""
"Mikä on seuraava toimenpide? Milloin? Mikä on odotettavissa oleva tulo?"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Why aren't you interested in this lead?"
msgstr "Miksi et ole kiinnostunut tästä liidistä?"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Won"
msgstr "Voitettu"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "World Map"
msgstr "Maailman kartta"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "ZIP"
msgstr "Postinumero"

#. module: website_crm_partner_assign
#: model:ir.model.fields.selection,name:website_crm_partner_assign.selection__crm_lead_forward_to_partner__forward_type__single
msgid "a single partner: manual selection of partner"
msgstr "yksi kumppani: kumppanin manuaalinen valinta"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "at"
msgstr "jossa"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "e.g. Gold Partner"
msgstr "esim. Gold Partner"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "in"
msgstr "tuumaa"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "latitude,"
msgstr "leveysaste,"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "longitude"
msgstr "pituusaste"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "on"
msgstr "on"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "reference"
msgstr "viite"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "references"
msgstr "referenssit"

#. module: website_crm_partner_assign
#: model:ir.model.fields.selection,name:website_crm_partner_assign.selection__crm_lead_forward_to_partner__forward_type__assigned
msgid ""
"several partners: automatic assignment, using GPS coordinates and partner's "
"grades"
msgstr ""
"useita kumppaneita: automaattinen määritys GPS-koordinaattien ja kumppanin "
"arvosanojen perusteella"
