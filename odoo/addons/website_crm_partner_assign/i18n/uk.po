# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_crm_partner_assign
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-16 13:41+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__nbr_opportunities
msgid "# of Opportunity"
msgstr "К-сть нагод"

#. module: website_crm_partner_assign
#. odoo-javascript
#: code:addons/website_crm_partner_assign/static/src/js/crm_partner_assign.js:0
msgid "%s's Opportunity"
msgstr "Нагода %s"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_view_kanban
msgid ""
"(<i class=\"fa fa-long-arrow-right me-1\" aria-label=\"Assigned Partner\" "
"title=\"Assigned Partner\"/>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "<i class=\"fa fa-chevron-left me-2\"/>Back to resellers"
msgstr "<i class=\"fa fa-chevron-left me-2\"/>Повернутися до партнерів"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid ""
"<i class=\"fa fa-info-circle me-2\"/>\n"
"                There are no matching partners found for the selected country. Displaying results across all countries instead."
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid ""
"<i class=\"fa fa-map-marker\" role=\"img\" aria-label=\"Open map\" "
"title=\"Open map\"/>"
msgstr ""
"<i class=\"fa fa-map-marker\" role=\"img\" aria-label=\"Open map\" "
"title=\"Open map\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<i class=\"fa fa-pencil me-1\"/>Edit"
msgstr "<i class=\"fa fa-pencil me-1\"/>Редагувати"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<option>Countries...</option>"
msgstr "<option>Країни...</option>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<option>States...</option>"
msgstr "<option>Області...</option>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"
msgstr "<span class=\"fa fa-envelope\" role=\"img\" aria-label=\"Email\" title=\"Email\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-mobile\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<span class=\"fa fa-mobile\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "<span class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"
msgstr "<span class=\"fa fa-phone\" role=\"img\" aria-label=\"Phone\" title=\"Phone\"/>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"input-group-text o_input_group_date_icon\">\n"
"                                                        <span class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\" title=\"Calendar\"/>\n"
"                                                    </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid ""
"<span class=\"input-group-text o_input_group_date_icon\">\n"
"                                                        <span class=\"fa fa-calendar\" role=\"img\" aria-label=\"Calendar\"/>\n"
"                                                    </span>"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" invisible=\"partner_latitude &lt;= 0\">N </span>\n"
"                                    <span class=\"oe_grey\" invisible=\"partner_latitude &gt;= 0\">S </span>"
msgstr ""
"<span class=\"oe_grey\" invisible=\"partner_latitude &lt;= 0\">П </span>\n"
"                                    <span class=\"oe_grey\" invisible=\"partner_latitude &gt;= 0\">З </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid ""
"<span class=\"oe_grey\" invisible=\"partner_longitude &lt;= 0\">E </span>\n"
"                                    <span class=\"oe_grey\" invisible=\"partner_longitude &gt;= 0\">W </span>\n"
"                                    <span class=\"oe_grey ps-1\">) </span>"
msgstr ""
"<span class=\"oe_grey\" invisible=\"partner_longitude &lt;= 0\">E </span>\n"
"                                    <span class=\"oe_grey\" invisible=\"partner_longitude &gt;= 0\">W </span>\n"
"                                    <span class=\"oe_grey ps-1\">) </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "<span class=\"oe_grey\">( </span>"
msgstr "<span class=\"oe_grey\">( </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_desinterested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""
"<span class=\"text-danger error_partner_assign_desinterested\" "
"style=\"display:none;\">Потрібно заповнити наступну дію та зв'язатися з "
"клієнтом, перш ніж прийняти лід</span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid ""
"<span class=\"text-danger error_partner_assign_interested\" "
"style=\"display:none;\">You need to fill up the next action and contact the "
"customer before accepting the lead</span>"
msgstr ""
"<span class=\"text-danger error_partner_assign_interested\" "
"style=\"display:none;\">Потрібно заповнити наступну дію та зв'язатися з "
"клієнтом, перш ніж прийняти лід</span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<span class=\"text-muted\"> - </span>"
msgstr "<span class=\"text-muted\"> - </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "<span> at </span>"
msgstr "<span> в </span>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-3\">Customer</strong>"
msgstr "<strong class=\"col-12 col-sm-3\">Клієнт</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Expected Closing</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Очікуване закриття</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Next Activity</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Наступна дія</strong>"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "<strong class=\"col-12 col-sm-4\">Priority</strong>"
msgstr "<strong class=\"col-12 col-sm-4\">Пріоритет</strong>"

#. module: website_crm_partner_assign
#: model:mail.template,body_html:website_crm_partner_assign.email_template_lead_forward_mail
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <span style=\"font-size: 10px;\">Your leads</span><br/>\n"
"                </td><td valign=\"middle\" align=\"right\" t-if=\"not user.company_id.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ user.company_id.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"user.company_id.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                  <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr>\n"
"                    <td valign=\"top\" style=\"font-size: 13px;\">\n"
"                        <div>\n"
"                            Hello,<br/>\n"
"                            We have been contacted by those prospects that are in your region. Thus, the following leads have been assigned to <t t-out=\"ctx['partner_id'].name or ''\"/>:<br/>\n"
"                            <ol>\n"
"                                <li t-foreach=\"ctx['partner_leads']\" t-as=\"lead\"><a t-att-href=\"lead['lead_link']\" t-out=\"lead['lead_id'].name or 'Subject Undefined'\">Subject Undefined</a>, <t t-out=\"lead['lead_id'].partner_name or lead['lead_id'].contact_name or 'Contact Name Undefined'\">Contact Name Undefined</t>, <t t-out=\"lead['lead_id'].country_id and lead['lead_id'].country_id.name or 'Country Undefined'\">Country Undefined</t>, <t t-out=\"lead['lead_id'].email_from or 'Email Undefined' or ''\">Email Undefined</t>, <t t-out=\"lead['lead_id'].phone or ''\">******-123-4567</t> </li><br/>\n"
"                            </ol>\n"
"                            <t t-if=\"ctx.get('partner_in_portal')\">\n"
"                                Please connect to your <a t-att-href=\"'%s?db=%s' % (object.get_base_url(), object.env.cr.dbname)\">Partner Portal</a> to get details. On each lead are two buttons on the top left corner that you should press after having contacted the lead: \"I'm interested\" &amp; \"I'm not interested\".<br/>\n"
"                            </t>\n"
"                            <t t-else=\"\">\n"
"                                You do not have yet a portal access to our database. Please contact\n"
"                                <t t-out=\"ctx['partner_id'].user_id and ctx['partner_id'].user_id.email and 'your account manager %s (%s)' % (ctx['partner_id'].user_id.name,ctx['partner_id'].user_id.email) or 'us'\">us</t>.<br/>\n"
"                            </t>\n"
"                            The lead will be sent to another partner if you do not contact the lead before 20 days.<br/><br/>\n"
"                            Thank you,<br/>\n"
"                            <t t-out=\"ctx['partner_id'].user_id and ctx['partner_id'].user_id.signature or ''\"/>\n"
"                            <br/>\n"
"                            <t t-if=\"not ctx['partner_id'].user_id\">\n"
"                                PS: It looks like you do not have an account manager assigned to you, please contact us.\n"
"                            </t>\n"
"                        </div>\n"
"                    </td>\n"
"                </tr>\n"
"                <tr>\n"
"                    <td style=\"text-align:center;\">\n"
"                        <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                    </td>\n"
"                </tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"user.company_id.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-out=\"user.company_id.phone or ''\">******-123-4567</t>\n"
"                    <t t-if=\"user.company_id.phone and (user.company_id.email or user.company_id.website)\">|</t>\n"
"                    <a t-if=\"user.company_id.email\" t-att-href=\"'mailto:%s' % user.company_id.email\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.email or ''\"><EMAIL></a>\n"
"                    <t t-if=\"user.company_id.email and user.company_id.website\">|</t>\n"
"                    <a t-if=\"user.company_id.website\" t-att-href=\"'%s' % user.company_id.website\" style=\"text-decoration:none; color: #454748;\" t-out=\"user.company_id.website or ''\">http://www.example.com</a>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=website\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__activation
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_tree
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
msgid "Activation"
msgstr "Активація"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__active
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__active
msgid "Active"
msgstr "Активно"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Add an opportunity"
msgstr "Додати нагоду"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "Address"
msgstr "Адреса"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Address:"
msgstr "Адреса:"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "All Categories"
msgstr "Всі категорії"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "All Countries"
msgstr "Всі країни"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "All fields are required!"
msgstr "Усі поля обовʼязкові!"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_grade_view_search
msgid "Archived"
msgstr "Заархівовано"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_assigned_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__partner_assigned_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Assigned Partner"
msgstr "Призначений партнер"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Assigned Partner:"
msgstr "Призначений партнер:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Automatic Assignment"
msgstr "Автоматичне призначення"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead_forward_to_partner__body
msgid "Automatically sanitized HTML contents"
msgstr "Автоматичне очищення вмісту HTML"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.partner
msgid "Back to resellers list"
msgstr "Повернутися до списку партнерів"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_bronze
msgid "Bronze"
msgstr "Бронзовий"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_partner_report_assign
msgid "CRM Partnership Analysis"
msgstr "Аналіз партнерства CRM"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Campaign:"
msgstr "Кампанія:"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__can_publish
msgid "Can Publish"
msgstr "Можна опублікувати"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Cancel"
msgstr "Скасувати"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "City"
msgstr "Місто"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
msgid "Close"
msgstr "Закрити"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Communication history"
msgstr "Історія комунікації"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Confirm"
msgstr "Підтвердити"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Contact"
msgstr "Контакт"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "Contact Name"
msgstr "Назва контакту"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Contact name"
msgstr "Контактне ім'я"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__body
msgid "Contents"
msgstr "Містить"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__country_id
msgid "Country"
msgstr "Країна"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Create Opportunity"
msgstr "Створити нагоду"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_activation_act
msgid "Create a Partner Activation"
msgstr "Створити активацію партнера"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_grade_action
msgid "Create a Partner Level"
msgstr "Створити рівень партнера"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__create_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__create_uid
msgid "Created by"
msgstr "Створив"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_own_opp
msgid "Created by Partner"
msgstr "Створено партнером"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__create_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__create_date
msgid "Created on"
msgstr "Створено"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Current stage of the opportunity"
msgstr "Поточний етап нагоди"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Customer Name"
msgstr "Ім'я клієнта"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Customer:"
msgstr "Клієнт:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
msgid "DROP BUILDING BLOCKS HERE TO MAKE THEM AVAILABLE ACROSS ALL RESELLERS"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Date"
msgstr "Дата"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Partnership"
msgstr "Дата партнерства"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Date Review"
msgstr "Дата перегляду"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Description"
msgstr "Опис"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Details Next Activity"
msgstr "Деталі наступної дії"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__display_name
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Contact"
msgstr "Редагувати контакт"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Edit Opportunity"
msgstr "Редагувати нагоду"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Edit to add a short description"
msgstr "Редагуйте для додавання короткого опису"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Email"
msgstr "Ел. пошта"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Email Template"
msgstr "Шаблон ел. листа"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Expected"
msgstr "Очікуються"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Expected Closing:"
msgstr "Очікуване закриття:"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Expected Revenue"
msgstr "Очікуваний дохід"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by category"
msgstr "Фільтр за категоріями"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filter by country"
msgstr "Фільтр за країнами"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Filters"
msgstr "Фільтри"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Find a reseller"
msgstr "Знайти партнера"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
msgid "Follow and convert your leads"
msgstr "Слідкуйте за вашими лідами та конвертуйте їх"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
msgid "Follow and convert your opportunities"
msgstr "Слідкуйте за вашими нагодами та конвертуйте їх"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__partner_id
msgid "Forward Leads To"
msgstr "Перемістити ліди до"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__forward_type
msgid "Forward selected leads to"
msgstr "Перемістити обрані ліди до"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.crm_lead_forward_to_partner_act
msgid "Forward to Partner"
msgstr "Перемістити партнеру"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_crm_send_mass_forward
msgid "Forward to partner"
msgstr "Перемістити партнеру"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Future Activities"
msgstr "Майбутні дії"

#. module: website_crm_partner_assign
#: model:mail.template,subject:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Fwd: Lead: {{ ctx['partner_id'].name }}"
msgstr "Fwd: Lead: {{ ctx['partner_id'].name }}"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_latitude
msgid "Geo Latitude"
msgstr "Широта"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_longitude
msgid "Geo Longitude"
msgstr "Довгота"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Geolocation"
msgstr "Геолокація"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "Geolocation:"
msgstr "Геолокація:"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade__partner_weight
msgid ""
"Gives the probability to assign a lead to this partner. (0 means no "
"assignment.)"
msgstr ""
"Дає ймовірність призначити ліда цьому партнеру. (0 означає відсутність "
"призначення.)"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Go to reseller"
msgstr "Перейти до партнера"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_gold
msgid "Gold"
msgstr "Золотий"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__grade_id
msgid "Grade"
msgstr "Оцінка"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Group By"
msgstr "Групувати за"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "I am interested by this lead."
msgstr "Я зацікавлений у цьому ліді."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "I am not interested by this lead. I contacted the lead."
msgstr "Я не зацікавлений цим лідом. Я зв'язався з лідом."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "I am not interested by this lead. I have not contacted the lead."
msgstr "Я не зацікавлений у цьому ліді. Я не зв'язувався з лідом."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "I have contacted the customer"
msgstr "Я зв'язався з клієнтом"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "I'm interested"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "I'm not interested"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__id
msgid "ID"
msgstr "ID"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__implemented_partner_ids
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__implemented_partner_ids
msgid "Implementation References"
msgstr "Посилання на впровадження"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__implemented_partner_count
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__implemented_partner_count
msgid "Implemented Partner Count"
msgstr "Підрахунок реалізованих партнерів"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__assigned_partner_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__assigned_partner_id
msgid "Implemented by"
msgstr "Впроваджено"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date
msgid "Invoice Account Date"
msgstr "Дата виставлення рахунку"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__is_published
msgid "Is Published"
msgstr "Опубліковано"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__write_uid
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__write_date
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead__date_partner_assign
msgid "Last date this case was forwarded/assigned to a partner"
msgstr "Остання дата цієї справи була переслана/призначена партнеру"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Late Activities"
msgstr "Останні дії"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_review
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_review
msgid "Latest Partner Review"
msgstr "Останній огляд партнера"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "Lead"
msgstr "Лід"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead -"
msgstr "Лід -"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_assignation
msgid "Lead Assignation"
msgstr "Призначення ліда"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Lead Feedback"
msgstr "Зворотній зв'язок ліда"

#. module: website_crm_partner_assign
#: model:mail.template,name:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Lead Forward: Send to partner"
msgstr "Направити лід: Надіслати партнеру"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_location
msgid "Lead Location"
msgstr "Місцезнаходження ліда"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead_forward_to_partner
msgid "Lead forward to partner"
msgstr "Лід належить партнеру"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Лід/Нагода"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "Leads"
msgstr "Ліди"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "Level"
msgstr "Рівень"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__name
msgid "Level Name"
msgstr "Назва рівня"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__partner_weight
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__partner_weight
msgid "Level Weight"
msgstr "Вага рівня"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__lead_link
msgid "Link to Lead"
msgstr "Посилання на лід"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Lost"
msgstr "Втрачено"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Medium:"
msgstr "Канал:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Mobile"
msgstr "Мобільний"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_partner_filter
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_opportunity_partner_filter
msgid "My Assigned Partners"
msgstr "Мої призначені партнери"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__name
msgid "Name"
msgstr "Назва"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "New Opportunity"
msgstr "Нова нагода"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Newest"
msgstr "Найновіші"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity"
msgstr "Наступна дія"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Next Activity Date"
msgstr "Дата наступної дії"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_review_next
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_review_next
msgid "Next Partner Review"
msgstr "Наступний огляд партнера"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "No Activities"
msgstr ""

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.action_report_crm_partner_assign
msgid "No data yet!"
msgstr "Ще немає даних!"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_partner_unavailable
msgid "No more partner available"
msgstr "Більше немає доступних партнерів"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "No results found for \""
msgstr "Не знайдено результатів для \""

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "Not allowed to update the following field(s): %s."
msgstr "Не дозволено оновлювати наступні поля: %s."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Open categories dropdown"
msgstr "Відкрити спадаючий список категорій"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Open countries dropdown"
msgstr "Відкрити спадаючий список країн"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_home_menu_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Opportunities"
msgstr "Нагоди"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_graph
msgid "Opportunities Assignment Analysis"
msgstr "Аналіз залежності нагод"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Opportunity"
msgstr "Нагода"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Opportunity -"
msgstr "Нагода -"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__partner_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner"
msgstr "Партнер"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_activation
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_activation_view_search
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Activation"
msgstr "Активація партнера"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_activation_act
#: model:ir.ui.menu,name:website_crm_partner_assign.res_partner_activation_config_mi
msgid "Partner Activations"
msgstr "Активації партнера"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__forward_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_forward_to_partner__assignation_lines
msgid "Partner Assignment"
msgstr "Призначення партнера"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__date_partner_assign
msgid "Partner Assignment Date"
msgstr "Дата призначення партнера"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "Partner Assignment Date:"
msgstr "Дата призначення партнера:"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_res_partner_grade
msgid "Partner Grade"
msgstr "Оцінка партнера"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__grade_id
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__grade_id
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_tree
msgid "Partner Level"
msgstr "Рівень партнера"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.res_partner_grade_action
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_res_partner_grade_action
msgid "Partner Levels"
msgstr "Рівні партнера"

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_grade_action
msgid ""
"Partner Levels allow you to rank your Partners based on their performances."
msgstr ""
"Рівні партнерів дозволяють оцінювати ваших партнерів на основі їхніх "
"результатів."

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead_assignation__partner_location
msgid "Partner Location"
msgstr "Місцезнаходження партнера"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_partner_assign_form
msgid "Partner Review"
msgstr "Огляд партнера"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Partner assigned Analysis"
msgstr "Призначений аналіз партнера"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_lead__partner_declined_ids
msgid "Partner not interested"
msgstr "Партнер не зацікавлений"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_crm_lead__partner_assigned_id
msgid "Partner this case has been forwarded/assigned to."
msgstr "Партнер цієї справи був переадресований/призначений."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "Partners Page"
msgstr "Партнерська сторінка"

#. module: website_crm_partner_assign
#: model:ir.actions.act_window,name:website_crm_partner_assign.action_report_crm_partner_assign
msgid "Partnership Analysis"
msgstr "Аналіз партнерства"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__date_partnership
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__date_partnership
msgid "Partnership Date"
msgstr "Дата партнерства"

#. module: website_crm_partner_assign
#: model:ir.ui.menu,name:website_crm_partner_assign.menu_report_crm_partner_assign_tree
msgid "Partnerships"
msgstr "Партнерства"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Phone"
msgstr "Телефон"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Planned Revenue"
msgstr "Запланований дохід"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Priority:"
msgstr "Пріоритет:"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Probability"
msgstr "Ймовірність"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating"
msgstr "Оцінка"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Rating: #{lead.priority} on 3"
msgstr "Оцінка: #{lead.priority} на 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: #{opportunity.priority} on 4"
msgstr "Оцінка: #{opportunity.priority} на 4"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 0 on 3"
msgstr "Оцінка: 0 на 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 1 on 3"
msgstr "Оцінка: 1 на 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 2 on 3"
msgstr "Оцінка: 2 на 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Rating: 3 on 3"
msgstr "Оцінка: 3 на 3"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.references_block
msgid "References"
msgstr "Референси"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/website.py:0
#: model:ir.ui.menu,name:website_crm_partner_assign.crm_menu_resellers
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.layout
msgid "Resellers"
msgstr "Партнери"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Sales Team:"
msgstr "Команда продажу:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_report_crm_partner_assign_filter
msgid "Salesperson"
msgstr "Продавець"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Salesperson:"
msgstr "Продавець:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "Search"
msgstr "Пошук"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.res_partner_grade_view_search
msgid "Search Partner Grade"
msgstr "Пошук рівня пратнера"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "See all resellers"
msgstr "Переглянути всіх партнерів"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "See categories filters"
msgstr "Переглянути фільтри категорій"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "See countries filters"
msgstr "Переглянути фільтри країн"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send"
msgstr "Надіслати"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_crm_lead_opportunity_geo_assign_form
msgid "Send Email"
msgstr "Надіслати ел. листа"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_forward_to_partner_form
msgid "Send Mail"
msgstr "Надіслати листа"

#. module: website_crm_partner_assign
#: model:mail.template,description:website_crm_partner_assign.email_template_lead_forward_mail
msgid "Sent to partner when a lead has been assigned to him"
msgstr "Надсилається партнеру після призначення його на лід"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner__grade_sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_activation__sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__sequence
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_users__grade_sequence
msgid "Sequence"
msgstr "Послідовність"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
msgid "Set an email address for the partner %s"
msgstr "Встановіть електронну адресу для партнера %s"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
msgid "Set an email address for the partner(s): %s"
msgstr "Встановіть електронну адресу для партнера(ів): %s"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "Show Leads / Opps"
msgstr "Показати ліди / нагоди"

#. module: website_crm_partner_assign
#: model:res.partner.grade,name:website_crm_partner_assign.res_partner_grade_data_silver
msgid "Silver"
msgstr "Срібний"

#. module: website_crm_partner_assign
#: model:crm.tag,name:website_crm_partner_assign.tag_portal_lead_is_spam
msgid "Spam"
msgstr "Спам"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "Stage"
msgstr "Етап"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Stage:"
msgstr "Етап:"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street"
msgstr "Вулиця"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "Street2"
msgstr "Вулиця 2"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Tags"
msgstr "Мітки"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/wizard/crm_forward_to_partner.py:0
msgid "The Forward Email Template is not in the database"
msgstr "Шаблону для переадресації електронної пошти немає в базі даних"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner_grade__website_url
msgid "The full URL to access the document through the website."
msgstr "URL-адреса повністю, для доступу до документації через сайт."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_leads
msgid "There are no leads."
msgstr "Немає лідів."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunities
msgid "There are no opportunities."
msgstr "Немає нагод."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "There is no country set in addresses for %(lead_names)s."
msgstr "Не вказано жодної країни в адресі на %(lead_names)s."

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "This lead is a spam"
msgstr "Цей лід - спам"

#. module: website_crm_partner_assign
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_partner__partner_weight
#: model:ir.model.fields,help:website_crm_partner_assign.field_res_users__partner_weight
msgid ""
"This should be a numerical value greater than 0 which will decide the "
"contention for this partner to take this lead/opportunity."
msgstr ""
"Це має бути числове значення, що перевищує 0, що визначатиме виклик для "
"цього партнера для отримання ліда/нагоди."

#. module: website_crm_partner_assign
#: model_terms:ir.actions.act_window,help:website_crm_partner_assign.res_partner_activation_act
msgid ""
"Those are used to know where your Partners stand in your onboarding process."
msgstr ""
"Вони використовуються для того, щоб знати, на якій позиції знаходяться ваші "
"партнери в процесі адаптації."

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Today Activities"
msgstr "Сьогоднішні дії"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__turnover
msgid "Turnover"
msgstr "Оборот"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_crm_partner_report_assign__user_id
msgid "User"
msgstr "Користувач"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__website_published
msgid "Visible on current website"
msgstr "Видимий на поточному веб-сайті"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/models/crm_lead.py:0
msgid "Warning"
msgstr "Попередження"

#. module: website_crm_partner_assign
#: model:ir.model,name:website_crm_partner_assign.model_website
msgid "Website"
msgstr "Веб-сайт"

#. module: website_crm_partner_assign
#: model:ir.model.fields,field_description:website_crm_partner_assign.field_res_partner_grade__website_url
msgid "Website URL"
msgstr "URL сайту"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "What is the next action? When? What is the expected revenue?"
msgstr "Яка наступна дія? Коли? Який очікуваний дохід?"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_lead
msgid "Why aren't you interested in this lead?"
msgstr "Чому ви не зацікавлені у цьому ліді?"

#. module: website_crm_partner_assign
#. odoo-python
#: code:addons/website_crm_partner_assign/controllers/main.py:0
msgid "Won"
msgstr "Впіймано"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.ref_country
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.snippet_options
msgid "World Map"
msgstr "Мапа світу"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "ZIP"
msgstr "Індекс"

#. module: website_crm_partner_assign
#: model:ir.model.fields.selection,name:website_crm_partner_assign.selection__crm_lead_forward_to_partner__forward_type__single
msgid "a single partner: manual selection of partner"
msgstr "єдиний партнер: ручний вибір партнера"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "at"
msgstr "за"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.view_partner_grade_form
msgid "e.g. Gold Partner"
msgstr "наприклад, золотий партнер"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "in"
msgstr "в"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "latitude,"
msgstr "широта,"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.crm_lead_merge_summary_inherit_partner_assign
msgid "longitude"
msgstr "довгота"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.portal_my_opportunity
msgid "on"
msgstr "на"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "reference"
msgstr "посилання"

#. module: website_crm_partner_assign
#: model_terms:ir.ui.view,arch_db:website_crm_partner_assign.index
msgid "references"
msgstr ""

#. module: website_crm_partner_assign
#: model:ir.model.fields.selection,name:website_crm_partner_assign.selection__crm_lead_forward_to_partner__forward_type__assigned
msgid ""
"several partners: automatic assignment, using GPS coordinates and partner's "
"grades"
msgstr ""
"кілька партнерів: автоматичне призначення, використовуючи GPS-координати та "
"рівень партнера"
