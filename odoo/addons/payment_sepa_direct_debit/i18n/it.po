# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sepa_direct_debit
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Marianna <PERSON>, 2025\n"
"Language-Team: Italian (https://app.transifex.com/odoo/teams/41243/it/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: it\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.sepa_state_header
msgid "<strong>Amount: </strong>"
msgstr "<strong>Importo: </strong>"

#. module: payment_sepa_direct_debit
#: model:mail.template,body_html:payment_sepa_direct_debit.mail_template_sepa_notify_validation
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Your SEPA Direct Debit Mandate</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.partner_bank_id.acc_number or ''\">BE15001559627230</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"\n"
"                    <p>A SEPA Direct Debit Mandate authorization has just been validated for use with <t t-out=\"company.name or ''\">YourCompany</t>.</p>\n"
"\n"
"                    <p>You will find a copy of the mandate validation attached to this email.</p>\n"
"\n"
"                    <p>Do not hesitate to contact us if you have any question.</p>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"/>\n"
"                    <span style=\"font-size: 10px;\">Autorizzazione addebito diretto SEPA</span><br/>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.partner_bank_id.acc_number or ''\">BE15001559627230</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\"/>\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Ciao <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br/><br/>\n"
"\n"
"                    <p>è stata appena concessa l'autorizzazione per un addebito diretto SEPA da utilizzare con <t t-out=\"company.name or ''\">YourCompany</t>.</p>\n"
"\n"
"                    <p>In allegato, trovi una copia della convalida dell'autorizzazione.</p>\n"
"\n"
"                    <p>Non esitare a contattarci se hai domande.</p>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\"/>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_account_bank_statement_line
msgid "Bank Statement Line"
msgstr "Riga estratto conto bancario"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_res_partner
msgid "Contact"
msgstr "Contatto"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_payment_provider__custom_mode
msgid "Custom Mode"
msgstr "Modalità personalizzata"

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "IBAN"
msgstr "IBAN"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_payment_transaction__mandate_id
msgid "Mandate"
msgstr "Mandato"

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
msgid "Missing or invalid IBAN."
msgstr "IBAN mancante o non valido."

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr "Nessuna transazione trovata corrispondente al riferimento %s."

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_account_payment_method
msgid "Payment Methods"
msgstr "Metodi di pagamento"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_payment_provider
msgid "Payment Provider"
msgstr "Fornitore di pagamenti"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_payment_token
msgid "Payment Token"
msgstr "Token di pagamento"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transazione di pagamento"

#. module: payment_sepa_direct_debit
#. odoo-javascript
#: code:addons/payment_sepa_direct_debit/static/src/js/payment_form.js:0
msgid "Payment processing failed"
msgstr "Elaborazione del pagamento non riuscita"

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_provider.py:0
msgid "Restricted to countries in the SEPA zone. Forbidden countries: %s"
msgstr "Limitato ai paesi della zona SEPA. Paesi vietati: %s"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_sdd_mandate
msgid "SDD Mandate"
msgstr "Mandato SDD"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields.selection,name:payment_sepa_direct_debit.selection__payment_provider__custom_mode__sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr "Addebito Diretto SEPA"

#. module: payment_sepa_direct_debit
#: model:account.payment.method,name:payment_sepa_direct_debit.payment_method_sepa_direct_debit
msgid "SEPA Direct Debit (provider)"
msgstr "Addebito diretto SEPA (fornitore)"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_payment_token__sdd_mandate_id
msgid "SEPA Direct Debit Mandate"
msgstr "Mandato di addebito diretto SEPA"

#. module: payment_sepa_direct_debit
#: model:ir.actions.server,name:payment_sepa_direct_debit.cron_auto_confirm_paid_sepa_txs_ir_actions_server
msgid "SEPA Direct Debit: Confirm paid transactions"
msgstr "Addebito diretto SEPA: conferma transazioni pagate"

#. module: payment_sepa_direct_debit
#: model:mail.template,name:payment_sepa_direct_debit.mail_template_sepa_notify_validation
msgid "SEPA Direct Debit: Validation Notification"
msgstr "Addebito diretto SEPA: Notifica di convalida"

#. module: payment_sepa_direct_debit
#: model:mail.template,description:payment_sepa_direct_debit.mail_template_sepa_notify_validation
msgid ""
"Send the SEPA mandate in attachement, to partners who signed a new mandate"
msgstr ""
"E-mail per i partner che hanno firmato una nuova autorizzazione con "
"l'autorizzazione SEPA in allegato"

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_provider.py:0
msgid "The bank account of the journal is not a valid IBAN."
msgstr "Il conto bancario del giornale non è un IBAN valido."

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_transaction.py:0
msgid "The mandate is invalid."
msgstr "Il mandato non è valido."

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_provider.py:0
msgid "The mandate owner and customer do not match."
msgstr "Il proprietario del mandato e il cliente non corrispondono."

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_transaction.py:0
msgid "The token is not linked to a mandate."
msgstr "Il token non è legato a un mandato."

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr "La transazione non è legata a un token."

#. module: payment_sepa_direct_debit
#: model:mail.template,subject:payment_sepa_direct_debit.mail_template_sepa_notify_validation
msgid "Your SEPA Direct Debit Mandate with {{ object.company_id.name }}"
msgstr "Mandato di addebito diretto SEPA con {{ object.company_id.name }}"

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_provider.py:0
msgid ""
"Your company must have a creditor identifier in order to issue a SEPA Direct"
" Debit payment request. It can be set in Accounting settings."
msgstr ""
"Per emettere una richiesta di pagamento con addebito diretto SEPA la tua "
"azienda deve possedere un Codice Identificativo del Creditore. È possibile "
"configurarlo nelle impostazioni relative al modulo Contabilità."
