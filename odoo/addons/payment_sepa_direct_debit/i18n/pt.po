# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* payment_sepa_direct_debit
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON>, 2022
# <PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON><PERSON> <<EMAIL>>, 2022
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 16:03+0000\n"
"PO-Revision-Date: 2022-09-22 05:47+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2022\n"
"Language-Team: Portuguese (https://app.transifex.com/odoo/teams/41243/pt/)\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "+32123456789"
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.sdd_payment_mandate_form
msgid "<strong>Authenticated by SMS by:</strong>"
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.sdd_payment_mandate_form
msgid "<strong>Date and place of signature:</strong>"
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.sdd_payment_mandate_form
msgid ""
"<strong>Name of the reference party:</strong>\n"
"                            ......................................"
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.sdd_payment_mandate_form
msgid "<strong>Signature:</strong>"
msgstr "<strong>Assinatura: </strong>"

#. module: payment_sepa_direct_debit
#: model:mail.template,body_html:payment_sepa_direct_debit.mail_template_sepa_notify_debit
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.company_id\"></t>\n"
"                    <span style=\"font-size: 10px;\">Your SEPA Direct Debit Transaction</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.reference or ''\"></span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br><br>\n"
"\n"
"                    A SEPA Direct Debit payment request amounting <t t-out=\"format_amount(object.amount, object.currency_id) or ''\">$ 10.00</t> will be sent to your bank.<br>\n"
"\n"
"                    Your account ending in <t t-out=\"ctx.get('iban_last_4') or ''\">1234</t> will be debited in up to two calendar days or shortly after, please make sure you have the requested funds.<br><br>\n"
"\n"
"                    <t t-if=\"ctx.get('creditor_identifier') or ctx.get('mandate_ref')\">\n"
"                        Merchant data:<br>\n"
"                        <ul>\n"
"                            <t t-if=\"ctx.get('creditor_identifier')\">\n"
"                                <li>IBAN: <t t-out=\"ctx['creditor_identifier'] or ''\">NO 93 8601 1117947</t></li>\n"
"                            </t>\n"
"                            <t t-if=\"ctx.get('mandate_ref')\">\n"
"                                <li>SEPA DIRECT DEBIT MANDATE REFERENCE: <t t-out=\"ctx['mandate_ref'] or ''\"></t></li>\n"
"                            </t>\n"
"                        </ul>\n"
"                    </t>\n"
"                    Do not hesitate to contact us if you have any question.\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: payment_sepa_direct_debit
#: model:mail.template,body_html:payment_sepa_direct_debit.mail_template_sepa_notify_validation
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"padding-top: 16px; background-color: #F1F1F1; font-family:Verdana, Arial,sans-serif; color: #454748; width: 100%; border-collapse:separate;\"><tr><td align=\"center\">\n"
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"padding: 16px; background-color: white; color: #454748; border-collapse:separate;\">\n"
"<tbody>\n"
"    <!-- HEADER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\">\n"
"                    <t t-set=\"company\" t-value=\"object.payment_journal_id.company_id\"></t>\n"
"                    <span style=\"font-size: 10px;\">Your SEPA Direct Debit Manddate</span><br>\n"
"                    <span style=\"font-size: 20px; font-weight: bold;\" t-out=\"object.partner_bank_id.acc_number or ''\">BE15001559627230</span>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"right\" t-if=\"not company.uses_default_logo\">\n"
"                    <img t-attf-src=\"/logo.png?company={{ company.id }}\" style=\"padding: 0px; margin: 0px; height: auto; width: 80px;\" t-att-alt=\"company.name\">\n"
"                </td></tr>\n"
"                <tr><td colspan=\"2\" style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin:16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- CONTENT -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"top\" style=\"font-size: 13px;\">\n"
"                    Hello <t t-out=\"object.partner_id.name or ''\">Brandon Freeman</t>,<br><br>\n"
"\n"
"                    <p>A SEPA Direct Debit Mandate authorization has just been validated for use with <t t-out=\"company.name or ''\">YourCompany</t>.</p>\n"
"\n"
"                    <p>You will find a copy of the mandate validation attached to this email.</p>\n"
"\n"
"                    <p>Do not hesitate to contact us if you have any question.</p>\n"
"                </td></tr>\n"
"                <tr><td style=\"text-align:center;\">\n"
"                    <hr width=\"100%\" style=\"background-color:rgb(204,204,204);border:medium none;clear:both;display:block;font-size:0px;min-height:1px;line-height:0; margin: 16px 0px 16px 0px;\">\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"    <!-- FOOTER -->\n"
"    <tr>\n"
"        <td align=\"center\" style=\"min-width: 590px;\">\n"
"            <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; font-size: 11px; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"                <tr><td valign=\"middle\" align=\"left\">\n"
"                    <t t-out=\"company.name or ''\">YourCompany</t>\n"
"                </td></tr>\n"
"                <tr><td valign=\"middle\" align=\"left\" style=\"opacity: 0.7;\">\n"
"                    <t t-if=\"company.phone\">\n"
"                        <t t-out=\"company.phone or ''\">******-123-4567</t> |\n"
"                    </t>\n"
"                    <t t-if=\"company.email\">\n"
"                        <a t-attf-href=\"'mailto:%s' % {{ company.email }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.email or ''\"><EMAIL></a> |\n"
"                    </t>\n"
"                    <t t-if=\"company.website\">\n"
"                        <a t-attf-href=\"'%s' % {{ company.website }}\" style=\"text-decoration:none; color: #454748;\" t-out=\"company.website or ''\">http://www.example.com</a>\n"
"                    </t>\n"
"                </td></tr>\n"
"            </table>\n"
"        </td>\n"
"    </tr>\n"
"</tbody>\n"
"</table>\n"
"</td></tr>\n"
"<!-- POWERED BY -->\n"
"<tr><td align=\"center\" style=\"min-width: 590px;\">\n"
"    <table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: #F1F1F1; color: #454748; padding: 8px; border-collapse:separate;\">\n"
"      <tr><td style=\"text-align: center; font-size: 13px;\">\n"
"        Powered by <a target=\"_blank\" href=\"https://www.odoo.com?utm_source=db&amp;utm_medium=mail\" style=\"color: #875A7B;\">Odoo</a>\n"
"      </td></tr>\n"
"    </table>\n"
"</td></tr>\n"
"</table>\n"
"            "
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "A copy of the mandate will be sent by email to"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Both the phone number and the verification code are required."
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.payment_provider_form
msgid "Buy credits"
msgstr "Comprar créditos"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_payment_provider__code
msgid "Code"
msgstr "Código"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: payment_sepa_direct_debit
#. odoo-javascript
#: code:addons/payment_sepa_direct_debit/static/src/js/payment_form.js:0
msgid "Could not send the verification code."
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.sdd_mandate_form
msgid "Electronic Signature"
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "IBAN"
msgstr "IBAN"

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
msgid "Incorrect phone number."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
msgid "Missing or invalid IBAN."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_transaction.py:0
msgid "No transaction found matching reference %s."
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_payment_provider__sdd_signature_required
msgid "Online Signature"
msgstr "Assinatura Digital"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_account_payment_method
msgid "Payment Methods"
msgstr "Métodos de Pagamento"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_payment_token
msgid "Payment Token"
msgstr "Código de Pagamento"

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transação de Pagamento"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_sdd_mandate__phone_number
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "Phone Number"
msgstr "Número de Telefone"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_payment_provider__sdd_sms_verification_required
msgid "Phone Verification"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-javascript
#: code:addons/payment_sepa_direct_debit/static/src/js/payment_form.js:0
msgid "Re-send SMS"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_provider.py:0
msgid "Restricted to countries in the SEPA zone. Forbidden countries: %s"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model,name:payment_sepa_direct_debit.model_sdd_mandate
msgid "SDD Mandate"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields.selection,name:payment_sepa_direct_debit.selection__payment_provider__code__sepa_direct_debit
msgid "SEPA Direct Debit"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:account.payment.method,name:payment_sepa_direct_debit.payment_method_sepa_direct_debit
msgid "SEPA Direct Debit (provider)"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_payment_token__sdd_mandate_id
msgid "SEPA Direct Debit Mandate"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:mail.template,name:payment_sepa_direct_debit.mail_template_sepa_notify_debit
#: model:mail.template,subject:payment_sepa_direct_debit.mail_template_sepa_notify_debit
msgid "SEPA Direct Debit: Charge Notification"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:mail.template,name:payment_sepa_direct_debit.mail_template_sepa_notify_validation
msgid "SEPA Direct Debit: Validation Notification"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_payment_provider__sdd_sms_credits
msgid "SMS Credits"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-javascript
#: code:addons/payment_sepa_direct_debit/static/src/js/payment_form.js:0
msgid "SMS Sent"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
msgid "SMS could not be sent due to insufficient credit."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
msgid "SMS verification is disabled."
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "Send SMS"
msgstr "Enviar SMS"

#. module: payment_sepa_direct_debit
#: model:mail.template,description:payment_sepa_direct_debit.mail_template_sepa_notify_validation
msgid "Send the SEPA mandate in attachement, to partners who signed a new mandate"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:mail.template,description:payment_sepa_direct_debit.mail_template_sepa_notify_debit
msgid "Sent to the customer to indicate their account will be charged"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-javascript
#: code:addons/payment_sepa_direct_debit/static/src/js/payment_form.js:0
msgid "Server Error"
msgstr "Erro de Servidor"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_sdd_mandate__signature
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "Signature"
msgstr "Assinatura"

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_sdd_mandate__signed_by
msgid "Signed By"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_sdd_mandate__signed_on
msgid "Signed On"
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.sdd_payment_mandate_form
msgid "Signed online on"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_provider.py:0
msgid "The bank account of the journal is not a valid IBAN."
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,help:payment_sepa_direct_debit.field_sdd_mandate__signed_on
msgid "The date of the signature"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_transaction.py:0
msgid "The mandate is invalid."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_provider.py:0
msgid "The mandate owner and customer do not match."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The mandate was signed by %s."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The mandate was verified with phone number %s."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
msgid "The name and signature must be provided."
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,help:payment_sepa_direct_debit.field_sdd_mandate__signed_by
msgid "The name of the signer"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The phone number does not match."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
msgid "The phone number must be provided and verified."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
#: code:addons/payment_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The phone number should be in international format."
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,help:payment_sepa_direct_debit.field_sdd_mandate__phone_number
msgid "The phone number used for verification by SMS code"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,help:payment_sepa_direct_debit.field_payment_provider__code
msgid "The technical code of this payment provider."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_transaction.py:0
msgid "The token is not linked to a mandate."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_transaction.py:0
msgid "The transaction is not linked to a token."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/sdd_mandate.py:0
msgid "The verification code does not match."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/sdd_mandate.py:0
msgid "This mandate has already been verified."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/controllers/main.py:0
msgid "Unknown mandate ID."
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_sdd_mandate__verification_code
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "Verification Code"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,field_description:payment_sepa_direct_debit.field_sdd_mandate__verified
msgid "Verified"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-javascript
#: code:addons/payment_sepa_direct_debit/static/src/js/payment_form.js:0
msgid "We are not able to process your payment."
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,help:payment_sepa_direct_debit.field_payment_provider__sdd_signature_required
msgid "Whether a signature is required to create a new mandate."
msgstr ""

#. module: payment_sepa_direct_debit
#: model:ir.model.fields,help:payment_sepa_direct_debit.field_payment_provider__sdd_sms_verification_required
msgid "Whether phone numbers must be verified by an SMS code."
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.payment_provider_form
msgid "You don't have enough credit to send SMS. You can buy new credits here:"
msgstr ""

#. module: payment_sepa_direct_debit
#: model:mail.template,subject:payment_sepa_direct_debit.mail_template_sepa_notify_validation
msgid "Your SEPA Direct Debit Mandate with {{ object.payment_journal_id.company_id.name }}"
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/payment_provider.py:0
msgid "Your company must have a creditor identifier in order to issue a SEPA Direct Debit payment request. It can be set in Accounting settings."
msgstr ""

#. module: payment_sepa_direct_debit
#. odoo-python
#: code:addons/payment_sepa_direct_debit/models/sdd_mandate.py:0
msgid "Your confirmation code is %s"
msgstr ""

#. module: payment_sepa_direct_debit
#: model_terms:ir.ui.view,arch_db:payment_sepa_direct_debit.inline_form
msgid "upon validation."
msgstr ""
