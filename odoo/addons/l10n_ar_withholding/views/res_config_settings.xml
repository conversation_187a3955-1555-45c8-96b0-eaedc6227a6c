<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record model="ir.ui.view" id="res_config_settings_view_form">
        <field name="name">res.config.settings.view.form</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="l10n_ar.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//block[@id='argentina_localization']" position="inside">
                <setting id="withholding_tax" string="Withholding"  company_dependent="1" title="Account that will be set on lines created to represent the tax base amounts.">
                    <div class="content-group" invisible="country_code != 'AR'">
                        <div class="row mt16">
                            <label for="l10n_ar_tax_base_account_id" class="col-lg-3 o_light_label"/>
                            <field name="l10n_ar_tax_base_account_id"/>
                        </div>
                    </div>
                </setting>
            </xpath>
        </field>
    </record>
</odoo>
