# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ar_withholding
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-08 08:50+0000\n"
"PO-Revision-Date: 2025-01-08 08:50+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ar_withholding
#. odoo-python
#: code:addons/l10n_ar_withholding/models/l10n_ar_partner_tax.py:0
msgid "\"From date\" must be lower than \"To date\" on Withholding (AR) taxes."
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"<b><u>AFIP Sources</u></b><br/>\n"
"                    <span>Calculator of the Withholding Amount: </span>"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid "<b><u>Formula Earnings with Scale</u></b><br/>"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"<br/>\n"
"                    <span>Link to AFIP aditional info: </span>"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.report_payment_receipt_document
msgid "<span>Amount</span>"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.report_payment_receipt_document
msgid "<span>Base</span>"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.report_payment_receipt_document
msgid "<span>Tax</span>"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.report_payment_receipt_document
msgid "<span>Withholding number</span>"
msgstr ""

#. module: l10n_ar_withholding
#. odoo-python
#: code:addons/l10n_ar_withholding/wizards/account_payment_register.py:0
msgid ""
"A payment cannot have withholding if the payment method has no outstanding "
"accounts"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid "AFIP Additional info"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid "AFIP Calculator"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_code
msgid "AFIP Code"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.actions.act_window,name:l10n_ar_withholding.act_afip_earnings_table_scale
msgid "AFIP tax"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_res_company__l10n_ar_tax_base_account_id
#: model:ir.model.fields,help:l10n_ar_withholding.field_res_config_settings__l10n_ar_tax_base_account_id
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.res_config_settings_view_form
msgid ""
"Account that will be set on lines created to represent the tax base amounts."
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__percentage
msgid "Add %"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid ""
"Adjust total amount or withholdings amount so that the check amount is the "
"correct one."
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__amount
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
msgid "Amount"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_type_tax_use
msgid "Argentina Tax Type"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_withholding_payment_type
msgid "Argentina Withholding Payment Type"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_l10n_ar_partner_tax
msgid "Argentinean Partner Taxes"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_res_partner__l10n_ar_partner_tax_ids
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_res_users__l10n_ar_partner_tax_ids
msgid "Argentinean Withholding Taxes"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__base_amount
msgid "Base Amount"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"Base amount = Tax base + tax bases applied this month to the same tax and "
"partner - non-taxable amount"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__scale_id
msgid ""
"Calculation of the withholding amount: From the taxable amount (tax base + "
"tax bases applied this month to same tax and partner - non-taxable minimum) "
"subtract the immediately previous amount of the column 'S/ Exceeding $' to "
"detect which row to work with and apply the percentage of said row to the "
"result of the subtraction. Then add to this amount the amount of the '$' "
"column."
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid "Check amount"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__company_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__company_id
msgid "Company"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_res_partner
msgid "Contact"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__create_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__create_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__create_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__create_uid
msgid "Created by"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__create_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__create_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__create_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__create_date
msgid "Created on"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__currency_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__currency_id
msgid "Currency"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_withholding_payment_type__customer
msgid "Customer Payment"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__customer
msgid "Customer Payment Withholding"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__display_name
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__display_name
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__display_name
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_tax_type__earnings
msgid "Earnings"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_tax_type__earnings_scale
#: model:ir.ui.menu,name:l10n_ar_withholding.menu_action_afip_earnings_table_scale_line
msgid "Earnings Scale"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_scale_id
msgid "Earnings table scale if tax type is 'Earnings Scale'."
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__from_amount
msgid "From $"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__from_date
msgid "From Date"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__excess_amount
msgid ""
"From the taxable amount (tax base + tax bases applied this month to same tax"
" and partner - non-taxable minimum) subtract the immediately previous amount"
" of this column to detect which row to work with and apply the percentage of"
" said row to the result of the subtraction."
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__id
msgid "ID"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_tax_type__iibb_total
msgid "IIBB Total Amount"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_tax_type__iibb_untaxed
msgid "IIBB Untaxed"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_withholding_sequence_id
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__withholding_sequence_id
msgid ""
"If no sequence provided then it will be required for you to enter "
"withholding number when registering one."
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid "If the base amount is lower than 0.0 then the withholding is 0.0"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_minimum_threshold
msgid ""
"If the calculated withholding tax amount is lower than minimum withholding "
"threshold then it is 0.0."
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"If the withholding amount is lower than the minimum threshold on the tax "
"then the final withholding amount is 0.0"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_state_id
msgid "Jurisdiction"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_payment_register__l10n_ar_adjustment_warning
msgid "L10N Ar Adjustment Warning"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_payment_register__l10n_ar_net_amount
msgid "L10N Ar Net Amount"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__write_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__write_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__write_uid
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__write_uid
msgid "Last Updated by"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__write_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__write_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__write_date
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__write_date
msgid "Last Updated on"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__line_ids
msgid "Line"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_minimum_threshold
msgid "Minimum Treshold"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale__name
msgid "Name"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid "Net Amount"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_payment_register__l10n_ar_net_amount
msgid "Net amount after withholdings"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_non_taxable_amount
msgid "Non Taxable Amount"
msgstr ""

#. module: l10n_ar_withholding
#: model:l10n_ar.earnings.scale,name:l10n_ar_withholding.normal_scale
msgid "Normal Scale"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__name
msgid "Number"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__none
msgid "Other"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__partner_id
msgid "Partner"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_payment_register
msgid "Pay"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__payment_register_id
msgid "Payment Register"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_l10n_ar_payment_register_withholding
msgid "Payment register withholding lines"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_payment
msgid "Payments"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__percentage
msgid ""
"Percentage to apply to the result of the subtraction between the taxable "
"amount (tax base + tax basis of the previous month - non-taxable minimum) "
"and the immediately previous amount of 'S/ Exced. from $' column."
msgstr ""

#. module: l10n_ar_withholding
#. odoo-python
#: code:addons/l10n_ar_withholding/wizards/account_payment_register.py:0
msgid "Please enter withholding number for tax %s"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_partner_form
msgid "Purchase Withholding"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__purchase
msgid "Purchases"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__excess_amount
msgid "S/ Exceeding $"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__sale
msgid "Sales"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_scale_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__scale_id
msgid "Scale"
msgstr ""

#. module: l10n_ar_withholding
#: model:l10n_ar.earnings.scale,name:l10n_ar_withholding.scale_119
msgid "Scale 119"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_account_tax
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__tax_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__tax_id
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
msgid "Tax"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_res_company__l10n_ar_tax_base_account_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_res_config_settings__l10n_ar_tax_base_account_id
msgid "Tax Base Account"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_tax_form-l10n_ar
msgid "Tax Type"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__currency_id
msgid "The payment's currency."
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__to_amount
msgid ""
"The taxable amount (tax base + tax bases applied this month to same tax and "
"partner - non-taxable minimum) must be between the amount in the 'S/ Exced' "
"column. of $' and the amount of this column."
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__to_amount
msgid "To $"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__to_date
msgid "To Date"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_l10n_ar_earnings_scale_line__fixed_amount
msgid ""
"To obtain the withholding amount first from the taxable amount (tax base + "
"tax bases applied this month to same tax and partner - non-taxable minimum) "
"subtract the immediately previous amount of 'S/ Exced. of $' column to "
"detect which row to work with and apply the percentage of said row to the "
"result of the subtraction. Then add the amount of this column to the result "
"of applying the percentage."
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
msgid "Total"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_non_taxable_amount
msgid "Until this base amount, the tax is not applied."
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_withholding_payment_type__supplier
msgid "Vendor Payment"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields.selection,name:l10n_ar_withholding.selection__account_tax__l10n_ar_type_tax_use__supplier
msgid "Vendor Payment Withholding"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_withholding_sequence_id
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_payment_register_withholding__withholding_sequence_id
msgid "WTH Sequence"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_tax__l10n_ar_tax_type
msgid "WTH Tax"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.res_config_settings_view_form
msgid "Withholding"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
msgid "Withholding Number"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_afip_earnings_table_scale_form
msgid ""
"Withholding amount = (Base amount - immediately previous amount of the "
"column 'S/ Exceeding $') * row percentage / 100 + row fixed amount ('$' "
"column)"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,help:l10n_ar_withholding.field_account_tax__l10n_ar_withholding_payment_type
msgid "Withholding tax for supplier or customer payments."
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_bank_statement_line__l10n_ar_withholding_ids
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_move__l10n_ar_withholding_ids
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_payment__l10n_ar_withholding_ids
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_account_payment_register__l10n_ar_withholding_ids
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_form
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid "Withholdings"
msgstr ""

#. module: l10n_ar_withholding
#: model_terms:ir.ui.view,arch_db:l10n_ar_withholding.view_account_payment_register_form
msgid ""
"You can't register withholdings when paying invoices of different partners "
"or same partner without grouping"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_l10n_ar_earnings_scale
msgid "l10n_ar.earnings.scale"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model,name:l10n_ar_withholding.model_l10n_ar_earnings_scale_line
msgid "l10n_ar.earnings.scale.line"
msgstr ""

#. module: l10n_ar_withholding
#: model:ir.model.fields,field_description:l10n_ar_withholding.field_l10n_ar_partner_tax__ref
msgid "ref"
msgstr ""
