<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_jp" model="res.partner" forcecreate="1">
        <field name="name">JP Company</field>
        <field name="vat">7482543580381</field>
        <field name="street">池上通り</field>
        <field name="city">品川区</field>
        <field name="country_id" ref="base.jp"/>
        <field name="state_id" ref="base.state_jp_jp-19"/>
        <field name="zip">140-0004</field>
        <field name="phone">+81 90-1234-5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.jpexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_jp" model="res.company" forcecreate="1">
        <field name="name">JP Company</field>
        <field name="partner_id" ref="base.partner_demo_company_jp"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_jp')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_jp'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>jp</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_jp')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
