<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="eg_partner_address_form" model="ir.ui.view">
        <field name="name">eg.partner.form.address</field>
        <field name="model">res.partner</field>
        <field name="priority" eval="900"/>
        <field name="arch" type="xml">
            <form>
                <div class="o_address_format">
                    <field name="parent_id" invisible="1"/>
                    <field name="type" invisible="1"/>
                    <field name="l10n_eg_building_no" placeholder="Building Number..." class="o_address_street"/>
                    <field name="street" placeholder="Street" class="o_address_street"
                           readonly="type == 'contact' and parent_id"/>
                    <field name="street2" invisible="1"/>
                    <field name="city"/>
                    <field name="state_id" class="o_address_state" placeholder="State..." options='{"no_open": True}'
                           readonly="type == 'contact' and parent_id"/>
                    <field name="zip" placeholder="ZIP" class="o_address_zip"
                           readonly="type == 'contact' and parent_id"/>
                    <field name="country_id" placeholder="Country" class="o_address_country" options='{"no_open": True, "no_create": True}'
                           readonly="type == 'contact' and parent_id"/>
                </div>
            </form>
        </field>
    </record>
    <record id="base.eg" model="res.country">
        <field name="address_view_id" ref="eg_partner_address_form" />
        <field name="address_format" eval="'%(l10n_eg_building_no)s %(street)s\n%(city)s %(state_name)s\n%(zip)s\n%(country_name)s'"/>
    </record>
</odoo>
