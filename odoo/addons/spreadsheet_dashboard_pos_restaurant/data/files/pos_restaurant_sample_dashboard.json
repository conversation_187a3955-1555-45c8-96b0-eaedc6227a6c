{"version": 21, "sheets": [{"id": "c960176a-048b-4f9a-a326-3cc55c5fd1f2", "name": "Dahsboard", "colNumber": 11, "rowNumber": 84, "rows": {"11": {"size": 36}, "29": {"size": 41}, "48": {"size": 39}, "49": {"size": 36}, "50": {"size": 26}, "51": {"size": 26}, "52": {"size": 26}, "53": {"size": 26}, "54": {"size": 26}, "55": {"size": 26}, "56": {"size": 26}, "57": {"size": 26}, "58": {"size": 26}, "59": {"size": 26}, "60": {"size": 26}, "61": {"size": 26}, "62": {"size": 26}, "63": {"size": 26}, "64": {"size": 26}, "66": {"size": 39}, "67": {"size": 34}, "68": {"size": 26}, "69": {"size": 26}, "70": {"size": 26}, "71": {"size": 26}, "72": {"size": 26}, "73": {"size": 26}, "74": {"size": 26}, "75": {"size": 26}, "76": {"size": 26}, "77": {"size": 26}, "78": {"size": 26}, "79": {"size": 26}, "80": {"size": 26}, "81": {"size": 26}, "82": {"size": 26}}, "cols": {"0": {"isHidden": true, "size": 40}, "1": {"size": 288}, "2": {"size": 99}, "3": {"size": 76}, "4": {"size": 65}, "5": {"size": 27}, "6": {"isHidden": true, "size": 41}, "7": {"size": 217}, "8": {"size": 92}, "9": {"size": 80}, "10": {"size": 84}}, "merges": ["H68:I68", "H69:I69", "H70:I70", "H71:I71", "H72:I72", "H73:I73", "H74:I74", "H75:I75", "H76:I76", "H77:I77", "H78:I78"], "cells": {"A50": {"content": "#"}, "A51": {"content": "1"}, "A52": {"content": "2"}, "A53": {"content": "3"}, "A54": {"content": "4"}, "A55": {"content": "5"}, "A56": {"content": "6"}, "A57": {"content": "7"}, "A58": {"content": "8"}, "A59": {"content": "9"}, "A60": {"content": "10"}, "A61": {"content": "11"}, "A62": {"content": "12"}, "A63": {"content": "13"}, "A64": {"content": "14"}, "A65": {"content": "15"}, "A68": {"content": "#"}, "A69": {"content": "1"}, "A70": {"content": "2"}, "A71": {"content": "3"}, "A72": {"content": "4"}, "A73": {"content": "5"}, "A74": {"content": "6"}, "A75": {"content": "7"}, "A76": {"content": "8"}, "A77": {"content": "9"}, "A78": {"content": "10"}, "A79": {"content": "11"}, "A80": {"content": "12"}, "A81": {"content": "13"}, "A82": {"content": "14"}, "A83": {"content": "15"}, "B12": {"content": "=_t(\"Hourly Total Revenue\")"}, "B30": {"content": "=_t(\"Daily Total Revenue\")"}, "B49": {"content": "=_t(\"Top 15 Products per Margin\") "}, "B50": {"content": "=_t(\"Product \")"}, "B67": {"content": "=_t(\"Top 15 Tables per Revenue\")"}, "B68": {"content": "=_t(\"Table\")"}, "C50": {"content": "=_t(\"Quantity Sold\")"}, "C68": {"content": "=_t(\"Orders\")"}, "D50": {"content": "=_t(\"Revenue\")"}, "D68": {"content": "=_t(\"Revenue\")"}, "E50": {"content": "=_t(\"Margin\")"}, "E68": {"content": "=_t(\"Tip\")"}, "G50": {"content": "#"}, "G51": {"content": "1"}, "G52": {"content": "2"}, "G53": {"content": "3"}, "G54": {"content": "4"}, "G55": {"content": "5"}, "G56": {"content": "6"}, "G57": {"content": "7"}, "G58": {"content": "8"}, "G59": {"content": "9"}, "G60": {"content": "10"}, "G61": {"content": "11"}, "G62": {"content": "12"}, "G63": {"content": "13"}, "G64": {"content": "14"}, "G65": {"content": "15"}, "H49": {"content": "=_t(\"Top 15 Products per Category\")"}, "H50": {"content": "=_t(\"Category \")"}, "H67": {"content": "=_t(\"Top Responsibles\")"}, "H68": {"content": "=_t(\"Responsible\")"}, "I50": {"content": "=_t(\"Product \")"}, "J50": {"content": "=_t(\"Quantity\")"}, "J68": {"content": "=_t(\"Orders\")"}, "K50": {"content": "=_t(\"Revenue \")"}, "K68": {"content": "=_t(\"Revenue\")"}}, "styles": {"A50": 1, "A68": 1, "G50": 1, "A51:A65": 2, "A69:A83": 2, "E69:E83": 2, "G51:G65": 2, "B12": 3, "B30": 3, "B49": 3, "B67": 3, "H49": 3, "B50": 4, "B68": 4, "H50": 4, "H68": 4, "C50:E50": 5, "C68:E68": 5, "I50:K50": 5, "J68:K68": 5, "H67": 6}, "formats": {}, "borders": {"B49:E49": 1, "B67:E67": 1, "B12:K12": 1, "B30:K30": 1, "H49:K49": 1, "H67:K67": 1, "B50:E50": 2, "B68:E68": 2, "B13:K13": 2, "B31:K31": 2, "H50:K50": 2, "H68:K68": 2, "B51": 3, "B69": 3, "H51": 3, "I69": 3, "B52:B64": 4, "B70:B74": 4, "B76:B82": 4, "H52:H64": 4, "I70:I74": 4, "I76:I78": 4, "B65": 5, "B83": 5, "H65": 5, "B75:D75": 6, "H70:H78": 6, "I75:K75": 6, "C69": 7, "C51:D51": 7, "I51:J51": 7, "J69:K69": 7, "C70:C74": 8, "C76:C82": 8, "C52:D64": 8, "I52:J64": 8, "J76:J78": 8, "J70:K74": 8, "K76:K77": 8, "C83": 9, "C65:D65": 9, "I65:J65": 9, "D69": 10, "E51": 10, "K51": 10, "D70:D74": 11, "D76:D82": 11, "E52:E64": 11, "K52:K64": 11, "K78": 11, "D83": 12, "E65": 12, "K65": 12, "H69": 13, "H79:K79": 14}, "conditionalFormats": [], "figures": [{"id": "fdceb3b9-643f-4d30-a572-2d9b0b684f17", "x": 0, "y": 289, "width": 1027, "height": 391, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "Data!B13:B37", "yAxisId": "y", "label": "Revenue"}, {"dataRange": "Data!D13:D37", "label": "Average per order", "yAxisId": "y1", "trend": {"type": "polynomial", "order": 2, "display": false}}], "legendPosition": "top", "labelRange": "Data!A13:A37", "title": {"text": ""}, "aggregated": false, "axesDesign": {"y": {"title": {"text": "Total revenue"}}, "y1": {"title": {"text": "Average revenue per order"}}}, "showValues": false}}, {"id": "1ebdd3d0-8af0-4922-9223-2da0d6ec024c", "x": 0, "y": 721, "width": 1029, "height": 416, "tag": "chart", "data": {"type": "combo", "dataSetsHaveTitle": true, "dataSets": [{"dataRange": "Data!H13:H20", "yAxisId": "y"}, {"dataRange": "Data!J13:J20", "yAxisId": "y1", "label": "Average per order"}], "legendPosition": "top", "labelRange": "Data!G13:G20", "title": {"text": ""}, "aggregated": false, "axesDesign": {"y": {"title": {"text": "Total revenue"}}, "y1": {"title": {"text": "Average revenue per order"}}}}}, {"id": "49b3e330-3a31-4d43-9930-c42f2805c0c3", "x": 620, "y": 10, "width": 200, "height": 108, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "#Orders", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#EFF6FF", "keyValue": "Data!B2", "humanize": false}}, {"id": "d96a0fa5-e91b-455c-8004-9f39eeb09ee2", "x": 0, "y": 127, "width": 200, "height": 108, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Total revenue", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "keyValue": "Data!B3", "humanize": false}}, {"id": "6b97f89c-8416-43db-a32e-d0ab5cf9051e", "x": 0, "y": 10, "width": 200, "height": 108, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Untaxed revenue", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "keyValue": "Data!B4", "humanize": false}}, {"id": "cd102569-7656-4986-bf87-6e87b3ea493a", "x": 207, "y": 10, "width": 200, "height": 108, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Untaxed revenue per order", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "keyValue": "Data!B5", "humanize": false}}, {"id": "7b541ea8-8f93-44f4-9626-67073e540f16", "x": 414, "y": 10, "width": 200, "height": 108, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Untaxed revenue per guest", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "keyValue": "Data!B7", "humanize": false}}, {"id": "150978db-0307-4a65-a70a-e3a634dcfd48", "x": 620, "y": 127, "width": 200, "height": 108, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Discount Rate", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FEF2F2", "keyValue": "Data!B9", "humanize": false}}, {"id": "77f395fa-f25d-4ba8-a99d-405a99ebeef7", "x": 207, "y": 127, "width": 200, "height": 108, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Total revenue per order", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "keyValue": "Data!B6", "humanize": false}}, {"id": "87cda1aa-ba42-4865-8f56-912dd9e323a7", "x": 414, "y": 127, "width": 200, "height": 108, "tag": "chart", "data": {"baselineColorDown": "#EA6175", "baselineColorUp": "#43C5B1", "baselineMode": "difference", "title": {"text": "Total revenue per guest", "bold": true, "color": "#434343"}, "type": "scorecard", "background": "#FFF7ED", "keyValue": "Data!B8", "humanize": false}}], "tables": [], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}, {"id": "5d9c050d-2ba4-49ea-992f-142a93cad05a", "name": "Data", "colNumber": 29, "rowNumber": 113, "rows": {}, "cols": {}, "merges": [], "cells": {"A1": {"content": "=_t(\"Measure\")"}, "A2": {"content": "=_t(\"# Orders\")"}, "A3": {"content": "=_t(\"Total revenue\")"}, "A4": {"content": "=_t(\"Net revenue\")"}, "A5": {"content": "=_t(\"Average untaxed revenue per order\")"}, "A6": {"content": "=_t(\"Average total revenue per order\")"}, "A7": {"content": "=_t(\"Average net revenue / sitting \")"}, "A8": {"content": "=_t(\"Average total revenue / sitting\")"}, "A9": {"content": "=_t(\"Average discount\")"}, "A12": {"content": "=_t(\"Revenue per hour\")"}, "A13": {"content": "=_t(\"Time\")"}, "A14": {"content": "0"}, "A15": {"content": "1"}, "A16": {"content": "2"}, "A17": {"content": "3"}, "A18": {"content": "4"}, "A19": {"content": "5"}, "A20": {"content": "6"}, "A21": {"content": "7"}, "A22": {"content": "8"}, "A23": {"content": "9"}, "A24": {"content": "10"}, "A25": {"content": "11"}, "A26": {"content": "12"}, "A27": {"content": "13"}, "A28": {"content": "14"}, "A29": {"content": "15"}, "A30": {"content": "16"}, "A31": {"content": "17"}, "A32": {"content": "18"}, "A33": {"content": "19"}, "A34": {"content": "20"}, "A35": {"content": "21"}, "A36": {"content": "22"}, "A37": {"content": "23"}, "A38": {"content": "24"}, "B1": {"content": "=_t(\"Always\")"}, "B2": {"content": "897"}, "B3": {"content": "454666"}, "B4": {"content": "289756"}, "B5": {"content": "12807"}, "B6": {"content": "32149"}, "B7": {"content": "43512"}, "B8": {"content": "11946"}, "B9": {"content": "18584"}, "B13": {"content": "=_t(\"Revenue\")"}, "B14": {"content": "39778"}, "B15": {"content": "18736"}, "B16": {"content": "33392"}, "B17": {"content": "15876"}, "B18": {"content": "37547"}, "B19": {"content": "35778"}, "B20": {"content": "42506"}, "B21": {"content": "28653"}, "B22": {"content": "47055"}, "B23": {"content": "35883"}, "B24": {"content": "47506"}, "B25": {"content": "43113"}, "B26": {"content": "26248"}, "B27": {"content": "33474"}, "B28": {"content": "44053"}, "B29": {"content": "27609"}, "B30": {"content": "19591"}, "B31": {"content": "21330"}, "B32": {"content": "11756"}, "B33": {"content": "32755"}, "B34": {"content": "28535"}, "B35": {"content": "33462"}, "B36": {"content": "18652"}, "B37": {"content": "29928"}, "B38": {"content": "12889"}, "D13": {"content": "=_t(\"Average per table\")"}, "D14": {"content": "108"}, "D15": {"content": "241"}, "D16": {"content": "227"}, "D17": {"content": "242"}, "D18": {"content": "170"}, "D19": {"content": "124"}, "D20": {"content": "264"}, "D21": {"content": "252"}, "D22": {"content": "269"}, "D23": {"content": "95"}, "D24": {"content": "254"}, "D25": {"content": "86"}, "D26": {"content": "215"}, "D27": {"content": "200"}, "D28": {"content": "69"}, "D29": {"content": "212"}, "D30": {"content": "315"}, "D31": {"content": "204"}, "D32": {"content": "119"}, "D33": {"content": "267"}, "D34": {"content": "60"}, "D35": {"content": "142"}, "D36": {"content": "316"}, "D37": {"content": "73"}, "D38": {"content": "282"}, "F12": {"content": "=_t(\"Revenue per day\")"}, "F13": {"content": "=_t(\"Time\")"}, "F14": {"content": "1"}, "F15": {"content": "2"}, "F16": {"content": "3"}, "F17": {"content": "4"}, "F18": {"content": "5"}, "F19": {"content": "6"}, "F20": {"content": "7"}, "G13": {"content": "=_t(\"Day of the week\")"}, "G14": {"content": "=TODAY() - F14"}, "G15": {"content": "=TODAY() - F15"}, "G16": {"content": "=TODAY() - F16"}, "G17": {"content": "=TODAY() - F17"}, "G18": {"content": "=TODAY() - F18"}, "G19": {"content": "=TODAY() - F19"}, "G20": {"content": "=TODAY() - F20"}, "H13": {"content": "=_t(\"Revenue\")"}, "H14": {"content": "=RANDBETWEEN(1000,10000)"}, "H15": {"content": "=RANDBETWEEN(1000,10000)"}, "H16": {"content": "=RANDBETWEEN(1000,10000)"}, "H17": {"content": "=RANDBETWEEN(1000,10000)"}, "H18": {"content": "=RANDBETWEEN(1000,10000)"}, "H19": {"content": "=RANDBETWEEN(1000,10000)"}, "H20": {"content": "=RANDBETWEEN(1000,10000)"}, "I13": {"content": "=_t(\"# Tables\")"}, "J13": {"content": "=_t(\"Average per table\")"}, "J14": {"content": "=RANDBETWEEN(50,200)"}, "J15": {"content": "=RANDBETWEEN(50,200)"}, "J16": {"content": "=RANDBETWEEN(50,200)"}, "J17": {"content": "=RANDBETWEEN(50,200)"}, "J18": {"content": "=RANDBETWEEN(50,200)"}, "J19": {"content": "=RANDBETWEEN(50,200)"}, "J20": {"content": "=RANDBETWEEN(50,200)"}}, "styles": {"A1:B1": 7, "A12": 8, "F12": 8}, "formats": {"B3:B9": 1, "B14:B38": 1, "G14:G20": 2}, "borders": {}, "conditionalFormats": [], "figures": [], "tables": [{"range": "A13:D38", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": false, "bandedColumns": false, "automaticAutofill": true, "styleId": "TableStyleMedium5"}}, {"range": "F13:J20", "type": "static", "config": {"hasFilters": false, "totalRow": false, "firstColumn": false, "lastColumn": false, "numberOfHeaders": 1, "bandedRows": false, "bandedColumns": false, "automaticAutofill": true, "styleId": "TableStyleMedium5"}}], "areGridLinesVisible": true, "isVisible": true, "headerGroups": {"ROW": [], "COL": []}, "dataValidationRules": [], "comments": {}}], "styles": {"1": {"textColor": "#434343"}, "2": {"textColor": "#434343", "verticalAlign": "middle"}, "3": {"fontSize": 16, "textColor": "#01666B", "bold": true}, "4": {"textColor": "#434343", "bold": true, "fontSize": 11}, "5": {"textColor": "#434343", "bold": true, "fontSize": 11, "align": "center"}, "6": {"textColor": "#01666b", "fontSize": 16, "bold": true}, "7": {"fillColor": "#01666B", "textColor": "#FFFFFF", "bold": true}, "8": {"bold": true, "textColor": "#01666B"}}, "formats": {"1": "[$$]#,##0", "2": "dddd d mmmm yyyy"}, "borders": {"1": {"bottom": {"style": "thin", "color": "#CCCCCC"}}, "2": {"top": {"style": "thin", "color": "#CCCCCC"}}, "3": {"bottom": {"style": "thick", "color": "#FFFFFF"}, "right": {"style": "thick", "color": "#FFFFFF"}}, "4": {"top": {"style": "thick", "color": "#FFFFFF"}, "bottom": {"style": "thick", "color": "#FFFFFF"}, "right": {"style": "thick", "color": "#FFFFFF"}}, "5": {"top": {"style": "thick", "color": "#FFFFFF"}, "right": {"style": "thick", "color": "#FFFFFF"}}, "6": {"top": {"style": "thick", "color": "#FFFFFF"}, "bottom": {"style": "thick", "color": "#FFFFFF"}}, "7": {"bottom": {"style": "thick", "color": "#FFFFFF"}, "left": {"style": "thick", "color": "#FFFFFF"}, "right": {"style": "thick", "color": "#FFFFFF"}}, "8": {"top": {"style": "thick", "color": "#FFFFFF"}, "bottom": {"style": "thick", "color": "#FFFFFF"}, "left": {"style": "thick", "color": "#FFFFFF"}, "right": {"style": "thick", "color": "#FFFFFF"}}, "9": {"top": {"style": "thick", "color": "#FFFFFF"}, "left": {"style": "thick", "color": "#FFFFFF"}, "right": {"style": "thick", "color": "#FFFFFF"}}, "10": {"bottom": {"style": "thick", "color": "#FFFFFF"}, "left": {"style": "thick", "color": "#FFFFFF"}}, "11": {"top": {"style": "thick", "color": "#FFFFFF"}, "bottom": {"style": "thick", "color": "#FFFFFF"}, "left": {"style": "thick", "color": "#FFFFFF"}}, "12": {"top": {"style": "thick", "color": "#FFFFFF"}, "left": {"style": "thick", "color": "#FFFFFF"}}, "13": {"bottom": {"style": "thick", "color": "#FFFFFF"}}, "14": {"top": {"style": "thick", "color": "#FFFFFF"}}}, "revisionId": "START_REVISION", "uniqueFigureIds": true, "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ",", "weekStart": 7}}, "pivots": {}, "pivotNextId": 11, "customTableStyles": {}, "odooVersion": 12, "globalFilters": [], "lists": {}, "listNextId": 1}