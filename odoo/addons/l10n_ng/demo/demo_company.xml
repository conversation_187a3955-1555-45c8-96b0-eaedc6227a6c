<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_ng" model="res.partner">
        <field name="name">NG Company</field>
        <field name="vat">12345678-1234</field>
        <field name="company_registry">RC123456</field>
        <field name="street">Adeola Odeku St 1</field>
        <field name="city">Lagos</field>
        <field name="country_id" ref="base.ng"/>
        <field name="zip">106104</field>
        <field name="phone">+*************</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.demo-company.ng</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="demo_company_ng" model="res.company">
        <field name="name">NG Company</field>
        <field name="partner_id" ref="partner_demo_company_ng"/>
    </record>

    <record id="demo_bank_ng" model="res.partner.bank">
        <field name="acc_number">**********</field>
        <field name="partner_id" ref="partner_demo_company_ng"/>
        <field name="company_id" ref="demo_company_ng"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_ng')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_ng.demo_company_ng'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>ng</value>
        <value model="res.company" eval="obj().env.ref('l10n_ng.demo_company_ng')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
