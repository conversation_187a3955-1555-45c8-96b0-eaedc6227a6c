<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="l10n_ng_wh_vat_report" model="account.report">
        <field name="name">Withholding VAT returns (form 006)</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.ng"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="l10n_ng_wh_vat_column" model="account.report.column">
                <field name="name">Amount</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="l10n_ng_wh_vat_10" model="account.report.line">
                <field name="name">Total VAT withheld</field>
                <field name="code">L10N_NG_WHVAT_10</field>
                <field name="expression_ids">
                    <record id="l10n_ng_wh_vat_10_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">10</field>
                    </record>
                </field>
            </record>
            <record id="l10n_ng_wh_vat_20" model="account.report.line">
                <field name="name">Total withheld VAT payable</field>
                <field name="code">L10N_NG_WHVAT_20</field>
                <field name="expression_ids">
                    <record id="l10n_ng_wh_vat_20_tag" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">10</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
