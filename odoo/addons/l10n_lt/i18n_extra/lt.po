# Translation of Odoo Server.
# This file contains the translation of the following modules:
#   * l10n_lt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-05 09:10+0000\n"
"PO-Revision-Date: 2019-09-05 09:10+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_1_net_turnover
msgid "1. Net turnover"
msgstr "1. Pardavimo pajamos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_9_other_interest_similar_income
msgid "9. Other interest and similar income"
msgstr "9. <PERSON><PERSON> palū<PERSON> ir panašios pajamos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_10_impaired_fin_assets_short_investments
msgid "10. The impairment of the financial assets and short-term investments"
msgstr "10. Finansinio turto ir trumpalaikių investicijų vertės sumažėjimas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_11_interest_other_similar_expenses
msgid "11. Interest and other similar expenses"
msgstr "11. Palūkanų ir kitos panašios sąnaudos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_12_tax_on_profit
msgid "12. Tax on profit"
msgstr "12. Pelno mokestis"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_2_cost_of_sales
msgid "2. Cost of sales"
msgstr "2. Pardavimo savikaina"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_3_adjustments_of_biological_assets
msgid "3. Fair value adjustments of the biological assets"
msgstr "3. Biologinio turto tikrosios vertės pokytis"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_4_selling_expenses
msgid "4. Selling expenses"
msgstr "4. Pardavimo sąnaudos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_5_general_administrative_expenses
msgid "5. General and administrative expenses"
msgstr "5. Bendrosios ir administracinės sąnaudos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_6_other_operating_results
msgid "6. Other operating results"
msgstr "6. Kitos veiklos rezultatai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_7_income_investments_parent
msgid ""
"7. Income from investments in the shares of parent, subsidiaries and "
"associated entities"
msgstr ""
"7. Investicijų į patronuojančiosios, patronuojamųjų ir asocijuotųjų įmonių "
"akcijas pajamos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_8_income_other_longterm_investments_loans
msgid "8. Income from other long-term investments and loans"
msgstr "8. Kitų ilgalaikių investicijų ir paskolų pajamos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_1
msgid "A.1.1. Assets arising from development"
msgstr "A.1.1. Plėtros darbai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_2
msgid "A.1.2. Goodwill"
msgstr "A.1.2. Prestižas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_3
msgid "A.1.3. Software"
msgstr "A.1.3. Programinė įranga"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_4
msgid "A.1.4. Concessions, patents, licenses, trade marks and similar rights"
msgstr ""
"A.1.4. Koncesijos, patentai, licencijos, prekių ženklai ir panašios teisės"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_5
msgid "A.1.5. Other intangible assets"
msgstr "A.1.5. Kitas nematerialusis turtas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_1_6
msgid "A.1.6. Advance payments"
msgstr "A.1.6. Sumokėti avansai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_1
msgid "A.2.1. Land"
msgstr "A.2.1. Žemė"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_2
msgid "A.2.2. Buildings and structures"
msgstr "A.2.2. Pastatai ir statiniai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_3
msgid "A.2.3. Machinery and plant"
msgstr "A.2.3. Mašinos ir įranga"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_4
msgid "A.2.4. Vehicles"
msgstr "A.2.4. Transporto priemonės"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_5
msgid "A.2.5. Other equipment, fittings and tools"
msgstr "A.2.5. Kiti įrenginiai, prietaisai ir įrankiai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_6_1
msgid "A.2.6.1. Land"
msgstr "A.2.6.1. Žemė"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_6_2
msgid "A.2.6.2. Buildings"
msgstr "A.2.6.2. Pastatai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_2_7
msgid ""
"A.2.7. Advance payments and tangible assets under construction (production)"
msgstr ""
"A.2.7. Sumokėti avansai ir vykdomi materialio turto statybos (gamybos) "
"darbai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_1
msgid "A.3.1. Shares in entities of the entities group"
msgstr "A.3.1. Įmonių grupės įmonių akcijos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_2
msgid "A.3.2. Loans to entities of the entities group"
msgstr "A.3.2. Paskolos įmonių grupės įmonėms"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_3
msgid "A.3.3. Amounts receivable from entities of the entities group"
msgstr "A.3.3. Iš įmonių grupės įmonių gautinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_4
msgid "A.3.4. Shares in associated entities"
msgstr "A.3.4. Asocijuotųjų įmonių akcijos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_5
msgid "A.3.5. Loans to associated entities"
msgstr "A.3.5. Paskolos asocijuotosioms įmonėms "

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_6
msgid "A.3.6. Amounts receivable from the associated entities"
msgstr "A.3.6. Iš asocijuotųjų įmonių gautinos sumos "

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_7
msgid "A.3.7. Long-term investments"
msgstr "A.3.7. Ilgalaikės investicijos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_8
msgid "A.3.8. Amounts receivable after one year"
msgstr "A.3.8. Po vienų metų gautinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_3_9
msgid "A.3.9. Other financial assets"
msgstr "A.3.9. Kitas finansinis turtas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_4_1
msgid "A.4.1. Assets of the deferred tax on profit"
msgstr "A.4.1. Atidėtojo pelno mokesčio turtas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_4_2
msgid "A.4.2. Biological assets"
msgstr "A.4.2. Biologinis turtas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_a_4_3
msgid "A.4.3. Other assets"
msgstr "A.4.3. Kitas turtas"

#. module: l10n_lt
#: model:ir.model,name:l10n_lt.model_account_chart_template
msgid "Account Chart Template"
msgstr "Sąskaitų plano šablonas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_1
msgid "B.1.1. Raw materials, materials and consumables"
msgstr "B.1.1. Žaliavos, medžiagos ir komplektavimo detalės"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_2
msgid "B.1.2. Production and work in progress"
msgstr "B.1.2. Nebaigta produkcija ir vykdomi darbai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_3
msgid "B.1.3. Finished goods"
msgstr "B.1.3. Produkcija"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_4
msgid "B.1.4. Goods for resale"
msgstr "B.1.4. Pirktos prekės, skirtos perparduoti"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_5
msgid "B.1.5. Biological assets"
msgstr "B.1.5. Biologinis turtas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_6
msgid "B.1.6. Fixed tangible assets held for sale"
msgstr "B.1.6. Ilgalaikis materialusis turtas, skirtas parduoti"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_1_7
msgid "B.1.7. Advance payments"
msgstr "B.1.7. Sumokėti avansai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_2_1
msgid "B.2.1. Trade debtors"
msgstr "B.2.1. Pirkėjų skolos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_2_2
msgid "B.2.2. Amounts owed by entities of the entities group"
msgstr "B.2.2. Įmonių grupės įmonių skolos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_2_3
msgid "B.2.3. Amounts owed by associates entities"
msgstr "B.2.3. Asocijuotųjų įmonių skolos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_2_4
msgid "B.2.4. Other debtors"
msgstr "B.2.4. Kitos gautinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_3_1
msgid "B.3.1. Shares in entities of the entities group"
msgstr "B.3.1. Įmonių grupės įmonių akcijos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_3_2
msgid "B.3.2. Other investments"
msgstr "B.3.2. Kitos investicijos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_b_4
msgid "B.4. CASH AND CASH EQUIVALENTS"
msgstr "B.4. PINIGAI IR PINIGŲ EKVIVALENTAI"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_c_prepayments_accrued_income
msgid "C. PREPAYMENTS AND ACCRUED INCOME"
msgstr "C. ATEINANČIŲ LAIKOTARPIŲ SĄNAUDOS IR SUKAUPTOS PAJAMOS"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_1_1
msgid "D.1.1. Authorized (subscribed) or primary capital"
msgstr "D.1.1. Įstatinis (pasirašytasis) arba pagrindinis kapitalas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_1_2
msgid "D.1.2. Subscribed capital unpaid (–)"
msgstr "D.1.2. Pasirašytasis neapmokėtas kapitalas (–)"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_1_3
msgid "D.1.3. Own shares (–)"
msgstr "D.1.3. Savos akcijos, pajai (–)"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_2_share_premium
msgid "D.2. SHARE PREMIUM ACCOUNT"
msgstr "D.2. AKCIJŲ PRIEDAI"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_3_revaluation_reserve
msgid "D.3. REVALUATION RESERVE"
msgstr "D.3. PERKAINOJIMO REZERVAS"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_4_1
msgid "D.4.1. Compulsory reserve or emergency (reserve) capital"
msgstr "D.4.1. Privalomasis rezervas arba atsargos (rezervinis) kapitalas"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_4_2
msgid "D.4.2. Reserve for acquiring own shares"
msgstr "D.4.2. Savoms akcijoms įsigyti"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_4_3
msgid "D.4.3. Other reserves"
msgstr "D.4.3. Kiti rezervai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_5_1
msgid "D.5.1. Profit (loss) for the reporting year "
msgstr "D.5.1. Ataskaitinių metų pelnas (nuostoliai)"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_5_2
msgid "D.5.2. Profit (loss) brought forward"
msgstr "D.5.2. Ankstesnių metų pelnas (nuostoliai)"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_d_6
msgid "D.6. Common Summary of Accounts"
msgstr "D.6. Bendra sąskaitų santrauka"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_e_grants_subsidies
msgid "E. GRANTS, SUBSIDIES"
msgstr "E. DOTACIJOS, SUBSIDIJOS"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_f_1
msgid "F.1. Provisions for pensions and similar obligations"
msgstr "F.1. Pensijų ir panašių įsipareigojimų atidėjiniai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_f_2
msgid "F.2. Provisions for taxation"
msgstr "F.2. Mokesčių atidėjiniai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_f_3
msgid "F.3. Other provisions"
msgstr "F.3. Kiti atidėjiniai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_1
msgid "G.1.1. Debenture loans"
msgstr "G.1.1. Skoliniai įsipareigojimai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_2
msgid "G.1.2. Amounts owed to credit institutions"
msgstr "G.1.2. Skolos kredito įstaigoms"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_3
msgid "G.1.3. Payments received on account"
msgstr "G.1.3. Gauti avansai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_4
msgid "G.1.4. Trade creditors"
msgstr "G.1.4. Skolos tiekėjams"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_5
msgid "G.1.5. Amounts payable under the bills and checks "
msgstr "G.1.5. Pagal vekselius ir čekius mokėtinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_6
msgid "G.1.6. Amounts payable to the entities of the entities group"
msgstr "G.1.6. Įmonių grupės įmonėms mokėtinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_7
msgid "G.1.7. Amounts payable to the associated entities"
msgstr "G.1.7. Asocijuotosioms įmonėms mokėtinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_1_8
msgid "G.1.8. Other amounts payable and long-term liabilities"
msgstr "G.1.8. Kitos mokėtinos sumos ir ilgalaikiai įsipareigojimai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_1
msgid "G.2.1. Debenture loans"
msgstr "G.2.1. Skoliniai įsipareigojimai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_10
msgid "G.2.10. Other amounts payable and short-term liabilities"
msgstr "G.2.10. Kitos mokėtinos sumos ir trumpalaikiai įsipareigojimai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_2
msgid "G.2.2. Amounts owed to credit institutions"
msgstr "G.2.2. Skolos kredito įstaigoms"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_3
msgid "G.2.3. Payments received on account"
msgstr "G.2.3. Gauti avansai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_4
msgid "G.2.4. Trade creditors"
msgstr "G.2.4. Skolos tiekėjams"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_5
msgid "G.2.5. Amounts payable under the bills and checks "
msgstr "G.2.5. Pagal vekselius ir čekius mokėtinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_6
msgid "G.2.6. Amounts payable to the entities of the entities group"
msgstr "G.2.6. Įmonių grupės įmonėms mokėtinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_7
msgid "G.2.7. Amounts payable to the associated entities"
msgstr "G.2.7. Asocijuotosioms įmonėms mokėtinos sumos"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_8
msgid "G.2.8. Liabilities of tax on profit"
msgstr "G.2.8. Pelno mokesčio įsipareigojimai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_g_2_9
msgid "G.2.9. Liabilities related to employment relations"
msgstr "G.2.9. Su darbo santykiais susiję įsipareigojimai"

#. module: l10n_lt
#: model:account.account.tag,name:l10n_lt.account_account_tag_h_accruals_deferred_income
msgid "H. ACCRUALS AND DEFERRED INCOME"
msgstr "H. SUKAUPTOS SĄNAUDOS IR ATEINANČIŲ LAIKOTARPIŲ PAJAMOS"

#. module: l10n_lt
#: model:ir.model,name:l10n_lt.model_account_journal
msgid "Journal"
msgstr "Žurnalas"
