# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_auto_transfer
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Spanish (Latin America) (https://app.transifex.com/odoo/teams/41243/es_419/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_419\n"
"Plural-Forms: nplurals=3; plural=n == 1 ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "<span> to </span>"
msgstr "<span> para </span>"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_chart_template
msgid "Account Chart Template"
msgstr "Plantilla de plan de cuentas"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model
msgid "Account Transfer Model"
msgstr "Modelo de transferencia de cuenta"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_transfer_model_line
msgid "Account Transfer Model Line"
msgstr "Línea del modelo de transferencia de cuenta"

#. module: account_auto_transfer
#: model:ir.actions.server,name:account_auto_transfer.ir_cron_auto_transfer_ir_actions_server
msgid "Account automatic transfers: Perform transfers"
msgstr "Transferencias automáticas de cuenta: realizar transferencias"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Activate"
msgstr "Activar"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__active
msgid "Active"
msgstr "Activo"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these analytic accounts to the destination account"
msgstr ""
"Agrega una condición para transferir a la cuenta de destino solo la suma de "
"las líneas desde las cuentas de origen que coincidan con las cuentas "
"analíticas de la cuenta de destino."

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid ""
"Adds a condition to only transfer the sum of the lines from the origin "
"accounts that match these partners to the destination account"
msgstr ""
"Agrega una condición para transferir solo la suma de las líneas de las "
"cuentas de origen que coinciden con estos contactos a la cuenta de destino."

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__analytic_account_ids
msgid "Analytic Filter"
msgstr "Filtro analítico"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_search
msgid "Archived"
msgstr "Archivado"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Automated Transfer"
msgstr "Transferencia automática"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (%(percent)s%% from account %(origin_account)s)"
msgstr ""
"Transferencia automática (%(percent)s%% desde la cuenta %(origin_account)s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (-%s%%)"
msgstr "Transferencia automática (-%s%%)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (entries with analytic account(s): %(analytic_accounts)s "
"and partner(s): %(partners)s)"
msgstr ""
"Transferencia automática (asientos con cuentas analíticas: "
"%(analytic_accounts)s y contactos: %(partners)s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with analytic account(s): %s)"
msgstr "Transferencia automática (asientos con cuentas analíticas: %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (entries with partner(s): %s)"
msgstr "Transferencia automática (asientos con contactos: %s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with analytic "
"account(s): %(analytic_accounts)s and partner(s): %(partners)s)"
msgstr ""
"Transferencia automática (desde la cuenta %(origin_account)s con cuenta "
"analítica: %(analytic_accounts)s y contactos: %(partners)s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with analytic "
"account(s): %(analytic_accounts)s)"
msgstr ""
"Transferencia automática (desde la cuenta %(origin_account)s con la cuenta "
"analítica: %(analytic_accounts)s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"Automatic Transfer (from account %(origin_account)s with partner(s): "
"%(partners)s)"
msgstr ""
"Transferencia automática (desde la cuenta %(origin_account)s con contactos: "
"%(partners)s)"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "Automatic Transfer (to account %s)"
msgstr "Transferencia automática (a la cuenta %s)"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.transfer_model_action
msgid "Automatic Transfers"
msgstr "Transferencias automáticas"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company"
msgstr "Empresa"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model__company_id
msgid "Company related to this journal"
msgstr "Empresa relacionada con este diario"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Compute Transfer"
msgstr "Calcular transferencia"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__create_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__create_date
msgid "Created on"
msgstr "Creado el"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Description"
msgstr "Descripción"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__account_id
msgid "Destination Account"
msgstr "Cuenta de destino"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__line_ids
msgid "Destination Accounts"
msgstr "Cuentas de destino"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__journal_id
msgid "Destination Journal"
msgstr "Diario de destino"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Disable"
msgstr "Deshabilitar"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__disabled
msgid "Disabled"
msgstr "Deshabilitado"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__display_name
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__display_name
msgid "Display Name"
msgstr "Mostrar nombre"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__frequency
msgid "Frequency"
msgstr "Frecuencia"

#. module: account_auto_transfer
#: model:ir.actions.act_window,name:account_auto_transfer.generated_transfers_action
msgid "Generated Entries"
msgstr "Asientos generados"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids
msgid "Generated Moves"
msgstr "Movimientos generados"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__id
msgid "ID"
msgstr "ID"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "IFRS Automatic Transfers"
msgstr "Transferencias automáticas NIIF "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "IFRS rent expense transfer"
msgstr "Transferencia NIIF de gastos de renta "

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_journal
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Journal"
msgstr "Diario"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move
msgid "Journal Entry"
msgstr "Asiento contable"

#. module: account_auto_transfer
#: model:ir.model,name:account_auto_transfer.model_account_move_line
msgid "Journal Item"
msgstr "Apunte contable"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_uid
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__write_date
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__month
msgid "Monthly"
msgstr "Mensual"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__move_ids_count
msgid "Move Ids Count"
msgstr "Número de ID de movimiento"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Move Model"
msgstr "Modelo de movimiento"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_tree
msgid "Move Models"
msgstr "Modelos de movimiento"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__name
msgid "Name"
msgstr "Nombre"

#. module: account_auto_transfer
#: model:ir.model.constraint,message:account_auto_transfer.constraint_account_transfer_model_line_unique_account_by_transfer_model
msgid "Only one account occurrence by transfer model"
msgstr "Solo una ocurrencia de cuenta por modelo de transferencia"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__account_ids
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Origin Accounts"
msgstr "Cuentas de origen"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_bank_statement_line__transfer_model_id
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_move__transfer_model_id
msgid "Originating Model"
msgstr "Modelo de origen"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__partner_ids
msgid "Partner Filter"
msgstr "Filtro de contacto"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent
msgid "Percent"
msgstr "Porcentaje"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Percent (%)"
msgstr "Porcentaje (%)"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__percent_is_readonly
msgid "Percent Is Readonly"
msgstr "El porcentaje es de solo lectura"

#. module: account_auto_transfer
#: model:ir.model.fields,help:account_auto_transfer.field_account_transfer_model_line__percent
msgid ""
"Percentage of the sum of lines from the origin accounts will be transferred "
"to the destination account"
msgstr ""
"El porcentaje de la suma de líneas de las cuentas de origen se transferirá a"
" la cuenta de destino"

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Period"
msgstr "Periodo"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__quarter
msgid "Quarterly"
msgstr "Trimestral"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__state__in_progress
msgid "Running"
msgstr "En proceso "

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__sequence
msgid "Sequence"
msgstr "Secuencia"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_start
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__state
msgid "State"
msgstr "Estado"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__date_stop
msgid "Stop Date"
msgstr "Fecha de finalización"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The analytic filter %s is duplicated"
msgstr "El filtro analítico %s está duplicado"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"The partner filter %(partner_filter)s in combination with the analytic "
"filter %(analytic_filter)s is duplicated"
msgstr ""
"El filtro de contactos %(partner_filter)s junto con el filtro analítico "
"%(analytic_filter)s está duplicado"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The partner filter %s is duplicated"
msgstr "El filtro de contacto %s está duplicado"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid "The total percentage (%s) should be less or equal to 100!"
msgstr "¡El porcentaje total (%s) debe ser menor o igual a 100!"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model__total_percent
msgid "Total Percent"
msgstr "Porcentaje total"

#. module: account_auto_transfer
#: model:ir.model.fields,field_description:account_auto_transfer.field_account_transfer_model_line__transfer_model_id
msgid "Transfer Model"
msgstr "Modelo de transferencia"

#. module: account_auto_transfer
#: model:ir.ui.menu,name:account_auto_transfer.menu_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "Transfers"
msgstr "Transferencias"

#. module: account_auto_transfer
#: model:ir.model.fields.selection,name:account_auto_transfer.selection__account_transfer_model__frequency__year
msgid "Yearly"
msgstr "Anual"

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/demo/account_demo.py:0
msgid "Yearly liabilites auto transfers"
msgstr "Transferencias automáticas anuales de pasivos "

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"You cannot delete an automatic transfer that has draft moves attached "
"('%s'). Please delete them before deleting this transfer."
msgstr ""
"No puede eliminar una transferencia automática que tiene movimientos en "
"borrador adjuntos ('%s'). Elimínelos antes de eliminar esta transferencia."

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/transfer_model.py:0
msgid ""
"You cannot delete an automatic transfer that has posted moves attached "
"('%s')."
msgstr ""
"No puede eliminar una transferencia automática que tiene movimientos "
"registrados adjuntos ('%s')."

#. module: account_auto_transfer
#. odoo-python
#: code:addons/account_auto_transfer/models/account_move_line.py:0
msgid "You cannot set Tax on Automatic Transfer's entries."
msgstr ""
"No puede establecer impuestos a los asientos de transferencia automática."

#. module: account_auto_transfer
#: model_terms:ir.ui.view,arch_db:account_auto_transfer.view_transfer_model_form
msgid "e.g. Monthly Expense Transfer"
msgstr "Por ejemplo, transferencia mensual de gastos"
