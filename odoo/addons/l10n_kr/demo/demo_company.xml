<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_kr" model="res.partner" forcecreate="1">
        <field name="name">KR Company</field>
        <field name="vat"/>
        <field name="street">210-7, Daecheon 1(il)-dong</field>
        <field name="city">Boryeong-si</field>
        <field name="country_id" ref="base.kr"/>
        <field name="zip"/>
        <field name="phone">+82-7-975-9012</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.krexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_kr" model="res.company" forcecreate="1">
        <field name="name">KR Company</field>
        <field name="partner_id" ref="base.partner_demo_company_kr"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_kr')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_kr'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>kr</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_kr')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
