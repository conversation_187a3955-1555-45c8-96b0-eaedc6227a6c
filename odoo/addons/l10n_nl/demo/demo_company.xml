<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_nl" model="res.partner" forcecreate="1">
        <field name="name">NL Company</field>
        <field name="vat">NL219987701B73</field>
        <field name="street">Bloemstraat 42</field>
        <field name="city">Groningen</field>
        <field name="country_id" ref="base.nl"/>
        <field name="state_id" ref="base.state_nl_gr"/>
        <field name="zip">9700</field>
        <field name="phone">+31 6 ********</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.nlexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_nl" model="res.company" forcecreate="1">
        <field name="name">NL Company</field>
        <field name="partner_id" ref="base.partner_demo_company_nl"/>
    </record>

    <record id="base.demo_bank_nl" model="res.partner.bank" forcecreate="1">
        <field name="acc_number">NL78RABO7948612920</field>
        <field name="partner_id" ref="base.partner_demo_company_nl"/>
        <field name="company_id" ref="base.demo_company_nl"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_nl')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_nl'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>nl</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_nl')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
