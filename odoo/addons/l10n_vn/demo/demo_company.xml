<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_vn" model="res.partner" forcecreate="1">
        <field name="name">VN Company</field>
        <field name="vat"/>
        <field name="street">Lê A</field>
        <field name="city">Phường Bảo Vinh</field>
        <field name="country_id" ref="base.vn"/>
        <field name="state_id" ref="base.state_vn_VN-06"/>
        <field name="zip">76463</field>
        <field name="phone">+84 91 234 56 78</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.vnexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_vn" model="res.company" forcecreate="1">
        <field name="name">VN Company</field>
        <field name="partner_id" ref="base.partner_demo_company_vn"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_vn')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_vn'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>vn</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_vn')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
