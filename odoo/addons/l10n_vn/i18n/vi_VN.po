# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_vn
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.5alpha1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-15 11:07+0000\n"
"PO-Revision-Date: 2024-08-15 11:07+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_vn
#: model:ir.model.fields.selection,name:l10n_vn.selection__res_partner_bank__proxy_type__atm_card
msgid "ATM Card Number"
msgstr "Số thẻ ATM"

#. module: l10n_vn
#: model:ir.model,name:l10n_vn.model_account_chart_template
msgid "Account Chart Template"
msgstr "Bảng hệ thống tài k<PERSON>n"

#. module: l10n_vn
#: model:account.report.column,name:l10n_vn.tax_report_balance
msgid "Balance"
msgstr "Số dư"

#. module: l10n_vn
#: model:ir.model.fields.selection,name:l10n_vn.selection__res_partner_bank__proxy_type__bank_acc
msgid "Bank Account"
msgstr "Số TK ngân hàng"

#. module: l10n_vn
#: model:ir.model,name:l10n_vn.model_res_partner_bank
msgid "Bank Accounts"
msgstr "Tài khoản Ngân hàng"

#. module: l10n_vn
#: model:account.report.column,name:l10n_vn.tax_report_amount_untaxed
msgid "Base Amount"
msgstr "Số tiền gốc"

#. module: l10n_vn
#. odoo-python
#: code:addons/l10n_vn/models/res_bank.py:0
msgid ""
"Can't generate a Vietnamese QR banking code with a currency other than VND."
msgstr "Không thể tạo mã QR ngân hàng với tiền tệ khác Việt Nam Đồng."

#. module: l10n_vn
#: model_terms:ir.ui.view,arch_db:l10n_vn.view_partner_bank_form_inherit_account
msgid "Documentation"
msgstr "Tài liệu"

#. module: l10n_vn
#: model:ir.model.fields,help:l10n_vn.field_account_bank_statement_line__l10n_vn_e_invoice_number
#: model:ir.model.fields,help:l10n_vn.field_account_move__l10n_vn_e_invoice_number
#: model:ir.model.fields,help:l10n_vn.field_account_payment__l10n_vn_e_invoice_number
msgid "Electronic Invoicing number."
msgstr "Số hoá đơn trên SInvoice."

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_03_01_vn
msgid "Import Tax"
msgstr "Thuế nhập khẩu"

#. module: l10n_vn
#: model:ir.model,name:l10n_vn.model_account_move
msgid "Journal Entry"
msgstr "Bút toán sổ nhật ký"

#. module: l10n_vn
#: model:ir.model.fields.selection,name:l10n_vn.selection__res_partner_bank__proxy_type__merchant_id
msgid "Merchant ID"
msgstr "ID Người bán"

#. module: l10n_vn
#. odoo-python
#: code:addons/l10n_vn/models/res_bank.py:0
msgid ""
"Missing Bank Identifier Code.\n"
"Please configure the Bank Identifier Code inside the bank settings."
msgstr ""
"Thiếu mã định danh ngân hàng.\n"
"Hãy cấu hình nó ở trong phần thiết lập ngân hàng."

#. module: l10n_vn
#: model:ir.model.fields.selection,name:l10n_vn.selection__res_partner_bank__proxy_type__payment_service
msgid "Payment Service"
msgstr "Dịch vụ Thanh toán"

#. module: l10n_vn
#: model:ir.model.fields,field_description:l10n_vn.field_account_setup_bank_manual_config__proxy_type
#: model:ir.model.fields,field_description:l10n_vn.field_res_partner_bank__proxy_type
msgid "Proxy Type"
msgstr "Loại proxy"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_01_vn
msgid "Purchase of Goods and Services"
msgstr "HHDV Mua vào"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_02_vn
msgid "Sales of Goods and Services"
msgstr "HHDV Bán ra"

#. module: l10n_vn
#: model:account.report,name:l10n_vn.tax_report
msgid "Tax Report"
msgstr "Báo cáo Thuế"

#. module: l10n_vn
#. odoo-python
#: code:addons/l10n_vn/models/res_bank.py:0
msgid ""
"The QR Code Type must be either Merchant ID, ATM Card Number or Bank Account"
" to generate a Vietnam Bank QR code for account number %s."
msgstr ""
"Loại mã QR phải là ID người bán, số thẻ ATM hoặc tài khoản ngân hàng để tạo "
"mã QR Ngân hàng Việt Nam cho số tài khoản %s."

#. module: l10n_vn
#. odoo-python
#: code:addons/l10n_vn/models/res_bank.py:0
msgid ""
"The proxy type %s is not supported for Vietnamese partners. It must be "
"either Merchant ID, ATM Card Number or Bank Account"
msgstr ""
"Loại proxy %s không được hỗ trợ cho khách hàng Việt Nam. Nó phải là ID người"
" bán, số thẻ ATM hoặc tài khoản ngân hàng"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_01_01_vn
msgid "VAT on purchase of goods and services"
msgstr "Thuế GTGT HHDV mua vào"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_01_01_01_vn
msgid "VAT on purchase of goods and services 0%"
msgstr "Thuế GTGT HHDV mua vào chịu thuế 0%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_03_01_01_vn
msgid "VAT on purchase of goods and services 10%"
msgstr "Thuế GTGT HHDV mua vào chịu thuế 10%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_02_01_01_vn
msgid "VAT on purchase of goods and services 5%"
msgstr "Thuế GTGT HHDV mua vào chịu thuế 5%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_04_01_01_vn
msgid "VAT on purchase of goods and services 8%"
msgstr "Thuế GTGT HHDV mua vào chịu thuế 8%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_05_01_01_vn
msgid "VAT on purchase of goods and services Exemption"
msgstr "Không chịu thuế HHDV mua vào"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_02_01_vn
msgid "VAT on purchase of imported goods"
msgstr "Giá trị HHDV Mua vào"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_01_02_01_vn
msgid "VAT on purchase of imported goods 0%"
msgstr "Giá trị HHDV Mua vào chịu thuế 0%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_03_02_01_vn
msgid "VAT on purchase of imported goods 10%"
msgstr "Giá trị HHDV Mua vào chịu thuế 10%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_02_02_01_vn
msgid "VAT on purchase of imported goods 5%"
msgstr "Giá trị HHDV Mua vào chịu thuế 5%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_04_02_01_vn
msgid "VAT on purchase of imported goods 8%"
msgstr "Giá trị HHDV Mua vào chịu thuế 8%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_01_02_vn
msgid "VAT on sales of goods and services"
msgstr "Thuế GTGT HHDV bán ra"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_01_01_02_vn
msgid "VAT on sales of goods and services 0%"
msgstr "Thuế GTGT HHDV bán ra chịu thuế 0%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_03_01_02_vn
msgid "VAT on sales of goods and services 10%"
msgstr "Thuế GTGT HHDV bán ra chịu thuế 10%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_02_01_02_vn
msgid "VAT on sales of goods and services 5%"
msgstr "Thuế GTGT HHDV bán ra chịu thuế 5%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_04_01_02_vn
msgid "VAT on sales of goods and services 8%"
msgstr "Thuế GTGT HHDV bán ra chịu thuế 8%"

#. module: l10n_vn
#: model:account.report.line,name:l10n_vn.account_tax_report_line_05_01_02_vn
msgid "VAT on sales of goods and services Exemption"
msgstr "Không chịu thuế HHDV bán ra"

#. module: l10n_vn
#: model_terms:ir.ui.view,arch_db:l10n_vn.view_invoice_form_inherit_l10n_vn
msgid "Vietnamese Electronic Invoicing"
msgstr ""

#. module: l10n_vn
#: model:ir.model.fields,field_description:l10n_vn.field_account_bank_statement_line__l10n_vn_e_invoice_number
#: model:ir.model.fields,field_description:l10n_vn.field_account_move__l10n_vn_e_invoice_number
#: model:ir.model.fields,field_description:l10n_vn.field_account_payment__l10n_vn_e_invoice_number
msgid "eInvoice Number"
msgstr ""
