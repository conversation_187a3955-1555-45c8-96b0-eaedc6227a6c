# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_reports
# 
# Translators:
# Wil Odoo, 2025
# <PERSON><PERSON> <alina.lis<PERSON><PERSON>@erp.co.ua>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-25 17:23+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "\" account balance is affected by"
msgstr "\" баланс рахунку змінений через"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "%(journal)s - %(account)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%(names)s and %(remaining)s others"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%(names)s and one other"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "%(report_label)s: %(period)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/budget.py:0
msgid "%s (copy)"
msgstr "%s (копія)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "%s is not a numeric value"
msgstr "%s не є числовим значенням"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "%s selected"
msgstr "%s вибрано"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"'Open General Ledger' caret option is only available form report lines "
"targetting accounts."
msgstr ""
"Параметр «Відкрити головну книгу» доступний лише для рахунків, націлених на "
"рядки звіту."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"'View Bank Statement' caret option is only available for report lines "
"targeting bank statements."
msgstr ""
"Опція створення 'Переглянути банківську виписку' доступна лише для рядків "
"звіту банківської виписки."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "'external' engine does not support groupby, limit nor offset."
msgstr "«зовнішній» механізм не підтримує групування, обмеження та зміщення."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(%s lines)"
msgstr "(%s рядки)"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_receipts
msgid "(+) Outstanding Receipts"
msgstr "(+) Неузгоджені платежі"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding_payments
msgid "(-) Outstanding Payments"
msgstr "(-) Неузгоджені вихідні платежі"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(1 line)"
msgstr "(1 рядок)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "(No %s)"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "(No Group)"
msgstr "(Немає групи)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid ", leading to an unexplained difference of"
msgstr ", що призводить до незрозумілої різниці"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "-> Refresh"
msgstr "-> Оновлення"

#. module: account_reports
#: model:mail.template,body_html:account_reports.email_template_customer_statement
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"                <p style=\"margin: 0px; padding: 0px;\">\n"
"                    <t t-if=\"object.id != object.commercial_partner_id.id\">Dear <t t-out=\"object.name or ''\"/> (<t t-out=\"object.commercial_partner_id.name or ''\"/>),</t>\n"
"                    <t t-else=\"\">Dear <t t-out=\"object.name or ''\"/>,</t>\n"
"                    <br/>\n"
"                    Please find enclosed the statement of your account.\n"
"                    <br/>\n"
"                    Do not hesitate to contact us if you have any questions.\n"
"                    <br/>\n"
"                    Sincerely,\n"
"                    <br/>\n"
"\t                <t t-out=\"object._get_followup_responsible().name if is_html_empty(object._get_followup_responsible().signature) else object._get_followup_responsible().signature\"/>\n"
"                </p>\n"
"            </div>\n"
"        "
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid ""
"<i class=\"fa fa-question-circle ml4\" role=\"img\" aria-label=\"Warning\" "
"title=\"The email address is unknown on the partner\" invisible=\"not "
"send_mail_readonly\"/>"
msgstr ""
"<i class=\"fa fa-question-circle ml4\" role=\"img\" aria-label=\"Warning\" "
"title=\"The email address is unknown on the partner\" invisible=\"not "
"send_mail_readonly\"/>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid ""
"<i>Errors marked with <i class=\"fa fa-warning\"/> are critical and prevent "
"the file generation.</i>"
msgstr ""
"<i>Позначені помилки <i class=\"fa fa-warning\"/> є критичними та "
"запобігають створенню файлів.</i>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "<span role=\"separator\">Reconciliation</span>"
msgstr "<span role=\"separator\">Узгодження</span>"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "<span>One or more error(s) occurred during file generation:</span>"
msgstr "<span>Виникла ще одна помилка під час створення файлу:</span>"

#. module: account_reports
#: model:ir.model.constraint,message:account_reports.constraint_account_report_horizontal_group_name_uniq
msgid "A horizontal group with the same name already exists."
msgstr "Горизонтальна група з такою назвою вже існує."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
msgid "A line with a 'Group By' value cannot have children."
msgstr "Рядок зі значення 'Групування' не може мати дочірніх."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"A tax unit can only be created between companies sharing the same main "
"currency."
msgstr ""
"Податкова одиниця може бути створена лише між компаніями, які використовують"
" одну основну валюту."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"A tax unit must contain a minimum of two companies. You might want to delete"
" the unit."
msgstr ""
"Податкова одиниця має містити щонайменше дві компанії. Можливо ви хочете "
"видалити одиницю."

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_total_assets0
msgid "ASSETS"
msgstr "АКТИВИ"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_account_name
#: model:account.report.column,name:account_reports.aged_receivable_report_account_name
#: model:account.report.column,name:account_reports.partner_ledger_report_account_code
#: model:ir.model,name:account_reports.model_account_account
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__account_id
msgid "Account"
msgstr "Рахунок"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Шаблон плану рахунків"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Account Code"
msgstr "Код рахунку"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Code / Tag"
msgstr "Код рахунку / Тег"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_display_representative_field
msgid "Account Display Representative Field"
msgstr "Поле відображення рахунку"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Account Label"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Account Name"
msgstr "Назва рахунку"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_annotation
msgid "Account Report Annotation"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_custom_handler
msgid "Account Report Custom Handler"
msgstr "Кстомний обробник бухгалтерського звіту"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_report_handler
msgid "Account Report Handler for Tax Reports"
msgstr "Обробник звітів облікового запису для податкових звітів"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_send
msgid "Account Report Send"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_reports_show_per_company_setting
msgid "Account Reports Show Per Company Setting"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_partner__account_represented_company_ids
#: model:ir.model.fields,field_description:account_reports.field_res_users__account_represented_company_ids
msgid "Account Represented Company"
msgstr "Компанія, що представляє рахунок"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_journal_id
msgid "Account Revaluation Journal"
msgstr "Журнал переоцінки рахунку"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_account_type.xml:0
msgid "Account:"
msgstr "Рахунок:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_representative_id
msgid "Accounting Firm"
msgstr "Бухгалтерська компанія"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report
msgid "Accounting Report"
msgstr "Звіт бухобліку"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_budget
msgid "Accounting Report Budget"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_budget_item
msgid "Accounting Report Budget Item"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_expression
msgid "Accounting Report Expression"
msgstr "Вираз бухгалтерського звіту"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_line
msgid "Accounting Report Line"
msgstr "Рядок бухгалтерського звіту"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_tree
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Accounting Reports"
msgstr "Бухгалтерські звіти"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Accounts"
msgstr "Рахунки"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Accounts Coverage Report"
msgstr "Звіт покриття рахунків"

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_to_adjust
msgid "Accounts To Adjust"
msgstr "Рахунки для коригування"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Accounts coverage"
msgstr "Покриття рахунків"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity_type__category
msgid "Action"
msgstr "Дія"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__actionable_errors
msgid "Actionable Errors"
msgstr "Помилки, що підлягають вживанню заходів"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Дії повинні запустити конкретну дію, таку як відкриття календаря або "
"автоматичне позначення як зроблено після завантаження документа."

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity
msgid "Activity"
msgstr "Дія"

#. module: account_reports
#: model:ir.model,name:account_reports.model_mail_activity_type
msgid "Activity Type"
msgstr "Тип дії"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.xml:0
msgid "Add a line"
msgstr "Додати рядок"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Add contacts to notify..."
msgstr "Додати контактних осіб для сповіщення..."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__totals_below_sections
msgid "Add totals below sections"
msgstr "Додати підсумки нижче розділів"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_adjustment
msgid "Adjustment"
msgstr "Налаштування"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Adjustment Entry"
msgstr "Корегуючий запис"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance Payments received from customers"
msgstr "Авансові платежі, отримані від клієнтів"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Advance payments made to suppliers"
msgstr "Авансові платежі постачальникам"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Advanced"
msgstr "Розширено"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_partner_balance_report_handler
msgid "Aged Partner Balance Custom Handler"
msgstr "Спеціальний обробник протермінованого балансу партнера"

#. module: account_reports
#: model:account.report,name:account_reports.aged_payable_report
#: model:account.report.line,name:account_reports.aged_payable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ap
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_payable
msgid "Aged Payable"
msgstr "Протермінована оплата"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_payable_report_handler
msgid "Aged Payable Custom Handler"
msgstr "Обробник за терміном, що підлягає оплаті"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Payables"
msgstr "Протерміновані оплати"

#. module: account_reports
#: model:account.report,name:account_reports.aged_receivable_report
#: model:account.report.line,name:account_reports.aged_receivable_line
#: model:ir.actions.client,name:account_reports.action_account_report_ar
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_aged_receivable
msgid "Aged Receivable"
msgstr "Протермінована дебіторська заборгованість"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_aged_receivable_report_handler
msgid "Aged Receivable Custom Handler"
msgstr "Обробник застарілої дебіторської заборгованості"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Aged Receivables"
msgstr "Протерміновані дебіторські заборгованості"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/sales_report/filters/filters.js:0
msgid "All"
msgstr "Всі"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Journals"
msgstr "Усі журнали"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "All Payable"
msgstr "Всі дебітори"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "All Receivable"
msgstr "Всі кредитори"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "All Report Variants"
msgstr "Усі варіанти звіту"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid ""
"All selected companies or branches do not share the same Tax ID. Please "
"check the Tax ID of the selected companies."
msgstr ""
"Усі вибрані компанії або філії не мають однакового податкового номера. "
"Перевірте податкові ідентифікаційні номери вибраних компаній."

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_amount
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount
#: model:account.report.column,name:account_reports.customer_statement_amount
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__amount
msgid "Amount"
msgstr "Сума"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_amount_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_amount_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_amount_currency
#: model:account.report.column,name:account_reports.customer_statement_report_amount_currency
#: model:account.report.column,name:account_reports.partner_ledger_report_amount_currency
msgid "Amount Currency"
msgstr "Сума валюти"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Amount in currency: %s"
msgstr "Сума у валюті: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Lakhs"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Millions"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Amounts in Thousands"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Analytic"
msgstr "Аналітика"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__filter_analytic_groupby
msgid "Analytic Group By"
msgstr "Групування аналітики"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Analytic Simulations"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/line_name.xml:0
msgid "Annotate"
msgstr "Анотація"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
msgid "Annotation"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__annotations_ids
msgid "Annotations"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "As of %s"
msgstr "Як на %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Ascending"
msgstr "Висхідний"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period0
#: model:account.report.column,name:account_reports.aged_receivable_report_period0
msgid "At Date"
msgstr "На дату"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Attach a file"
msgstr "Долучити файл"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: code:addons/account_reports/static/src/components/journal_report/line_name.xml:0
msgid "Audit"
msgstr "Аудит"

#. module: account_reports
#: model:ir.ui.menu,name:account_reports.account_reports_audit_reports_menu
msgid "Audit Reports"
msgstr "Звіти аудиту"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avgcre0
msgid "Average creditors days"
msgstr "Середні дні кредиторів"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_avdebt0
msgid "Average debtors days"
msgstr "Середні дебіторські дні"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "B: %s"
msgstr "B: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model:account.report.column,name:account_reports.balance_sheet_balance
#: model:account.report.column,name:account_reports.cash_flow_report_balance
#: model:account.report.column,name:account_reports.customer_statement_report_balance
#: model:account.report.column,name:account_reports.executive_summary_column
#: model:account.report.column,name:account_reports.general_ledger_report_balance
#: model:account.report.column,name:account_reports.journal_report_balance
#: model:account.report.column,name:account_reports.partner_ledger_report_balance
#: model:account.report.column,name:account_reports.profit_and_loss_column
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Balance"
msgstr "Баланс"

#. module: account_reports
#: model:account.report,name:account_reports.balance_sheet
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_balancesheet0
#: model:ir.actions.client,name:account_reports.action_account_report_bs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_balance_sheet
msgid "Balance Sheet"
msgstr "Бухгалтерський баланс"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_balance_sheet_report_handler
msgid "Balance Sheet Custom Handler"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_current
msgid "Balance at Current Rate"
msgstr "Баланс за поточною ставкою"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_operation
msgid "Balance at Operation Rate"
msgstr "Баланс за операційною ставкою"

#. module: account_reports
#: model:account.report.column,name:account_reports.multicurrency_revaluation_report_balance_currency
msgid "Balance in Foreign Currency"
msgstr "Баланс в іноземній валюті"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid "Balance of '%s'"
msgstr "Баланс '%s'"

#. module: account_reports
#: model:account.report.line,name:account_reports.balance_bank
msgid "Balance of Bank"
msgstr "Баланс банку"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax advance payment account"
msgstr "Баланс рахунку авансового платежу податку"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (payable)"
msgstr "Баланс поточного рахунку податку (до сплати)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Balance tax current account (receivable)"
msgstr "Баланс поточного рахунку податку (дебіторська заборгованість)"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_bank_reconciliation
msgid "Bank Reconciliation"
msgstr "Узгодження виписок"

#. module: account_reports
#: model:account.report,name:account_reports.bank_reconciliation_report
msgid "Bank Reconciliation Report"
msgstr "Звіт узгодження банківської виписки"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_bank_reconciliation_report_handler
msgid "Bank Reconciliation Report Custom Handler"
msgstr "Спеціальний обробник звіту узгодження банківської виписки"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_bank_view0
msgid "Bank and Cash Accounts"
msgstr "Банківські та грошові рахунки"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Base Amount"
msgstr "Базова сума"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
msgid "Based on"
msgstr "Базується на"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Before"
msgstr "Перед"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__budget_id
msgid "Budget"
msgstr "Бюджет"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__budget_item_ids
msgid "Budget Item"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Budget Items"
msgstr "Пункти бюджету"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Budget Name"
msgstr "Назва бюджету"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Budget items can only be edited from account lines."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/redirectAction/redirectAction.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Cancel"
msgstr "Скасувати"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Cannot audit tax from another model than account.tax."
msgstr "Неможливо перевірити податок з іншої моделі, а не account.tax."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Cannot generate carryover values for all fiscal positions at once!"
msgstr ""
"Неможливо створити залишкові значення для всіх схем оподаткування одночасно!"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "Carryover"
msgstr "Перенесення"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover adjustment for tax unit"
msgstr "Коригування перенесення для податкової одиниці"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover can only be generated for a single column group."
msgstr "Перенесення можна створити лише для однієї групи стовпців."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover from %(date_from)s to %(date_to)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Carryover lines for: %s"
msgstr "Перенсти рядки для: %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash0
msgid "Cash"
msgstr "Готівка"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_cash_flow_report_handler
msgid "Cash Flow Report Custom Handler"
msgstr "Кастомний обробник звіту про рух грошових коштів"

#. module: account_reports
#: model:account.report,name:account_reports.cash_flow_report
#: model:ir.actions.client,name:account_reports.action_account_report_cs
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_cash_flow
msgid "Cash Flow Statement"
msgstr "Звіт про рух коштів"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, beginning of period"
msgstr "Гроші та їх еквіваленти на початок періоду"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash and cash equivalents, closing balance"
msgstr "Гроші та їх еквіваленти на кінець періоду"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from financing activities"
msgstr "Рух коштів фінансової діяльності"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from investing & extraordinary activities"
msgstr "Рух коштів від інвестицій та незвичайних дій"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from operating activities"
msgstr "Рух коштів від операційної діяльності"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash flows from unclassified activities"
msgstr "Рух коштів від некласифікованих дій"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash in"
msgstr "Гроші в"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash out"
msgstr "Гроші з "

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash paid for operating activities"
msgstr "Кошти, сплачені за операційну діяльність"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_received0
msgid "Cash received"
msgstr "Повернені гроші"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Cash received from operating activities"
msgstr "Кошти, отримані від операційної діяльності"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_spent0
msgid "Cash spent"
msgstr "Витрачені кошти"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_cash_surplus0
msgid "Cash surplus"
msgstr "Надлишок готівки"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_change_lock_date
msgid "Change Lock Date"
msgstr "Змінити дату блокування"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Check Partner(s) Email(s)"
msgstr "Перевірте електронні адреси партнера(ів)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "Check them"
msgstr "Перевірте їх"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Close"
msgstr "Закрити"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Closing Entry"
msgstr "Запис закриття"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_closing_bank_balance0
msgid "Closing bank balance"
msgstr "Залишок коштів у банку"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:account.report.column,name:account_reports.journal_report_code
msgid "Code"
msgstr "Код"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/filters/filter_code.xml:0
msgid "Codes:"
msgstr "Коди:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Columns"
msgstr "Стовпці"

#. module: account_reports
#: model:account.report.column,name:account_reports.general_ledger_report_communication
msgid "Communication"
msgstr "Зв'язок"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model,name:account_reports.model_res_company
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__company_ids
msgid "Companies"
msgstr "Компанії"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__company_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__company_id
msgid "Company"
msgstr "Компанія"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"Company %(company)s already belongs to a tax unit in %(country)s. A company "
"can at most be part of one tax unit per country."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Currency"
msgstr "Валюта компанії"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Company Only"
msgstr "Лише компанія"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Company Settings"
msgstr "Налаштування компанії"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Comparison"
msgstr "Порівняння"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_config_settings
msgid "Config Settings"
msgstr "Налаштування"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure start dates"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Configure your TAX accounts - %s"
msgstr "Налаштуйте ваші податкові рахунки - %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_config_settings.py:0
msgid "Configure your start dates"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Configure your tax accounts"
msgstr "Налаштуйте ваші рахунки податку"

#. module: account_reports
#: model:ir.model,name:account_reports.model_res_partner
msgid "Contact"
msgstr "Контакт"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_body
msgid "Contents"
msgstr "Містить"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_direct_costs0
msgid "Cost of Revenue"
msgstr "Собівартість продажу"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Could not expand term %(term)s while evaluating formula "
"%(unexpanded_formula)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Could not parse account_code formula from token '%s'"
msgstr "Не вдалося проаналізувати формулу account_code з токена '%s'"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__country_id
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Country"
msgstr "Країна"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr "Код країни"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
msgid "Create"
msgstr "Створити"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_composite_report_list
msgid "Create Composite Report"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Create Entry"
msgstr "Створити запис"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_create_report_menu
msgid "Create Menu Item"
msgstr "Створити елемент меню"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_uid
msgid "Created by"
msgstr "Створив"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__create_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__create_date
msgid "Created on"
msgstr "Створено"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_credit
#: model:account.report.column,name:account_reports.journal_report_credit
#: model:account.report.column,name:account_reports.partner_ledger_report_credit
#: model:account.report.column,name:account_reports.trial_balance_report_credit
msgid "Credit"
msgstr "Кредит"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_currency
#: model:account.report.column,name:account_reports.aged_receivable_report_currency
#: model:account.report.column,name:account_reports.bank_reconciliation_report_currency
#: model:account.report.column,name:account_reports.general_ledger_report_amount_currency
msgid "Currency"
msgstr "Валюта"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "Currency Rates (%s)"
msgstr "Курс валют (%s)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Currency:"
msgstr "Валюта:"

#. module: account_reports
#: model:account.report.column,name:account_reports.deferred_expense_current
#: model:account.report.column,name:account_reports.deferred_revenue_current
msgid "Current"
msgstr "Поточний"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_assets0
#: model:account.report.line,name:account_reports.account_financial_report_current_assets_view0
msgid "Current Assets"
msgstr "Оборотні активи"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities0
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities1
msgid "Current Liabilities"
msgstr "Короткострокові зобов’язання"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings_line_1
msgid "Current Year Retained Earnings"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_current_year_earnings0
msgid "Current Year Unallocated Earnings"
msgstr "Нерозподілений прибуток поточного періоду"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_ca_to_l0
msgid "Current assets to liabilities"
msgstr "Відношення активів до зобов’язань"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Custom Dates"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_id
msgid "Custom Handler Model"
msgstr "Модель кастомної обробки"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__custom_handler_model_name
msgid "Custom Handler Model Name"
msgstr "Назва моделі кастомної обробки"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid ""
"Custom engine _report_custom_engine_last_statement_balance_amount does not "
"support groupby"
msgstr ""
"Кастомний двигун _report_custom_engine_last_statement_balance_amount does не"
" підтримує групування"

#. module: account_reports
#: model:account.report,name:account_reports.customer_statement_report
#: model:ir.actions.client,name:account_reports.action_account_report_customer_statement
#: model:mail.template,name:account_reports.email_template_customer_statement
msgid "Customer Statement"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_customer_statement_report_handler
msgid "Customer Statement Custom Handler"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover/annotations_popover.xml:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_date
#: model:account.report.column,name:account_reports.general_ledger_report_date
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__date
msgid "Date"
msgstr "Дата"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Date cannot be empty"
msgstr "Дата не може бути чистою"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__date
msgid "Date considered as annotated by the annotation."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
msgid "Days"
msgstr "Дні"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.general_ledger_report_debit
#: model:account.report.column,name:account_reports.journal_report_debit
#: model:account.report.column,name:account_reports.partner_ledger_report_debit
#: model:account.report.column,name:account_reports.trial_balance_report_debit
msgid "Debit"
msgstr "Дебет"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Deductible"
msgstr "Франшиза"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "Deferrals have not yet been completely generated for this period."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "Deferrals have not yet been generated for this period."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Deferred Entries"
msgstr "Записи майбутніх періодів"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_expense
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_expense
msgid "Deferred Expense"
msgstr "Витрати майбутніх періодів"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_expense_report_handler
msgid "Deferred Expense Custom Handler"
msgstr "Кастомний обробник витрат майбутніх періодів"

#. module: account_reports
#: model:account.report,name:account_reports.deferred_expense_report
msgid "Deferred Expense Report"
msgstr "Звіт витрат майбутніх періодів"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_report_handler
msgid "Deferred Expense Report Custom Handler"
msgstr "Кастомний обробник звіту витрат майбутніх періодів"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_deferred_revenue
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_deferred_revenue
msgid "Deferred Revenue"
msgstr "Доходи майбутніх періодів"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_deferred_revenue_report_handler
msgid "Deferred Revenue Custom Handler"
msgstr "Кастомний обробник доходів майбутніх періодів"

#. module: account_reports
#: model:account.report,name:account_reports.deferred_revenue_report
msgid "Deferred Revenue Report"
msgstr "Звіт доходів майбутніх періодів"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Definition"
msgstr "Опис"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity
msgid "Delay units"
msgstr "Одиниці затримки"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Depending moves"
msgstr "Залежні проводки"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Descending"
msgstr "За спаданням"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Difference from rounding taxes"
msgstr "Різниця з округлення податків"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_line__display_custom_groupby_warning
msgid "Display Custom Groupby Warning"
msgstr "Відображати попередження кастомного групування"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__display_mail_composer
msgid "Display Mail Composer"
msgstr "Відобразити Mail Composer"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__display_name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__display_name
msgid "Display Name"
msgstr "Назва для відображення"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Document"
msgstr "Документ"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__doc_name
msgid "Documents Name"
msgstr "Назва документів"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__domain
msgid "Domain"
msgstr "Домен"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Domestic"
msgstr "Вітчизняний"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__checkbox_download
msgid "Download"
msgstr "Завантажити"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "Download Anyway"
msgstr "Завантажити у будь-якому випадку"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Download the Data Inalterability Check Report"
msgstr "Завантажте звіт перевірки непрацездатності даних"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Draft Entries"
msgstr "Чорнові записи"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Due"
msgstr "Борг"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
#: model:account.report.column,name:account_reports.customer_statement_report_date_maturity
#: model:account.report.column,name:account_reports.partner_ledger_report_date_maturity
msgid "Due Date"
msgstr "Установлений термін"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_sales
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_sales
msgid "EC Sales List"
msgstr "Список продажів EC "

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_ec_sales_report_handler
msgid "EC Sales Report Custom Handler"
msgstr "Кастомний обробник звіту продажів EC"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on non EC countries"
msgstr "Податок ЄС на країни, що не є ЄС"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "EC tax on same country"
msgstr "Податок ЄС на тій же країні"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_equity0
msgid "EQUITY"
msgstr "КАПІТАЛ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Editing a manual report line is not allowed in multivat setup when "
"displaying data from all fiscal positions."
msgstr ""
"Редагування рядка звіту вручну не дозволяється в налаштуваннях кількох "
"податків під час відображення даних з усіх схем оподаткування."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Editing a manual report line is not allowed when multiple companies are "
"selected."
msgstr ""
"Редагування рядка звіту вручну не дозволяється, якщо вибрано кілька "
"компаній."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__checkbox_send_mail
msgid "Email"
msgstr "Ел. пошта"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_template_id
msgid "Email template"
msgstr "Шаблон листа"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__enable_download
msgid "Enable Download"
msgstr "Увімкнути завантаження"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Enable Sections"
msgstr "Увімкнути підрозділи"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__enable_send_mail
msgid "Enable Send Mail"
msgstr "Увімкнути надсилання листів"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Enable more ..."
msgstr "Увімкнути більше ..."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "End Balance"
msgstr "Кінцевий баланс"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Month"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Quarter"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "End of Year"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Engine"
msgstr "Двигун"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Entries with partners with no VAT"
msgstr "Записи з партнерами без ПДВ"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Error message"
msgstr "Повідомлення про помилку"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/filters/filter_exchange_rate.xml:0
msgid "Exchange Rates"
msgstr "Курси валют"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_move_line__exclude_bank_lines
msgid "Exclude Bank Lines"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_search
msgid "Exclude Bank lines"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_account__exclude_provision_currency_ids
msgid "Exclude Provision Currency"
msgstr "Виключити резервну валюту"

#. module: account_reports
#: model:account.report.line,name:account_reports.multicurrency_revaluation_excluded
msgid "Excluded Accounts"
msgstr "Виключені рахунки"

#. module: account_reports
#: model:account.report,name:account_reports.executive_summary
#: model:ir.actions.client,name:account_reports.action_account_report_exec_summary
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_exec_summary
msgid "Executive Summary"
msgstr "Управлінський підсумок"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__expense_provision_account_id
msgid "Expense Account"
msgstr "Рахунок витрат"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_expense_provision_account_id
msgid "Expense Provision Account"
msgstr "Рахунок витрат резерву"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Expense Provision for %s"
msgstr "Забезпечення витрат на %s"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_expenses0
msgid "Expenses"
msgstr "Витрати"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_report_export_wizard
msgid "Export"
msgstr "Експорт"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Експортувати формат для бухгалтерських звітів"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__export_format_ids
msgid "Export to"
msgstr "Експортувати до"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Помічник експорту для бухгалтерських звітів"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
msgid "Expression"
msgstr "Вираз"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Expression labelled '%(label)s' of line '%(line)s' is being overwritten when"
" computing the current report. Make sure the cross-report aggregations of "
"this report only reference terms belonging to other reports."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__field_name
msgid "Field"
msgstr "Поле"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field %s does not exist on account.move.line, and is not supported by this "
"report's custom handler."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Field %s does not exist on account.move.line."
msgstr "Поле %s не існує на account.move.line."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Field %s of account.move.line cannot be used in a groupby expression."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field %s of account.move.line is not searchable and can therefore not be "
"used in a groupby expression."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Field 'Custom Handler Model' can only reference records inheriting from "
"[%s]."
msgstr ""
"Поле 'Модель кастомної обробки' може посилатися лише на записи, успадковані "
"від [%s]."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_content
msgid "File Content"
msgstr "Вміст файлу"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_file_download_error_wizard_form
msgid "File Download Errors"
msgstr "Помики завантаження файлу"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__file_name
msgid "File Name"
msgstr "Назва файлу"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Filters"
msgstr "Фільтри"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_aml_ir_filters.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Filters:"
msgstr "Фільтри:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_budget_tree
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_budget_tree
msgid "Financial Budgets"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_fiscal_position
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__fiscal_position_id
msgid "Fiscal Position"
msgstr "Схема оподаткування"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_fiscal_position.xml:0
msgid "Fiscal Position:"
msgstr "Схема оподаткування:"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__fpos_synced
msgid "Fiscal Positions Synchronised"
msgstr "Схеми оподаткування синхронізовано"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid ""
"Fiscal Positions should apply to all companies of the tax unit. You may want"
" to"
msgstr ""
"Схеми оподаткування повинні застосовуватися до всіх компаній податкової "
"одиниці. Можливо ви хочете"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Foreign currencies adjustment entry as of %s"
msgstr "Запис корегування іноземних валют, як %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Formula"
msgstr "Формула"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"From %(date_from)s\n"
"to  %(date_to)s"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_param
msgid "Function Parameter"
msgstr "Параметр функції"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__fun_to_call
msgid "Function to Call"
msgstr "Функція виклику"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
#: model:account.report,name:account_reports.general_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_general_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_general_ledger
msgid "General Ledger"
msgstr "Загальна бухгалтерська книга"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr "Кастомний обробник бухгалтерської книги"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Generate entry"
msgstr "Створити запис"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "Generated Documents"
msgstr "Згенеровані документи"

#. module: account_reports
#: model:account.report,name:account_reports.generic_ec_sales_report
msgid "Generic EC Sales List"
msgstr "Список продажів Generic EC"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler
msgid "Generic Tax Report Custom Handler"
msgstr "Кастомний обробник податкового звіту Generic"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_account_tax
msgid "Generic Tax Report Custom Handler (Account -> Tax)"
msgstr "Кастмоний обробник податкового звіту Generic (Рахунок -> Податок)"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_generic_tax_report_handler_tax_account
msgid "Generic Tax Report Custom Handler (Tax -> Account)"
msgstr "Кастомний обробник податкового звіту Generic (Податок -> Рахунок)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main
msgid "Global Tax Summary"
msgstr "Загальний податковий підсумок"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Goods"
msgstr "Товари"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Grid"
msgstr "Сітка"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_gross_profit0
msgid "Gross Profit"
msgstr "Валовий дохід"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gross_profit0
msgid "Gross profit"
msgstr "Валовий дохід"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_gpmargin0
msgid "Gross profit margin (gross profit / operating income)"
msgstr "Маржа валового прибутку (валовий прибуток / операційний дохід)"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Group By"
msgstr "Групувати за"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_horizontal_group_form
msgid "Group Name"
msgstr "Назва групи"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Group by"
msgstr "Групувати за"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Grouped Deferral Entry of %s"
msgstr "Згруповано записи майбутніх періодів %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/filter_extra_options.xml:0
msgid "Hide Account"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/partner_ledger/filter_extra_options.xml:0
msgid "Hide Debit/Credit"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hide lines at 0"
msgstr "Приховати рядки на 0"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Hierarchy and Subtotals"
msgstr "Ієрархія та підсумок"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__horizontal_group_id
msgid "Horizontal Group"
msgstr "Горизонтальне групування"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
msgid "Horizontal Group:"
msgstr "Горизонтальне групування:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_account_report_horizontal_groups
#: model:ir.model.fields,field_description:account_reports.field_account_report__horizontal_group_ids
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_horizontal_groups
msgid "Horizontal Groups"
msgstr "Горизонтальні групи"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group
msgid "Horizontal group for reports"
msgstr "Горизонтальне групування для звітів"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_horizontal_group_rule
msgid "Horizontal group rule for reports"
msgstr "Правило горизонтального групування для звітів"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Horizontal:"
msgstr "Горизонтально:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "How often tax returns have to be made"
msgstr "Як часто потрібно робити податкові декларації"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__id
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__id
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__id
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__id
msgid "ID"
msgstr "ID"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Impact On Grid"
msgstr "Закріпити сітку"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Impacted Tax Grids"
msgstr "Уражені податкові сітки"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "In %s"
msgstr "В %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Inactive"
msgstr "Неактивний"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/journal_report/filter_extra_options.xml:0
msgid "Include Payments"
msgstr "Включно з платежами"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Including Analytic Simulations"
msgstr "Включно з аналітичним моделюванням"

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_payments
#: model:account.report.line,name:account_reports.unreconciled_last_statement_payments
msgid "Including Unreconciled Payments"
msgstr "Включаючи неузгоджені платежі"

#. module: account_reports
#: model:account.report.line,name:account_reports.no_statement_unreconciled_receipt
#: model:account.report.line,name:account_reports.unreconciled_last_statement_receipts
msgid "Including Unreconciled Receipts"
msgstr "Включаючи неузгоджені платежі"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__income_provision_account_id
msgid "Income Account"
msgstr "Рахунок доходів"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_revaluation_income_provision_account_id
msgid "Income Provision Account"
msgstr "Рахунок резерву доходу"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Income Provision for %s"
msgstr "Забезпечення доходів для %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
msgid "Inconsistent Statements"
msgstr "Невідповідні виписки"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Inconsistent data: more than one external value at the same date for a "
"'most_recent' external line."
msgstr ""
"Непослідовні дані: більше ніж одне зовнішнє значення на ту саму дату для "
"зовнішнього рядка 'most_recent'."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Inconsistent report_id in options dictionary. Options says "
"%(options_report)s; report is %(report)s."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
msgid "Initial Balance"
msgstr "Початковий баланс"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Integer Rounding"
msgstr "Цілочисельне округлення"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filters.js:0
msgid "Intervals cannot be smaller than 1"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "Intra-community taxes are applied on"
msgstr "Застосовуються податки всередині спільноти"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Invalid domain formula in expression \"%(expression)s\" of line "
"\"%(line)s\": %(formula)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid method “%s”"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Invalid subformula in expression \"%(expression)s\" of line \"%(line)s\": "
"%(subformula)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Invalid token '%(token)s' in account_codes formula '%(formula)s'"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_aging.xml:0
#: model:account.report.column,name:account_reports.aged_payable_report_invoice_date
#: model:account.report.column,name:account_reports.aged_receivable_report_invoice_date
#: model:account.report.column,name:account_reports.customer_statement_report_invoicing_date
#: model:account.report.column,name:account_reports.partner_ledger_report_invoicing_date
msgid "Invoice Date"
msgstr "Дата рахунку"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_search
msgid "Invoice lines"
msgstr "Рядки рахунку"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__is_account_coverage_report_available
msgid "Is Account Coverage Report Available"
msgstr "Чи доступний звіт про покриття рахунку"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "It seems there is some depending closing move to be posted"
msgstr "Здається, потрібно опублікувати деяку повʼязану закриваючу проводку"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid ""
"It's not possible to select a budget with the horizontal group feature."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid ""
"It's not possible to select a horizontal group with the budget feature."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__item_ids
msgid "Items"
msgstr "Елементи"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_journal_code
#: model:ir.model,name:account_reports.model_account_journal
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_journal_id
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_journal_id
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Journal"
msgstr "Журнал"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_ja
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_ja
msgid "Journal Audit"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move
msgid "Journal Entry"
msgstr "Запис у журналі"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_move_line
msgid "Journal Item"
msgstr "Елемент журналу"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_trial_balance_report.py:0
#: code:addons/account_reports/models/bank_reconciliation_report.py:0
#: code:addons/account_reports/static/src/components/general_ledger/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Journal Items"
msgstr "Елементи журналу"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Journal Items for Tax Audit"
msgstr "Елементи журналу для податкового аудиту"

#. module: account_reports
#: model:account.report,name:account_reports.journal_report
msgid "Journal Report"
msgstr "Звіт журналу"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_journal_report_handler
msgid "Journal Report Custom Handler"
msgstr "Кастомний обробник звіту журналу"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Journal items with archived tax tags"
msgstr "Елементи журналу з заархівованими тегами податку"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Journals"
msgstr "Журнали"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Journals:"
msgstr "Журнали:"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_view0
msgid "LIABILITIES"
msgstr "ЗОБОВ'ЯЗАННЯ"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_liabilities_and_equity_view0
msgid "LIABILITIES + EQUITY"
msgstr "ЗОБОВ'ЯЗАННЯ + КАПІТАЛ"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
#: model:account.report.column,name:account_reports.bank_reconciliation_report_label
msgid "Label"
msgstr "Мітка"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_lang
msgid "Lang"
msgstr "Мова"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Last Statement balance + Transactions since statement"
msgstr "Баланс останньої виписки + Транзакції з останньої виписки"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_uid
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_uid
msgid "Last Updated by"
msgstr "Востаннє оновив"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget_item__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_file_download_error_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__write_date
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__write_date
msgid "Last Updated on"
msgstr "Останнє оновлення"

#. module: account_reports
#: model:account.report.line,name:account_reports.last_statement_balance
msgid "Last statement balance"
msgstr "Останній баланс по виписці"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Later"
msgstr "Пізніше"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_cost_sales0
msgid "Less Costs of Revenue"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_expense0
msgid "Less Operating Expenses"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_depreciation0
msgid "Less Other Expenses"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__line_id
msgid "Line"
msgstr "Рядок"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid ""
"Line '%(child)s' is configured to appear before its parent '%(parent)s'. "
"This is not allowed."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Line without formula"
msgstr "Рядок без формули"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Lines"
msgstr "Рядки"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Load more..."
msgstr "Завантажити більше..."

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_attachments_widget
msgid "Mail Attachments Widget"
msgstr "Віджет прикріплення пошти"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__main_company_id
msgid "Main Company"
msgstr "Головна компанія"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__main_company_id
msgid ""
"Main company of this unit; the one actually reporting and paying the taxes."
msgstr ""
"Головна компанія цієї одиниці; той, хто фактично звітує та сплачує податки."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid "Make Adjustment Entry"
msgstr "Зробити запис корегування"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_report_file_download_error_wizard
msgid "Manage the file generation errors from report exports."
msgstr "Керуйте помилками генерації файлів із експортованих звітів."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual value"
msgstr "Ручне значення"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Manual values"
msgstr "Ручні значення"

#. module: account_reports
#: model:account.report.column,name:account_reports.partner_ledger_report_matching_number
msgid "Matching"
msgstr "Узгодження"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__company_ids
msgid "Members of this unit"
msgstr "Члени цієї одиниці"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Method '%(method_name)s' must start with the '%(prefix)s' prefix."
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.misc_operations
msgid "Misc. operations"
msgstr "Інші операції"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mode
msgid "Mode"
msgstr "Режим"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group_rule__res_model_name
msgid "Model"
msgstr "Модель"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Month"
msgstr "Місяць"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_budget_form
msgid "Months"
msgstr "Місяці"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Multi-Ledger:"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Multi-ledger"
msgstr "Кілька бухгалтерських книг"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_report_handler
msgid "Multicurrency Revaluation Report Custom Handler"
msgstr "Кастомний обробник звіту мультивалютної переоцінки"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_multicurrency_revaluation_wizard
msgid "Multicurrency Revaluation Wizard"
msgstr "Помічник переоцінки мультивалютності"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_report_send__mode__multi
msgid "Multiple Recipients"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for fiscal position %(position)s after %(period_start)s. There should be at most one. \n"
" %(closing_entries)s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid ""
"Multiple draft tax closing entries exist for your domestic region after %(period_start)s. There should be at most one. \n"
" %(closing_entries)s"
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model:account.report.line,name:account_reports.journal_report_line
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__name
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__name
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__name
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__name
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Name"
msgstr "Назва"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_reports_export_wizard__doc_name
msgid "Name to give to the generated documents."
msgstr "Назва, що надається згенерованим документам."

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profit0
#: model:account.report.line,name:account_reports.account_financial_report_net_profit0
msgid "Net Profit"
msgstr "Чистий дохід"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_net_assets0
msgid "Net assets"
msgstr "Чисті активи"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_cash_flow_report.py:0
msgid "Net increase in cash and cash equivalents"
msgstr "Чистий грошовий потік"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_npmargin0
msgid "Net profit margin (net profit / revenue)"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Never miss a tax deadline."
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "No"
msgstr "Ні"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "No Comparison"
msgstr "Немає порівняння"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No Journal"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "No VAT number associated with your company. Please define one."
msgstr "Номер ПДВ не пов’язаний з вашою компанією. Будь ласка, визначте один."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No adjustment needed"
msgstr "Не потрібно ніяких корегувань"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "No data to display !"
msgstr "Немає даних для відображення!"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/chart_template.py:0
msgid "No default miscellaneous journal could be found for the active company"
msgstr ""
"Для активної компанії не вдалося знайти журнал різних операцій за "
"замовчуванням"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "No entry to generate."
msgstr "Немає запису для створення."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "No provision needed was found."
msgstr "Не знайдено жодних необхідних резервувань."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Non Trade Partners"
msgstr "Неторговельні партнери"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Payable"
msgstr "Неторговельні кредитори"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Non Trade Receivable"
msgstr "Неторговельні дебітори"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Non-Deductible"
msgstr "Не підлягає франшизі"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_horizontal_groups.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/sales_report/filters/filters.js:0
msgid "None"
msgstr "Немає"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Not Started"
msgstr "Не почалося"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Number of periods cannot be smaller than 1"
msgstr "Кількість періодів не може бути менше ніж 1"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_off_sheet
msgid "OFF BALANCE SHEET ACCOUNTS"
msgstr "ВИМКНУТИ РАХУНКИ ЗВІТУ БАЛАНСУ"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: code:addons/account_reports/static/src/components/aged_partner_balance/filters.js:0
msgid "Odoo Warning"
msgstr "Попередження Odoo"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period5
#: model:account.report.column,name:account_reports.aged_receivable_report_period5
msgid "Older"
msgstr "Старіший"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/report_export_wizard.py:0
msgid "One of the formats chosen can not be exported in the DMS"
msgstr "Один із вибраних форматів не можна експортувати в DMS"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Only Billing Administrators are allowed to change lock dates!"
msgstr "Лише адміністратори рахунків можуть перевіряти дати блокування!"

#. module: account_reports
#: model:ir.actions.server,name:account_reports.action_account_reports_customer_statements
msgid "Open Customer Statements"
msgstr ""

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_financial_year_op
msgid "Opening Balance of Financial Year"
msgstr "Відкритий баланс звітного періоду"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_operating_income0
msgid "Operating Income (or Loss)"
msgstr ""

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_expression_form
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Options"
msgstr "Опції"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Options:"
msgstr "Опції:"

#. module: account_reports
#: model:account.report.line,name:account_reports.outstanding
msgid "Outstanding Receipts/Payments"
msgstr "Неузгоджені платежі"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "PDF"
msgstr "PDF"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard__report_id
msgid "Parent Report Id"
msgstr " Id батьківського звіту"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_reports_export_wizard_format__export_wizard_id
msgid "Parent Wizard"
msgstr "Батьківський помічник"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
#: model:account.report.column,name:account_reports.general_ledger_report_partner_name
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__partner_ids
msgid "Partner"
msgstr "Партнер"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Partner Categories"
msgstr "Категорії партнерів"

#. module: account_reports
#: model:account.report,name:account_reports.partner_ledger_report
#: model:ir.actions.client,name:account_reports.action_account_report_partner_ledger
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_partner_ledger
msgid "Partner Ledger"
msgstr "Розрахунки з партнерами"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_partner_ledger_report_handler
msgid "Partner Ledger Custom Handler"
msgstr "Кастомний обробник розрахунків з партнерами"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner is bad"
msgstr "Поганий партнер"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
#: code:addons/account_reports/static/src/components/partner_ledger/line_name.xml:0
msgid "Partner is good"
msgstr "Хороший партнер"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Partner(s) should have an email address."
msgstr "Партнери повинні мати електронну адресу."

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Partners"
msgstr "Партнери"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners Categories:"
msgstr "Категорії партнерів:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filters
msgid "Partners:"
msgstr "Партнери"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.mail_activity_type_tax_report_to_pay
msgid "Pay Tax"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Pay tax: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Payable"
msgstr "Кредитор"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Payable tax amount"
msgstr "Сума податку, що підлягає оплаті"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_current_liabilities_payable
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_creditors0
msgid "Payables"
msgstr "Кредиторська заборгованість"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_performance0
msgid "Performance"
msgstr "Досягнення"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Period"
msgstr "Період"

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period1
#: model:account.report.column,name:account_reports.aged_receivable_report_period1
msgid "Period 1"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period2
#: model:account.report.column,name:account_reports.aged_receivable_report_period2
msgid "Period 2"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period3
#: model:account.report.column,name:account_reports.aged_receivable_report_period3
msgid "Period 3"
msgstr ""

#. module: account_reports
#: model:account.report.column,name:account_reports.aged_payable_report_period4
#: model:account.report.column,name:account_reports.aged_receivable_report_period4
msgid "Period 4"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Period order"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_account_financial_year_op__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_periodicity
#: model:ir.model.fields,help:account_reports.field_res_config_settings__account_tax_periodicity
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Periodicity"
msgstr "Періодичність"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity
msgid "Periodicity in month"
msgstr "Періодичність у місяці"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Periods"
msgstr "Періоди"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_analytic_groupby.xml:0
msgid "Plans"
msgstr "Плани"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/budget.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Please enter a valid budget name."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Please select a mail template to send multiple statements."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Please select the main company and its branches in the company selector to "
"proceed."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Please set the deferred accounts in the accounting settings."
msgstr ""
"Будь ласка, встановіть рахунки майбутніх періодів в налаштуваннях бухобліку."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Please set the deferred journal in the accounting settings."
msgstr ""
"В налаштуваннях бухгалтерського обліку встановіть журнал майбутніх періодів."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Please specify the accounts necessary for the Tax Closing Entry."
msgstr "Будь ласка, вкажіть рахунки, необхідні для запису закриття податку."

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_fixed_assets_view0
msgid "Plus Fixed Assets"
msgstr "Плюс основні засоби"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_assets_view0
msgid "Plus Non-current Assets"
msgstr "Додати довгострокові активи"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_non_current_liabilities0
msgid "Plus Non-current Liabilities"
msgstr "Додати постійні зобов'язання"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_other_income0
msgid "Plus Other Income"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_position0
msgid "Position"
msgstr "Вакансія"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Post"
msgstr "Опублікувати"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Posted Entries"
msgstr "Опубліковані записи"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_prepayements0
msgid "Prepayments"
msgstr "Аванси"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__preview_data
msgid "Preview Data"
msgstr "Попередній перегляд даних"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Previous Period"
msgstr "Попередній період"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Previous Periods"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Previous Year"
msgstr "Попередній рік"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Previous Years"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings_line_2
msgid "Previous Years Retained Earnings"
msgstr ""

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_previous_year_earnings0
msgid "Previous Years Unallocated Earnings"
msgstr "Нерозподілені прибутки попередніх років"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Print & Send"
msgstr "Надрукувати та надіслати"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Proceed"
msgstr "Продовжити"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_multicurrency_revaluation_wizard
msgid ""
"Proceed with caution as there might be an existing adjustment for this "
"period ("
msgstr ""
"Будьте обережні, оскільки для цього періоду може існувати корегування ("

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Product"
msgstr "Товар"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/groupby.xml:0
msgid "Product Category"
msgstr "Категорія товару"

#. module: account_reports
#: model:account.report,name:account_reports.profit_and_loss
#: model:ir.actions.client,name:account_reports.action_account_report_pl
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_profit_and_loss
msgid "Profit and Loss"
msgstr "Доходи і витрати"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_profitability0
msgid "Profitability"
msgstr "Прибутковість"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Proposition of tax closing journal entry."
msgstr "Пропозиція запису журналу закриття податку."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Provision for %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"
msgstr "Забезпечення для %(for_cur)s (1 %(comp_cur)s = %(rate)s %(for_cur)s)"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Quarter"
msgstr "Чверть"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/line_name.xml:0
msgid "Rates"
msgstr "Ставки"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_aged_partner_balance.py:0
#: code:addons/account_reports/models/account_report.py:0
msgid "Receivable"
msgstr "Дебітор"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Receivable tax amount"
msgstr "Сума податку на дебіторську заборгованість"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_debtors0
#: model:account.report.line,name:account_reports.account_financial_report_receivable0
msgid "Receivables"
msgstr "Дебіторська заборгованість"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_partner_ids
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Recipients"
msgstr "Одержувачі"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_reports_journal_dashboard_kanban_view
msgid "Reconciliation Report"
msgstr "Узгоджений звіт"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_financial_year_op__account_tax_periodicity_reminder_day
#: model:ir.model.fields,field_description:account_reports.field_res_config_settings__account_tax_periodicity_reminder_day
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "Reminder"
msgstr "Нагадування"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__report_id
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__account_report_id
msgid "Report"
msgstr "Звіт"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Report Line"
msgstr "Рядок звіту"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Report Name"
msgstr "Назва звіту"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__report_options
msgid "Report Options"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Report lines mentioning the account code"
msgstr "Рядки звіту із зазначенням коду рахунку"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_variant.xml:0
msgid "Report:"
msgstr "Звіт:"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Reporting"
msgstr "Звітність"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__report_ids
msgid "Reports"
msgstr "Звіти"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "Reset to Standard"
msgstr "Скинути до стандарту"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_retained_earnings0
msgid "Retained Earnings"
msgstr "Збережений прибуток"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_return_investment0
msgid "Return on investments (net profit / assets)"
msgstr "Повернення інвестицій (чистий прибуток / активи)"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_income0
#: model:account.report.line,name:account_reports.account_financial_report_revenue0
msgid "Revenue"
msgstr "Дохід"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__reversal_date
msgid "Reversal Date"
msgstr "Дата сторнування"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "Reversal of Grouped Deferral Entry of %s"
msgstr "Повернення групового запису майбутніх періодів %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/multicurrency_revaluation.py:0
msgid "Reversal of: %s"
msgstr "Сторнування: %s"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_account_report_search
msgid "Root Report"
msgstr "Кореневий звіт"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_horizontal_group__rule_ids
msgid "Rules"
msgstr "Правила"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
msgid "Same Period Last Year"
msgstr "Той самий період минулого року"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/search_bar/search_bar.xml:0
msgid "Search..."
msgstr "Пошук..."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "Sections"
msgstr "Розділи"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_customer_statement.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send"
msgstr "Надіслати"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send %s Statement"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__send_and_print_values
msgid "Send And Print Values"
msgstr "Надіслати та друкувати значення"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__send_mail_readonly
msgid "Send Mail Readonly"
msgstr "Надіслати  лист лише для читання"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Send Partner Ledgers"
msgstr ""

#. module: account_reports
#: model:ir.actions.server,name:account_reports.ir_cron_account_report_send_ir_actions_server
msgid "Send account reports automatically"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Send tax report: %s"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Sending statements"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_budget__sequence
msgid "Sequence"
msgstr "Послідовність"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Services"
msgstr "Послуги"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "Set as Checked"
msgstr "Встановити як Позначено"

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_report_executivesummary_st_cash_forecast0
msgid "Short term cash forecast"
msgstr "Короткостроковий грошовий прогноз"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_extra_options.xml:0
msgid "Show Account"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_budgets.xml:0
msgid "Show All Accounts"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/filter_extra_options.xml:0
msgid "Show Currency"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_multicurrency_revaluation_wizard__show_warning_move_id
msgid "Show Warning Move"
msgstr "Показувати попередження переміщення"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__account_report_send__mode__single
msgid "Single Recipient"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "Show already generated deferrals."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "Some"
msgstr "Деякі"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "Some journal items appear to point to obsolete report lines."
msgstr "Здається, деякі елементи журналу вказують на застарілі рядки звіту."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Specific Date"
msgstr "Спеціальна дата"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_representative_id
msgid ""
"Specify an Accounting Firm that will act as a representative when exporting "
"reports."
msgstr ""
"Укажіть бухгалтерську фірму, яка виступатиме представником під час експорту "
"звітів."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Split Horizontally"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report__tax_closing_start_date
msgid "Start Date"
msgstr "Початкова дата"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_periodicity_reminder_day
msgid "Start from"
msgstr "Початок з"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Starting Balance"
msgstr "Початковий баланс"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/aged_partner_balance/line_name/line_name.xml:0
msgid "Statement"
msgstr "Виписка"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "Statements are being sent in the background."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Subformula"
msgstr "Підформула"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__mail_subject
msgid "Subject"
msgstr "Тема"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_send_form
msgid "Subject..."
msgstr "Тема..."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "T: %s"
msgstr "T: %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_partner.xml:0
msgid "Tags"
msgstr "Мітки"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Tax Amount"
msgstr "Сума податків"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_journal_report_taxes_summary
msgid "Tax Applied"
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_alert
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_alert
msgid "Tax Closing Alert"
msgstr "Сповіщення закриття податку"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_bank_statement_line__tax_closing_report_id
#: model:ir.model.fields,field_description:account_reports.field_account_move__tax_closing_report_id
msgid "Tax Closing Report"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Tax Declaration"
msgstr "Податкова декларація"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Tax Grids"
msgstr "Сітки податків"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_tax_unit__vat
msgid "Tax ID"
msgstr "ІПН"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.company_information
msgid "Tax ID:"
msgstr "ID податку:"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Paid Adjustment"
msgstr "Коригування податку"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/filters/filter_date.xml:0
msgid "Tax Period"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid "Tax Received Adjustment"
msgstr "Коригування отриманого податку"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.tax_closing_activity_type
#: model:mail.activity.type,summary:account_reports.tax_closing_activity_type
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid "Tax Report"
msgstr "Податковий звіт"

#. module: account_reports
#: model:mail.activity.type,name:account_reports.mail_activity_type_tax_report_to_be_sent
msgid "Tax Report Ready"
msgstr "Податковий звіт готовий"

#. module: account_reports
#: model:ir.actions.client,name:account_reports.action_account_report_gt
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_gt
msgid "Tax Return"
msgstr "Повернення податку"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid "Tax Return Periodicity"
msgstr "Періодичність повернення податку"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_tax_unit
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "Tax Unit"
msgstr "Одиниця податку"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_tax_unit.xml:0
msgid "Tax Unit:"
msgstr "Одиниця податку:"

#. module: account_reports
#: model:ir.actions.act_window,name:account_reports.action_view_tax_units
#: model:ir.model.fields,field_description:account_reports.field_res_company__account_tax_unit_ids
#: model:ir.ui.menu,name:account_reports.menu_view_tax_units
msgid "Tax Units"
msgstr "Одиниці податку"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_mail_activity__account_tax_closing_params
msgid "Tax closing additional params"
msgstr ""

#. module: account_reports
#: model:mail.activity.type,summary:account_reports.mail_activity_type_tax_report_to_pay
msgid "Tax is ready to be paid"
msgstr ""

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__mail_activity_type__category__tax_report
msgid "Tax report"
msgstr "Податковий звіт"

#. module: account_reports
#: model:mail.activity.type,summary:account_reports.mail_activity_type_tax_report_to_be_sent
msgid "Tax report is ready to be sent to the administration"
msgstr "Податкова звітність готова до відправки в адміністрацію"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/res_company.py:0
msgid "Tax return"
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
msgid "Taxes"
msgstr "Податки"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/static/src/components/journal_report/line/line.xml:0
msgid "Taxes Applied"
msgstr "Податки застосовуються"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__fpos_synced
msgid ""
"Technical field indicating whether Fiscal Positions exist for all companies "
"in the unit"
msgstr ""
"Технічне поле, яке вказує, чи існують схеми оподаткування для всіх компаній "
"в одиниці"

#. module: account_reports
#: model:ir.model,name:account_reports.model_ir_actions_account_report_download
msgid "Technical model for accounting report downloads"
msgstr "Технічна модель для завантажень бухгалтерських звітів"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/ellipsis/ellipsis.js:0
msgid "Text copied"
msgstr "Текст скопійовано"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The Accounts Coverage Report is not available for this report."
msgstr "Звіт про покриття рахунків недоступний для цього звіту."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_name/popover_line/annotation_popover_line.js:0
msgid "The annotation shouldn't have an empty value."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_annotation__text
msgid "The annotation's content."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"The attachments of the tax report can be found on the %(link_start)sclosing "
"entry%(link_end)s of the representative company."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "The column '%s' is not available for this report."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid ""
"The country detected for this VAT number does not match the one set on this "
"Tax Unit."
msgstr ""
"Країна, визначена для цього номера платника ПДВ, не збігається з країною, "
"указаною для цієї податкової одиниці."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__country_id
msgid ""
"The country in which this tax unit is used to group your companies' tax "
"reports declaration."
msgstr ""
"Країна, в якій ця одиниця оподаткування використовується для групування "
"податкових звітів ваших компаній."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "The currency rate cannot be equal to zero"
msgstr "Курс валюти не може дорівнювати нулю"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "The current balance in the"
msgstr "Поточний баланс на"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid ""
"The currently selected dates don't match a tax period. The closing entry "
"will be created for the closest-matching period according to your "
"periodicity setup."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/deferred_reports/warnings.xml:0
msgid "The entry that will be generated will take them into account."
msgstr "У записі, який буде згенеровано, їх буде враховано."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__fiscal_position_id
msgid "The fiscal position used while annotating."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__line_id
msgid "The id of the annotated line."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_report_annotation__report_id
msgid "The id of the annotated report."
msgstr ""

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_tax_unit__vat
msgid "The identifier to be used when submitting a report for this unit."
msgstr ""
"Ідентифікатор, який буде використовуватися під час подання звіту для цього "
"підрозділу."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "The main company of a tax unit has to be part of it."
msgstr "Головна компанія одиниці податку повинна бути її частиною."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__account_tax_unit_ids
msgid "The tax units this company belongs to."
msgstr "Одиниця податку, до яких належить ця компанія."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "The used operator is not supported for this expression."
msgstr "Використаний оператор не підтримується для цього виразу."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "There are"
msgstr "Там є"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid ""
"There are currently reports waiting to be sent, please try again later."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/account_report.xml:0
msgid "There is no data to display for the given filters."
msgstr "Немає даних для відображення для вказаних фільтрів."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This account exists in the Chart of Accounts but is not mentioned in any "
"line of the report"
msgstr ""
"Цей рахунок існує в Плані рахунків, але не згадується в жодному рядку звіту"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This account is reported in a line of the report but does not exist in the "
"Chart of Accounts"
msgstr ""
"Цей рахунок відображається в рядку звіту, але не існує в Плані рахунків"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported in multiple lines of the report"
msgstr "Цей рахунок відображається в кількох рядках звіту"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This account is reported multiple times on the same line of the report"
msgstr "Цей рахунок звітується кілька разів в одному рядку звіту"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"This allows you to choose the position of totals in your financial reports."
msgstr "Це дозволяє вибрати позицію підсумків у фінансових звітах."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid ""
"This company is part of a tax unit. You're currently not viewing the whole "
"unit."
msgstr ""
"Ця компанія є частиною податкової одиниці. Зараз ви не переглядаєте всю "
"одиницю."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.js:0
msgid ""
"This line and all its children will be deleted. Are you sure you want to "
"proceed?"
msgstr ""
"Цей рядок і всі його дочірні елементи буде видалено. Ви впевнені, що бажаєте"
" продовжити?"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.xml:0
#, python-format
msgid "This line is out of sequence."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/widgets/account_report_x2many/account_report_x2many.xml:0
#, python-format
msgid ""
"This line is placed before its parent, which is not allowed. You can fix it "
"by dragging it to the proper position."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_line_form
msgid "This line uses a custom user-defined 'Group By' value."
msgstr "Цей рядок використовує визначене користувачем значення «Групування»."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid "This option hides lines with a value of 0"
msgstr "Ця опція приховує рядки зі значенням 0"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This report already has a menuitem."
msgstr "У цьому звіті вже є пункт меню."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid ""
"This report contains inconsistencies. The affected lines are marked with a "
"warning."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "This report only displays the data of the active company."
msgstr "Цей звіт відображає лише дані активної компанії."

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.account_report_form
msgid ""
"This report uses report-specific code.\n"
"                        You can customize it manually, but any change in the parameters used for its computation could lead to errors."
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid ""
"This report uses the CTA conversion method to consolidate multiple companies using different currencies,\n"
"        which can lead the report to be unbalanced."
msgstr ""

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "This subformula references an unknown expression: %s"
msgstr "Ця підформула посилається на невідомий вираз: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"This tag is reported in a line of the report but is not linked to any "
"account of the Chart of Accounts"
msgstr ""
"Цей тег відображається в рядку звіту, але не пов’язаний з жодним рахунком "
"Плану рахунків"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_move_form_vat_return
msgid ""
"This tax closing entry is posted, but the tax lock date is earlier than the "
"covered period's last day. You might need to reset it to draft and refresh "
"its content, in case other entries using taxes have been posted in the "
"meantime."
msgstr ""
"Цей запис про закриття податку опубліковано, але дата податкового блокування"
" передує останньому дню звітного періоду. Можливо, вам знадобиться скинути "
"його до чернетки та оновити його вміст, якщо тим часом було опубліковано "
"інші записи з використанням податків."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "Today"
msgstr "Сьогодні"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
#: code:addons/account_reports/models/account_general_ledger.py:0
#: code:addons/account_reports/models/account_journal_report.py:0
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model:account.report.column,name:account_reports.aged_payable_report_total
#: model:account.report.column,name:account_reports.aged_receivable_report_total
msgid "Total"
msgstr "Разом"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Total %s"
msgstr "Разом %s"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Trade Partners"
msgstr "Торгові партнери"

#. module: account_reports
#: model:account.report.line,name:account_reports.transaction_without_statement
msgid "Transactions without statement"
msgstr "Транзакції без виписок"

#. module: account_reports
#: model:account.report,name:account_reports.trial_balance_report
#: model:ir.actions.client,name:account_reports.action_account_report_coa
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_coa
msgid "Trial Balance"
msgstr "Оборотно-сальдова відомість"

#. module: account_reports
#: model:ir.model,name:account_reports.model_account_trial_balance_report_handler
msgid "Trial Balance Custom Handler"
msgstr "Кастомний' обробник пробного балансу"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Triangular"
msgstr "Трикутний"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Trying to dispatch an action on a report not compatible with the provided "
"options."
msgstr "Спроба відправити дію зі звітом, несумісну з наданими параметрами."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid ""
"Trying to expand a group for a line which was not generated by a report "
"line: %s"
msgstr ""
"Спроба розширити групу для рядка, який не був створений рядком звіту: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand a line without an expansion function."
msgstr "Спроба розгорнути рядок без функції розширення."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Trying to expand groupby results on lines without a groupby value."
msgstr "Спроба розгорнути результати groupby у рядках без значення groupby."

#. module: account_reports
#: model:account.report.line,name:account_reports.account_financial_unaffected_earnings0
msgid "Unallocated Earnings"
msgstr "Нерозподілені прибутки"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#, python-format
msgid "Undefined"
msgstr "Невизначений"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
msgid "Unfold All"
msgstr "Розгорнути все"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown"
msgstr "Невідомо"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
msgid "Unknown Partner"
msgstr "Невідомий партнер"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown bound criterium: %s"
msgstr "Невідомий обмежений критерій: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Unknown date scope: %s"
msgstr "Невідомий діапазон дат: %s"

#. module: account_reports
#: model:account.report,name:account_reports.multicurrency_revaluation_report
#: model:ir.actions.client,name:account_reports.action_account_report_multicurrency_revaluation
#: model:ir.ui.menu,name:account_reports.menu_action_account_report_multicurrency_revaluation
msgid "Unrealized Currency Gains/Losses"
msgstr "Нереалізовані валютні доходи/витрати"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_extra_options.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "Unreconciled Entries"
msgstr "Неузгоджені записи"

#. module: account_reports
#: model:account.report.column,name:account_reports.account_financial_report_ec_sales_vat
msgid "VAT Number"
msgstr "ІПН"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "VAT Periodicity"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line/popover/debug_popover.xml:0
msgid "Value"
msgstr "Значення"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "Vat closing from %(date_from)s to %(date_to)s"
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "View"
msgstr "Перегляд"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Bank Statement"
msgstr "Переглянути банківську виписку"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "View Carryover Lines"
msgstr "Переглянути рядки перенесення"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Journal Entry"
msgstr "Переглянути проводки"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/models/account_sales_report.py:0
#: model_terms:ir.ui.view,arch_db:account_reports.view_journal_report_audit_move_line_tree
msgid "View Partner"
msgstr "Переглянути партнера"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/wizard/account_report_send.py:0
msgid "View Partner(s)"
msgstr "Переглянути партнера(ів)"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "View Payment"
msgstr "Перегляд платежів"

#. module: account_reports
#: model:ir.model.fields,field_description:account_reports.field_account_report_send__warnings
msgid "Warnings"
msgstr "Попередження"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
msgid ""
"When ticked, totals and subtotals appear below the sections of the report"
msgstr ""
"Якщо позначено, під розділами звіту відображаються підсумки та підсумкові "
"дані"

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_res_company__totals_below_sections
#: model:ir.model.fields,help:account_reports.field_res_config_settings__totals_below_sections
msgid ""
"When ticked, totals and subtotals appear below the sections of the report."
msgstr ""
"Коли буде позначено цей пункт, під розділами звіту відображатимуться "
"підсумки."

#. module: account_reports
#: model:ir.model.fields,help:account_reports.field_account_account__exclude_provision_currency_ids
msgid ""
"Whether or not we have to make provisions for the selected foreign "
"currencies."
msgstr ""
"Незалежно від того, чи потрібно нам робити резерви для обраних іноземних "
"валют."

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_filter_extra_options_template
msgid "With Draft Entries"
msgstr "З проектом записів"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_general_ledger.py:0
msgid "Wrong ID for general ledger line to expand: %s"
msgstr "Неправильний ID для розгортання рядка бухгалтерської книги: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_partner_ledger.py:0
msgid "Wrong ID for partner ledger line to expand: %s"
msgstr "Неправильний ID для розширення рядка розрахунків з контрагентами: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "Wrong format for if_other_expr_above/if_other_expr_below formula: %s"
msgstr ""
"Невірний формат для формули if_other_expr_above/if_other_expr_below: %s"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "XLSX"
msgstr "XLSX"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filters.js:0
msgid "Year"
msgstr "Рік"

#. module: account_reports
#. odoo-javascript
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/edit_popover.xml:0
msgid "Yes"
msgstr "Так"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "You are using custom exchange rates."
msgstr "Ви використовуєте користувацький курс валют."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid "You can't open a tax report from a move without a VAT closing date."
msgstr ""
"Не можна відкрити податкову звітність з проводки без дати закриття ПДВ."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move_line.py:0
msgid "You cannot add taxes on a tax closing move line."
msgstr "Ви не можете додати податки на рядок закриття проводки."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid ""
"You cannot generate entries for a period that does not end at the end of the"
" month."
msgstr ""
"Ви не можете створювати записи для періоду, який не закінчується в кінці "
"місяця."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_deferred_reports.py:0
msgid "You cannot generate entries for a period that is locked."
msgstr "Ви не можете створювати записи для періоду, який заблоковано."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"You cannot reset this closing entry to draft, as another closing entry has "
"been posted at a later date."
msgstr ""
"Ви не можете скинути цей проводку закриття до чернетки, оскільки інша "
"проводка закриття була опублікована пізніше."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_move.py:0
msgid ""
"You cannot reset this closing entry to draft, as it would delete carryover "
"values impacting the tax report of a locked period. To do this, you first "
"need to modify you tax return lock date."
msgstr ""
"Ви не можете скинути цей кінцевий запис до чернетки, оскільки це призведе до"
" видалення залишкових значень, які впливають на податковий звіт "
"заблокованого періоду. Для цього вам спочатку потрібно змінити дату "
"блокування податкової декларації."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_multicurrency_revaluation_report.py:0
msgid "You need to activate more than one currency to access this report."
msgstr ""
"Вам потрібно активувати більше ніж одну валюту, щоб отримати доступ до цього"
" звіту."

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_generic_tax_report.py:0
msgid ""
"You're about the generate the closing entries of multiple companies at once."
" Each of them will be created in accordance with its company tax "
"periodicity."
msgstr ""

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.journal_report_pdf_export_main
#: model_terms:ir.ui.view,arch_db:account_reports.pdf_export_main
msgid "[Draft]"
msgstr "[Чернетка]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "addressed to"
msgstr "адресовано"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/tax_report/warnings.xml:0
msgid "and correct their tax tags if necessary."
msgstr "і виправити свої податкові теги, якщо необхідно."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__year
msgid "annually"
msgstr "щорічно"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:account_reports.setup_financial_year_opening_form
msgid "days after period"
msgstr "днів після періоду"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "doesn't match the balance of your"
msgstr "не відповідає балансу вашого"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__2_months
msgid "every 2 months"
msgstr "кожні 2 місяці"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__4_months
msgid "every 4 months"
msgstr "кожні 4 місяці"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "have a starting balance different from the previous ending balance."
msgstr ""
"мають початковий баланс, відмінний від попереднього кінцевого балансу."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "in the next period."
msgstr "в наступному періоді."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "invoices"
msgstr "рахунки-фактури"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "journal items"
msgstr "записи журналу"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "last bank statement"
msgstr "остання банківська виписка"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__monthly
msgid "monthly"
msgstr "щомісяця"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_report.py:0
msgid "n/a"
msgstr "немає"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "partners"
msgstr "партнери"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "prior or included in this period."
msgstr "до або включені у цей період."

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__trimester
msgid "quarterly"
msgstr "щоквартально"

#. module: account_reports
#: model:ir.model.fields.selection,name:account_reports.selection__res_company__account_tax_periodicity__semester
msgid "semi-annually"
msgstr "раз на пів року"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "statements"
msgstr "виписки"

#. module: account_reports
#: model_terms:ir.ui.view,arch_db:account_reports.view_tax_unit_form
msgid "synchronize fiscal positions"
msgstr "синхронізуйте схеми оподаткування"

#. module: account_reports
#. odoo-python
#: code:addons/account_reports/models/account_tax.py:0
msgid "tax unit [%s]"
msgstr "одиниця податку [%s]"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "that are not established abroad."
msgstr "які не створені за кордоном."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/filters/filter_comparison.xml:0
#: code:addons/account_reports/static/src/components/account_report/filters/filter_date.xml:0
msgid "to"
msgstr "до"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/warnings.xml:0
msgid "unposted Journal Entries"
msgstr "неопубліковані проведення"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "were carried over to this line from previous period."
msgstr "були перенесені в цей рядок з попереднього періоду."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/bank_reconciliation_report/warnings.xml:0
msgid "which don't originate from a bank statement nor payment."
msgstr "які не походять ні з банківської виписки, ні з платежу."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "who are not established in any of the EC countries."
msgstr "які не зареєстровані в жодній із країн ЄС."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to"
msgstr "буде перенесено на"

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/account_report/line_cell/popover/carryover_popover.xml:0
msgid "will be carried over to this line in the next period."
msgstr "буде перенесено на цей рядок в наступному періоді."

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/sales_report/warnings.xml:0
msgid "without a valid intra-community VAT number."
msgstr "без дійсного внутрішнього номера ПДВ."

#. module: account_reports
#: model:mail.template,subject:account_reports.email_template_customer_statement
msgid ""
"{{ (object.company_id or object._get_followup_responsible().company_id).name"
" }} Statement - {{ object.commercial_company_name }}"
msgstr ""

#. module: account_reports
#. odoo-javascript
#: code:addons/account_reports/static/src/components/multicurrency_revaluation_report/warnings.xml:0
msgid "⇒ Reset to Odoo’s Rate"
msgstr "⇒ Встановити на ставку Odoo"
