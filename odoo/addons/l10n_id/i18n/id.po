# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_id
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-12-22 08:18+0000\n"
"PO-Revision-Date: 2023-12-22 08:18+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
#: code:addons/l10n_id/models/res_bank.py:0
msgid ""
"Communication with QRIS failed. QRIS returned with the following error: %s"
msgstr "Komunikasi dengan QRIS gagal. QRIS kembali dengan error berikut: %s"

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid "Could not establish a connection to the QRIS API."
msgstr "Gagal membuat sambungan ke API"

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid ""
"To use QRIS QR code, Please setup the QRIS API Key and Merchant ID on the "
"bank's configuration"
msgstr "Untuk menggunakan QRIS QR, dipersilahkan untuk menyelesaikan setup "
"QRIS di halaman konfigurasi bank"

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid ""
"You cannot generate a QRIS QR code with a bank account that is not in "
"Indonesia."
msgstr "Anda tidak boleh membuat kode QR QRIS dari Bank di luar Indonesia."

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/res_bank.py:0
msgid "You cannot generate a QRIS QR code with a currency other than IDR"
msgstr "Kode QRIS hanya berlaku untuk mata uang Rupiah"

#. module: l10n_id
#. odoo-python
#: code:addons/l10n_id/models/account_move.py:0
#, python-format
msgid ""
"QR is only valid for 30 minutes, please visit the customer portal directly "
"if payment fails"
msgstr "QR hanya berlaku selama 30 menit. Silahkan mengunjungi portal pelanggan"
