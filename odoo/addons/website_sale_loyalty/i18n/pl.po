# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_sale_loyalty
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Polish (https://app.transifex.com/odoo/teams/41243/pl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: pl\n"
"Plural-Forms: nplurals=4; plural=(n==1 ? 0 : (n%10>=2 && n%10<=4) && (n%100<12 || n%100>14) ? 1 : n!=1 && (n%10>=0 && n%10<=1) || (n%10>=5 && n%10<=9) || (n%100>=12 && n%100<=14) ? 2 : 3);\n"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "<strong> - </strong>"
msgstr ""

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "<strong>Coupons - </strong>"
msgstr ""

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "A coupon is needed for coupon programs."
msgstr "Kupon jest wymagany w przypadku programów kuponowych."

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
msgid "A coupon with the same code was found."
msgstr "Znaleziono kupon z tym samym kodem."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "All websites"
msgstr "Wszystkie witryny"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__ecommerce_ok
msgid "Available on Website"
msgstr "Dostępne na stronie internetowej"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/controllers/payment.py:0
msgid "Cannot process payment: applied reward was changed or has expired."
msgstr ""

#. module: website_sale_loyalty
#. odoo-javascript
#: code:addons/website_sale_loyalty/static/src/js/portal_loyalty_card.xml:0
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Claim"
msgstr "Twierdzenie"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Code:"
msgstr "Kod:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Costs"
msgstr "Koszty"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "Could not apply the promo code:"
msgstr "Nie można zastosować kodu promocyjnego:"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__coupon_id
msgid "Coupon"
msgstr "Kupon"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_coupon_share
msgid "Create links that apply a coupon and redirect to a specific page"
msgstr ""
"Tworzenie linków, które stosują kupon i przekierowują do określonej strony."

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_uid
msgid "Created by"
msgstr "Utworzył(a)"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__create_date
msgid "Created on"
msgstr "Data utworzenia"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_sale_order__disabled_auto_rewards
msgid "Disabled Auto Rewards"
msgstr "Wyłączone nagrody automatyczne"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_discount_loyalty_type_config
msgid "Discount & Loyalty"
msgstr "Upusty i programy lojalnościowe"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discount:"
msgstr "Upust:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.cart_discount
msgid "Discounted amount"
msgstr "Kwota upustu"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__display_name
msgid "Display Name"
msgstr "Nazwa wyświetlana"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Done"
msgstr "Wykonano"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Expired Date:"
msgstr "Data wygaśnięcia:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Generate Short Link"
msgstr "Wygeneruj krótki link"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.sale_coupon_result
msgid "Gift card or discount code..."
msgstr "Karta podarunkowa lub kod upustu..."

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_gift_ewallet_type_config
msgid "Gift cards & eWallet"
msgstr "Karty podarunkowe i e-portfel"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__id
msgid "ID"
msgstr "ID"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Invalid or expired promo code."
msgstr "Nieważny lub wygasły kod promocyjny."

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_uid
msgid "Last Updated by"
msgstr "Ostatnio aktualizowane przez"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__write_date
msgid "Last Updated on"
msgstr "Data ostatniej aktualizacji"

#. module: website_sale_loyalty
#: model:ir.ui.menu,name:website_sale_loyalty.menu_loyalty
msgid "Loyalty"
msgstr "Programy lojalnościowe"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_card
msgid "Loyalty Coupon"
msgstr "Kupon lojalnościowy"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_program
msgid "Loyalty Program"
msgstr "Program lojalnościowy"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.res_config_settings_view_form_inherit_website_sale_loyalty
msgid "Loyalty Programs"
msgstr "Programy lojalnościowe"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_loyalty_rule
msgid "Loyalty Rule"
msgstr "Zasady lojalności"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Pay with eWallet"
msgstr "Płać za pomocą e-portfela"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_id
msgid "Program"
msgstr "Program"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__program_website_id
msgid "Program Website"
msgstr "Strona internetowa programu"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__promo_code
msgid "Promo Code"
msgstr "Kod promocyjny"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "Provide either a coupon or a program."
msgstr "Dostarcz kupon lub program."

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__redirect
msgid "Redirect"
msgstr "Przekieruj"

#. module: website_sale_loyalty
#: model:ir.model.fields,help:website_sale_loyalty.field_coupon_share__program_website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,help:website_sale_loyalty.field_loyalty_rule__website_id
msgid "Restrict to a specific website."
msgstr "Ogranicz do określonej strony."

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order
msgid "Sales Order"
msgstr "Zamówienie sprzedaży"

#. module: website_sale_loyalty
#: model:ir.model,name:website_sale_loyalty.model_sale_order_line
msgid "Sales Order Line"
msgstr "Pozycja zamówienia sprzedaży"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_card_view_tree_inherit_website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_tree_inherit_website_sale_loyalty
msgid "Share"
msgstr "Udostępnij"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "Share %s"
msgstr ""

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__share_link
msgid "Share Link"
msgstr "Link do udostępniania"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid "Share Loyalty Card"
msgstr "Udostępnij kartę lojalnościową"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.snippet_options
msgid "Show Discount in Subtotal"
msgstr "Pokaż upust w sumie częściowej"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/controllers/main.py:0
msgid ""
"The coupon will be automatically applied when you add something in your "
"cart."
msgstr ""
"Kupon zostanie automatycznie zastosowany po dodaniu produktu do koszyka."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.layout
msgid "The following promo code was applied on your order:"
msgstr "Następujący kod promocyjny został zastosowany do Twojego zamówienia:"

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/models/loyalty_rule.py:0
msgid "The promo code must be unique."
msgstr "Kod promocyjny musi być unikalny."

#. module: website_sale_loyalty
#. odoo-python
#: code:addons/website_sale_loyalty/wizard/coupon_share.py:0
msgid "The shared website should correspond to the website of the program."
msgstr ""
"Udostępniona strona internetowa powinna odpowiadać stronie internetowej "
"programu."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "Use"
msgstr "Użyj"

#. module: website_sale_loyalty
#: model:ir.model.fields,field_description:website_sale_loyalty.field_coupon_share__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_program__website_id
#: model:ir.model.fields,field_description:website_sale_loyalty.field_loyalty_rule__website_id
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.loyalty_program_view_form_inherit_website_sale_loyalty
msgid "Website"
msgstr "Strona internetowa"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.coupon_share_view_form
msgid ""
"You can share this promotion with your customers.\n"
"                            It will be applied at checkout when the customer uses this link."
msgstr ""
"Możesz udostępnić tę promocję swoim klientom.\n"
"Zostanie ona zastosowana przy kasie, gdy klient skorzysta z tego linku."

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have"
msgstr "Masz"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "You have successfully applied the following code:"
msgstr "Poniższy kod został pomyślnie zastosowany:"

#. module: website_sale_loyalty
#: model_terms:ir.ui.view,arch_db:website_sale_loyalty.modify_code_form
msgid "in your ewallet"
msgstr "w portfelu elektronicznym"
