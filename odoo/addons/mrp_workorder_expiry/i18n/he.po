# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_workorder_expiry
# 
# Translators:
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: דודי מלכה <<EMAIL>>, 2024\n"
"Language-Team: Hebrew (https://app.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=3; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: 2;\n"

#. module: mrp_workorder_expiry
#: model_terms:ir.ui.view,arch_db:mrp_workorder_expiry.quality_check_view_form_tablet_inherit_expiry
msgid "<span class=\"fa fa-exclamation-triangle\"/> This lot is expired."
msgstr ""

#. module: mrp_workorder_expiry
#: model:ir.model.fields,field_description:mrp_workorder_expiry.field_quality_check__is_expired
msgid "Product Expiry Alert"
msgstr "התראת פקיעת תוקף מוצר"

#. module: mrp_workorder_expiry
#: model:ir.model,name:mrp_workorder_expiry.model_quality_check
msgid "Quality Check"
msgstr "בדיקת איכות"

#. module: mrp_workorder_expiry
#: model:ir.model.fields,help:mrp_workorder_expiry.field_quality_check__is_expired
msgid "The Expiration Date has been reached."
msgstr "תאריך התפוגה הגיע."
