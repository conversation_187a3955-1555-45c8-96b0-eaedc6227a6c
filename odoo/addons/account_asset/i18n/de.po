# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_asset
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciation_entries_count
msgid "# Depreciation Entries"
msgstr "# Abschreibungsbuchungen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_count
msgid "# Gross Increases"
msgstr "# Bruttoerhöhung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_entries_count
msgid "# Posted Depreciation Entries"
msgstr "# Gebuchte Abschreibungsbuchungen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%(asset)s: Disposal"
msgstr "%(asset)s: Veräußerung"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%(asset)s: Sale"
msgstr "%(asset)s: Verkauf"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "%(months)s m"
msgstr "%(months)s Monate"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "%(move_line)s (%(current)s of %(total)s)"
msgstr "%(move_line)s (%(current)s von %(total)s)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "%(years)s y"
msgstr "%(years)s Jahre"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "%s (copy)"
msgstr "%s (Kopie)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"%s Future entries will be recomputed to depreciate the asset following the "
"changes."
msgstr ""
"%s Künftige Posten werden neu berechnet, um den Vermögensgegenstand nach den"
" Änderungen abzuschreiben."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "%s: Depreciation"
msgstr "%s: Abschreibung"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "(No %s)"
msgstr "(Ohne %s)"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "(incl."
msgstr "(inkl."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s. "
"<br/> %(extra_text)s Future entries will be recomputed to depreciate the "
"asset following the changes."
msgstr ""
"Ein Abschreibungsposten wird am %(date)s und einschließlich dieses Datums "
"gebucht. <br/> %(extra_text)s Zukünftige Buchungen werden neu berechnet, um "
"die Anlage entsprechend der Änderungen abzuschreiben."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s.<br/>"
" A disposal entry will be posted on the %(account_type)s account "
"<b>%(account)s</b>."
msgstr ""
"Ein Abschreibungsposten wird am %(date)s und einschließlich dieses Datums "
"gebucht.<br/> Ein Veräußerungsposten wird auf das %(account_type)s-Konto "
"<b>%(account)s</b> gebucht."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"A depreciation entry will be posted on and including the date %(date)s.<br/>"
" A second entry will neutralize the original income and post the  outcome of"
" this sale on account <b>%(account)s</b>."
msgstr ""
"Ein Abschreibungsposten wird am %(date)s und einschließlich dieses Datums "
"gebucht.<br/> Eine zweite Buchung wird die ursprüngliche Einnahme "
"neutralisieren und das Ergebnis dieses Verkaufs auf dem Konto "
"<b>%(account)s</b> verbuchen."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "A depreciation entry will be posted on and including the date %s."
msgstr ""
"Ein Abschreibungsposten wird am und einschließlich des Datums %s gebucht."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "A document linked to %(move_line_name)s has been deleted: %(link)s"
msgstr ""
"Ein mit %(move_line_name)s verknüpftes Dokument wurde gelöscht: %(link)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "A document linked to this move has been deleted: %s"
msgstr "Ein mit dieser Buchung verknüpftes Dokument wurde gelöscht: %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "A gross increase has been created: %(link)s"
msgstr "Eine Bruttoerhöhung wurde erstellt: %(link)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"A non deductible tax value of %(tax_value)s was added to %(name)s's initial "
"value of %(purchase_value)s"
msgstr ""
"Ein nichtabzugsfähiger Steuerwert von %(tax_value)s wurde zum ursprünglichen"
" Wert von %(name)s in Höhe von %(purchase_value)s hinzugefügt."

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_account
msgid "Account"
msgstr "Konto"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_type
msgid ""
"Account Type is used for information purpose, to generate country-specific "
"legal reports, and set the rules to close a fiscal year and generate opening"
" entries."
msgstr ""
"Der Kontotyp wird zu Informationszwecken verwendet, um länderspezifische "
"gesetzliche Berichte zu erstellen und die Regeln für den Abschluss eines "
"Geschäftsjahres und die Erstellung von Eröffnungsbuchungen festzulegen."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_id
msgid "Account used in the depreciation entries, to decrease the asset value."
msgstr ""
"Konto, das in den Abschreibungsbuchungen verwendet wird, um den "
"Vermögenswert zu verringern."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_depreciation_expense_id
msgid ""
"Account used in the periodical entries, to record a part of the asset as "
"expense."
msgstr ""
"Konto, das in den periodischen Buchungen verwendet wird, um einen Teil des "
"Vermögenswertes als Aufwand zu erfassen."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__account_asset_id
msgid ""
"Account used to record the purchase of the asset at its original price."
msgstr ""
"Konto, auf dem der Kauf der Anlage zu seinem ursprünglichen Preis verbucht "
"wird."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__gain_account_id
msgid "Account used to write the journal item in case of gain"
msgstr "Konto, auf dem die Buchungszeile im Falle eines Gewinns verbucht wird"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__gain_account_id
msgid ""
"Account used to write the journal item in case of gain while selling an "
"asset"
msgstr ""
"Konto, das für die Buchungszeile im Falle eines Gewinns beim Verkauf einer "
"Anlage verwendet wird"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__loss_account_id
msgid "Account used to write the journal item in case of loss"
msgstr ""
"Konto, auf dem die Buchungszeile im Falle eines Verlusts verbucht wird"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_res_company__loss_account_id
msgid ""
"Account used to write the journal item in case of loss while selling an "
"asset"
msgstr ""
"Konto, das für die Buchungszeile im Falle eines Verlusts beim Verkauf einer "
"Anlage verwendet wird"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Accounting"
msgstr "Buchhaltung"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_report
msgid "Accounting Report"
msgstr "Buchhaltungsbericht"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_acquisition_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset__acquisition_date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Acquisition Date"
msgstr "Kaufdatum"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__modify_action
msgid "Action"
msgstr "Aktion"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__active
msgid "Active"
msgstr "Aktiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_ids
msgid "Activities"
msgstr "Aktivitäten"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitätsausnahme-Dekoration"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_state
msgid "Activity State"
msgstr "Status der Aktivität"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_icon
msgid "Activity Type Icon"
msgstr "Symbol des Aktivitätstyps"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Add an internal note"
msgstr "Interne Notiz hinzufügen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be from the same account"
msgstr "Alle Zeilen sollten von demselben Konto stammen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be from the same company"
msgstr "Alle Zeilen sollten von demselben Unternehmen stammen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
msgid "All the lines should be posted"
msgstr "Alle Zeilen sollten gebucht sein"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__already_depreciated_amount_import
msgid "Already Depreciated Amount Import"
msgstr "Import des bereits abgeschriebenen Betrags"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__parent_id
msgid "An asset has a parent when it is the result of gaining value"
msgstr ""
"Ein Vermögenswert hat ein übergeordnetes Element, wenn er das Ergebnis eines"
" Wertzuwachses ist"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "An asset has been created for this move:"
msgstr "Ein Vermögensgegenstand wurde erstellt für die Buchung:"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__asset_model_ids
msgid ""
"An asset wil be created for each asset model when this account is used on a "
"vendor bill or a refund"
msgstr ""
"Für jedes Anlagenmodell wird ein Vermögenswert erstellt, wenn dieses Konto "
"auf einer Lieferantenrechnung oder einer Rückerstattung verwendet wird"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "An asset will be created for the value increase of the asset. <br/>"
msgstr ""
"Ein Vermögenswert wird für die Wertsteigerung der Anlage erstellt. <br/>"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_distribution
msgid "Analytic Distribution"
msgstr "Kostenverteilung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__analytic_precision
msgid "Analytic Precision"
msgstr "Kostengenauigkeit"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Archived"
msgstr "Archiviert"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/models/account_move.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset"
msgstr "Asset"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Asset Account"
msgstr "Aktivkonto"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset Cancelled"
msgstr "Vermögensgegenstand storniert"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_counterpart_id
msgid "Asset Counterpart Account"
msgstr "Aktiva-Gegenkonto"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_group
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_group_id
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_form_view
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_list_view
msgid "Asset Group"
msgstr "Vermögensgruppe"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_id_display_name
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_id_display_name
msgid "Asset Id Display Name"
msgstr "Anzeigename der Anlagen-ID"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_lifetime_days
msgid "Asset Lifetime Days"
msgstr "Lebensdauertage des Vermögensgegenstandes"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__asset_model_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Asset Model"
msgstr "Vermögensmodell"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Model name"
msgstr "Name des Vermögensmodell"

#. module: account_asset
#: model:ir.actions.act_window,name:account_asset.action_account_asset_model_form
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_model_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Asset Models"
msgstr "Vermögensmodelle"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_move_type
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_move_type
msgid "Asset Move Type"
msgstr "Buchungsart des Vermögensgegenstands"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__name
msgid "Asset Name"
msgstr "Name des Vermögensgegenstands"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_paused_days
msgid "Asset Paused Days"
msgstr "Pausierte Tage des Vermögensgegenstandes"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_value_change
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_value_change
msgid "Asset Value Change"
msgstr "Wertänderung des Vermögensgegenstandes"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Asset Values"
msgstr "Vermögenswerte"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset created"
msgstr "Vermögensgegenstand erstellt"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Asset created from invoice: %s"
msgstr "Vermögensgegenstand erstellt aus Rechnung: %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset disposed. %s"
msgstr "Vermögensgegenstand veräußert. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset paused. %s"
msgstr "Vermögensgegenstand pausiert. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Asset sold. %s"
msgstr "Vermögensgegenstand verkauft. %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Asset unpaused. %s"
msgstr "Vermögensgegenstand nicht pausiert. %s"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_group_form_view
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_form_asset_inherit
msgid "Asset(s)"
msgstr "Vermögenswert(e)"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset
msgid "Asset/Revenue Recognition"
msgstr "Realisierung von Vermögenswerten/Umsätzen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.actions.act_window,name:account_asset.action_account_asset_form
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_ids
#: model:ir.ui.menu,name:account_asset.menu_action_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_tree
msgid "Assets"
msgstr "Vermögensgegenstände"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_asset_report_handler
msgid "Assets Report Custom Handler"
msgstr "Bericht über Vermögensgegenstände Benutzerdefinierter Handler"

#. module: account_asset
#: model:ir.ui.menu,name:account_asset.menu_finance_config_assets
msgid "Assets and Revenues"
msgstr "Vermögen und Erträge"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in closed state"
msgstr "Vermögensgegenstände im abgeschlossenen Status"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Assets in draft and open states"
msgstr "Vermögensgegenstände im Status Entwurf und Offen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"Atleast one asset (%s) couldn't be set as running because it lacks any "
"required information"
msgstr ""
"Mindestens ein Vermögensgegenstand (%s) konnte nicht als laufend festgelegt "
"werden, weil ihm die erforderlichen Informationen fehlen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automate Asset"
msgstr "Aktiva automatisieren"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Automation"
msgstr "Automatisierung"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__daily_computation
msgid "Based on days per period"
msgstr "Basierend auf Tagen pro Periode"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Bills"
msgstr "Rechnungen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__book_value
msgid "Book Value"
msgstr "Buchwert"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__can_create_asset
msgid "Can Create Asset"
msgstr "Kann Vermögensgegenstand erstellen"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Cancel Asset"
msgstr "Vermögensgegenstand stornieren"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__cancelled
msgid "Cancelled"
msgstr "Abgebrochen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Characteristics"
msgstr "Spezifikationen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__children_ids
msgid "Children"
msgstr "Kinder"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method
msgid ""
"Choose the method to use to compute the amount of depreciation lines.\n"
"  * Straight Line: Calculated on basis of: Gross Value / Duration\n"
"  * Declining: Calculated on basis of: Residual Value * Declining Factor\n"
"  * Declining then Straight Line: Like Declining but with a minimum depreciation value equal to the straight line value."
msgstr ""
"Wählen Sie eine Methode zur Berechnung des Betrags der Abschreibungszeilen.\n"
"* Linear: Berechnet auf der Basis von: Bruttowert/Dauer\n"
"* Degressiv: Berechnet auf der Grundlage von: Restwert * Degressionsfaktor\n"
"* Degressiv, dann linear: Analog zur degressiven Abschreibung, jedoch mit einem Mindestabschreibungswert in Höhe des linearen Wertes."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__close
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Closed"
msgstr "Abgeschlossen"

#. module: account_asset
#: model:ir.model,name:account_asset.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__company_id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__company_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_computation_type
msgid "Computation"
msgstr "Berechnung"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_asset_compute_depreciations
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Compute Depreciation"
msgstr "Abschreibung berechnen"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_asset_run
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Confirm"
msgstr "Bestätigen"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__constant_periods
msgid "Constant Periods"
msgstr "Konstante Perioden"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__count_asset
#: model:ir.model.fields,field_description:account_asset.field_account_move__count_asset
msgid "Count Asset"
msgstr "Anzahl Vermögensgegenstände"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__count_linked_asset
msgid "Count Linked Asset"
msgstr "Anzahl verknüpfter Vermögensgegenstände"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__count_linked_assets
msgid "Count Linked Assets"
msgstr "Anzahl verknüpfter Vermögensgegenstände"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__country_code
msgid "Country Code"
msgstr "Ländercode"

#. module: account_asset
#: model:ir.actions.server,name:account_asset.action_account_aml_to_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__create_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_tree
msgid "Create Asset"
msgstr "Vermögensgegenstand erstellen"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__validate
msgid "Create and validate"
msgstr "Erstellen und validieren"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__draft
msgid "Create in draft"
msgstr "Als Entwurf erstellen"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_form
msgid "Create new asset"
msgstr "Neuen Vermögensgegenstand erstellen"

#. module: account_asset
#: model_terms:ir.actions.act_window,help:account_asset.action_account_asset_model_form
msgid "Create new asset model"
msgstr "Neues Vermögensmodell erstellen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__create_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__create_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__create_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciated_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciated_value
msgid "Cumulative Depreciation"
msgstr "Kumulierte Abschreibung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__currency_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__currency_id
msgid "Currency"
msgstr "Währung"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Current"
msgstr "Aktuell"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Current Values"
msgstr "Aktuelle Werte"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__invoice_ids
msgid "Customer Invoice"
msgstr "Kundenrechnung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__date
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Date"
msgstr "Datum"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_depreciation_beginning_date
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_depreciation_beginning_date
msgid "Date of the beginning of the depreciation"
msgstr "Startdatum der Abschreibung"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Dec. then Straight"
msgstr "Degressiv, dann linear"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive
msgid "Declining"
msgstr "Degressiv"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_progress_factor
msgid "Declining Factor"
msgstr "Degressionsfaktor"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__degressive_then_linear
msgid "Declining then Straight Line"
msgstr "Degressiv, dann linear"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__value_residual
msgid "Depreciable Amount"
msgstr "Abzuschreibender Betrag"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__value_residual
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_remaining_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_remaining_value
msgid "Depreciable Value"
msgstr "Abzuschreibender Wert"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciated Amount"
msgstr "Abgeschriebener Betrag"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__depreciation_value
#: model:ir.model.fields,field_description:account_asset.field_account_move__depreciation_value
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__depreciation
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation"
msgstr "Abschreibung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Depreciation Account"
msgstr "Abschreibungskonto"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Board"
msgstr "Abschreibungsübersicht"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Date"
msgstr "Abschreibungsdatum"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__depreciation_move_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Lines"
msgstr "Abschreibungszeilen"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Depreciation Method"
msgstr "Abschreibungsmethode"

#. module: account_asset
#: model:account.report,name:account_asset.assets_report
#: model:ir.actions.client,name:account_asset.action_account_report_assets
#: model:ir.ui.menu,name:account_asset.menu_action_account_report_assets
msgid "Depreciation Schedule"
msgstr "Abschreibungsplan"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Depreciation board modified %s"
msgstr "Abschreibungsübersicht geändert %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Depreciation entry %(name)s posted (%(value)s)"
msgstr "Abschreibungsbuchung %(name)s gebucht (%(value)s)"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Depreciation entry %(name)s reversed (%(value)s)"
msgstr "Abschreibungsbuchung %(name)s storniert (%(value)s)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_account_asset_id
msgid "Display Account Asset"
msgstr "Aktivkonto anzeigen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__display_name
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__display_name
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__disposal
msgid "Disposal"
msgstr "Veräußerung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__disposal_date
msgid "Disposal Date"
msgstr "Datum der Veräußerung"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Disposal Move"
msgstr "Veräußerungsbuchung"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Disposal Moves"
msgstr "Veräußerungsbuchungen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Dispose"
msgstr "Veräußern"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Verteilung der Kostenstellen"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__draft
msgid "Draft"
msgstr "Entwurf"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__draft_asset_exists
#: model:ir.model.fields,field_description:account_asset.field_account_move__draft_asset_exists
msgid "Draft Asset Exists"
msgstr "Entwurfsvermögensgegenstand existiert"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_number
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_number
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Duration"
msgstr "Dauer"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_duration_rate
msgid "Duration / Rate"
msgstr "Dauer/Rate"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_depreciation_expense_id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_depreciation_expense_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Expense Account"
msgstr "Aufwandskonto"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_first_depreciation
msgid "First Depreciation"
msgstr "Erste Abschreibung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_asset_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Fixed Asset Account"
msgstr "Konto für Sachanlagen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "FontAwesome-Icon, z. B. fa-tasks"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__form_view_ref
msgid "Form View Ref"
msgstr "Formular-Ansichtsref."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Future Activities"
msgstr "Anstehende Aktivitäten"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__gain
msgid "Gain"
msgstr "Gewinn"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__gain_account_id
msgid "Gain Account"
msgstr "Gewinnkonto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_or_loss
msgid "Gain Or Loss"
msgstr "Gewinn oder Verlust"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__gain_value
msgid "Gain Value"
msgstr "Gewinnwert"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Gross Increase"
msgstr "Bruttoerhöhung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__account_asset_id
msgid "Gross Increase Account"
msgstr "Bruttoerhöhungskonto"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__gross_increase_value
msgid "Gross Increase Value"
msgstr "Bruttowertsteigerung"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group By Account"
msgstr "Nach Konto gruppieren"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group By Asset Group"
msgstr "Nach Vermögensgruppe gruppieren"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Group By..."
msgstr "Gruppieren nach ..."

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group by Account"
msgstr "Nach Konto gruppieren"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "Group by Asset Group"
msgstr "Nach Vermögensgruppe gruppieren"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__id
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__id
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__id
msgid "ID"
msgstr "ID"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon, um eine Ausnahmeaktivität anzuzeigen."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__already_depreciated_amount_import
msgid ""
"In case of an import from another software, you might need to use this field"
" to have the right depreciation table report. This is the value that was "
"already depreciated with entries not computed from this model"
msgstr ""
"Im Falle eines Imports aus einer anderen Software müssen Sie dieses Feld "
"möglicherweise verwenden, um den richtigen Abschreibungstabellenbericht zu "
"erhalten. Dies ist der Wert, der bereits mit Posten abgeschrieben wurde, die"
" nicht aus diesem Modell berechnet wurden"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__informational_text
msgid "Informational Text"
msgstr "Informativer Text"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__invoice_line_ids
msgid "Invoice Line"
msgstr "Rechnungszeile"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value
#: model:ir.model.fields,help:account_asset.field_account_asset__salvage_value_pct
msgid "It is the amount you plan to have that you cannot depreciate."
msgstr "Das ist der geplante Wert, der nicht abgeschrieben werden kann."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__journal_id
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_search
msgid "Journal"
msgstr "Journal"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Journal Entries"
msgstr "Journalbuchungen"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Journal Entry"
msgstr "Journalbuchung"

#. module: account_asset
#: model:ir.model,name:account_asset.model_account_move_line
msgid "Journal Item"
msgstr "Buchungszeile"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_move_line_ids
msgid "Journal Items"
msgstr "Buchungszeilen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid ""
"Journal Items of %(account)s should have a label in order to generate an "
"asset"
msgstr ""
"Buchungszeilen von %(account)s müssen entsprechend gekennzeichnet sein, um "
"einen Vermögensgegenstand daraus zu generieren"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_uid
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__write_uid
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__write_date
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__write_date
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Late Activities"
msgstr "Verspätete Aktivitäten"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Linear"
msgstr "Linear"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__linked_assets_ids
msgid "Linked Assets"
msgstr "Verknüpfte Vermögensgegenstände"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__loss
msgid "Loss"
msgstr "Verlust"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__loss_account_id
#: model:ir.model.fields,field_description:account_asset.field_res_company__loss_account_id
msgid "Loss Account"
msgstr "Verlustkonto"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_form_asset_inherit
msgid "Manage Items"
msgstr "Zeilen verwalten"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_first_method
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method
msgid "Method"
msgstr "Methode"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__model_id
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__model
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Model"
msgstr "Modell"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_properties_definition
msgid "Model Properties"
msgstr "Modelleigenschaften"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify"
msgstr "Ändern"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model:ir.model,name:account_asset.model_asset_modify
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Modify Asset"
msgstr "Vermögensgegenstand bearbeiten"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Modify Depreciation"
msgstr "Abschreibung ändern"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__1
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__1
msgid "Months"
msgstr "Monate"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_account__multiple_assets_per_line
msgid "Multiple Assets per Line"
msgstr "Mehrere Vermögenswerte pro Zeile"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_account__multiple_assets_per_line
msgid ""
"Multiple asset items will be generated depending on the bill line quantity "
"instead of 1 global asset."
msgstr ""
"Es werden mehrere Aktivposten in Abhängigkeit von der Menge der "
"Rechnungszeile anstelle eines einzigen globalen Vermögensgegenstandes "
"erzeugt."

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Frist für meine Aktivitäten"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__name
msgid "Name"
msgstr "Name"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__negative_revaluation
msgid "Negative revaluation"
msgstr "Negative Neubewertung"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__net_gain_on_sale
msgid "Net gain on sale"
msgstr "Nettogewinn aus Verkauf"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__net_gain_on_sale
msgid "Net value of gain or loss on sale of an asset"
msgstr ""
"Nettowert des Gewinns oder Verlusts aus dem Verkauf eines Vermögenswerts"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__value_residual
msgid "New residual amount for the asset"
msgstr "Neuer Restwert der Anlage"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__salvage_value
msgid "New salvage amount for the asset"
msgstr "Neuer Veräußerungswert der Anlage"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nächstes Aktivitätskalenderereignis"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nächste Aktivitätsfrist"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_account__create_asset__no
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__gain_or_loss__no
msgid "No"
msgstr "Nein"

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/depreciation_schedule/groupby.xml:0
msgid "No Grouping"
msgstr "Keine Gruppierung"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__prorata_computation_type__none
msgid "No Prorata"
msgstr "Kein Pro-Rata"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__non_deductible_tax_value
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__non_deductible_tax_value
msgid "Non Deductible Tax Value"
msgstr "Nichtabzugsfähiger Steuerwert"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__salvage_value
msgid "Not Depreciable Amount"
msgstr "Nicht abzuschreibender Betrag"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value
msgid "Not Depreciable Value"
msgstr "Nicht abzuschreibender Wert"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__salvage_value_pct
msgid "Not Depreciable Value Percent"
msgstr "Nicht abzuschreibender Wertprozentsatz"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__name
msgid "Note"
msgstr "Notiz"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Number of Depreciations"
msgstr "Anzahl der Abschreibungen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__method_period
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__method_period
msgid "Number of Months in a Period"
msgstr "Anzahl der Monate in einer Periode"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__gross_increase_count
msgid "Number of assets made to increase the value of the asset"
msgstr ""
"Anzahl der Vermögenswerte, die den Wert des Vermögensgegenstandes erhöhen"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_bank_statement_line__asset_number_days
#: model:ir.model.fields,field_description:account_asset.field_account_move__asset_number_days
msgid "Number of days"
msgstr "Anzahl der Tage"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__total_depreciation_entries_count
msgid "Number of depreciation entries (posted or not)"
msgstr "Anzahl Abschreibungsbuchungen (gebucht oder nicht)"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__paused
msgid "On Hold"
msgstr "In Warteschlange"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Open Asset"
msgstr "Offener Vermögensgegenstand"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__original_value
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_kanban
msgid "Original Value"
msgstr "Ursprünglicher Wert"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__parent_id
msgid "Parent"
msgstr "Übergeordnet"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Parent Asset"
msgstr "Übergeordneter Vermögensgegenstand"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Pause"
msgstr "Pause"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__paused_prorata_date
msgid "Paused Prorata Date"
msgstr "Pausiertes Pro-Rata-Datum"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_model_tree
msgid "Period length"
msgstr "Länge der Periode"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__positive_revaluation
msgid "Positive revaluation"
msgstr "Positive Neubewertung"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Posted Entries"
msgstr "Gebuchte Posten"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__asset_properties
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Properties"
msgstr "Eigenschaften"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__prorata_date
msgid "Prorata Date"
msgstr "Pro-Rata-Datum"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__purchase
msgid "Purchase"
msgstr "Einkauf"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Re-evaluate"
msgstr "Neu bewerten"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset_group__linked_asset_ids
#: model:ir.model.fields,field_description:account_asset.field_account_move_line__asset_ids
#: model_terms:ir.ui.view,arch_db:account_asset.view_move_line_form_asset_inherit
msgid "Related Assets"
msgstr "Verknüpfte Vermögenswerte"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__related_purchase_value
msgid "Related Purchase Value"
msgstr "Zugehöriger Einkaufswert"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Reset to running"
msgstr "Reaktivieren"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Resume"
msgstr "Fortsetzen"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Resume Depreciation"
msgstr "Abschreibung fortsetzen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"Reverse the depreciation entries posted in the future in order to modify the"
" depreciation"
msgstr ""
"Die zukünftig gebuchten Abschreibungsbuchungen stornieren, um die "
"Abschreibung zu ändern"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__state__open
msgid "Running"
msgstr "Laufend"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_move__asset_move_type__sale
msgid "Sale"
msgstr "Verkauf"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Save as Model"
msgstr "Als Modell speichern"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Save model"
msgstr "Modell speichern"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_asset_modify__select_invoice_line_id
msgid "Select Invoice Line"
msgstr "Rechnungszeile auswählen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
#: model_terms:ir.ui.view,arch_db:account_asset.asset_modify_form
msgid "Sell"
msgstr "Verkaufen"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Draft"
msgstr "Auf Entwurf setzen"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Set to Running"
msgstr "Starten"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Show all records which has next action date is before today"
msgstr "Alle Datensätze mit vor heute geplanten Aktionen anzeigen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Some fields are missing %s"
msgstr "Einige Felder fehlen %s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Some required values are missing"
msgstr "Einige benötigte Werte fehlen"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__prorata_date
msgid ""
"Starting date of the period used in the prorata calculation of the first "
"depreciation"
msgstr ""
"Datum des Beginns des Zeitraums, der für die anteilige Berechnung der ersten"
" Abschreibung verwendet wird"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__state
msgid "Status"
msgstr "Status"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum bereits überschritten\n"
"Heute: Aktivitätsdatum ist heute\n"
"Geplant: anstehende Aktivitäten."

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method__linear
msgid "Straight Line"
msgstr "Lineare Abschreibung"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__book_value
msgid ""
"Sum of the depreciable value, the salvage value and the book value of all "
"value increase items"
msgstr ""
"Summe des Abschreibungswerts, des Veräußerungswerts und des Buchwerts aller "
"wertsteigenden Posten"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""
"ISO-Ländercode in zwei Zeichen. \n"
"Sie können dieses Feld auch für eine Schnellsuche einsetzen."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"The account %(exp_acc)s has been credited by %(exp_delta)s, while the "
"account %(dep_acc)s has been debited by %(dep_delta)s. This corresponds to "
"%(move_count)s cancelled %(word)s:"
msgstr ""
"Dem Konto %(exp_acc)s wurden %(exp_delta)s gutgeschrieben, während das Konto"
" %(dep_acc)s mit %(dep_delta)s belastet wurde. Das entspricht %(move_count)s"
" stornierten %(word)s:"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_period
#: model:ir.model.fields,help:account_asset.field_asset_modify__method_period
msgid "The amount of time between two depreciations"
msgstr "Der Zeitwert zwischen zwei Abschreibungen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"The amount you have entered (%(entered_amount)s) does not match the Related "
"Purchase's value (%(purchase_value)s). Please make sure this is what you "
"want."
msgstr ""
"Der von Ihnen eingegebene Betrag (%(entered_amount)s) stimmt nicht mit dem "
"Wert des zugehörigen Einkaufs (%(purchase_value)s) überein. Bitte "
"vergewissern Sie sich, dass dies Ihren Wünschen entspricht."

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__asset_id
msgid "The asset to be modified by this wizard"
msgstr "Der durch diesen Assistenten zu bearbeitenden Vermögensgegenstand"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__children_ids
msgid "The children are the gains in value of this asset"
msgstr ""
"Die abhängigen Elemente sind die Wertgewinne dieses Vermögensgegenstandes"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__invoice_ids
msgid ""
"The disposal invoice is needed in order to generate the closing journal "
"entry."
msgstr ""
"Der Veräußerungsbeleg wird gebraucht, um die Abschlussbuchung zu erstellen"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__method_number
msgid "The number of depreciations needed to depreciate your asset"
msgstr ""
"Die Anzahl der Abschreibungen, die erforderlich sind, um Ihren "
"Vermögensgegenstand abzuschreiben"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "The remaining value on the last depreciation line must be 0"
msgstr "Der Restwert der letzten Abschreibung sollte 0 sein"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_asset_modify__invoice_line_ids
msgid "There are multiple lines that could be the related to this asset"
msgstr ""
"Es gibt zahlreiche Buchungszeilen, die mit dieser Anlage verbunden sein "
"könnten"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"There are unposted depreciations prior to the selected operation date, "
"please deal with them first."
msgstr ""
"Es gibt noch nichtgebuchte Abschreibungen vor dem gewählten Datum, bitte "
"bearbeiten Sie diese zuerst."

#. module: account_asset
#. odoo-javascript
#: code:addons/account_asset/static/src/components/move_reversed/move_reversed.xml:0
msgid "This move has been reversed"
msgstr "Diese Buchung wurde storniert"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_search
msgid "Today Activities"
msgstr "Heutige Aktivitäten"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_assets_report.py:0
msgid "Total"
msgstr "Gesamt"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__total_depreciable_value
msgid "Total Depreciable Value"
msgstr "Insgesamt abzuschreibender Wert"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "Turn as an asset"
msgstr "In Vermögenswert umwandeln"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__account_type
msgid "Type of the account"
msgstr "Kontotyp"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der Ausnahmeaktivität im Datensatz."

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "Value at Import"
msgstr "Wert bei Import"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Value decrease for: %(asset)s"
msgstr "Wertminderung für: %(asset)s"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "Value increase for: %(asset)s"
msgstr "Wertsteigerung für: %(asset)s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__warning_count_assets
msgid "Warning Count Assets"
msgstr "Warnung für Anzahl Vermögensgegenstände"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "Warning for the Original Value of %s"
msgstr "Warnung für den ursprünglichen Wert von %s"

#. module: account_asset
#: model:ir.model.fields,field_description:account_asset.field_account_asset__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: account_asset
#: model:ir.model.fields,help:account_asset.field_account_asset__state
msgid ""
"When an asset is created, the status is 'Draft'.\n"
"If the asset is confirmed, the status goes in 'Running' and the depreciation lines can be posted in the accounting.\n"
"The 'On Hold' status can be set manually when you want to pause the depreciation of an asset for some time.\n"
"You can manually close an asset when the depreciation is over.\n"
"By cancelling an asset, all depreciation entries will be reversed"
msgstr ""
"Beim Anlegen eines Vermögensgegenstandes ist der Status „Entwurf“.\n"
"Wird der Vermögensgegenstand bestätigt, geht der Status auf „Laufend“ und die Abschreibungszeilen können in der Buchhaltung gebucht werden.\n"
"Der Status „Zurückgestellt“ kann manuell gesetzt werden, wenn die Abschreibung eines Vermögensgegenstandes für einige Zeit unterbrochen werden soll.\n"
"Nach Ablauf der Abschreibung kann ein Vermögensgegenstand manuell abgeschlossen werden. Wird ein Vermögensgegenstand storniert, werden alle Abschreibungsbuchungen rückgängig gemacht"

#. module: account_asset
#: model:ir.model.fields.selection,name:account_asset.selection__account_asset__method_period__12
#: model:ir.model.fields.selection,name:account_asset.selection__asset_modify__method_period__12
msgid "Years"
msgstr "Jahre"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid ""
"You can't post an entry related to a draft asset. Please post the asset "
"before."
msgstr ""
"Sie können keinen Eintrag zu einem Vermögensgegenstandsentwurf "
"veröffentlichen. Bitte buchen Sie den Vermögensgegenstand vorher."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You can't re-evaluate the asset before the lock date."
msgstr ""
"Sie können einen Vermögensgegenstand nicht vor dem Sperrdatum neu bewerten."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot add or remove bills when the asset is already running or closed."
msgstr ""
"Sie können keine Rechnungen hinzufügen oder entfernen, wenn der "
"Vermögensgegenstand bereits läuft oder geschlossen wurde."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot archive a record that is not closed"
msgstr "Sie können keinen Eintrag archivieren der noch nicht geschlossen ist."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid ""
"You cannot automate the journal entry for an asset that has a running gross "
"increase. Please use 'Dispose' on the increase(s)."
msgstr ""
"Sie können die Journalbuchung für einen Vermögenswert, der eine laufende "
"Bruttoerhöhung aufweist, nicht automatisieren. Bitte verwenden Sie "
"„Veräußern“ für die Erhöhung(en)."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot create an asset from lines containing credit and debit on the "
"account or with a null amount"
msgstr ""
"Es ist nicht möglich, eine Anlage aus Buchungszeilen mit Haben und Soll auf "
"dem Konto oder mit einem Nullbetrag anzulegen."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot delete a document that is in %s state."
msgstr "Sie dürfen kein Dokument löschen, welches den Status %s hat."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid ""
"You cannot delete an asset linked to posted entries.\n"
"You should either confirm the asset, then, sell or dispose of it, or cancel the linked journal entries."
msgstr ""
"Sie können einen Vermögensgegenstand, der mit Buchungen verknüpft ist, nicht löschen.\n"
"Sie sollten den Vermögensgegenstand entweder bestätigen und dann verkaufen oder veräußern oder die verknüpften Journalbuchungen stornieren."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "You cannot dispose of an asset before the lock date."
msgstr ""
"Sie können einen Vermögensgegenstand nicht vor dem Sperrdatum veräußern."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_move.py:0
msgid "You cannot reset to draft an entry related to a posted asset"
msgstr ""
"Sie können den Entwurf eines Eintrags für einen gebuchten Aktivposten nicht "
"zurücksetzen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You cannot resume at a date equal to or before the pause date"
msgstr ""
"Sie können nicht zu einem Datum fortfahren, das dem Datum der Unterbrechung "
"entspricht oder davor liegt."

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "You cannot select the same account as the Depreciation Account"
msgstr "Sie können nicht dasselbe Konto wie das Abschreibungskonto auswählen"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_balance
msgid "book_value"
msgstr "book_value"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_date_from
#: model:account.report.column,name:account_asset.assets_report_depre_date_from
msgid "date from"
msgstr "Datum vom"

#. module: account_asset
#: model:account.report.column,name:account_asset.assets_report_assets_date_to
#: model:account.report.column,name:account_asset.assets_report_depre_date_to
msgid "date to"
msgstr "Datum bis"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "depreciable)"
msgstr "abschreibungsfähig)"

#. module: account_asset
#: model_terms:ir.ui.view,arch_db:account_asset.view_account_asset_form
msgid "e.g. Laptop iBook"
msgstr "z. B. Laptop iBook"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "entries"
msgstr "Buchungen"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/models/account_asset.py:0
msgid "entry"
msgstr "Buchung"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "gain"
msgstr "Gewinn"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "gain/loss"
msgstr "Gewinn/Verlust"

#. module: account_asset
#. odoo-python
#: code:addons/account_asset/wizard/asset_modify.py:0
msgid "loss"
msgstr "Verlust"
