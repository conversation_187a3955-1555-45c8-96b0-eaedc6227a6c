# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* account_base_import
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-12-19 09:52+0000\n"
"PO-Revision-Date: 2024-09-25 09:43+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(end of year balances)</span>"
msgstr "<span class=\"text-muted\">(年度末残高)</span>"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "<span class=\"text-muted\">(for full history)</span>"
msgstr "<span class=\"text-muted\">(全履歴)</span>"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_account
msgid "Account"
msgstr "勘定科目"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Account Winbooks Import module"
msgstr "勘定科目Winbooksインポートモジュール"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_import_summary
msgid "Account import summary view"
msgstr "勘定科目インポート概要ビュー"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Accounting Import"
msgstr "会計インポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
#: model:ir.actions.client,name:account_base_import.action_open_import_guide
msgid "Accounting Import Guide"
msgstr "会計インポートご案内"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Accounting Import Options"
msgstr "会計インポートオプション"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_base_import_import
msgid "Base Import"
msgstr "基本インポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
#: model:ir.actions.act_window,name:account_base_import.action_open_coa_setup
msgid "Chart of Accounts"
msgstr "勘定科目表"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Choose how you want to setup your CoA"
msgstr "勘定科目表をどのように設定するかを選択"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__create_uid
msgid "Created by"
msgstr "作成者"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__create_date
msgid "Created on"
msgstr "作成日"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
msgid "Customers"
msgstr "顧客"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__display_name
msgid "Display Name"
msgstr "表示名"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Download"
msgstr "ダウンロード"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Excel Import"
msgstr "Excelでインポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "FEC"
msgstr "FEC"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "FEC Import module"
msgstr "FECインポートモジュール"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__id
msgid "ID"
msgstr "ID"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
#: model_terms:ir.ui.view,arch_db:account_base_import.view_account_base_import_list
#: model_terms:ir.ui.view,arch_db:account_base_import.view_account_setup_base_import_list
msgid "Import"
msgstr "インポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_import
msgid "Import Chart of Accounts"
msgstr "勘定科目表をインポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import CoA"
msgstr "勘定科目表をインポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import Contacts"
msgstr "連絡先をインポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model:ir.actions.client,name:account_base_import.action_account_move_line_import
msgid "Import Journal Items"
msgstr "仕訳項目をインポート"

#. module: account_base_import
#: model:ir.actions.client,name:account_base_import.action_partner_import
msgid "Import Partners"
msgstr "取引先をインポート"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/wizard/account_import_summary.py:0
msgid "Import Summary"
msgstr "概要をインポート"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_account_ids
msgid "Import Summary Account"
msgstr "概要勘定科目をインポート"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_journal_ids
msgid "Import Summary Journal"
msgstr "概要仕訳帳をインポート"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_move_ids
msgid "Import Summary Move"
msgstr "概要仕訳をインポート"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_name
msgid "Import Summary Name"
msgstr "概要名をインポート"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_partner_ids
msgid "Import Summary Partner"
msgstr "概要取引先をインポート"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__import_summary_tax_ids
msgid "Import Summary Tax"
msgstr "概要税をインポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import contacts"
msgstr "連絡先をインポート"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import customers or suppliers (partners) and their contacts using a"
msgstr "お客様や仕入先 (取引先)の連絡先情報をインポートするに"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Import the Chart of Accounts and initial balances using a"
msgstr "勘定科目表及び開始残高をインポートするに"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "Imported Data"
msgstr "データをインポート"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Initial Setup"
msgstr "初期セットアップ"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_guide.js:0
msgid "Install a module"
msgstr "モジュールをインストール"

#. module: account_base_import
#: model:ir.model,name:account_base_import.model_account_move_line
msgid "Journal Item"
msgstr "仕訳項目"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/js/account_import_action.js:0
msgid "Journal Items"
msgstr "仕訳項目"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: account_base_import
#: model:ir.model.fields,field_description:account_base_import.field_account_import_summary__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Most accounting software in Europe support exporting SAF-T file for audit purposes.\n"
"                            Use the"
msgstr "フランスの大抵の会計ソフトは、監査目的としたSAF-Tファイルのエクスポートに対応しています。"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Most accounting software in France support exporting FEC file for audit purposes.\n"
"                            Use the"
msgstr "フランス大抵の会計ソフトは、監査目的としたFECファイルのエクスポートに対応しています。"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "No data was imported."
msgstr "インポートされたデータはありません。"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Optional, but useful to import open receivables & payables using a"
msgstr "オプションですが、役に立ちます。未決済の買掛・売掛をインポートするに"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
#: model_terms:ir.ui.view,arch_db:account_base_import.res_config_settings_import_view_form
msgid "Review Manually"
msgstr "手動で確認"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SAF-T"
msgstr "SAF-T"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SAF-T Import module"
msgstr "SAF-Tインポートモジュール"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE 5"
msgstr "SIE 5"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "SIE Import module"
msgstr "SIEインポートモジュール"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"The SIE standard file format is very common in Sweden for several purposes "
"such as auditing, importing and exporting data from and to other accounting "
"softwares."
msgstr "SIE標準ファイル形式は、監査、他の会計ソフトからのデータのインポートやエクスポートといった目的のためにスウェーデンでは非常に一般的です。"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_move_line.py:0
msgid "The import file is missing the following required columns: %s"
msgstr "インポートファイルには以下の必須カラムがありません：%s"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Tip: we recommend importing your initial balances using the Chart of Account"
" import. Only use the Journal Items import for unreconciled entries in your "
"Payable and Receivable Accounts."
msgstr ""
"ヒント: "
"開始残高のインポートは、勘定科目表インポートを使用することをお勧めします。売掛及び買掛の勘定科目に未収・未払の項目がある場合のみ、仕訳項目のインポートを使用してください。"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Use predefined format to import your data faster."
msgstr "既定フォーマットで、より速くデータをインポートします。"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Use templates to import CSV or Excel for your accounting setup."
msgstr "会計設定をするために、テンプレートを使ってCSVやExcelをインポートします。"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"We will setup your charts of accounts and the history of journal entries, "
"that will stay in draft."
msgstr "勘定科目表と仕訳履歴を設定し、ドラフトに残します。"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "Winbooks"
msgstr "Winbooks"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"Winbooks is an old school Belgian accounting software acquired by Exact.\n"
"                            Use the"
msgstr "Winbooksは、Exactに買収されたベルギーの会計ソフトです。"

#. module: account_base_import
#. odoo-python
#: code:addons/account_base_import/models/account_account.py:0
msgid ""
"You must provide both the `code_mapping_ids/company_id` and the "
"`code_mapping_ids/code` columns."
msgstr "`code_mapping_ids/company_id` および `code_mapping_ids/code` 列両方が必要です。"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "accounts imported"
msgstr "インポート済勘定科目"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"is required to import the SIE and SIE entry files.\n"
"                            For general SIE, we will setup your charts of accounts balances, journals, partners, and the history of journal entries (journals data must be present in the file).\n"
"                            For the SIE entry, only entries and partners will be created, the rest must already be present in the system."
msgstr ""
"SIEまたはSIEエントリファイル。\n"
"                            一般的なSIEでは、勘定科目残高、仕訳、取引先、仕訳履歴(仕訳データはファイル内に存在する必要があります)を設定します。\n"
"                            SIEエントリ用には、エントリおよび仕入先のみ作成され、残りはシステム上に既に存在していなければなりません。"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "journals imported"
msgstr "インポート済仕訳帳"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "moves imported"
msgstr "インポート済記帳"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "or"
msgstr "又は"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "partners imported"
msgstr "インポート済取引先"

#. module: account_base_import
#: model_terms:ir.ui.view,arch_db:account_base_import.account_import_summary_form
msgid "taxes imported"
msgstr "インポート済税"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "template."
msgstr "テンプレートを使います。"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"to import a Winbooks full back-up (Maintenance > Backup) to get the chart of accounts, contacts, taxes, history of journal entries, and documents.\n"
"                            Support versions: Winbooks Desktop 5.50, 6, 7, 8."
msgstr ""
"を使って、Winbooks完全バックアップ (整備 > バックアップ)をインポートして、勘定科目表、連絡先、税金、仕訳履歴、及び書類を取得します。\n"
"対応バージョン: Winbooks Desktop 5.50, 6, 7, 8"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid ""
"to import the FEC file. We will setup your charts of accounts and the "
"history of journal entries."
msgstr "を使って、FECファイルをインポートしましょう。こちらが勘定科目表及び仕訳履歴を設定します。"

#. module: account_base_import
#. odoo-javascript
#: code:addons/account_base_import/static/src/xml/account_import.xml:0
msgid "to import the SAF-T file."
msgstr " SAF-Tファイルをインポート。"
