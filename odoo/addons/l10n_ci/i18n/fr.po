# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ci
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 09:52+0000\n"
"PO-Revision-Date: 2023-11-30 09:52+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_operations
msgid "02. Operations realised"
msgstr "02. Opérations réalisées"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_turnover
msgid "03. Taxable turnover"
msgstr "°3. Chi<PERSON>re d'affaires taxable"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_adjustment
msgid "04. Adjustment of previously deducted vat to be repaid"
msgstr "04. Régularisation TVA antérieurement déduite à reverser"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_gross
msgid "05. Total gross VAT"
msgstr "05. Total TVA brute"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_deduction
msgid "06. Deductions"
msgstr "06. Déductions"

#. module: l10n_ci
#: model:ir.model,name:l10n_ci.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de Plan Comptable"

#. module: l10n_ci
#: model:account.report.column,name:l10n_ci.account_tax_report_ci_base
msgid "Base"
msgstr "Base"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_exempt_conventional
msgid "Conventional exempt operations"
msgstr "Opérations exonérées conventionnelles"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_asked_reimbursement
msgid "Credit asked to be reimbursed"
msgstr "Crédit demandé en remboursement"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_to_report
msgid "Credit to report"
msgstr "Crédit à reporter"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_deductible
msgid "Deductible VAT"
msgstr "TVA déductible"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_exportation
msgid "Exportations"
msgstr "Exportations"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_tva_credit
msgid "Last month's credit reported"
msgstr "Crédit reporté du mois précédent"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_exempt_legal
msgid "Legal exempt operations"
msgstr "Opérations exonérées légales"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_net_to_pay
msgid "Net VAT to pay (05 - 06)"
msgstr "TVA nette à payer (05 - 06)"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_self_delivery_18
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_turnover_18
msgid "Normal rate"
msgstr "Taux normal"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_non_taxed
msgid "Other non taxed operations"
msgstr "Autres opérations non taxables"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_self_delivery_9
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_turnover_9
msgid "Reduced rate"
msgstr "Taux réduit"

#. module: l10n_ci
#. odoo-python
#: code:addons/l10n_ci/models/template_ci_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_ci
#. odoo-python
#: code:addons/l10n_ci/models/template_ci.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_self_delivery
msgid "Self delivery or service"
msgstr "Livraisons à soi-même de biens ou services"

#. module: l10n_ci
#: model:account.report.column,name:l10n_ci.account_tax_report_ci_tax
msgid "Tax"
msgstr "Taxe"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_18
msgid "Taxable - normal rate"
msgstr "Taxable - taux normal"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable_9
msgid "Taxable - reduced rate"
msgstr "Taxable - taux réduit"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_taxable
msgid "Taxable operations"
msgstr "Opérations taxable"

#. module: l10n_ci
#: model:account.report.line,name:l10n_ci.account_tax_report_line_ci_operations_total
msgid "Total amount of operations"
msgstr "Montant total des opérations"

#. module: l10n_ci
#: model:account.report,name:l10n_ci.account_tax_report_ci
msgid "VAT Report"
msgstr "Déclaration TVA"
