<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record model="ir.ui.view" id="res_config_settings_view_form">
        <field name="name">res.config.settings.view.form.inherit.l10n_sa_edi</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="account.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//app[@name='account']/block" position="before">
                <field name="country_code" invisible="1"/>
                <block title="ZATCA E-Invoicing Settings" name="zatca_einvoicing_setting_container" invisible="country_code != 'SA'">
                    <setting string="ZATCA E-Invoicing Settings" company_dependent="1" help="ZATCA specific settings for Saudi eInvoicing">
                        <div class="text-muted">
                            You can select the API used for submissions down below. There are three modes available: Sandbox, Pre-Production and Production.
                            Once you have selected the correct API, you can start the Onboarding process by going to the Journals and checking the options under the ZATCA tab.
                        </div>
                        <div class="content-group">
                            <div class="row mt16">
                                <label for="l10n_sa_api_mode" string="API Mode" class="col-3 o_light_label"/>
                                <field name="l10n_sa_api_mode" help="Set whether the system should use the Production API"/>
                            </div>
                        </div>
                        <div class="alert alert-warning mt8" role="alert">
                            <h4 class="alert-heading" role="alert">
                                <i class="fa fa-warning me-2" /> Warning
                            </h4>
                            Once you change the submission mode to <strong>Production</strong>, you cannot change it anymore.
                            Be very careful, as any invoice submitted to ZATCA in Production mode will be accounted for
                            and might lead to <strong>Fines &amp; Penalties</strong>.
                        </div>
                    </setting>
                </block>
            </xpath>
        </field>
    </record>

</odoo>
