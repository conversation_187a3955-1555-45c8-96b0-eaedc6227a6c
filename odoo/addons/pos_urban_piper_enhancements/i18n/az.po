# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_urban_piper_enhancements
# 
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# <AUTHOR> <EMAIL>, 2025
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:56+0000\n"
"PO-Revision-Date: 2025-01-28 08:25+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_urban_piper_enhancements
#: model_terms:ir.actions.act_window,help:pos_urban_piper_enhancements.action_pos_store_timing
msgid "Add a store timing"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_product_template_attribute_value__urbanpiper_pos_config_ids
msgid "Available on Food Delivery"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,help:pos_urban_piper_enhancements.field_product_template_attribute_value__urbanpiper_pos_config_ids
msgid "Check this if the value is available for food delivery."
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_order__delivery_datetime
msgid "Delivery Datetime"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__display_name
msgid "Display Name"
msgstr "Göstəriləcək Ad"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__end_hour
msgid "Ending Hour"
msgstr "Bitiş saatı"

#. module: pos_urban_piper_enhancements
#. odoo-javascript
#: code:addons/pos_urban_piper_enhancements/static/src/point_of_sale_override/store/pos_store.js:0
msgid "Error to send delivery order in preparation display."
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,help:pos_urban_piper_enhancements.field_pos_order__is_notified
msgid "Flag indicating if the delivery notification has been sent."
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields.selection,name:pos_urban_piper_enhancements.selection__pos_store_timing__weekday__friday
msgid "Friday"
msgstr "Cümə günü"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__id
msgid "ID"
msgstr "ID"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_order__is_notified
msgid "Is Notified"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: pos_urban_piper_enhancements
#: model_terms:ir.actions.act_window,help:pos_urban_piper_enhancements.action_pos_store_timing
msgid "Manage store timings for POS delivery order(s)."
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_config__urbanpiper_minimum_preparation_time
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_res_config_settings__pos_urbanpiper_minimum_preparation_time
msgid "Minimum Preparation Time (Seconds)"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields.selection,name:pos_urban_piper_enhancements.selection__pos_store_timing__weekday__monday
msgid "Monday"
msgstr "Bazar ertəsi"

#. module: pos_urban_piper_enhancements
#: model_terms:ir.ui.view,arch_db:pos_urban_piper_enhancements.view_pos_order_form_inherit_pos_urban_piper
msgid "Online Order ID"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Satış Nöqtəsi Konfiqurasiyası"

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_pos_order
msgid "Point of Sale Orders"
msgstr "Satış Nöqtəsi Sifarişləri"

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_pos_session
msgid "Point of Sale Session"
msgstr "Satış Nöqtəsi Sessiyası"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__config_ids
msgid "Point of Sale accociated to this timing"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_pos_store_timing
msgid "Pos Store Timings"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model_terms:ir.ui.view,arch_db:pos_urban_piper_enhancements.res_config_settings_view_form_pos_urban_piper_enhancements
msgid "Preparation Time(seconds)"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_pos_preparation_display_order
msgid "Preparation orders"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_product_template
msgid "Product"
msgstr "Məhsul"

#. module: pos_urban_piper_enhancements
#: model:ir.model,name:pos_urban_piper_enhancements.model_product_template_attribute_value
msgid "Product Template Attribute Value"
msgstr "Məhsul Şablon Atribut Dəyəri"

#. module: pos_urban_piper_enhancements
#: model_terms:ir.ui.view,arch_db:pos_urban_piper_enhancements.report_invoice_document_pos_urban_piper_enhancements
msgid "Reference"
msgstr "Rəy"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields.selection,name:pos_urban_piper_enhancements.selection__pos_store_timing__weekday__saturday
msgid "Saturday"
msgstr "Şənbə günü"

#. module: pos_urban_piper_enhancements
#. odoo-javascript
#: code:addons/pos_urban_piper_enhancements/static/src/pos_preparation_display_override/components/order/order.xml:0
msgid "Scheduled At:"
msgstr ""

#. module: pos_urban_piper_enhancements
#. odoo-javascript
#: code:addons/pos_urban_piper_enhancements/static/src/point_of_sale_override/store/pos_store.js:0
msgid "Scheduled Order"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,help:pos_urban_piper_enhancements.field_pos_order__delivery_datetime
msgid "Scheduled delivery datetime for the delivery order."
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__start_hour
msgid "Starting Hour"
msgstr "Başlama saatı"

#. module: pos_urban_piper_enhancements
#: model:ir.actions.act_window,name:pos_urban_piper_enhancements.action_pos_store_timing
#: model:ir.ui.menu,name:pos_urban_piper_enhancements.menu_products_pos_store_timing
#: model_terms:ir.ui.view,arch_db:pos_urban_piper_enhancements.view_pos_store_timing_list
msgid "Store Timings"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields.selection,name:pos_urban_piper_enhancements.selection__pos_store_timing__weekday__sunday
msgid "Sunday"
msgstr "Bazar günü"

#. module: pos_urban_piper_enhancements
#: model:ir.model.constraint,message:pos_urban_piper_enhancements.constraint_pos_store_timing_check_start_and_end_hour
msgid "The end time must be later than the start time."
msgstr "Bitiş vaxtı başlama vaxtından sonra olmalıdır."

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,help:pos_urban_piper_enhancements.field_pos_config__urbanpiper_minimum_preparation_time
#: model:ir.model.fields,help:pos_urban_piper_enhancements.field_res_config_settings__pos_urbanpiper_minimum_preparation_time
msgid ""
"The minimum amount of time the customer must wait for the order to be "
"prepared."
msgstr ""

#. module: pos_urban_piper_enhancements
#. odoo-javascript
#: code:addons/pos_urban_piper_enhancements/static/src/point_of_sale_override/screens/ticket_screen/ticket_screen.xml:0
msgid "This order is scheduled order."
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields.selection,name:pos_urban_piper_enhancements.selection__pos_store_timing__weekday__thursday
msgid "Thursday"
msgstr "Cümə axşamı"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields.selection,name:pos_urban_piper_enhancements.selection__pos_store_timing__weekday__tuesday
msgid "Tuesday"
msgstr "Çərşənbə axşamı"

#. module: pos_urban_piper_enhancements
#: model_terms:ir.ui.view,arch_db:pos_urban_piper_enhancements.view_pos_order_form_inherit_pos_urban_piper
msgid "Urban Piper"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.ui.menu,name:pos_urban_piper_enhancements.pos_menu_urban_piper_configuration
msgid "UrbanPiper"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.actions.server,name:pos_urban_piper_enhancements.cron_check_future_delivery_orders_ir_actions_server
msgid "UrbanPiper: Notify future delivery orders"
msgstr ""

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields.selection,name:pos_urban_piper_enhancements.selection__pos_store_timing__weekday__wednesday
msgid "Wednesday"
msgstr "Çərşənbə günü"

#. module: pos_urban_piper_enhancements
#: model:ir.model.fields,field_description:pos_urban_piper_enhancements.field_pos_store_timing__weekday
msgid "Week Day"
msgstr "Həftə günü"
