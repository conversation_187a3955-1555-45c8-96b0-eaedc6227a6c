<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report_ke" model="account.report">
        <field name="name">Tax Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.ke"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_base_column" model="account.report.column">
                <field name="name">Base</field>
                <field name="expression_label">base</field>
            </record>
            <record id="tax_report_tax_column" model="account.report.column">
                <field name="name">VAT</field>
                <field name="expression_label">tax</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tax_report_line_general_rate_sales" model="account.report.line">
                <field name="name">1. Taxable Sales (General Rate 16%)</field>
                <field name="code">box_1</field>
                <field name="expression_ids">
                    <record id="tax_report_general_rate_sales_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">16% Sales Base</field>
                    </record>
                    <record id="tax_report_general_rate_sales_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">16% Sales Tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_other_rate_sales" model="account.report.line">
                <field name="name">2. Taxable Sales (Other Rate 8%)</field>
                <field name="code">box_2</field>
                <field name="expression_ids">
                    <record id="tax_report_other_rate_sales_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">8% Sales Base</field>
                    </record>
                    <record id="tax_report_other_rate_sales_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">8% Sales Tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_zero_rated_sales" model="account.report.line">
                <field name="name">3. Sales (Zero Rated 0%)</field>
                <field name="code">box_3</field>
                <field name="expression_ids">
                    <record id="tax_report_zero_rated_sales_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Zero Rated Sales Base</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_exempt_sales" model="account.report.line">
                <field name="name">4. Sales (Exempt)</field>
                <field name="code">box_4</field>
                <field name="expression_ids">
                    <record id="tax_report_exempt_sales_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Exempt Sales Base</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_total_sales" model="account.report.line">
                <field name="name">5. Total Sales</field>
                <field name="code">box_5</field>
                <field name="expression_ids">
                    <record id="tax_report_total_sales_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_1.base + box_2.base + box_3.base + box_4.base</field>
                    </record>
                    <record id="tax_report_total_sales_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_1.tax + box_2.tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_output_vat" model="account.report.line">
                <field name="name">6. Total Output VAT</field>
                <field name="code">box_6</field>
                <field name="expression_ids">
                    <record id="tax_report_output_vat_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_1.base + box_2.base + box_3.base</field>
                    </record>
                    <record id="tax_report_output_vat_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_1.tax + box_2.tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_general_rate_purchases" model="account.report.line">
                <field name="name">7. Taxable Purchases (General Rate 16%)</field>
                <field name="code">box_7</field>
                <field name="expression_ids">
                    <record id="tax_report_general_rate_purchases_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">16% Purchases Base</field>
                    </record>
                    <record id="tax_report_general_rate_purchases_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">16% Purchases Tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_other_rate_purchases" model="account.report.line">
                <field name="name">8. Taxable Purchases (Other Rate 8%)</field>
                <field name="code">box_8</field>
                <field name="expression_ids">
                    <record id="tax_report_other_rate_purchases_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">8% purchases Base</field>
                    </record>
                    <record id="tax_report_other_rate_purchases_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">8% Purchases Tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_zero_rated_purchases" model="account.report.line">
                <field name="name">9. Purchases (Zero Rated 0%)</field>
                <field name="code">box_9</field>
                <field name="expression_ids">
                    <record id="tax_report_zero_rated_purchases_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Zero Rated Purchases Base</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_exempt_purchases" model="account.report.line">
                <field name="name">10. Purchases (Exempt)</field>
                <field name="code">box_10</field>
                <field name="expression_ids">
                    <record id="tax_report_exempt_purchases_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Exempt Purchases Base</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_total_purchases" model="account.report.line">
                <field name="name">11. Total Purchases</field>
                <field name="code">box_11</field>
                <field name="expression_ids">
                    <record id="tax_report_total_purchases_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_7.base + box_8.base + box_9.base + box_10.base</field>
                    </record>
                    <record id="tax_report_total_purchases_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_7.tax + box_8.tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_input_vat" model="account.report.line">
                <field name="name">12. Total Input VAT</field>
                <field name="code">box_12</field>
                <field name="expression_ids">
                    <record id="tax_report_input_vat_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_7.base + box_8.base + box_9.base</field>
                    </record>
                    <record id="tax_report_input_vat_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_7.tax + box_8.tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_vat_claimable_on_imported_services" model="account.report.line">
                <field name="name">13. VAT Claimable on Services Imported into Kenya</field>
                <field name="code">box_13</field>
                <field name="expression_ids">
                    <record id="tax_report_vat_claimable_on_imported_services_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Import Base</field>
                    </record>
                    <record id="tax_report_vat_claimable_on_imported_services_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">Import Tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_input_vat_attributable_to_exempt_supplies" model="account.report.line">
                <field name="name">14. Input VAT attributable to Only Exempt Supplies</field>
                <field name="code">box_14</field>
                <field name="expression_ids">
                    <record id="tax_report_input_vat_attributable_to_exempt_supplies_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">external</field>
                        <field name="formula">sum</field>
                        <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_input_vat_attributable_to_taxable_and_exempt_supplies" model="account.report.line">
                <field name="name">15. Input VAT attributable to Taxable and Exempt Supplies</field>
                <field name="code">box_15</field>
                <field name="expression_ids">
                    <record id="tax_report_input_vat_attributable_to_taxable_and_exempt_supplies_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">external</field>
                        <field name="formula">sum</field>
                        <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_non_deductible_input_vat" model="account.report.line">
                <field name="name">16. Non-Deductible Input VAT</field>
                <field name="code">box_16</field>
                <field name="expression_ids">
                    <record id="tax_report_non_deductible_input_vat_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_15.tax - (((box_1.tax + box_2.tax + box_3.base) / box_5.base) * box_15.tax)</field>
                        <field name="subformula">ignore_zero_division</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_deductible_input_vat" model="account.report.line">
                <field name="name">17. Deductible Input VAT</field>
                <field name="code">box_17</field>
                <field name="expression_ids">
                    <record id="tax_report_deductible_input_vat_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_12.tax + box_13.tax - box_14.tax - box_16.tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_vat_payable_or_credit_due_for_period" model="account.report.line">
                <field name="name">18. VAT Payable/Credit Due for the period</field>
                <field name="code">box_18</field>
                <field name="expression_ids">
                    <record id="tax_report_vat_payable_or_credit_due_for_period_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_6.tax - box_17.tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_credit_brought_forward_from_previous_month" model="account.report.line">
                <field name="name">19. Credit Brought Forward from previous month</field>
                <field name="code">box_19</field>
                <field name="expression_ids">
                    <record id="tax_report_credit_brought_forward_from_previous_month_applied_carryover" model="account.report.expression">
                        <field name="label">_applied_carryover_tax</field>
                        <field name="engine">external</field>
                        <field name="formula">most_recent</field>
                        <field name="date_scope">previous_tax_period</field>
                    </record>
                    <record id="tax_report_credit_brought_forward_from_previous_month_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">-box_19._applied_carryover_tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_total_withholding_vat_credit" model="account.report.line">
                <field name="name">20. Total Withholding VAT Credit</field>
                <field name="code">box_20</field>
                <field name="expression_ids">
                    <record id="tax_report_total_withholding_vat_credit_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">WH base</field>
                    </record>
                    <record id="tax_report_total_withholding_vat_credit_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">tax_tags</field>
                        <field name="formula">WH Sales</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_refund_claim_lodged" model="account.report.line">
                <field name="name">21. Refund Claim Lodged</field>
                <field name="code">box_21</field>
                <field name="expression_ids">
                    <record id="tax_report_refund_claim_lodged_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">external</field>
                        <field name="formula">sum</field>
                        <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_total_vat_payable" model="account.report.line">
                <field name="name">22. Total VAT Payable</field>
                <field name="code">box_22</field>
                <field name="expression_ids">
                    <record id="tax_report_total_vat_payable_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_18.tax - box_19.tax - box_20.tax + box_21.tax</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_total_vat_paid" model="account.report.line">
                <field name="name">23. Total VAT Paid</field>
                <field name="code">box_23</field>
                <field name="expression_ids">
                    <record id="tax_report_vat_paid_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">external</field>
                        <field name="formula">sum</field>
                        <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_total_credit_adjustment_inventory_approval_order" model="account.report.line">
                <field name="name">24. Total Credit Adjustment/Inventory Approval Order</field>
                <field name="code">box_24</field>
                <field name="expression_ids">
                    <record id="tax_report_total_credit_adjustment_inventory_approval_order_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">external</field>
                        <field name="formula">sum</field>
                        <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_total_debit_adjustment_voucher" model="account.report.line">
                <field name="name">25. Total Debit Adjustment Voucher</field>
                <field name="code">box_25</field>
                <field name="expression_ids">
                    <record id="tax_report_total_debit_adjustment_voucher_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">external</field>
                        <field name="formula">sum</field>
                        <field name="subformula">editable;rounding=2</field>
                    </record>
                </field>
            </record>
            <record id="tax_report_line_net_vat_payable_or_credit_carried_forward" model="account.report.line">
                <field name="name">26. Net VAT Payable/Credit Carried Forward</field>
                <field name="code">box_26</field>
                <field name="expression_ids">
                    <record id="tax_report_net_vat_payable_or_credit_carried_forward_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_22.tax - box_23.tax - box_24.tax + box_25.tax</field>
                    </record>
                    <record id="tax_report_net_vat_payable_or_credit_carried_forward_carryover" model="account.report.expression">
                        <field name="label">_carryover_tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">box_26.tax</field>
                        <field name="carryover_target">box_19._applied_carryover_tax</field>
                        <field name="subformula">if_below(RON(0))</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
