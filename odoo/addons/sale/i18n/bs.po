# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * sale
#
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2018-10-08 06:49+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2018\n"
"Language-Team: Bosnian (https://www.transifex.com/odoo/teams/41243/bs/)\n"
"Language: bs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard___data_fetched
msgid " Data Fetched"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sale_order_count
msgid "# Sale Orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__nbr
msgid "# of Lines"
msgstr "# Linija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids_nbr
msgid "# of Sales Orders"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "%(line_description)s (Canceled)"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "%(line_description)s (Draft)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_pro_forma_invoice
msgid "'PRO-FORMA - %s' % (object.name)"
msgstr ""

#. module: sale
#: model:ir.actions.report,print_report_name:sale.action_report_saleorder
msgid "(object.state in ('draft', 'sent') and 'Quotation - %s' % (object.name)) or 'Order - %s' % (object.name)"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "<b>Congratulations</b>, your first quotation is sent!<br>Check your email to validate the quote."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "<b>Send the quote</b> to yourself and check what the customer will receive."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "<b>Set a price</b>."
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_confirmation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 12px;\">\n"
"        Hello,\n"
"        <br><br>\n"
"        <t t-set=\"tx_sudo\" t-value=\"object.get_portal_last_transaction()\"></t>\n"
"        Your order <span style=\"font-weight:bold;\" t-out=\"object.name or ''\">S00049</span> amounting in <span style=\"font-weight:bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span>\n"
"        <t t-if=\"object.state == 'sale' or (tx_sudo and tx_sudo.state in ('done', 'authorized'))\">\n"
"            has been confirmed.<br>\n"
"            Thank you for your trust!\n"
"        </t>\n"
"        <t t-elif=\"tx_sudo and tx_sudo.state == 'pending'\">\n"
"            is pending. It will be confirmed when the payment is received.\n"
"            <t t-if=\"object.reference\">\n"
"                Your payment reference is <span style=\"font-weight:bold;\" t-out=\"object.reference or ''\"></span>.\n"
"            </t>\n"
"        </t>\n"
"        <br><br>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"<t t-if=\"hasattr(object, 'website_id') and object.website_id\">\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"            <tr style=\"border-bottom: 2px solid #dee2e6;\">\n"
"                <td style=\"width: 150px;\"><span style=\"font-weight:bold;\">Products</span></td>\n"
"                <td></td>\n"
"                <td width=\"15%\" align=\"center\"><span style=\"font-weight:bold;\">Quantity</span></td>\n"
"                <td width=\"20%\" align=\"right\">\n"
"                    <span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            VAT Excl.\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            VAT Incl.\n"
"                        </t>\n"
"                    </span>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <t t-foreach=\"object.order_line\" t-as=\"line\">\n"
"            <t t-if=\"(not hasattr(line, 'is_delivery') or not line.is_delivery) and line.display_type in ['line_section', 'line_note']\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"></t>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"></t>\n"
"                        <td colspan=\"4\">\n"
"                            <t t-if=\"line.display_type == 'line_section'\">\n"
"                                <span style=\"font-weight:bold;\" t-out=\"line.name or ''\">Taking care of Trees Course</span>\n"
"                            </t>\n"
"                            <t t-elif=\"line.display_type == 'line_note'\">\n"
"                                <i t-out=\"line.name or ''\">Taking care of Trees Course</i>\n"
"                            </t>\n"
"                        </td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"            <t t-elif=\"(not hasattr(line, 'is_delivery') or not line.is_delivery)\">\n"
"                <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-collapse: collapse;\">\n"
"                    <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number or 0\"></t>\n"
"                    <tr t-att-style=\"'background-color: #f2f2f2' if loop_cycle_number % 2 == 0 else 'background-color: #ffffff'\">\n"
"                        <t t-set=\"loop_cycle_number\" t-value=\"loop_cycle_number + 1\"></t>\n"
"                        <td style=\"width: 150px;\">\n"
"                            <img t-attf-src=\"/web/image/product.product/{{ line.product_id.id }}/image_128\" style=\"width: 64px; height: 64px; object-fit: contain;\" alt=\"Product image\">\n"
"                        </td>\n"
"                        <td align=\"left\" t-out=\"line.product_id.name or ''\">\tTaking care of Trees Course</td>\n"
"                        <td width=\"15%\" align=\"center\" t-out=\"line.product_uom_qty or ''\">1</td>\n"
"                        <td width=\"20%\" align=\"right\"><span style=\"font-weight:bold;\">\n"
"                        <t t-if=\"object.website_id.show_line_subtotals_tax_selection == 'tax_excluded'\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxexcl, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        <t t-else=\"\">\n"
"                            <t t-out=\"format_amount(line.price_reduce_taxinc, object.currency_id) or ''\">$ 10.00</t>\n"
"                        </t>\n"
"                        </span></td>\n"
"                    </tr>\n"
"                </table>\n"
"            </t>\n"
"        </t>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Delivery:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">SubTotal:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\" t-else=\"\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">SubTotal:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_untaxed, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px; border-spacing: 0px 4px;\" align=\"right\">\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%;\" align=\"right\"><span style=\"font-weight:bold;\">Taxes:</span></td>\n"
"                <td style=\"width: 10%;\" align=\"right\" t-out=\"format_amount(object.amount_tax, object.currency_id) or ''\">$ 0.00</td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td style=\"width: 60%\"></td>\n"
"                <td style=\"width: 30%; border-top: 1px solid #dee2e6;\" align=\"right\"><span style=\"font-weight:bold;\">Total:</span></td>\n"
"                <td style=\"width: 10%; border-top: 1px solid #dee2e6;\" align=\"right\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_invoice_id\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td style=\"padding-top: 10px;\">\n"
"                    <span style=\"font-weight:bold;\">Bill to:</span>\n"
"                    <t t-out=\"object.partner_invoice_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_invoice_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_invoice_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_invoice_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_invoice_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Payment Method:</span>\n"
"                    <t t-if=\"tx_sudo.token_id\">\n"
"                        <t t-out=\"tx_sudo.token_id.display_name or ''\"></t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        <t t-out=\"tx_sudo.provider_id.sudo().name or ''\"></t>\n"
"                    </t>\n"
"                    (<t t-out=\"format_amount(tx_sudo.amount, object.currency_id) or ''\">$ 10.00</t>)\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"    <div t-if=\"object.partner_shipping_id and not object.only_services\" style=\"margin: 0px; padding: 0px;\">\n"
"        <table width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <br>\n"
"                    <span style=\"font-weight:bold;\">Ship to:</span>\n"
"                    <t t-out=\"object.partner_shipping_id.street or ''\">1201 S Figueroa St</t>\n"
"                    <t t-out=\"object.partner_shipping_id.city or ''\">Los Angeles</t>\n"
"                    <t t-out=\"object.partner_shipping_id.state_id.name or ''\">California</t>\n"
"                    <t t-out=\"object.partner_shipping_id.zip or ''\">90015</t>\n"
"                    <t t-out=\"object.partner_shipping_id.country_id.name or ''\">United States</t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"        <table t-if=\"hasattr(object, 'carrier_id') and object.carrier_id\" width=\"100%\" style=\"color: #454748; font-size: 12px;\">\n"
"            <tr>\n"
"                <td>\n"
"                    <span style=\"font-weight:bold;\">Shipping Method:</span>\n"
"                    <t t-out=\"object.carrier_id.name or ''\"></t>\n"
"                    <t t-if=\"object.amount_delivery == 0.0\">\n"
"                        (Free)\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        (<t t-out=\"format_amount(object.amount_delivery, object.currency_id) or ''\">$ 10.00</t>)\n"
"                    </t>\n"
"                </td>\n"
"            </tr>\n"
"            <tr t-if=\"object.carrier_id.carrier_description\">\n"
"                <td>\n"
"                    <strong>Shipping Description:</strong>\n"
"                    <t t-out=\"object.carrier_id.carrier_description\"></t>\n"
"                </td>\n"
"            </tr>\n"
"        </table>\n"
"    </div>\n"
"</t>\n"
"</div>"
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.email_template_edi_sale
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"'quotation' if object.state in ('draft', 'sent') else 'order'\"></t>\n"
"        Hello,\n"
"        <br><br>\n"
"        Your\n"
"        <t t-if=\"ctx.get('proforma')\">\n"
"            Pro forma invoice for <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\">S00052</span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\"></t> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is available.\n"
"        </t>\n"
"        <t t-else=\"\">\n"
"            <t t-out=\"doc_name or ''\">quotation</t> <span style=\"font-weight: bold;\" t-out=\"object.name or ''\"></span>\n"
"            <t t-if=\"object.origin\">\n"
"                (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"            </t>\n"
"            amounting in <span style=\"font-weight: bold;\" t-out=\"format_amount(object.amount_total, object.currency_id) or ''\">$ 10.00</span> is ready for review.\n"
"        </t>\n"
"        <br><br>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <t t-if=\"not is_html_empty(object.user_id.signature)\">\n"
"            <br><br>\n"
"            <t t-out=\"object.user_id.signature or ''\">--<br>Mitchell Admin</t>\n"
"        </t>\n"
"        <br><br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model:mail.template,body_html:sale.mail_template_sale_cancellation
msgid ""
"<div style=\"margin: 0px; padding: 0px;\">\n"
"    <p style=\"margin: 0px; padding: 0px; font-size: 13px;\">\n"
"        <t t-set=\"doc_name\" t-value=\"object.type_name\"></t>\n"
"        Dear <t t-out=\"object.partner_id.name or ''\">user</t>,\n"
"        <br><br>\n"
"        Please be advised that your\n"
"        <t t-out=\"doc_name or ''\">quotation</t> <strong t-out=\"object.name or ''\">S00052</strong>\n"
"        <t t-if=\"object.origin\">\n"
"            (with reference: <t t-out=\"object.origin or ''\">S00052</t> )\n"
"        </t>\n"
"        has been cancelled. Therefore, you should not be charged further for this order.\n"
"        If any refund is necessary, this will be executed at best convenience.\n"
"        <br><br>\n"
"        Do not hesitate to contact us if you have any questions.\n"
"        <br>\n"
"    </p>\n"
"</div>\n"
"            "
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Contact us to get a new quotation."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Feedback"
msgstr "<i class=\"fa fa-comment\"/> Utisak"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-comment\"/> Send message"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-download\"/> Download"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "<i class=\"fa fa-fw fa-check\" role=\"img\" aria-label=\"Done\" title=\"Done\"/>Done"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Authorized</b>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-check\"/> <b>Paid</b>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<i class=\"fa fa-fw fa-clock-o\"/> <b>Waiting Payment</b>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-clock-o\"/> Expired"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "<i class=\"fa fa-fw fa-money me-1\" aria-label=\"Quotations\" role=\"img\"/>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "<i class=\"fa fa-fw fa-remove\"/> Cancelled"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_lead_partner_kanban_view
msgid "<i class=\"fa fa-fw fa-usd\" role=\"img\" aria-label=\"Sale orders\" title=\"Sales orders\"/>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-print\"/> Print"
msgstr "<i class=\"fa fa-print\"/> Štampaj"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> Odbi"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                Create a new product\n"
"            </p><p>\n"
"                You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service.\n"
"            </p>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">This offer expires on</b></small>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<small><b class=\"text-muted\">Your advantage</b></small>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid ""
"<span attrs=\"{'invisible': [('advance_payment_method', '!=', 'percentage')]}\" class=\"oe_inline\">% </span>\n"
"                        <span attrs=\"{'invisible': [('display_invoice_amount_warning', '=', False)]}\" class=\"oe_inline text-danger\" title=\"The Down Payment is greater than the amount remaining to be invoiced.\">\n"
"                            <i class=\"fa fa-warning\"/>\n"
"                        </span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid ""
"<span class=\"d-none d-md-inline\">Sales Order #</span>\n"
"                            <span class=\"d-block d-md-none\">Ref.</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_salesteams_view_form
msgid "<span class=\"flex-grow-1\">/ Month</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "<span class=\"o_stat_text\">Sold</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>Accepted on the behalf of:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid ""
"<span>Are you sure you want to cancel this order? <br/></span>\n"
"                        <span id=\"display_invoice_alert\" attrs=\"{'invisible': [('display_invoice_alert', '=', False)]}\">\n"
"                            Draft invoices for this order will be cancelled. <br/>\n"
"                        </span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By paying this proposal, I agree to the following terms:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>By signing this proposal, I agree to the following terms:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Disc.%</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>For an amount of:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Payment</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<span>Signature</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Tax excl.</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Tax incl.</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<span>Taxes</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<span>With payment terms:</span>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid ""
"<strong class=\"d-block mb-1\">\n"
"                            Shipping Address:\n"
"                        </strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"d-block mb-1\">Invoices</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong class=\"mr16\">Subtotal</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong class=\"text-muted\">Salesperson</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Expiration Date:</strong>"
msgstr "<strong>Važi do:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Expiration:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Fiscal Position Remark:</strong>"
msgstr "<strong>Zabilješka fiskalne pozicije:</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid ""
"<strong>No suitable payment option could be found.</strong><br/>\n"
"                                        If you believe that it is an error, please contact the website administrator."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Order Date:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "<strong>Quotation Date:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Salesperson:</strong>"
msgstr "<strong>Prodavač(ica):</strong>"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Shipping Address:</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Signature</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<strong>Tax excl.: </strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "<strong>Tax incl.: </strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>Thank You!</strong><br/>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This offer expired!</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "<strong>This quotation has been canceled.</strong>"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "<strong>Your Reference:</strong>"
msgstr "<strong>Vaša referenca:</strong>"

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_date_order_conditional_required
msgid "A confirmed sales order requires a confirmation date."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__advance_payment_method
msgid "A standard invoice is issued with all the order lines ready for invoicing,according to their invoicing policy (based on ordered or delivered quantity)."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_type
msgid ""
"A storable product is a product for which you manage stock. The Inventory app has to be installed.\n"
"A consumable product is a product for which stock is not managed.\n"
"A service is a non-material product you provide."
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_warning_sale
msgid "A warning can be set on a product or a customer (Sale)"
msgstr "Upozorenje se može postaviti na proizvod ili kupca (Prodaja)"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Ability to select a package type in sales orders and to force a quantity that is a multiple of the number of units per package."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Pay Quotation"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Accept & Sign Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Pay"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Accept &amp; Sign"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_warning
msgid "Access warning"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__qty_delivered_method
msgid ""
"According to product configuration, the delivered quantity can be automatically computed by mechanism:\n"
"  - Manual: the quantity is set manually on the line\n"
"  - Analytic From expenses: the quantity is the quantity sum from posted expenses\n"
"  - Timesheet: the quantity is the sum of hours recorded on tasks linked to this sale line\n"
"  - Stock Moves: the quantity comes from confirmed pickings\n"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__acc_number
msgid "Account Number"
msgstr "Broj računa"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Account used for deposits"
msgstr "Konto za depozite"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_accrued_revenue_entry
msgid "Accrued Revenue Entry"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction
msgid "Action Needed"
msgstr "Potrebna akcija"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_ids
msgid "Activities"
msgstr "Aktivnosti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_state
msgid "Activity State"
msgstr "Status aktivnosti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.mail_activity_type_action_config_sale
#: model:ir.ui.menu,name:sale.sale_menu_config_activity_type
msgid "Activity Types"
msgstr "Tipovi aktivnosti"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_catalog/sale_order_line/sale_order_line.xml:0
msgid "Add"
msgstr "Dodaj"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a note"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a product"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Add a section"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Add several variants to an order from a grid"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Allows you to send Pro-Forma Invoice to your customers"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__group_proforma_sales
msgid "Allows you to send pro-forma invoice."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_invoiced
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_invoiced
msgid "Already invoiced"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_amazon
msgid "Amazon Sync"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_undiscounted
msgid "Amount Before Discount"
msgstr "Iznos prije popusta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_amount
msgid "Amount of quotations to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_to_invoice
msgid "Amount to invoice"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"An order is to upsell when delivered quantities are above initially\n"
"            ordered quantities, and the invoicing policy is based on ordered quantities."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__analytic_account_id
#: model:ir.model.fields,field_description:sale.field_sale_report__analytic_account_id
msgid "Analytic Account"
msgstr "Analitički konto"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_distribution
msgid "Analytic Distribution"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__analytic
msgid "Analytic From Expenses"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_line
msgid "Analytic Line"
msgstr "Analitička stavka"

#. module: sale
#: model:ir.model,name:sale.model_account_analytic_applicability
msgid "Analytic Plan's Applicabilities"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__analytic_line_ids
msgid "Analytic lines"
msgstr "Retci analitike"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Apply manual discounts on sales order lines or display discounts computed from pricelists (option to activate in the pricelist configuration)."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Are you sure you want to cancel the selected quotation?"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Are you sure you want to void the authorized transaction? This action can't be undone."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid ""
"As an example, if you sell pre-paid hours of services, Odoo recommends you\n"
"            to sell extra hours when all ordered hours have been consumed."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__cost
msgid "At cost"
msgstr "Na nabavnu cijenu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_attachment_count
msgid "Attachment Count"
msgstr "Broj zakački"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_value
msgid "Attribute Value"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_attribute_action
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
msgid "Attributes"
msgstr "Atributi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__author_id
msgid "Author"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__authorized_transaction_ids
msgid "Authorized Transactions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__automatic_invoice
msgid "Automatic Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__journal_name
msgid "Bank Name"
msgstr "Naziv banke"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__partner
msgid "Based on Customer ID"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__payment_provider__so_reference_type__so_name
msgid "Based on Document Reference"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__block
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__block
msgid "Blocking Message"
msgstr "Blokiranje poruke"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body_has_template_value
msgid "Body content is the same as tmeplate"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Boost your sales with multiple kinds of programs: Coupons, Promotions, Gift Card, Loyalty. Specific conditions can be set (products, customers, minimum purchase amount, period). Rewards can be discounts (% or amount) or free products."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,field_description:sale.field_account_move__campaign_id
#: model:ir.model.fields,field_description:sale.field_account_payment__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_order__campaign_id
#: model:ir.model.fields,field_description:sale.field_sale_report__campaign_id
msgid "Campaign"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__can_edit_body
msgid "Can Edit Body"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_updatable
msgid "Can Edit Product"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Cancel"
msgstr "Otkaži"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Cancel %s"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_mass_cancel_orders
msgid "Cancel multiple quotations"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_mass_cancel_orders
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid "Cancel quotations"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__cancel
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__cancel
msgid "Cancelled"
msgstr "Otkazan"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Capture Transaction"
msgstr "Uhvati transakciju"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Catalog"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_category_id
msgid "Category"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Changing the company of an existing quotation might need some manual adjustments in the details of the lines. You might consider updating the prices."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Choose between electronic signatures or online payments."
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_open_sale_payment_provider_onboarding_wizard
msgid "Choose how to confirm quotations"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Click here to add some products or services to your quotation."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/xml/sales_team_progress_bar_template.xml:0
msgid "Click to define an invoicing target"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Close"
msgstr "Zatvori"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__closed
msgid "Closed"
msgstr "Zatvoreno"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_payment_provider__so_reference_type
msgid "Communication"
msgstr "Komunikacija"

#. module: sale
#: model:ir.model,name:sale.model_res_company
msgid "Companies"
msgstr "Kompanije"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order__company_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__company_id
#: model:ir.model.fields,field_description:sale.field_sale_report__company_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__company_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Company"
msgstr "Kompanija"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Complete your company's data"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with DHL"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Easypost"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with FedEx"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with Sendcloud"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with UPS"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with USPS"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs and ship with bpost"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Compute shipping costs on orders"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_config
msgid "Configuration"
msgstr "Konfiguracija"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Confirm"
msgstr "Portvrdi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Connectors"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_res_partner
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Contact"
msgstr "Kontakt"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__body
msgid "Contents"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__product_uom_category_id
msgid "Conversion between Units of Measure can only occur if they belong to the same category. The conversion will be made based on the ratios."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__country_code
msgid "Country code"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_loyalty
msgid "Coupons & Loyalty"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "Create Date"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Create Draft Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__advance_payment_method
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Create Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Create Invoices"
msgstr "Napravi fakture"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create a customer invoice"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid "Create a new product"
msgstr "Kreirajte novi proizvod"

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Create a new quotation, the first step of a new sale!"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_view_sale_advance_payment_inv
msgid "Create invoices"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_invoice_salesteams
msgid "Create invoices, register payments and keep track of the discussions with your customers."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__create_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__create_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__create_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__create_date
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Creation Date"
msgstr "Datum kreiranja"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__date_order
msgid ""
"Creation date of draft/sent orders,\n"
"Confirmation date of confirmed orders."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__stripe
msgid "Credit & Debit card (via Stripe)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__currency_id
#: model:ir.model.fields,field_description:sale.field_sale_report__currency_id
#: model:ir.model.fields,field_description:sale.field_utm_campaign__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__currency_rate
msgid "Currency Rate"
msgstr "Kursna lista"

#. module: sale
#: model:product.attribute.value,name:sale.product_attribute_value_7
msgid "Custom"
msgstr "Prilagođeno"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_custom_attribute_value_ids
msgid "Custom Values"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__manual
msgid "Custom payment instructions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_partner_id
#: model:ir.model.fields,field_description:sale.field_sale_report__partner_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Customer"
msgstr "Kupac"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__country_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Country"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__commercial_partner_id
msgid "Customer Entity"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__industry_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Customer Industry"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__access_url
msgid "Customer Portal URL"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__client_order_ref
msgid "Customer Reference"
msgstr "Referenca kupca"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Customer Signature"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Customer Taxes"
msgstr "Porezi kupca"

#. module: sale
#: model:ir.ui.menu,name:sale.res_partner_menu
msgid "Customers"
msgstr "Kupci"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Customize the look of your quotations."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Customize your quotes and orders."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_dhl
msgid "DHL Express Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Date"
msgstr "Datum"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Date:"
msgstr "Datum:"

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,help:sale.field_res_config_settings__quotation_validity_days
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Days between quotation proposal and expiration. 0 days means automatic expiration is disabled"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deduct_down_payments
msgid "Deduct down payments"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__quotation_validity_days
#: model:ir.model.fields,field_description:sale.field_res_config_settings__quotation_validity_days
msgid "Default Quotation Validity"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_company__sale_down_payment_product_id
#: model:ir.model.fields,help:sale.field_res_config_settings__deposit_default_product_id
msgid "Default product used for down payments"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Default values"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Default values used when creating new quotations. Those can still be changed on each quotation."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Deliver Content by Email"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivered"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Delivered Quantity: %s"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__delivery
msgid "Delivered quantities"
msgstr "Isporučene količine"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_shipping_id
msgid "Delivery Address"
msgstr "Adresa dostave"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__commitment_date
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Delivery Date"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery
msgid "Delivery Methods"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered
msgid "Delivery Quantity"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__expected_date
msgid "Delivery date you can promise to the customer, computed from the minimum lead time of the order lines in case of Service products. In case of shipping, the shipping policy of the order will be taken into account to either use the minimum or maximum lead time of the order lines."
msgstr ""

#. module: sale
#: model:product.template,name:sale.advance_product_0_product_template
msgid "Deposit"
msgstr "Depozit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_down_payment_product_id
#: model:ir.model.fields,field_description:sale.field_res_config_settings__deposit_default_product_id
msgid "Deposit Product"
msgstr "Proizvod za depozit"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__name
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Description"
msgstr "Opis"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_discount_field.js:0
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Disc.%"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Discard"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount
msgid "Discount %"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__discount
msgid "Discount (%)"
msgstr "Popust (%)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__discount_amount
msgid "Discount Amount"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Discounts, Loyalty & Gift Card"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_draft_invoice_warning
msgid "Display Draft Invoice Warning"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_invoice_amount_warning
msgid "Display Invoice Amount Warning"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__display_name
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_name
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__display_name
#: model:ir.model.fields,field_description:sale.field_sale_report__display_name
msgid "Display Name"
msgstr "Prikazani naziv"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__display_type
msgid "Display Type"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_discount_field.js:0
msgid "Do you want to apply this value to all lines?"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Documentation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_applicability__business_domain
msgid "Domain"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__done
msgid "Done"
msgstr "Gotovo"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down Payment"
msgstr "Predujam"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__amount
msgid "Down Payment Amount"
msgstr "Iznos predujma"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__fixed_amount
msgid "Down Payment Amount (Fixed)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__product_id
msgid "Down Payment Product"
msgstr "Proizvod predujma"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down Payment: %s (Draft)"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Down Payments"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__fixed
msgid "Down payment (fixed amount)"
msgstr "Predujam (fiksni iznos)"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__percentage
msgid "Down payment (percentage)"
msgstr "Predujam (procenat)"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "Down payment of %s%%"
msgstr "Predujam za %s%%"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_downpayment
msgid "Down payments are made when creating invoices from a sales order. They are not copied when duplicating a sales order."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Download"
msgstr "Preuzimanje"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Draft Invoices"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__draft
msgid "Draft Quotation"
msgstr "Predračun u pripremi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_easypost
msgid "Easypost Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_kanban_view
msgid "Edit"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/sale_product_field.js:0
msgid "Edit Configuration"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__digital_signature
msgid "Electronic signature"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__paypal_email_account
msgid "Email"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_mail_compose_message
msgid "Email composition wizard"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__invoice_mail_template_id
msgid "Email sent to the customer once the invoice is available."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__expected_date
msgid "Expected Date"
msgstr "Očekivani datum"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Expected:"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__expense_policy
#: model:ir.model.fields,help:sale.field_product_template__expense_policy
msgid "Expenses and vendor bills can be re-invoiced to a customer.With this option, a validated expense can be re-invoice to a customer at its cost or sales price."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__validity_date
msgid "Expiration"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Expires on %(date)s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Extended Filters"
msgstr "Prošireni filteri"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_no_variant_attribute_value_ids
msgid "Extra Values"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Extra line with %s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
msgid "Favorites"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_fedex
msgid "FedEx Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__fiscal_position_id
msgid "Fiscal Position"
msgstr "Fiskalna pozicija"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__fiscal_position_id
msgid "Fiscal positions are used to adapt taxes and accounts for particular customers or sales orders/invoices.The default value comes from the customer."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_follower_ids
msgid "Followers"
msgstr "Pratioci"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_partner_ids
msgid "Followers (Partners)"
msgstr "Pratioci (Partneri)"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_non_accountable_null_fields
msgid "Forbidden values on non-accountable sale order line"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_account_invoice_report_salesteam
msgid "From this report, you can have an overview of the amount invoiced to your customer. The search tool can also be used to personalise your Invoices reports and so, match this analysis to your needs."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__invoiced
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__invoiced
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Fully Invoiced"
msgstr "U potpunosti fakturisano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Future Activities"
msgstr "Buduće aktivnosti"

#. module: sale
#: model:ir.model,name:sale.model_payment_link_wizard
msgid "Generate Sales Payment Link"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_order_generate_link
msgid "Generate a Payment Link"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Generate the invoice automatically when the online payment is confirmed"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Get warnings in orders for products or customers"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Good job, let's continue."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Grant discounts on sales order lines"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__weight
msgid "Gross Weight"
msgstr "Bruto težina"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Group By"
msgstr "Grupiši po"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_active_pricelist
msgid "Has Active Pricelist"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__has_confirmed_order
msgid "Has Confirmed Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_fpos
msgid "Has Fiscal Position Changed"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__has_message
msgid "Has Message"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__show_update_pricelist
msgid "Has Pricelist Changed"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__has_down_payments
msgid "Has down payments"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "History"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__id
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__id
#: model:ir.model.fields,field_description:sale.field_sale_order__id
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__id
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__id
#: model:ir.model.fields,field_description:sale.field_sale_report__id
msgid "ID"
msgstr "ID"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ako je zakačeno, nove poruke će zahtjevati vašu pažnju"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error
#: model:ir.model.fields,help:sale.field_sale_order__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "If the sale is locked, you can not modify it anymore. However, you will still be able to invoice or deliver."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_packaging__sales
msgid "If true, the packaging can be used for sales orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__pricelist_id
msgid "If you change the pricelist, only newly added lines will be affected."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Import Amazon orders and sync deliveries"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid "Import Template for Products"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
msgid "In the Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Incl. tax)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__deposit_account_id
msgid "Income Account"
msgstr "Konto prihoda"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid order."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Invalid signature data."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move.py:0
msgid "Invoice %s paid"
msgstr "Faktura %s plaćena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_invoice_id
msgid "Invoice Address"
msgstr "Adresa fakture (kupca)"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__display_invoice_alert
msgid "Invoice Alert"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_confirmed
msgid "Invoice Confirmed"
msgstr "Faktura potvrđena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_count
msgid "Invoice Count"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_salesteam_invoice_created
msgid "Invoice Created"
msgstr "Faktura kreirana"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__invoice_mail_template_id
msgid "Invoice Email Template"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_lines
msgid "Invoice Lines"
msgstr "Stavke fakture"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "Invoice Sales Order"
msgstr "Fakturiši prodajnu narudžbu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_order_line__invoice_status
#: model:ir.model.fields,field_description:sale.field_sale_report__invoice_status
msgid "Invoice Status"
msgstr "Status fakture"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "Invoice after delivery, based on quantities delivered, not ordered."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced
msgid "Invoice revenue for the current month. This is the amount the sales channel has invoiced this month. It is used to compute the progression ratio of the current and target revenue on the kanban view."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__delivery
msgid "Invoice what is delivered"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_config_settings__default_invoice_policy__order
msgid "Invoice what is ordered"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoiced"
msgstr "Fakturisano"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_invoiced
msgid "Invoiced Quantity"
msgstr "Fakturisana količina"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Invoiced Quantity: %s"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced
msgid "Invoiced This Month"
msgstr "Fakturisano ovaj mjesec"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_sale_order__invoice_ids
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoices"
msgstr "Fakture"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_account_invoice_report_salesteam
msgid "Invoices Analysis"
msgstr "Analiza faktura"

#. module: sale
#: model:ir.model,name:sale.model_account_invoice_report
msgid "Invoices Statistics"
msgstr "Statistika faktura"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Invoicing"
msgstr "Fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing Address:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__invoice_policy
#: model:ir.model.fields,field_description:sale.field_product_template__invoice_policy
#: model:ir.model.fields,field_description:sale.field_res_config_settings__default_invoice_policy
msgid "Invoicing Policy"
msgstr "Pravilo fakturisanja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__invoiced_target
msgid "Invoicing Target"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Invoicing and Payments"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Invoicing and Shipping Address:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__is_downpayment
msgid "Is Downpayment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__is_expired
msgid "Is Expired"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_is_follower
msgid "Is Follower"
msgstr "Je pratilac"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_downpayment
msgid "Is a down payment"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__is_expense
msgid "Is expense"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__is_expense
msgid "Is true if the sales order line comes from an expense or a vendor bills"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"It is forbidden to modify the following fields in a locked order:\n"
"%s"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "It is not allowed to confirm an order in the following states: %s"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_move
msgid "Journal Entry"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_move_line
msgid "Journal Item"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__just_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__just_done
msgid "Just done"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__lang
msgid "Language"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_uid
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_uid
msgid "Last Updated by"
msgstr "Zadnji ažurirao"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__write_date
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__write_date
#: model:ir.model.fields,field_description:sale.field_sale_order_line__write_date
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__write_date
msgid "Last Updated on"
msgstr "Zadnje ažurirano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Late Activities"
msgstr "Aktivnosti u kašnjenju"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__customer_lead
msgid "Lead Time"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Let your customers log in to see their documents"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Let's send the quote."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Lets keep electronic signature for now."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Lock"
msgstr "Zaključaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_auto_done_setting
#: model:res.groups,name:sale.group_auto_done_setting
msgid "Lock Confirmed Sales"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__done
msgid "Locked"
msgstr "Zaključano"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Looks good. Let's continue."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Looks great!"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__template_id
msgid "Mail Template"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Manage Promotions, coupons, loyalty cards, Gift cards & eWallet"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__qty_delivered_method__manual
msgid "Manual"
msgstr "Ručno"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__manual
msgid "Manual Payment"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__service_type__manual
msgid "Manually set quantities on order"
msgstr "Ručno postavi količine na narudžbu"

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__service_type
#: model:ir.model.fields,help:sale.field_product_template__service_type
msgid ""
"Manually set quantities on order: Invoice based on the manually entered quantity, without creating an analytic account.\n"
"Timesheets on contract: Invoice based on the tracked hours on the related timesheet.\n"
"Create a task and track hours: Create a task on the sales order validation and track the work hours."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_sale_margin
msgid "Margins"
msgstr "Marže"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_quotation_sent
msgid "Mark Quotation as Sent"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Marketing"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,field_description:sale.field_account_move__medium_id
#: model:ir.model.fields,field_description:sale.field_account_payment__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_order__medium_id
#: model:ir.model.fields,field_description:sale.field_sale_report__medium_id
msgid "Medium"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Message"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn_msg
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn_msg
msgid "Message for Sales Order"
msgstr "Poruka za prodajnu narudžbu"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn_msg
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn_msg
msgid "Message for Sales Order Line"
msgstr "Upozorenje za stavku prodajne narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_ids
msgid "Messages"
msgstr "Poruke"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_name
msgid "Method"
msgstr "Metoda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_delivered_method
msgid "Method to update delivered qty"
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_sale_order_line_accountable_required_fields
msgid "Missing required fields on accountable sale order line."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "My Orders"
msgstr "Moje narudžbe"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
msgid "My Quotations"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "My Sales Order Lines"
msgstr "Moje stavke prodajne narudžbe"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "New"
msgstr "Novi"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotation_form
msgid "New Quotation"
msgstr "Novi predračun"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Krajnji rok za sljedeću aktivnost"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_summary
msgid "Next Activity Summary"
msgstr "Pregled sljedeće aktivnosti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_type_id
msgid "Next Activity Type"
msgstr "Tip sljedeće aktivnosti"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__no
msgid "No"
msgstr "Ne"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__no-message
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__no-message
msgid "No Message"
msgstr "Bez Poruka"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "No longer edit orders once confirmed"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid "No orders to invoice found"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_upselling
msgid "No orders to upsell found."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_order_confirmation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_sample_quotation_state__not_done
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_quotation_onboarding_state__not_done
msgid "Not done"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_note
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Note"
msgstr "Zabilješka"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__no
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__no
msgid "Nothing to Invoice"
msgstr "Nema ništa za fakturisanje"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Now, we'll create a sample quote."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Number"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_needaction_counter
msgid "Number of Actions"
msgstr "Broj akcija"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_line__customer_lead
msgid "Number of days between the order confirmation and the shipping of the products to the customer"
msgstr "Broj dana između potvrde narudžbe i isporuke proizvoda kupcu."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Broj poruka koje zahtjevaju neku akciju"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__quotations_count
msgid "Number of quotations to invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_crm_team__sales_to_invoice_count
msgid "Number of sales to invoice"
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.act_res_partner_2_sale_order
#: model_terms:ir.actions.act_window,help:sale.action_orders_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations
#: model_terms:ir.actions.act_window,help:sale.action_quotations_salesteams
#: model_terms:ir.actions.act_window,help:sale.action_quotations_with_onboarding
msgid "Once the quotation is confirmed by the customer, it becomes a sales order.<br> You will be able to create an invoice and collect the payment."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders
msgid "Once the quotation is confirmed, it becomes a sales order.<br> You will be able to create an invoice and collect the payment."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_pay
#: model:ir.model.fields,field_description:sale.field_sale_order__require_payment
msgid "Online Payment"
msgstr "Online plaćanje"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_res_config_settings__portal_confirmation_sign
#: model:ir.model.fields,field_description:sale.field_sale_order__require_signature
msgid "Online Signature"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Online confirmation"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_invoiced
msgid "Only confirmed down payments are considered."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Only draft orders can be marked as sent directly."
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_product_attribute_custom_value_sol_custom_value_unique
msgid "Only one Custom Value is allowed per Attribute Value per Sales Order Line."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Open Sales app to send your first quotation in a few clicks."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
msgid "Operation not supported"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order_cancel__lang
msgid "Optional translation language (ISO code) to select when sending out an email. If not set, the english version will be used. This should usually be a placeholder expression that provides the appropriate language, e.g. {{ object.partner_id.lang }}."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Order"
msgstr "Nalog"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__order_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order #"
msgstr "Narudžba #"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Order Confirmation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__count
msgid "Order Count"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
#: model:ir.model.fields,field_description:sale.field_sale_order__date_order
#: model:ir.model.fields,field_description:sale.field_sale_report__date
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Order Date"
msgstr "Datum narudžbe"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Order Date:"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Order Date: Last 365 Days"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__order_line
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Order Lines"
msgstr "Stavke naloga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__name
#: model:ir.model.fields,field_description:sale.field_sale_order_line__order_id
#: model:ir.model.fields,field_description:sale.field_sale_report__name
msgid "Order Reference"
msgstr "Referenca naloga"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__state
msgid "Order Status"
msgstr "Status narudžbe"

#. module: sale
#: model:mail.activity.type,name:sale.mail_act_sale_upsell
msgid "Order Upsell"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Order signed by %s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Order to Invoice"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "Ordered Quantity: %(old_qty)s -> %(new_qty)s"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__invoice_policy
#: model:ir.model.fields,help:sale.field_product_template__invoice_policy
msgid ""
"Ordered Quantity: Invoice quantities ordered by the customer.\n"
"Delivered Quantity: Invoice quantities delivered to the customer."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__invoice_policy__order
msgid "Ordered quantities"
msgstr "Naručene količine"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_order
#: model:ir.ui.menu,name:sale.sale_order_menu
msgid "Orders"
msgstr "Narudžbe"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_order_invoice
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Orders to Invoice"
msgstr "Narudžbe za fakturisati"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders_upselling
#: model:ir.ui.menu,name:sale.menu_sale_order_upselling
msgid "Orders to Upsell"
msgstr "Narudžbe za naknadno fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Other Info"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__paypal_pdt_token
msgid "PDT Identity Token"
msgstr ""

#. module: sale
#: model:ir.actions.report,name:sale.action_report_pro_forma_invoice
msgid "PRO-FORMA Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_id
msgid "Packaging"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_packaging_qty
msgid "Packaging Quantity"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__partner_credit_warning
msgid "Partner Credit Warning"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay Now"
msgstr "Plati Sad"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Pay with"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__other
msgid "Pay with another payment provider"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__paypal
#: model:ir.model.fields.selection,name:sale.selection__sale_payment_provider_onboarding_wizard__payment_method__paypal
msgid "PayPal"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__manual_post_msg
msgid "Payment Instructions"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_payment_provider_onboarding_wizard__payment_method
msgid "Payment Method"
msgstr "Metoda plaćanja"

#. module: sale
#: model:ir.model,name:sale.model_payment_provider
msgid "Payment Provider"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Payment Providers"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__reference
msgid "Payment Ref."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__payment_term_id
msgid "Payment Terms"
msgstr "Uslovi plaćanja"

#. module: sale
#: model:ir.model,name:sale.model_payment_transaction
msgid "Payment Transaction"
msgstr "Transakcija plaćanja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Payment terms"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_url
msgid "Portal Access URL"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Preview"
msgstr "Pregled"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxexcl
msgid "Price Reduce Tax excl"
msgstr "Umanjena cijena bez PDV-a"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_reduce_taxinc
msgid "Price Reduce Tax incl"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__pricelist_id
#: model:ir.model.fields,field_description:sale.field_sale_report__pricelist_id
msgid "Pricelist"
msgstr "Cijenovnik"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__pricelist_item_id
msgid "Pricelist Item"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_pricelist_main
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Pricelists"
msgstr "Cjenovnik"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Pricing"
msgstr "Cijene"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Print"
msgstr "Ispis"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_proforma_sales
msgid "Pro-Forma Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Pro-Forma Invoice #"
msgstr ""

#. module: sale
#: model:res.groups,name:sale.group_proforma_sales
msgid "Pro-forma Invoices"
msgstr "Pro-forma fakture"

#. module: sale
#: model:ir.model,name:sale.model_product_template
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_id
#: model:ir.model.fields,field_description:sale.field_sale_report__product_tmpl_id
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_kanban_view
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Product"
msgstr "Proizvod"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute
msgid "Product Attribute"
msgstr "Atribut proizvoda"

#. module: sale
#: model:ir.model,name:sale.model_product_attribute_custom_value
msgid "Product Attribute Custom Value"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product Catalog"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__product_catalog_product_is_in_sale_order
msgid "Product Catalog Product Is In Sale Order"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__categ_id
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Product Category"
msgstr "Kategorija proizvoda"

#. module: sale
#: model:ir.model,name:sale.model_product_packaging
msgid "Product Packaging"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_template_id
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
msgid "Product Template"
msgstr "Predlog proizvoda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_type
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
msgid "Product Type"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_readonly
msgid "Product Uom Readonly"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_product_product
#: model:ir.model.fields,field_description:sale.field_sale_report__product_id
msgid "Product Variant"
msgstr "Varijante proizvoda"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_products
msgid "Product Variants"
msgstr "Varijante proizvoda"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed according to pricelist %s."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product prices have been recomputed."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Product taxes have been recomputed according to fiscal position %s."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Product used for down payments"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
#: model:ir.actions.act_window,name:sale.product_template_action
#: model:ir.ui.menu,name:sale.menu_product_template_action
#: model:ir.ui.menu,name:sale.prod_config_main
#: model:ir.ui.menu,name:sale.product_menu_catalog
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
msgid "Products"
msgstr "Proizvodi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Qty"
msgstr "Količina"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_delivered
msgid "Qty Delivered"
msgstr "Isporučena kol."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_invoiced
msgid "Qty Invoiced"
msgstr "Fakturisana kol."

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom_qty
msgid "Qty Ordered"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_deliver
msgid "Qty To Deliver"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__qty_to_invoice
msgid "Qty To Invoice"
msgstr "Kol. za fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quantities to invoice from sales orders"
msgstr "Količine za fakturisanje sa prodajne narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom_qty
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Quantity"
msgstr "Količina"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__qty_to_invoice
msgid "Quantity To Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_qty_configurator
#: model:ir.model.fields,field_description:sale.field_product_template__visible_qty_configurator
msgid "Quantity visible in configurator"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quantity:"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__draft
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
msgid "Quotation"
msgstr "Predračun"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation #"
msgstr "Predračun #"

#. module: sale
#: model:ir.actions.report,name:sale.action_report_saleorder
msgid "Quotation / Order"
msgstr "Ponuda / Nalog"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__quotation_count
msgid "Quotation Count"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Quotation Date"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Quotation Date:"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.onboarding_quotation_layout_step
msgid "Quotation Layout"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sent
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sent
msgid "Quotation Sent"
msgstr "Predračun poslan"

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/res_config_settings.py:0
msgid "Quotation Validity is required and must be greater or equal to 0."
msgstr ""

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_confirmed
msgid "Quotation confirmed"
msgstr "Predračun potvrđen"

#. module: sale
#: model:mail.message.subtype,description:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_order_sent
#: model:mail.message.subtype,name:sale.mt_salesteam_order_sent
msgid "Quotation sent"
msgstr "Predračun poslan"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Quotation viewed by customer %s"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_quotations
#: model:ir.actions.act_window,name:sale.action_quotations_salesteams
#: model:ir.actions.act_window,name:sale.action_quotations_with_onboarding
#: model:ir.ui.menu,name:sale.menu_sale_quotations
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_quotation_tree
msgid "Quotations"
msgstr "Predračuni"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Quotations & Orders"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_order_report_quotation_salesteam
msgid "Quotations Analysis"
msgstr "Analiza predračuna"

#. module: sale
#: model:ir.actions.act_window,name:sale.act_res_partner_2_sale_order
msgid "Quotations and Sales"
msgstr "Predračuni i Prodaja"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__rating_ids
msgid "Ratings"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__expense_policy
msgid "Re-Invoice Expenses"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__visible_expense_policy
#: model:ir.model.fields,field_description:sale.field_product_template__visible_expense_policy
msgid "Re-Invoice Policy visible"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__recipient_ids
msgid "Recipients"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all prices based on this pricelist"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Recompute all taxes based on this fiscal position"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Reference"
msgstr "Referenca"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__origin
msgid "Reference of the document that generated this sales order request"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_account_payment_register
msgid "Register Payment"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_advance_payment_inv__advance_payment_method__delivered
msgid "Regular invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Reject This Quotation"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_catalog/sale_order_line/sale_order_line.xml:0
msgid "Remove"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__render_model
msgid "Rendering Model"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_sale_report
msgid "Reporting"
msgstr "Izvještavanje"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__require_signature
msgid "Request a online signature and/or payment to the customer in order to confirm orders automatically."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online payment to confirm orders"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Request an online signature to confirm orders"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Requested date is too soon."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__activity_user_id
msgid "Responsible User"
msgstr "Odgovorni korisnik"

#. module: sale
#: model:ir.model.fields,help:sale.field_crm_team__invoiced_target
msgid "Revenue target for the current month (untaxed total of confirmed invoices)."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_form
#: model_terms:ir.ui.view,arch_db:sale.utm_campaign_view_kanban
msgid "Revenues"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_utm_campaign__invoiced_amount
msgid "Revenues generated by the campaign"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_advance_payment_inv__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__order_id
#: model:ir.model.fields.selection,name:sale.selection__account_analytic_applicability__business_domain__sale_order
msgid "Sale Order"
msgstr "Prodajni nalog"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__sale_order_count
#: model:ir.model.fields,field_description:sale.field_account_move__sale_order_count
#: model:ir.model.fields,field_description:sale.field_account_payment__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_count
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_count
msgid "Sale Order Count"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__group_warning_sale
msgid "Sale Order Warnings"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_form
msgid "Sale Orders"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_orders_count
msgid "Sale Orders Count"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_payment_provider_onboarding_wizard
msgid "Sale Payment provider onboarding wizard"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sale Warnings"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_payment_method
msgid "Sale onboarding selected payment method"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_mass_cancel_orders__sale_order_ids
msgid "Sale orders to cancel"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_packaging__sales
#: model:ir.ui.menu,name:sale.sale_menu_root
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales"
msgstr "Prodaja"

#. module: sale
#: model:ir.model,name:sale.model_sale_advance_payment_inv
msgid "Sales Advance Payment Invoice"
msgstr "Avansna faktura prodaje"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
#: model:ir.actions.act_window,name:sale.action_order_report_all
#: model:ir.actions.act_window,name:sale.action_order_report_so_salesteam
#: model:ir.actions.act_window,name:sale.report_all_channels_sales_action
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_graph
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_pivot
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Sales Analysis"
msgstr "Analiza prodaje"

#. module: sale
#: model:ir.model,name:sale.model_sale_report
msgid "Sales Analysis Report"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__done
msgid "Sales Done"
msgstr "Prodaja završena"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: model:ir.model,name:sale.model_sale_order
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_order_ids
#: model:ir.model.fields,field_description:sale.field_res_users__sale_order_ids
#: model:ir.model.fields.selection,name:sale.selection__sale_order__state__sale
#: model:ir.model.fields.selection,name:sale.selection__sale_report__state__sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Sales Order"
msgstr "Prodajna narudžba"

#. module: sale
#: model:ir.model,name:sale.model_sale_order_cancel
msgid "Sales Order Cancel"
msgstr ""

#. module: sale
#: model:mail.message.subtype,name:sale.mt_order_confirmed
#: model:mail.message.subtype,name:sale.mt_salesteam_order_confirmed
msgid "Sales Order Confirmed"
msgstr "Prodajna narudžba potvrđena"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_analytic_line__so_line
#: model_terms:ir.ui.view,arch_db:sale.sale_order_line_view_form_readonly
msgid "Sales Order Item"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_sale_order_line
#: model:ir.model.fields,field_description:sale.field_product_attribute_custom_value__sale_order_line_id
#: model:ir.model.fields,field_description:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,field_description:sale.field_product_template__sale_line_warn
msgid "Sales Order Line"
msgstr "Stavka prodajne narudžbe"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_move_line__sale_line_ids
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Sales Order Lines"
msgstr "Stavke Prodajnog naloga"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines ready to be invoiced"
msgstr "Stavke prodajne narudžbe spremne za fakturisanje"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Sales Order Lines related to a Sales Order of mine"
msgstr "Stavke prodajne narudžbe vezane za moju prodajnu narudžbu"

#. module: sale
#. odoo-python
#: code:addons/sale/models/payment_transaction.py:0
#: model_terms:ir.ui.view,arch_db:sale.transaction_form_inherit_sale
msgid "Sales Order(s)"
msgstr ""

#. module: sale
#: model:ir.actions.act_window,name:sale.action_orders
#: model:ir.actions.act_window,name:sale.action_orders_salesteams
#: model:ir.actions.act_window,name:sale.action_orders_to_invoice_salesteams
#: model:ir.model.fields,field_description:sale.field_payment_transaction__sale_order_ids
#: model:ir.ui.menu,name:sale.menu_sales_config
#: model_terms:ir.ui.view,arch_db:sale.crm_team_view_kanban_dashboard
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_menu_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_home_sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_activity
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_quotation
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_order_tree
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_calendar
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_graph
#: model_terms:ir.ui.view,arch_db:sale.view_sale_order_pivot
msgid "Sales Orders"
msgstr "Prodajni nalozi"

#. module: sale
#: model:ir.model,name:sale.model_crm_team
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__team_id
#: model:ir.model.fields,field_description:sale.field_account_invoice_report__team_id
#: model:ir.model.fields,field_description:sale.field_account_move__team_id
#: model:ir.model.fields,field_description:sale.field_account_payment__team_id
#: model:ir.model.fields,field_description:sale.field_sale_order__team_id
#: model:ir.model.fields,field_description:sale.field_sale_report__team_id
#: model_terms:ir.ui.view,arch_db:sale.account_invoice_groupby_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_account_invoice_report_search_inherit
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Sales Team"
msgstr "Prodajni tim"

#. module: sale
#: model:ir.ui.menu,name:sale.report_sales_team
#: model:ir.ui.menu,name:sale.sales_team_config
msgid "Sales Teams"
msgstr "Prodajni timovi"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_partner__sale_warn
#: model:ir.model.fields,field_description:sale.field_res_users__sale_warn
msgid "Sales Warnings"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_account_analytic_line__so_line
msgid "Sales order item to which the time spent will be added in order to be invoiced to your customer. Remove the sales order item for the timesheet entry to be non-billable."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__product_template__expense_policy__sales_price
msgid "Sales price"
msgstr ""

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_cancellation
msgid "Sales: Order Cancellation"
msgstr ""

#. module: sale
#: model:mail.template,name:sale.mail_template_sale_confirmation
msgid "Sales: Order Confirmation"
msgstr ""

#. module: sale
#: model:mail.template,name:sale.email_template_edi_sale
msgid "Sales: Send Quotation"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid "Sales: Untaxed Total"
msgstr "Prodaja: ukupno bez PDVa"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__user_id
#: model:ir.model.fields,field_description:sale.field_sale_order_line__salesman_id
#: model:ir.model.fields,field_description:sale.field_sale_report__user_id
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Salesperson"
msgstr "Prodavač(ica)"

#. module: sale
#. odoo-python
#: code:addons/sale/models/res_company.py:0
msgid "Sample Order Line"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/res_company.py:0
msgid "Sample Product"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Sample Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "Search Sales Order"
msgstr "Pretraži prodajne narudžbe"

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__display_type__line_section
msgid "Section"
msgstr "Odjel"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Section Name (eg. Products, Services)"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__access_token
msgid "Security Token"
msgstr "Sigurnosni token"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Select a product, or create a new one on the fly."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_product_product__sale_line_warn
#: model:ir.model.fields,help:sale.field_product_template__sale_line_warn
#: model:ir.model.fields,help:sale.field_res_partner__sale_warn
#: model:ir.model.fields,help:sale.field_res_users__sale_warn
msgid "Selecting the \"Warning\" option will notify user with the message, Selecting \"Blocking Message\" will throw an exception with the message and block the flow. The Message has to be written in the next field."
msgstr "Odabirom opcije \"Upozorenje\" obavijestit ćete korisnika porukom, odabirom opcije \"Blokiranje poruke\" učinit ćete izuzetak s porukom i blokirati tok. Poruku treba napisati u slijedećem polju."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell and purchase products in different units of measure"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell products by multiple of unit # per package"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sell variants of a product using attributes (size, color, etc.)"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send PRO-FORMA Invoice"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Send a product-specific email once the invoice is validated"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send a quotation to test the customer portal."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Send and cancel"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Send by Email"
msgstr "Pošalji email-om"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_sample_quotation_step
msgid "Send sample"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_sendcloud
msgid "Sendcloud Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Sending an email is useful if you need to share specific information or content about a product (instructions, rules, links, media, etc.). Create and set the email template from the product detail form (in Accounting tab)."
msgstr ""

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_cancellation
msgid "Sent automatically to customers when you cancel an order"
msgstr ""

#. module: sale
#: model:mail.template,description:sale.mail_template_sale_confirmation
msgid "Sent to customers on order confirmation"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__sequence
msgid "Sequence"
msgstr "Sekvenca"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_product_catalog_search_view
msgid "Services"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Set multiple prices per product, automated discounts, etc."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_onboarding_order_confirmation_step
msgid "Set payments"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Set to Quotation"
msgstr "Postavi na predračun"

#. module: sale
#: model:ir.actions.act_window,name:sale.action_sale_config_settings
#: model:ir.ui.menu,name:sale.menu_sale_general_settings
msgid "Settings"
msgstr "Postavke"

#. module: sale
#: model:ir.actions.server,name:sale.model_sale_order_action_share
msgid "Share"
msgstr "Podijeli"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Shipping"
msgstr "Prevoznik"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Show all records which has next action date is before today"
msgstr "Prikaži sve zapise koji imaju datum sljedeće akcije prije danas"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Show margins on orders"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Sign & Pay Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Sign &amp; Pay"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__digital_signature
msgid "Sign online"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signature
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Signature"
msgstr "Potpis"

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Signature is missing."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_by
msgid "Signed By"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__signed_on
msgid "Signed On"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__sales_count
#: model:ir.model.fields,field_description:sale.field_product_template__sales_count
msgid "Sold"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Sold in the last 365 days"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"Some confirmed sale orders are selected. Their related documents might be\n"
"                        affected by the cancellation."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,field_description:sale.field_account_move__source_id
#: model:ir.model.fields,field_description:sale.field_account_payment__source_id
#: model:ir.model.fields,field_description:sale.field_sale_order__source_id
#: model:ir.model.fields,field_description:sale.field_sale_report__source_id
msgid "Source"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__origin
msgid "Source Document"
msgstr "Izvorni dokument"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_product_email_template
msgid "Specific Email"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "Stage"
msgstr "Faza"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Start by checking your company's data."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_order_confirmation_state
msgid "State of the onboarding confirmation order step"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_onboarding_sample_quotation_state
msgid "State of the onboarding sample quotation step"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_company__sale_quotation_onboarding_state
msgid "State of the sale onboarding panel"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__state
#: model:ir.model.fields,field_description:sale.field_sale_report__state
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
msgid "Status"
msgstr "Status"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__res_company__sale_onboarding_payment_method__stripe
msgid "Stripe"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_cancel__subject
#: model_terms:ir.ui.view,arch_db:sale.sale_order_cancel_view_form
msgid "Subject"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_subtotal
msgid "Subtotal"
msgstr "Podukupno"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Total"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_report_view_tree
msgid "Sum of Untaxed Total"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tag_ids
#: model:ir.ui.menu,name:sale.menu_tag_config
msgid "Tags"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_calculation_rounding_method
msgid "Tax Calculation Rounding Method"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_country_id
msgid "Tax Country"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Tax Total"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__tax_totals
msgid "Tax Totals"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_calculation_rounding_method
msgid "Tax calculation rounding method"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax excl."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tax incl."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_tax
#: model:ir.model.fields,field_description:sale.field_sale_order_line__tax_id
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
msgid "Taxes"
msgstr "Porezi"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__deposit_taxes_id
msgid "Taxes used for deposits"
msgstr "Upotreba PDV-a za depozite"

#. module: sale
#. odoo-python
#: code:addons/sale/models/crm_team.py:0
msgid "Team %(team_name)s has %(sale_order_count)s active sale orders. Consider canceling them or archiving the team instead."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Tell us why you are refusing this quotation, this will help us improve our services."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__terms_type
msgid "Terms & Conditions format"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Terms & Conditions: %s"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions"
msgstr "Termini i Uslovi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Terms &amp; Conditions:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__note
msgid "Terms and conditions"
msgstr "Pravila i Uslovi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Terms and conditions..."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid "The Sales Order %s linked to the Analytic Account %s is cancelled. You cannot register an expense on a cancelled Sales Order."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid "The Sales Order %s linked to the Analytic Account %s is currently locked. You cannot register an expense on a locked Sales Order. Please create a new SO linked to this Analytic Account."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/account_move_line.py:0
msgid "The Sales Order %s linked to the Analytic Account %s must be validated before registering expenses."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The access token is invalid."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount_to_invoice
msgid "The amount to invoice = Sale Order Total - Confirmed Down Payments."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "The delivery date is sooner than the expected date. You may be unable to honor the delivery date."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__fixed_amount
msgid "The fixed amount to be invoiced in advance."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_template.py:0
msgid ""
"The following products cannot be restricted to the company %s because they have already been used in quotations or sales orders in another company:\n"
"%s\n"
"You can archive these products and recreate them with your company restriction instead, or leave them as shared product."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_res_config_settings__automatic_invoice
msgid ""
"The invoice is generated automatically and available in the customer portal when the transaction is confirmed by the payment provider.\n"
"The invoice is marked as paid and the payment is registered in the payment journal defined in the configuration of the payment provider.\n"
"This mode is advised if you issue the final invoice at the order and not after the delivery."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "The margin is computed as the sum of product sales prices minus the cost set in their detail form."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "The new invoice will deduct draft invoices linked to this sale order."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "The order is not in a state requiring customer payment."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The order is not in a state requiring customer signature."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "The ordered quantity has been updated."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__reference
msgid "The payment communication of this sale order."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_advance_payment_inv__amount
msgid "The percentage of amount to be invoiced in advance."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "The product used to invoice a down payment should be of type 'Service'. Please use another product or update this product."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "The product used to invoice a down payment should have an invoice policyset to \"Ordered quantities\". Please update your deposit product to be able to create a deposit invoice."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/controllers/portal.py:0
msgid "The provided parameters are invalid."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/wizard/sale_make_invoice_advance.py:0
msgid "The value of the down payment amount must be positive."
msgstr "Iznos predujma mora biti pozitivan."

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
msgid "There are currently no orders for your account."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "There are currently no quotations for your account."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "There are existing"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"There is nothing to invoice!\n"
"\n"
"Reason(s) of this behavior could be:\n"
"- You should deliver your products before invoicing them.\n"
"- You should modify the invoicing policy of your product: Open the product, go to the \"Sales\" tab and modify invoicing policy from \"delivered quantities\" to \"ordered quantities\". For Services, you should modify the Service Invoicing Policy to 'Prepaid'."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "This default value is applied to any new product created. This can be changed in the product detail form."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__campaign_id
#: model:ir.model.fields,help:sale.field_account_move__campaign_id
#: model:ir.model.fields,help:sale.field_account_payment__campaign_id
#: model:ir.model.fields,help:sale.field_sale_order__campaign_id
msgid "This is a name that helps you keep track of your different campaign efforts, e.g. Fall_Drive, Christmas_Special"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__commitment_date
msgid "This is the delivery date promised to the customer. If set, the delivery order will be scheduled based on this date rather than product lead times."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__medium_id
#: model:ir.model.fields,help:sale.field_account_move__medium_id
#: model:ir.model.fields,help:sale.field_account_payment__medium_id
#: model:ir.model.fields,help:sale.field_sale_order__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_account_bank_statement_line__source_id
#: model:ir.model.fields,help:sale.field_account_move__source_id
#: model:ir.model.fields,help:sale.field_account_payment__source_id
#: model:ir.model.fields,help:sale.field_sale_order__source_id
msgid "This is the source of the link, e.g. Search Engine, another domain, or name of email list"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "This product is packaged by %(pack_size).2f %(pack_name)s. You should sell %(quantity).2f %(unit)s."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_all
msgid "This report performs analysis on your quotations and sales orders. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application."
msgstr "Ovaj izvještaj radi analizu vaših ponuda i prodajnih narudžbi. Analiza provjerava prihode od prodaje i sortira ih prema različitim kriterijima grupisanja (prodavač, partner, proizvod, itd.) Koristite ovaj izvještaj za izvođenje analize na prodajama koje još nisu fakturisane. Ako želite analizirati prihod, trebali bi koristiti Izvještaj analize faktura u računovodstvenoj aplikaciji."

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_quotation_salesteam
msgid "This report performs analysis on your quotations. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_order_report_so_salesteam
msgid "This report performs analysis on your sales orders. Analysis check your sales revenues and sort it by different group criteria (salesman, partner, product, etc.) Use this report to perform analysis on sales not having invoiced yet. If you want to analyse your turnover, you should use the Invoice Analysis report in the Accounting application."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "This will update all taxes based on the currently selected fiscal position."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "This will update all unit prices based on the currently set pricelist."
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__to_invoice
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__to_invoice
#: model:ir.ui.menu,name:sale.menu_sale_invoicing
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_product_search
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_line_filter
msgid "To Invoice"
msgstr "Za fakturisati"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_view_search_inherit_sale
msgid "To Upsell"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "To send invitations in B2B mode, open a contact or select several ones in list view and click on 'Portal Access Management' option in the dropdown menu *Action*."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "To speed up order confirmation, we can activate electronic signatures or payments."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_filter
msgid "Today Activities"
msgstr "Današnje aktivnosti"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_total
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_total
#: model:ir.model.fields,field_description:sale.field_sale_report__price_total
#: model_terms:ir.ui.view,arch_db:sale.portal_my_orders
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Total"
msgstr "Ukupno"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_tax
msgid "Total Tax"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Excluded"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_tree
msgid "Total Tax Included"
msgstr "Ukupan uračunati porez"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_product_product__service_type
#: model:ir.model.fields,field_description:sale.field_product_template__service_type
msgid "Track Service"
msgstr "Usluga praćenja"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Tracking"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__transaction_ids
msgid "Transactions"
msgstr "Transakcije"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__type_name
msgid "Type Name"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Type a message..."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a customer..."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Type to find a product..."
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_ups
msgid "UPS Connector"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_usps
msgid "USPS Connector"
msgstr ""

#. module: sale
#: model:ir.model,name:sale.model_utm_campaign
msgid "UTM Campaign"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__price_unit
#: model_terms:ir.ui.view,arch_db:sale.report_saleorder_document
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_content
msgid "Unit Price"
msgstr "Jedinična cijena"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Unit Price:"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__product_uom
#: model:ir.model.fields,field_description:sale.field_sale_report__product_uom
#: model_terms:ir.ui.view,arch_db:sale.view_order_line_tree
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_catalog/sale_order_line/sale_order_line.xml:0
msgid "Unit price:"
msgstr ""

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_form_action
#: model:ir.ui.menu,name:sale.next_id_16
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Units of Measure"
msgstr "Jedinice mere"

#. module: sale
#: model:ir.ui.menu,name:sale.menu_product_uom_categ_form_action
msgid "Units of Measure Categories"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sales_order_auto_done_setting
msgid "Unlock"
msgstr "Otključaj"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__amount_untaxed
msgid "Untaxed Amount"
msgstr "Neoporezivi iznos"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_invoiced
msgid "Untaxed Amount Invoiced"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_to_invoice
#: model:ir.model.fields,field_description:sale.field_sale_report__untaxed_amount_to_invoice
msgid "Untaxed Amount To Invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order_line__untaxed_amount_invoiced
msgid "Untaxed Invoiced Amount"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__price_subtotal
msgid "Untaxed Total"
msgstr "Ukupno neoporezovano"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "UoM"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Prices"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Update Taxes"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Upsell %(order)s for customer %(customer)s"
msgstr ""

#. module: sale
#: model:ir.model.fields.selection,name:sale.selection__sale_order__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_order_line__invoice_status__upselling
#: model:ir.model.fields.selection,name:sale.selection__sale_report__invoice_status__upselling
msgid "Upselling Opportunity"
msgstr "Prilika naknadnog fakturisanja"

#. module: sale
#: model:mail.template,description:sale.email_template_edi_sale
msgid "Used by salespeople when they send quotations or proforma to prospects"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.portal_my_quotations
msgid "Valid Until"
msgstr "Važi do"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Validate Order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
msgid "Variant Grid Entry"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "View Order"
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "View Quotation"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "Void Transaction"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_report__volume
msgid "Volume"
msgstr "Volumen"

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
#: code:addons/sale/models/sale_order_line.py:0
#: code:addons/sale/wizard/res_config_settings.py:0
#: model:ir.model.fields.selection,name:sale.selection__product_template__sale_line_warn__warning
#: model:ir.model.fields.selection,name:sale.selection__res_partner__sale_warn__warning
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning"
msgstr "Upozorenje"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
#: code:addons/sale/models/sale_order_line.py:0
msgid "Warning for %s"
msgstr "Upozorenje za %s"

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "Warning for the change of your quotation's company"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_partner_view_buttons
msgid "Warning on the Sales Order"
msgstr "Upozorenje na prodajnoj narudžbi"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_form_view_sale_order_button
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view_sale_order_button
msgid "Warning when Selling this Product"
msgstr "Upozorenje kod Prodaje ovog Proizvoda"

#. module: sale
#: model:ir.model.fields,field_description:sale.field_sale_order__website_message_ids
msgid "Website Messages"
msgstr "Poruke sa website-a"

#. module: sale
#: model:ir.model.fields,help:sale.field_sale_order__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/tours/sale.js:0
msgid "Write a company name to create one, or see suggestions."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.product_template_form_view
msgid "You can invoice them before they are delivered."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "You can not delete a sent quotation or a confirmed sales order. You must first cancel it."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid ""
"You can not remove an order line once the sales order is confirmed.\n"
"You should rather set the quantity to 0."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.action_orders_to_invoice
msgid ""
"You can select all orders and invoice them in batch,<br>\n"
"            or check every order and invoice them one by one."
msgstr ""

#. module: sale
#: model:ir.model.fields,help:sale.field_payment_provider__so_reference_type
msgid "You can set here the communication type that will appear on sales orders.The communication will be given to the customer when they choose the payment method."
msgstr ""

#. module: sale
#. odoo-javascript
#: code:addons/sale/static/src/js/product_catalog/sale_order_line/sale_order_line.xml:0
msgid "You can't edit this product in the catalog."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/product_product.py:0
#: code:addons/sale/models/product_template.py:0
msgid "You cannot change the product's type because it is already used in sales orders."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order_line.py:0
msgid "You cannot change the type of a sale order line. Instead you should delete the current line and create a new line of the proper type."
msgstr ""

#. module: sale
#: model:ir.model.constraint,message:sale.constraint_res_company_check_quotation_validity_days
msgid "You cannot set a negative number for the default quotation validity. Leave empty (or 0) to disable the automatic expiration of quotations."
msgstr ""

#. module: sale
#: model_terms:ir.actions.act_window,help:sale.product_template_action
msgid ""
"You must define a product for everything you sell or purchase,\n"
"                whether it's a storable product, a consumable or a service."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your feedback..."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed but still needs to be paid to be confirmed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order has been signed."
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "Your order is not in a state to be rejected."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid ""
"Your quotation contains products from company %(product_company)s whereas your quotation belongs to company %(quote_company)s. \n"
" Please change the company of your quotation or remove the products from other companies (%(bad_products)s)."
msgstr ""

#. module: sale
#: model:ir.actions.server,name:sale.send_invoice_cron_ir_actions_server
msgid "automatic invoicing: send ready invoice"
msgstr ""

#. module: sale
#: model:ir.model.fields,field_description:sale.field_res_config_settings__module_delivery_bpost
msgid "bpost Connector"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.sale_order_portal_template
msgid "close"
msgstr "zatvori"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:sale.view_order_form
msgid "days"
msgstr "Dani"

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.view_sale_advance_payment_inv
msgid "for this Sale Order."
msgstr ""

#. module: sale
#. odoo-python
#: code:addons/sale/models/sale_order.py:0
msgid "sale order"
msgstr ""

#. module: sale
#: model_terms:ir.ui.view,arch_db:sale.mass_cancel_orders_view_form
msgid ""
"selected\n"
"                    quotations?"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_confirmation
msgid "{{ object.company_id.name }} {{ (object.get_portal_last_transaction().state == 'pending') and 'Pending Order' or 'Order' }} (Ref {{ object.name or 'n/a' }})"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.email_template_edi_sale
msgid "{{ object.company_id.name }} {{ object.state in ('draft', 'sent') and (ctx.get('proforma') and 'Proforma' or 'Quotation') or 'Order' }} (Ref {{ object.name or 'n/a' }})"
msgstr ""

#. module: sale
#: model:mail.template,subject:sale.mail_template_sale_cancellation
msgid "{{ object.company_id.name }} {{ object.type_name }} Cancelled (Ref {{ object.name or 'n/a' }})"
msgstr ""
