# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_forum
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:42+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Korean (https://app.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid " Flag"
msgstr "신고하기"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid " Flagged"
msgstr "신고함"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_answers
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "# Answers"
msgstr "# 답변"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_favorites
msgid "# Favorites"
msgstr "# 즐겨찾기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_posts
msgid "# Posts"
msgstr "# 게시물"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__total_views
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "# Views"
msgstr "# 조회수"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to accept or refuse an answer."
msgstr "답변을 채택하거나 거절하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to answer a question."
msgstr "질문에 답하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to close or reopen a post."
msgstr "게시물을 재게시하거나 닫으려면  %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to comment."
msgstr "댓글에는 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert a comment to an answer."
msgstr "댓글을 답변으로 변환하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert an answer to a comment."
msgstr "답변을 댓글로 변환하려면 %d 명성이 필요합니다. "

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to convert your comment to an answer."
msgstr "귀하의 댓글을 답변으로 변환하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_tag.py:0
msgid "%d karma required to create a new Tag."
msgstr "새 태그를 만들려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to create a new question."
msgstr "새 질문을 작성하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to delete a comment."
msgstr "댓글을 삭제하려면 %d 카르마가 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to delete or reactivate a post."
msgstr "게시물을 삭제하거나 다시 활성화하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "%d karma required to downvote."
msgstr "반대투표를 하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to edit a post."
msgstr "게시물을 수정하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to flag a post."
msgstr "게시물을 신고하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to mark a post as offensive."
msgstr "게시물을 불쾌한 것으로 표시하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to post an image or link."
msgstr "이미지 또는 링크를 게시하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to refuse a post."
msgstr "게시를 거절하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to retag."
msgstr "태그를 다시 할려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to unlink a post."
msgstr "게시물의 링크를 해제하려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "%d karma required to upvote."
msgstr "찬성투표를 할려면 %d 명성이 필요합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "%d karma required to validate a post."
msgstr "게시물을 승인하려면 %d 명성이 필요합니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "&amp;nbsp;"
msgstr "&amp;nbsp;"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "'Please enter 2 or more characters'"
msgstr "'2자 이상 입력해 주세요'"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "'Tags'"
msgstr "'태그'"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "(The above section was adapted from Stackoverflow’s FAQ.)"
msgstr "(위의 섹션은 Stackoverflow의 FAQ에서 수정되었습니다.)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "(votes - 1) **"
msgstr "(투표 - 1) **"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "/ (days + 2) **"
msgstr "/ (일 + 2) **"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "45% of questions shared"
msgstr "질문의 45 % 공유"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"65% more chance to get an\n"
"        answer"
msgstr ""
"65%의 확률로 답을 얻을 수 \n"
"          있습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<b class=\"d-block \">This answer has been flagged</b>\n"
"                            As a moderator, you can either validate or reject this answer."
msgstr ""
"<b class=\"d-block \">이 답변이 신고되었습니다.</b>\n"
"                            운영자는 이 답변을 승인하거나 거부할 수 있습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<b class=\"d-block\">You have a pending post</b>\n"
"                        Please wait for a moderator to validate your previous post to be allowed replying questions."
msgstr ""
"<b class=\"d-block\">보류 중인 게시물이 있습니다.</b>\n"
"                        중재자가 질문에 대한 답변이 허용되도록 이전 게시물을 승인하는 동안 기다리십시오."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead either edit the "
"question or add a question comment."
msgstr "<b>답변은 질문을 추가하거나 확장해서는 안됩니다</b>. 대신 질문을 편집하거나 질문 주석을 추가하십시오."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not add or expand questions</b>. Instead, either edit the "
"question or add a comment."
msgstr "<b>답변에는 질문을 추가하거나 확장할 수 없습니다</b>. 대신 질문을 수정하거나 의견을 추가할 수 있습니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not comment other answers</b>. Instead add a comment on "
"the other answers."
msgstr "<b>답변은 다른 답변을 언급해서는 안됩니다</b>. 대신 다른 답변에 의견을 추가하십시오."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers should not start debates</b> This community Q&amp;A is not a "
"discussion group. Please avoid holding debates in your answers as they tend "
"to dilute the essence of questions and answers. For brief discussions please"
" use commenting facility."
msgstr ""
"<b>답변은 토론을 시작해서는 안됩니다.</b> 이 커뮤니티 Q&amp;A는 토론 그룹이 아닙니다. 질문과 답변의 본질을 희석시키는 "
"경향이 있으므로 답변에 토론을 포함시키지 마십시오. 간단한 토론을 하려면 주석 기능을 사용하십시오."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other Questions</b>. Instead add a "
"question comment indication \"Possible duplicate of...\". However, it's ok "
"to include links to other questions or answers providing relevant additional"
" information."
msgstr ""
"<b>답변이 다른 질문을 지목해서는 안됩니다</b>. 대신 \"가능한 복제 가능 항목...\"이라는 질문 주석 표시를 추가하십시오. "
"그러나 관련 추가 정보를 제공하는 다른 질문이나 답변에 대한 링크를 포함해도 됩니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just point to other questions</b>.Instead add a comment"
" indicating <i>\"Possible duplicate of...\"</i>. However, it's fine to "
"include links to other questions or answers providing relevant additional "
"information."
msgstr ""
"<b>답변은 다른 질문을 지목해서는 안됩니다</b>. 대신 <i>\"가능한 복제 가능 항목...\"</i>이라는 질문 주석 표시를 "
"추가하십시오. 그러나 관련 추가 정보를 제공하는 다른 질문이나 답변에 대한 링크를 포함해도됩니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Answers shouldn't just provide a link a solution</b>. Instead provide the"
" solution description text in your answer, even if it's just a copy/paste. "
"Links are welcome, but should be complementary to answer, referring sources "
"or additional reading."
msgstr ""
"<b>답변은 단지 솔루션 링크를 제공해서는 안됩니다</b>. 대신 복사/붙여넣기인 경우에도 답에 솔루션 설명 텍스트를 제공하십시오. "
"링크는 환영하지만 답변, 추천 소스 또는 추가 자료를 보완하려면 보완 적이어야합니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Before you ask - please make sure to search for a similar question.</b> "
"You can search questions by their title or tags. It’s also OK to answer your"
" own question."
msgstr ""
"<b>문의하기 전에 유사한 질문을 검색하십시오.</b> 제목이나 태그별로 질문을 검색할 수 있습니다. 자신의 질문에 대답해도 됩니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<b>Please avoid asking questions that are too subjective and "
"argumentative</b> or not relevant to this community."
msgstr "이 커뮤니티와 관련이 없거나 <b>너무 주관적이고 논쟁적인 질문은 피하십시오.</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid ""
"<b>Please try to give a substantial answer.</b> If you wanted to comment on the question or answer, just\n"
"            <b>use the commenting tool.</b> Please remember that you can always <b>revise your answers</b>\n"
"            - no need to answer the same question twice. Also, please <b>don't forget to vote</b>\n"
"            - it really helps to select the best questions and answers!"
msgstr ""
"<b>실질적인 답변을 하도록 노력하세요.</b> 질문이나 답변에 대해 댓글을 달려면 \n"
"              <b>댓글 도구</b>를 사용합니다. 항상 <b>답변을 수정</b>할 수 있다는 것을 기억하세요.\n"
"               - 같은 질문에 두 번 대답할 필요가 없습니다. 그리고 <b>꼭 투표</b>해주세요.\n"
"              - 최고의 질문과 답변을 선택하는 데 정말 도움이 됩니다!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "<b>Why can other people edit my questions/answers?</b>"
msgstr "<b>다른 사람들이 내 질문/답변을 수정할 수 있는 이유는 무엇인가요?</b>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<b>You already have a pending post.</b><br/>"
msgstr "<b>이미 대기 중인 게시물이 있습니다.</b><br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy o-text-gold ms-2\" role=\"img\" aria-label=\"Gold badge\" title=\"Gold badge\"/>"
msgstr ""
"<b>|</b>\n"
"                    <span class=\"fa fa-trophy o-text-gold ms-2\" role=\"img\" aria-label=\"금배지\" title=\"금배지\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<br/>by"
msgstr "<br/>작성자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-angle-left me-2\"/>Back to All Posts"
msgstr "<i class=\"fa fa-angle-left me-2\"/>모든 게시물로 돌아가기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "<i class=\"fa fa-arrow-right\"/> Go To Forums"
msgstr "<i class=\"fa fa-arrow-right\"/> 포럼으로 이동"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "<i class=\"fa fa-bug me-1\"/>Filter Tool"
msgstr "<i class=\"fa fa-bug me-1\"/>필터 도구"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid ""
"<i class=\"fa fa-caret-down\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Downvote\"/>"
msgstr ""
"<i class=\"fa fa-caret-down\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"반대투표\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid ""
"<i class=\"fa fa-caret-up\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Upvote\"/>"
msgstr ""
"<i class=\"fa fa-caret-up\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"찬성투표\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Accept"
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>수락"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid ""
"<i class=\"fa fa-check fa-fw me-1\"/>Be less specific in your wording for a "
"wider search result."
msgstr ""
"<i class=\"fa fa-check fa-fw me-1\"/>더 폭넓은 검색 결과를 얻으려면 검색어를 덜 구체적으로 작성하세요."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Check your spelling and try again."
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>철자를 확인하고 다시 시도해 주세요."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "<i class=\"fa fa-check fa-fw me-1\"/>Try searching for one or two words."
msgstr "<i class=\"fa fa-check fa-fw me-1\"/>한두 개의 단어로 검색해 보세요."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "<i class=\"fa fa-check me-1\"/>Solved"
msgstr "<i class=\"fa fa-check me-1\"/>해결 완료"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "<i class=\"fa fa-check\"/> Accept"
msgstr "<i class=\"fa fa-check\"/> 수락"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "<i class=\"fa fa-check\"/> How to configure TPS and TVQ's canadian taxes?"
msgstr "<i class=\"fa fa-check\"/> TPS 및 TVQ의 캐나다 세금을 구성하는 방법은 무엇입니까?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-check\"/><span class=\"ms-2\">Accept</span>"
msgstr "<i class=\"fa fa-check\"/><span class=\"ms-2\">수락</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-comments-o me-1\" title=\"Forum\"/>"
msgstr "<i class=\"fa fa-comments-o me-1\" title=\"게시판\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid ""
"<i class=\"fa fa-ellipsis-h\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"More\"/>"
msgstr ""
"<i class=\"fa fa-ellipsis-h\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"더\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-eye me-1\" title=\"Views\"/>"
msgstr "<i class=\"fa fa-eye me-1\" title=\"조회수\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-flag\"/> Country"
msgstr "<i class=\"fa fa-flag\"/> 국가"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-font\"/> Text"
msgstr "<i class=\"fa fa-font\"/> 텍스트"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "<i class=\"fa fa-fw fa-check me-1\"/>Following"
msgstr "<i class=\"fa fa-fw fa-check me-1\"/>팔로잉"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
msgid "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"
msgstr "<i class=\"fa fa-globe me-1\" title=\"Website\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_mobile
msgid "<i class=\"fa fa-info-circle fa-fw\"/> About this forum"
msgstr "<i class=\"fa fa-info-circle fa-fw\"/> 게시판 정보"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<i class=\"fa fa-pencil\"/>\n"
"                                Edit<span class=\"d-none d-lg-inline\"> your answer</span>"
msgstr ""
"<i class=\"fa fa-pencil\"/>\n"
"                                <span class=\"d-none d-lg-inline\"> 답변</span> 수정하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid ""
"<i class=\"fa fa-reply me-1 d-lg-none\"/>\n"
"                0"
msgstr ""
"<i class=\"fa fa-reply me-1 d-lg-none\"/>\n"
"                0"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "<i class=\"fa fa-reply me-1\"/>Reply"
msgstr "<i class=\"fa fa-reply me-1\"/>댓글"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid ""
"<i class=\"fa fa-share-alt\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"Share\"/>"
msgstr ""
"<i class=\"fa fa-share-alt\" data-bs-toggle=\"tooltip\" data-bs-"
"placement=\"top\" title=\"공유\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<i class=\"fa fa-shield fa-fw opacity-50\"/> Badges"
msgstr "<i class=\"fa fa-shield fa-fw opacity-50\"/> 배지"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"Negative votes\" title=\"Negative votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-down text-danger ms-3\" role=\"img\" aria-"
"label=\"부정적 투표\" title=\"부정적 투표\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"
msgstr ""
"<i class=\"fa fa-thumbs-up text-success\" role=\"img\" aria-label=\"Positive"
" votes\" title=\"Positive votes\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<i class=\"fa fa-times fa-fw me-1\"/>Reject"
msgstr "<i class=\"fa fa-times fa-fw me-1\"/>거부"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<i class=\"fa fa-times\"/> Good morning to all! Please, can someone help "
"solve my tax computation problem in Canada? Thanks!"
msgstr ""
"<i class=\"fa fa-times\"/> 안녕하세요, 캐나다에서 세금 계산 문제를 해결하는 데 도움을 받을 수 있나요? "
"감사합니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "<i class=\"fa fa-times\"/> Reject"
msgstr "<i class=\"fa fa-times\"/> 거부"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-times\"/><span class=\"ms-2\">Offensive</span>"
msgstr "<i class=\"fa fa-times\"/><span class=\"ms-2\">모욕</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-times\"/><span class=\"ms-2\">Reject</span>"
msgstr "<i class=\"fa fa-times\"/><span class=\"ms-2\">거부</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "<i class=\"fa fa-user\"/> User"
msgstr "<i class=\"fa fa-user\"/> 사용자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<i class=\"fa fa-users fa-fw opacity-50\"/> People"
msgstr "<i class=\"fa fa-users fa-fw opacity-50\"/> 사용자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<i class=\"oi oi-arrow-right d-inline-block\"/> Go to Forums"
msgstr "<i class=\"oi oi-arrow-right d-inline-block\"/> 게시판으로 이동하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid ""
"<i class=\"oi oi-arrow-right display-inline-block\"/> Return to questions "
"list"
msgstr "<i class=\"oi oi-arrow-right display-inline-block\"/> 질문 목록으로 돌아가기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_sub_nav
msgid "<i class=\"oi oi-chevron-left small\"/> Back"
msgstr "<i class=\"oi oi-chevron-left small\"/> 뒤로"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "<small class=\"fw-bold\">Votes</small>"
msgstr "<small class=\"fw-bold\">투표</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "<small>(View all)</small>"
msgstr "<small>(모두 보기)</small>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "<span class=\"badge bg-dark me-1\">Last post:</span>"
msgstr "<span class=\"badge bg-dark me-1\">최근 게시물:</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy o-text-bronze ms-2\" role=\"img\" aria-"
"label=\"Bronze badge\" title=\"Bronze badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy o-text-bronze ms-2\" role=\"img\" aria-"
"label=\"동배지\" title=\"동배지\"/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.contact
msgid ""
"<span class=\"fa fa-trophy o-text-silver ms-2\" role=\"img\" aria-"
"label=\"Silver badge\" title=\"Silver badge\"/>"
msgstr ""
"<span class=\"fa fa-trophy o-text-silver ms-2\" role=\"img\" aria-"
"label=\"은배지\" title=\"은배지\"/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What kind of questions can I ask here?</span>"
msgstr "<span class=\"flex-grow-1\">여기에서 어떤 질문을 할 수 있나요?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What should I avoid in my answers?</span>"
msgstr "<span class=\"flex-grow-1\">답변에서 피해야 할 사항은 무엇인가요?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "<span class=\"flex-grow-1\">What should I avoid in my questions?</span>"
msgstr "<span class=\"flex-grow-1\">질문에서 피해야 할 사항은 무엇인가요?</span>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"<span class=\"flex-grow-1\">Why can other people edit my "
"questions/answers?</span>"
msgstr "<span class=\"flex-grow-1\">다른 사람이 내 질문/답변을 수정할 수 있는 이유는 무엇인가요?</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">consider adding an "
"example.</span>"
msgstr "<span class=\"form-text small text-muted d-block\">예제를 추가해 보세요.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">select text to format "
"it.</span>"
msgstr ""
"<span class=\"form-text small text-muted d-block\">텍스트를 선택하여 서식을 "
"지정합니다.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted d-block\">use '/' to insert "
"images.</span>"
msgstr ""
"<span class=\"form-text small text-muted d-block\">'/'를 사용하여 이미지를 "
"삽입합니다.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"<span class=\"form-text small text-muted me-1\"><i class=\"fa fa-"
"lightbulb-o\"/> Tip:</span>"
msgstr ""
"<span class=\"form-text small text-muted me-1\"><i class=\"fa fa-"
"lightbulb-o\"/> 팁:</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "<span class=\"o_stat_text\">Favorites</span>"
msgstr "<span class=\"o_stat_text\">즐겨찾기</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "<span class=\"o_stat_text\">Go to <br/>Website</span>"
msgstr "<span class=\"o_stat_text\"><br/>웹사이트</span>로 이동"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "<span class=\"o_stat_text\">Posts</span>"
msgstr "<span class=\"o_stat_text\">게시물</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_activities
msgid "<span class=\"text-muted\">There is no activity yet.</span>"
msgstr "<span class=\"text-muted\">아직 활동이 없습니다.</span>"

#. module: website_forum
#: model_terms:web_tour.tour,rainbow_man_message:website_forum.question
msgid "<span><b>Good job!</b> You went through all steps of this tour.</span>"
msgstr "<span><b>잘하셨습니다!</b>  이번 여정의 모든 단계를 마쳤습니다.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "<span>Be the first to answer this question</span>"
msgstr "<span>이 질문에 가장 먼저 답변을 남겨주세요</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "<span>Moderation</span>"
msgstr "<span>조정</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"<span>Please wait for a moderator to validate your previous post to be "
"allowed to reply to questions.</span>"
msgstr "<span>운영자가 이전 게시물을 승인할 때까지 기다려야 질문에 답변할 수 있습니다.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "<span>You need to be registered to interact with the community.</span>"
msgstr "<span>커뮤니티에 참여하려면 회원 가입을 하시기 바랍니다.</span>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "A new answer on"
msgstr "다음에 대한 새로운 답변 : "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "A new question"
msgstr "새로운 질문"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_own
msgid "Accept an answer on own questions"
msgstr "자신의 질문에 대한 답변으로 채택하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer_accept_all
msgid "Accept an answer to all questions"
msgstr "모든 질문에 대한 답변 채택하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Accepted Answer"
msgstr "답변으로 채택됨"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Accepted answer removed"
msgstr "채택된 답변 삭제됨"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accept
msgid "Accepting an answer"
msgstr "답변으로 채택하기"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Access Denied"
msgstr "접근이 거부되었습니다"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction
msgid "Action Needed"
msgstr "조치 필요"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__active
#: model:ir.model.fields,field_description:website_forum.field_forum_post__active
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__active
msgid "Active"
msgstr "활성화"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activities"
msgstr "활동"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Activity"
msgstr "활동"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Add a comment"
msgstr "댓글 남기기"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__allow_share
msgid ""
"After posting the user will be proposed to share its question or answer on "
"social networks, enabling social network propagation of the forum content."
msgstr "게시물 등록 후 소셜 네트워크에 질문이나 답변을 공유하여 게시판 콘텐츠의 소셜 네트워크 전파를 가능하게 합니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "All"
msgstr "전체"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "All Posts"
msgstr "모든 게시물"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "All Topics"
msgstr "모든 주제"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "All forums"
msgstr "모든 게시판"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Animation of a pen checking a checkbox"
msgstr "확인란에 체크하는 펜 애니메이션"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "Answer %s"
msgstr "답변 %s "

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
#: model:mail.message.subtype,description:website_forum.mt_answer_edit
#: model:mail.message.subtype,name:website_forum.mt_answer_edit
msgid "Answer Edited"
msgstr "답변 편집됨"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_accepted
msgid "Answer accepted"
msgstr "답변으로 채택됨"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_6
msgid "Answer accepted with 15 or more votes"
msgstr "15표 이상으로 답변이 채택됨"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_downvote
msgid "Answer downvoted"
msgstr "답변에 반대투표함"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_flagged
msgid "Answer flagged"
msgstr "신고한 답변"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_answer
msgid "Answer questions"
msgstr "질문 답변"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_answer_upvote
msgid "Answer upvoted"
msgstr "답변에 찬성투표함"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_4
msgid "Answer voted up 15 times"
msgstr "15표 이상 찬성투표된 답변"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_2
msgid "Answer voted up 4 times"
msgstr "4표 이상 찬성투표된 답변"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_3
msgid "Answer voted up 6 times"
msgstr "6표 이상 찬성투표된 답변"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_5
msgid "Answer was accepted with 3 or more votes"
msgstr "3표 이상으로 답변이 채택되었습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Answer:"
msgstr "답변:"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__child_count_desc
msgid "Answered"
msgstr "답변함"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Answered Posts"
msgstr "답변이 달린 게시물"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "Answered by"
msgstr "답변자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Answered on"
msgstr "답변일"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_8
msgid "Answered own question with at least 4 up votes"
msgstr "최소 4표 이상 자신의 질문에 답변함"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_count
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_kanban
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Answers"
msgstr "답변"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_gamification_challenge__challenge_category
msgid "Appears in"
msgstr "다음에 표시"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Archived"
msgstr "보관됨"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Are you sure you want to delete this comment?"
msgstr "이 댓글을 정말 삭제하시겠습니까?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "As a moderator, you can either validate or reject this answer."
msgstr "운영자는 이 답변을 승인하거나 거부할 수 있습니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Ask a new question"
msgstr "새로운 질문하기"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Ask a question"
msgstr "질문하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_ask
msgid "Ask questions"
msgstr "질문하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_post
msgid "Ask questions without validation"
msgstr "승인없이 질문하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Ask your question"
msgstr "질문하기"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_26
msgid "Asked a question and accepted an answer"
msgstr "질문하고 답변을 채택하기"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_1
msgid "Asked a question with at least 150 views"
msgstr "조회수가 150회 이상인 질문"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_2
msgid "Asked a question with at least 250 views"
msgstr "조회수가 250회 이상인 질문"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_3
msgid "Asked a question with at least 500 views"
msgstr "조회수가 500회 이상인 질문"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_7
msgid "Asked first question with at least one up vote"
msgstr "1표 이상의 첫 번째 질문을 함"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_date
msgid "Asked on"
msgstr "질문일"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_new
msgid "Asking a question"
msgstr "질문하기"

#. module: website_forum
#: model:ir.model,name:website_forum.model_ir_attachment
msgid "Attachment"
msgstr "첨부 파일"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_attachment_count
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_attachment_count
msgid "Attachment Count"
msgstr "첨부 파일 수"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Author"
msgstr "작성자"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__authorized_group_id
msgid "Authorized Group"
msgstr "인증 그룹"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_1
msgid "Autobiographer"
msgstr "자서전 작가"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "Avatar"
msgstr "아바타"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Back to Question"
msgstr "질문으로 돌아가기"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad \"filters\" value \"%(filters)s\"."
msgstr "잘못된 \"filters\" 값 \"%(filters)s\"."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad \"tag_char\" value \"%(tag_char)s\""
msgstr "잘못된 \"tag_char\" 값 \"%(tag_char)s\""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Bad Request"
msgstr "나쁜 요청"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_badges
msgid "Badges"
msgstr "뱃지"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__basic
msgid "Basic"
msgstr "기본"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Because there are no posts in this forum yet."
msgstr "이 게시판에는 아직 게시물이 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Best Answer"
msgstr "베스트 답변"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "By sharing you answer, you will get additional"
msgstr "답변을 공유하면 추가 정보를 얻을 수 있습니다"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_accept
msgid "Can Accept"
msgstr "채택 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_answer
msgid "Can Answer"
msgstr "답변 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_ask
msgid "Can Ask"
msgstr "질문 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_post
msgid "Can Automatically be Validated"
msgstr "자동으로 승인 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_close
msgid "Can Close"
msgstr "마감 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment
msgid "Can Comment"
msgstr "댓글 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_comment_convert
msgid "Can Convert to Comment"
msgstr "댓글 변환 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_downvote
msgid "Can Downvote"
msgstr "반대투표 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_edit
msgid "Can Edit"
msgstr "편집 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_flag
msgid "Can Flag"
msgstr "신고 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_moderate
msgid "Can Moderate"
msgstr "중재 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_unlink
msgid "Can Unlink"
msgstr "링크 해제 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_upvote
msgid "Can Upvote"
msgstr "찬성투표 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_use_full_editor
msgid "Can Use Full Editor"
msgstr "전체 에디터 사용 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_view
msgid "Can View"
msgstr "보기 가능"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_retag
msgid "Change question tags"
msgstr "질문 태그 변경"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_4
#: model:gamification.challenge,name:website_forum.challenge_chief_commentator
msgid "Chief Commentator"
msgstr "최고 논평가"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click here to accept this answer."
msgstr "이 답변을 채택하려면 여기를 클릭하십시오."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to post your answer."
msgstr "답변을 게시하려면 클릭하십시오."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to post your question."
msgstr "질문을 게시하려면 클릭하십시오."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Click to reply."
msgstr "클릭하여 답글 작성하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_mobile
msgid "Close"
msgstr "닫기"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_reasons
msgid "Close Reasons"
msgstr "마감 사유"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_all
msgid "Close all posts"
msgstr "모든 게시물 마감하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_close_own
msgid "Close own posts"
msgstr "자신의 게시물 마감하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Close post"
msgstr "게시 마감하기"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__close
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Closed"
msgstr "마감됨"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Closed Posts"
msgstr "비공개 게시물"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_uid
msgid "Closed by"
msgstr "마감자"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_date
msgid "Closed on"
msgstr "마감일"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Closing"
msgstr "마감하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__name
msgid "Closing Reason"
msgstr "마감 사유"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__color
msgid "Color"
msgstr "색상"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Comment"
msgstr "댓글"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_all
msgid "Comment all posts"
msgstr "모든 게시물에 댓글 달기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_own
msgid "Comment own posts"
msgstr "자신의 게시글의 댓글"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Comment this post"
msgstr "이 게시물에 댓글 달기"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_p_2
#: model:gamification.challenge,name:website_forum.challenge_commentator
#: model:gamification.goal.definition,name:website_forum.definition_commentator
msgid "Commentator"
msgstr "논평자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Community Forums"
msgstr "커뮤니티 게시판"

#. module: website_forum
#: model:gamification.challenge,name:website_forum.challenge_configure_profile
msgid "Complete own biography"
msgstr "자기 소개 완성"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_configure_profile
#: model_terms:gamification.badge,description:website_forum.badge_p_1
msgid "Completed own biography"
msgstr "완성된 자기 소개"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_7
msgid "Contains offensive or malicious remarks"
msgstr "모욕적이거나 악의적인 발언이 포함되어 있는 경우"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__content
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Content"
msgstr "내용"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_all
msgid "Convert all comments to answers"
msgstr "모든 댓글을 답변으로 전환"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_accept
msgid "Convert comment to answer"
msgstr "댓글을 답변으로 변환"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_convert_own
msgid "Convert own comments to answers"
msgstr "자신의 댓글을 답변으로 전환"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Convert to Comment"
msgstr "댓글로 전환"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Convert to answer"
msgstr "답변으로 변환"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_correct
msgid "Correct"
msgstr "정답"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__is_correct
msgid "Correct answer or answer accepted"
msgstr "정답 또는 답변이 채택됨"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_date
#: model:ir.model.fields,field_description:website_forum.field_res_users__create_date
msgid "Create Date"
msgstr "작성일자"

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_post_action
msgid "Create a new forum post"
msgstr "새 게시물 작성하기"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Create a new post in this forum by clicking on the button."
msgstr "버튼을 클릭하여 이 게시판에 새 게시물을 작성합니다."

#. module: website_forum
#: model_terms:ir.actions.act_window,help:website_forum.forum_tag_action
msgid "Create a new tag"
msgstr "새 태그 만들기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
msgid ""
"Create an account today to enjoy exclusive features and engage with our "
"awesome community!"
msgstr "지금 바로 가입하여 독점 서비스를 이용해보고 특별한 커뮤니티와 소통하세요!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_tag_create
msgid "Create new tags"
msgstr "새 태그 만들기"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/website_forum_tags_wrapper.xml:0
msgid "Create option \""
msgstr "생성 옵션 \""

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_32
msgid "Created a tag used by 15 questions"
msgstr "15가지 질문에 사용되는 태그 만들기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__create_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_uid
msgid "Created by"
msgstr "작성자"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__create_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__create_date
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Created on"
msgstr "작성일자"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_4
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_1
msgid "Credible Question"
msgstr "신뢰할 수 있는 질문"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_5
#: model:gamification.challenge,name:website_forum.challenge_critic
#: model:gamification.goal.definition,name:website_forum.definition_critic
msgid "Critic"
msgstr "비평가"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date"
msgstr "날짜"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (high to low)"
msgstr "날짜 (내림차순)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Date (low to high)"
msgstr "날짜 (오름차순)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__default_order
msgid "Default"
msgstr "기본"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Default Sort"
msgstr "기본 정렬"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_gamification_challenge__challenge_category
msgid "Define the visibility of the challenge through menus"
msgstr "메뉴를 통한 도전의 가시성 정의"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Delete"
msgstr "삭제"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_all
msgid "Delete all comments"
msgstr "모든 댓글 삭제"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_all
msgid "Delete all posts"
msgstr "모든 게시물 삭제하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_comment_unlink_own
msgid "Delete own comments"
msgstr "내 댓글 삭제"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_unlink_own
msgid "Delete own posts"
msgstr "자신의 게시물 삭제하기"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Delete the accepted answer"
msgstr "채택된 답변 삭제"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Deleted"
msgstr "삭제됨"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_23
msgid "Deleted own post with 3 or more downvotes"
msgstr "반대투표가 3개 이상인 게시물을 삭제했습니다."

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_6
msgid "Deleted own post with 3 or more upvotes"
msgstr "찬성투표가 3개 이상인 게시물을 삭제했습니다."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__description
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Description"
msgstr "설명"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Description visible on website"
msgstr "웹사이트에 표시되는 설명"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
#: model_terms:ir.ui.view,arch_db:website_forum.post_comment
msgid "Discard"
msgstr "취소"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_6
#: model:gamification.challenge,name:website_forum.challenge_disciplined
#: model:gamification.goal.definition,name:website_forum.definition_disciplined
msgid "Disciplined"
msgstr "규율가"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__discussions
msgid "Discussions (multiple answers)"
msgstr "토론 (복수 응답)"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Dismiss"
msgstr "해산"

#. module: website_forum
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Dismiss message"
msgstr "메시지 삭제"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__display_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__display_name
msgid "Display Name"
msgstr "표시명"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_user_bio
msgid "Display detailed user biography"
msgstr "상세한 사용자 이력 표시"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_downvote
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Downvote"
msgstr "반대투표"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Downvote for posting offensive contents"
msgstr "모욕적인 콘텐츠 게시물에 대한 반대투표"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_1
msgid "Duplicate post"
msgstr "중복 게시물"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Edit"
msgstr "편집"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Edit Answer"
msgstr "답변 편집하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Edit Question"
msgstr "질문 편집하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_all
msgid "Edit all posts"
msgstr "모든 게시물 편집하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_edit_own
msgid "Edit own posts"
msgstr "자신의 게시물 편집하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your Post"
msgstr "귀하의 게시물 편집하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your answer"
msgstr "답변 수정하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Edit your question"
msgstr "질문 수정하기"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_7
#: model:gamification.challenge,name:website_forum.challenge_editor
#: model:gamification.goal.definition,name:website_forum.definition_editor
msgid "Editor"
msgstr "편집기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_editor
msgid "Editor Features: image and links"
msgstr "편집기 기능 : 이미지 및 링크"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Empty box"
msgstr "비어 있는 박스"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
msgid "Enjoying the discussion? Don't just read, join in!"
msgstr "토론이 재미있으신가요? 직접 참여해보세요!"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_5
#: model:gamification.challenge,name:website_forum.challenge_enlightened
#: model:gamification.goal.definition,name:website_forum.definition_enlightened
msgid "Enlightened"
msgstr "계몽가"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Example\n"
"                            <i class=\"fa fa-question-circle\"/>"
msgstr ""
"예제\n"
"                            <i class=\"fa fa-question-circle\"/>"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Facebook"
msgstr "페이스북"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_3
#: model:gamification.challenge,name:website_forum.challenge_famous_question
msgid "Famous Question"
msgstr "유명한 질문"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_count
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Favorite"
msgstr "즐겨찾기"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_5
#: model:gamification.challenge,name:website_forum.challenge_favorite_question_5
msgid "Favorite Question"
msgstr "즐겨찾는 질문"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__favourite_ids
msgid "Favourite"
msgstr "즐겨찾기"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_1
msgid "Favourite Question (1)"
msgstr "즐겨찾는 질문(1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_stellar_question_25
msgid "Favourite Question (25)"
msgstr "즐겨찾는 질문(25)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_favorite_question_5
msgid "Favourite Question (5)"
msgstr "즐겨찾는 질문(5)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Favourite Questions"
msgstr "즐겨찾는 질문"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Favourites"
msgstr "즐겨찾기"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_post__last_activity_date
msgid ""
"Field to keep track of a post's last activity. Updated whenever it is "
"replied to, or when a comment is added on the post or one of its replies."
msgstr "필드를 사용하여 게시물의 마지막 활동을 추적합니다. 댓글이 달리거나 추가 댓글이 달릴 때마다 업데이트됩니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Filter by:"
msgstr "필터 :"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_post_vote
msgid "First Relevance Parameter"
msgstr "첫 번째 관련성 매개 변수"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_5
msgid "First downvote"
msgstr "첫번째 반대투표"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_7
msgid "First edit"
msgstr "첫 번째 편집"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_31
msgid "First upvote"
msgstr "첫 번째 찬성투표"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Flag"
msgstr "신고하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_flag
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_flag
msgid "Flag a post as offensive"
msgstr "모욕 게시물 신고하기"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__flagged
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Flagged"
msgstr "신고함"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__flag_user_id
msgid "Flagged by"
msgstr "신고자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Follow"
msgstr "팔로우"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Followed Questions"
msgstr "팔로우하는 질문"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Followed Tags"
msgstr "팔로우된 태그"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_follower_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_follower_ids
msgid "Followers"
msgstr "팔로워"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_partner_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_partner_ids
msgid "Followers (Partners)"
msgstr "팔로워 (협력사)"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"For example, if you ask an interesting question or give a helpful answer, "
"your input will be upvoted. On the other hand if the answer is misleading - "
"it will be downvoted. Each vote in favor will generate 10 points, each vote "
"against will subtract 2 points. There is a limit of 200 points that can be "
"accumulated for a question or answer per day. The table given at the end "
"explains reputation point requirements for each type of moderation task."
msgstr ""
"예를 들어, 흥미로운 질문이나 유용한 답변을 제공하면 해당 의견은 찬성투표를 받게 됩니다. 반면 답변에 오해의 소지가 있는 경우, 해당 "
"답변은 반대투표를 받게 됩니다. 찬성투표를 받을 때마다 10점을 획득하게 되며, 반대투표를 받을 때마다 2점이 차감됩니다. 각각의 질문 "
"또는 답변에 대해 누적되는 포인트는 하루 총 200점으로 제한되어 있습니다. 마지막에 제시된 표에서 각 유형에 대한 점수 요건을 확인할 "
"수 있습니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/website.py:0
#: model:ir.actions.act_url,name:website_forum.action_open_forum
#: model:ir.model,name:website_forum.model_forum_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__forum_id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__forum_id
#: model:ir.ui.menu,name:website_forum.menu_website_forum_global
#: model:website.menu,name:website_forum.menu_website_forums
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.gamification_karma_tracking_view_search
msgid "Forum"
msgstr "게시판"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_website__forum_count
msgid "Forum Count"
msgstr "게시판 수"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Mode"
msgstr "게시판 모드"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__name
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "Forum Name"
msgstr "게시판 이름"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Forum Page"
msgstr "게시판 페이지"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "Forum Post"
msgstr "게시물"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action
msgid "Forum Post Pages"
msgstr "게시판 게시물 페이지"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_post_pages
msgid "Forum Posts"
msgstr "게시물"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_tag
msgid "Forum Tag"
msgstr "게시판 태그"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_tag_action
msgid "Forum Tags"
msgstr "게시판 태그"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action
#: model:ir.ui.menu,name:website_forum.menu_forum_global
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
#: model_terms:ir.ui.view,arch_db:website_forum.forum_searchbar_input_snippet_options
msgid "Forums"
msgstr "게시판"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_challenge
msgid "Gamification Challenge"
msgstr "도전 과제 업적"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Get notified when there's activity on this post"
msgstr "이 게시물에 활동이 있으면 알림 받기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Get notified when this tag is used"
msgstr "이 태그가 사용되면 알림 받기"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Give your post title."
msgstr "글 제목을 입력합니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Go back to the list of"
msgstr "목록으로 돌아가기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Go enjoy a cup of coffee."
msgstr "커피 한 잔의 여유를 즐겨보세요."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_3
#: model:gamification.challenge,name:website_forum.challenge_good_answer
msgid "Good Answer"
msgstr "좋은 답변"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_answer
msgid "Good Answer (6)"
msgstr "좋은 답변(6)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_9
#: model:gamification.challenge,name:website_forum.challenge_good_question
msgid "Good Question"
msgstr "좋은 질문"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_graph
msgid "Graph of Posts"
msgstr "게시물 그래프"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_4
#: model:gamification.challenge,name:website_forum.challenge_great_answer
msgid "Great Answer"
msgstr "훌륭한 답변"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_answer
msgid "Great Answer (15)"
msgstr "훌륭한 답변(15)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_10
#: model:gamification.challenge,name:website_forum.challenge_great_question
msgid "Great Question"
msgstr "훌륭한 질문"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Grid"
msgstr "그리드"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Group By"
msgstr "그룹별"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq
msgid "Guidelines"
msgstr "지침"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_6
#: model:gamification.challenge,name:website_forum.challenge_guru
msgid "Guru"
msgstr "전문가"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_guru
msgid "Guru (15)"
msgstr "전문가(15)"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__uid_has_answered
msgid "Has Answered"
msgstr "답변 완료됨"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_message
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__has_message
msgid "Has Message"
msgstr "메시지가 있습니다"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__has_pending_post
msgid "Has pending post"
msgstr "보류 중인 게시물 있음"

#. module: website_forum
#: model:forum.forum,name:website_forum.forum_help
msgid "Help"
msgstr "도움말"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Help moderating the forums by upvoting and downvoting posts. <br/>"
msgstr "게시글에 대한 찬성표와 반대표를 던져 게시판을 중재하는 데 도움을 줍니다. <br/>"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "Here a table with the privileges and the karma level"
msgstr "다음은 권한과 카르마 레벨이 표시된 표입니다"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__id
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__id
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__id
msgid "ID"
msgstr "ID"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction
msgid "If checked, new messages require your attention."
msgstr "만약 선택하였으면, 신규 메시지에 주의를 기울여야 합니다."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "이 옵션을 선택하면 일부 정보가 전달 오류를 생성합니다."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__karma_dofollow
msgid ""
"If the author has not enough karma, a nofollow attribute is added to links"
msgstr "작성자가 명성이 충분하지 않으면 링크에 nofollow 특성이 추가됩니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "If this approach is not for you, please respect the community."
msgstr "이 방법이 적합하지 않은 경우에도 커뮤니티를 존중해 주세요."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you close this post, it will be hidden for most users. Only\n"
"            users having a high karma can see closed posts to moderate\n"
"            them."
msgstr ""
"이 게시물을 마감하면 대부분의 사용자가 해당 게시물을 \n"
"               볼 수 없습니다.  명성이 높은 사용자만 \n"
"               마감된 게시물을 볼 수 있습니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"If you fit in one of these example or if your motivation for asking the "
"question is “I would like to participate in a discussion about ______”, then"
" you should not be asking here but on our mailing lists. However, if your "
"motivation is “I would like others to explain ______ to me”, then you are "
"probably OK."
msgstr ""
"만약 당신이 이러한 예시들 중 하나에 들어맞거나 질문을 한 동기가 “저는 ______에 대한 토론에 참여하고 싶습니다”라면, 당신은 "
"여기서가 아니라 우리의 메일링 리스트에 있어야 합니다. 하지만, 만약 여러분의 동기가 “다른 사람들이 나에게 ______를 설명해주었으면"
" 좋겠어요”라면, 여러분은 아마 괜찮을 거예요."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid ""
"If you mark this post as offensive, it will be hidden for most users. Only\n"
"            users having a high karma can see offensive posts to moderate\n"
"            them."
msgstr ""
"이 게시물을 모욕 대상으로 표시하면 대부분의 사용자에게 \n"
"                해당 게시물이 숨겨집니다.  명성이 높은 사용자만 \n"
"                모욕 게시물을 볼 수 있습니다."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1920
msgid "Image"
msgstr "이미지"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_1024
msgid "Image 1024"
msgstr "1024 이미지"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_128
msgid "Image 128"
msgstr "128 이미지"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_256
msgid "Image 256"
msgstr "256 이미지"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__image_512
msgid "Image 512"
msgstr "512 이미지"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_13
msgid "Inappropriate and unacceptable statements"
msgstr "부적절하고 허용되지 않는 발언"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Insert tags related to your question."
msgstr "질문과 관련된 태그를 삽입하십시오."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_11
msgid "Insulting and offensive language"
msgstr "모욕적이고 불쾌감을 주는 언어"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_favourite
msgid "Is Favourite"
msgstr "즐겨찾기 여부"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_is_follower
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_is_follower
msgid "Is Follower"
msgstr "팔로워임"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__can_moderate
msgid "Is a moderator"
msgstr "중재자임"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__has_validated_answer
msgid "Is answered"
msgstr "답변되었는지 여부"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__can_display_biography
msgid "Is the author's biography visible from his post"
msgstr "해당 게시물에 저자의 이력 표시 여부"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "It is not allowed to modify someone else's vote."
msgstr "다른 사람의 투표는 수정할 수 없습니다."

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "It is not allowed to vote for its own post."
msgstr "자신의 게시물에 투표할 수 없습니다."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Karma Error"
msgstr "명성 오류"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Karma Gains"
msgstr "명성 획득"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Karma Related Rights"
msgstr "옳은 것과 관련된 명성"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_close
msgid "Karma to close"
msgstr "마감하기 명성"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment
msgid "Karma to comment"
msgstr "댓글달기 명성"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_comment_convert
msgid "Karma to convert comment to answer"
msgstr "댓글을 답변으로 변환하는 명성"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_edit
msgid "Karma to edit"
msgstr "편집하기 명성"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__karma_unlink
msgid "Karma to unlink"
msgstr "링크해제하기 명성"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post
msgid "Last Activity"
msgstr "최근 활동"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__last_post_id
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Last Post"
msgstr "최근 게시물"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__last_activity_date_desc
msgid "Last Updated"
msgstr "최근 갱신"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_uid
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__write_date
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__last_activity_date
msgid "Last activity on"
msgstr "최근 활동일"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Layout"
msgstr "배치"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_25
msgid "Left 10 answers with score of 10 or more"
msgstr "10점 이상으로 10개의 답변을 남겼습니다."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_url
msgid "Link to questions with the tag"
msgstr "태그가 포함된 질문 링크"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "LinkedIn"
msgstr "링크드인"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "List"
msgstr "목록"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Mark as Best Answer"
msgstr "최고의 답변으로 표시"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Mark as Offensive"
msgstr "모욕적인 게시물로 표시"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as offensive"
msgstr "모욕 게시물로 표시"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Mark as spam"
msgstr "스팸으로 표시"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid "Meet our community members"
msgstr "커뮤니티 회원 만나보기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error
msgid "Message Delivery error"
msgstr "메시지 전송 오류"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_ids
msgid "Messages"
msgstr "메시지"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__mode
msgid "Mode"
msgstr "모드"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_moderate
msgid "Moderate posts"
msgstr "중재 게시물"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Moderation tools"
msgstr "중재 도구"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "More over:"
msgstr "추가 :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Most Used Tags"
msgstr "가장 많이 사용된 태그"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__vote_count_desc
msgid "Most Voted"
msgstr "최고 득표"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Most used Tags"
msgstr "가장 많이 사용된 태그"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_most_used_ids
msgid "Most used tags"
msgstr "가장 많이 사용된 태그"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
msgid "My Favorites"
msgstr "내 즐겨찾기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "My Posts"
msgstr "내 게시물"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__user_vote
msgid "My Vote"
msgstr "나의 투표"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "My forums"
msgstr "내 게시판"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "My profile"
msgstr "개인 프로필"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__name
msgid "Name"
msgstr "이름"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Negative vote"
msgstr "부정적인 투표"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_answer_new
#: model:mail.message.subtype,name:website_forum.mt_forum_answer_new
msgid "New Answer"
msgstr "새로운 답변"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_forum_action_add
msgid "New Forum"
msgstr "새 게시판"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "New Post"
msgstr "새로운 게시물"

#. module: website_forum
#: model:mail.message.subtype,description:website_forum.mt_question_new
#: model:mail.message.subtype,name:website_forum.mt_forum_question_new
#: model:mail.message.subtype,name:website_forum.mt_question_new
msgid "New Question"
msgstr "새로운 질문"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__create_date_desc
msgid "Newest"
msgstr "최신"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_2
#: model:gamification.challenge,name:website_forum.challenge_nice_answer
msgid "Nice Answer"
msgstr "좋은 답변"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_answer
msgid "Nice Answer (4)"
msgstr "좋은 답변(4)"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_8
#: model:gamification.challenge,name:website_forum.challenge_nice_question
msgid "Nice Question"
msgstr "좋은 질문"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all
msgid "No forum is available yet."
msgstr "사용가능한 게시판이 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "No posts yet"
msgstr "아직 게시물이 없습니다"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_dofollow
msgid "Nofollow links"
msgstr "링크 팔로우 없음"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_4
msgid "Not a real post"
msgstr "실제 게시물이 아님"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_6
msgid "Not relevant or out dated"
msgstr "관련성이 없거나 오래됨"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_2
#: model:gamification.challenge,name:website_forum.challenge_notable_question
msgid "Notable Question"
msgstr "주목할만한 질문"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of Actions"
msgstr "작업 수"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__posts_count
msgid "Number of Posts"
msgstr "게시물 수"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of errors"
msgstr "오류 횟수"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_flagged_posts
msgid "Number of flagged posts"
msgstr "신고한 게시물 수"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_needaction_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "조치가 필요한 메시지 수"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_post__message_has_error_counter
#: model:ir.model.fields,help:website_forum.field_forum_tag__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "전송 오류가 발생한 메시지 수입니다."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__count_posts_waiting_validation
msgid "Number of posts waiting for validation"
msgstr "승인 대기 중인 게시물 수"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_2
msgid "Off-topic or not relevant"
msgstr "주제를 벗어났거나 관련성이 없음"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__offensive
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post_reason__reason_type__offensive
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive"
msgstr "모욕"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Offensive Post"
msgstr "모욕 게시물"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Offensive Posts"
msgstr "불쾌감을 주는 게시물"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Oh no! Please <a href=\"%s\">sign in</a> to perform this action"
msgstr "안됩니다! 이 작업을 수행하려면 먼저 <a href=\"%s\">로그인</a>해 주세요."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "On average,"
msgstr "평균적."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Oops!"
msgstr "이런!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Options"
msgstr "옵션"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Order and Visibility"
msgstr "주문 및 표시"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_23
#: model:gamification.challenge,name:website_forum.challenge_peer_pressure
#: model:gamification.goal.definition,name:website_forum.definition_peer_pressure
msgid "Peer Pressure"
msgstr "집단 압박"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__plain_content
msgid "Plain Content"
msgstr "일반 내용"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid ""
"Please wait for a moderator to validate your previous post before "
"continuing."
msgstr "계속하기 전에 중재자가 이전 게시물을 승인할 때까지 기다리십시오."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_1
#: model:gamification.challenge,name:website_forum.challenge_popular_question
msgid "Popular Question"
msgstr "인기있는 질문"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_popular_question
msgid "Popular Question (150)"
msgstr "인기있는 질문(150)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_notable_question
msgid "Popular Question (250)"
msgstr "인기있는 질문(250)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_famous_question
msgid "Popular Question (500)"
msgstr "인기있는 질문(500)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "Positive vote"
msgstr "긍정적인 투표"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__post_id
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Post"
msgstr "발행"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Post Answer"
msgstr "답변 게시하기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__child_ids
msgid "Post Answers"
msgstr "답변 게시하기"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_reason_action
msgid "Post Close Reason"
msgstr "게시물 마감 사유"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_reason
msgid "Post Closing Reason"
msgstr "게시 마감 사유"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.snippet_options
msgid "Post Count"
msgstr "게시물 수"

#. module: website_forum
#: model:ir.model,name:website_forum.model_forum_post_vote
msgid "Post Vote"
msgstr "게시물 투표"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Post Your Question"
msgstr "질문 게시하기"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Post is closed and marked as offensive content"
msgstr "게시물이 폐쇄되고 불쾌감을 주는 콘텐츠로 표시됩니다"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Post is closed and marked as spam"
msgstr "게시물이 폐쇄되고 스팸으로 표시됩니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Post:"
msgstr "게시 :"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_2
msgid "Posted 10 comments"
msgstr "10개의 댓글이 등록됨"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_p_4
msgid "Posted 100 comments"
msgstr "100개의 댓글이 등록됨"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Posting answer on a [Deleted] or [Closed] question is not possible."
msgstr "[삭제] 또는 [마감]된 질문에는 답변을 등록할 수 없습니다."

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action_forum_main
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__post_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__post_ids
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Posts"
msgstr "게시물"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__privacy
msgid "Privacy"
msgstr "비공개"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__public
msgid "Public"
msgstr "공개"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__privacy
msgid ""
"Public: Forum is public\n"
"Signed In: Forum is visible for signed in users\n"
"Some users: Forum and their content are hidden for non members of selected group"
msgstr ""
"공개: 게시판이 전체 공개됩니다\n"
"로그인 됨: 로그인한 사용자에게만 게시판이 공개됩니다.\n"
"일부 사용자: 선택한 그룹의 구성원이\n"
" 아닌 사용자에게는 게시판 및 해당 콘텐츠가 숨겨집니다."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_25
#: model:gamification.challenge,name:website_forum.challenge_pundit
#: model:gamification.goal.definition,name:website_forum.definition_pundit
msgid "Pundit"
msgstr "전문가"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Put your answer here."
msgstr "여기에 답을 넣으십시오."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/tours/website_forum.js:0
msgid "Put your question here."
msgstr "여기에 질문을 넣으십시오."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__parent_id
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Question"
msgstr "질문"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "Question %s"
msgstr "질문 %s"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
#: model:mail.message.subtype,description:website_forum.mt_question_edit
#: model:mail.message.subtype,name:website_forum.mt_question_edit
msgid "Question Edited"
msgstr "질문 편집됨"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_downvote
msgid "Question downvoted"
msgstr "질문에 반대투표됨"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Question not found!"
msgstr "질문을 찾을 수 없습니다!"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_4
msgid "Question set as favorite by 1 user"
msgstr "1 명이 즐겨찾기로 설정한 질문"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_6
msgid "Question set as favorite by 25 users"
msgstr "25명이 즐겨찾기로 설정한 질문"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_5
msgid "Question set as favorite by 5 users"
msgstr "5명이 즐겨찾기로 설정한 질문"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Question should not be empty."
msgstr "질문 내용은 비워둘 수 없습니다."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_gen_question_upvote
msgid "Question upvoted"
msgstr "질문에 찬성투표됨"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_10
msgid "Question voted up 15 times"
msgstr "15표 이상 찬성투표된 질문"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_8
msgid "Question voted up 4 times"
msgstr "4표 이상 찬성투표된 질문"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_q_9
msgid "Question voted up 6 times"
msgstr "6표 이상 찬성투표된 질문"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Question:"
msgstr "질문:"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Questions"
msgstr "질문"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__mode__questions
msgid "Questions (1 answer)"
msgstr "질문 (답변 1)"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__mode
msgid ""
"Questions mode: only one answer allowed\n"
" Discussions mode: multiple answers allowed"
msgstr ""
"질문 모드 : 하나의 답변만 허용 \n"
"토론 모드 : 여러 답변 허용"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_15
msgid "Racist and hate speech"
msgstr "인종 차별 및 혐오 표현"

#. module: website_forum
#: model:ir.ui.menu,name:website_forum.menu_forum_rank_global
msgid "Ranks"
msgstr "등급"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__rating_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__rating_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__rating_ids
msgid "Ratings"
msgstr "평가"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Re: %s"
msgstr "회신 : %s"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Read: #{question.name}"
msgstr "읽기 : #{question.name}"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__closed_reason_id
msgid "Reason"
msgstr "사유"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_reason__reason_type
msgid "Reason Type"
msgstr "사유 유형"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.mark_as_offensive
msgid "Reason:"
msgstr "사유 :"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_reason_view_list
msgid "Reasons"
msgstr "사유"

#. module: website_forum
#: model_terms:gamification.badge,description:website_forum.badge_a_1
msgid "Received at least 3 upvote for an answer for the first time"
msgstr "처음 답변을 위해 최소 3개의 찬성투표를 받았습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Refuse"
msgstr "반려"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
msgid "Related Posts"
msgstr "관련 게시물"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__relevancy
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__default_order__relevancy_desc
msgid "Relevance"
msgstr "관련성"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "Relevance Computation"
msgstr "관련성 계산"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Remove validated answer"
msgstr "승인된 답변 삭제"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Reopen"
msgstr "다시 열기"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Reopen a banned question"
msgstr "차단된 질문 다시 열기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Replies"
msgstr "답글"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Reply"
msgstr "회신"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Reply should not be empty."
msgstr "답장 내용은 비워둘 수 없습니다."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__self_reply
msgid "Reply to own question"
msgstr "자신의 질문에 대한 답변"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,help:website_forum.field_forum_post__website_id
msgid "Restrict to a specific website."
msgstr "특정 웹사이트로만 제한."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.profile_access_denied
msgid "Return to the forum"
msgstr "게시판으로 돌아가기"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__moderator_id
msgid "Reviewed by"
msgstr "검토자"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_post__is_seo_optimized
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__is_seo_optimized
msgid "SEO optimized"
msgstr "SEO 최적화"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_post__message_has_sms_error
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS 전송 에러"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Save Changes"
msgstr "변경 사항 저장"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_26
#: model:gamification.challenge,name:website_forum.challenge_scholar
#: model:gamification.goal.definition,name:website_forum.definition_scholar
msgid "Scholar"
msgstr "학자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
msgid "Search in Post"
msgstr "게시물 검색"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Search..."
msgstr "검색..."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__relevancy_time_decay
msgid "Second Relevance Parameter"
msgstr "두 번째 관련성 매개 변수"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/res_users.py:0
msgid "See our Forum"
msgstr "게시판 보기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "See post"
msgstr "게시물 보기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "See question"
msgstr "질문 보기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Select All"
msgstr "전체 선택"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_8
#: model:gamification.challenge,name:website_forum.challenge_self_learner
#: model:gamification.goal.definition,name:website_forum.definition_self_learner
msgid "Self-Learner"
msgstr "자가 학습자"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_post__seo_name
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__seo_name
msgid "Seo name"
msgstr "Seo 이름"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__sequence
msgid "Sequence"
msgstr "순서"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid ""
"Share and discuss the best content and new marketing ideas, build your "
"professional profile and become a better marketer together."
msgstr ""
"최고의 콘텐츠와 새로운 마케팅 아이디어를 공유하고 토론하며 전문적인 프로필을 쌓아 더 나은 마케터가 될 수 있도록 함께 노력할 수 "
"있습니다."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"Share this content to increase your chances to be featured on the front page"
" and attract more visitors."
msgstr "이 콘텐츠를 공유하여 첫 페이지에 게재될 기회를 늘리고 더 많은 방문자를 유치하십시오."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__allow_share
msgid "Sharing Options"
msgstr "공유 옵션"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "Show Tags Starting with"
msgstr "다음으로 시작하는 태그 보기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Showing results for"
msgstr "다음에 대한 결과 표시 중"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.forum_sign_up_cta
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "Sign up"
msgstr "가입"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__connected
msgid "Signed In"
msgstr "로그인함"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Solved"
msgstr "해결 완료"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_forum__privacy__private
msgid "Some users"
msgstr "일부 사용자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.404
msgid "Sorry, this question is not available anymore."
msgstr "죄송합니다, 이 질문은 더이상 사용할 수 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>solved</b> results"
msgstr "죄송합니다, <b>해결</b> 결과를 찾을 수 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>unanswered</b> results"
msgstr "죄송합니다, <b>미답변</b> 결과를 찾을 수 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any <b>unsolved</b> results"
msgstr "죄송합니다, <b>미해결</b> 결과를 찾을 수 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Sorry, we could not find any results"
msgstr "죄송합니다, 내용을 찾을 수 없습니다 "

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Sorry, you cannot select your own posts as best answer"
msgstr "자신의 게시물은 베스트 글로 추천할 수 없습니다."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "Sorry, you cannot vote for your own posts"
msgstr "죄송합니다. 자신의 게시물에 투표할 수 없습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
msgid "Spam all post"
msgstr "스팸 전체 게시물"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_8
msgid "Spam or advertising"
msgstr "스팸 또는 광고"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "Start by creating a post"
msgstr "먼저 게시물을 만들어 보세요"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__state
msgid "Status"
msgstr "상태"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_6
#: model:gamification.challenge,name:website_forum.challenge_stellar_question_25
msgid "Stellar Question"
msgstr "주요한 질문"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_q_7
#: model:gamification.challenge,name:website_forum.challenge_student
msgid "Student"
msgstr "학생"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Subscribe"
msgstr "구독"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_31
#: model:gamification.challenge,name:website_forum.challenge_supporter
#: model:gamification.goal.definition,name:website_forum.definition_supporter
msgid "Supporter"
msgstr "후원자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_search
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_form
msgid "Tag"
msgstr "태그"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_tag_name_uniq
msgid "Tag name already exists!"
msgstr "태그명이 이미 존재합니다!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__tag_ids
#: model:ir.ui.menu,name:website_forum.menu_forum_tag_global
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
#: model_terms:ir.ui.view,arch_db:website_forum.forum_tag_view_list
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "Tags"
msgstr "태그"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_oe_structure_forum_all_top
msgid ""
"Tap into the collective knowledge of our community by asking your questions "
"in our forums,<br/> where helpful members are ready to assist you."
msgstr "도움이 필요한 회원들은 게시판에<br/> 질문을 남겨 커뮤니티 구성원의 집단지성을 활용할 수 있습니다."

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_32
#: model:gamification.challenge,name:website_forum.challenge_taxonomist
#: model:gamification.goal.definition,name:website_forum.definition_taxonomist
msgid "Taxonomist"
msgstr "분류가"

#. module: website_forum
#: model:gamification.badge,name:website_forum.badge_a_1
#: model:gamification.challenge,name:website_forum.challenge_teacher
#: model:gamification.goal.definition,name:website_forum.definition_teacher
msgid "Teacher"
msgstr "교사"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__teaser
msgid "Teaser"
msgstr "티저"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "Thanks for posting!"
msgstr "게시해 주셔서 감사합니다!"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "The accepted answer is deleted"
msgstr "채택된 답변 삭제됨"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"The goal of this site is create a relevant knowledge base that would answer "
"questions related to Odoo."
msgstr "이 사이트의 목표는 Odoo와 관련된 질문에 답변 할 수 있는 관련 기술 자료를 만드는 것입니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "The question has been closed"
msgstr "질문이 마감되었습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "There are no answers yet"
msgstr "아직 답변이 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags being used in this forum."
msgstr "이 게시판에서 사용되는 태그가 아직 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags matching the selected filter"
msgstr "선택한 필터와 일치하는 태그가 없습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "There are no tags matching the selected search."
msgstr "선택한 검색어와 일치하는 태그가 없습니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid ""
"Therefore questions and answers can be edited like wiki pages by experienced"
" users of this site in order to improve the overall quality of the knowledge"
" base content. Such privileges are granted based on user karma level: you "
"will be able to do the same once your karma gets high enough."
msgstr ""
"따라서 본 사이트의 숙련된 사용자가 질문과 답변을 위키 페이지처럼 편집하여 전반적인 기술 자료 내용을 개선할 수 있습니다. 이러한 권한은"
" 사용자의 명성 수준에 따라 부여됩니다. 명성이 충분히 높아지면 동일한 작업을 수행할 수 있습니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"This community is for professional and enthusiast users, partners and "
"programmers. You can ask questions about:"
msgstr ""
"이 커뮤니티는 전문적이며 열정적인 사용자들과 협력사, 그리고 프로그래머들을 위해 마련되었습니다. 다음 내용에 대해 질문이 가능합니다:"

#. module: website_forum
#: model:forum.forum,description:website_forum.forum_help
msgid ""
"This community is for professionals and enthusiasts of our products and "
"services. Share and discuss the best content and new marketing ideas, build "
"your professional profile and become a better marketer together."
msgstr ""
"이 커뮤니티는 NAT 제품 및 서비스의 전문가와 매니아를 위한 커뮤니티입니다. 최고의 컨텐츠와 새로운 마케팅 아이디어를 공유하고 업무 "
"협의하고, 프로페셔널 프로필을 구축하고, 함께 더 나은 마케팅 전문가가 될 수 있습니다."

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__relevancy_post_vote
msgid ""
"This formula is used in order to sort by relevance. The variable 'votes' "
"represents number of votes for a post, and 'days' is number of days since "
"the post creation"
msgstr ""
"이 공식은 관련성을 기준으로 정렬하기 위해 사용됩니다. 변수 'votes'는 게시물의 투표 수를 나타내며, 'days'는 게시물 작성 후"
" 지난 일 수입니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "This forum has been archived."
msgstr "이 게시판은 보존 처리되었습니다."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "This post can not be flagged"
msgstr "이 게시물은 신고할 수 없습니다"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "This post is already flagged"
msgstr "이 게시물은 이미 신고되었습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "This post is awaiting validation"
msgstr "승인 대기 중인 게시물입니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid ""
"This post is currently awaiting moderation and is not published yet.<br/>\n"
"                    As a moderator you can either <b>Accept</b> or <b>Reject</b> this post."
msgstr ""
"이 게시물은 현재 검토 대기 중이며 아직 게시되지 않았습니다.<br/>\n"
"                    이 게시물을 <b>승인</b>하거나 <b>거부</b>할 수 있습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "This question has been flagged"
msgstr "신고된 질문입니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "This user hasn't answered any questions yet. <br/>"
msgstr "이 사용자는 아직 질문에 답변하지 않았습니다. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "This user hasn't posted any questions yet.<br/>"
msgstr "이 사용자는 아직 질문을 게시하지 않았습니다.<br/>"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_14
msgid "Threatening language"
msgstr "위협적인 언어"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__name
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title"
msgstr "제목"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Title must not be empty"
msgstr "제목은 비워둘 수 없습니다"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/controllers/website_forum.py:0
msgid "Title should not be empty."
msgstr "제목은 비워둘 수 없습니다."

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__recipient_id
msgid "To"
msgstr "종료 시간"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_body
msgid "To Validate"
msgstr "승인하기"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"To prevent your question from being flagged and possibly removed, avoid "
"asking subjective questions where …"
msgstr "질문이 신고되거나 제거되지 않도록 하려면 여기에선 주관적인 질문을 하지 마십시오..."

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_9
msgid "Too localized"
msgstr "너무 현지화되어 있음"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_3
msgid "Too subjective and argumentative"
msgstr "지나치게 주관적이고 논쟁적"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "Toolbar with button groups"
msgstr "버튼 그룹을 가진 툴바"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "Total Answers"
msgstr "모든 답변"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
msgid "Total Posts"
msgstr "모든 게시물"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_tree
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_tree
msgid "Total Views"
msgstr "총 조회수"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_count
msgid "Total Votes"
msgstr "전체 투표수"

#. module: website_forum
#: model:ir.model,name:website_forum.model_gamification_karma_tracking
msgid "Track Karma Changes"
msgstr "카르마 변경 사항 추적"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Twitter"
msgstr "트위터"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unanswered"
msgstr "미답변"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
msgid "Undelete"
msgstr "삭제 취소"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "Unfollow"
msgstr "팔로우 취소"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "Unmark as Best Answer"
msgstr "최고의 답변으로 표시 해제"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unsolved"
msgstr "미해결"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "Unused Tags"
msgstr "사용되지 않은 태그"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__tag_unused_ids
msgid "Unused tags"
msgstr "사용되지 않은 태그"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_uid
msgid "Updated by"
msgstr "갱신자"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__write_date
msgid "Updated on"
msgstr "업데이트 일자"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__karma_upvote
#: model_terms:ir.ui.view,arch_db:website_forum.vote
msgid "Upvote"
msgstr "찬성투표"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_student
msgid "Upvoted question (1)"
msgstr "질문에 찬성투표(1)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_great_question
msgid "Upvoted question (15)"
msgstr "질문에 찬성투표(15)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_nice_question
msgid "Upvoted question (4)"
msgstr "질문에 찬성투표(4)"

#. module: website_forum
#: model:gamification.goal.definition,name:website_forum.definition_good_question
msgid "Upvoted question (6)"
msgstr "질문에 찬성투표(6)"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "Use a clear, explicit and concise title"
msgstr "명확하고 간결한 제목을 사용하세요"

#. module: website_forum
#: model:ir.model,name:website_forum.model_res_users
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__user_id
msgid "User"
msgstr "사용자"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "User answer accepted"
msgstr "사용자 답변으로 채택됨"

#. module: website_forum
#: model:ir.actions.act_window,name:website_forum.forum_post_action_favorites
msgid "Users favorite posts"
msgstr "사용자가 즐겨찾는 게시물"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.moderation_queue
#: model_terms:ir.ui.view,arch_db:website_forum.post_dropdown
#: model_terms:ir.ui.view,arch_db:website_forum.show_flag_validator
msgid "Validate"
msgstr "승인"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "Validate an answer"
msgstr "답변 승인하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid "Validate question"
msgstr "질문 검증"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "View"
msgstr "화면"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "View my answer <i class=\"oi oi-arrow-right ms-1\"/>"
msgstr "내 답변 보기 <i class=\"oi oi-arrow-right ms-1\"/>"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__views
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.forum_related_posts
#: model_terms:ir.ui.view,arch_db:website_forum.post_stats
msgid "Views"
msgstr "화면"

#. module: website_forum
#: model:forum.post.reason,name:website_forum.reason_12
msgid "Violent language"
msgstr "폭력적인 언어"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post_vote__vote
msgid "Vote"
msgstr "투표"

#. module: website_forum
#: model:ir.model.constraint,message:website_forum.constraint_forum_post_vote_vote_uniq
msgid "Vote already exists!"
msgstr "투표가 이미 존재합니다!"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__vote_ids
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "Votes"
msgstr "투표"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__forum_post__state__pending
msgid "Waiting Validation"
msgstr "승인 대기 중"

#. module: website_forum
#: model:ir.model,name:website_forum.model_website
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_id
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_id
msgid "Website"
msgstr "웹사이트"

#. module: website_forum
#: model:ir.model.fields.selection,name:website_forum.selection__gamification_challenge__challenge_category__forum
msgid "Website / Forum"
msgstr "웹사이트 / 포럼"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_message_ids
msgid "Website Messages"
msgstr "웹사이트 메시지"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_url
msgid "Website URL"
msgstr "웹 사이트 URL"

#. module: website_forum
#: model:ir.model.fields,help:website_forum.field_forum_forum__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_post__website_message_ids
#: model:ir.model.fields,help:website_forum.field_forum_tag__website_message_ids
msgid "Website communication history"
msgstr "웹사이트 대화 이력"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_description
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_description
msgid "Website meta description"
msgstr "웹사이트 메타 설명"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_keywords
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_keywords
msgid "Website meta keywords"
msgstr "웹사이트 메타 핵심어"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_title
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_title
msgid "Website meta title"
msgstr "웹사이트 메타 제목"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_post__website_meta_og_img
#: model:ir.model.fields,field_description:website_forum.field_forum_tag__website_meta_og_img
msgid "Website opengraph image"
msgstr "웹사이트 오픈그래프 이미지"

#. module: website_forum
#: model:ir.model.fields,field_description:website_forum.field_forum_forum__welcome_message
msgid "Welcome Message"
msgstr "환영 메시지"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_forum.py:0
#: model_terms:forum.forum,welcome_message:website_forum.forum_help
msgid "Welcome!"
msgstr "환영합니다!"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"When a question or answer is upvoted, the user who posted them will gain "
"some points, which are called \"karma points\". These points serve as a "
"rough measure of the community trust to him/her. Various moderation tasks "
"are gradually assigned to the users based on those points."
msgstr ""
"질문이나 답변이 상향 조정되면 이를 게시한 사용자가 어느 정도 점수를 얻게 되는데, 이를 \"명성 포인트\"라고 합니다. 이러한 점들은 "
"그/그녀에 대한 지역사회의 신뢰의 대략적인 척도로 작용합니다. 이러한 점에 따라 다양한 조정 작업이 점차 사용자에게 할당됩니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.new_question
msgid "Write a clear, explicit and concise title"
msgstr "명확하고 간결한 제목을 사용하세요"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_sidebar_header
msgid "XP"
msgstr "XP"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "You already have a pending post"
msgstr "이미 대기 중인 게시물이 있습니다"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "You can share your question once it has been validated"
msgstr "질문이 승인되면 공유할 수 있습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "You can't vote for your own post"
msgstr "자신의 게시물에 투표할 수 없습니다"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post.py:0
msgid "You cannot create recursive forum posts."
msgstr "재귀적 게시판 게시물을 만들 수 없습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "You cannot post an empty answer"
msgstr "빈 답변은 게시할 수 없습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_display
msgid "You don't have enough karma"
msgstr "투표를 위한 카르마가 충분하지 않습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "You have a pending post"
msgstr "대기 중인 게시물이 있습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "You have not answered any questions yet. <br/>"
msgstr "아직 질문에 답변하지 않으셨습니다. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_profile_content
msgid "You have not posted any questions yet. <br/>"
msgstr "아직 질문을 게시하지 않으셨습니다. <br/>"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.user_votes
msgid "You haven't given any votes yet."
msgstr "아직 투표하지 않으셨습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "You may now participate in our forums."
msgstr "이제 게시판에 참여하실 수 있습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "You need to have sufficient karma to edit tags"
msgstr "태그를 편집하려면 충분한 명성이 있어야 합니다."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"You should only ask practical, answerable questions based on actual problems"
" that you face. Chatty, open-ended questions diminish the usefulness of this"
" site and push other questions off the front page."
msgstr ""
"여러분은 여러분이 직면하고 있는 실제 문제들에 근거해서만 실용적이고 대답 가능한 질문을 해야 합니다. 수다스럽고 열린 질문은 이 사이트의"
" 유용성을 떨어뜨리고 다른 질문들을 첫페이지에서 밀어냅니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "You're following this post"
msgstr "이 게시물을 팔로우 중입니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "You've Completely Caught&amp;nbsp;Up!"
msgstr "최신 정보를 확인했습니다!"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
#: model_terms:ir.ui.view,arch_db:website_forum.post_answer
msgid "Your Answer"
msgstr "귀하의 답변"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.display_post_question_block
msgid "Your favourite"
msgstr "귀하의 즐겨찾기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "accept any answer"
msgstr "아무 답변이나 채택하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.header
msgid "and join this Forum"
msgstr "또한 이 게시판에 참여합니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "and search"
msgstr "그리고 검색"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
msgid "at"
msgstr "at"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_model_nav
msgid "breadcrumb"
msgstr "사이트 이동 경로"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.edit_post
msgid "by"
msgstr "작성자"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "close any posts"
msgstr "모든 게시물 마감하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any comment"
msgstr "모든 댓글 삭제하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete any question or answer"
msgstr "모든 질문 또는 답변 삭제하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "delete own comment"
msgstr "자신의 댓글 삭제하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "downvote"
msgstr "반대투표"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "downvoted"
msgstr "반대투표"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form
msgid "e.g. Help"
msgstr "예. 도움말"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_forum_view_form_add
msgid "e.g. Technical Assistance"
msgstr "예. 기술 지원"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_view_form
msgid "e.g. When should I plant my tomatoes?"
msgstr "예. 토마토는 언제 심어야 하나요?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "edit any post, view offensive flags"
msgstr "게시물 수정, 모욕 게시물 신고 보기"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "every answer is equally valid: “What’s your favorite ______?”"
msgstr "모든 대답은 똑같이 유효합니다 : “가장 좋아하는 ______?”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "flag offensive, close own questions"
msgstr "모욕 게시물 신고. 자신의 질문 마감하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "for reason:"
msgstr "이유 : "

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
msgid ""
"has been posted and require your validation. Click here to access the "
"question :"
msgstr "게시되었으며 승인 검사가 필요합니다. 다음 질문에 접근하려면 여기를 클릭하세요."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_answer
msgid "has been posted. Click here to access the post :"
msgstr "게시되었습니다. 게시물에 접근하려면 여기를 클릭하세요."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
msgid "has been posted. Click here to access the question :"
msgstr "게시되었습니다. 질문에 접근하려면 여기를 클릭하세요 :"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "here"
msgstr "여기"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to configure or customize Odoo to specific business needs,"
msgstr "특정 비즈니스 요구에 맞게 Odoo를 구성하거나 사용자 정의하는 방법"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to develop modules for your own need,"
msgstr "자신의 필요에 맞는 모듈을 개발하는 방법"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "how to install Odoo on a specific infrastructure,"
msgstr "특정 인프라에 Odoo를 설치하는 방법"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"if your\n"
"        answer is selected as the right one. See what you can do with karma"
msgstr ""
"귀하의 답변이 \n"
"         올바른 답변으로 선택된 경우. 명성으로 할 수 있는 일 보기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "in your favourites"
msgstr "귀하의 즐겨찾기에서"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "in your posts"
msgstr "게시물"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "insert text link, upload files"
msgstr "텍스트 링크 삽입. 파일 업로드"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "instead."
msgstr "대신."

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "it is a rant disguised as a question: “______ sucks, am I right?”"
msgstr "그것은 질문으로 위장한 소리입니다 : “______ 정말 형편없죠?”"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/js/website_forum.js:0
msgid "karma is required to perform this action. "
msgstr "이 작업을 수행하려면 명성이 필요합니다."

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid "karma points"
msgstr "명성 포인트"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "matching \""
msgstr "일치 작업 \""

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no changes"
msgstr "변경 사항 없음"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no more downvoted"
msgstr "더 이상 반대투표가 없습니다"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "no more upvoted"
msgstr "더 이상 찬성투표가 없습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_new_question
#: model_terms:ir.ui.view,arch_db:website_forum.forum_post_template_validation
#: model_terms:ir.ui.view,arch_db:website_forum.post_description_full
msgid "on"
msgstr "있음"

#. module: website_forum
#. odoo-javascript
#: code:addons/website_forum/static/src/xml/public_templates.xml:0
msgid ""
"on social networks get an answer within\n"
"        5 hours. Questions shared on two social networks have"
msgstr ""
"5시간 안에 답을 얻을 수 있습니다.\n"
"          두 소셜 네트워크에서 공유되는 질문은 다음과 같습니다."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
msgid "post"
msgstr "게시하기"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_all_all_entries
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "posts"
msgstr "게시물"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "specific questions about Odoo service offers, etc."
msgstr "Odoo 서비스 제공 등에 대한 특정 질문 등."

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "tag"
msgstr "태그"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.forum_index_tags
msgid "tags"
msgstr "태그"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"there is no actual problem to be solved: “I’m curious if other people feel "
"like I do.”"
msgstr "실제로 해결해야 할 문제는 없습니다 : “다른 사람들이 저처럼 느끼는 지 궁금해요.”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "upvote, add comments"
msgstr "찬성투표. 댓글 추가"

#. module: website_forum
#. odoo-python
#: code:addons/website_forum/models/forum_post_vote.py:0
msgid "upvoted"
msgstr "찬성투표"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.no_results_message
msgid "using the"
msgstr "사용"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"we are being asked an open-ended, hypothetical question: “What if ______ "
"happened?”"
msgstr "우리는 개방형의 가상의 질문을 받습니다 : “______가 발생하면 어떻게 됩니까?”"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid "what's the best way to use Odoo for a specific business need,"
msgstr "특정 비즈니스 요구에 Odoo를 사용하는 가장 좋은 방법은 무엇입니까?"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.author_box
msgid "xp"
msgstr "xp"

#. module: website_forum
#: model_terms:forum.forum,faq:website_forum.forum_help
#: model_terms:ir.ui.view,arch_db:website_forum.default_faq
msgid ""
"your answer is provided along with the question, and you expect more "
"answers: “I use ______ for ______, what do you use?”"
msgstr ""
"귀하의 답변은 질문과 함께 제공되며 더 많은 답변을 기대합니다. : “______에는 ______를 사용합니다. 무엇을 사용하십니까?”"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.faq_karma
msgid "your biography can be seen as tooltip"
msgstr "툴팁으로 귀하의 이력을 볼 수 있습니다"

#. module: website_forum
#: model_terms:ir.ui.view,arch_db:website_forum.follow
msgid "your email..."
msgstr "귀하의 이메일..."
