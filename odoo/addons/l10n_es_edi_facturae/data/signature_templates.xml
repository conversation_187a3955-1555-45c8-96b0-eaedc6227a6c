<odoo>
    <data>
        <template id="template_xades_signature">
            <ds:Signature t-att-Id="signature_id" xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
                <ds:SignedInfo>
                    <ds:CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>
                    <ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"/>
                    <ds:Reference t-att-Id="reference_uri" Type="http://www.w3.org/2000/09/xmldsig#Object" URI="">
                        <ds:Transforms>
                            <ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
                        </ds:Transforms>
                        <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                        <ds:DigestValue></ds:DigestValue>
                    </ds:Reference>
                    <ds:Reference Type="http://uri.etsi.org/01903#SignedProperties" t-attf-URI="##{sigproperties_id}">
                        <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                        <ds:DigestValue></ds:DigestValue>
                    </ds:Reference>
                    <ds:Reference t-attf-URI="##{keyinfo_id}">
                        <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                        <ds:DigestValue></ds:DigestValue>
                    </ds:Reference>
                </ds:SignedInfo>
                <ds:SignatureValue></ds:SignatureValue>
                <ds:KeyInfo t-att-Id="keyinfo_id">
                    <ds:X509Data>
                        <ds:X509Certificate t-out="x509_certificate"/>
                    </ds:X509Data>
                    <ds:KeyValue>
                        <ds:RSAKeyValue>
                            <ds:Modulus t-out="public_modulus"/>
                            <ds:Exponent t-out="public_exponent"/>
                        </ds:RSAKeyValue>
                    </ds:KeyValue>
                </ds:KeyInfo>
                <ds:Object>
                    <xades:QualifyingProperties xmlns:xades="http://uri.etsi.org/01903/v1.3.2#" t-attf-Target="##{signature_id}">
                        <xades:SignedProperties t-att-Id="sigproperties_id">
                            <xades:SignedSignatureProperties>
                                <xades:SigningTime t-out="iso_now"/>
                                <xades:SigningCertificate>
                                    <xades:Cert>
                                        <xades:CertDigest>
                                            <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                                            <ds:DigestValue t-out="sigcertif_digest"/>
                                        </xades:CertDigest>
                                        <xades:IssuerSerial>
                                            <ds:X509IssuerName t-out="x509_issuer_description"/>
                                            <ds:X509SerialNumber t-out="x509_serial_number"/>
                                        </xades:IssuerSerial>
                                    </xades:Cert>
                                </xades:SigningCertificate>
                                <xades:SignaturePolicyIdentifier>
                                    <xades:SignaturePolicyId>
                                        <xades:SigPolicyId>
                                            <xades:Identifier t-out="sigpolicy_url"/>
                                            <xades:Description t-out="sigpolicy_description"/>
                                        </xades:SigPolicyId>
                                        <xades:SigPolicyHash>
                                            <ds:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>
                                            <ds:DigestValue>Ohixl6upD6av8N7pEvDABhEL6hM=</ds:DigestValue>
                                        </xades:SigPolicyHash>
                                        <xades:SigPolicyQualifiers>
                                            <xades:SigPolicyQualifier>
                                                <xades:SPURI t-out="sigpolicy_url"/>
                                            </xades:SigPolicyQualifier>
                                        </xades:SigPolicyQualifiers>
                                    </xades:SignaturePolicyId>
                                </xades:SignaturePolicyIdentifier>
                            </xades:SignedSignatureProperties>
                            <xades:SignedDataObjectProperties>
                                <xades:DataObjectFormat t-attf-ObjectReference="##{reference_uri}">
                                    <xades:Description/>
                                    <xades:ObjectIdentifier>
                                        <!-- Identifier below is Uniform Resource Number for MimeType "text/xml" (no specific version) -->
                                        <!-- 1.2.840.10003.5: see http://www.oid-info.com/cgi-bin/display?oid=1.2.840.10003.5&a=display -->
                                        <!-- 109.10: see https://www.loc.gov/z3950/agency/defns/oids.html#5 -->
                                        <xades:Identifier Qualifier="OIDAsURN">urn:oid:1.2.840.10003.5.109.10</xades:Identifier>
                                        <xades:Description/>
                                    </xades:ObjectIdentifier>
                                    <xades:MimeType>text/xml</xades:MimeType>
                                    <xades:Encoding/>
                                </xades:DataObjectFormat>
                            </xades:SignedDataObjectProperties>
                        </xades:SignedProperties>
                    </xades:QualifyingProperties>
                </ds:Object>
            </ds:Signature>
        </template>
    </data>
</odoo>
