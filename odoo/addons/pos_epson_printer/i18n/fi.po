# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* pos_epson_printer
# 
# Translators:
# <PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <ossi.manty<PERSON><EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Finnish (https://app.transifex.com/odoo/teams/41243/fi/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fi\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Cashdrawer"
msgstr "Kassalipas"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid ""
"Check on the printer configuration for the 'Device ID' setting. It should be"
" set to: "
msgstr ""
"Tarkista tulostimen kokoonpanosta 'Device ID' -asetus. Sen pitäisi olla "
"asetettu seuraavaan arvoon: "

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_res_config_settings
msgid "Config Settings"
msgstr "Asetukset"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_config__epson_printer_ip
msgid "Epson Printer IP"
msgstr "Epson Tulostin IP"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Epson Printer IP Address"
msgstr "Epsonin tulostimen IP-osoite"

#. module: pos_epson_printer
#. odoo-python
#: code:addons/pos_epson_printer/models/pos_printer.py:0
msgid "Epson Printer IP Address cannot be empty."
msgstr "Epsonin tulostimen IP-osoite ei voi olla tyhjä."

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid "Epson Receipt Printer IP Address"
msgstr "Epson kuittitulostimen IP-osoite"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid ""
"If you are on a secure server (HTTPS) please make sure you manually accepted"
" the certificate by accessing %s. "
msgstr ""
"Jos olet suojatulla palvelimella (HTTPS), varmista, että olet hyväksynyt "
"varmenteen manuaalisesti painamalla %s."

#. module: pos_epson_printer
#: model:ir.model.fields,help:pos_epson_printer.field_pos_config__epson_printer_ip
#: model:ir.model.fields,help:pos_epson_printer.field_pos_printer__epson_printer_ip
msgid "Local IP address of an Epson receipt printer."
msgstr "Epsonin kuittitulostimen paikallinen IP-osoite."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "No paper was detected by the printer"
msgstr "Tulostin ei havainnut paperia"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "Please check if the printer has enough paper and is ready to print."
msgstr ""
"Tarkista, että tulostimessa on tarpeeksi paperia ja että se on valmis "
"tulostamaan."

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_config
msgid "Point of Sale Configuration"
msgstr "Kassan asetukset"

#. module: pos_epson_printer
#: model:ir.model,name:pos_epson_printer.model_pos_printer
msgid "Point of Sale Printer"
msgstr "Myyntipisteen tulostin"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_res_config_settings__pos_epson_printer_ip
msgid "Pos Epson Printer Ip"
msgstr "Kassan Epson-tulostimen IP"

#. module: pos_epson_printer
#: model:ir.model.fields,field_description:pos_epson_printer.field_pos_printer__printer_type
msgid "Printer Type"
msgstr "Tulostimen tyyppi"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "Printing failed"
msgstr "Tulostus epäonnistui"

#. module: pos_epson_printer
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.pos_iot_config_view_form
#: model_terms:ir.ui.view,arch_db:pos_epson_printer.res_config_settings_view_form
msgid ""
"The Epson receipt printer will be used instead of the receipt printer "
"connected to the IoT Box."
msgstr ""
"Epsonin kuittitulostinta käytetään IoT Boxiin liitetyn kuittitulostimen "
"sijasta."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "The following error code was given by the printer:"
msgstr "Tulostin antoi seuraavan virhekoodin:"

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "The printer was successfully reached, but it wasn't able to print."
msgstr ""
"Tulostin tavoitettiin onnistuneesti, mutta se ei pystynyt tulostamaan."

#. module: pos_epson_printer
#. odoo-javascript
#: code:addons/pos_epson_printer/static/src/app/epson_printer.js:0
msgid "To find more details on the error reason, please search online for:"
msgstr "Jos haluat lisätietoja virheen syystä, etsi verkossa osoitteesta:"

#. module: pos_epson_printer
#: model:ir.model.fields.selection,name:pos_epson_printer.selection__pos_printer__printer_type__epson_epos
msgid "Use an Epson printer"
msgstr "Käytä Epson-tulostinta"
