<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="group_sale_order_template" model="res.groups">
        <field name="name">Quotation Templates</field>
        <field name="category_id" ref="base.module_category_hidden"/>
    </record>

    <data noupdate="1">
        <record id="sale_order_template_rule_company" model="ir.rule">
            <field name="name">Quotation Template multi-company</field>
            <field name="model_id" ref="model_sale_order_template"/>
            <field name="domain_force">[('company_id', 'in', company_ids + [False])]</field>
        </record>
    </data>
</odoo>
