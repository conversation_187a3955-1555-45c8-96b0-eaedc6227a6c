<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="report_invoice_document" inherit_id="account.report_invoice_document" primary="True">
        <xpath expr="//div[hasclass('page')]/t[@t-set='layout_document_title']" position="replace">
            <t t-set="layout_document_title">
                <span t-if="o.move_type == 'out_invoice' and o.state == 'posted'">Tax Invoice</span>
                <span t-elif="o.move_type == 'out_invoice' and o.state == 'draft'">Draft Tax Invoice</span>
                <span t-elif="o.move_type == 'out_invoice' and o.state == 'cancel'">Cancelled Tax Invoice</span>
                <span t-elif="o.move_type == 'out_refund' and o.state == 'posted'">Tax Credit Note</span>
                <span t-elif="o.move_type == 'out_refund' and o.state == 'draft'">Draft Tax Credit Note</span>
                <span t-elif="o.move_type == 'out_refund' and o.state == 'cancel'">Cancelled Tax Credit Note</span>
                <span t-elif="o.move_type == 'in_refund'">Tax Vendor Credit Note</span>
                <span t-elif="o.move_type == 'in_invoice'">Tax Vendor Bill</span>
                <span t-if="o.name != '/'" t-field="o.name"/>
            </t>
        </xpath>
    </template>

    <!-- Workaround for Studio reports, see odoo/odoo#60660 -->
    <template id="report_invoice" inherit_id="account.report_invoice">
        <xpath expr='//t[@t-call="account.report_invoice_document"]' position="after">
            <t t-elif="o._get_name_invoice_report() == 'l10n_au.report_invoice_document'"
               t-call="l10n_au.report_invoice_document"
               t-lang="lang"/>
        </xpath>
    </template>
</odoo>
