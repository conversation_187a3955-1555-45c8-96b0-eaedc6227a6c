<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="tax_report" model="account.report">
        <field name="name">BAS Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.au"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tax_report_balance" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_tax_report_gstrpt_sale_total" model="account.report.line">
                <field name="name">GST amounts you owe the Tax Office from sales</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_gstrpt_g1" model="account.report.line">
                        <field name="name">G1: Total Sales (including any GST)</field>
                        <field name="code">G1</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_gstrpt_g1_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">G1</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_tax_report_gstrpt_g2" model="account.report.line">
                                <field name="name">G2: Export sales</field>
                                <field name="code">G2</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_g2_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">G2</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_gstrpt_g3" model="account.report.line">
                                <field name="name">G3: Other GST-free sales</field>
                                <field name="code">G3</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_g3_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">G3</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_gstrpt_g4" model="account.report.line">
                                <field name="name">G4: Input taxed sales</field>
                                <field name="code">G4</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_g4_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">G4</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_g5" model="account.report.line">
                        <field name="name">G5: G2 + G3 + G4</field>
                        <field name="code">G5</field>
                        <field name="aggregation_formula">G2.balance+G3.balance+G4.balance</field>
                    </record>
                    <record id="account_tax_report_gstrpt_g6" model="account.report.line">
                        <field name="name">G6: Total sales subject to GST (G1 minus G5)</field>
                        <field name="code">G6</field>
                        <field name="aggregation_formula">G1.balance-G5.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_gstrpt_g7" model="account.report.line">
                                <field name="name">G7: Adjustments (if applicable)</field>
                                <field name="code">G7</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_g7_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">G7</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_g8" model="account.report.line">
                        <field name="name">G8: Total sales subject to GST after adjustments (G6 + G7)</field>
                        <field name="code">G8</field>
                        <field name="aggregation_formula">G6.balance+G7.balance</field>
                    </record>
                    <record id="account_tax_report_gstrpt_g9" model="account.report.line">
                        <field name="name">G9: GST on sales (G8 divided by eleven)</field>
                        <field name="code">G9</field>
                        <field name="aggregation_formula">G8.balance/11</field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_gstrpt_purchase_total" model="account.report.line">
                <field name="name">GST amounts the Tax Office owes you from purchases</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_gstrpt_g10" model="account.report.line">
                        <field name="name">G10: Capital purchases (including any GST)</field>
                        <field name="code">G10</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_gstrpt_g10_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">G10</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_g11" model="account.report.line">
                        <field name="name">G11: Non-capital purchases (including GST)</field>
                        <field name="code">G11</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_gstrpt_g11_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">G11</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_g12" model="account.report.line">
                        <field name="name">G12: G10 + G11</field>
                        <field name="code">G12</field>
                        <field name="aggregation_formula">G10.balance+G11.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_gstrpt_g13" model="account.report.line">
                                <field name="name">G13: Purchases for making input taxed sales</field>
                                <field name="code">G13</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_g13_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">G13</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_gstrpt_g14" model="account.report.line">
                                <field name="name">G14: Purchases without GST in the price</field>
                                <field name="code">G14</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_g14_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">G14</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_gstrpt_g15" model="account.report.line">
                                <field name="name">G15: Estimated purchases for private use or not income tax deductible</field>
                                <field name="code">G15</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_g15_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">G15</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_g16" model="account.report.line">
                        <field name="name">G16: G13 + G14 + G15</field>
                        <field name="code">G16</field>
                        <field name="aggregation_formula">G13.balance+G14.balance+G15.balance</field>
                    </record>
                    <record id="account_tax_report_gstrpt_g17" model="account.report.line">
                        <field name="name">G17: Total purchases subject to GST (G12 minus G16) </field>
                        <field name="code">G17</field>
                        <field name="aggregation_formula">G12.balance-G16.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_gstrpt_g18" model="account.report.line">
                                <field name="name">G18: Adjustments (if applicable)</field>
                                <field name="code">G18</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_g18_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">G18</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_g19" model="account.report.line">
                        <field name="name">G19: Total purchases subject to GST after adjustments (G17 + G18) </field>
                        <field name="code">G19</field>
                        <field name="aggregation_formula">G17.balance+G18.balance</field>
                    </record>
                    <record id="account_tax_report_gstrpt_g20a" model="account.report.line">
                        <field name="name">GST on purchases (G19 divided by eleven)</field>
                        <field name="code">GP</field>
                        <field name="aggregation_formula">G19.balance/11</field>
                    </record>
                    <record id="account_tax_report_gstrpt_gstonly" model="account.report.line">
                        <field name="name">GST only purchases</field>
                        <field name="code">ONLY</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_gstrpt_gstonly_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">ONLY</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_g20b" model="account.report.line">
                        <field name="name">G20: GST on purchases</field>
                        <field name="code">G20</field>
                        <field name="aggregation_formula">GP.balance+ONLY.balance</field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_paygw" model="account.report.line">
                <field name="name">PAYG Tax Withheld</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_payg_w1" model="account.report.line">
                        <field name="name">W1: Total salaries, wages and other payments</field>
                        <field name="code">W1</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_payg_w1_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">W1</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_payg_w2" model="account.report.line">
                        <field name="name">W2: Amounts withheld from salaries or wages and other payments shown at W1</field>
                        <field name="code">W2</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_payg_w2_tag" model="account.report.expression">
                                <field name="label">sub_balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">W2</field>
                            </record>
                            <record id="account_tax_report_payg_w2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-W2.sub_balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_payg_w4" model="account.report.line">
                        <field name="name">W4: Amounts withheld where no ABN is quoted</field>
                        <field name="code">W4</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_payg_w4_tag" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">W4</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_payg_w3" model="account.report.line">
                        <field name="name">W3: Other amounts withheld (excluding any amount shown at W2 or W4)</field>
                        <field name="code">W3</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_payg_w3_tag" model="account.report.expression">
                                <field name="label">sub_balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">W3</field>
                            </record>
                            <record id="account_tax_report_payg_w3_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">-W3.sub_balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_payg_w5" model="account.report.line">
                        <field name="name">W5: Total amounts withheld (W2 + W4 + W3)</field>
                        <field name="code">W5</field>
                        <field name="aggregation_formula">W2.balance+W3.balance+W4.balance</field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_gstrpt_summary" model="account.report.line">
                <field name="name">Summary</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="account_tax_report_gstrpt_summary_8a" model="account.report.line">
                        <field name="name">8A: Amounts owed to ATO</field>
                        <field name="code">8A</field>
                        <field name="aggregation_formula">1A.balance+4.balance+7A.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_gstrpt_summary_1a" model="account.report.line">
                                <field name="name">1A: GST on sales</field>
                                <field name="code">1A</field>
                                <field name="aggregation_formula">G9.balance</field>
                            </record>
                            <record id="account_tax_report_payg_summary_4" model="account.report.line">
                                <field name="name">4: PAYG Tax Withheld</field>
                                <field name="code">4</field>
                                <field name="aggregation_formula">W5.balance</field>
                            </record>
                            <record id="account_tax_report_gstrpt_summary_7a" model="account.report.line">
                                <field name="name">7A: Deferred GST</field>
                                <field name="code">7A</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_gstrpt_summary_tag" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">7A</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_summary_8b" model="account.report.line">
                        <field name="name">8B: Amounts owed by ATO</field>
                        <field name="code">8B</field>
                        <field name="aggregation_formula">1B.balance</field>
                        <field name="children_ids">
                            <record id="account_tax_report_gstrpt_summary_1b" model="account.report.line">
                                <field name="name">1B: GST on purchases</field>
                                <field name="code">1B</field>
                                <field name="aggregation_formula">G20.balance</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_gstrpt_summary_9" model="account.report.line">
                        <field name="name">9: Liability to ATO</field>
                        <field name="aggregation_formula">8A.balance-8B.balance</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
