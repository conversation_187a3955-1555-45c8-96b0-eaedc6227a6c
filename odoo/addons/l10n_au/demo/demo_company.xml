<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_au" model="res.partner" forcecreate="1">
        <field name="name">AU Company</field>
        <field name="vat">11 ***********</field>
        <field name="street">Henry Lawson Drive</field>
        <field name="city">Home Rule</field>
        <field name="country_id" ref="base.au"/>
        <field name="state_id" ref="base.state_au_8"/>
        <field name="zip">2850</field>
        <field name="phone">+61 ***********</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.auexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_au" model="res.company" forcecreate="1">
        <field name="name">AU Company</field>
        <field name="partner_id" ref="base.partner_demo_company_au"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_au')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_au'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>au</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_au')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
