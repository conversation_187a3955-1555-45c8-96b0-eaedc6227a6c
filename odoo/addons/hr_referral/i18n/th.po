# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_referral
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Rasareeyar Lappiam, 2025\n"
"Language-Team: Thai (https://app.transifex.com/odoo/teams/41243/th/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: th\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid ""
" You've gained {gained} points with this progress.{new_line}It makes you a "
"new total of {total} points. Visit {link1}this link{link2} to pick a gift!"
msgstr ""
"คุณได้รับ {gained} คะแนนจากความคืบหน้านี้ {new_line} "
"ซึ่งจะทำให้คุณมีคะแนนรวมใหม่เป็น {total} คะแนน ไปที่ {link1} ลิงก์นี้ "
"{link2} เพื่อเลือกของขวัญ!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_report__referral_hired
msgid "# Hired by Referral"
msgstr "# จ้างโดยการแนะนำ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_sms_view_form
msgid "+1 123 456 789"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
msgid ""
",\n"
"            <br/>\n"
"            <br/>"
msgstr ""
",\n"
"            <br/>\n"
"            <br/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_sms_template
msgid ""
".\n"
"If you know someone who would be a great fit for this position, please share this link with them:"
msgstr ""
".\n"
"หากคุณรู้จักใครที่เหมาะกับตำแหน่งนี้ โปรดแชร์ลิงก์นี้กับบุคคลเหล่านั้น:"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid "/ Job Offers"
msgstr "/ ข้อเสนองาน"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<b>Responsible: </b>"
msgstr "<b>รับผิดชอบ: </b>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
msgid ""
"<br/>\n"
"            <br/>\n"
"            Thanks!"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<i class=\"fa fa-2x fa-envelope-o\" role=\"img\" aria-label=\"Send by Mail\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"Send by Mail\">Send Email</span>"
msgstr ""
"<i class=\"fa fa-2x fa-envelope-o\" role=\"img\" aria-label=\"Send by Mail\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"Send by Mail\">ส่งอีเมล</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<i class=\"fa fa-2x fa-globe\" role=\"img\" aria-label=\"Job Page\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"More info\">Job Page</span>"
msgstr ""
"<i class=\"fa fa-2x fa-globe\" role=\"img\" aria-label=\"Job Page\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"More info\">หน้าตำแหน่งงาน</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<i class=\"fa fa-2x fa-mobile\" role=\"img\" aria-label=\"Send by SMS\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"Send by SMS\">Send SMS</span>"
msgstr ""
"<i class=\"fa fa-2x fa-mobile\" role=\"img\" aria-label=\"Send by SMS\"/>\n"
"                                    <br/>\n"
"                                    <span title=\"Send by SMS\">ส่ง SMS</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"fa fa-square-o\" title=\"Open\"/>"
msgstr "<i class=\"fa fa-square-o\" title=\"เปิด\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"text-danger fa fa-times\" title=\"Closed\"/>"
msgstr "<i class=\"text-danger fa fa-times\" title=\"ปิดแล้ว\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "<i class=\"text-success fa fa-check\" title=\"Done\"/>"
msgstr "<i class=\"text-success fa fa-check\" title=\"เสร็จสิ้น\"/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_level_form
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "<span class=\"ml8\">Points</span>"
msgstr "<span class=\"ml8\">คะแนน</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid ""
"<span class=\"o_stat_text\">\n"
"                                    Awarded\n"
"                                </span>"
msgstr ""
"<span class=\"o_stat_text\">\n"
"                                    ได้รับรางวัล\n"
"                                </span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid ""
"<span title=\"Buy\"><i class=\"fa fa-shopping-basket me-3\" role=\"img\" "
"aria-label=\"Buy\"/>Buy</span>"
msgstr ""
"<span title=\"ซื้อ\"><i class=\"fa fa-shopping-basket me-3\" role=\"รูปภาพ\""
" aria-label=\"ซื้อ\"/>ซื้อ</span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<span title=\"Share on Facebook\">\n"
"                                        <i class=\"fa fa-lg fa-facebook\" role=\"img\" aria-label=\"Share on facebook\"/>\n"
"                                    </span>"
msgstr ""
"<span title=\"แชร์บน Facebook\">\n"
"                                        <i class=\"fa fa-lg fa-facebook\" role=\"img\" aria-label=\"แชร์บน Facebook\"/>\n"
"                                    </span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<span title=\"Share on Linkedin\">\n"
"                                        <i class=\"fa fa-lg fa-linkedin\" role=\"img\" aria-label=\"Share on linkedin\"/>\n"
"                                    </span>"
msgstr ""
"<span title=\"แชร์บน Linkedin\">\n"
"                                        <i class=\"fa fa-lg fa-linkedin\" role=\"img\" aria-label=\"แชร์บน Linkedin\"/>\n"
"                                    </span>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid ""
"<span title=\"Share on Twitter\">\n"
"                                        <i class=\"fa fa-lg fa-twitter\" role=\"img\" aria-label=\"Share on twitter\"/>\n"
"                                    </span>"
msgstr ""
"<span title=\"แชร์บน Twitter\">\n"
"                                        <i class=\"fa fa-lg fa-twitter\" role=\"img\" aria-label=\"แชร์บน Twitter\"/>\n"
"                                    </span>"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_alert_mail_wizard.py:0
msgid ""
"A new alert has been added to the Referrals app! Check your <a "
"href=%(url)s>dashboard</a> now!"
msgstr ""
"มีการเพิ่มการแจ้งเตือนใหม่ในแอปแนะนำพนักงาน! ตรวจสอบ <a "
"href=%(url)s>แดชบอร์ด</a> ของคุณตอนนี้!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_needaction
msgid "Action Needed"
msgstr "จำเป็นต้องดำเนินการ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__active
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__active
msgid "Active"
msgstr "เปิดใช้งาน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_ids
msgid "Activities"
msgstr "กิจกรรม"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "การตกแต่งข้อยกเว้นกิจกรรม"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_state
msgid "Activity State"
msgstr "สถานะกิจกรรม"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_type_icon
msgid "Activity Type Icon"
msgstr "ไอคอนประเภทกิจกรรม"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__name
msgid "Alert"
msgstr "การเตือน"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_alert
msgid "Alert in Referral App"
msgstr "แจ้งเตือนในแอปแนะนำพนักงาน"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_alert_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_alert_configuration
msgid "Alerts"
msgstr "การเตือน"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_alert_configuration
msgid ""
"Alerts will be displayed on the first screen to give information or "
"redirects the employees"
msgstr ""
"การแจ้งเตือนจะแสดงบนหน้าจอแรกเพื่อให้ข้อมูลหรือเปลี่ยนเส้นทางของพนักงาน"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_campaign_wizard__target__all
msgid "All"
msgstr "ทั้งหมด"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_recruitment_stage__points
msgid ""
"Amount of points that the referent will receive when the applicant will "
"reach this stage"
msgstr "จำนวนคะแนนที่ผู้แนะนำจะได้รับเมื่อผู้สมัครมาถึงขั้นตอนนี้"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_applicant
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__applicant_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__applicant_id
msgid "Applicant"
msgstr "ผู้สมัคร"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Applicant must have a company."
msgstr "ผู้สมัครต้องมีบริษัท"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "Archived"
msgstr "เก็บถาวรแล้ว"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_attachment_count
msgid "Attachment Count"
msgstr "จำนวนสิ่งที่แนบมา"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__awarded_employees
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_backend_kanban
msgid "Awarded Employees"
msgstr "พนักงานที่ได้รับรางวัล"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_friend__position__back
msgid "Back"
msgstr "กลับ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Background"
msgstr "พื้นหลัง"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Background Image"
msgstr "ภาพพื้นหลัง"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__body
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__mail_body
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__body_html
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__body_plaintext
msgid "Body"
msgstr "เนื้อความ"

#. module: hr_referral
#: model:hr.referral.onboarding,text:hr_referral.welcome2
msgid ""
"Browse through open job positions, promote them on social media, or refer "
"friends."
msgstr "เรียกดูตำแหน่งงานที่เปิดรับ โปรโมตบนโซเชียลมีเดีย หรือแนะนำเพื่อน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__utm_campaign_id
msgid "Campaign"
msgstr "แคมเปญ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_mail_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_sms_view_form
msgid "Cancel"
msgstr "ยกเลิก"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__channel
msgid "Channel"
msgstr "ช่อง"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Choose an avatar for your new friend!"
msgstr "เลือกอวตารสำหรับเพื่อนใหม่ของคุณ!"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Click to level up!"
msgstr "คลิกเพื่อเพิ่มระดับ!"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.xml:0
msgid "Click(s)"
msgstr "คลิก"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid "Close"
msgstr "ปิด"

#. module: hr_referral
#: model:hr.referral.onboarding,text:hr_referral.welcome3
msgid "Collect points and exchange them for awesome gifts in the shop."
msgstr "สะสมคะแนนและแลกเป็นของขวัญสุดเจ๋งในร้าน"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_company
msgid "Companies"
msgstr "บริษัท"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__company_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__company_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Company"
msgstr "บริษัท"

#. module: hr_referral
#: model:hr.referral.onboarding,text:hr_referral.welcome4
msgid "Compete against your colleagues to build the best justice league!"
msgstr "แข่งขันกับเพื่อนร่วมงานของคุณเพื่อสร้างทีมซุเปอร์ฮีโร่ที่ดีที่สุด!"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_config_settings
msgid "Config Settings"
msgstr "ตั้งค่าการกำหนดค่า"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_configuration
msgid "Configuration"
msgstr "การกำหนดค่า"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__target
msgid "Contacted Employees"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__cost
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Cost"
msgstr "ต้นทุน"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_friend_configuration
msgid "Create a new friend"
msgstr "สร้างเพื่อนใหม่"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_level_configuration
msgid "Create a new level"
msgstr "สร้างระดับใหม่"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_alert_configuration
msgid "Create new alerts"
msgstr "สร้างการเตือนใหม่"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_points
msgid "Create new reward points"
msgstr "สร้างคะแนนสะสมใหม่"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__create_uid
msgid "Created by"
msgstr "สร้างโดย"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__create_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__create_date
msgid "Created on"
msgstr "สร้างเมื่อ"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.js:0
#: model:ir.actions.client,name:hr_referral.action_hr_referral_welcome_screen
#: model:ir.ui.menu,name:hr_referral.menu_hr_applicant_employee_referral_dashboard
msgid "Dashboard"
msgstr "แดชบอร์ด"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__image
msgid "Dashboard Image"
msgstr "ภาพแดชบอร์ด"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Date"
msgstr "วันที่"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__date_from
msgid "Date From"
msgstr "จากวันที่"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__date_to
msgid "Date To"
msgstr "ถึงวันที่"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__position
msgid ""
"Define the position of the friend. If it's a small friend like a dog, you "
"must select Front, it will be placed in the front of the dashboard, above "
"superhero."
msgstr ""
"กำหนดตำแหน่งของเพื่อน ถ้าเป็นเพื่อนตัวเล็กอย่างหมา ต้องเลือก หน้า "
"มันจะไปอยู่ด้านหน้าแดชบอร์ดเหนือซุปเปอร์ฮีโร่"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Department"
msgstr "แผนก"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__description
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Description"
msgstr "คำอธิบาย"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__direct_clicks
msgid "Direct Clicks"
msgstr "คลิกโดยตรง"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/report/hr_referral_report.py:0
msgid "Direct Referral"
msgstr "การแนะนำโดยตรง"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__dismissed_user_ids
msgid "Dismissed User"
msgstr "ผู้ใช้ที่ถูกปลด"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__display_name
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__display_name
msgid "Display Name"
msgstr "แสดงชื่อ"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_mail.py:0
msgid "Do not have access"
msgstr "ไม่มีสิทธิ์เข้าถึง"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid ""
"Do you want to confirm this reward? After confirmation an HR will contact "
"you."
msgstr "คุณต้องการยืนยันรางวัลนี้หรือไม่? หลังจากยืนยันแล้ว HR จะติดต่อคุณ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__earned_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__earned_points
msgid "Earned Points"
msgstr "คะแนนที่ได้รับ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__email_to
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_campaign_wizard__sending_method__work_email
msgid "Email"
msgstr "อีเมล"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Email a friend"
msgstr "ส่งอีเมลถึงเพื่อน"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Employee"
msgstr "พนักงาน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__employee_referral_hired
msgid "Employee Referral Hired"
msgstr "พนักงานที่แนะนำได้รับการจ้าง"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__employee_referral_refused
msgid "Employee Referral Refused"
msgstr "พนักงานที่แนะนำถูกปฏิเสธ"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_report
msgid "Employee Referral Report"
msgstr "รายงานแนะนำพนักงาน"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_form
msgid "Employees"
msgstr "พนักงาน"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_tree
msgid "Employees Analysis"
msgstr "วิเคราะห์พนักงาน"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.employee_referral_report_action
msgid "Employees Referral Analysis"
msgstr "วิเคราะห์การแนะนำพนักงาน"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.js:0
msgid "Error while copying the Referral link: %s to clipboard"
msgstr "เกิดข้อผิดพลาดขณะคัดลอกลิงก์การอ้างอิง: %s ไปยังคลิปบอร์ด"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_alert__url
msgid ""
"External links must start with 'http://www.'. For an internal url, you don't"
" need to put domain name, you can just insert the path."
msgstr ""
"ลิงก์ภายนอกต้องขึ้นต้นด้วย 'http://www.' สำหรับ url ภายใน "
"คุณไม่จำเป็นต้องใส่ชื่อโดเมน คุณสามารถแทรกเส้นทางได้"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__facebook
msgid "Facebook"
msgstr "Facebook"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__facebook_clicks
msgid "Facebook Clicks"
msgstr "คลิกเฟสบุ๊ค"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_follower_ids
msgid "Followers"
msgstr "ผู้ติดตาม"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_partner_ids
msgid "Followers (Partners)"
msgstr "ผู้ติดตาม (พาร์ทเนอร์)"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "ไอคอนแบบฟอนต์ที่ยอดเยี่ยมเช่น fa-tasks"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_onboarding_tree
msgid "Force Onboarding"
msgstr "บังคับให้เริ่มต้นใช้งาน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__friend_id
msgid "Friend"
msgstr "เพื่อน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__name
msgid "Friend Name"
msgstr "ชื่อเพื่อน"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_friend_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_friend_configuration
msgid "Friends"
msgstr "เพื่อน"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_friend
msgid "Friends for Referrals"
msgstr "เพื่อนสำหรับแนะนำ"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_friend__position__front
msgid "Front"
msgstr "ด้านหน้า"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Gather your team"
msgstr "รวบรวมทีมของคุณ"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Gather your team!"
msgstr "รวบรวมทีมของคุณ!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__gift_manager_id
msgid "Gift Responsible"
msgstr "ของขวัญที่รับผิดชอบ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Gifts"
msgstr "ของขวัญ"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__all_jobs
msgid "Go to All Jobs"
msgstr "ไปที่งานทั้งหมด"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Group By"
msgstr "กลุ่มโดย"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_view_search
msgid "HR Referral Alert Search"
msgstr "ค้นหาการแจ้งเตือนการแนะนำ HR"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__has_message
msgid "Has Message"
msgstr "มีข้อความ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_report__has_referrer
msgid "Has Referrer"
msgstr "มีผู้แนะนำ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_sms_template
msgid "Hello"
msgstr "สวัสดี"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_sms.py:0
msgid ""
"Hello! There is an amazing job offer for %(job_name)s in my company! It will"
" be a fit for you %(referral_url)s"
msgstr ""
"สวัสดี! มีข้อเสนองานสุดพิเศษสำหรับ %(job_name)s ในบริษัทของฉัน! "
"ตำแหน่งงานนี้จะเหมาะกับคุณ %(referral_url)s"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid ""
"Hello,<br/><br/>\n"
"                There are some amazing job offers in my company! Have a look, they can be interesting for you:"
msgstr ""
"สวัสดี<br/><br/>\n"
"                มีข้อเสนองานที่น่าทึ่งในบริษัทของฉัน! ลองดูสิ ข้อเสนอนี้อาจจะน่าสนใจสำหรับคุณ:"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid ""
"Hello,<br/><br/>\n"
"                There is an amazing job offer for a"
msgstr ""
"สวัสดี<br/><br/>\n"
"                มีข้อเสนองานที่น่าทึ่งสำหรับ"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__hired
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__hired
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Hired"
msgstr "จ้างงานแล้ว"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__hr_referral_level_id
msgid "Hr Referral Level"
msgstr "ระดับการอ้างอิง Hr"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__hr_referral_onboarding_page
msgid "Hr Referral Onboarding Page"
msgstr "เพจแนะนำการเริ่มงาน Hr"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__id
msgid "ID"
msgstr "ไอดี"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_exception_icon
msgid "Icon"
msgstr "ไอคอน"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "ไอคอนเพื่อระบุการยกเว้นกิจกรรม"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_needaction
msgid "If checked, new messages require your attention."
msgstr "ถ้าเลือก ข้อความใหม่จะต้องการความสนใจจากคุณ"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_error
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "ถ้าเลือก ข้อความบางข้อความมีข้อผิดพลาดในการส่ง"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__image_head
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__image
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__image
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__image
msgid "Image"
msgstr "รูปภาพ"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__progress
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "In Progress"
msgstr "กำลังดำเนินการ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__is_accessible_to_current_user
msgid "Is Accessible To Current User"
msgstr "สามารถเข้าถึงได้โดยผู้ใช้ปัจจุบัน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_is_follower
msgid "Is Follower"
msgstr "เป็นผู้ติดตาม"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__job_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__job_id
#: model_terms:ir.ui.view,arch_db:hr_referral.employee_referral_report_view_search
msgid "Job"
msgstr "งาน"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_campaign_wizard.py:0
msgid "Job Offer for a %(job_title)s at %(company_name)s"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_job
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__job_id
msgid "Job Position"
msgstr "ตำแหน่งงาน"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_job_employee_referral
msgid "Job Positions"
msgstr "ตำแหน่งงาน"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Job Referral Program"
msgstr "โปรแกรมแนะนำงาน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__job_open_date
msgid "Job Start Recruitment Date"
msgstr "วันที่เริ่มรับสมัครงาน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__write_date
msgid "Last Update Date"
msgstr "วันที่อัปเดตล่าสุด"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__write_uid
msgid "Last Updated by"
msgstr "อัปเดตครั้งล่าสุดโดย"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__write_date
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__write_date
msgid "Last Updated on"
msgstr "อัปเดตครั้งล่าสุดเมื่อ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__last_valuable_stage_id
msgid "Last Valuable Stage"
msgstr "ขั้นตอนสุดท้ายที่มีคุณค่า"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_reward_configuration
msgid "Let's create Super Rewards to thank your employees."
msgstr "มาสร้าง รางวัลพิเศษ เพื่อขอบคุณพนักงานของคุณกันเถอะ"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_referral_reward
msgid "Let's create super Rewards to thank<br>your employees."
msgstr "มาสร้าง รางวัลพิเศษ เพื่อขอบคุณ<br>พนักงานของคุณ"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_applicant_employee_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_refused_applicant_employee_referral
msgid "Let's share a job position."
msgstr "มาแชร์ตำแหน่งงานกัน"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Level"
msgstr "ระดับ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__name
msgid "Level Name"
msgstr "ชื่อระดับ"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_level
msgid "Level for referrals"
msgstr "ระดับสำหรับการแนะนำ"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_level_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_level_configuration
msgid "Levels"
msgstr "ระดับ"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__direct
msgid "Link"
msgstr "ลิงก์"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_link_to_share_action
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid "Link to Share"
msgstr "ลิงก์ที่จะแชร์"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.xml:0
msgid "Link to share"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_link_to_share__channel__linkedin
msgid "Linkedin"
msgstr "Linkedin"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__linkedin_clicks
msgid "Linkedin Clicks"
msgstr "คลิก LinkedIn"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "Mail"
msgstr "เมล"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__max_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__max_points
msgid "Max Points"
msgstr "คะแนนสูงสุด"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__medium_id
msgid "Medium"
msgstr "วิธีที่เข้ามา"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_error
msgid "Message Delivery error"
msgstr "เกิดข้อผิดพลาดในการส่งข้อความ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_ids
msgid "Messages"
msgstr "ข้อความ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมของฉัน"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_applicant_employee_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_refused_applicant_employee_referral
msgid "My Referral"
msgstr "การแนะนำของฉัน"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_alert_mail_wizard.py:0
msgid "New Alert In Referrals App"
msgstr "การเตือนใหม่ในแอปการแนะนำ"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
msgid "New gift awarded for %s"
msgstr "ของขวัญใหม่สำหรับ%s"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Next"
msgstr "ถัดไป"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "ปฏิทินอีเวนต์กิจกรรมถัดไป"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "วันครบกำหนดกิจกรรมถัดไป"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_summary
msgid "Next Activity Summary"
msgstr "สรุปกิจกรรมถัดไป"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_type_id
msgid "Next Activity Type"
msgstr "ประเภทกิจกรรมถัดไป"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.employee_referral_report_action
msgid "No data to display"
msgstr "ไม่มีข้อมูลให้แสดง"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_job_employee_referral
msgid "No job positions are available to share."
msgstr "ไม่มีตำแหน่งงานที่สามารถแชร์ได้"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_campaign_wizard.py:0
msgid "No users to send the campaign to. Please adapt your target."
msgstr "ไม่มีผู้ใช้ที่จะส่งแคมเปญถึง โปรดปรับเปลี่ยนเป้าหมายของคุณ"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__no
msgid "Not Clickable"
msgstr "ไม่สามารถคลิกได้"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__closed
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_report__referral_state__closed
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Not Hired"
msgstr "ไม่มีการจ้าง"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_needaction_counter
msgid "Number of Actions"
msgstr "จํานวนการดําเนินการ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_error_counter
msgid "Number of errors"
msgstr "จํานวนข้อผิดพลาด"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "จำนวนข้อความที่ต้องดำเนินการ"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "จํานวนข้อความที่มีข้อผิดพลาดในการส่ง"

#. module: hr_referral
#: model:hr.referral.onboarding,text:hr_referral.welcome1
msgid ""
"Oh no!\n"
"Villains are lurking the city!\n"
"Help us recruit a team of superheroes to save the day!"
msgstr ""
"ไม่นะ!\n"
"คนร้ายกำลังหลบซ่อนอยู่ในเมือง!\n"
"ช่วยเรารับสมัครทีมฮีโร่เพื่อกอบกู้โลก!"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__onclick
msgid "On Click"
msgstr "เมื่อคลิก"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_onboarding_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_onboarding_configuration
msgid "Onboarding"
msgstr "การเริ่มงาน"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_applicant__referral_state__progress
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "Ongoing"
msgstr "กำลังดำเนินการ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Open Position"
msgstr "เปิดตำแหน่ง"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Open Positions"
msgstr "เปิดตำแหน่ง"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
msgid "Our company is hiring for a"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__applicant_name
msgid "Partner Name"
msgstr "ชื่อคู่ค้า"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Point icon"
msgstr "ไอคอนคะแนน"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_points
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_stage__points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_level__points
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__points
#: model:ir.ui.menu,name:hr_referral.menu_hr_points_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_backend_kanban
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Points"
msgstr "คะแนน"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__points_not_hired
msgid "Points Given For Not Hired"
msgstr "คะแนนที่ให้สำหรับการไม่ได้รับการว่าจ้าง"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__points_missing
msgid "Points Missing"
msgstr "คะแนนที่หายไป"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Points Received"
msgstr "คะแนนที่ได้รับ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Points icon"
msgstr "ไอคอนคะแนน"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_points
msgid "Points line for referrals"
msgstr "รายการคะแนนสำหรับการแนะนำ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Points to buy this"
msgstr "คะแนนสำหรับซื้อสิ่งนี้"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Points to spend"
msgstr "คะแนนที่จะใช้"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_friend__position
msgid "Position"
msgstr "ตำแหน่ง"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "Product"
msgstr "สินค้า"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__name
msgid "Product Name"
msgstr "ชื่อสินค้า"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "Publish & Send"
msgstr "เผยแพร่และส่ง"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__rating_ids
msgid "Ratings"
msgstr "การให้คะแนน"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_applicant_employee_referral
#: model_terms:ir.actions.act_window,help:hr_referral.action_hr_refused_applicant_employee_referral
msgid "Ready to receive points?"
msgstr "พร้อมที่จะรับคะแนนแล้วหรือยัง?"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__recipient
msgid "Recipient"
msgstr "ผู้รับ"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr "รายงานการวิเคราะห์การสรรหา"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "ขั้นตอนการสรรหา"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_report_view_tree
msgid "Referer"
msgstr "ผู้แนะนำ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Referral"
msgstr "การแนะนำ"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_job.py:0
msgid "Referral %(user)s: %(job_url)s"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_alert_mail_wizard
msgid "Referral Alert Mail Wizard"
msgstr "ตัวช่วยเมลการเตือนสำหรับการแนะนำ"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_report_employee_referral_all
msgid "Referral Analysis"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_company__hr_referral_background
#: model:ir.model.fields,field_description:hr_referral.field_res_config_settings__hr_referral_background
msgid "Referral Background"
msgstr "พื้นหลังการอ้างอิง"

#. module: hr_referral
#: model:ir.actions.server,name:hr_referral.action_hr_job_launch_referral_campaign
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_kanban_inherit_referral
msgid "Referral Campaign"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_campaign_wizard
msgid "Referral Campaign Wizard"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_job.py:0
msgid "Referral Campaign for %(job)s"
msgstr ""

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_link_to_share
msgid "Referral Link To Share"
msgstr "ลิงก์การแนะนำที่จะแชร์"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__referral_point_ids
msgid "Referral Point"
msgstr "คะแนนการแนะนำ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__referral_points_ids
msgid "Referral Points"
msgstr "คะแนนการแนะนำ"

#. module: hr_referral
#: model:res.groups,name:hr_referral.group_hr_referral_reward_responsible_user
msgid "Referral Reward Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบรางวัลการแนะนำ"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_send_mail
msgid "Referral Send Mail"
msgstr "ส่งเมลการแนะนำ"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_send_sms
msgid "Referral Send sms"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__referral_state
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__referral_state
msgid "Referral State"
msgstr "สถานะการแนะนำ"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.js:0
msgid "Referral link: %s has been copied to clipboard"
msgstr "ลิงก์แนะนำพนักงาน: %s ได้ถูกคัดลอกไปยังคลิปบอร์ดแล้ว"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_link_to_share.py:0
msgid "Referral: %(name)s"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Referral: %(partner)s (%(applicant)s)"
msgstr "การแนะนำ: %(partner)s (%(applicant)s)"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_link_to_share.py:0
msgid "Referral: %(url)s"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Referral: %s"
msgstr "การแนะนำ: %s"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_root
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Referrals"
msgstr "แนะนำ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_applicant_view_search_bis_inherit_referral
msgid "Referred By"
msgstr "แนะนำโดย"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__ref_user_id
msgid "Referred By User"
msgstr "แนะนำโดยผู้ใช้"

#. module: hr_referral
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_reporting
msgid "Reporting"
msgstr "การรายงาน"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_level_form
msgid "Requirements"
msgstr "ความต้องการ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__activity_user_id
msgid "Responsible User"
msgstr "ผู้ใช้ที่รับผิดชอบ"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Restart Onboarding"
msgstr "เริ่มต้นการทำงานใหม่"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.res_config_settings_view__form
msgid "Restore Default"
msgstr "เรียกคืนค่าเริ่มต้น"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__hr_referral_reward_id
msgid "Reward"
msgstr "รางวัล"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_reward
msgid "Reward for Referrals"
msgstr "รางวัลสำหรับการแนะนำ"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_reward
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_reward_configuration
#: model:ir.ui.menu,name:hr_referral.menu_hr_referral_reward_configuration
msgid "Rewards"
msgstr "รางวัล"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_campaign_wizard__sending_method__work_phone
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "SMS"
msgstr "SMS"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__sms_body
msgid "SMS Content"
msgstr "เนื้อหา SMS"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__message_has_sms_error
msgid "SMS Delivery error"
msgstr "ข้อผิดพลาดในการส่ง SMS"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "Search Points / Gifts"
msgstr "ค้นหาคะแนน / ของขวัญ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_filter
msgid "Search Referral"
msgstr "ค้นหาการแนะนำ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid "See Job Offer"
msgstr "ดูข้อเสนองาน"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "Select employees"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_campaign_wizard__target__selection
msgid "Selection"
msgstr "การเลือก"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_alert_mail_wizard_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid "Send"
msgstr "ส่ง"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_send_mail_action
msgid "Send Job Offer by Mail"
msgstr "ส่งข้อเสนองานโดยเมล์"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_send_sms_action
msgid "Send Job Offer by SMS"
msgstr "ส่งข้อเสนองานทาง SMS"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
msgid "Send Job by Mail"
msgstr "ส่งงานโดยเมล"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_sms_view_form
msgid "Send Job by sms"
msgstr ""

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.hr_referral_alert_mail_wizard_action
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_mail_view_form
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "Send Mail"
msgstr "ส่งเมล"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_send_sms_view_form
msgid "Send SMS"
msgstr "ส่ง SMS"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__sending_method
msgid "Sending Method"
msgstr ""

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__sequence
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__sequence
msgid "Sequence"
msgstr "ลำดับ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__sequence_stage
msgid "Sequence of stage"
msgstr "ลำดับของขั้นตอน"

#. module: hr_referral
#: model:ir.actions.act_window,name:hr_referral.action_hr_referral_configuration
#: model:ir.ui.menu,name:hr_referral.hr_referral_menu_configuration
msgid "Settings"
msgstr "การตั้งค่า"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Share"
msgstr "แชร์"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/widgets/copy_referral_link_button.xml:0
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "Share Now"
msgstr "แชร์ตอนนี้"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_applicant__shared_item_infos
msgid "Shared Item Infos"
msgstr "แชร์ข้อมูลรายการ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_recruitment_stage__use_in_referral
msgid "Show in Referrals"
msgstr "แสดงในการแนะนำ"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Skip"
msgstr "ข้าม"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Skip and Start"
msgstr "ข้ามและเริ่ม"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Sorry, your referral %s has been refused in the recruitment process."
msgstr "ขออภัย ผู้ที่คุณแนะนำ %s ถูกปฏิเสธในกระบวนการสรรหาบุคลากร"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_res_users__utm_source_id
msgid "Source"
msgstr "แหล่งที่มา"

#. module: hr_referral
#: model:ir.model.fields.selection,name:hr_referral.selection__hr_referral_alert__onclick__url
msgid "Specify URL"
msgstr "ระบุ URL"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__stage_id
msgid "Stage"
msgstr "ขั้นตอน"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Start"
msgstr "เริ่ม"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Start Now"
msgstr "เริ่มเลย"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"สถานะตามกิจกรรม\n"
"เกินกำหนด: วันที่ครบกำหนดผ่านไปแล้ว\n"
"วันนี้: วันที่จัดกิจกรรมคือวันนี้\n"
"วางแผน: กิจกรรมในอนาคต"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__subject
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_campaign_wizard__mail_subject
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__subject
msgid "Subject"
msgstr "หัวเรื่อง"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Successful"
msgstr "ประสบความสำเร็จ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_onboarding__text
msgid "Text"
msgstr "ข้อความ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_sms_template
msgid "Thanks!"
msgstr "ขอบคุณ!"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__image
msgid ""
"This field holds the image used as image for the friend on the dashboard, "
"limited to 1024x1024px."
msgstr ""
"ฟิลด์นี้เก็บรูปภาพที่ใช้เป็นรูปภาพสำหรับเพื่อนบนแดชบอร์ด ซึ่งจำกัดที่ "
"1024x1024px"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_friend__image_head
msgid ""
"This field holds the image used as image for the head's friend when the user"
" must choose a new friend, limited to 1024x1024px."
msgstr ""
"ฟิลด์นี้เก็บภาพที่ใช้เป็นภาพสำหรับหัวของเพื่อนเพื่อน "
"เมื่อผู้ใช้ต้องเลือกเพื่อนใหม่ ซึ่งจำกัด 1024x1024px"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__image
msgid ""
"This field holds the image used as image for the product, limited to "
"1024x1024px."
msgstr ""
"ฟิลด์นี้เก็บรูปภาพที่ใช้เป็นรูปภาพสำหรับสินค้า โดยจำกัดที่ 1024x1024px"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_campaign_wizard_view_form
msgid ""
"This job position is not published. \n"
"                    The referral campaign will automatically publish it."
msgstr ""
"ตำแหน่งงานนี้ไม่ได้เผยแพร่ \n"
"                    แคมเปญการแนะนำจะเผยแพร่โดยอัตโนมัติ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.hr_referral_link_to_share_view_form
msgid ""
"This link contains a tracker so that people clicking on it will account to a"
" referral for you, even if they apply on a position after a few days."
msgstr ""
"ลิงก์นี้มีตัวติดตามเพื่อให้ผู้ที่คลิกลิงก์นั้นจะเป็นการแนะนำสำหรับคุณ "
"แม้ว่าพวกเขาจะสมัครตำแหน่งนี้หลังจากผ่านไปสองสามวัน"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_recruitment_stage__use_in_referral
msgid ""
"This option is used in app 'Referrals'. If checked, the stage is displayed "
"in 'Referrals Dashboard' and points are given to the employee."
msgstr ""
"ตัวเลือกนี้ใช้ในแอป 'การแนะนำ' หากเลือก ขั้นตอนจะแสดงใน 'แดชบอร์ดการแนะนำ' "
"และมอบคะแนนให้กับพนักงาน"

#. module: hr_referral
#: model_terms:ir.actions.act_window,help:hr_referral.employee_referral_report_action
msgid "This reports allow you to follow the referrals and their evolution."
msgstr "รายงานนี้ช่วยให้คุณติดตามการอ้างอิงและวิวัฒนาการของพวกเขา"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_onboarding_tree
msgid ""
"This will make the onboarding visible to all employees who have already seen"
" it. Do you want to continue?"
msgstr ""
"ซึ่งจะทำให้พนักงานทุกคนที่ได้เห็นการเริ่มต้นใช้งานแล้วสามารถมองเห็นได้ "
"คุณต้องการดำเนินการต่อหรือไม่?"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "To Spend"
msgstr "เพื่อจ่าย"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "Total"
msgstr "รวม"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "ประเภทกิจกรรมข้อยกเว้นบนบันทึก"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert__url
msgid "URL"
msgstr "URL"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_utm_campaign
msgid "UTM Campaign"
msgstr "แคมเปญ UTM "

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_utm_source
msgid "UTM Source"
msgstr "แหล่งที่มา UTM"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid ""
"Unsupported search on field is_accessible_to_current_user: %(operator)s "
"operator & %(value)s value. Only = and != operator and boolean values are "
"supported."
msgstr ""
"การค้นหาที่ไม่รองรับในฟิลด์ is_accessible_to_current_user: "
"%(operator)คือตัวดำเนินการและค่า %(value)s รองรับเฉพาะตัวดำเนินการ = and != "
"และค่าบูลีนเท่านั้น"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_link_to_share__url
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_mail__url
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_send_sms__url
msgid "Url"
msgstr "Url"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_res_users
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_alert_mail_wizard__user_ids
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_points__ref_user_id
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_report__ref_user_id
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_points_filter
msgid "User"
msgstr "ผู้ใช้"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__gift_manager_id
msgid "User responsible of this gift."
msgstr "ผู้ใช้ที่รับผิดชอบของขวัญนี้"

#. module: hr_referral
#: model:res.groups,name:hr_referral.group_hr_recruitment_referral_user
msgid "User: Referral only"
msgstr "ผู้ใช้: การแนะนำเท่านั้น"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "View Jobs"
msgstr "ดูงาน"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_job.py:0
msgid "Visit Webpage"
msgstr "เยี่ยมชมหน้าเว็บ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_referral_reward__website_message_ids
msgid "Website Messages"
msgstr "ข้อความเว็บไซต์"

#. module: hr_referral
#: model:ir.model.fields,help:hr_referral.field_hr_referral_reward__website_message_ids
msgid "Website communication history"
msgstr "ประวัติการสื่อสารของเว็บไซต์"

#. module: hr_referral
#: model:ir.model,name:hr_referral.model_hr_referral_onboarding
msgid "Welcome Onboarding in Referral App"
msgstr "ยินดีต้อนรับการเริ่มงานในแอปการแนะนำ"

#. module: hr_referral
#: model:ir.model.fields,field_description:hr_referral.field_hr_job__twitter_clicks
msgid "X Clicks"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "You are not allowed to access applicant records."
msgstr "คุณไม่ได้รับอนุญาตให้เข้าถึงบันทึกของผู้สมัคร"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid ""
"You are not allowed to access this application record because you're not one"
" of the interviewers of this application. If you think that's an error, "
"please contact your administrator."
msgstr ""
"คุณไม่ได้รับอนุญาตให้เข้าถึงบันทึกการสมัครนี้ "
"เนื่องจากคุณไม่ใช่หนึ่งในผู้สัมภาษณ์ใบสมัครนี้ หากคุณคิดว่าเกิดข้อผิดพลาด "
"โปรดติดต่อผู้ดูแลระบบของคุณ"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/utm_campaign.py:0
msgid ""
"You cannot delete these UTM Campaigns as they are linked to the following jobs in Referral:\n"
"%(job_names)s"
msgstr ""
"คุณไม่สามารถลบแคมเปญ UTM เหล่านี้ได้ เนื่องจากเชื่อมโยงกับงานต่อไปนี้ในการอ้างอิง:\n"
"%(job_names)s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/utm_source.py:0
msgid ""
"You cannot delete these UTM Sources as they are linked to the following users in Referral:\n"
"%(employee_names)s"
msgstr ""
"คุณไม่สามารถลบแหล่งที่มา UTM เหล่านี้ได้ เนื่องจากเชื่อมโยงกับผู้ใช้ต่อไปนี้ในการอ้างอิง:\n"
"%(employee_names)s"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_referral_reward.py:0
msgid ""
"You do not have enough points in this company to buy this product. In this "
"company, you have %s points."
msgstr ""
"คุณมีคะแนนไม่เพียงพอในบริษัทเพื่อซื้อสินค้านี้ ในบริษัทนี้คุณมี %s คะแนน"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/wizard/hr_referral_send_sms.py:0
msgid "You do not have the required access rights to send SMS."
msgstr "คุณไม่มีสิทธิ์การเข้าถึงที่จำเป็นสำหรับการส่ง SMS"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_applicant_employee_referral_kanban
msgid "You earned"
msgstr "คุณได้รับ"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_reward_kanban
msgid "You need another"
msgstr "คุณต้องมีอีกรายการ"

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Your referrer got a step further! %(message)s"
msgstr ""

#. module: hr_referral
#. odoo-python
#: code:addons/hr_referral/models/hr_applicant.py:0
msgid "Your referrer is hired! %(message)s"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_job_employee_referral_kanban
msgid "click(s)"
msgstr "คลิก"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "has been hired!"
msgstr "ได้รับการว่าจ้างแล้ว!"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.view_hr_referral_alert_form
msgid "https://www.google.com"
msgstr "https://www.google.com"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_email_body_template
msgid ""
"in my company! It will be a fit for you.\n"
"                <br/>"
msgstr ""
"ในบริษัทของฉัน! มันจะพอดีสำหรับคุณ\n"
"                <br/>"

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_sms_template
msgid "is hiring for a position"
msgstr ""

#. module: hr_referral
#: model_terms:ir.ui.view,arch_db:hr_referral.referral_campaign_email_body_template
msgid ""
"position.\n"
"                If you know someone who would be a great fit for this position, please share this link with them:"
msgstr ""
"ตำแหน่ง\n"
"                หากคุณรู้จักใครที่เหมาะกับตำแหน่งนี้ โปรดแชร์ลิงก์นี้กับบุคคลเหล่านั้น:"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "reward"
msgstr "รางวัล"

#. module: hr_referral
#. odoo-javascript
#: code:addons/hr_referral/static/src/views/dashboard.xml:0
msgid "share"
msgstr "แชร์"
