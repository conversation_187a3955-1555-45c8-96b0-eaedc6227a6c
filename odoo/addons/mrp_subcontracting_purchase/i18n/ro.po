# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_purchase
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Romanian (https://app.transifex.com/odoo/teams/41243/ro/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ro\n"
"Plural-Forms: nplurals=3; plural=(n==1?0:(((n%100>19)||((n%100==0)&&(n!=0)))?2:1));\n"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d zi(le)"

#. module: mrp_subcontracting_purchase
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_purchase.purchase_order_form_mrp_subcontracting_purchase
msgid "<span class=\"o_stat_text\">Resupply</span>"
msgstr "<span class=\"o_stat_text\">Reaprovisionare</span>"

#. module: mrp_subcontracting_purchase
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_purchase.stock_picking_form_mrp_subcontracting
msgid "<span class=\"o_stat_text\">Source PO</span>"
msgstr "<span class=\"o_stat_text\">CA sursă</span>"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Raport general LDM"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,field_description:mrp_subcontracting_purchase.field_purchase_order__subcontracting_resupply_picking_count
msgid "Count of Subcontracting Resupply"
msgstr "Număr de reaprovizionare subcontractare"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,help:mrp_subcontracting_purchase.field_purchase_order__subcontracting_resupply_picking_count
msgid "Count of Subcontracting Resupply for component"
msgstr "Număr de reaprovizionare subcontractare pentru componentă"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_rule.py:0
msgid "Days to Supply Components"
msgstr "Zile pentru a furniza componente"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_account_move_line
msgid "Journal Item"
msgstr "Element de jurnal"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_rule.py:0
msgid "Manufacturing Lead Time"
msgstr "Timpul de așteptare in Producție"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,field_description:mrp_subcontracting_purchase.field_stock_picking__subcontracting_source_purchase_count
msgid "Number of subcontracting PO Source"
msgstr "Număr de CA subcontractare sursă"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,help:mrp_subcontracting_purchase.field_stock_picking__subcontracting_source_purchase_count
msgid "Number of subcontracting Purchase Order Source"
msgstr "Număr de comenzi de achiziție subcontractare sursă"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Comandă de achiziție"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_picking.py:0
msgid "Source PO of %s"
msgstr "CA sursă a %s"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_move
msgid "Stock Move"
msgstr "Mișcare de stoc"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_rule
msgid "Stock Rule"
msgstr "Regulă stoc"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "Nivel evaluare stoc"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_picking
msgid "Transfer"
msgstr "Transfer"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_rule.py:0
msgid "Vendor Lead Time"
msgstr "Termen Livrare Vânzători"
