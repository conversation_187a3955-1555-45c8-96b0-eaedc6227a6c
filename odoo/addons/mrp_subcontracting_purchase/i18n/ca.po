# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_subcontracting_purchase
# 
# Translators:
# <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> G<PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Catalan (https://app.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_rule.py:0
msgid "+ %d day(s)"
msgstr "+ %d dia(s)"

#. module: mrp_subcontracting_purchase
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_purchase.purchase_order_form_mrp_subcontracting_purchase
msgid "<span class=\"o_stat_text\">Resupply</span>"
msgstr "<span class=\"o_stat_text\">Reaprovisionament</span>"

#. module: mrp_subcontracting_purchase
#: model_terms:ir.ui.view,arch_db:mrp_subcontracting_purchase.stock_picking_form_mrp_subcontracting
msgid "<span class=\"o_stat_text\">Source PO</span>"
msgstr "<span class=\"o_stat_text\">Font PO</span>"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Informe de visió general del LDM"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,field_description:mrp_subcontracting_purchase.field_purchase_order__subcontracting_resupply_picking_count
msgid "Count of Subcontracting Resupply"
msgstr "Nombre de reaprovisionaments de subcontractacions"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,help:mrp_subcontracting_purchase.field_purchase_order__subcontracting_resupply_picking_count
msgid "Count of Subcontracting Resupply for component"
msgstr "Nombre de reaprovisionaments de subcontractacions per als components"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_rule.py:0
msgid "Days to Supply Components"
msgstr "Dies per subministrar components"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_account_move_line
msgid "Journal Item"
msgstr "Apunt comptable"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_rule.py:0
msgid "Manufacturing Lead Time"
msgstr "Termini de lliurament de fabricació"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,field_description:mrp_subcontracting_purchase.field_stock_picking__subcontracting_source_purchase_count
msgid "Number of subcontracting PO Source"
msgstr "Número de la font PO de la subcontractació"

#. module: mrp_subcontracting_purchase
#: model:ir.model.fields,help:mrp_subcontracting_purchase.field_stock_picking__subcontracting_source_purchase_count
msgid "Number of subcontracting Purchase Order Source"
msgstr "Number of subcontracting Purchase Order Source"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_purchase_order
msgid "Purchase Order"
msgstr "Comanda de compra"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_picking.py:0
msgid "Source PO of %s"
msgstr "Font PO de %s"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_move
msgid "Stock Move"
msgstr "Moviment d'estoc"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_rule
msgid "Stock Rule"
msgstr "Regla d'estoc"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_valuation_layer
msgid "Stock Valuation Layer"
msgstr "Capa de valoració d'estoc"

#. module: mrp_subcontracting_purchase
#: model:ir.model,name:mrp_subcontracting_purchase.model_stock_picking
msgid "Transfer"
msgstr "Transferència"

#. module: mrp_subcontracting_purchase
#. odoo-python
#: code:addons/mrp_subcontracting_purchase/models/stock_rule.py:0
msgid "Vendor Lead Time"
msgstr "Termini de lliurament del proveïdor"
