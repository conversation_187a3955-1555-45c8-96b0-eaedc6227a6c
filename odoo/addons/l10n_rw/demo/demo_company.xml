<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="partner_demo_company_rw" model="res.partner">
        <field name="name">RW Company</field>
        <field name="vat">RW000111111</field>
        <field name="street">KN 4 St</field>
        <field name="city">Kigali</field>
        <field name="country_id" ref="base.rw"/>
        <field name="zip">00000</field>
        <field name="phone">+***********</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.rwexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="demo_company_rw" model="res.company">
        <field name="name">RW Company</field>
        <field name="partner_id" ref="partner_demo_company_rw"/>
    </record>

    <record id="demo_bank_rw" model="res.partner.bank">
        <field name="acc_number">BKIGRWRR</field>
        <field name="partner_id" ref="partner_demo_company_rw"/>
        <field name="company_id" ref="demo_company_rw"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('demo_company_rw')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('l10n_rw.demo_company_rw'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>rw</value>
        <value model="res.company" eval="obj().env.ref('l10n_rw.demo_company_rw')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
