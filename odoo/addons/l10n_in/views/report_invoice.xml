<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="l10n_in_report_invoice_document_inherit" inherit_id="account.report_invoice_document" primary="True">
        <xpath expr="//div[@name='shipping_address_block']" position="inside">
            <div t-if="o.company_id.account_fiscal_country_id.code == 'IN' and o.partner_shipping_id.vat">
                GSTIN: <span t-field="o.partner_shipping_id.vat"/>
            </div>
        </xpath>
        <xpath expr="//div[@name='address_not_same_as_shipping']//t[@t-set='address']" position="inside">
            <t t-call="l10n_in.place_of_supply"/>
        </xpath>
        <xpath expr="//div[@name='address_same_as_shipping']//t[@t-set='address']" position="inside">
            <t t-call="l10n_in.place_of_supply"/>
        </xpath>
        <xpath expr="//div[@name='no_shipping']//t[@t-set='address']" position="inside">
            <t t-call="l10n_in.place_of_supply"/>
        </xpath>

        <xpath expr="//table[@name='invoice_line_table']/thead/tr/th[1]" position="after">
            <t t-if="o.company_id.account_fiscal_country_id.code == 'IN'">
                <th>HSN/SAC</th>
            </t>
        </xpath>

        <xpath expr="//t[@name='account_invoice_line_accountable']/td[1]" position="after">
            <td t-if="o.company_id.account_fiscal_country_id.code == 'IN'">
              <span t-if="line.l10n_in_hsn_code" t-field="line.l10n_in_hsn_code"></span>
            </td>
        </xpath>

        <xpath expr="//div[@id='qrcode_info']" position="attributes">
            <attribute name="t-if" add="o.company_id.account_fiscal_country_id.code != 'IN'" separator="and"/>
        </xpath>

        <xpath expr="//div[@id='qrcode_info']" position="after">
            <t t-if="o.company_id.account_fiscal_country_id.code == 'IN' and o.company_id.l10n_in_upi_id">
                <div style="display:-webkit-flex;" class="flex-column">
                    <strong>PAYMENT QR CODE</strong>
                    <div class="mt-1 mb-1">
                        <p class="mb-0">UPI ID:</p>
                        <span class="mb-0" t-field="o.company_id.l10n_in_upi_id"/>
                    </div>
                    <div class="d-flex flex-row" t-attf-style="#{'-webkit-transform:translateX(-0.5rem);' if report_type != 'html' else '-webkit-transform:translate(-0.5rem,-0.8rem);'}">
                        <img src="/l10n_in/static/src/img/PhonePe-Logo.svg" style="width:4.5rem;"/>
                        <img src="/l10n_in/static/src/img/Google_Pay-Logo.svg" style="width:3.5rem;"/>
                        <img src="/l10n_in/static/src/img/Paytm-Logo.svg" style="width:4rem;"/>
                        <img src="/l10n_in/static/src/img/Upi-logo.svg" t-attf-style="#{'' if report_type != 'html' else 'padding:0.5rem;'} width:4rem;"/>
                    </div>
                </div>
            </t>
        </xpath>

        <xpath expr="//t[@t-set='layout_document_title']" position="replace">
            <t t-set="layout_document_title">
                <span t-if="o.move_type == 'out_invoice' and o.state == 'posted'" t-field="o.journal_id.name"/>
                <span t-elif="o.move_type == 'out_invoice' and o.state == 'draft'">Draft <span t-field="o.journal_id.name"/></span>
                <span t-elif="o.move_type == 'out_invoice' and o.state == 'cancel'">Cancelled <span t-field="o.journal_id.name"/></span>
                <span t-elif="o.move_type == 'out_refund' and o.state == 'posted'">Credit Note</span>
                <span t-elif="o.move_type == 'out_refund' and o.state == 'draft'">Draft Credit Note</span>
                <span t-elif="o.move_type == 'out_refund' and o.state == 'cancel'">Cancelled Credit Note</span>
                <span t-elif="o.move_type == 'in_refund'">Vendor Credit Note</span>
                <span t-elif="o.move_type == 'in_invoice'">Vendor Bill</span>
                <span t-if="o.name != '/'" t-field="o.name"/>
            </t>
        </xpath>

        <xpath expr="//div[@id='payment_term']" position="after">
            <t t-if="o.company_id.account_fiscal_country_id.code == 'IN'">
                <t t-set="hsn_summary" t-value="o._l10n_in_get_hsn_summary_table()"/>
                <t t-if="hsn_summary">
                    <div name="l10n_in_hsn_summary" class="mt-3" style="page-break-inside: avoid;">
                        <h3>HSN Summary</h3>
                        <table class="table table-sm table-borderless col-6">
                            <thead>
                                <th>HSN/SAC</th>
                                <th class="text-end">Quantity</th>
                                <th class="text-end">Rate %</th>
                                <th class="text-end">Taxable Value</th>
                                <th class="text-end" t-if="hsn_summary['has_gst']">SGST</th>
                                <th class="text-end" t-if="hsn_summary['has_gst']">CGST</th>
                                <th class="text-end" t-if="hsn_summary['has_igst']">IGST</th>
                                <th class="text-end" t-if="hsn_summary['has_cess']">CESS</th>
                            </thead>
                            <tr t-foreach="hsn_summary['items']" t-as="item">
                                <td t-esc="item['l10n_in_hsn_code']"/>
                                <td class="text-end">
                                    <span t-esc="item['quantity']"/>
                                    <span t-if="hsn_summary['display_uom']">(<t t-esc="item['uom_name']"/>)</span>
                                </td>
                                <td class="text-end" t-esc="item['rate']"/>
                                <td class="text-end">
                                    <span t-esc="item['amount_untaxed']"
                                          t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                                </td>
                                <td class="text-end" t-if="hsn_summary['has_gst']">
                                    <span t-esc="item['tax_amount_sgst']"
                                          t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                                </td>
                                <td class="text-end" t-if="hsn_summary['has_gst']">
                                    <span t-esc="item['tax_amount_cgst']"
                                          t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                                </td>
                                <td class="text-end" t-if="hsn_summary['has_igst']">
                                    <span t-esc="item['tax_amount_igst']"
                                          t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                                </td>
                                <td class="text-end" t-if="hsn_summary['has_cess']">
                                    <span t-esc="item['tax_amount_cess']"
                                          t-options="{'widget': 'monetary', 'display_currency': o.currency_id}"/>
                                </td>
                            </tr>
                        </table>
                    </div>
                </t>
            </t>
        </xpath>

    </template>

    <!-- Workaround for Studio reports, see odoo/odoo#60660 -->
    <template id="report_invoice" inherit_id="account.report_invoice">
        <xpath expr='//t[@t-call="account.report_invoice_document"]' position="after">
            <t t-elif="o._get_name_invoice_report() == 'l10n_in.l10n_in_report_invoice_document_inherit'"
               t-call="l10n_in.l10n_in_report_invoice_document_inherit"
               t-lang="lang"/>
        </xpath>
    </template>

    <template id="place_of_supply">
        <div t-if="o.l10n_in_state_id">
            Place of supply: <span t-out="o.l10n_in_state_id.name" />
        </div>
    </template>

</odoo>
