# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_mt
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.1alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-03 10:20+0000\n"
"PO-Revision-Date: 2024-01-03 10:20+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_mt
#: model:ir.model,name:l10n_mt.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_adjustment_2
msgid "Adjustment in favour of registered person"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_adjustment_1
msgid "Adjustment in favour of vat department"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_IV_4
msgid "Capital Goods"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_domestic_imp
msgid "Domestic purchases and imports"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_domestic_sup
msgid "Domestic supplies and exports"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_excess_credit
msgid "Excess Credit"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_excess_credit_BF
msgid "Excess Credit B/F"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_I_1
msgid ""
"Exempt IC Supplies of Goods and Supplies of Services where customer is "
"liable for the tax"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_IV_3
msgid "Exempt Purchases for re-sale"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_III_4
msgid "Exempt with Credit/Exports"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_III_5
#: model:account.report.line,name:l10n_mt.tax_report_line_IV_6
#: model:account.report.line,name:l10n_mt.tax_report_line_IV_7
msgid "Exempt without Credit"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_II_4
msgid "Goods and Services received where place of supply is Malta"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_I_4
msgid ""
"Goods and Services received where place of supply is Malta other than those "
"reported in \"Box 3\""
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_II_3
msgid "IC Acquisition of Capital Goods"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_I_3
msgid ""
"IC Acquisition of Goods and Services received from other EU member states"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_II_1
msgid "IC Acquisition of goods for re-sale"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_intra_com
msgid "Intra-community and Non-EU Trade"
msgstr ""

#. module: l10n_mt
#: model:ir.ui.menu,name:l10n_mt.account_reports_mt_statements_menu
msgid "Malta"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_reverse_charge
msgid "Reverse charge"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_IV_5
msgid "Services and overheads at 18%"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_II_2
msgid ""
"Services received from EU member stated where the purchaser is liable for "
"vat"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_III_6_subtotal
#: model:account.report.line,name:l10n_mt.tax_report_line_II_5_subtotal
#: model:account.report.line,name:l10n_mt.tax_report_line_IV_8_subtotal
#: model:account.report.line,name:l10n_mt.tax_report_line_I_5_subtotal
msgid "Subtotal"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_I_2
msgid ""
"Supplies of Goods and Services where place of supply is outside Malta - EU "
"and non EU"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_title_tax_payable
msgid "Tax Payable"
msgstr ""

#. module: l10n_mt
#: model:account.report,name:l10n_mt.tax_report
msgid "Tax Report"
msgstr ""

#. module: l10n_mt
#: model:account.report.column,name:l10n_mt.tax_report_tax_amount
msgid "Tax amount"
msgstr ""

#. module: l10n_mt
#: model:account.report.column,name:l10n_mt.tax_report_tax_base
msgid "Tax base"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_III_1
msgid "Taxable Goods/Services at 18%"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_III_3
msgid "Taxable Goods/Services at 5%"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_IV_1
msgid "Taxable Purchases for re-sale at 18%"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_IV_2
msgid "Taxable Purchases for re-sale at 5%"
msgstr ""

#. module: l10n_mt
#: model:account.report.line,name:l10n_mt.tax_report_line_III_2
msgid "Taxable Services at 7%"
msgstr ""
