<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!--Manage the job_id to get in hr.applicant-->
        <record id="hr.job_developer" model="hr.job">
            <field name="survey_id" ref="survey_recruitment_form"/>
        </record>
        <record id="hr.job_ceo" model="hr.job">
            <field name="survey_id" ref="survey_recruitment_form"/>
        </record>
        <record id="hr.job_cto" model="hr.job">
            <field name="survey_id" ref="survey_recruitment_form"/>
        </record>
        <record id="hr.job_consultant" model="hr.job">
            <field name="survey_id" ref="survey_recruitment_form"/>
        </record>
        <record id="hr.job_hrm" model="hr.job">
            <field name="survey_id" ref="survey_recruitment_form"/>
        </record>
        <record id="hr.job_marketing" model="hr.job">
            <field name="survey_id" ref="survey_recruitment_form"/>
        </record>
        <record id="hr.job_trainee" model="hr.job">
            <field name="survey_id" ref="survey_recruitment_form"/>
        </record>

    </data>
</odoo>
