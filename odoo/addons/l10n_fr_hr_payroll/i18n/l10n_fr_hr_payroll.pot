# Translation of Odoo Server.
# This file contains the translation of the following modules:
#	* l10n_fr_hr_payroll
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-09-17 13:45+0000\n"
"PO-Revision-Date: 2018-09-17 13:45+0000\n"
"Last-Translator: <>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.res_config_settings_view_form
msgid "<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-specific.\" aria-label=\"Values set here are company-specific.\" groups=\"base.group_multi_company\" role=\"img\"/>\n"
"                            <span class=\"o_form_label\">Paie</span>"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "<span>Matricule :</span><br/>\n"
"                                        <span>N° s.s :</span><br/>\n"
"                                        <span>Emploi :</span><br/>\n"
"                                        <span>Qualif :</span><br/>\n"
"                                        <span>Niveau :</span><br/>\n"
"                                        <span>Coef :</span><br/>\n"
"                                        <span>Entrée :</span><br/>\n"
"                                        <span>Sortie :</span><br/>\n"
"                                        <span>ORG. S.S :</span>"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "<strong>Mode de réglement</strong> :"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "<strong>Net à payer </strong>:"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "<strong>Payé</strong>"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "<strong>Total Charges Patronales</strong> :"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C39_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C39_employer
msgid "AGFF Cadre Tranche A"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C18_employer
msgid "AGFF Cadre Tranche B"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C13_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C13_employer
msgid "AGFF Non-cadre Tranche 1"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C15_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C15_employer
msgid "AGFF Non-cadre Tranche 2"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C18_employe
msgid "AGFF Non-cadre Tranche B"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C38_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C38_employer
msgid "AGIRC Cadre Tranche C"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C17_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C17_employer
msgid "AGIRC et GMP Cadre Tranche B"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C11_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C11_employer
msgid "AGS (FNGS)"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "APE:"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C20_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C20_employer
msgid "APEC"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C16_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C16_employer
msgid "ARRCO Cadre Tranche A"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C12_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C12_employer
msgid "ARRCO Non-cadre Tranche 1"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C14_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C14_employer
msgid "ARRCO Non-cadre Tranche 2"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C5_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C5_employer
msgid "Accident du travail"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C4_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C4_employer
msgid "Allocations familiales"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C10_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C10_employer
msgid "Assurance chomage"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C21_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C21_employer
msgid "Assurance deces cadres"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C1_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C1_employer
msgid "Assurance maladie, maternite, invalidite, deces, solidaritee des personnes agees et handicapees"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule.category,name:l10n_fr_hr_payroll.other_totals
msgid "Autres totaux"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Bulletin de paie"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C19_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C19_employer
msgid "CET"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C30_employe
msgid "CRDS"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C29_employe
msgid "CSG deductible"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C28_employe
msgid "CSG non deductible"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Code"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_hr_contract__coef
msgid "Coefficient"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model,name:l10n_fr_hr_payroll.model_res_company
msgid "Companies"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C24_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C24_employer
msgid "Contribution additionnelle au developpement de l'apprentissage"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_company__conv_coll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_config_settings__conv_coll
msgid "Convention collective"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_company__cotisation_prevoyance
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_config_settings__cotisation_prevoyance
msgid "Cotisation Patronale Prevoyance"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule.category,name:l10n_fr_hr_payroll.PREV
msgid "Cotisations Prevoyance Patronales"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_total
#: model:hr.salary.rule.category,name:l10n_fr_hr_payroll.TOTAL
msgid "Cout total pour l'entreprise"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_cumul_imposable
#: model:hr.salary.rule.category,name:l10n_fr_hr_payroll.C_IMP
msgid "Cumul Imposable"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "DANS VOTRE INTERET ET POUR VOUS AIDER A FAIRE VALOIR VOS DROITS, CONSERVEZ CE \n"
"BULLETIN DE PAIE SANS LIMITATION DE DUREE"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_report_l10n_fr_hr_payroll_report_l10n_fr_fiche_paye__display_name
msgid "Display Name"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Désignation"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C22_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C22_employer
msgid "Effort a la construction"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model,name:l10n_fr_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C8_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C8_employer
msgid "FNAL +20 Employes"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C7_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C7_employer
msgid "FNAL -20 Employes"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.actions.report,name:l10n_fr_hr_payroll.action_report_report_l10nfrfichepaye
msgid "Fiche de paye"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C37_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C37_employer
msgid "Forfait Mutuelle"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C25_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C25_employer
msgid "Formation professionnelle 10- salaries"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C26_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C26_employer
msgid "Formation professionnelle 10-19 salaries"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C27_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C27_employer
msgid "Formation professionnelle 20+ salaries"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.leave.type,name:l10n_fr_hr_payroll.holiday_status_heures_sup
msgid "Heures Supplementaires"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_report_l10n_fr_hr_payroll_report_l10n_fr_fiche_paye__id
msgid "ID"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_report_l10n_fr_hr_payroll_report_l10n_fr_fiche_paye____last_update
msgid "Last Modified on"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_hr_payslip__payment_mode
msgid "Mode de paiement"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Montant"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Montant Charges Patronales"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Net Imposable"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_hr_contract__niveau
msgid "Niveau"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_company__nombre_employes
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_config_settings__nombre_employes
msgid "Nombre d'employes"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_company__org_ss
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_config_settings__org_ss
msgid "Organisme de securite sociale"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Paie du"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model,name:l10n_fr_hr_payroll.model_hr_payslip
msgid "Pay Slips"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_secu
msgid "Plafond Securite Sociale"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_tranche_1
msgid "Plafond Tranche 1"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_tranche_2
msgid "Plafond Tranche 2"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_tranche_a
msgid "Plafond Tranche A"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_tranche_b
msgid "Plafond Tranche B"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_tranche_c
msgid "Plafond Tranche C"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule.category,name:l10n_fr_hr_payroll.SECU
msgid "Plafond de Securite Sociale"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_company__plafond_secu
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_res_config_settings__plafond_secu
msgid "Plafond de la Securite Sociale"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Plafond s.s."
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C35_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C35_employer
msgid "Prevoyance Cadre TA"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C36_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C36_employer
msgid "Prevoyance Cadre TB"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model.fields,field_description:l10n_fr_hr_payroll.field_hr_contract__qualif
msgid "Qualification"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Quantité / Base"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "SIRET:"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Salaire Brut"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Taux"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Taux Charges Patronales"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C40_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C40_employer
msgid "Taxe Prevoyance"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C23_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C23_employer
msgid "Taxe d'apprentissage"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_total_charges_patronales
msgid "Total Charges Patronales"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule.category,name:l10n_fr_hr_payroll.SALC
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Total Charges Salariales"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_total_retenues
#: model:hr.salary.rule.category,name:l10n_fr_hr_payroll.RETENUES
msgid "Total Retenues"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_rule_total_charges_salariales
msgid "Total des charges salariales"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "Total versé par l'employeur"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C9_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C9_employer
msgid "Versement Transport"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C3_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C3_employer
msgid "Vieillesse deplafonnee"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C2_employe
#: model:hr.salary.rule,name:l10n_fr_hr_payroll.hr_payroll_rules_C2_employer
msgid "Vieillesse plafonnee"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_fr_hr_payroll.report_l10n_fr_fiche_paye
msgid "au"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model,name:l10n_fr_hr_payroll.model_report_l10n_fr_hr_payroll_report_l10n_fr_fiche_paye
msgid "report.l10n_fr_hr_payroll.report_l10n_fr_fiche_paye"
msgstr ""

#. module: l10n_fr_hr_payroll
#: model:ir.model,name:l10n_fr_hr_payroll.model_res_config_settings
msgid "res.config.settings"
msgstr ""

