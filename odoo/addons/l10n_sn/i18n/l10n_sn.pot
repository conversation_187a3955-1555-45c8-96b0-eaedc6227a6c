# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_sn
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 12:04+0000\n"
"PO-Revision-Date: 2023-11-30 12:04+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_sn
#: model:ir.model,name:l10n_sn.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_due
msgid "Balance due"
msgstr ""

#. module: l10n_sn
#: model:account.report.column,name:l10n_sn.account_tax_report_sn_base
msgid "Base"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_to_report
msgid "Credit to report"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_ddi
msgid "DDI checks"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_deductible
msgid "Deductible"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_deductible_taxable
msgid "Domestic purchases"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_exportation
msgid "Exportations"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_deductible_import
msgid "Importations"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_tva_credit
msgid "Last month's credit"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_non_taxed
msgid "Non Taxed Domestic Operation"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_suspension
msgid "Operation done under suspension of VAT"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_exempt
msgid "Operations exempted"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_reimbursement_review
msgid "Reimbursement under review"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_reimbursement_accepted
msgid "Reimbursements accepted"
msgstr ""

#. module: l10n_sn
#. odoo-python
#: code:addons/l10n_sn/models/template_sn_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr ""

#. module: l10n_sn
#. odoo-python
#: code:addons/l10n_sn/models/template_sn.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_self_delivery
msgid "Self delivery or service"
msgstr ""

#. module: l10n_sn
#: model:account.report.column,name:l10n_sn.account_tax_report_sn_tax
msgid "Tax"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_prepayment
msgid "Tax Prepayment"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_taxable_18
msgid "Taxable - normal rate"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_taxable_10
msgid "Taxable - reduced rate"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_taxable
msgid "Taxable operations"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_operations
msgid "Total amount of operations"
msgstr ""

#. module: l10n_sn
#: model:account.report,name:l10n_sn.account_tax_report_sn
msgid "VAT Report"
msgstr ""

#. module: l10n_sn
#: model:account.report.line,name:l10n_sn.account_tax_report_line_sn_withholding
msgid "Withholding"
msgstr ""
