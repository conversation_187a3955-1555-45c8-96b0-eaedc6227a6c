# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_work_entry_contract_attendance
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:26+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON>, 2024\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,help:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid ""
"\n"
"        Defines the source for work entries generation\n"
"\n"
"        Working Schedule: Work entries will be generated from the working hours below.\n"
"        Attendances: Work entries will be generated from the employee's attendances. (requires Attendance app)\n"
"        Planning: Work entries will be generated from the employee's planning. (requires Planning app)\n"
"    "
msgstr ""
"\n"
"        Puantaj Kayıtları oluşturma kaynağını tanımlar\n"
"\n"
"        Çalışma Programı: Puantaj Kayıtları aşağıdaki çalışma saatlerinden oluşturulacaktır.\n"
"        Katılımlar: Puantaj Kayıtları, çalışanın katılımlarından oluşturulacaktır. (Katılım uygulaması gerektirir)\n"
"        Planlama: Puantaj Kayıtları çalışanın planlamasından oluşturulacaktır. (Planlama uygulaması gerektirir)\n"
"    "

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_work_entry__attendance_id
msgid "Attendance"
msgstr "Katılım"

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields.selection,name:hr_work_entry_contract_attendance.selection__hr_contract__work_entry_source__attendance
msgid "Attendances"
msgstr "Katılımcılar"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_contract
msgid "Employee Contract"
msgstr "Personel Sözleşmesi"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry
msgid "HR Work Entry"
msgstr "İK Puantaj Kaydı"

#. module: hr_work_entry_contract_attendance
#: model:ir.model,name:hr_work_entry_contract_attendance.model_hr_work_entry_regeneration_wizard
msgid "Regenerate Employee Work Entries"
msgstr "Çalışan İş Girişlerini Yeniden Oluşturun"

#. module: hr_work_entry_contract_attendance
#. odoo-python
#: code:addons/hr_work_entry_contract_attendance/models/hr_attendance.py:0
msgid ""
"This attendance record is linked to a validated working entry. You can't "
"delete it."
msgstr ""

#. module: hr_work_entry_contract_attendance
#. odoo-python
#: code:addons/hr_work_entry_contract_attendance/models/hr_attendance.py:0
msgid ""
"This attendance record is linked to a validated working entry. You can't "
"modify it."
msgstr ""

#. module: hr_work_entry_contract_attendance
#: model:ir.model.fields,field_description:hr_work_entry_contract_attendance.field_hr_contract__work_entry_source
msgid "Work Entry Source"
msgstr "Puantaj Kaydı Kaynağı"
