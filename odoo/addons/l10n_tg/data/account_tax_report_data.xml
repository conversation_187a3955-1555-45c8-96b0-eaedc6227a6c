<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="account_tax_report_tg" model="account.report">
        <field name="name">VAT Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.tg"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="account_tax_report_tg_balance" model="account.report.column">
                <field name="name">Base</field>
                <field name="expression_label">base</field>
            </record>
            <record id="account_tax_report_tg_tax" model="account.report.column">
                <field name="name">Tax</field>
                <field name="expression_label">tax</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_tax_report_line_tg_sales" model="account.report.line">
                <field name="name">1/11. Outgoing</field>
                <field name="code">TG_SALES</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_tax_report_line_tg_sales_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TG_NON_TAXABLE.base + TG_TAXABLE.base</field>
                    </record>
                    <record id="account_tax_report_line_tg_sales_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TG_TAXABLE.tax</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_tax_report_line_tg_sales_non_taxable" model="account.report.line">
                        <field name="name">2. Non Taxable operations</field>
                        <field name="code">TG_NON_TAXABLE</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_sales_non_taxable_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TG_SALE_EXEMPT.base + TG_SALE_NOT_IMPOSABLE.base + TG_EXPORT.base</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_tg_sales_non_taxable_exempt" model="account.report.line">
                                <field name="name">3. Exempt</field>
                                <field name="code">TG_SALE_EXEMPT</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_tg_sales_non_taxable_exempt_base_tag" model="account.report.expression">
                                        <field name="label">base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_3</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_tg_sales_non_taxable_not_imposable" model="account.report.line">
                                <field name="name">4. Not imposable</field>
                                <field name="code">TG_SALE_NOT_IMPOSABLE</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_tg_sales_non_taxable_not_imposable_base_tag" model="account.report.expression">
                                        <field name="label">base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_4</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_tg_sales_non_taxable_sales_export" model="account.report.line">
                                <field name="name">5. Export Non imposable</field>
                                <field name="code">TG_EXPORT</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_tg_sales_non_taxable_export_base_tag" model="account.report.expression">
                                        <field name="label">base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_5</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_tg_sales_taxable" model="account.report.line">
                        <field name="name">6/12. Taxable operations</field>
                        <field name="code">TG_TAXABLE</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_sales_taxable_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TG_TAXABLE_18.base + TG_TAXABLE_PM.base + TG_TAXABLE_SD.base + TG_TAXABLE_EXPORT.base</field>
                            </record>
                            <record id="account_tax_report_line_tg_sales_taxable_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TG_TAXABLE_18.tax + TG_TAXABLE_PM.tax + TG_TAXABLE_SD.tax</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_tax_report_line_tg_sales_taxable_18" model="account.report.line">
                                <field name="name">7/13. At 18%</field>
                                <field name="code">TG_TAXABLE_18</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_tg_sales_taxable_18_base_tag" model="account.report.expression">
                                        <field name="label">base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_7</field>
                                    </record>
                                    <record id="account_tax_report_line_tg_sales_taxable_18_tax_tag" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_13</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_tg_sales_taxable_public_market" model="account.report.line">
                                <field name="name">8/14. Public Markets</field>
                                <field name="code">TG_TAXABLE_PM</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_tg_sales_taxable_public_market_base_tag" model="account.report.expression">
                                        <field name="label">base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_8</field>
                                    </record>
                                    <record id="account_tax_report_line_tg_sales_taxable_public_market_tax_tag" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_14</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_tg_sales_taxable_self_delivery" model="account.report.line">
                                <field name="name">9/15. Self Delivery</field>
                                <field name="code">TG_TAXABLE_SD</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_tg_sales_taxable_self_delivery_base_tag" model="account.report.expression">
                                        <field name="label">base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_9</field>
                                    </record>
                                    <record id="account_tax_report_line_tg_sales_taxable_self_delivery_tax_tag" model="account.report.expression">
                                        <field name="label">tax</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_15</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_tax_report_line_tg_sales_taxable_export" model="account.report.line">
                                <field name="name">10. Exports</field>
                                <field name="code">TG_TAXABLE_EXPORT</field>
                                <field name="expression_ids">
                                    <record id="account_tax_report_line_tg_sales_taxable_taxable_export_base_tag" model="account.report.expression">
                                        <field name="label">base</field>
                                        <field name="engine">tax_tags</field>
                                        <field name="formula">TG_10</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_tg_deductible" model="account.report.line">
                <field name="name">16. Deductible VAT</field>
                <field name="code">TG_VAT_DEDUCT</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_tax_report_line_tg_deductible_base_tag" model="account.report.expression">
                        <field name="label">base</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TG_GOODS_SERVICE.base + TG_Assets.base</field>
                    </record>
                    <record id="account_tax_report_line_tg_deductible_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TG_VAT_REPORTED.tax + TG_GOODS_SERVICE.tax + TG_Assets.tax - TG_ADD_RED.tax + TG_REIMBURSEMENT.tax</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_tax_report_line_tg_deductible_reported" model="account.report.line">
                        <field name="name">17. VAT Reported</field>
                        <field name="code">TG_VAT_REPORTED</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_deductible_reported_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TG_VAT_REPORTED._applied_carryover_tax</field>
                            </record>
                            <record id="account_tax_report_line_tg_deductible_reported_tax_tag_carryover" model="account.report.expression">
                                <field name="label">_applied_carryover_tax</field>
                                <field name="engine">external</field>
                                <field name="formula">most_recent</field>
                                <field name="date_scope">previous_tax_period</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_tg_deductible_goods_services" model="account.report.line">
                        <field name="name">18. On goods and services except assets</field>
                        <field name="code">TG_GOODS_SERVICE</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_goods_services_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">TG_18_Base</field>
                            </record>
                            <record id="account_tax_report_line_tg_goods_services_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">TG_18</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_tg_deductible_assets" model="account.report.line">
                        <field name="name">19. On assets</field>
                        <field name="code">TG_Assets</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_assets_base_tag" model="account.report.expression">
                                <field name="label">base</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">TG_19_base</field>
                            </record>
                            <record id="account_tax_report_line_tg_assets_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">TG_19</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_tg_deductible_complement" model="account.report.line">
                        <field name="name">20. Additional reduction asked</field>
                        <field name="code">TG_ADD_RED</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_complement_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_tg_deductible_reimbursement" model="account.report.line">
                        <field name="name">21. Reimbursement</field>
                        <field name="code">TG_REIMBURSEMENT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_reimbursement_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">external</field>
                                <field name="formula">sum</field>
                                <field name="subformula">editable</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_tax_report_line_tg_net_vat" model="account.report.line">
                <field name="name">23. Net VAT</field>
                <field name="code">TG_NET_VAT</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_tax_report_line_tg_net_vat_tax_tag" model="account.report.expression">
                        <field name="label">tax</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">TG_SALES.tax - TG_VAT_DEDUCT.tax</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_tax_report_line_tg_to_pay" model="account.report.line">
                        <field name="name">26. Net vat to pay</field>
                        <field name="code">TG_TO_PAY</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_to_pay_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TG_SALES.tax - TG_VAT_DEDUCT.tax</field>
                                <field name="subformula">if_above(XOF(0))</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_tax_report_line_tg_to_report" model="account.report.line">
                        <field name="name">27. Credit to report</field>
                        <field name="code">TG_REPORT</field>
                        <field name="expression_ids">
                            <record id="account_tax_report_line_tg_to_report_tax_tag" model="account.report.expression">
                                <field name="label">tax</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TG_VAT_DEDUCT.tax - TG_SALES.tax</field>
                                <field name="subformula">if_above(XOF(0))</field>
                                <field name="carryover_target" eval="False"/>
                            </record>
                            <record id="account_tax_report_line_tg_to_report_tax_carryover" model="account.report.expression">
                                <field name="label">_carryover_tax</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">TG_REPORT.tax</field>
                                <field name="carryover_target">TG_VAT_REPORTED._applied_carryover_tax</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
