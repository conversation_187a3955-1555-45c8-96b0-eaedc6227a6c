# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_tg
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-11-30 12:40+0000\n"
"PO-Revision-Date: 2023-11-30 12:40+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales
msgid "1/11. Outgoing"
msgstr "1/11. Sortant"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_taxable_export
msgid "10. Exports"
msgstr "10. Exportations"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_deductible
msgid "16. Deductible VAT"
msgstr "16. TVA Déductible"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_deductible_reported
msgid "17. VAT Reported"
msgstr "17. TVA reportée"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_deductible_goods_services
msgid "18. On goods and services except assets"
msgstr "18. Sur les biens et services exceptés les immobilisations"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_deductible_assets
msgid "19. On assets"
msgstr "19. Sur les immobilisations"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_non_taxable
msgid "2. Non Taxable operations"
msgstr "2. Opérations non taxables"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_deductible_complement
msgid "20. Additional reduction asked"
msgstr "20. Complément de déduction demandé"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_deductible_reimbursement
msgid "21. Reimbursement"
msgstr "21. Reversement à effectuer"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_net_vat
msgid "23. Net VAT"
msgstr "23. TVA Nette"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_to_pay
msgid "26. Net vat to pay"
msgstr "26. TVA nette à payer"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_to_report
msgid "27. Credit to report"
msgstr "27. Crédit à reporter"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_non_taxable_exempt
msgid "3. Exempt"
msgstr "3. Exonérées"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_non_taxable_not_imposable
msgid "4. Not imposable"
msgstr "4. Non imposées"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_non_taxable_sales_export
msgid "5. Export Non imposable"
msgstr "5. Exportations de produits non taxables"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_taxable
msgid "6/12. Taxable operations"
msgstr "6/12. Opérations taxables"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_taxable_18
msgid "7/13. At 18%"
msgstr "7/13. A 18%"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_taxable_public_market
msgid "8/14. Public Markets"
msgstr "8/14. Marchés publics avec Chèque Trésor"

#. module: l10n_tg
#: model:account.report.line,name:l10n_tg.account_tax_report_line_tg_sales_taxable_self_delivery
msgid "9/15. Self Delivery"
msgstr "9/15. Livraison à soi-meme (L.A.S.M.)"

#. module: l10n_tg
#: model:ir.model,name:l10n_tg.model_account_chart_template
msgid "Account Chart Template"
msgstr "Modèle de Plan Comptable"

#. module: l10n_tg
#: model:account.report.column,name:l10n_tg.account_tax_report_tg_balance
msgid "Base"
msgstr "Base"

#. module: l10n_tg
#. odoo-python
#: code:addons/l10n_tg/models/template_tg_syscebnl.py:0
#, python-format
msgid "SYSCEBNL for Associations"
msgstr "SYSCEBNL pour Associations"

#. module: l10n_tg
#. odoo-python
#: code:addons/l10n_tg/models/template_tg.py:0
#, python-format
msgid "SYSCOHADA for Companies"
msgstr "SYSCOHADA pour Sociétés"

#. module: l10n_tg
#: model:account.report.column,name:l10n_tg.account_tax_report_tg_tax
msgid "Tax"
msgstr "Taxe"

#. module: l10n_tg
#: model:account.report,name:l10n_tg.account_tax_report_tg
msgid "VAT Report"
msgstr "Déclaration TVA"
