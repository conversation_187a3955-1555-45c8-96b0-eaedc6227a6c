<?xml version="1.0" encoding="utf-8"?>
<odoo auto_sequence="1">
    <record id="account_financial_report_l10n_fi_pl" model="account.report">
        <field name="name">Profit and Loss</field>
        <field name="root_report_id" ref="account_reports.profit_and_loss"/>
        <field name="filter_analytic_groupby" eval="True"/>
        <field name="filter_unfold_all" eval="True"/>
        <field name="filter_journals" eval="True"/>
        <field name="country_id" ref="base.fi"/>
        <field name="filter_multi_company">selector</field>
        <field name="column_ids">
            <record id="account_financial_report_l10n_fi_pl_column" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="account_financial_report_l10n_fi_pl_line_1" model="account.report.line">
                <field name="name">A) TURNOVER</field>
                <field name="code">FIPL_1</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_1_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_1_1.balance + FIPL_1_2.balance + FIPL_1_3.balance + FIPL_1_4.balance + FIPL_1_5.balance + FIPL_1_6.balance + FIPL_1_7.balance + FIPL_1_8.balance + FIPL_1_9.balance + FIPL_1_10.balance + FIPL_1_11.balance + FIPL_1_12.balance + FIPL_1_13.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_1_1" model="account.report.line">
                        <field name="name">I. General sales accounts</field>
                        <field name="code">FIPL_1_1</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-317 - 30 - 316</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_2" model="account.report.line">
                        <field name="name">II. Sales, construction services</field>
                        <field name="code">FIPL_1_2</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-319 - 318</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_3" model="account.report.line">
                        <field name="name">III. Ancillary services</field>
                        <field name="code">FIPL_1_3</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_3_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-323 - 322 - 320 - 321</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_4" model="account.report.line">
                        <field name="name">IV. Delivery charges and installment surcharges</field>
                        <field name="code">FIPL_1_4</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_4_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-328 - 329 - 327 - 325 - 326</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_5" model="account.report.line">
                        <field name="name">V. Commission and agency</field>
                        <field name="code">FIPL_1_5</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_5_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-331 - 330 - 332</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_6" model="account.report.line">
                        <field name="name">VI. Sales of goods, Aland</field>
                        <field name="code">FIPL_1_6</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_6_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-333</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_7" model="account.report.line">
                        <field name="name">VII. Community service sales</field>
                        <field name="code">FIPL_1_7</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_7_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-334</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_8" model="account.report.line">
                        <field name="name">VIII. Community sales</field>
                        <field name="code">FIPL_1_8</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_8_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-335 - 336 - 337</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_9" model="account.report.line">
                        <field name="name">IX. Export of goods</field>
                        <field name="code">FIPL_1_9</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_9_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-338</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_10" model="account.report.line">
                        <field name="name">X. Sales of services outside the EU</field>
                        <field name="code">FIPL_1_10</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_10_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-339</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_11" model="account.report.line">
                        <field name="name">XI. Sales, second-hand goods and works of art, collectors' items and antiques</field>
                        <field name="code">FIPL_1_11</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_11_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-341 - 343 - 342 - 340</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_12" model="account.report.line">
                        <field name="name">XII. Sales, securities and real estate</field>
                        <field name="code">FIPL_1_12</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_12_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-346 - 347 - 348 - 345 - 349 - 344</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_1_13" model="account.report.line">
                        <field name="name">XIII. Adjustments to sales</field>
                        <field name="code">FIPL_1_13</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_13_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_1_13_1.balance + FIPL_1_13_2.balance + FIPL_1_13_3.balance + FIPL_1_13_4.balance + FIPL_1_13_5.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_1_13_1" model="account.report.line">
                                <field name="name">1. Sales discounts</field>
                                <field name="code">FIPL_1_13_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_1_13_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-350 - 351 - 353 - 352</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_1_13_2" model="account.report.line">
                                <field name="name">2. Indirect taxes</field>
                                <field name="code">FIPL_1_13_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_1_13_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-356 - 355</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_1_13_3" model="account.report.line">
                                <field name="name">3. Transfers and transit items</field>
                                <field name="code">FIPL_1_13_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_1_13_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-357</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_1_13_4" model="account.report.line">
                                <field name="name">4. Exchange differences on sales</field>
                                <field name="code">FIPL_1_13_4</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_1_13_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-358</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_1_13_5" model="account.report.line">
                                <field name="name">5. Other sales adjustments</field>
                                <field name="code">FIPL_1_13_5</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_1_13_5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-359</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_2" model="account.report.line">
                <field name="name">B) Increase (+) or decrease (-) of inventories of finished and work-in-progress products</field>
                <field name="code">FIPL_2</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_2_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-362 - 360 - 361</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_3" model="account.report.line">
                <field name="name">C) Manufacturing for own use (+)</field>
                <field name="code">FIPL_3</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_3_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-364 - 363</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_4" model="account.report.line">
                <field name="name">D) Other operating income</field>
                <field name="code">FIPL_4</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_4_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_4_1.balance + FIPL_4_2.balance + FIPL_4_3.balance + FIPL_4_4.balance + FIPL_4_5.balance + FIPL_4_6.balance + FIPL_4_7.balance + FIPL_4_8.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_4_1" model="account.report.line">
                        <field name="name">I. Gains on sales of fixed assets</field>
                        <field name="code">FIPL_4_1</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_4_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-366 - 368 - 367 - 369 - 365</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_4_2" model="account.report.line">
                        <field name="name">II. Leasing credits</field>
                        <field name="code">FIPL_4_2</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_4_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-370</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_4_3" model="account.report.line">
                        <field name="name">III. Insurance and damages</field>
                        <field name="code">FIPL_4_3</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_4_3_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-372 - 371 - 373</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_4_4" model="account.report.line">
                        <field name="name">IV. Rental income</field>
                        <field name="code">FIPL_4_4</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_4_4_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-378 - 379 - 375 - 377 - 376</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_4_5" model="account.report.line">
                        <field name="name">V. Grants and subsidies</field>
                        <field name="code">FIPL_4_5</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_4_5_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-384 - 382 - 381 - 380</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_4_6" model="account.report.line">
                        <field name="name">VI. Revenue from services</field>
                        <field name="code">FIPL_4_6</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_4_6_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-388 - 387 - 385 - 386 - 389</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_4_7" model="account.report.line">
                        <field name="name">VII. Fees and allowances</field>
                        <field name="code">FIPL_4_7</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_4_7_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-391 - 392 - 393 - 395 - 390 - 397</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_4_8" model="account.report.line">
                        <field name="name">VIII. Other revenue</field>
                        <field name="code">FIPL_4_8</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_4_8_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-399 - 398</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_5" model="account.report.line">
                <field name="name">E) Materials and services</field>
                <field name="code">FIPL_5</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_5_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_5a.balance + FIPL_5b.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_5_1" model="account.report.line">
                        <field name="name">I. Materials, supplies and goods</field>
                        <field name="code">FIPL_5a</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_5_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_5aa.balance + FIPL_5ab.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1" model="account.report.line">
                                <field name="name">1. Purchases during the accounting period</field>
                                <field name="code">FIPL_5aa</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_5aa_1.balance + FIPL_5aa_2.balance + FIPL_5aa_3.balance + FIPL_5aa_4.balance + FIPL_5aa_5.balance + FIPL_5aa_6.balance + FIPL_5aa_7.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_1" model="account.report.line">
                                        <field name="name">a) Purchases of materials, supplies and goods</field>
                                        <field name="code">FIPL_5aa_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-403 - 407 - 402 - 405 - 408 - 400 - 404 - 401 - 406</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_2" model="account.report.line">
                                        <field name="name">b) Purchases of goods, Åland</field>
                                        <field name="code">FIPL_5aa_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-410 - 409</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_3" model="account.report.line">
                                        <field name="name">c) Community acquisitions of goods</field>
                                        <field name="code">FIPL_5aa_3</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-411 - 412</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_4" model="account.report.line">
                                        <field name="name">d) Imports</field>
                                        <field name="code">FIPL_5aa_4</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-413 - 414</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_5" model="account.report.line">
                                        <field name="name">e) Purchases, second-hand goods and collectors' items of art and antiques</field>
                                        <field name="code">FIPL_5aa_5</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-416 - 415 - 418 - 417 - 419</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_6" model="account.report.line">
                                        <field name="name">f) Purchases, securities and real estate</field>
                                        <field name="code">FIPL_5aa_6</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-420 - 422 - 421</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7" model="account.report.line">
                                        <field name="name">g) Purchase adjustments</field>
                                        <field name="code">FIPL_5aa_7</field>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">aggregation</field>
                                                <field name="formula">FIPL_5aa_7_1.balance + FIPL_5aa_7_2.balance + FIPL_5aa_7_3.balance + FIPL_5aa_7_4.balance + FIPL_5aa_7_5.balance + FIPL_5aa_7_6.balance + FIPL_5aa_7_7.balance</field>
                                            </record>
                                        </field>
                                        <field name="children_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_1" model="account.report.line">
                                                <field name="name">a. Discounts on purchases</field>
                                                <field name="code">FIPL_5aa_7_1</field>
                                                <field name="groupby">account_id</field>
                                                <field name="hide_if_zero" eval="1"/>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_1_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-424 - 423</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_2" model="account.report.line">
                                                <field name="name">b. Returned goods</field>
                                                <field name="code">FIPL_5aa_7_2</field>
                                                <field name="groupby">account_id</field>
                                                <field name="hide_if_zero" eval="1"/>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_2_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-426</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_3" model="account.report.line">
                                                <field name="name">c. Damages and allowances</field>
                                                <field name="code">FIPL_5aa_7_3</field>
                                                <field name="groupby">account_id</field>
                                                <field name="hide_if_zero" eval="1"/>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_3_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-428 - 427</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_4" model="account.report.line">
                                                <field name="name">d. Freight, freight forwarding and other procurement costs</field>
                                                <field name="code">FIPL_5aa_7_4</field>
                                                <field name="groupby">account_id</field>
                                                <field name="hide_if_zero" eval="1"/>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_4_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-430 - 432 - 429 - 431 - 433</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_5" model="account.report.line">
                                                <field name="name">e. Transfers for purposes other than sale</field>
                                                <field name="code">FIPL_5aa_7_5</field>
                                                <field name="groupby">account_id</field>
                                                <field name="hide_if_zero" eval="1"/>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_5_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-435 - 434 - 436</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_6" model="account.report.line">
                                                <field name="name">f. Exchange differences on purchases</field>
                                                <field name="code">FIPL_5aa_7_6</field>
                                                <field name="groupby">account_id</field>
                                                <field name="hide_if_zero" eval="1"/>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_6_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-437</field>
                                                    </record>
                                                </field>
                                            </record>
                                            <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_7" model="account.report.line">
                                                <field name="name">g. Other adjustments to purchases</field>
                                                <field name="code">FIPL_5aa_7_7</field>
                                                <field name="groupby">account_id</field>
                                                <field name="hide_if_zero" eval="1"/>
                                                <field name="foldable" eval="True"/>
                                                <field name="expression_ids">
                                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_1_7_7_balance" model="account.report.expression">
                                                        <field name="label">balance</field>
                                                        <field name="engine">account_codes</field>
                                                        <field name="formula">-438 - 439</field>
                                                    </record>
                                                </field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_5_1_2" model="account.report.line">
                                <field name="name">2. Increase (+) or decrease (-) in stocks</field>
                                <field name="code">FIPL_5ab</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_5_1_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-442 - 441 - 440 - 444</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_5_2" model="account.report.line">
                        <field name="name">II. External services</field>
                        <field name="code">FIPL_5b</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_5_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_5b_1.balance + FIPL_5b_2.balance + FIPL_5b_3.balance + FIPL_5b_4.balance + FIPL_5b_5.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_5_2_1" model="account.report.line">
                                <field name="name">1. Subcontracting</field>
                                <field name="code">FIPL_5b_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_5_2_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-445</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_5_2_2" model="account.report.line">
                                <field name="name">2. Purchases of construction services</field>
                                <field name="code">FIPL_5b_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_5_2_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-446</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_5_2_3" model="account.report.line">
                                <field name="name">3. Procurement of community services</field>
                                <field name="code">FIPL_5b_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_5_2_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-447</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_5_2_4" model="account.report.line">
                                <field name="name">4. Hired labour</field>
                                <field name="code">FIPL_5b_4</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_5_2_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-448</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_5_2_5" model="account.report.line">
                                <field name="name">5. Other external services</field>
                                <field name="code">FIPL_5b_5</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_5_2_5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-449</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_6" model="account.report.line">
                <field name="name">F) Variable expenses</field>
                <field name="code">FIPL_6</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_6_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_6a.balance + FIPL_6b.balance + FIPL_6c.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_6_1" model="account.report.line">
                        <field name="name">I. Changing wages</field>
                        <field name="code">FIPL_6a</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_6_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_6a_1.balance + FIPL_6a_2.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_6a_1" model="account.report.line">
                                <field name="name">1. Wages for working time</field>
                                <field name="code">FIPL_6a_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6a_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-451 - 450 - 453 - 454 - 452</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_6a_2" model="account.report.line">
                                <field name="name">2. Holiday and social wages</field>
                                <field name="code">FIPL_6a_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6a_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-455 - 456 - 457 - 458 - 459</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_6_2" model="account.report.line">
                        <field name="name">II. Variable personnel costs</field>
                        <field name="code">FIPL_6b</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_6_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_6b_1.balance + FIPL_6b_2.balance + FIPL_6b_3.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_6_2_1" model="account.report.line">
                                <field name="name">1. Pension insurance premiums</field>
                                <field name="code">FIPL_6b_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6_2_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-460 - 463 - 462 - 461 - 464</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_6_2_2" model="account.report.line">
                                <field name="name">2. Other compulsory insurance premiums</field>
                                <field name="code">FIPL_6b_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6_2_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-465 - 468 - 466 - 467</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_6b_3" model="account.report.line">
                                <field name="name">3. Voluntary personal insurance contributions</field>
                                <field name="code">FIPL_6b_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6b_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-469</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_6c" model="account.report.line">
                        <field name="name">III. Other variable costs</field>
                        <field name="code">FIPL_6c</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_6c_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_6c_1.balance + FIPL_6c_2.balance + FIPL_6c_3.balance + FIPL_6c_4.balance + FIPL_6c_5.balance + FIPL_6c_6.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_6c_1" model="account.report.line">
                                <field name="name">1. Voluntary personal expenses</field>
                                <field name="code">FIPL_6c_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6c_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-470 - 471</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_6c_2" model="account.report.line">
                                <field name="name">2. Travel expenses</field>
                                <field name="code">FIPL_6c_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6c_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-478 - 479 - 476 - 477 - 475</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_6c_3" model="account.report.line">
                                <field name="name">3. Use and maintenance of production</field>
                                <field name="code">FIPL_6c_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6c_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-480 - 481</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_6c_4" model="account.report.line">
                                <field name="name">4. Expenditure on production facilities</field>
                                <field name="code">FIPL_6c_4</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6c_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-485 - 486</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_6c_5" model="account.report.line">
                                <field name="name">5. Vehicle expenses</field>
                                <field name="code">FIPL_6c_5</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6c_5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-490 - 491</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_6c_6" model="account.report.line">
                                <field name="name">6. Other variable costs</field>
                                <field name="code">FIPL_6c_6</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_6c_6_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-496 - 495</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_7" model="account.report.line">
                <field name="name">G) Personnel costs</field>
                <field name="code">FIPL_7</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_7_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_7a.balance + FIPL_7b.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_7_1" model="account.report.line">
                        <field name="name">I. Remuneration and fees</field>
                        <field name="code">FIPL_7a</field>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_7_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_7a_1.balance + FIPL_7a_2.balance + FIPL_7a_3.balance + FIPL_7a_4.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_7_1_1" model="account.report.line">
                                <field name="name">1. Salaries and bonuses of employees</field>
                                <field name="code">FIPL_7a_1</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_7a_1_1.balance + FIPL_7a_1_2.balance + FIPL_7a_1_3.balance + FIPL_7a_1_4.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_1_1" model="account.report.line">
                                        <field name="name">a) Normal wages during working hours</field>
                                        <field name="code">FIPL_7a_1_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_1_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-50</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_1_2" model="account.report.line">
                                        <field name="name">b) Supplements and allowances</field>
                                        <field name="code">FIPL_7a_1_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_1_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-51</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_1_3" model="account.report.line">
                                        <field name="name">c) Fees and commissions</field>
                                        <field name="code">FIPL_7a_1_3</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_1_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-52</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_1_4" model="account.report.line">
                                        <field name="name">d) Holiday and social wages</field>
                                        <field name="code">FIPL_7a_1_4</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_1_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-53</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_1_5" model="account.report.line">
                                        <field name="name">e) Benefits in kind</field>
                                        <field name="code">FIPL_7a_1_5</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_1_5_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-541 - 540 - 543 - 544 - 542</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_1_6" model="account.report.line">
                                        <field name="name">f) Salary reimbursements received</field>
                                        <field name="code">FIPL_7a_1_6</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_1_6_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-549 - 547 - 548</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_7_1_2" model="account.report.line">
                                <field name="name">2. Management salaries</field>
                                <field name="code">FIPL_7a_2</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_7a_2_1.balance + FIPL_7a_2_2.balance + FIPL_7a_2_3.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_2_1" model="account.report.line">
                                        <field name="name">a) Management salaries and fees</field>
                                        <field name="code">FIPL_7a_2_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_2_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-56</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_2_2" model="account.report.line">
                                        <field name="name">b) Management benefits in kind</field>
                                        <field name="code">FIPL_7a_2_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_2_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-570 - 574 - 571 - 572 - 573</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_2_3" model="account.report.line">
                                        <field name="name">c) Compensation received for management salaries</field>
                                        <field name="code">FIPL_7a_2_3</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_2_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-578 - 577 - 579</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_7_1_3" model="account.report.line">
                                <field name="name">3. Salaries of shareholders and relatives</field>
                                <field name="code">FIPL_7a_3</field>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_7a_3_1.balance + FIPL_7a_3_2.balance + FIPL_7a_3_3.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_3_1" model="account.report.line">
                                        <field name="name">a) Salaries and fees of shareholders and relatives</field>
                                        <field name="code">FIPL_7a_3_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_3_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-58</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_3_2" model="account.report.line">
                                        <field name="name">b) Benefits in kind for shareholders and relatives</field>
                                        <field name="code">FIPL_7a_3_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_3_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-590 - 591 - 593 - 594 - 592</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_3_3" model="account.report.line">
                                        <field name="name">c) Compensation received for the salaries of shareholders and relatives</field>
                                        <field name="code">FIPL_7a_3_3</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_1_3_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-598 - 597 - 596</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_7_1_4" model="account.report.line">
                                <field name="name">4. Counterpart account for benefits in kind</field>
                                <field name="code">FIPL_7a_4</field>
                                <field name="groupby">account_id</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_1_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-599</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_7_2" model="account.report.line">
                        <field name="name">II. Staff expenses</field>
                        <field name="code">FIPL_7b</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_7_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_7b_1.balance + FIPL_7b_2.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_7_2_1" model="account.report.line">
                                <field name="name">1. Pension expenses</field>
                                <field name="code">FIPL_7b_1</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_7b_1_1.balance + FIPL_7b_1_2.balance + FIPL_7b_1_3.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_1_1" model="account.report.line">
                                        <field name="name">a) Pensions paid</field>
                                        <field name="code">FIPL_7b_1_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_2_1_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-60</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_1_2" model="account.report.line">
                                        <field name="name">b) Pension insurance contributions</field>
                                        <field name="code">FIPL_7b_1_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_2_1_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-625 - 627 - 624 - 61 - 628</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_1_3" model="account.report.line">
                                        <field name="name">c) Periodicity during the financial year</field>
                                        <field name="code">FIPL_7b_1_3</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_2_1_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-629</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_7_2_2" model="account.report.line">
                                <field name="name">2. Other personnel costs</field>
                                <field name="code">FIPL_7b_2</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_7b_2_1.balance + FIPL_7b_2_2.balance + FIPL_7b_2_3.balance + FIPL_7b_2_4.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_2_1" model="account.report.line">
                                        <field name="name">a) Social security contributions</field>
                                        <field name="code">FIPL_7b_2_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_2_2_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-63</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_2_2" model="account.report.line">
                                        <field name="name">b) Compulsory insurance premiums</field>
                                        <field name="code">FIPL_7b_2_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_2_2_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-64</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_2_3" model="account.report.line">
                                        <field name="name">c) Other staff insurance contributions</field>
                                        <field name="code">FIPL_7b_2_3</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_2_2_3_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-65</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_7_2_2_4" model="account.report.line">
                                        <field name="name">d) Periodicity during the financial year</field>
                                        <field name="code">FIPL_7b_2_4</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_7_2_2_4_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-66</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_8" model="account.report.line">
                <field name="name">H) Depreciation, amortisation and impairment</field>
                <field name="code">FIPL_8</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_8_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_8a.balance + FIPL_8b.balance + FIPL_8c.balance + FIPL_8c.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_8_1" model="account.report.line">
                        <field name="name">I. Depreciation according to plan</field>
                        <field name="code">FIPL_8a</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_8_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-68</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_8_2" model="account.report.line">
                        <field name="name">II. Amortisation of consolidated goodwill and reduction of consolidated reserves</field>
                        <field name="code">FIPL_8b</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_8_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-694 - 693</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_8_3" model="account.report.line">
                        <field name="name">III. Impairment of fixed assets</field>
                        <field name="code">FIPL_8c</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_8_3_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-696 - 698 - 697 - 695</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_8_4" model="account.report.line">
                        <field name="name">IV. Extraordinary impairments of current assets</field>
                        <field name="code">FIPL_8d</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_8_4_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-699</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_9" model="account.report.line">
                <field name="name">I) Other operating expenses</field>
                <field name="code">FIPL_9</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_9_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_9_1.balance + FIPL_9_2.balance + FIPL_9_3.balance + FIPL_9_4.balance + FIPL_9_5.balance + FIPL_9_6.balance + FIPL_9_7.balance + FIPL_9_8.balance + FIPL_9_9.balance + FIPL_9_10.balance + FIPL_9_11.balance + FIPL_9_12.balance + FIPL_9_13.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_9_1" model="account.report.line">
                        <field name="name">I. Voluntary expenditure on staff</field>
                        <field name="code">FIPL_9_1</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_9_1_1.balance + FIPL_9_1_2.balance + FIPL_9_1_3.balance + FIPL_9_1_4.balance + FIPL_9_1_5.balance + FIPL_9_1_6.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_1_1" model="account.report.line">
                                <field name="name">1. Staff training</field>
                                <field name="code">FIPL_9_1_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_1_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-700</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_1_2" model="account.report.line">
                                <field name="name">2. Staff meetings and recreation</field>
                                <field name="code">FIPL_9_1_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_1_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-701 - 703 - 704 - 702</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_1_3" model="account.report.line">
                                <field name="name">3. Occupational health care</field>
                                <field name="code">FIPL_9_1_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_1_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-706 - 705</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_1_4" model="account.report.line">
                                <field name="name">4. Providing food and coffee for staff</field>
                                <field name="code">FIPL_9_1_4</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_1_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-707 - 709 - 711 - 710 - 708</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_1_5" model="account.report.line">
                                <field name="name">5. Work clothing and protective equipment</field>
                                <field name="code">FIPL_9_1_5</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_1_5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-713 - 712</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_1_6" model="account.report.line">
                                <field name="name">6. Other voluntary personal expenses</field>
                                <field name="code">FIPL_9_1_6</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_1_6_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-717 - 714 - 716 - 715</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_2" model="account.report.line">
                        <field name="name">II. Premises expenses</field>
                        <field name="code">FIPL_9_2</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_9_2_1.balance + FIPL_9_2_2.balance + FIPL_9_2_3.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_2_1" model="account.report.line">
                                <field name="name">1. Rents and consideration</field>
                                <field name="code">FIPL_9_2_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_2_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-72 - 730 - 732 - 731</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_2_2" model="account.report.line">
                                <field name="name">2. Treatment costs</field>
                                <field name="code">FIPL_9_2_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_2_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-738 - 743 - 736 - 734 - 744 - 740 - 745 - 733 - 741 - 739 - 735 - 742 - 737</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_2_3" model="account.report.line">
                                <field name="name">3. Other office expenses</field>
                                <field name="code">FIPL_9_2_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_2_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-747 - 746 - 748</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_3" model="account.report.line">
                        <field name="name">III. Vehicle expenses</field>
                        <field name="code">FIPL_9_3</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_3_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-763 - 75 - 761 - 760 - 762</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_4" model="account.report.line">
                        <field name="name">IV. Computer equipment and software costs</field>
                        <field name="code">FIPL_9_4</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_4_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-766 - 765 - 769 - 767 - 768 - 764 - 770</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_5" model="account.report.line">
                        <field name="name">V. Other machinery and equipment costs</field>
                        <field name="code">FIPL_9_5</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_5_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-77\(770)</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_6" model="account.report.line">
                        <field name="name">VI. Travel expenses</field>
                        <field name="code">FIPL_9_6</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_6_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_9_6_1.balance + FIPL_9_6_2.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_6_1" model="account.report.line">
                                <field name="name">1. Air tickets, accommodation and other travel expenses</field>
                                <field name="code">FIPL_9_6_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_6_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-783 - 781 - 780 - 786 - 782 - 784 - 785</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_6_2" model="account.report.line">
                                <field name="name">2. Reimbursement of travel expenses</field>
                                <field name="code">FIPL_9_6_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_6_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-788 - 787 - 789 - 790 - 792 - 791</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_7" model="account.report.line">
                        <field name="name">VII. Representation expenses</field>
                        <field name="code">FIPL_9_7</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_7_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-796 - 795 - 799 - 798 - 797</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_8" model="account.report.line">
                        <field name="name">VIII. Costs of sale</field>
                        <field name="code">FIPL_9_8</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_8_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_9_8_1.balance + FIPL_9_8_2.balance + FIPL_9_8_3.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_8_1" model="account.report.line">
                                <field name="name">1. Commissions paid</field>
                                <field name="code">FIPL_9_8_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_8_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-801 - 800</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_8_2" model="account.report.line">
                                <field name="name">2. Sales freight and handling</field>
                                <field name="code">FIPL_9_8_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_8_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-803 - 802</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_8_3" model="account.report.line">
                                <field name="name">3. Other selling expenses</field>
                                <field name="code">FIPL_9_8_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_8_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-804</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_9" model="account.report.line">
                        <field name="name">IX. Marketing expenses</field>
                        <field name="code">FIPL_9_9</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_9_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_9_9_1.balance + FIPL_9_9_2.balance + FIPL_9_9_3.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_9_1" model="account.report.line">
                                <field name="name">1. Advertising</field>
                                <field name="code">FIPL_9_9_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_9_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-812 - 807 - 808 - 805 - 811 - 814 - 810 - 813 - 806 - 809</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_9_2" model="account.report.line">
                                <field name="name">2. Promoting sales</field>
                                <field name="code">FIPL_9_9_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_9_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-823 - 824 - 818 - 819 - 821 - 815 - 820 - 817 - 816 - 822</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_9_3" model="account.report.line">
                                <field name="name">3. Relationship activities</field>
                                <field name="code">FIPL_9_9_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_9_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-828 - 825 - 827 - 826</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_10" model="account.report.line">
                        <field name="name">X. Research and development costs</field>
                        <field name="code">FIPL_9_10</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_10_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-833 - 831 - 835 - 832 - 830 - 834</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_11" model="account.report.line">
                        <field name="name">XI. Administrative costs</field>
                        <field name="code">FIPL_9_11</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_11_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_9_11_1.balance + FIPL_9_11_2.balance + FIPL_9_11_3.balance + FIPL_9_11_4.balance + FIPL_9_11_5.balance + FIPL_9_11_6.balance + FIPL_9_11_7.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_11_1" model="account.report.line">
                                <field name="name">1. Administrative services</field>
                                <field name="code">FIPL_9_11_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_11_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-842 - 837 - 839 - 843 - 844 - 841 - 838</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_11_2" model="account.report.line">
                                <field name="name">2. Gathering information</field>
                                <field name="code">FIPL_9_11_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_11_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-848 - 847 - 846 - 849 - 845</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_11_3" model="account.report.line">
                                <field name="name">3. Information and financial communication</field>
                                <field name="code">FIPL_9_11_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_11_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-851 - 855 - 853 - 854 - 856 - 857 - 852 - 850</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_11_4" model="account.report.line">
                                <field name="name">4. Insurance and compensation</field>
                                <field name="code">FIPL_9_11_4</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_11_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-861 - 858 - 859 - 860</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_11_5" model="account.report.line">
                                <field name="name">5. Office supplies</field>
                                <field name="code">FIPL_9_11_5</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_11_5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-864 - 863 - 862</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_11_6" model="account.report.line">
                                <field name="name">6. Other administrative expenses</field>
                                <field name="code">FIPL_9_11_6</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_11_6_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-865 - 868</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_11_7" model="account.report.line">
                                <field name="name">7. Received administrative expense allowances</field>
                                <field name="code">FIPL_9_11_7</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_11_7_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-869</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_12" model="account.report.line">
                        <field name="name">XII. Other operating expenses</field>
                        <field name="code">FIPL_9_12</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_12_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_9_12_1.balance + FIPL_9_12_2.balance + FIPL_9_12_3.balance + FIPL_9_12_4.balance + FIPL_9_12_5.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_12_1" model="account.report.line">
                                <field name="name">1. Credit losses</field>
                                <field name="code">FIPL_9_12_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_12_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-871 - 873 - 872</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_12_2" model="account.report.line">
                                <field name="name">2. Other operating expenses</field>
                                <field name="code">FIPL_9_12_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_12_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-870</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_12_3" model="account.report.line">
                                <field name="name">3. Other deductible business expenses</field>
                                <field name="code">FIPL_9_12_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_12_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-874 - 875 - 876</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_12_4" model="account.report.line">
                                <field name="name">4. Other non-deductible operating expenses</field>
                                <field name="code">FIPL_9_12_4</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_12_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-877 - 878 - 879 - 880 - 884</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_9_12_5" model="account.report.line">
                                <field name="name">5. Losses on disposal of fixed assets</field>
                                <field name="code">FIPL_9_12_5</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_9_12_5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-885 - 886 - 887</field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_9_13" model="account.report.line">
                        <field name="name">XIII. Reconciliation differences</field>
                        <field name="code">FIPL_9_13</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_9_13_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-889</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_10" model="account.report.line">
                <field name="name">J) Share of the profit (loss) of associated companies</field>
                <field name="code">FIPL_10</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_10_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-89</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_11" model="account.report.line">
                <field name="name">K) OPERATING PROFIT (LOSS)</field>
                <field name="code">FIPL_11</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_11_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_1.balance + FIPL_2.balance + FIPL_3.balance + FIPL_4.balance + FIPL_5.balance + FIPL_6.balance + FIPL_7.balance + FIPL_8.balance + FIPL_9.balance + FIPL_10.balance</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_12" model="account.report.line">
                <field name="name">L) Financial income and expenses</field>
                <field name="code">FIPL_12</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_12_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_12_1.balance + FIPL_12_2.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_12_1" model="account.report.line">
                        <field name="name">I. Financial income</field>
                        <field name="code">FIPL_12_1</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_12_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_12_1_1.balance + FIPL_12_1_2.balance + FIPL_12_1_3.balance + FIPL_12_1_4.balance + FIPL_12_1_5.balance + FIPL_12_1_6.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_12_1_1" model="account.report.line">
                                <field name="name">1. Income from shares in companies of the same group</field>
                                <field name="code">FIPL_12_1_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-902 - 900 - 901</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_12_1_2" model="account.report.line">
                                <field name="name">2. Share of profit (loss) of associates</field>
                                <field name="code">FIPL_12_1_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-903</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_12_1_3" model="account.report.line">
                                <field name="name">3. Income from participating interests in associated enterprises</field>
                                <field name="code">FIPL_12_1_3</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-905 - 906 - 904</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_12_1_4" model="account.report.line">
                                <field name="name">4. Income from shares in other participating interests</field>
                                <field name="code">FIPL_12_1_4</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_4_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-907</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_12_1_5" model="account.report.line">
                                <field name="name">5. Income from other fixed assets</field>
                                <field name="code">FIPL_12_1_5</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_5_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_12_1_5_1.balance + FIPL_12_1_5_2.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_5_1" model="account.report.line">
                                        <field name="name">a) From companies within the same group</field>
                                        <field name="code">FIPL_12_1_5_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_12_1_5_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-908</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_5_2" model="account.report.line">
                                        <field name="name">b) From others</field>
                                        <field name="code">FIPL_12_1_5_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_12_1_5_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-909 - 913 - 914 - 910 - 912</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_12_1_6" model="account.report.line">
                                <field name="name">6. Other interest and financial income</field>
                                <field name="code">FIPL_12_1_6</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_6_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_12_1_6_1.balance + FIPL_12_1_6_2.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_6_1" model="account.report.line">
                                        <field name="name">a) From companies within the same group</field>
                                        <field name="code">FIPL_12_1_6_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_12_1_6_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-915</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_12_1_6_2" model="account.report.line">
                                        <field name="name">b) From others</field>
                                        <field name="code">FIPL_12_1_6_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_12_1_6_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-92 - 916 - 918 - 919 - 917</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_12_2" model="account.report.line">
                        <field name="name">II. Financial charges</field>
                        <field name="code">FIPL_12_2</field>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_12_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">FIPL_12_2_1.balance + FIPL_12_2_2.balance + FIPL_12_2_3.balance</field>
                            </record>
                        </field>
                        <field name="children_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_12_2_1" model="account.report.line">
                                <field name="name">1. Impairment of fixed assets investments</field>
                                <field name="code">FIPL_12_2_1</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_2_1_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-934 - 933 - 930 - 931 - 932 - 935 - 936</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_12_2_2" model="account.report.line">
                                <field name="name">2. Impairment losses on financial securities held as current assets</field>
                                <field name="code">FIPL_12_2_2</field>
                                <field name="groupby">account_id</field>
                                <field name="hide_if_zero" eval="1"/>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_2_2_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">account_codes</field>
                                        <field name="formula">-941 - 939 - 937 - 940 - 938</field>
                                    </record>
                                </field>
                            </record>
                            <record id="account_financial_report_l10n_fi_pl_line_12_2_3" model="account.report.line">
                                <field name="name">3. Interest and other financial charges</field>
                                <field name="code">FIPL_12_2_3</field>
                                <field name="foldable" eval="True"/>
                                <field name="expression_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_2_3_balance" model="account.report.expression">
                                        <field name="label">balance</field>
                                        <field name="engine">aggregation</field>
                                        <field name="formula">FIPL_12_2_3_1.balance + FIPL_12_2_3_2.balance</field>
                                    </record>
                                </field>
                                <field name="children_ids">
                                    <record id="account_financial_report_l10n_fi_pl_line_12_2_3_1" model="account.report.line">
                                        <field name="name">a) For companies within the same group</field>
                                        <field name="code">FIPL_12_2_3_1</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_12_2_3_1_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-943 - 942</field>
                                            </record>
                                        </field>
                                    </record>
                                    <record id="account_financial_report_l10n_fi_pl_line_12_2_3_2" model="account.report.line">
                                        <field name="name">b) For others</field>
                                        <field name="code">FIPL_12_2_3_2</field>
                                        <field name="groupby">account_id</field>
                                        <field name="hide_if_zero" eval="1"/>
                                        <field name="foldable" eval="True"/>
                                        <field name="expression_ids">
                                            <record id="account_financial_report_l10n_fi_pl_line_12_2_3_2_balance" model="account.report.expression">
                                                <field name="label">balance</field>
                                                <field name="engine">account_codes</field>
                                                <field name="formula">-947 - 945 - 944 - 949 - 96 - 946 - 95 - 948</field>
                                            </record>
                                        </field>
                                    </record>
                                </field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_13" model="account.report.line">
                <field name="name">M) Incidental items</field>
                <field name="code">FIPL_13</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_13_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">domain</field>
                        <field name="formula" eval="[('account_id.account_type', '=', 'equity_unaffected')]"/>
                        <field name="subformula">-sum</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_14" model="account.report.line">
                <field name="name">N) PROFIT (LOSS) BEFORE ACCOUNTING TRANSFERS AND TAXES</field>
                <field name="code">FIPL_14</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_14_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_11.balance + FIPL_12.balance + FIPL_13.balance</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_15" model="account.report.line">
                <field name="name">O) Balance sheet transfers</field>
                <field name="code">FIPL_15</field>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_15_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_15_1.balance + FIPL_15_2.balance + FIPL_15_3.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_15_1" model="account.report.line">
                        <field name="name">I. Change in the depreciation difference</field>
                        <field name="code">FIPL_15_1</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_15_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-981 - 980 - 983 - 982</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_15_2" model="account.report.line">
                        <field name="name">II. Change in tax provisions</field>
                        <field name="code">FIPL_15_2</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_15_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-987 - 984 - 985 - 986 - 9890 - 988</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_15_3" model="account.report.line">
                        <field name="name">III. Group support</field>
                        <field name="code">FIPL_15_3</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_15_3_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-9895 - 9891</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_16" model="account.report.line">
                <field name="name">P) Income taxes</field>
                <field name="code">FIPL_16</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_16_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">aggregation</field>
                        <field name="formula">FIPL_16_1.balance + FIPL_16_2.balance</field>
                    </record>
                </field>
                <field name="children_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_16_1" model="account.report.line">
                        <field name="name">I. Taxes for the financial year</field>
                        <field name="code">FIPL_16_1</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_16_1_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-996 - 993 - 994 - 991 - 990 - 995</field>
                            </record>
                        </field>
                    </record>
                    <record id="account_financial_report_l10n_fi_pl_line_16_2" model="account.report.line">
                        <field name="name">II. Deferred taxes</field>
                        <field name="code">FIPL_16_2</field>
                        <field name="groupby">account_id</field>
                        <field name="hide_if_zero" eval="1"/>
                        <field name="foldable" eval="True"/>
                        <field name="expression_ids">
                            <record id="account_financial_report_l10n_fi_pl_line_16_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">account_codes</field>
                                <field name="formula">-997</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_17" model="account.report.line">
                <field name="name">Q) Other direct taxes</field>
                <field name="code">FIPL_17</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_17_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-998</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_18" model="account.report.line">
                <field name="name">R) Minority shareholdings</field>
                <field name="code">FIPL_18</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_18_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">account_codes</field>
                        <field name="formula">-999</field>
                    </record>
                </field>
            </record>
            <record id="account_financial_report_l10n_fi_pl_line_19" model="account.report.line">
                <field name="name">S) PROFIT (LOSS) FOR THE FINANCIAL YEAR</field>
                <field name="code">FIPL_19</field>
                <field name="groupby">account_id</field>
                <field name="foldable" eval="True"/>
                <field name="hierarchy_level">0</field>
                <field name="expression_ids">
                    <record id="account_financial_report_l10n_fi_pl_line_19_balance" model="account.report.expression">
                        <field name="label">balance</field>
                        <field name="engine">domain</field>
                        <field name="formula" eval="[('account_id.account_type', 'in', ['income', 'income_other', 'expense', 'expense_depreciation', 'expense_direct_cost'])]"/>
                        <field name="subformula">-sum</field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
