# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_fi_reports
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.2alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-26 14:49+0000\n"
"PO-Revision-Date: 2023-01-26 14:49+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_11_1
msgid "1. Administrative services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_9_1
msgid "1. Advertising"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_6_1
msgid "1. Air tickets, accommodation and other travel expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_1
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_1
msgid "1. Capital loans"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_8_1
msgid "1. Commissions paid"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_12_1
msgid "1. Credit losses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_1_1
msgid "1. Development costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_2_1
msgid "1. Impairment of fixed assets investments"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_1
msgid "1. Income from shares in companies of the same group"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_5_1
msgid "1. Invested unrestricted equity fund"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_1
msgid "1. Land and water areas"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1
msgid "1. Long-term"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_1_1
msgid "1. Materials and supplies"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_4_a
msgid "1. Money"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_1
msgid "1. Pension expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6_2_1
msgid "1. Pension insurance premiums"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_1
msgid "1. Profit (loss) of previous financial periods"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1
msgid "1. Purchases during the accounting period"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_2_1
msgid "1. Rents and consideration"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_1
msgid "1. Salaries and bonuses of employees"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_13_1
msgid "1. Sales discounts"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_1_a
msgid "1. Share capital"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_3_1
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_3_1
msgid "1. Shares in companies of the same group"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_1_1
msgid "1. Staff training"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_2_1
msgid "1. Subcontracting"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6c_1
msgid "1. Voluntary personal expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6a_1
msgid "1. Wages for working time"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_10
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_10
msgid "10. Liabilities to affiliated companies"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_11
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_11
msgid "11. Deferred tax liabilities"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_12
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_12
msgid "12. Other liabilities"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_13
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_13
msgid "13. Accruals and deferred income"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_2
msgid "2. Accounts of a limited company"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_4_b
msgid "2. Bank receivables"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_2
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_2
msgid "2. Bonds and notes"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_2
msgid "2. Buildings and structures"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_5_2
msgid "2. Funds under the Articles of Association"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_11_2
msgid "2. Gathering information"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6a_2
msgid "2. Holiday and social wages"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_2_2
msgid "2. Impairment losses on financial securities held as current assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_2
msgid "2. Increase (+) or decrease (-) in stocks"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_1_b
msgid "2. Increase in share capital"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_13_2
msgid "2. Indirect taxes"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_1_2
msgid "2. Intangible rights"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_2
msgid "2. Management salaries"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6_2_2
msgid "2. Other compulsory insurance premiums"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_12_2
msgid "2. Other deductible business expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_2
msgid "2. Other personnel costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_3_2
msgid "2. Other shares and participations"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_9_2
msgid "2. Promoting sales"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_2_2
msgid "2. Purchases of construction services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_3_2
msgid "2. Receivables from companies in the same group"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_6_2
msgid "2. Reimbursement of travel expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_8_2
msgid "2. Sales freight and handling"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_2
msgid "2. Share of profit (loss) of associates"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_2
msgid "2. Short-term"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_1_2
msgid "2. Staff meetings and recreation"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6c_2
msgid "2. Travel expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_2_2
msgid "2. Treatment costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_1_2
msgid "2. Work in progress"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_3
msgid "3. Accounts of the cooperative"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_3
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_3
msgid "3. Convertible bonds"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_1_3
msgid "3. Finished products"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_1_3
msgid "3. Goodwill"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_3_3
msgid "3. Holdings in associated enterprises"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_3
msgid "3. Income from participating interests in associated enterprises"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_11_3
msgid "3. Information and financial communication"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_2_3
msgid "3. Interest and other financial charges"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_3
msgid "3. Machinery and equipment"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_1_3
msgid "3. Occupational health care"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_5_3
msgid "3. Other funds (oy)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_12_3
msgid "3. Other non-deductible operating expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_2_3
msgid "3. Other office expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_3_3
msgid "3. Other securities"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_8_3
msgid "3. Other selling expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_4_c
msgid "3. Payment transactions and reconciliations"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_2_3
msgid "3. Procurement of community services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_9_3
msgid "3. Relationship activities"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_3
msgid "3. Salaries of shareholders and relatives"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_13_3
msgid "3. Transfers and transit items"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6c_3
msgid "3. Use and maintenance of production"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6b_3
msgid "3. Voluntary personal insurance contributions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_1_4
msgid "4. Consolidated goodwill"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_4
msgid "4. Counterpart account for benefits in kind"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_13_4
msgid "4. Exchange differences on sales"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6c_4
msgid "4. Expenditure on production facilities"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_1_4
msgid "4. Goods"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_2_4
msgid "4. Hired labour"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_4
msgid "4. Income from shares in other participating interests"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_11_4
msgid "4. Insurance and compensation"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_4
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_4
msgid "4. Loans from financial institutions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_12_4
msgid "4. Losses on disposal of fixed assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_4
msgid "4. Other tangible assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_1_4
msgid "4. Providing food and coffee for staff"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_3_4
msgid "4. Receivables from affiliated enterprises"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_4
msgid "4. The accounts of the general partnership"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_5
msgid "5. Advances and work in progress"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_5
msgid "5. Income from other fixed assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_5
msgid "5. Limited partnership accounts"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_11_5
msgid "5. Office supplies"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_1_5
msgid "5. Other current assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_2_5
msgid "5. Other external services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_1_5
msgid "5. Other intangible assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_13_5
msgid "5. Other sales adjustments"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_3_5
msgid "5. Other shares and participations"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_5
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_5
msgid "5. Pension loans"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6c_5
msgid "5. Vehicle expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_1_5
msgid "5. Work clothing and protective equipment"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_6
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_6
msgid "6. Advances received"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_6
msgid "6. Capital deficiency from previous accounting periods"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_11_6
msgid "6. Other administrative expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_6
msgid "6. Other interest and financial income"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_3_6
msgid "6. Other receivables"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6c_6
msgid "6. Other variable costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_1_6
msgid "6. Other voluntary personal expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_1_6
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_1_6
msgid "6. Prepayments"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_7
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_7
msgid "7. Accounts payable"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_7
msgid "7. Private accounts in the accounting period"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_11_7
msgid "7. Received administrative expense allowances"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_8
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_8
msgid "8. Financial bills"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_8
msgid "8. Profit (loss) for the accounting period"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23_9
msgid "9. Capital loans"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1_9
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_9
msgid "9. Liabilities to companies of the same group"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1
msgid "A) EQUITY"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1
msgid "A) PERMANENT RESPONSIBILITIES"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1
msgid "A) TURNOVER"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2
msgid "B) CHANGING RESPONSIBILITIES"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_2
msgid ""
"B) Increase (+) or decrease (-) of inventories of finished and work-in-"
"progress products"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_2
msgid "B) MINORITY INTERESTS"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.column,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_column
#: model:account.report.column,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_column
msgid "Balance"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report,name:l10n_fi_reports.account_financial_report_l10n_fi_bs
msgid "Balance Sheet"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1
msgid "Balance sheet Corresponding"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2
msgid "Balance sheet Liabilities"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_3
msgid "C) ACCUMULATION OF ACCOUNTING TRANSFER"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_3
msgid "C) Manufacturing for own use (+)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_4
msgid "D) COMPULSORY RESERVES"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4
msgid "D) Other operating income"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_5
msgid "E) GROUP RESERVE"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5
msgid "E) Materials and services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6
msgid "F) DEBT CAPITAL"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6
msgid "F) Variable expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7
msgid "G) Personnel costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_8
msgid "H) Depreciation, amortisation and impairment"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9
msgid "I) Other operating expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_15_1
msgid "I. Change in the depreciation difference"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6_1
msgid "I. Changing wages"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_8_1
msgid "I. Depreciation according to plan"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1
msgid "I. Financial income"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4_1
msgid "I. Gains on sales of fixed assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_1
msgid "I. General sales accounts"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_1
msgid "I. Intangible assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_1
msgid "I. Inventories"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_1
msgid "I. Long-term"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1
msgid "I. Materials, supplies and goods"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_4_1
msgid "I. Pension reservations"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1
msgid "I. Remuneration and fees"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_1
msgid "I. Share capital"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_16_1
msgid "I. Taxes for the financial year"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_1
msgid "I. Voluntary expenditure on staff"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_3_1
msgid "I. Withdrawal"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_8_2
msgid ""
"II. Amortisation of consolidated goodwill and reduction of consolidated "
"reserves"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_15_2
msgid "II. Change in tax provisions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_16_2
msgid "II. Deferred taxes"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_2
msgid "II. External services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_2
msgid "II. Financial charges"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4_2
msgid "II. Leasing credits"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_2
msgid "II. Premises expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2
msgid "II. Receivables"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_2
msgid "II. Sales, construction services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_2
msgid "II. Share premium fund (oy)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2
msgid "II. Short-term"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2
msgid "II. Staff expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2
msgid "II. Tangible fixed assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_4_2
msgid "II. Tax provisions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6_2
msgid "II. Variable personnel costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_3_2
msgid "II. Voluntary reservations"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_3
msgid "III. Ancillary services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_3
msgid "III. Financial securities"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_15_3
msgid "III. Group support"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_8_3
msgid "III. Impairment of fixed assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4_3
msgid "III. Insurance and damages"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_3
msgid "III. Investments"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_4_3
msgid "III. Other compulsory provisions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_6c
msgid "III. Other variable costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_3
msgid "III. Stock appreciation fund (oy)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_3
msgid "III. Vehicle expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_4
msgid "IV. Computer equipment and software costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_4
msgid "IV. Delivery charges and installment surcharges"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_8_4
msgid "IV. Extraordinary impairments of current assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_4
msgid "IV. Fair value fund (oy)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_4
msgid "IV. Money and bank receivables"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4_4
msgid "IV. Rental income"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_9
msgid "IX. Export of goods"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_9
msgid "IX. Marketing expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_9
msgid "IX. Value appreciation fund (OSK)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_10
msgid "J) Share of the profit (loss) of associated companies"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_11
msgid "K) OPERATING PROFIT (LOSS)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12
msgid "L) Financial income and expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_13
msgid "M) Incidental items"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_14
msgid "N) PROFIT (LOSS) BEFORE ACCOUNTING TRANSFERS AND TAXES"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_15
msgid "O) Balance sheet transfers"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_16
msgid "P) Income taxes"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report,name:l10n_fi_reports.account_financial_report_l10n_fi_pl
msgid "Profit and Loss"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_17
msgid "Q) Other direct taxes"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_18
msgid "R) Minority shareholdings"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_19
msgid "S) PROFIT (LOSS) FOR THE FINANCIAL YEAR"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_5
msgid "V. Commission and agency"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4_5
msgid "V. Grants and subsidies"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_5
msgid "V. Other machinery and equipment costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_5
msgid "V. Reserve Fund (oy)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_6
msgid "VI. Other funds"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4_6
msgid "VI. Revenue from services"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_6
msgid "VI. Sales of goods, Aland"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_6
msgid "VI. Travel expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_7
msgid "VII. Community service sales"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_7
msgid "VII. Equity capital"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4_7
msgid "VII. Fees and allowances"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_7
msgid "VII. Representation expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_8
msgid "VIII. Community sales"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_8
msgid "VIII. Costs of sale"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_4_8
msgid "VIII. Other revenue"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_8
msgid "VIII. Share premium fund (osk)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_10
msgid "X. Fair value reserve (osk)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_10
msgid "X. Research and development costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_10
msgid "X. Sales of services outside the EU"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_11
msgid "XI. Administrative costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_11
msgid "XI. Reserve Fund (osk)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_11
msgid ""
"XI. Sales, second-hand goods and works of art, collectors' items and "
"antiques"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_12
msgid "XII. Other operating expenses"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_12
msgid "XII. Sales, securities and real estate"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_12
msgid "XII. Statutory funds"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_1_13
msgid "XIII. Adjustments to sales"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_13
msgid "XIII. Other funds (osk)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_9_13
msgid "XIII. Reconciliation differences"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_14
msgid "XIV. Capital contributions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_19
msgid "XIX. Fair value reserve (FVA)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_15
msgid "XV. Remuneration fund (ay)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_16
msgid "XVI. Fair value reserve (ay)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_17
msgid "XVII. Capital contributions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_18
msgid "XVIII. Value Adjustment Fund (ky)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_20
msgid "XX. Basic capital"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_21
msgid "XXI. Capital appreciation reserve (RDF)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_22
msgid "XXII. Fair value reserve (tmi)"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_1_23
msgid "XXIII. Common equity accounts"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_13_1
msgid "a) Deferred income"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_2_3_1
msgid "a) For companies within the same group"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_5_1
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_6_1
msgid "a) From companies within the same group"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_2_1
msgid "a) Management salaries and fees"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_1_1
msgid "a) Normal wages during working hours"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_1_a
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_2_a
msgid "a) Owned"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_1_1
msgid "a) Pensions paid"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_1
msgid "a) Purchases of materials, supplies and goods"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_3_1
msgid "a) Salaries and fees of shareholders and relatives"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_2_1
msgid "a) Social security contributions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1_1
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_2_1
msgid "a) Trade receivables"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_7_1
msgid "a. Discounts on purchases"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_2_8_1
msgid "a. Expenditure forecasts"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_3_2
msgid "b) Benefits in kind for shareholders and relatives"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_2_2
msgid "b) Compulsory insurance premiums"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_13_2
msgid "b) Expenditure"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_2_3_2
msgid "b) For others"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_5_2
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_12_1_6_2
msgid "b) From others"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_2_2
msgid "b) Management benefits in kind"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_1_2
msgid "b) Pension insurance contributions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_2
msgid "b) Purchases of goods, Åland"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1_2
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_2_2
msgid "b) Receivables from companies in the same group"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_1_b
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_1_2_2_b
msgid "b) Rental rights"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_1_2
msgid "b) Supplements and allowances"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_2_8_2
msgid "b. Residual income"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_7_2
msgid "b. Returned goods"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_3
msgid "c) Community acquisitions of goods"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_2_3
msgid "c) Compensation received for management salaries"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_3_3
msgid ""
"c) Compensation received for the salaries of shareholders and relatives"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_1_3
msgid "c) Fees and commissions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_2_6_2_13_3
msgid "c) Other accrued liabilities"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_2_3
msgid "c) Other staff insurance contributions"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_1_3
msgid "c) Periodicity during the financial year"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1_3
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_3
msgid "c) Receivables from associates"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_7_3
msgid "c. Damages and allowances"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_2_8_3
msgid "c. Other accrued income"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_1_4
msgid "d) Holiday and social wages"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_4
msgid "d) Imports"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1_4
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_4
msgid "d) Loan receivables"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_2_2_4
msgid "d) Periodicity during the financial year"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_7_4
msgid "d. Freight, freight forwarding and other procurement costs"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_1_5
msgid "e) Benefits in kind"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1_5
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_2_5
msgid "e) Deferred tax assets"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_5
msgid ""
"e) Purchases, second-hand goods and collectors' items of art and antiques"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_7_5
msgid "e. Transfers for purposes other than sale"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1_6
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_5
msgid "f) Other receivables"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_6
msgid "f) Purchases, securities and real estate"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_7_1_1_6
msgid "f) Salary reimbursements received"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_7_6
msgid "f. Exchange differences on purchases"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_7
msgid "g) Purchase adjustments"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1_7
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_6
msgid "g) Unpaid shares/units"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_pl_line_5_1_1_7_7
msgid "g. Other adjustments to purchases"
msgstr ""

#. module: l10n_fi_reports
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_1_8
#: model:account.report.line,name:l10n_fi_reports.account_financial_report_l10n_fi_bs_line_1_2_2_7
msgid "h) Accruals and deferred income"
msgstr ""
