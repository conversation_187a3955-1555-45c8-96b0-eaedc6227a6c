# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * l10n_be_coda
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-28 08:39+0000\n"
"PO-Revision-Date: 2016-07-11 08:21+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Polish (http://www.transifex.com/odoo/odoo-9/language/pl/)\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid ""
"\n"
"Movement data records of type 2.%s are not supported "
msgstr ""
"\n"
"ruchome dane z zapisem typu 2.%s są nie wspierane"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "1-st (recurrent)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "ATM/POS debit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Access right to database"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Agio on supplier's bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Amount as totalised by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Amount as totalised by the customer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Avgas"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Bancontact/Mister Cash"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_bank_statement
msgid "Bank Statement"
msgstr "Wyciąg bankowy"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Bank confirmation to revisor or accountant"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Bill claimed back"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Bills - calculation of interest"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_bank_statement__coda_note
msgid "CODA Notes"
msgstr "CODA Notes"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "CODA V%s statements are not supported, please contact your bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "CODA parsing error on information data record 3.2, seq nr %s! Please report this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "CODA parsing error on information data record 3.3, seq nr %s! Please report this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "CODA parsing error on movement data record 2.2, seq nr %s! Please report this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "CODA parsing error on movement data record 2.3, seq nr %s! Please report this issue via your Odoo support channel."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cancellation or correction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Capital and/or interest term investment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cards"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cash deposit at an ATM"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cash payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cash withdrawal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cash withdrawal by your branch or agents"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cash withdrawal from an ATM"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Charge for safe custody"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Charging fees for transactions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cheque-related costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Closing"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Closing (periodical settlements for interest, costs,...)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Codes proper to each bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Collective payments of wages"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Collective transfer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Collective transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Commercial bills"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Commercial paper claimed back"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Communication"
msgstr "Komunikacja"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Communication: "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Communicaton"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Compensation for missing coupon"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Correction for prepaid card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs for holding a documentary cash credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs for opening a bank guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs for the safe custody of correspondence"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs related to commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs relating to electronic output"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs relating to incoming foreign and non-SEPA transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs relating to outgoing foreign transfers and non-SEPA transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs relating to payment of foreign cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Costs relating to the payment of a foreign bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Counter Party"
msgstr "licznik imprez"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Counter Party Account"
msgstr "licznik imprez kontowych"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Counter Party Address"
msgstr "licznik adresów imprez"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Counter transactions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Country code of the principal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Credit"
msgstr "Ma"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Credit after Proton payments"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Credit after a payment at a terminal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Credit-related costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Creditor’s identification code"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Currency"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Damage relating to bills and cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Department store cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Detail"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Detail of Amount as totalised by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Detail of Amount as totalised by the customer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Detail of Simple amount with detailed data"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Difference in payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Direct Debit scheme"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Direct debit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Discount abroad"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Discount foreign supplier's bills"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Documentary credit charges"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Documentary export credits"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Documentary import credits"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Domestic commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Domestic or local SEPA credit transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Download of prepaid card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Drawing up a certificate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Equivalent in EUR"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Error"
msgstr "Błąd"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Extension"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Extension of maturity date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Fees and commissions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Financial centralisation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Financial centralisation (credit)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Financial centralisation (debit)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Financial centralization"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "First credit of cheques, vouchers, luncheon vouchers, postal orders, credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Fixed advance – capital and interest"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Fixed advance – interest only"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Foreign bank accounts with BBAN structure are not supported "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Foreign bank accounts with IBAN structure are not supported "
msgstr "zagraniczne rachunki bankowe o strukturze IBAN nie są obsługiwane"

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Foreign cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Foreign commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Forward purchase of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Forward sale of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Gross amount in the currency of the account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Gross amount in the original currency"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Handling costs instalment credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Idem without guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Income from payments by GSM"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Individual transfer order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Individual transfer order initiated by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Information charges"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Instant SEPA credit transfer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Insurance costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Interest term investment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Interim interest on subscription"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "International credit transfers - non-SEPA credit transfers"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Issues"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model,name:l10n_be_coda.model_account_journal
msgid "Journal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "LPG"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Loading GSM cards"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Loading Proton"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Loading a GSM card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Long-term loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Maestro"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Management fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Management/custody"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Mandate reference"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Masked PAN or card number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Method of calculation (VAT, withholding tax on income, commission, etc.)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Miscellaneous fees and commissions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Name: {name}, Town: {city}"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Night safe"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "No date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Non-presented circular cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Number of the credit card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Original amount of the transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Other"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Other credit applications"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "PAN or card number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "POS credit - individual transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "POS credit – Globalisation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "POS number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "POS others"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Paid or reason for refused payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Partial payment subscription"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Participation in and management of interest refund system"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Pay-packet charges"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payable coupons/repayable securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment by GSM"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment by means of a payment card outside the Eurozone"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment by means of a payment card within the Eurozone"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment by your branch/agents"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment documents abroad"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment in advance"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment in your favour"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment night safe"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment of a foreign cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment of coupons from a deposit or settlement of coupons delivered over the counter - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment of foreign bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment of voucher"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment of wages, etc."
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment of your cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Payment with tank card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Postage"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Printing of forms"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Private"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Provisionally unpaid"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Purchase of Smartcard"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Purchase of an international bank cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Purchase of fiscal stamps"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Purchase of foreign bank notes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Purchase of gold/pieces"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Purchase of petrol coupons"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Purchase of securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Purchase of traveller’s cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Reason"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Registering compensation for savings accounts"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Regularisation costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Reimbursement"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Reimbursement of cheque-related costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Reimbursement of costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of cheque by your branch - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of cheques, vouchers, etc. credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of commercial paper - credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of commercial paper - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of commercial paper for discount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of documents abroad - credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of documents abroad - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of foreign bill credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of foreign bill credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of foreign cheque credit after collection"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of foreign cheque credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of guaranteed foreign supplier's bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of supplier's bill with guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Remittance of supplier's bill without guarantee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Renting of direct debit box"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Renting of safes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Repayable securities from a deposit or delivered at the counter - credit under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Research costs"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Retrocession of issue commission"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Return of an irregular bill of exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Reversal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Reversal of cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Reversal of cheques"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Reversal of voucher"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "SEPA B2B"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "SEPA Direct Debit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "SEPA core"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Sale of foreign bank notes"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Sale of gold/pieces under usual reserve"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Sale of securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Sale of traveller’s cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Second credit of unpaid cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Separately charged costs and provisions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Settlement Date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Settlement credit cards"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Settlement of bank acceptances"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Settlement of discount bank acceptance"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Settlement of fixed advance"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Settlement of instalment credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Settlement of mortgage loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Settlement of securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Share option plan – exercising an option"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Short-term loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Simple amount with detailed data"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Simple amount without detailed data"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Special charge for safe custody"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,field_description:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split Transactions"
msgstr ""

#. module: l10n_be_coda
#: model:ir.model.fields,help:l10n_be_coda.field_account_journal__coda_split_transactions
msgid "Split collective payments for CODA files"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Spot purchase of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Spot sale of foreign exchange"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Standing order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Structured format communication"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Subscription fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Subscription to securities"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Subsidy"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Surety fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "TINA"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Tender"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Tenders"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Term Investments"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Term loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Terminal cash deposit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Trade information"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Transfer"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Transfer from your account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Transfer in your favour"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Transfer in your favour – initiated by the bank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Transfer to your account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Travel insurance premium"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Type Direct Debit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Type of R transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Type of structured communication not supported: "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Undefined transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unexecutable reimbursement"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unexecutable transfer order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unloading Proton"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unpaid commercial paper"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unpaid debt"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unpaid foreign bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unpaid foreign cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unpaid postal order"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unpaid voucher"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Unsupported bank account structure "
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Upload of prepaid card"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Value (date) correction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Various transactions"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Warrant"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Warrant fallen due"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Writ service fee"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Wrong CODA code"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Your certified cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Your issue"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Your issue circular cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Your purchase bank cheque"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Your repayment hire-purchase and similar claims"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Your repayment instalment credits"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Your repayment mortgage loan"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "Your repurchase of issue"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "account number of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "amount (equivalent in foreign currency)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "amount of interest"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "amount of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "amount on which %% is calculated"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "basic amount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "basic amount of the calculation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "cancellation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "card scheme"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "company number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "conformity code or blank"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "conventional maturity date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "cumulative"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "cumulative on network"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "currency"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "date of first transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "date of issue of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "date of last transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "date of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "debtor disagrees"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "debtor’s account problem"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "deposit amount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "deposit number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "diesel"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "distribution sector"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "domestic fuel oil"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "end date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "equivalent in EUR"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "equivalent in the currency of the account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "europremium"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "exchange rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "extension zone of account number of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "fuel"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "guarantee number (no. allocated by the bank)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "hour of payment"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "hour of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "identification number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "identification of terminal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "interest"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "interest rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "interest rates, calculation basis"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "invoice number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "issuing institution"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "last (recurrent)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "lubricants"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "maturity date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "maturity date of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "message (structured of free)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "minimum"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "minimum applicable"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "minimum not applicable"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "minimum rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "new balance of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "nominal interest rate or rate of charge"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "number of days"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "number of the bill"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "old balance of the credit"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "one-off"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "original amount"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "original amount (given by the customer)"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "other types"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "paid"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "payment day"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "percent"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "percentage"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "period from {} to {}"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "period number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "petrol"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "premium 99+"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "premium plus 98 oct"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "premium with lead substitute"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "product code"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "rate"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "reason not specified"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "recurrent"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "reference of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "reference of transaction on credit account"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "refund"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "regular unleaded"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "reject"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "return"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "reversal"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "reversal of purchases"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "sequence number of first transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "sequence number of last transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "sequence number of transaction"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "sequence number of validation"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "starting date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "technical problem"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "teledata"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "term in days"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "terminal number"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "transaction type"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "undefined"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "unit price"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "unset"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "unspecified"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "validation date"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "volume"
msgstr ""

#. module: l10n_be_coda
#. odoo-python
#: code:addons/l10n_be_coda/models/account_journal.py:0
msgid "withdrawal"
msgstr ""
