# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_skills
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: Japanese (https://app.transifex.com/odoo/teams/41243/ja/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ja\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_applicant
msgid "Applicant"
msgstr "応募者"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_candidate
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__candidate_id
msgid "Candidate"
msgstr "候補者"

#. module: hr_recruitment_skills
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_candidate_view_tree
msgid "Create Application"
msgstr "応募を作成"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__create_uid
msgid "Created by"
msgstr "作成者"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__create_date
msgid "Created on"
msgstr "作成日"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__display_name
msgid "Display Name"
msgstr "表示名"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_job__skill_ids
msgid "Expected Skills"
msgstr "期待するスキル"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_job
msgid "Job Position"
msgstr "職位"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__write_uid
msgid "Last Updated by"
msgstr "最終更新者"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__write_date
msgid "Last Updated on"
msgstr "最終更新日"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_job.py:0
msgid "Matching Candidates"
msgstr "マッチする候補者"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__matching_score
msgid "Matching Score(%)"
msgstr "マッチングスコア(%)"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__matching_skill_ids
msgid "Matching Skills"
msgstr "マッチングスキル"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__missing_skill_ids
msgid "Missing Skills"
msgstr "不足しているスキル"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_job.py:0
msgid "No Matching Candidates"
msgstr "マッチする候補者がいません"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__level_progress
msgid "Progress"
msgstr "進捗"

#. module: hr_recruitment_skills
#: model:ir.model.fields,help:hr_recruitment_skills.field_hr_candidate_skill__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "進捗は、知識ゼロ(0%)から完全習得(100%)まで。"

#. module: hr_recruitment_skills
#. odoo-javascript
#: code:addons/hr_recruitment_skills/static/src/components/search_job_applicant_menu/search_job_applicant_menu.xml:0
#: model:ir.actions.server,name:hr_recruitment_skills.action_applicant_search_applicant
msgid "Search Matching Applicants"
msgstr "マッチする応募者を検索"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant__skill_ids
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__skill_ids
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__skill_id
msgid "Skill"
msgstr "スキル"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__skill_level_id
msgid "Skill Level"
msgstr "スキルレベル"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate_skill__skill_type_id
msgid "Skill Type"
msgstr "スキルタイプ"

#. module: hr_recruitment_skills
#: model:ir.ui.menu,name:hr_recruitment_skills.hr_recruitment_skill_type_menu
msgid "Skill Types"
msgstr "スキルタイプ"

#. module: hr_recruitment_skills
#: model:ir.model,name:hr_recruitment_skills.model_hr_candidate_skill
msgid "Skill level for a candidate"
msgstr "候補者のスキルレベル"

#. module: hr_recruitment_skills
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_applicant__candidate_skill_ids
#: model:ir.model.fields,field_description:hr_recruitment_skills.field_hr_candidate__candidate_skill_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_candidate_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment_skills.hr_candidate_view_search
msgid "Skills"
msgstr "スキル"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_candidate_skill.py:0
msgid "The skill %(name)s and skill type %(type)s doesn't match"
msgstr "スキル %(name)s とスキルレベル %(type)s が一致しません。"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_candidate_skill.py:0
msgid "The skill level %(level)s is not valid for skill type: %(type)s"
msgstr "スキルレベル %(level)s は、スキルタイプ %(type)s に対して有効ではありません。"

#. module: hr_recruitment_skills
#: model:ir.model.constraint,message:hr_recruitment_skills.constraint_hr_candidate_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "同じスキルに2つのレベルを設定することはできません"

#. module: hr_recruitment_skills
#. odoo-python
#: code:addons/hr_recruitment_skills/models/hr_job.py:0
msgid ""
"We do not have any candidates who meet the skill requirements for this job "
"position in the database at the moment."
msgstr "現在、この求人ポジションのスキル要件を満たす候補者はデータベースに登録されていません。"
