<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="tr_form" model="account.report">
        <field name="name">Bangladesh Tax Report</field>
        <field name="root_report_id" ref="account.generic_tax_report"/>
        <field name="country_id" ref="base.bd"/>
        <field name="filter_fiscal_position" eval="True"/>
        <field name="availability_condition">country</field>
        <field name="column_ids">
            <record id="tr_column_net" model="account.report.column">
                <field name="name">Balance</field>
                <field name="expression_label">balance</field>
            </record>
            <record id="tr_column_tax" model="account.report.column">
                <field name="name">Tax Amount</field>
                <field name="expression_label">taxbalance</field>
            </record>
        </field>
        <field name="line_ids">
            <record id="tr_title_purchases_vat" model="account.report.line">
                <field name="name">Purchases Taxes</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tr_line_P_EX_0" model="account.report.line">
                        <field name="name">Imports</field>
                        <field name="code">PUR_EX0</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_EX_0_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_EX_0 Base</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_P_0" model="account.report.line">
                        <field name="name">Purchases at 0% VAT</field>
                        <field name="code">PUR0</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_0_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_0 Base</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_P_2" model="account.report.line">
                        <field name="name">Purchases at 2% VAT</field>
                        <field name="code">PUR2</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_2 Base</field>
                            </record>
                            <record id="tr_expression_P_2_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_2 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_P_45" model="account.report.line">
                        <field name="name">Purchases at 4.5% VAT</field>
                        <field name="code">PUR45</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_45_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_45 Base</field>
                            </record>
                            <record id="tr_expression_P_45_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_45 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_P_5" model="account.report.line">
                        <field name="name">Purchases at 5% VAT</field>
                        <field name="code">PUR5</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_5_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_5 Base</field>
                            </record>
                            <record id="tr_expression_P_5_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_5 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_P_75" model="account.report.line">
                        <field name="name">Purchases at 7.5% VAT</field>
                        <field name="code">PUR75</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_75_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_75 Base</field>
                            </record>
                            <record id="tr_expression_P_75_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_75 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_P_10" model="account.report.line">
                        <field name="name">Purchases at 10% VAT</field>
                        <field name="code">PUR10</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_10_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_10 Base</field>
                            </record>
                            <record id="tr_expression_P_10_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_10 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_P_15" model="account.report.line">
                        <field name="name">Purchases at 15% VAT</field>
                        <field name="code">PUR15</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_15_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_15 Base</field>
                            </record>
                            <record id="tr_expression_P_15_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">P_15 Tax</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tr_title_sales_vat" model="account.report.line">
                <field name="name">Sales Taxes</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tr_line_S_EX_0" model="account.report.line">
                        <field name="name">Exports</field>
                        <field name="code">SAL_EX0</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_EX_0_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_EX_0 Base</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_S_0" model="account.report.line">
                        <field name="name">Sales at 0% VAT</field>
                        <field name="code">SAL0</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_0_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_0 Base</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_S_2" model="account.report.line">
                        <field name="name">Sales at 2% VAT</field>
                        <field name="code">SAL2</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_2_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_2 Base</field>
                            </record>
                            <record id="tr_expression_S_2_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_2 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_S_45" model="account.report.line">
                        <field name="name">Sales at 4.5% VAT</field>
                        <field name="code">SAL45</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_45_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_45 Base</field>
                            </record>
                            <record id="tr_expression_S_45_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_45 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_S_5" model="account.report.line">
                        <field name="name">Sales at 5% VAT</field>
                        <field name="code">SAL5</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_5_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_5 Base</field>
                            </record>
                            <record id="tr_expression_S_5_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_5 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_S_75" model="account.report.line">
                        <field name="name">Sales at 7.5% VAT</field>
                        <field name="code">SAL75</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_75_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_75 Base</field>
                            </record>
                            <record id="tr_expression_S_75_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_75 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_S_10" model="account.report.line">
                        <field name="name">Sales at 10% VAT</field>
                        <field name="code">SAL10</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_10_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_10 Base</field>
                            </record>
                            <record id="tr_expression_S_10_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_10 Tax</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_S_15" model="account.report.line">
                        <field name="name">Sales at 15% VAT</field>
                        <field name="code">SAL15</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_15_balance" model="account.report.expression">
                                <field name="label">balance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_15 Base</field>
                            </record>
                            <record id="tr_expression_S_15_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">tax_tags</field>
                                <field name="formula">S_15 Tax</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
            <record id="tr_title_net_vat_due" model="account.report.line">
                <field name="name">Net VAT Due</field>
                <field name="hierarchy_level">0</field>
                <field name="children_ids">
                    <record id="tr_line_S_net_vat" model="account.report.line">
                        <field name="name">Sales VAT Received (Payable)</field>
                        <field name="code">l10n_bd_S_net_vat</field>
                        <field name="expression_ids">
                            <record id="tr_expression_S_net_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">SAL2.taxbalance + SAL45.taxbalance + SAL5.taxbalance 
                                                      + SAL75.taxbalance + SAL10.taxbalance + SAL15.taxbalance</field>
                            </record>
                        </field>
                    </record>
                    <record id="tr_line_P_net_vat" model="account.report.line">
                        <field name="name">Purchases VAT Paid (Recoverable)</field>
                        <field name="code">l10n_bd_P_net_vat</field>
                        <field name="expression_ids">
                            <record id="tr_expression_P_net_tax" model="account.report.expression">
                                <field name="label">taxbalance</field>
                                <field name="engine">aggregation</field>
                                <field name="formula">PUR2.taxbalance + PUR45.taxbalance + PUR5.taxbalance 
                                                      + PUR75.taxbalance + PUR10.taxbalance + PUR15.taxbalance</field>
                            </record>
                        </field>
                    </record>
                </field>
            </record>
        </field>
    </record>
</odoo>
