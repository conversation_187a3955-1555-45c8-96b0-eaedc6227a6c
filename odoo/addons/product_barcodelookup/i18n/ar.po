# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* product_barcodelookup
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:28+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Arabic (https://app.transifex.com/odoo/teams/41243/ar/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ar\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "API Key:"
msgstr "مفتاح الواجهة البرمجية للتطبيق: "

#. module: product_barcodelookup
#: model:ir.model.fields,field_description:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "API key"
msgstr "مفتاح الواجهة البرمجية للتطبيق "

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Barcode Database"
msgstr "قاعدة بيانات الباركود "

#. module: product_barcodelookup
#: model:ir.model.fields,help:product_barcodelookup.field_res_config_settings__barcodelookup_api_key
msgid "Barcode Lookup API Key for create product from barcode."
msgstr "مفتاح الواجهة البرمجية للبحث عن الباركود لإنشاء منتج من الباركود. "

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_res_config_settings
msgid "Config Settings"
msgstr "تهيئة الإعدادات "

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "Create products by scanning using"
msgstr "أنشئ المنتجات عن طريق المسح الضوئي باستخدام "

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_template
msgid "Product"
msgstr "المنتج "

#. module: product_barcodelookup
#: model:ir.model,name:product_barcodelookup.model_product_product
msgid "Product Variant"
msgstr "متغير المنتج "

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_8
msgid "age group"
msgstr "الفئة العمرية "

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "barcodelookup.com"
msgstr "barcodelookup.com"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_6
msgid "brand"
msgstr "العلامة التجارية "

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_1
msgid "color"
msgstr "اللون "

#. module: product_barcodelookup
#: model_terms:ir.ui.view,arch_db:product_barcodelookup.res_config_settings_view_form
msgid "e.g. d7vctmiv2rwgenebha8bxq7irooudn"
msgstr "مثال: d7vctmiv2rwgenebha8bxq7irooudn "

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_2
msgid "gender"
msgstr "الجنس"

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_5
msgid "manufacturer"
msgstr "المُصنّع "

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_3
msgid "material"
msgstr "المادة "

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_4
msgid "pattern"
msgstr "النمط "

#. module: product_barcodelookup
#: model:product.attribute,name:product_barcodelookup.product_attribute_lookup_7
msgid "size"
msgstr "الحجم "
