# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* documents_spreadsheet
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-27 13:55+0000\n"
"PO-Revision-Date: 2025-01-27 13:55+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/wizard/save_spreadsheet_template.py:0
msgid "\"%s\" saved as template"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "%(folder)s is used by %(company)s"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
msgid "%s (copy)"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "%s - Template"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_spreadsheet_contributor_spreadsheet_user_unique
msgid "A combination of the spreadsheet and the user already exist"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_documents_document_frozen_spreadsheet_access_via_link_access_internal
msgid "A frozen spreadsheet can not be editable"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid ""
"Any user will be able to create a new spreadsheet based on this template."
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid "Cancel"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Centralize your spreadsheets"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_company
msgid "Companies"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.save_spreadsheet_template_view_form
msgid "Confirm"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_contributor_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_contributor
msgid "Contributors"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
msgid "Create"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/spreadsheet_template_dialog.js:0
msgid "Create a Spreadsheet or select a Template"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_uid
msgid "Created by"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__create_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__create_date
msgid "Created on"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__current_revision_uuid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__current_revision_uuid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__current_revision_uuid
msgid "Current Revision Uuid"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
msgid "Discard"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__display_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__display_name
msgid "Display Name"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_document
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_cell_thread__document_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__document_id
msgid "Document"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_documents_access
msgid "Document / Partner"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_company__document_spreadsheet_folder_id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_res_config_settings__document_spreadsheet_folder_id
msgid "Document Spreadsheet Folder"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "Edit"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__excel_export
msgid "Excel Export"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Excel file preview"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_clone_xlsx_dialog/spreadsheet_clone_xlsx_dialog.xml:0
msgid ""
"Excel files cannot be previewed or edited directly in Odoo. Opening your "
"file with Odoo Spreadsheet will allow you to do so."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__file_name
msgid "File Name"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_freeze_and_share/spreadsheet_freeze_and_share.xml:0
#: code:addons/documents_spreadsheet/static/src/documents_control_panel/documents_control_panel.xml:0
msgid "Freeze and share"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__frozen_folder
msgid "Frozen Folder"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__frozen_spreadsheet
msgid "Frozen Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_access.py:0
msgid "Frozen Spreadsheets can not be editable."
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "Frozen at %(date)s: %(name)s"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "Frozen spreadsheets"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_ir_http
msgid "HTTP Routing"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__handler
msgid "Handler"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__id
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__id
msgid "ID"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_control_panel/documents_control_panel.xml:0
msgid "Insert in spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_uid
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_uid
msgid "Last Updated by"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__write_date
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__write_date
msgid "Last Updated on"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__last_update_date
msgid "Last update date"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "Make a copy"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_search
msgid "My Templates"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__name
msgid "Name"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_template_view_tree
msgid "New spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "New spreadsheet created in Documents"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "New spreadsheet created in My Drive"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#. odoo-python
#: code:addons/documents_spreadsheet/models/spreadsheet_template.py:0
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_template/spreadsheet_template_action.js:0
msgid "New spreadsheet template created"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Open with Odoo Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Restore"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid "Restore file?"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.save_spreadsheet_template_action
msgid "Save as template"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_control_panel/documents_control_panel.js:0
msgid "Select one and only one spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_clone_xlsx_dialog/spreadsheet_clone_xlsx_dialog.xml:0
msgid "Send source file to trash"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__sequence
msgid "Sequence"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_share/spreadsheet_share.xml:0
msgid "Share"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.xml:0
#: code:addons/documents_spreadsheet/static/src/spreadsheet_action_loader.js:0
#: model:ir.model.fields.selection,name:documents_spreadsheet.selection__documents_document__handler__spreadsheet
msgid "Spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_contributor
msgid "Spreadsheet Contributor"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_data
msgid "Spreadsheet Data"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_file_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_file_name
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_file_name
msgid "Spreadsheet File Name"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.spreadsheet_document_view_kanban
msgid "Spreadsheet Preview"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_revision_ids
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_revision_ids
msgid "Spreadsheet Revision"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_snapshot
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_snapshot
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_snapshot
msgid "Spreadsheet Snapshot"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_template
msgid "Spreadsheet Template"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_save_spreadsheet_template
msgid "Spreadsheet Template Save Wizard"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.actions.act_window,name:documents_spreadsheet.spreadsheet_template_action
#: model:ir.ui.menu,name:documents_spreadsheet.menu_technical_spreadsheet_template
msgid "Spreadsheet Templates"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model,name:documents_spreadsheet.model_spreadsheet_cell_thread
msgid "Spreadsheet discussion thread"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__spreadsheet_binary_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__spreadsheet_binary_data
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__spreadsheet_binary_data
msgid "Spreadsheet file"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_view/documents_spreadsheet_controller_mixin.js:0
msgid ""
"Spreadsheet files cannot be handled from the Trash. Would you like to "
"restore this document?"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.document_view_search_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Spreadsheets"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_access.py:0
msgid "Spreadsheets can not be shared in edit mode to non-internal users."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_cell_thread__template_id
msgid "Template"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__template_name
msgid "Template Name"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid ""
"The company for a folder cannot be changed if it is already used as the "
"spreadsheet workspace for at least one other company: %s"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/res_company.py:0
msgid ""
"The company of %(folder)s should either be undefined or set to %(company)s. "
"Otherwise, it is not possible to link the workspace to the company."
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The file is not a xlsx file"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_access_settings.js:0
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_member_invite.js:0
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_partner_access.js:0
msgid "The frozen spreadsheets are readonly."
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The spreadsheet you are trying to access does not exist."
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_access_settings.js:0
msgid ""
"The spreadsheets can not be shared in edit mode with a link, change Internal"
" to give write access."
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The xlsx file is corrupted"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "The xlsx file is too big"
msgstr ""

#. module: documents_spreadsheet
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.documents_error_live_data
msgid "This spreadsheet contains live data, only internal users can view it."
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_documents_document__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_save_spreadsheet_template__thumbnail
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_template__thumbnail
msgid "Thumbnail"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.constraint,message:documents_spreadsheet.constraint_documents_document_spreadsheet_access_via_link
msgid "To share a spreadsheet in edit mode, add the user in the accesses"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.xml:0
msgid "Toggle favorite"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "Untitled spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_template/spreadsheet_template_action.js:0
msgid "Untitled spreadsheet template"
msgstr ""

#. module: documents_spreadsheet
#: model:ir.model.fields,field_description:documents_spreadsheet.field_spreadsheet_contributor__user_id
msgid "User"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/spreadsheet_template/template_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:documents_spreadsheet.res_config_settings_view_form_inherit_documents_spreadsheet
msgid "Workspace"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "You are not allowed to freeze spreadsheets in Company"
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "You can not edit a frozen spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/bundle/actions/spreadsheet_action.js:0
msgid "You can not freeze a frozen spreadsheet"
msgstr ""

#. module: documents_spreadsheet
#. odoo-javascript
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_member_invite.js:0
#: code:addons/documents_spreadsheet/static/src/documents_permission_panel/documents_partner_access.js:0
msgid "You can not share spreadsheet in edit mode to non-internal user."
msgstr ""

#. module: documents_spreadsheet
#. odoo-python
#: code:addons/documents_spreadsheet/models/documents_document.py:0
msgid "You don't have access to this document"
msgstr ""
