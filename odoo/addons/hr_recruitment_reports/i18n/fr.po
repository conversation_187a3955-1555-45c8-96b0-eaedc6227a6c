# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment_reports
# 
# Translators:
# Wil <PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Man<PERSON>, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
msgid "Analysis"
msgstr "Analyses"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__count
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__applicant_id
msgid "Applicant"
msgstr "Candidat"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_action
msgid "Applicant Analysis"
msgstr "Analyse des candidats"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__name
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__name
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Applicant Name"
msgstr "Nom du candidat"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_date
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Application Date"
msgstr "Date de candidature"

#. module: hr_recruitment_reports
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__archived
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Archived"
msgstr "Archivé"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__company_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Company"
msgstr "Société"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__create_uid
msgid "Creator"
msgstr "Créateur"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__days_in_stage
msgid "Days In Stage"
msgstr "Jours dans l'étape"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__display_name
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__date_closed
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__date_end
msgid "End Date"
msgstr "Date de fin"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hired
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__is_hired
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__is_hired
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Hired"
msgstr "Engagé"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Hired Date"
msgstr "Date d'embauche"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__hiring_ratio
msgid "Hired Ratio"
msgstr "Taux de recrutement"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
msgid "Hiring ratio"
msgstr "Taux de recrutement"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__in_progress
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__in_progress
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__in_progress
msgid "In Progress"
msgstr "En cours"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__job_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__job_id
msgid "Job"
msgstr "Poste"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Job Position"
msgstr "Poste"

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Last 365 Days Applicant"
msgstr "Candidat des 365 derniers jours"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__medium_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Medium"
msgstr "Médium"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__meetings_amount
msgid "Meetings"
msgstr "Réunions"

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_job_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_team_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_job_action
msgid "No data to display"
msgstr "Aucune donnée à afficher"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__process_duration
msgid "Process Duration"
msgstr "Durée du processus"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__user_id
msgid "Recruiter"
msgstr "Recruteur"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Recruitment Analysis"
msgstr "Analyse du recrutement"

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_report
msgid "Recruitment Analysis Report"
msgstr "Rapport d'analyse du recrutement"

#. module: hr_recruitment_reports
#: model:ir.model,name:hr_recruitment_reports.model_hr_recruitment_stage_report
msgid "Recruitment Stage Analysis"
msgstr "Analyse de l'étape de recrutement"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refuse_reason_id
msgid "Refuse Reason"
msgstr "Raison de refus"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__refused
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_report__state__refused
#: model:ir.model.fields.selection,name:hr_recruitment_reports.selection__hr_recruitment_stage_report__state__refused
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Refused"
msgstr "Refusée"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__source_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
msgid "Source"
msgstr "Source"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_source_action
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_source_job_action
#: model:ir.ui.menu,name:hr_recruitment_reports.hr_applicant_report_source_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_source_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_team_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_source_pivot
msgid "Source Analysis"
msgstr "Analyse des sources"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__stage_id
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Stage"
msgstr "Étape"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__date_begin
msgid "Start Date"
msgstr "Date de début"

#. module: hr_recruitment_reports
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_report__state
#: model:ir.model.fields,field_description:hr_recruitment_reports.field_hr_recruitment_stage_report__state
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "State"
msgstr "Statut"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_report_team_action
#: model:ir.ui.menu,name:hr_recruitment_reports.hr_applicant_report_team_menu
msgid "Team Performance"
msgstr "Performance de l'équipe"

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_stage_report_job_action
msgid ""
"This report allows you to check the more time-consuming stages of your pipes"
" and optimize your recruitment flow."
msgstr ""
"Ce rapport vous permet de vérifier les étapes les plus chronophages de vos "
"pipelines et d'optimiser votre flux de recrutement."

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_team_action
msgid ""
"This report allows you to compare the recruiters and their performances."
msgstr ""
"Ce rapport vous permet de comparer les recruteurs et leurs performances."

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_action
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_source_job_action
msgid ""
"This report allows you to compare various measures grouped by your sources "
"of applicants (e.g. LinkedIn, Monster, etc.)."
msgstr ""
"Ce rapport vous permet de comparer diverses mesures regroupées par vos "
"sources de candidats (par exemple LinkedIn, Monster, etc.)."

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_action
msgid ""
"This report allows you to follow the evolution of the number of applicants "
"and hired employees over time."
msgstr ""
"Ce rapport vous permet de suivre l'évolution du nombre de candidats et "
"d'employés embauchés au fil du temps."

#. module: hr_recruitment_reports
#: model_terms:ir.actions.act_window,help:hr_recruitment_reports.recruitment_report_job_action
msgid "This report performs analysis on your recruitment."
msgstr "Ce rapport effectue une analyse de votre recrutement."

#. module: hr_recruitment_reports
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.hr_recruitment_report_inherit_kanban_view
msgid "Time By Stages"
msgstr "Temps par étapes"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_stage_report_job_action
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment_reports.recruitment_stage_report_view_search
msgid "Time In Stage Analysis"
msgstr "Analyse du temps par étape"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
msgid "Total Hired"
msgstr "Total des personnes engagées"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
msgid "Total Meetings"
msgstr "Total des réunions"

#. module: hr_recruitment_reports
#. odoo-python
#: code:addons/hr_recruitment_reports/report/hr_recruitment_report.py:0
msgid "Total applicants"
msgstr "Total des candidatures"

#. module: hr_recruitment_reports
#: model:ir.actions.act_window,name:hr_recruitment_reports.recruitment_stage_report_action
#: model:ir.ui.menu,name:hr_recruitment_reports.hr_applicant_stage_report_menu
msgid "Velocity Analysis"
msgstr "Analyse de la vitesse"
