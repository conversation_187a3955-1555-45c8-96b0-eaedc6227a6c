<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <!-- Templates -->
    <record id="knowledge_article_template_shared_todos" model="knowledge.article">
        <field name="icon">✅</field>
        <field name="cover_image_id" ref="knowledge_cover_todo_list"/>
        <field name="article_properties_definition" eval="
            [{
                'name': 'deadline',
                'type': 'date',
                'string': 'Deadline',
                'view_in_cards': True
            }, {
                'name': 'priority',
                'type': 'selection',
                'string': 'Priority',
                'view_in_cards': True,
                'selection': [
                    ['low_priority', '🔥'],
                    ['medium_priority', '🔥🔥'],
                    ['high_priority', '🔥🔥🔥']
                ]
            }, {
                'name': 'tags',
                'type': 'tags',
                'string': 'Tags',
                'view_in_cards': True,
                'tags': [
                    ['marketing', 'Marketing', 1],
                    ['admin', 'Admin', 2],
                    ['sales', 'Sales', 3]
                ]
            }]"/>
        <field name="full_width">True</field>
        <field name="is_template">True</field>
        <field name="template_name">Shared To-Do List</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_description">Keep track of your company to-dos and share them with your colleagues.</field>
        <field name="template_sequence">0</field>
        <field name="template_body" type="html">
            <h1>Shared To-Do List</h1>
            <hr/>
            <p>Please pick the following tasks first:</p>
            <div data-embedded="view" data-oe-protected="true" data-embedded-props="{
                'viewProps': {
                    'actionXmlId': 'knowledge.knowledge_article_item_action',
                    'displayName': 'Urgent Matters',
                    'viewType': 'list',
                    'favoriteFilters': {
                        'Urgent Tasks': {
                            'name': 'Urgent Tasks',
                            'is_default': True,
                            'model_id': 'knowledge.article',
                            'domain': [('article_properties.priority', '=', 'high_priority')],
                            'context': {}
                        }
                    },
                    'context': {
                        'active_id': ref('knowledge.knowledge_article_template_shared_todos'),
                        'default_parent_id': ref('knowledge.knowledge_article_template_shared_todos'),
                        'default_is_article_item': True
                    }
                }
            }"/>
            <p><br/></p>
            <p>Otherwise, feel free to handle others listed below:</p>
            <div data-embedded="view" data-oe-protected="true" data-embedded-props="{
                'viewProps': {
                    'actionXmlId': 'knowledge.knowledge_article_item_action_stages',
                    'displayName': 'All Tasks',
                    'viewType': 'kanban',
                    'context': {
                        'active_id': ref('knowledge.knowledge_article_template_shared_todos'),
                        'default_parent_id': ref('knowledge.knowledge_article_template_shared_todos'),
                        'default_is_article_item': True
                    }
                }
            }"/>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_shared_todos_water_the_plants" model="knowledge.article">
        <field name="icon">🌵</field>
        <field name="parent_id" ref="knowledge_article_template_shared_todos"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'deadline': (DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d'),
            'priority': 'high_priority',
            'tags': ['admin'],
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Water the Plants</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_body" type="html">
            <h1>Water the Plants</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_shared_todos_pay_the_electricity_bill" model="knowledge.article">
        <field name="icon">💡</field>
        <field name="parent_id" ref="knowledge_article_template_shared_todos"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'deadline': (DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d'),
            'priority': 'medium_priority',
            'tags': ['admin'],
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Pay the Electricity Bill</field>
        <field name="template_sequence">100</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_body" type="html">
            <h1>Pay the Electricity Bill</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_shared_todos_write_the_next_newsletter" model="knowledge.article">
        <field name="icon">📫</field>
        <field name="parent_id" ref="knowledge_article_template_shared_todos"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'deadline': (DateTime.now() + timedelta(days=5)).strftime('%Y-%m-%d'),
            'priority': 'low_priority',
            'tags': ['marketing'],
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Write the next Newsletter</field>
        <field name="template_sequence">200</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_body" type="html">
            <h1>Write the next Newsletter</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_shared_todos_contact_our_lawyer" model="knowledge.article">
        <field name="icon">👨‍⚖️</field>
        <field name="parent_id" ref="knowledge_article_template_shared_todos"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'deadline': (DateTime.now() + timedelta(days=-15)).strftime('%Y-%m-%d'),
            'priority': 'high_priority',
            'tags': ['admin'],
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Contact our Lawyer</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_sequence">300</field>
        <field name="template_body" type="html">
            <h1>Contact our Lawyer</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_personal_organizer" model="knowledge.article">
        <field name="icon">📅</field>
        <field name="full_width">True</field>
        <field name="is_template">True</field>
        <field name="template_name">Personal Organizer</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_description">Make every week a success by proactively organizing your priority and optional tasks.</field>
        <field name="template_body" type="html">
            <h1>Personal Organizer</h1>
            <hr/>
            <blockquote>
                <span style="font-size: 18px;">
                    Productivity is never an accident. It is always the result of a commitment to excellence, intelligent planning, and focused effort.
                </span>​
                - Paul J. Meyer
            </blockquote>
            <p>Things to handle this week*</p>
            <table class="table table-bordered o_table">
                <tbody>
                    <tr>
                        <td>
                            <ul class="o_checklist">
                                <li id="checkId-1">
                                    <font class="text-600">Task A</font>
                                </li>
                                <li id="checkId-2">
                                    <font class="text-600">Task B</font>
                                </li>
                                <li id="checkId-3">
                                    <font class="text-600">Task C</font>
                                </li>
                                <li id="checkId-4">
                                    <font class="text-600">Task D</font>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p>
                <span style="font-size: 12px;">
                    <font class="text-600">*💡 tick the box when the task is scheduled in the agenda</font>
                </span>
            </p>
            <p><br/></p>
            <h2>🗓 WEEKLY AGENDA</h2>
            <hr/>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <p>
                            <strong>
                                <span style="font-size: 18px;">
                                    <font class="text-o-color-1">Reminders</font>
                                </span>
                            </strong>
                        </p>
                        <ul>
                            <li><font class="text-600">Reminder 1</font></li>
                            <li><font class="text-600">Reminder 2</font></li>
                            <li><font class="text-600">Reminder 3</font></li>
                        </ul>
                        <p><br/></p>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <p>
                            <strong>
                                <span style="font-size: 18px;">
                                    <font class="text-o-color-1">MONDAY 🏢 @office</font>
                                </span>
                            </strong>
                        </p>
                        <p><strong>⚡ TOP 3 PRIORITIES</strong></p>
                        <ol>
                            <li><font class="text-600">Priority A</font></li>
                            <li><font class="text-600">Priority B</font></li>
                            <li><font class="text-600">Priority C</font></li>
                        </ol>
                        <p><strong>Optional Tasks</strong></p>
                        <ul class="o_checklist">
                            <li id="checkId-6">
                                <span style="font-size: 14px;">
                                    <font class="text-600">Task A</font>
                                </span>
                            </li>
                            <li id="checkId-7">
                                <span style="font-size: 14px;">
                                    <font class="text-600">Task B</font>
                                </span>
                            </li>
                            <li id="checkId-8">
                                <span style="font-size: 14px;">
                                    <font class="text-600">Task C</font>
                                </span>
                            </li>
                        </ul>
                        <p><strong>Extra Notes:</strong></p>
                        <p><br/></p>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <p>
                            <strong>
                                <span style="font-size: 18px;">
                                    <font class="text-o-color-1">TUESDAY 🏢 @office</font>
                                </span>
                            </strong>
                        </p>
                        <p><strong style="font-weight: 500">⚡ TOP 3 PRIORITIES</strong></p>
                        <ol>
                            <li><font class="text-600">Priority A</font></li>
                            <li><font class="text-600">Priority B</font></li>
                            <li><font class="text-600">Priority C</font></li>
                        </ol>
                        <p><strong style="font-weight: 500">Optional Tasks</strong></p>
                        <ul class="o_checklist">
                            <li id="checkId-9">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task A</font>
                                </span>
                            </li>
                            <li id="checkId-10">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task B</font>
                                </span>
                            </li>
                            <li id="checkId-11">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task C</font>
                                </span>
                            </li>
                        </ul>
                        <p><strong>Extra Notes:</strong></p>
                        <p><br/></p>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <p>
                            <strong>
                                <span style="font-size: 18px;">
                                    <font class="text-o-color-1">WEDNESDAY 🏠 @home</font>
                                </span>
                            </strong>
                        </p>
                        <p><strong style="font-weight: 500">⚡ TOP 3 PRIORITIES</strong></p>
                        <ol>
                            <li><font class="text-600">Priority A</font></li>
                            <li><font class="text-600">Priority B</font></li>
                            <li><font class="text-600">Priority C</font></li>
                        </ol>
                        <p><strong style="font-weight: 500">Optional Tasks</strong></p>
                        <ul class="o_checklist">
                            <li id="checkId-12">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task A</font>
                                </span>
                            </li>
                            <li id="checkId-13">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task B</font>
                                </span>
                            </li>
                            <li id="checkId-14">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task C</font>
                                </span>
                            </li>
                        </ul>
                        <p><strong>Extra Notes:</strong></p>
                        <p><br/></p>
                    </div>
                </div>
            </div>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <p>
                            <strong>
                                <span style="font-size: 18px;">
                                    <font class="text-o-color-1">THURSDAY 🏢 @office</font>
                                </span>
                            </strong>
                        </p>
                        <p><strong style="font-weight: 500">⚡ TOP 3 PRIORITIES</strong></p>
                        <ol>
                            <li><font class="text-600">Priority A</font></li>
                            <li><font class="text-600">Priority B</font></li>
                            <li><font class="text-600">Priority C</font></li>
                        </ol>
                        <p><strong style="font-weight: 500">Optional Tasks</strong></p>
                        <ul class="o_checklist">
                            <li id="checkId-15">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task A</font>
                                </span>
                            </li>
                            <li id="checkId-16">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task B</font>
                                </span>
                            </li>
                            <li id="checkId-17">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task C</font>
                                </span>
                            </li>
                        </ul>
                        <p><strong>Extra Notes:</strong></p>
                        <p><br/></p>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <p>
                            <strong>
                                <span style="font-size: 18px;">
                                    <font class="text-o-color-1">FRIDAY 🏠 @home</font>
                                </span>
                            </strong>
                        </p>
                        <p><strong style="font-weight: 500">⚡ TOP 3 PRIORITIES</strong></p>
                        <ol>
                            <li><font class="text-600">Priority A</font></li>
                            <li><font class="text-600">Priority B</font></li>
                            <li><font class="text-600">Priority C</font></li>
                        </ol>
                        <p><strong style="font-weight: 500">Optional Tasks</strong></p>
                        <ul class="o_checklist">
                            <li id="checkId-18">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task A</font>
                                </span>
                            </li>
                            <li id="checkId-19">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task B</font>
                                </span>
                            </li>
                            <li id="checkId-20">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task C</font>
                                </span>
                            </li>
                        </ul>
                        <p><strong>Extra Notes:</strong></p>
                        <p><br/></p>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <p>
                            <strong>
                                <span style="font-size: 18px;">
                                    <font class="text-o-color-1">SATURDAY 🏠 @home</font>
                                </span>
                            </strong>
                        </p>
                        <p><strong style="font-weight: 500">⚡ TOP 3 PRIORITIES</strong></p>
                        <ol>
                            <li><font class="text-600">Priority A</font></li>
                            <li><font class="text-600">Priority B</font></li>
                            <li><font class="text-600">Priority C</font></li>
                        </ol>
                        <p><strong style="font-weight: 500">Optional Tasks</strong></p>
                        <ul class="o_checklist">
                            <li id="checkId-21">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task A</font>
                                </span>
                            </li>
                            <li id="checkId-22">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task B</font>
                                </span>
                            </li>
                            <li id="checkId-23">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task C</font>
                                </span>
                            </li>
                        </ul>
                        <p><strong>Extra Notes:</strong></p>
                        <p><br/></p>
                    </div>
                    <div class="col-lg-3 col-md-4 col-sm-6">
                        <p>
                            <strong>
                                <span style="font-size: 18px;">
                                    <font class="text-o-color-1">SUNDAY 🏠 @home</font>
                                </span>
                            </strong>
                        </p>
                        <p><strong style="font-weight: 500">⚡ TOP 3 PRIORITIES</strong></p>
                        <ol>
                            <li><font class="text-600">Priority A</font></li>
                            <li><font class="text-600">Priority B</font></li>
                            <li><font class="text-600">Priority C</font></li>
                        </ol>
                        <p><strong style="font-weight: 500">Optional Tasks</strong></p>
                        <ul class="o_checklist">
                            <li id="checkId-24">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task A</font>
                                </span>
                            </li>
                            <li id="checkId-25">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task B</font>
                                </span>
                            </li>
                            <li id="checkId-26">
                                <span style="font-size: 14px">
                                    <font class="text-600">Task C</font>
                                </span>
                            </li>
                        </ul>
                        <p><strong>Extra Notes:</strong></p>
                        <p><br/></p>
                    </div>
                </div>
            </div>
            <h2>Backlog</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">List here all the text you could not do this week. These shall be postponed in the next weekly schedule.</font>
                </em>
            </p>
            <ul>
                <li><font class="text-600">Backlog 1</font></li>
                <li><font class="text-600">Backlog 2</font></li>
                <li><font class="text-600">Backlog 3</font></li>
            </ul>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_sales_playbook" model="knowledge.article">
        <field name="icon">📘</field>
        <field name="is_template">True</field>
        <field name="template_name">Sales Playbook</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_sales"/>
        <field name="template_description">Provide salespeople with tips, lexicon and templates to help them sell faster.</field>
        <field name="template_body" type="html">
            <h1>Sales Playbook</h1>
            <hr/>
            <p>Compiled below are tips &amp; tricks collected among our veterans to help newcomers get started. We hope it will help you sign deals.</p>
            <p><br/></p>
            <h3>Prospection Templates</h3>
            <hr/>
            <p><br/></p>
            <p>
                <strong><font class="text-o-color-2">PRO TIP</font></strong>: From a lead, use the book button in the chatter to find this article and autocomplete your email with this template.
            </p>
            <div data-embedded="clipboard">
                <div class="d-flex">
                    <div class="o_embedded_clipboard_label align-middle">Clipboard</div>
                </div>
                <div data-embedded-editable="clipboardContent">
                    <p>Hey ProspectName,</p>
                    <p>
                        I was doing some research online and found your company.<br/>
                        Considering we just launched ProductName, I was thinking you would be interested.
                    </p>
                    <p>
                        Could you please let me know on which number I could reach you so that we could get in touch?<br/>
                        It should not take longer than 15 minutes.
                    </p>
                    <p>
                        Talk to you soon,<br/>
                        YourName
                    </p>
                    <p><br/></p>
                </div>
            </div>
            <p><br/></p>
            <p>
                <strong><font class="text-o-color-2">PRO TIP</font></strong>: From a lead, use the book button in the chatter to find this article and autocomplete your description with this qualification template.
            </p>
            <div data-embedded="clipboard">
                <div class="d-flex">
                    <div class="o_embedded_clipboard_label align-middle">Clipboard</div>
                </div>
                <div data-embedded-editable="clipboardContent">
                    <h2>Prospect Qualification</h2>
                    <p><br/></p>
                    <ol>
                        <li><strong>Company</strong></li>
                        <li class="oe-nested">
                            <ul>
                                <li>Background:</li>
                                <li>Size:</li>
                                <li>Estimated Revenues:</li>
                                <li>
                                    Pain Points (<em>tick the relevant ones</em>)
                                    <ul class="o_checklist">
                                        <li id="checkId-1">Inconsistent customer experience</li>
                                        <li id="checkId-2">Complicated buying process</li>
                                        <li id="checkId-3">Costing too much money</li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                        <li><strong>Contact</strong></li>
                        <li class="oe-nested">
                            <ul>
                                <li>Job Position:</li>
                                <li>Email:</li>
                                <li>Phone:</li>
                            </ul>
                        </li>
                    </ol>
                    <p><br/></p>
                </div>
            </div>
            <p><br/></p>
            <h3>The 5 Commandments</h3>
            <hr/>
            <ol>
                <li>My Forecast will always be accurate and up-to-date</li>
                <li>All my activities will always be encoded in our CRM</li>
                <li>I will only sell a project if I am convinced it can be a success</li>
                <li>I will not steal prospects from colleagues</li>
                <li>I will not waste time and energy bad-mouthing competitors</li>
            </ol>
            <p><br/></p>
            <h3>Tips to close more deals</h3>
            <hr/>
            <ul>
                <li>Be assertive but listen to what is being said</li>
                <li>Identify the pain points and offer clear solutions</li>
                <li>Prepare your demos in advance and integrate the prospect's use case into it</li>
            </ul>
            <p><br/></p>
            <h3>Company Abbreviations</h3>
            <hr/>
            <p>
                You may have heard those a million times and yet you are not entirely sure of what it means.<br/>
                Here is a quick recap for you to shine during the next meeting.
            </p>
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <td><strong>MRR</strong></td>
                        <td>
                            <strong>M</strong>onthly
                            <strong>R</strong>ecurring
                            <strong>R</strong>evenues
                            (<em>subscriptions, ...</em>)
                        </td>
                    </tr>
                    <tr>
                        <td><strong>NRR</strong></td>
                        <td>
                            <strong>N</strong>on-<strong>R</strong>ecurring <strong>R</strong>evenues
                            (<em>consultancy services, ...</em>)
                        </td>
                    </tr>
                    <tr>
                        <td><strong>USP</strong></td>
                        <td>
                            <strong>U</strong>nique <strong>S</strong>elling <strong>P</strong>roposition:
                            Advantage that makes you stand out from the competition.
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Q1,2,3,4</strong></td>
                        <td>
                            Nth <strong>Q</strong>uarter of the fiscal year.<br/>
                            <em>E.g. Q4 starts on Oct. 1 and ends on Dec. 31.</em>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p><br/></p>
        </field>
    </record>


    <record id="knowledge_article_template_meeting_minutes" model="knowledge.article">
        <field name="icon">📝</field>
        <field name="is_template">True</field>
        <field name="template_name">Meeting Minutes</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_description">Centralize team meetings in a single article, while making sure notes are handled efficiently.</field>
        <field name="template_body" type="html">
            <h1>Meeting Minutes</h1>
            <hr/><br/>
            <div data-embedded="view" data-oe-protected="true" data-embedded-props="{
                'viewProps': {
                    'actionXmlId': 'knowledge.knowledge_article_item_action',
                    'displayName': 'Meeting Minute',
                    'viewType': 'list',
                    'context': {
                        'active_id': ref('knowledge.knowledge_article_template_meeting_minutes'),
                        'default_parent_id': ref('knowledge.knowledge_article_template_meeting_minutes'),
                        'default_is_article_item': True
                    }
                }
            }"/>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_meeting_example_branding" model="knowledge.article">
        <field name="icon">🧠</field>
        <field name="parent_id" ref="knowledge_article_template_meeting_minutes"/>
        <field name="is_article_item">True</field>
        <field name="is_template">True</field>
        <field name="template_name">Meeting Example</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_body" type="html">
            <h1>Meeting Minutes Template</h1>
            <p>☝🏼 Please prepare the following before the meeting:</p>
            <ul>
                <li>
                    <font class="text-400">
                        <a href="https://www.odoo.com/blog/odoo-news-5/odoo-the-new-openerp-156"
                            target="_blank" data-bs-original-title="" title="">
                            OpenERP becomes Odoo
                        </a>
                    </font>
                </li>
                <li>
                    <font class="text-400">
                        <a href="https://www.odoo.com/page/brand-assets" target="_blank"
                            data-bs-original-title="" title="">
                            Odoo Brand Assets
                        </a>
                    </font>
                </li>
            </ul>
            <table class="table table-bordered o_table">
                <tbody>
                    <tr>
                        <td>
                            <h2>🗣 Meeting Agenda</h2>
                        </td>
                        <td>
                            <h2>🙌🏼 Decisions Taken</h2>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-black">
                            <ul>
                                <li>Which color should we pick for the logo?</li>
                            </ul>
                        </td>
                        <td class="text-400">
                            <ul>
                                <li>
                                    <span style="color: rgb(255, 255, 255);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(113, 75, 103)">
                                        5125C
                                    </span>
                                </li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-black">
                            <ul>
                                <li>Which text should we print on the billboards?</li>
                            </ul>
                        </td>
                        <td class="text-black">
                            <ul>
                                <li>Odoo: Manage your SME online</li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-black">
                            <ul>
                                <li>Which size should the rollups be?</li>
                            </ul>
                        </td>
                        <td class="text-black">
                            <ul>
                                <li>85x200 or 100x200, depending on the content</li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p></p>
            <div class="row">
                <div class="col-lg-6">
                    <p></p>
                </div>
            </div>
            <h3>Next Meeting: <u>6th May, @John's Office</u></h3>
        </field>
    </record>

    <record id="knowledge_article_template_meeting_minutes_template" model="knowledge.article">
        <field name="icon">🖋️</field>
        <field name="parent_id" ref="knowledge_article_template_meeting_minutes"/>
        <field name="is_article_item">True</field>
        <field name="is_template">True</field>
        <field name="template_name">Meeting Minutes</field>
        <field name="template_category_id" ref="knowledge_article_template_category_productivity"/>
        <field name="template_sequence">0</field>
        <field name="template_body" type="html">
            <h1>Meeting Minutes Template</h1>
            <p>☝🏼 Please prepare the following before the meeting:</p>
            <ul>
                <li>
                    <font class="text-400">To Read 1</font>
                </li>
                <li>
                    <font class="text-400">To Read 2</font>
                </li>
            </ul>
            <table class="table table-bordered o_table">
                <tbody>
                    <tr>
                        <td>
                            <h2>🗣 Meeting Agenda</h2>
                        </td>
                        <td>
                            <h2>🙌🏼 Decisions Taken</h2>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-400">
                            <ul>
                                <li>Item 1</li>
                            </ul>
                        </td>
                        <td class="text-400">
                            <ul>
                                <li>Decision 1</li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-400">
                            <ul>
                                <li>Item 2</li>
                            </ul>
                        </td>
                        <td class="text-400">
                            <ul>
                                <li>Decision 2</li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-400">
                            <ul>
                                <li>Item 3</li>
                            </ul>
                        </td>
                        <td class="text-400">
                            <ul>
                                <li>Decision 3</li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-400">
                            <ul>
                                <li>Item 4</li>
                            </ul>
                        </td>
                        <td class="text-400">
                            <ul>
                                <li>Decision 4</li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-lg-6">
                        <p><br/></p>
                    </div>
                </div>
            </div>
            <h3>Next Meeting: <font class="text-400">@Date</font></h3>
        </field>
    </record>

    <record id="knowledge_article_template_account_management" model="knowledge.article">
        <field name="icon">🤝</field>
        <field name="is_template">True</field>
        <field name="template_name">Account Management</field>
        <field name="template_sequence">100</field>
        <field name="template_category_id" ref="knowledge_article_template_category_sales"/>
        <field name="template_description">Centralize account insights in one document for comprehensive monitoring and follow-up.</field>
        <field name="template_body" type="html">
            <h1>Account Management Cheat Sheet</h1>
            <hr/>
            <div data-embedded="tableOfContent"/>
            <blockquote>
                <font class="text-o-color-2">
                    <strong>Planned Next Step:</strong>
                </font>
                <br/>
                <br/>
            </blockquote>
            <p><br/></p>
            <h2>Company Details</h2>
            <hr/>
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <td class="bg-200">
                            <h3><strong>Fast Facts</strong></h3>
                        </td>
                        <td class="bg-200"><br/></td>
                    </tr>
                    <tr>
                        <td>Company Name:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td>Industry:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td>Company Location:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td># Employees:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td>Company Structure:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td>Estimated Revenues:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td class="bg-200">
                            <h3>Sales Details</h3>
                        </td>
                        <td class="bg-200"><br/></td>
                    </tr>
                    <tr>
                        <td>Current Contract:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td>Contract Due Date:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td>Current Satisfaction:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td class="bg-200">
                            <h3>Objectives with our Collaboration</h3>
                        </td>
                        <td class="bg-200"><br/></td>
                    </tr>
                    <tr>
                        <td>Issues they are trying to solve:</td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td>How they measure success:</td>
                        <td><br/></td>
                    </tr>
                    <tr style="height: 90px;">
                        <td>Potential Risks:</td>
                        <td><br/></td>
                    </tr>
                </tbody>
            </table>
            <h2>Stakeholders Analysis</h2>
            <hr/>
            <blockquote>
                <i>
                    <font style="color: rgb(8, 82, 148);">
                        <strong>Blue: Expects accurate and rigorous results</strong>
                    </font>
                </i>
                <p>
                    <i>
                        <font style="color: rgb(0, 255, 0);"><strong>Green: Values trust above all</strong></font><br/>
                        <font style="color: rgb(255, 0, 0);"><strong>Red: Values results above all</strong></font><br/>
                        <strong>
                            <font style="color: rgb(239, 198, 49);">
                                Yellow: Values creativity and enthusiasm above results
                            </font>
                        </strong>
                    </i>
                </p>
            </blockquote>
            <p><strong>Main Point of Contact:</strong></p>
            <ul>
                <li><font class="text-600">Name</font></li>
                <li><font class="text-600">Phone</font></li>
                <li><font class="text-600">Email</font></li>
                <li><font class="text-600">Job Title</font></li>
                <li><font class="text-600">Color</font></li>
            </ul>
            <table class="table table-bordered o_table">
                <tbody>
                    <tr>
                        <td class="text-black bg-200">
                            <span style="font-size: 18px;">
                                <strong>Name</strong>
                            </span>
                        </td>
                        <td class="text-black bg-200">
                            <span style="font-size: 18px;">
                                <strong>Role</strong>
                            </span>
                        </td>
                        <td class="text-black bg-200">
                            <span style="font-size: 18px;">
                                <strong>Color</strong>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><font class="text-600">Name 1</font></td>
                        <td><font class="text-600">Job Title 1</font></td>
                        <td><font class="text-600">Blue/Green/Red/Yellow</font></td>
                    </tr>
                    <tr>
                        <td><font class="text-600">Name 2</font></td>
                        <td><font class="text-600">Job Title 2</font></td>
                        <td><font class="text-600">Blue/Green/Red/Yellow</font></td>
                    </tr>
                    <tr>
                        <td><br/></td>
                        <td><br/></td>
                        <td><br/></td>
                    </tr>
                </tbody>
            </table>
            <p><br/></p>
            <h2>Challenges &amp; Competitive Landscape<br/></h2>
            <hr/>
            <table class="table table-bordered o_table">
                <tbody>
                    <tr>
                        <td class="bg-200">
                            <strong>
                                <span style="font-size: 18px;">Name</span>
                            </strong>
                        </td>
                        <td class="bg-200">
                            <strong>
                                <span style="font-size: 18px;">Strengths</span>
                            </strong>
                        </td>
                        <td class="bg-200">
                            <strong>
                                <span style="font-size: 18px;">Weaknesses</span>
                            </strong>
                        </td>
                    </tr>
                    <tr>
                        <td><font class="text-600">Competitor 1</font></td>
                        <td><br/></td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td><font class="text-600">Competitor 2</font></td>
                        <td><br/></td>
                        <td><br/></td>
                    </tr>
                    <tr>
                        <td><font class="text-600">Competitor 3</font></td>
                        <td><br/></td>
                        <td><br/></td>
                    </tr>
                </tbody>
            </table>
            <p><br/></p>
            <h2>Buying Process</h2>
            <hr/>
            <ul>
                <li><font class="text-600">What is their buyer's journey?</font></li>
                <li><font class="text-600">How do they compare and evaluate offers?</font></li>
                <li><font class="text-600">How do stakeholders interact regarding offers?</font></li>
                <li><font class="text-600">What is their key decision criteria?</font></li>
            </ul>
            <p><br/></p>
            <h2>Action Plan</h2>
            <hr/>
            <p>
                <font class="text-600">What is your action plan to move forward with this account?</font><br/>
                <font class="text-600">Which KPI will be used to measure this progress?</font>
            </p>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_brand_assets" model="knowledge.article">
        <field name="icon">🎨</field>
        <field name="is_template">True</field>
        <field name="template_name">Brand Assets</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_marketing"/>
        <field name="template_description">Distribute brand digital assets while ensuring compliance with company policies and guidelines.</field>
        <field name="template_body" type="html">
            <h1>Brand Assets</h1>
            <hr/>
            <p>Please submit a request for assistance to the Marketing Team if you fall into one of the following:</p>
            <ul>
                <li>Advertising that would appear online or on TV</li>
                <li>SEO &amp; SEA projects (betting on keywords, ...)</li>
                <li>Booklets for a total price &gt; $1000</li>
            </ul>
            <p>For all other categories, we simply require you to follow the rules listed below.</p>
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <td>
                            <span style="font-size: 24px;">
                                Make sure to comply as <strong>you will represent <u>our</u> brand</strong>
                            </span>
                            <br/>
                            <span style="font-size: 24px;">If in doubt, get in touch with us.</span>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p><br/></p>
            <h2>Logo</h2>
            <hr/>
            <p>
                Here are logos that you can use at your own convenience.
                <br/>
                They can also be shared with customers, journalists and resellers.
            </p>
            <div data-embedded="file" data-embedded-props='{
                "fileData":{
                    "extension": "png",
                    "filename": "Odoo Logo",
                    "mimetype": "image/png",
                    "name": "Odoo Logo.png",
                    "type": "url",
                    "url": "https://odoocdn.com/openerp_website/static/src/img/assets/png/odoo_logo.png?download=true"
                }
            }'/>
            <p><br/></p>
            <h4>Usage</h4>
            <ul>
                <li><strong>Do not recreate the Logo from scratch, use these ones</strong></li>
                <li>Logos should only be used in the colors provided</li>
                <li>Always include sufficient clear space around the logo</li>
            </ul>
            <p><br/></p>
            <h2>Brand Name Rules</h2>
            <hr/>
            <p>
                YourCompany is very proud of its brand image.
                <br/>
                When representing the brand, we thus ask you to be very cautious in how you refer to the company.
            </p>
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <td>
                            <h3>
                                <strong>
                                    <font style="color: rgb(148, 189, 123);">Do</font>
                                </strong>
                            </h3>
                            <ul>
                                <li>Capitalize the word <strong>YourCompany</strong>, except if it's part of an URL e.g. website/company</li>
                                <li>Always type <strong>YourCompany</strong> in the same font size and style as the content of the text</li>
                                <li>Write it without any article(<em><s>the</s> YourCompany, <s>a</s> YourCompany, ...</em>)</li>
                            </ul>
                        </td>
                        <td>
                            <h3>
                                <strong><font style="color: rgb(231, 99, 99);">Do Not</font></strong>
                            </h3>
                            <ul>
                                <li>Pluralize the trademark (e.g.<em>YourCompanies</em>)</li>
                                <li>Use the logo instead of the word inside sentences</li>
                                <li>Use the expression "YourCompanions" to refer to our community</li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
            <p><br/></p>
            <h2>"About Us" Template</h2>
            <hr/>
            <p>Use this template whenever you have an announcement to make.</p>
            <p><br/></p>
            <div data-embedded="clipboard">
                <div class="d-flex">
                    <div class="o_embedded_clipboard_label align-middle">Clipboard</div>
                </div>
                <div data-embedded-editable="clipboardContent">
                    <p>
                        250 Executive Park Blvd, Suite 3400 94134
                        <br/>
                        San Francisco California (US)
                        <br/>
                        United States
                        <br/>
                        <EMAIL>
                    </p>
                    <p><br/></p>
                    <p>YourCompany is proud to announce that...</p>
                    <p><br/></p>
                    <p>
                        About YourCompany: YourCompany is a team of passionate people whose goal is to improve everyone's life
                        through disruptive products.
                        We build great products to solve your business problems.
                        <br/>
                        Our products are designed for small to medium size companies willing to optimize their performance.
                    </p>
                    <p><br/></p>
                </div>
            </div>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_marketing_campaign_brief" model="knowledge.article">
        <field name="icon">✨</field>
        <field name="is_template">True</field>
        <field name="template_name">Marketing Campaign Brief</field>
        <field name="template_sequence">100</field>
        <field name="template_category_id" ref="knowledge_article_template_category_marketing"/>
        <field name="template_description">Document your Marketing Campaigns to prioritize key objectives and outcomes.</field>
        <field name="template_body" type="html">
            <h1>Marketing Campaign Brief</h1>
            <hr/>
            <p><br/></p>
            <h2>⌚ Elevator Pitch</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">
                        Describe your campaign in just a few words.
                    </font>
                </em>
            </p>
            <p><br/></p>
            <h2>🚀 Objective</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">
                        What are you trying to accomplish with this campaign?
                    </font>
                </em>
            </p>
            <p><br/></p>
            <h2>🎯 Target Audience</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">
                        Who are you trying to reach?
                    </font>
                </em>
            </p>
            <p><br/></p>
            <h2>📣 Message</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">
                        What are you trying to convince them of?
                    </font>
                </em>
            </p>
            <p><br/></p>
            <h2>📊 KPIs</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">
                        How will your measure progress?
                    </font>
                </em>
            </p>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_hr_faq" model="knowledge.article">
        <field name="icon">💼</field>
        <field name="is_template">True</field>
        <field name="template_name">HR FAQ</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_company_organization"/>
        <field name="template_description">Answer questions frequently asked by your employees</field>
        <field name="template_body" type="html">
            <h1>HR FAQ</h1>
            <hr/>
            <div data-embedded="tableOfContent"/>
            <p><br/></p>
            <h2>Expense Policy</h2>
            <h3>Who do I need to address if I need office supplies?</h3>
            <p>Instead of setting up complicated processes, <strong>we prefer to let our employees buy whatever they need</strong>. Just fill in an expense and we will reimburse you.</p>
            <p><br/></p>
            <h3>Why has my expense not been reimbursed? It has been accepted.</h3>
            <p>
                The Accounting team handles all payments on Fridays afternoon.
                <br/>
                If 2 weeks have passed and you are still waiting to be paid, get in touch with them.
            </p>
            <p><br/></p>
            <h2>Car Policy</h2>
            <h3>How do I know which model I can order or not?</h3>
            <p>Our catalog can be found here and is updated every 2 years. If you do not manage to find the specific model you are looking for, <a href="mailto:<EMAIL>" target="_blank">contact our FleetOfficer</a></p>
            <div data-embedded="file" data-embedded-props='{
                "fileData":{
                    "extension": "png",
                    "filename": "Car_Catalog.png",
                    "mimetype": "image/png",
                    "name": "Car_Catalog.png",
                    "type": "url",
                    "url": "/knowledge/static/src/demo/fleet_icon.png?download=true"
                }
            }'/>
            <p><br/></p>
            <h3>Who can drive my car?</h3>
            <p>
                Your car can be driven by <strong>you, your spouse and by any person living under the same roof</strong> as long as they have a valid permit.
            </p>
            <p><br/></p>
            <h2>Leaves &amp; Time Off</h2>
            <h3>What do I have to do if I cannot work?</h3>
            <p>Please refer to the chart below.</p>
            <p><br/></p>
            <table class="table table-bordered">
                <tbody>
                    <tr>
                        <td>An hour or two (medical appointment, ...)</td>
                        <td>Let your Team Leader know in advance.</td>
                    </tr>
                    <tr>
                        <td>A day or less</td>
                        <td>Inform HR and your Team Leader.</td>
                    </tr>
                    <tr>
                        <td>Absent for more than a day</td>
                        <td>See a doctor and send us the sick note.</td>
                    </tr>
                </tbody>
            </table>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_post_mortem" model="knowledge.article">
        <field name="icon">🧐</field>
        <field name="is_template">True</field>
        <field name="template_name">Post-mortem</field>
        <field name="template_sequence">100</field>
        <field name="template_category_id" ref="knowledge_article_template_category_company_organization"/>
        <field name="template_description">Analyze what went wrong and the underlying causes. Extract insights to avoid making similar mistakes in the future.</field>
        <field name="template_body" type="html">
            <h1>Post-Mortem Analysis</h1>
            <hr/>
            <h2>Issue Summary</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">Explain what went wrong in just a few words.</font>
                </em>
            </p>
            <p><br/></p>
            <h2>Impact</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">Explain the consequences of this issue.</font>
                </em>
            </p>
            <p><br/></p>
            <h2>Causes</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">How did it get to happen?</font>
                </em>
            </p>
            <p><br/></p>
            <h2>Recovery</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">How was it solved?</font>
                </em>
            </p>
            <p><br/></p>
            <h2>Outcome</h2>
            <hr/>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-sm-6">
                        <p><strong>Lessons Learnt:</strong></p>
                        <ol>
                            <li><font class="text-600">Lesson A</font></li>
                            <li><font class="text-600">Lesson B</font></li>
                            <li><font class="text-600">Lesson C</font></li>
                        </ol>
                    </div>
                    <div class="col-sm-6">
                        <p><strong>Actions Taken:</strong></p>
                        <ol>
                            <li><font class="text-600">Action A</font></li>
                            <li><font class="text-600">Action B</font></li>
                            <li><font class="text-600">Action C</font></li>
                        </ol>
                    </div>
                </div>
            </div>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_release_note" model="knowledge.article">
        <field name="icon">💻</field>
        <field name="cover_image_id" ref="knowledge_cover_release_note"/>
        <field name="is_template">True</field>
        <field name="template_name">Release Notes</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_product_management"/>
        <field name="template_description">Inform users about your latest software updates and improvements.</field>
        <field name="template_body" type="html">
            <h1>Release Notes 🎉</h1>
            <hr/>
            <div data-embedded="tableOfContent"/>
            <p><br/></p>
            <h1>VersionName - ReleaseDate</h1>
            <hr/>
            <p>
                <strong>Summary</strong>: <font class="text-600">What an exciting release! This time the focus was on...</font>
            </p>
            <h2>New Features 🎉</h2>
            <ul>
                <li><span class="text-600">New Feature 1</span></li>
                <li class="oe-nested">
                    <ul>
                        <li>
                            <font class="text-600">Detailed Explanation of the feature, with screenshots or a GIF</font>
                        </li>
                    </ul>
                </li>
                <li>
                    <font class="text-600">New Feature 2</font>
                </li>
                <li class="oe-nested">
                    <ul>
                        <li>
                            <font class="text-600">Detailed Explanation of the feature, with screenshots or a GIF</font>
                        </li>
                    </ul>
                </li>
            </ul>
            <h2>But Also...</h2>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-sm-6">
                        <h3>Improvements 🔬</h3>
                        <ul>
                            <li>
                                <font class="text-600">From now on, ...</font>
                            </li>
                        </ul>
                    </div>
                    <div class="col-sm-6">
                        <h3>Bug Fixes 🔨</h3>
                        <ul>
                            <li>
                                <font class="text-600">Fixed a bug where...</font>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_software_specification" model="knowledge.article">
        <field name="icon">✍️</field>
        <field name="is_template">True</field>
        <field name="template_name">Software Specification</field>
        <field name="template_sequence">100</field>
        <field name="template_category_id" ref="knowledge_article_template_category_product_management"/>
        <field name="template_description">Ensure all stakeholders of a product change are aligned by clearly communicating the requirements.</field>
        <field name="template_body" type="html">
            <h1>Software Specification</h1>
            <hr/>
            <h2>⭐ Release Notes</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">Explain in a single sentence what has been changed.</font>
                </em>
            </p>
            <p><br/></p>
            <h2>🔗 Links</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">List here all the material and documentation related to the task.</font>
                </em>
            </p>
            <p><a href="#" target="_blank" class="btn btn-secondary">Test Environment</a></p>
            <p><a href="#" target="_blank" class="btn btn-secondary">DESIGN PROTOTYPE</a></p>
            <p><a href="#" target="_blank" class="btn btn-secondary">Source Feedback</a></p>
            <p><br/></p>
            <h2>📝 Purpose</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">Explain in a few words the problem that this change is going to solve.</font>
                </em>
            </p>
            <p><br/></p>
            <h2>🧠 Functional Specifications</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">List here all the changes to implement.</font>
                </em>
            </p>
            <ul>
                <li><font class="text-600">Change 1</font></li>
                <li><font class="text-600">Change 2</font></li>
                <li><font class="text-600">Change 3</font></li>
            </ul>
            <p><br/></p>
            <h2>⚙ Technical Specifications</h2>
            <hr/>
            <p>
                <em>
                    <font class="text-600">Ask a Senior Engineer to fill this part before launching the task.</font>
                </em>
            </p>
            <table class="table table-bordered o_table">
                <tbody>
                    <tr>
                        <td>
                            <strong><span style="font-size: 18px;">Change</span></strong>
                        </td>
                        <td>
                            <strong><span style="font-size: 18px;">Complexity</span></strong>
                        </td>
                    </tr>
                    <tr>
                        <td>Change 1</td>
                        <td>
                            ​<span class="o_stars o_five_stars" id="checkId-1"><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span>
                        </td>
                    </tr>
                    <tr>
                        <td>Change 2</td>
                        <td>
                            ​<span class="o_stars o_five_stars" id="checkId-2"><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span>
                        </td>
                    </tr>
                    <tr>
                        <td>Change 3</td>
                        <td>
                            ​<span class="o_stars o_five_stars" id="checkId-3"><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span>
                        </td>
                    </tr>
                </tbody>
            </table>
            <pre>Extra Technical Instructions:</pre>
            <p><br/></p>
            <h3>❓ Open Questions</h3>
            <hr/>
            <p>
                <em>
                    <font class="text-600">Lay here any remaining question or doubt.</font>
                </em>
            </p>
            <blockquote>Q1: <font class="text-600">Would it be possible to...</font></blockquote>
            <blockquote>Q2: <font class="text-600">Would there be an issue if...</font></blockquote>
            <p><br/></p>
            <h3>🔍 Review Checklist</h3>
            <hr/>
            <p>
                <em>
                    <font class="text-600">Ask the developer to go through this checklist before asking for a review.</font>
                </em>
            </p>
            <ul class="o_checklist">
                <li id="checkId-4">
                    <font class="text-600">I made sure any visual change is responsive</font>
                </li>
                <li id="checkId-5">
                    <font class="text-600">I made sure it did not introduce any obvious regression</font>
                </li>
                <li id="checkId-6">
                    <font class="text-600">I made sure it did not introduce any security flaw</font>
                </li>
            </ul>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_product_catalog" model="knowledge.article">
        <field name="icon">🏡</field>
        <field name="cover_image_id" ref="knowledge_cover_house"/>
        <field name="is_template">True</field>
        <field name="template_name">Tiny House Catalog</field>
        <field name="template_sequence">300</field>
        <field name="template_category_id" ref="knowledge_article_template_category_sales"/>
        <field name="template_description">Create a simple catalog to provide technical details about your products.</field>
        <field name="article_properties_definition" eval="
            [{
                'name': 'available',
                'type': 'boolean',
                'string': 'Available',
                'view_in_cards': True
            }, {
                'name': 'total_surface',
                'type': 'float',
                'string': 'Total Surface (m²)',
                'view_in_cards': True
            }, {
                'name': 'price',
                'type': 'float',
                'string': 'Price ($)',
                'view_in_cards': True
            }, {
                'name': 'materials',
                'type': 'tags',
                'string': 'Materials',
                'view_in_cards': True,
                'tags': [
                    ['aluminium_shingles', 'Aluminium Shingles', 1],
                    ['membrane_roofing', 'Membrane Roofing', 2],
                    ['metal_frame', 'Metal Frame', 3],
                    ['metal_walls', 'Metal Walls', 4],
                    ['steel_shingles', 'Steel Shingles', 5],
                    ['vinyl_walls', 'Vinyl Walls', 6],
                    ['wooden_frame', 'Wooden Frame', 7],
                    ['wooden_walls', 'Wooden Walls', 8]
                ]
            }]"/>
        <field name="template_body" type="html">
            <h1>Tiny House Catalog</h1>
            <hr/>
            <div data-embedded="tableOfContent"/>
            <p><br/></p>
            <h2>Available Models ✅</h2>
            <div data-embedded="view" data-oe-protected="true" data-embedded-props="{
                'viewProps': {
                    'actionXmlId': 'knowledge.knowledge_article_item_action',
                    'displayName': 'Available Models',
                    'viewType': 'kanban',
                    'favoriteFilters': {
                        'Available Models': {
                            'name': 'Available Models',
                            'is_default': True,
                            'model_id': 'knowledge.article',
                            'domain': [('article_properties.available', '=', True)],
                            'context': {}
                        }
                    },
                    'context': {
                        'active_id': ref('knowledge.knowledge_article_template_product_catalog'),
                        'default_parent_id': ref('knowledge.knowledge_article_template_product_catalog'),
                        'default_is_article_item': True
                    }
                }
            }"/>
            <p><br/></p>
            <hr/>
            <h2>How to find the perfect model for your needs 😍</h2>
            <p>Here is a short guide that will help you pick the right tiny house for you.</p>
            <p><br/></p>
            <blockquote>
                <span style="font-size: 18px;">
                    1. How many people will live in this house?
                </span>
            </blockquote>
            <p>While tiny houses are inherently small, it's important to prioritize comfort when sharing such limited space. To ensure everyone's happiness, we recommend allowing at least 9m² per person. If you plan to live with multiple roommates, our Serenità model is highly recommended, providing ample space for each individual to enjoy<br/></p>
            <p><a class="o_knowledge_article_link btn btn-sm"
                target="_blank" data-oe-protected="true" contenteditable="false"
                data-res_id="ref('knowledge.knowledge_article_template_product_catalog_house_serenita')"
                >🏢 House Model - Serenità</a></p>
            <p></p>
            <p><br/></p>
            <blockquote>
                <span style="font-size: 18px;">
                    2. What is your budget?
                </span>
            </blockquote>
            <p>While tiny houses do offer a more cost-effective alternative to traditional houses, at MyCompany, we prioritize durability, which comes with a price. However, if you're on a tight budget, we have the perfect solution: our Cielo model.<br/>It provides everything you need while allowing you to save.<br/></p>
            <p></p>
            <p><a class="o_knowledge_article_link btn btn-sm"
                target="_blank" data-oe-protected="true" contenteditable="false"
                data-res_id="ref('knowledge.knowledge_article_template_product_catalog_house_cielo')"
                >🏠 House Model - Cielo</a></p>
            <p><br/></p>
            <blockquote>
                <span style="font-size: 18px;">
                    3. Which style do you prefer: Natural or Industrial?
                </span>
            </blockquote>
            <p>Whether your heart leans towards the rustic charm of a wooden hut or the intricate beauty of Victorian steelworks, we have you covered. Desiring a specific option? Rest assured, we are here to fulfill your wishes! Just get in touch with our architects and we will make sure to provide any specific choice you desire.</p>
            <p>Natural Style ☘ -<a class="o_knowledge_article_link btn btn-sm"
                target="_blank" data-oe-protected="true" contenteditable="false"
                data-res_id="ref('knowledge.knowledge_article_template_product_catalog_house_incanto')"
                >🏘️ House Model - Incanto</a></p>
            <p>Industrial ⚙-<a class="o_knowledge_article_link btn btn-sm"
                target="_blank" data-oe-protected="true" contenteditable="false"
                data-res_id="ref('knowledge.knowledge_article_template_product_catalog_house_dolcezza')"
                >🛕 House Model - Dolcezza</a></p>
            <p><br/></p>
            <blockquote>
                <span style="font-size: 18px;">
                    4. What can I do if I haven't found exactly what I wanted?"
                </span>
            </blockquote>
            <p>If none of those offers convinced you, just get in touch with our team.<br/>At MyCompany, your happiness is our utmost priority, and we'll go the extra mile to make sure you find what you're looking for!</p>
            <p><a href="mailto:<EMAIL>" target="_blank" class="btn btn-secondary">Contact Us</a><br/></p>
            <p><br/></p>
            <hr/>
            <p><br/></p>
            <h2>All Models 📖</h2>
            <div data-embedded="view" data-oe-protected="true" data-embedded-props="{
                'viewProps': {
                    'actionXmlId': 'knowledge.knowledge_article_item_action',
                    'displayName': 'All Models',
                    'viewType': 'list',
                    'context': {
                        'active_id': ref('knowledge.knowledge_article_template_product_catalog'),
                        'default_parent_id': ref('knowledge.knowledge_article_template_product_catalog'),
                        'default_is_article_item': True
                    }
                }
            }"/>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_product_catalog_house_serenita" model="knowledge.article">
        <field name="icon">🏢</field>
        <field name="parent_id" ref="knowledge_article_template_product_catalog"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'price': 80000,
            'available': True,
            'materials': ['metal_frame', 'membrane_roofing', 'vinyl_walls'],
            'total_surface': 35
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">House Model "Serenità"</field>
        <field name="template_sequence">300</field>
        <field name="template_category_id" ref="knowledge_article_template_category_sales"/>
        <field name="template_body" type="html">
            <h1>House Model - Serenità</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_product_catalog_house_cielo" model="knowledge.article">
        <field name="icon">🏠</field>
        <field name="parent_id" ref="knowledge_article_template_product_catalog"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'price': 35000,
            'available': False,
            'materials': ['wooden_frame', 'membrane_roofing', 'wooden_walls'],
            'total_surface': 35
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">House Model "Cielo"</field>
        <field name="template_category_id" ref="knowledge_article_template_category_sales"/>
        <field name="template_sequence">300</field>
        <field name="template_body" type="html">
            <h1>House Model - Cielo</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_product_catalog_house_dolcezza" model="knowledge.article">
        <field name="icon">🛕</field>
        <field name="parent_id" ref="knowledge_article_template_product_catalog"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'price': 40000,
            'available': True,
            'materials': ['metal_frame', 'steel_shingles', 'metal_walls'],
            'total_surface': 18
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">House Model "Dolcezza"</field>
        <field name="template_sequence">400</field>
        <field name="template_category_id" ref="knowledge_article_template_category_sales"/>
        <field name="template_body" type="html">
            <h1>House Model - Dolcezza</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_product_catalog_house_incanto" model="knowledge.article">
        <field name="icon">🏘️</field>
        <field name="parent_id" ref="knowledge_article_template_product_catalog"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'price': 46000,
            'available': False,
            'materials': ['wooden_frame', 'aluminium_shingles', 'vinyl_walls'],
            'total_surface': 22
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">House Model "Incanto"</field>
        <field name="template_sequence">500</field>
        <field name="template_category_id" ref="knowledge_article_template_category_sales"/>
        <field name="template_body" type="html">
            <h1>House Model - Incanto</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_marketing_customer_personas" model="knowledge.article">
        <field name="icon">👤</field>
        <field name="article_properties_definition" eval="
            [{
                'name': 'channels',
                'type': 'tags',
                'string': 'Channels',
                'view_in_cards': True,
                'tags': [
                    ['billboards', 'Billboards', 1],
                    ['radio_ads', 'Radio ads', 2],
                    ['social_media', 'Social media', 3],
                    ['tv_ads', 'TV ads', 4],
                ]
            }]"/>
        <field name="is_template">True</field>
        <field name="template_name">Customer Personas</field>
        <field name="template_sequence">500</field>
        <field name="template_category_id" ref="knowledge_article_template_category_marketing"/>
        <field name="template_description">Build fictional representation of your customers to better tailor your advertising messages for them. </field>
        <field name="template_body" type="html">
            <h1>Customer Personas</h1>
            <hr/><br/>
            <div data-embedded="view" data-oe-protected="true" data-embedded-props="{
                'viewProps': {
                    'actionXmlId': 'knowledge.knowledge_article_item_action',
                    'displayName': 'Customer Personas',
                    'viewType': 'kanban',
                    'context': {
                        'active_id': ref('knowledge.knowledge_article_template_marketing_customer_personas'),
                        'default_parent_id': ref('knowledge.knowledge_article_template_marketing_customer_personas'),
                        'default_is_article_item': True
                    }
                }
            }"/>
            <p><br/></p>
            <p>
                <span style="font-size: 18px;">
                    To add more, use the clipboard below 👇🏼
                </span>
            </p>
            <div data-embedded="clipboard">
                <div data-embedded-editable="clipboardContent">
                    <h2><u>Demographics</u></h2>
                    <ul>
                        <li>Age:</li>
                        <li>Gender(s):</li>
                        <li>Education:</li>
                        <li>Income:</li>
                        <li>Interests:</li>
                    </ul>
                    <p><br/></p>
                    <h2><u>Key Decision Factors</u></h2>
                    <ul>
                        <li>Price <span class="o_stars o_five_stars" id="checkId-1"><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span>​</li>
                        <li>Speed of Service ​<span class="o_stars o_five_stars" id="checkId-2"><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                        <li>Brand Attraction <span class="o_stars o_five_stars" id="checkId-3"><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                        <li>Social Status <span class="o_stars o_five_stars" id="checkId-4"><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                        <li>After-Sales Service​ <span class="o_stars o_five_stars" id="checkId-5"><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                    </ul>
                    <p><br/></p>
                    <h2><u>Strategy</u></h2>
                    <p>
                        <font class="text-400">
                            <em>How to best reach this customer type.</em>
                        </font>
                    </p>
                    <p><br/></p>
                </div>
            </div>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_marketing_customer_persona_1" model="knowledge.article">
        <field name="icon">👱‍♀️</field>
        <field name="cover_image_id" ref="knowledge_cover_persona_1"/>
        <field name="parent_id" ref="knowledge_article_template_marketing_customer_personas"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'channels': ['radio_ads'],
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Persona 1</field>
        <field name="template_sequence">100</field>
        <field name="template_category_id" ref="knowledge_article_template_category_marketing"/>
        <field name="template_body" type="html">
            <h1>Sonya</h1>
            <h2><u>Demographics</u></h2>
            <ul>
                <li>Age: 42</li>
                <li>Gender(s): W</li>
                <li>Education: Bachelor's degree in Marketing</li>
                <li>Income: $142,170</li>
                <li>Interests: Music</li>
            </ul>
            <p><br/></p>
            <h2><u>Key Decision Factors</u></h2>
            <ul>
                <li>Price <span class="o_stars o_five_stars" id="checkId-1"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                <li>Speed of Service <span class="o_stars o_five_stars" id="checkId-2"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                <li>Brand Attraction <span class="o_stars o_five_stars" id="checkId-3"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/></span></li>
                <li>Social Status ​<span class="o_stars o_five_stars" id="checkId-4"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/></span></li>
                <li>After-Sales Service <span class="o_stars o_five_stars" id="checkId-5"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
            </ul>
            <p><br/></p>
            <h2><u>Strategy</u></h2>
            <p>
                <font class="text-400">
                    <em>How to best reach this customer type.<br/></em>
                </font>
                As an avid music listener, the best way to reach Sonya is through the radio since hers is always on.
            </p>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_marketing_customer_persona_2" model="knowledge.article">
        <field name="icon">👨</field>
        <field name="cover_image_id" ref="knowledge_cover_persona_2"/>
        <field name="parent_id" ref="knowledge_article_template_marketing_customer_personas"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'channels': ['tv_ads'],
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Persona 2</field>
        <field name="template_sequence">200</field>
        <field name="template_category_id" ref="knowledge_article_template_category_marketing"/>
        <field name="template_body" type="html">
            <h1>Julius</h1>
            <h2><u>Demographics</u></h2>
            <ul>
                <li>Age: 29</li>
                <li>Gender(s): M</li>
                <li>Education: High school diploma</li>
                <li>Income: $109,160</li>
                <li>Interests: Politics</li>
            </ul>
            <p><br/></p>
            <h2><u>Key Decision Factors</u></h2>
            <ul>
                <li>Price <span class="o_stars o_five_stars" id="checkId-1"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/></span></li>
                <li>Speed of Service <span class="o_stars o_five_stars" id="checkId-2"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                <li>Brand Attraction <span class="o_stars o_five_stars" id="checkId-3"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                <li>Social <span class="o_stars o_five_stars" id="checkId-4"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/></span></li>
                <li>After-Sales Service <span class="o_stars o_five_stars" id="checkId-5"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
            </ul>
            <p><br/></p>
            <h2><u>Strategy</u></h2>
            <p>
                <font class="text-400">
                    <em>How to best reach this customer type.<br/></em>
                </font>
                Julius follows politics very tightly, and can best be reached with TV ads.
            </p>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_marketing_customer_persona_3" model="knowledge.article">
        <field name="icon">👩‍🔬</field>
        <field name="cover_image_id" ref="knowledge_cover_persona_3"/>
        <field name="parent_id" ref="knowledge_article_template_marketing_customer_personas"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'channels': ['social_media', 'billboards'],
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Persona 3</field>
        <field name="template_category_id" ref="knowledge_article_template_category_marketing"/>
        <field name="template_sequence">300</field>
        <field name="template_body" type="html">
            <h1>Abigail</h1>
            <h2><u>Demographics</u></h2>
            <ul>
                <li>Age: 29</li>
                <li>Gender(s): W</li>
                <li>Education: PhD.</li>
                <li>Income: $293,650</li>
                <li>Interests: Science</li>
            </ul>
            <p><br/></p>
            <h2><u>Key Decision Factors</u></h2>
            <ul>
                <li>Price <span class="o_stars o_five_stars" id="checkId-1"><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                <li>Speed of Service <span class="o_stars o_five_stars" id="checkId-2"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/></span></li>
                <li>Brand Attraction <span class="o_stars o_five_stars" id="checkId-3"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/></span></li>
                <li>Social Status <span class="o_stars o_five_stars" id="checkId-4"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                <li>After-Sales Service​ <span class="o_stars o_five_stars" id="checkId-5"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
            </ul>
            <p><br/></p>
            <h2><u>Strategy</u></h2>
            <p>
                <font class="text-400">
                    <em>How to best reach this customer type.<br/></em>
                </font>
                Abigail works a lot and never watches TV. Better reach her on her phone or on her to work.
            </p>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_marketing_customer_persona_4" model="knowledge.article">
        <field name="icon">👨‍🍳</field>
        <field name="cover_image_id" ref="knowledge_cover_persona_4"/>
        <field name="parent_id" ref="knowledge_article_template_marketing_customer_personas"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'channels': ['social_media']
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Persona 4</field>
        <field name="template_sequence">400</field>
        <field name="template_category_id" ref="knowledge_article_template_category_marketing"/>
        <field name="template_body" type="html">
            <h1>Vittorio</h1>
            <h2><u>Demographics</u></h2>
            <ul>
                <li>Age: 24</li>
                <li>Gender(s): M</li>
                <li>Education: Student</li>
                <li>Income: $68,170</li>
                <li>Interests: Cooking</li>
            </ul>
            <p><br/></p>
            <h2><u>Key Decision Factors</u></h2>
            <ul>
                <li>Price <span class="o_stars o_five_stars" id="checkId-1"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/></span></li>
                <li>Speed of Service <span class="o_stars o_five_stars" id="checkId-2"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                <li>Brand Attraction <span class="o_stars o_five_stars" id="checkId-3"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/><i class="fa fa-star-o"/></span></li>
                <li>Social Status <span class="o_stars o_five_stars" id="checkId-4"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star-o"/></span></li>
                <li>After-Sales Service <span class="o_stars o_five_stars" id="checkId-5"><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/><i class="fa fa-star"/></span></li>
            </ul>
            <p><br/></p>
            <h2><u>Strategy</u></h2>
            <p>
                <font class="text-400">
                    <em>How to best reach this customer type.<br/></em>
                </font>
                As a classic Gen Z member, Vittorio never watches TV and never listens to the radio. For him to see our message, we need to get to his Instagram feed.
            </p>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_sprint_calendar" model="knowledge.article">
        <field name="icon">🏃</field>
        <field name="full_width">True</field>
        <field name="article_properties_definition" eval="
            [{
                'name': 'start_date_time',
                'type': 'datetime',
                'string': 'Start Date Time'
            }, {
                'name': 'end_date_time',
                'type': 'datetime',
                'string': 'End Date Time'
            }]"/>
        <field name="is_template">True</field>
        <field name="template_name">Sprint Calendar</field>
        <field name="template_description">Manage your team schedule for the upcoming sprint.</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_product_management"/>
        <field name="template_body" type="html">
            <h1>Sprint Calendar</h1>
            <hr/><br/>
            <div data-embedded="view" data-oe-protected="true" data-embedded-props="{
                'viewProps': {
                    'actionXmlId': 'knowledge.knowledge_article_action_item_calendar',
                    'displayName': 'Sprint Calendar',
                    'viewType': 'calendar',
                    'context': {
                        'active_id': ref('knowledge.knowledge_article_template_sprint_calendar'),
                        'default_parent_id': ref('knowledge.knowledge_article_template_sprint_calendar'),
                        'default_is_article_item': True
                    },
                    'additionalViewProps': {
                        'itemCalendarProps': {
                            'dateStartPropertyId': 'start_date_time',
                            'dateStopPropertyId': 'end_date_time',
                            'dateType': 'datetime',
                            'scale': 'week'
                        }
                    }
                }
            }"/>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_sprint_calendar_meeting" model="knowledge.article">
        <field name="icon">🧑‍🤝‍🧑</field>
        <field name="parent_id" ref="knowledge_article_template_sprint_calendar"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'start_date_time': (DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 08:00:00'),
            'end_date_time': (DateTime.now() + timedelta(days=1)).strftime('%Y-%m-%d 09:00:00')
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Meeting</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_product_management"/>
        <field name="template_body" type="html">
            <h1>Meeting</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_sprint_calendar_public_holiday" model="knowledge.article">
        <field name="icon">🏖️</field>
        <field name="parent_id" ref="knowledge_article_template_sprint_calendar"/>
        <field name="is_article_item">True</field>
        <field name="article_properties" eval="{
            'start_date_time': (DateTime.now() + timedelta(days=2)).strftime('%Y-%m-%d 08:00:00'),
            'end_date_time': (DateTime.now() + timedelta(days=3)).strftime('%Y-%m-%d 18:00:00')
        }"/>
        <field name="is_template">True</field>
        <field name="template_name">Public Holidays</field>
        <field name="template_sequence">100</field>
        <field name="template_category_id" ref="knowledge_article_template_category_product_management"/>
        <field name="template_body" type="html">
            <h1>Public Holidays</h1>
            <p><br/></p>
        </field>
    </record>

    <record id="knowledge_article_template_company_newsletter" model="knowledge.article">
        <field name="icon">💌</field>
        <field name="is_template">True</field>
        <field name="template_name">Company Newsletter</field>
        <field name="template_sequence">0</field>
        <field name="template_category_id" ref="knowledge_article_template_category_company_organization"/>
        <field name="template_description">Keep your colleagues informed about the company's latest developments and activities through periodic updates.</field>
        <field name="template_body" type="html">
            <h1>
                <span style="font-weight: bolder;">
                    Company Newsletter
                </span>: <font class="text-400">Month</font>
            </h1>
            <hr/>
            <p>
                Welcome to the last edition of the MyCompany Newsletter!<br/>
                We are very excited to share with you the hottest updates and news! 🤩
            </p>
            <p><br/></p>
            <div data-embedded="tableOfContent"/>
            <p><br/></p>
            <h2>Podcast updates 📻</h2>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-sm-6">
                        <p>The podcast launches its new technical talks, for all tech-savvy listeners out there! This
                            new episode of the series features Géry, the mastermind behind OWL, the world fastest JS
                            framework. 🚀</p>
                        <p>A must listen for all developers out there and anyone interested in Javascript!
                            <a href="https://emojipedia.org/man-technologist/" target="_blank">👨‍💻</a>
                            <br/>
                            Subscribe here to make sure you will not miss an episode!
                        </p>
                        <p><a href="https://www.odoo.fm/2108856/12216790-owl-the-fastest-javascript-framework-in-the-world"
                            target="_blank" class="btn btn-primary">PLANET ODOO</a>
                            <br/>
                        </p>
                        <p><br/></p>
                    </div>
                    <div class="col-sm-6">
                        <p><img src="/knowledge/static/src/img/podcast.jpg"
                                class="img img-fluid o_we_custom_image" data-bs-original-title="" title=""
                                aria-describedby="tooltip813995"/><br/></p>
                    </div>
                </div>
            </div>
            <p><br/></p>
            <h2>Events 🌍</h2>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-sm-6">
                        <p><img src="/knowledge/static/src/img/entrepreneurship_fair.jpg"
                                class="img img-fluid o_we_custom_image"/><br/></p>
                    </div>
                    <div class="col-sm-6">
                        <h3>The Future Entrepreneurship Fair</h3>
                        <p>This year again, Odoo was present at the <em>Tech and Innovation festival for students</em> in
                            Antwerp, ready to promote our company!<br/>Fabien was also there and gave an interview on the main
                            stage.<a href="https://emojipedia.org/loudspeaker/" target="_blank">📢</a></p>
                    </div>
                </div>
            </div>
            <p><br/></p>
            <h3>Odoo Experience 🎉</h3>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-sm-6">
                        <h1><img src="/knowledge/static/src/img/odoo_experience.jpg"
                                class="img img-fluid o_we_custom_image"/><br/></h1>
                    </div>
                    <div class="col-sm-6">
                        <p><strong style="font-weight: 500"><span style="font-size: 14px">
                            Early bird alert</span></strong>
                            <a href="https://emojipedia.org/bird/" target="_blank">🐦</a></p>
                        <p>The tickets to participate to Odoo Experience 23 are now available and they are cheaper if you book them now!</p>
                        <p>Don't forget to spread the word, we're <em>so</em> looking forward to unveiling this new Odoo version! 🥳</p>
                        <p><a href="https://www.odoo.com/event/odoo-experience-2023-3735/register" class="btn btn-primary">BOOK NOW</a><br/></p>
                        <p><br/></p>
                    </div>
                </div>
            </div>
            <p><br/></p>
            <h2>Egg'cellent run<a href="https://emojipedia.org/rabbit-face/" target="_blank">🐰</a></h2>
            <div class="container o_text_columns">
                <div class="row">
                    <div class="col-sm-6">
                        <p>More than 150 Odooers participated in this years' edition of the run of 5 or 11
                            kilometers.<br/>Starting from the office, they enjoyed a great tour in the countryside before
                            coming back to Grand-Rosière, where they were welcomed with a drink and a burger.<a
                                href="https://emojiguide.com/food-drink/">🍔</a></p>
                        <p>Thank you to everyone involved in the organization of this edition of the
                            <font class="text-o-color-2">
                                <strong>Odoo Run</strong>
                            </font>!</p>
                        <p>We already cannot wait for the next one🏃</p>
                    </div>
                    <div class="col-sm-6">
                        <p><img src="/knowledge/static/src/img/odoo_run.jpg"
                                class="img img-fluid o_we_custom_image rounded-circle" style="width: 50%;"/><br/></p>
                    </div>
                </div>
            </div>
            <p><br/></p>
            <p>
                <span style="font-size: 36px;">And that's all for this month, folks!<br/>
                    Thanks for reading, see you soon.👋
                </span>
            </p>
        </field>
    </record>
</odoo>
