# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* knowledge
# 
# Translators:
# <PERSON><PERSON>, 2025
# Wil Odoo, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-10-25 09:12+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Wil Odoo, 2025\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (Selection)"
msgstr "\" (Sélection)"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (date and time)"
msgstr "\" (date et heure)"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "\" (date)"
msgstr "\" (date)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_item_name)s\" is an Article Item from \"%(article_name)s\" and "
"cannot be restored on its own. Contact the owner of \"%(article_name)s\" to "
"have it restored instead."
msgstr ""
"\"%(article_item_name)s\" est un élément d'article de \"%(article_name)s\" "
"et ne peut pas être restauré seul. Contactez le popriétaire de "
"\"%(article_name)s\" pour obtenir une restauration."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_name)s\" is a template and can not be a child of an article "
"(\"%(parent_article_name)s\")."
msgstr ""
"\"%(article_name)s\" est un modèle et ne peut pas être un enfant d'un "
"article (\"%(parent_article_name)s\")."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"\"%(article_name)s\" is an article and can not be a child of a template "
"(\"%(parent_article_name)s\").\""
msgstr ""
"\"%(article_name)s\" est un article et ne peut pas être un enfant d'un "
"modèle (\"%(parent_article_name)s\").\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "\"About Us\" Template"
msgstr "Modèle \"À propos de nous\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "# Employees:"
msgstr "# Employés :"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__favorite_count
msgid "#Is Favorite"
msgstr "#Est favori"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "%(article_name)s (copy)"
msgstr "%(article_name)s (copie)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "%s has been sent to Trash"
msgstr "%s a été envoyé à la corbeille"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid ".<br/>"
msgstr ".<br/>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"250 Executive Park Blvd, Suite 3400 94134\n"
"                        <br>\n"
"                        San Francisco California (US)\n"
"                        <br>\n"
"                        United States\n"
"                        <br>\n"
"                        <EMAIL>"
msgstr ""
"250 Executive Park Blvd, Suite 3400 94134\n"
"                        <br>\n"
"                        San Francisco California (US)\n"
"                        <br>\n"
"                        United States\n"
"                        <br>\n"
"                        <EMAIL>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "85x200 or 100x200, depending on the content"
msgstr "85x200 ou 100x200, en fonction du contenu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<br>\n"
"                            Subscribe here to make sure you will not miss an episode!"
msgstr ""
"<br>\n"
"                            Inscrivez-vous ici pour être sûr de ne rater aucun épisode !"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Describe your campaign in just a few words.\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Décrivez votre campagne en quelques mots.\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        How will your measure progress?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Comment comptez-vous mesurer le progrès réalisés ?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        What are you trying to accomplish with this campaign?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Quel est l'objectif de cette campagne ?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        What are you trying to convince them of?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        De quoi essayez-vous de les convaincre ?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Who are you trying to reach?\n"
"                    </font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">\n"
"                        Qui essayez-vous d'atteindre ?\n"
"                    </font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Ask a Senior Engineer to fill this part before launching the task.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Demandez à un ingénieur senior de compléter cette partie avant de lancer la tâche.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Ask the developer to go through this checklist before asking for a review.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Demandez au développeur de parcourir cette check-list avant de demander une révision.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain in a few words the problem that this change is going to solve.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Expliquez en quelques mots quel problème ce changement va résoudre.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain in a single sentence what has been changed.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Expliquez en une seule phrase ce qui a été changé.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain the consequences of this issue.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Expliquez les conséquences de ce problème.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Explain what went wrong in just a few words.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Expliquez ce qui n'a pas fonctionné en quelques mots.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">How did it get to happen?</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Comment est-ce arrivé ?</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid ""
"<em>\n"
"                    <font class=\"text-600\">How was it solved?</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Quelle a été la solution ?</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">Lay here any remaining question or doubt.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Posez ici toute question ou tout doute subsistant.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the changes to implement.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Répertoriez ici tous les changements à implémenter.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the material and documentation related to the task.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Répertoriez ici le matériel et la documentation associés à la tâche.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<em>\n"
"                    <font class=\"text-600\">List here all the text you could not do this week. These shall be postponed in the next weekly schedule.</font>\n"
"                </em>"
msgstr ""
"<em>\n"
"                    <font class=\"text-600\">Répertoriez ici tout ce que vous n'avez pas réussi à faire cette semaine. Ces tâches seront reportées dans l'agenda de la semaine prochaine.</font>\n"
"                </em>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"<font class=\"text-400\">\n"
"                            <em>How to best reach this customer type.</em>\n"
"                        </font>"
msgstr ""
"<font class=\"text-400\">\n"
"                            <em>Comment atteindre au mieux ce type de client.</em>\n"
"                        </font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                Abigail works a lot and never watches TV. Better reach her on her phone or on her to work."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>Comment atteindre le mieux ce type de client.<br></em>\n"
"                </font>\n"
"                Abigail travaille beaucoup et ne regarde jamais la télévision. Il vaut mieux l'atteindre sur son téléphone ou sur le chemin du travail."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                As a classic Gen Z member, Vittorio never watches TV and never listens to the radio. For him to see our message, we need to get to his Instagram feed."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>Comment atteindre le mieux ce type de client.<br></em>\n"
"                </font>\n"
"                En tant que membre classique de la génération Z, Vittorio ne regarde jamais la télévision et n'écoute jamais la radio. Pour qu'il voie notre message, il doit apparaître sur son feed Instagram."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                As an avid music listener, the best way to reach Sonya is through the radio since hers is always on."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>Comment atteindre le mieux ce type de client.<br></em>\n"
"                </font>\n"
"                En tant qu'auditrice passionnée de musique, le meilleur moyen de joindre Sonya est la radio, car la sienne est toujours allumée."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"<font class=\"text-400\">\n"
"                    <em>How to best reach this customer type.<br></em>\n"
"                </font>\n"
"                Julius follows politics very tightly, and can best be reached with TV ads."
msgstr ""
"<font class=\"text-400\">\n"
"                    <em>Comment atteindre le mieux ce type de client.<br></em>\n"
"                </font>\n"
"                Julius suit la politique de près et la meilleure façon de l'atteindre est de diffuser de publicités télévisées."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "<font class=\"text-400\">To Read 1</font>"
msgstr "<font class=\"text-400\">À lire 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "<font class=\"text-400\">To Read 2</font>"
msgstr "<font class=\"text-400\">À lire 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action A</font>"
msgstr "<font class=\"text-600\">Action A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action B</font>"
msgstr "<font class=\"text-600\">Action B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Action C</font>"
msgstr "<font class=\"text-600\">Action C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 1</font>"
msgstr "<font class=\"text-600\">Tâche en retard 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 2</font>"
msgstr "<font class=\"text-600\">Tâche en retard 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Backlog 3</font>"
msgstr "<font class=\"text-600\">Tâche en retard 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Blue/Green/Red/Yellow</font>"
msgstr "<font class=\"text-600\">Bleu/Vert/Rouge/Jaune</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 1</font>"
msgstr "<font class=\"text-600\">Changement 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 2</font>"
msgstr "<font class=\"text-600\">Changement 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<font class=\"text-600\">Change 3</font>"
msgstr "<font class=\"text-600\">Changement 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Color</font>"
msgstr "<font class=\"text-600\">Coleur</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 1</font>"
msgstr "<font class=\"text-600\">Concurrent 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 2</font>"
msgstr "<font class=\"text-600\">Concurrent 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Competitor 3</font>"
msgstr "<font class=\"text-600\">Concurrent 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid ""
"<font class=\"text-600\">Detailed Explanation of the feature, with "
"screenshots or a GIF</font>"
msgstr ""
"<font class=\"text-600\">Explication détaillée de la fonctionnalité, avec "
"des captures d'écran ou un GIF</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Email</font>"
msgstr "<font class=\"text-600\">E-mail</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">Fixed a bug where...</font>"
msgstr "<font class=\"text-600\">Correction d'un bug qui...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">From now on, ...</font>"
msgstr "<font class=\"text-600\">Désormais, ...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-600\">How do stakeholders interact regarding "
"offers?</font>"
msgstr ""
"<font class=\"text-600\">Comment les parties prenantes interagissent-elles "
"en ce qui concerne les offres ?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">How do they compare and evaluate offers?</font>"
msgstr ""
"<font class=\"text-600\">Comment est-ce qu'ils comparent et évaluent les "
"offres ?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure any visual change is responsive</font>"
msgstr ""
"<font class=\"text-600\">J'ai veillé à ce que tout changement visuel soit "
"adapté</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure it did not introduce any obvious "
"regression</font>"
msgstr ""
"<font class=\"text-600\">J'ai veillé à ne pas introduire de régression "
"évidente</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"<font class=\"text-600\">I made sure it did not introduce any security "
"flaw</font>"
msgstr ""
"<font class=\"text-600\">J'ai veillé à ne pas introduire de faille de "
"sécurité</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title 1</font>"
msgstr "<font class=\"text-600\">Fonction 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title 2</font>"
msgstr "<font class=\"text-600\">Fonction 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Job Title</font>"
msgstr "<font class=\"text-600\">Fonction</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson A</font>"
msgstr "<font class=\"text-600\">Leçon A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson B</font>"
msgstr "<font class=\"text-600\">Leçon B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<font class=\"text-600\">Lesson C</font>"
msgstr "<font class=\"text-600\">Leçon C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name 1</font>"
msgstr "<font class=\"text-600\">Nom 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name 2</font>"
msgstr "<font class=\"text-600\">Nom 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Name</font>"
msgstr "<font class=\"text-600\">Nom</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<font class=\"text-600\">New Feature 2</font>"
msgstr "<font class=\"text-600\">Nouvelle fonctionnalité 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">Phone</font>"
msgstr "<font class=\"text-600\">Téléphone</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority A</font>"
msgstr "<font class=\"text-600\">Priorité A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority B</font>"
msgstr "<font class=\"text-600\">Priorité B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Priority C</font>"
msgstr "<font class=\"text-600\">Priorité C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 1</font>"
msgstr "<font class=\"text-600\">Rappel 1</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 2</font>"
msgstr "<font class=\"text-600\">Rappel 2</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Reminder 3</font>"
msgstr "<font class=\"text-600\">Rappel 3</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task A</font>"
msgstr "<font class=\"text-600\">Tâche A</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task B</font>"
msgstr "<font class=\"text-600\">Tâche B</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task C</font>"
msgstr "<font class=\"text-600\">Tâche C</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<font class=\"text-600\">Task D</font>"
msgstr "<font class=\"text-600\">Tâche D</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">What is their buyer's journey?</font>"
msgstr "<font class=\"text-600\">Quel est le parcours de leur acheteur ?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<font class=\"text-600\">What is their key decision criteria?</font>"
msgstr ""
"<font class=\"text-600\">Quels sont leurs critères décisionnels essentiels "
"?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-600\">What is your action plan to move forward with this account?</font><br>\n"
"                <font class=\"text-600\">Which KPI will be used to measure this progress?</font>"
msgstr ""
"<font class=\"text-600\">Quel est votre plan d'action pour faire progresser ce compte ?</font><br>\n"
"                <font class=\"text-600\">Quels sont les indicateurs clés de performance qui seront utilisés pour mesurer ces progrès ?</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<font class=\"text-o-color-2\">\n"
"                    <strong>Planned Next Step:</strong>\n"
"                </font>\n"
"                <br>\n"
"                <br>"
msgstr ""
"<font class=\"text-o-color-2\">\n"
"                    <strong>Étape suivante planifiée :</strong>\n"
"                </font>\n"
"                <br>\n"
"                <br>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid ""
"<i class=\"fa fa-w fa-info-circle\"/> All external users you selected won't "
"be added to the members."
msgstr ""
"<i class=\"fa fa-w fa-info-circle\"/> Tous les utilisateurs externes que "
"vous avez sélectionnés ne seront pas ajoutés aux membres."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban_items
msgid ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Remove from favorites\"/>\n"
"                                    <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Add to favorites\"/>"
msgstr ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Supprimer des favoris\"/>\n"
"                                    <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Ajouter aux favoris\"/>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban
msgid ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Remove from favorites\"/>\n"
"                                <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Add to favorites\"/>"
msgstr ""
"<i invisible=\"not is_user_favorite\" class=\"fa fa-star\" title=\"Retirer des favoris\"/>\n"
"                                <i invisible=\"is_user_favorite\" class=\"fa fa-star-o\" title=\"Ajouter aux favoris\"/>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<i>\n"
"                        <font style=\"color: rgb(0, 255, 0);\"><strong>Green: Values trust above all</strong></font><br>\n"
"                        <font style=\"color: rgb(255, 0, 0);\"><strong>Red: Values results above all</strong></font><br>\n"
"                        <strong>\n"
"                            <font style=\"color: rgb(239, 198, 49);\">\n"
"                                Yellow: Values creativity and enthusiasm above results\n"
"                            </font>\n"
"                        </strong>\n"
"                    </i>"
msgstr ""
"<i>\n"
"                        <font style=\"color: rgb(0, 255, 0);\"><strong>Vert : Valorise la confiance avant tout</strong></font><br>\n"
"                        <font style=\"color: rgb(255, 0, 0);\"><strong>Rouge : Valorise les résultats avant tout</strong></font><br>\n"
"                        <strong>\n"
"                            <font style=\"color: rgb(239, 198, 49);\">\n"
"                                Jaune : Valorise la créativité et l'enthousiasme avant les résultats\n"
"                            </font>\n"
"                        </strong>\n"
"                    </i>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<i>\n"
"                    <font style=\"color: rgb(8, 82, 148);\">\n"
"                        <strong>Blue: Expects accurate and rigorous results</strong>\n"
"                    </font>\n"
"                </i>"
msgstr ""
"<i>\n"
"                    <font style=\"color: rgb(8, 82, 148);\">\n"
"                        <strong>Bleu : Attend des résultats précis et rigoureux</strong>\n"
"                    </font>\n"
"                </i>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "<span class=\"text-600\">New Feature 1</span>"
msgstr "<span class=\"text-600\">Nouvelle fonctionnalité 1</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid ""
"<span style=\"color: rgb(255, 255, 255);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(113, 75, 103)\">\n"
"                                        5125C\n"
"                                    </span>"
msgstr ""
"<span style=\"color: rgb(255, 255, 255);font-size: 16px;font-style: normal;font-weight: 400;background-color: rgb(113, 75, 103)\">\n"
"                                        5125C\n"
"                                    </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 12px;\">\n"
"                    <font class=\"text-600\">*💡 tick the box when the task is scheduled in the agenda</font>\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 12px;\">\n"
"                    <font class=\"text-600\">*💡 cochez la case lorsque la tâche est planifiée dans l'agenda</font>\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task A</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Tâche A</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task B</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Tâche B</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Task C</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px\">\n"
"                                    <font class=\"text-600\">Tâche C</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task A</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Tâche A</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task B</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Tâche B</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Task C</font>\n"
"                                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                                    <font class=\"text-600\">Tâche C</font>\n"
"                                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a checklist\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Ajouter une check-list\n"
"                    (/<span style=\"font-style: italic;\">checklist</span>)\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Add a separator\n"
"                    (/<span style=\"font-style: italic;\">separator</span>)\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Ajouter un séparateur\n"
"                    (/<span style=\"font-style: italic;\">separateur</span>)\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"                    Use\n"
"                    /<span style=\"font-style: italic;\">heading</span>\n"
"                    to convert a text into a title\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"                    Utiliser\n"
"                    /<span style=\"font-style: italic;\">titre</span>\n"
"                    pour convertir un texte en un titre\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Favorites</font>\n"
"            </span>\n"
"            — Those are\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">shortcuts</font>\n"
"            </span>\n"
"            you create for yourself.\n"
"            Unstar ⭐ this page at the top to remove it from your favorites.\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Favoris</font>\n"
"            </span>\n"
"            — Il s'agit de\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">raccourcis</font>\n"
"            </span>\n"
"            yque vous créez pour vous-même.\n"
"            Supprimez l'étoile ⭐ de cette page en haut pour la supprimer de vos favoris.\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Private</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — This is\n"
"            <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">your stuff</font></span>,\n"
"            the things you keep for yourself\n"
"            (<span style=\"font-style: italic;\">Drafts, Todo lists, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Privé</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Il s'agit de\n"
"            <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">vos affaires</font></span>,\n"
"            les choses que vous gardez pour vous\n"
"            (<span style=\"font-style: italic;\">Brouillons, listes à faire, ...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Shared</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Those are the ones you\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">have invited someone or been invited to</font>\n"
"            </span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Partagé</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Il s'agit des ressources\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">auxquelles vous avez invité quelqu'un ou auxquelles vous avez été invité à</font>\n"
"            </span>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Workspace</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Articles there can be accessed by\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">your team</font>\n"
"        </span>\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Espace de travail</font>\n"
"            </span>\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            — Les articles qui s'y trouvent sont accessibles par\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">votre équipe</font>\n"
"        </span>\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Below this list, try\n"
"            <span style=\"font-weight: bolder;\">commands</span>\n"
"            by\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">typing</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            En dessous de cette liste, essayez\n"
"            <span style=\"font-weight: bolder;\">des commandes</span>\n"
"            en\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">tapant</font>\n"
"            </span>\n"
"            \"<span style=\"font-weight: bolder;\">/</span>\"\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Content — Just click and\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">start typing</font>\n"
"            </span>\n"
"            (<span style=\"font-style: italic;\">documentation, tips, reports, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Contenu — Cliquez simplement et\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">commencez à taper</font>\n"
"            </span>\n"
"            (<span style=\"font-style: italic;\">documentation, astuces, rapports, ...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Folders —\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Nest other Articles</font>\n"
"            </span>\n"
"            under it to regroup them\n"
"            (<span style=\"font-style: italic;\">per team, topic, project, ...</span>)\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Dossiers —\n"
"        </span>\n"
"        <span style=\"font-size: 14px;\">\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">Classer d'autres articles</font>\n"
"            </span>\n"
"            dans un dossier pour les regrouper\n"
"            (</span>par équipe, sujet, projet, ...</span>)\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Select text to\n"
"            <font class=\"bg-o-color-2\">Highlight</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">strikethrough</span>\n"
"            or\n"
"            <span style=\"font-weight: bolder;\">style</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">it</span>\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Sélectionnez le texte pour\n"
"            <font class=\"bg-o-color-2\">le mettre en Surbrillance</font>,\n"
"            <span style=\"text-decoration-line: line-through;\">le Barrer</span>\n"
"            ou\n"
"            <span style=\"font-weight: bolder;\">le styler</span>\n"
"            <span style=\"font-style: italic; text-decoration-line: underline;\">.</span>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Use\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">clipboard</font></span>\n"
"            to insert a\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">clipboard</font>\n"
"            </span>\n"
"            box. Need to re-use its content?\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Utilisez\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">presse-papiers</font></span>\n"
"            pour insérer une zone de\n"
"            <span style=\"font-weight: bolder;\">\n"
"                <font class=\"text-o-color-2\">presse-papiers</font>\n"
"            </span>\n"
"            . Vous voulez réutiliser son contenu ?\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"            Use\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">file</font></span>\n"
"            to share documents that are frequently needed\n"
"        </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"            Utilisez\n"
"            /<span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">fichier</font></span>\n"
"           pour partager des documents fréquemment utilisés\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        are stored into different\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Sections</font></span>:\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Les articles</font>\n"
"        </span>\n"
"        sont sauvegardés dans différentes\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Sections</font></span>:\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        A good workflow is to write your drafts in\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Private</font>\n"
"        </span>\n"
"        and, once done, move it from\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Private</font>\n"
"        </span>\n"
"        to\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Workspace</font>\n"
"        </span>\n"
"        to share it with everyone.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Un bon flux de travail est d'écrire vos brouillons en\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Privé</font>\n"
"        </span>\n"
"        et, une fois qu'ils sont prêts, de les déplacer de\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Privé</font>\n"
"        </span>\n"
"        vers\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">l'Espace de travail</font>\n"
"        </span>\n"
"        afin de les partager avec tout le monde.\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        And again, to move an\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Article</font>\n"
"        </span>\n"
"        from a\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Section</font>\n"
"        </span>\n"
"        to another, just\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Drag &amp; Drop</font>\n"
"        </span>\n"
"        it.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Encore une fois, pour déplacer un\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Article</font>\n"
"        </span>\n"
"        d'une\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Section</font>\n"
"        </span>\n"
"       vers une autre, il suffit de\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">le glisser et de le déposer</font>\n"
"        </span>\n"
"        .\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        This is where you and your team can centralize your\n"
"        <font class=\"text-o-color-2\" style=\"font-weight: bolder;\">Knowledge</font>\n"
"        and best practices! 🚀\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        C'est ici que vous et votre équipe pouvez centraliser vos\n"
"        <font class=\"text-o-color-2\" style=\"font-weight: bolder;\">Connaissances</font>\n"
"        et bonnes pratiques ! 🚀\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Those are your\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Articles</font></span>.\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Il s'agit de vos\n"
"        <span style=\"font-weight: bolder;\"><font class=\"text-o-color-2\">Articles</font></span>.\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        To change the way\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        are organized, you can simply\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Drag &amp; Drop</font>\n"
"        </span>\n"
"        them\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Pour changer la manière dont vos\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        sont organisés, vous pouvez simplement\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">les glisser et les déposer</font>\n"
"        </span>\n"
"        \n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        Want to go\n"
"        <span style=\"font-style: italic;\">even</span>\n"
"        faster? ⚡️\n"
"    </span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Access\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        by opening the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Command Palette</font>\n"
"        </span>\n"
"        (Ctrl+k/⌘+k) then search through articles by starting your query with\n"
"        \"<span style=\"font-weight: bolder;\">?</span>\".\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        Vous voulez aller\n"
"        <span style=\"font-style: italic;\">encore</span>\n"
"        plus vite ? ⚡️\n"
"    </span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">\n"
"        Accédez aux\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Articles</font>\n"
"        </span>\n"
"        en ouvrant la\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Palette de commandes</font>\n"
"        </span>\n"
"        (Ctrl+k/⌘+k) puis recherchez parmi les articles en commençant votre requête par\n"
"        \"<span style=\"font-weight: bolder;\"> ?</span>\".\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">\n"
"        👈 See the\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Menu</font>\n"
"        </span>\n"
"        there, on the left?\n"
"    </span>"
msgstr ""
"<span style=\"font-size: 14px;\">\n"
"        👈 Vous voyez le\n"
"        <span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-2\">Menu</font>\n"
"        </span>\n"
"        ici, sur la gauche ?\n"
"    </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">And voilà, it is that simple.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Et voilà, c'est aussi simple que ça.</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Check this box to indicate it's done</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Cochez cette case pour indiquer que c'est "
"fait</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Click anywhere, and just start "
"typing</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Cliquez n'importe où et commencez "
"simplement à écrire</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">Each of them can be used both as:</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Chacun d'eux peut être utilisé à la fois "
"comme :</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">From any Odoo document, find this article "
"by clicking on the 📗 icon in the chatter.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">À partir de n'importe quel document d'Odoo,"
" trouvez cet article en cliquant sur l'icône 📗 dans le chatter.</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">From this box, files can be previewed, "
"forwarded and downloaded. 📁</span>"
msgstr ""
"<span style=\"font-size: 14px;\">À partir de cette zone, vous pouvez "
"afficher, transférer et télécharger des fichiers. 📁</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Got it? Now let's try advanced features "
"🧠</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Vous avez compris ? Essayons maintenant des"
" fonctionnalités plus avancées 🧠</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Need this document somewhere? Come back "
"here by clicking on the 📗 icon in the chatter.</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Vous avez besoin de ce document quelque "
"part ? Revenez ici en cliquant sur l'icône 📗 dans le chatter.</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Not sure how to do it? Check the video "
"below 👇</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Vous ne savez pas comment faire ? Regardez "
"la vidéo ci-dessous 👇</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">Press Ctrl+Z/⌘+Z to undo any change</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Appuyez sur Ctrl+Z/⌘+Z pour annuler toute "
"modification</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">This private page is for you to play around with.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Ready to give it a spin?</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Cette page privée est pour vous, à vous de jouer.</span>\n"
"    <br/>\n"
"    <span style=\"font-size: 14px;\">Prêt à essayer ?</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 14px;\">Try the following 👇</span>"
msgstr "<span style=\"font-size: 14px;\">Essayez ceci 👇</span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-size: 14px;\">You can use the clipboard as a description,"
" a message or simply copy it to your clipboard! 👌</span>"
msgstr ""
"<span style=\"font-size: 14px;\">Vous pouvez utiliser le presse-papiers "
"comme une description, un message ou copiez-le simplement dans votre presse-"
"papiers ! 👌</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Color</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Couleur</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Name</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Nom</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Role</strong>\n"
"                            </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                                <strong>Fonction</strong>\n"
"                            </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    1. How many people will live in this house?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    1. Combien de personnes vivront dans cette maison ?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    2. What is your budget?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    2. Quel est votre budget ?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    3. Which style do you prefer: Natural or Industrial?\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    3. Quel style préférez-vous : Naturel ou Industriel ?\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    4. What can I do if I haven't found exactly what I wanted?\"\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    4. Que puis-je faire si je n'ai pas trouvé exactement ce que je voulais ?\"\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    Productivity is never an accident. It is always the result of a commitment to excellence, intelligent planning, and focused effort.\n"
"                </span>​\n"
"                - Paul J. Meyer"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    La productivité n'est jamais un accident. C'est toujours le résultat d'en engagement d'excellence, une planification intelligente et un effort ciblé.\n"
"                </span>​\n"
"                - Paul J. Meyer"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"<span style=\"font-size: 18px;\">\n"
"                    To add more, use the clipboard below 👇🏼\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 18px;\">\n"
"                    Pour en ajouter plus, utilisez le presse-papiers ci-dessous 👇🏼\n"
"                </span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"<span style=\"font-size: 24px;\">\n"
"                                Make sure to comply as <strong>you will represent <u>our</u> brand</strong>\n"
"                            </span>\n"
"                            <br>\n"
"                            <span style=\"font-size: 24px;\">If in doubt, get in touch with us.</span>"
msgstr ""
"<span style=\"font-size: 24px;\">\n"
"                                Veillez à respecter ces règles, car <strong>vous représenterez <u>notre</u> marque</strong>\n"
"                            </span>\n"
"                            <br>\n"
"                            <span style=\"font-size: 24px;\">En cas de doute, n'hésitez pas à nous contacter.</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<span style=\"font-size: 36px;\">And that's all for this month, folks!<br>\n"
"                    Thanks for reading, see you soon.👋\n"
"                </span>"
msgstr ""
"<span style=\"font-size: 36px;\">Et c'est tout pour ce mois-ci, les amis !<br>\n"
"                    Merci d'avoir pris le temps de lire, à bientôt.👋\n"
"                </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "<span style=\"font-size: 48px;\">🚀</span>"
msgstr "<span style=\"font-size: 48px;\">🚀</span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"                    Company Newsletter\n"
"                </span>: <font class=\"text-400\">Month</font>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"                    Newsletter de la société\n"
"                </span>: <font class=\"text-400\">Mois</font>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-1\">YouTube</font>\n"
"        </span>"
msgstr ""
"<span style=\"font-weight: bolder;\">\n"
"            <font class=\"text-o-color-1\">YouTube</font>\n"
"        </span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">Facebook</font></span>"
msgstr ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">Facebook</font></span>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">X</font></span>"
msgstr ""
"<span style=\"font-weight: bolder;\"><font class=\"text-o-"
"color-1\">X</font></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"<strong style=\"font-weight: 500\"><span style=\"font-size: 14px\">\n"
"                            Early bird alert</span></strong>"
msgstr ""
"<strong style=\"font-weight: 500\"><span style=\"font-size: 14px\">\n"
"                            Alerte précoce</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong style=\"font-weight: 500\">Optional Tasks</strong>"
msgstr "<strong style=\"font-weight: 500\">Tâches optionnelles</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong style=\"font-weight: 500\">⚡ TOP 3 PRIORITIES</strong>"
msgstr "<strong style=\"font-weight: 500\">⚡ TOP 3 DES PRIORITES</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"<strong>\n"
"                                    <font style=\"color: rgb(148, 189, 123);\">Do</font>\n"
"                                </strong>"
msgstr ""
"<strong>\n"
"                                    <font style=\"color: rgb(148, 189, 123);\">À faire</font>\n"
"                                </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">FRIDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">VENDREDI 🏠 @maison</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">MONDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">LUNDI 🏢 @bureau</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">Reminders</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">Rappels</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SATURDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SAMEDI 🏠 @maison</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">SUNDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">DIMANCHE 🏠 @maison</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">THURSDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">JEUDI 🏢 @bureau</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">TUESDAY 🏢 @office</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">MARDI 🏢 @bureau</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">WEDNESDAY 🏠 @home</font>\n"
"                                </span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">\n"
"                                    <font class=\"text-o-color-1\">MERCREDI 🏠 @maison</font>\n"
"                                </span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Name</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Nom</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Strengths</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Forces</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Weaknesses</span>\n"
"                            </strong>"
msgstr ""
"<strong>\n"
"                                <span style=\"font-size: 18px;\">Points faibles</span>\n"
"                            </strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong><font class=\"text-o-color-2\">PRO TIP</font></strong>: From a lead,"
" use the book button in the chatter to find this article and autocomplete "
"your description with this qualification template."
msgstr ""
"<strong><font class=\"text-o-color-2\">ASTUCE PRO</font></strong> : À partir"
" d'une piste, utilisez le bouton \"livre\" dans le chatter pour trouver cet "
"article et compléter automatiquement votre description avec ce modèle de "
"qualification."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong><font class=\"text-o-color-2\">PRO TIP</font></strong>: From a lead,"
" use the book button in the chatter to find this article and autocomplete "
"your email with this template."
msgstr ""
"<strong><font class=\"text-o-color-2\">CONSEIL PRO</font></strong> : À "
"partir d'une piste, utilisez le bouton livre dans le chatter pour trouver "
"cet article et complétez automatiquement votre e-mail avec ce modèle."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "<strong><font style=\"color: rgb(231, 99, 99);\">Do Not</font></strong>"
msgstr "<strong><font style=\"color: rgb(231, 99, 99);\">À éviter</font></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<strong><span style=\"font-size: 18px;\">Change</span></strong>"
msgstr "<strong><span style=\"font-size: 18px;\">Changement</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "<strong><span style=\"font-size: 18px;\">Complexity</span></strong>"
msgstr "<strong><span style=\"font-size: 18px;\">Complexité</span></strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<strong>Actions Taken:</strong>"
msgstr "<strong>Actions prises :</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Company</strong>"
msgstr "<strong>Société</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Contact</strong>"
msgstr "<strong>Personne de contact</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "<strong>Do not recreate the Logo from scratch, use these ones</strong>"
msgstr ""
"<strong>Ne pas recréer le Logo à partir de zéro. Utiliser ces logos "
"fournis</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>Extra Notes:</strong>"
msgstr "<strong>Notes supplémentaires :</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<strong>Fast Facts</strong>"
msgstr "<strong>Faits rapides</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "<strong>Lessons Learnt:</strong>"
msgstr "<strong>Leçons apprises :</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>M</strong>onthly\n"
"                            <strong>R</strong>ecurring\n"
"                            <strong>R</strong>evenues\n"
"                            (<em>subscriptions, ...</em>)"
msgstr ""
"<strong>M</strong>onthly\n"
"                            <strong>R</strong>ecurring\n"
"                            <strong>R</strong>evenues\n"
"                            (Revenu mensuel récurrent) (<em>abonnements, ...</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>MRR</strong>"
msgstr "<strong>MRR</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "<strong>Main Point of Contact:</strong>"
msgstr "<strong>Principal point de contact :</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>N</strong>on-<strong>R</strong>ecurring <strong>R</strong>evenues\n"
"                            (<em>consultancy services, ...</em>)"
msgstr ""
"<strong>N</strong>on-<strong>R</strong>ecurring <strong>R</strong>evenues\n"
"                            (Revenu non récurrent <em>services de consultance, ...</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>NRR</strong>"
msgstr "<strong>NRR</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>Optional Tasks</strong>"
msgstr "<strong>Tâches optionnelles</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>Q1,2,3,4</strong>"
msgstr "<strong>Q1,2,3,4</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid ""
"<strong>Summary</strong>: <font class=\"text-600\">What an exciting release!"
" This time the focus was on...</font>"
msgstr ""
"<strong>Résumé</strong> : <font class=\"text-600\">Quelle version "
"passionnante ! Cette fois-ci, l'accent a été mis sur...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"<strong>U</strong>nique <strong>S</strong>elling <strong>P</strong>roposition:\n"
"                            Advantage that makes you stand out from the competition."
msgstr ""
"<strong>U</strong>nique <strong>S</strong>elling <strong>P</strong>roposition (Proposition unique de vente) :\n"
"                            Avantage qui vous permet de vous démarquer de la concurrence."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "<strong>USP</strong>"
msgstr "<strong>USP</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "<strong>⚡ TOP 3 PRIORITIES</strong>"
msgstr "<strong>⚡ TOP 3 DES PRIORITES</strong>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Demographics</u>"
msgstr "<u>Démographie</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Key Decision Factors</u>"
msgstr "<u>Facteurs décisionnels essentiels</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "<u>Strategy</u>"
msgstr "<u>Strategie</u>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "A day or less"
msgstr "Un jour ou moins"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"A must listen for all developers out there and anyone interested in "
"Javascript!"
msgstr ""
"À écouter absolument pour tous les développeurs et tous ceux qui "
"s'intéressent au Javascript !"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Abigail"
msgstr "Abigail"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"About YourCompany: YourCompany is a team of passionate people whose goal is to improve everyone's life\n"
"                        through disruptive products.\n"
"                        We build great products to solve your business problems.\n"
"                        <br>\n"
"                        Our products are designed for small to medium size companies willing to optimize their performance."
msgstr ""
"À propos de VotreSociété : VotreSociété est une équipe de passionnés dont l'objectif est d'améliorer la vie de chacun\n"
"                        grâce à des produits disruptifs.\n"
"                        Nous construisons de grands produits pour résoudre les problèmes de votre entreprise.\n"
"                        <br>\n"
"                        Nos produits sont conçus pour les petites et moyennes entreprises qui souhaitent optimiser leurs performances."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Absent for more than a day"
msgstr "Absence de plus d'un jour"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
msgid "Access Denied"
msgstr "Accès refusé"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Access Restricted. May not be shared with everyone from"
msgstr "Accès restreint. Ne peut pas être partagé avec tout le monde de"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_account_management
msgid "Account Management"
msgstr "Gestion des comptes"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Account Management Cheat Sheet"
msgstr "Aide-mémoire pour les gestion des comptes"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_needaction
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_needaction
msgid "Action Needed"
msgstr "Nécessite une action"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Action Plan"
msgstr "Plan d'action"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__active
msgid "Active"
msgstr "Actif"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_ids
msgid "Activities"
msgstr "Activités"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Activité exception décoration"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_state
msgid "Activity State"
msgstr "Statut de l'activité"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icône de type d'activité"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Cover"
msgstr "Ajouter une couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Icon"
msgstr "Ajouter une icône"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Add Properties"
msgstr "Ajouter des propriétés"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid "Add Property Fields"
msgstr "Ajouter des champs de propriété"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
msgid "Add a Comment..."
msgstr "Ajouter un commentaire..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_clipboard_plugin/embedded_clipboard_plugin.js:0
msgid "Add a clipboard section"
msgstr "Ajouter une section de presse-papiers"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Add a comment to an image"
msgstr "Ajouter un commentaire à une image"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Add a comment to selection"
msgstr "Ajouter un commentaire à la sélection"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_popover/comments_popover.xml:0
msgid "Add a comment..."
msgstr "Ajouter un commentaire..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_icon/knowledge_icon.xml:0
msgid "Add a random icon"
msgstr "Ajouter une icône au hasard"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_stage_action
msgid ""
"Add an embed kanban view of article items in the body of an article by using"
" '/kanban' command."
msgstr ""
"Ajouter une vue kanban intégrée d'éléments d'articles dans le corps d'un "
"article en utilisant la commande '/kanban'."

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_favorite_action
msgid ""
"Add articles in your list of favorites by clicking on the <i class=\"fa fa-"
"star-o\"></i> next to the article name."
msgstr ""
"Ajoutez des articles dans votre liste de favoris en cliquant sur <i "
"class=\"fa fa-star-o\"></i> à côté du nom de l'article."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Add people or email addresses"
msgstr "Ajouter des personnes ou des adresses e-mail"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Add people or email addresses..."
msgstr "Ajouter des personnes ou des adresses e-mail..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
msgid "Add to favorites"
msgstr "Ajouter aux favoris"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_member_action
msgid ""
"Adding members allows you to share Articles while granting specific access "
"rights<br>(can write, can read, ...)."
msgstr ""
"L'ajout de membres vous permet de partager des articles tout en accordant "
"des droits d'accès spécifiques<br> (peut écrire, peut lire, ...)."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Admin"
msgstr "Admin"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Administrator"
msgstr "Administrateur"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Advanced Search"
msgstr "Recherche avancée"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Advertising that would appear online or on TV"
msgstr "Publicité en ligne ou à la télévision"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"Services après-vente <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Services après-vente <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"After-Sales Service <span class=\"o_stars o_five_stars\" id=\"checkId-5\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Services après-vente <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"After-Sales Service​ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Services après-vente <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"After-Sales Service​ <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Services après-vente <span class=\"o_stars o_five_stars\" "
"id=\"checkId-5\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Age:"
msgstr "Âge :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Age: 24"
msgstr "Âge : 24 "

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Age: 29"
msgstr "Âge : 29"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Age: 42"
msgstr "Âge : 42"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "All Discussions"
msgstr "Toutes les discussions"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "All Models 📖"
msgstr "Tous les modèles 📖"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/form_status_indicator/form_status_indicator.xml:0
msgid "All changes saved"
msgstr "Tous les changements sont enregistrés"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "All my activities will always be encoded in our CRM"
msgstr "Toutes mes activités seront toujours encodées dans notre CRM"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Always include sufficient clear space around the logo"
msgstr "Le logo doit toujours être entouré de suffisamment d'espace libre"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Always type <strong>YourCompany</strong> in the same font size and style as "
"the content of the text"
msgstr ""
"Toujours écrire <strong>VotreSociété</strong> dans la même taille et le même"
" style de police que le contenu du texte"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "An hour or two (medical appointment, ...)"
msgstr "Une heure ou deux (rendez-vous médical, ...)"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_post_mortem
msgid ""
"Analyze what went wrong and the underlying causes. Extract insights to avoid"
" making similar mistakes in the future."
msgstr ""
"Analysez ce qui n'a pas fonctionné mal passé et les causes sous-jacentes. "
"Tirez des enseignements pour éviter de commettre des erreurs similaires à "
"l'avenir."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__article_anchor_text
msgid "Anchor Text"
msgstr "Texte d'ancrage"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_hr_faq
msgid "Answer questions frequently asked by your employees"
msgstr "Répondez aux questions fréquemment posées par vos employés"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Archived"
msgstr "Archivé"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.js:0
msgid ""
"Are you sure you want to delete this cover? It will be removed from every "
"article it is used in."
msgstr ""
"Êtes-vous sûr de vouloir supprimer cette couverture ? Elle sera retirée de "
"chaque article dans lequel elle est utilisée."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to leave your private Article? As you are its last "
"member, it will be moved to the Trash."
msgstr ""
"Êtes-vous sûr de vouloir quitter votre article privé ? Comme vous êtes le "
"dernier membre, il sera déplacé vers la corbeille."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to private? Only you "
"will be able to access it."
msgstr ""
"Êtes-vous sûr de vouloir faire passer \"%(icon)s%(title)s\" en privé ? Seul "
"vous pourrez y accéder."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to the Shared section? "
"It will be shared with all listed members."
msgstr ""
"Êtes-vous sûr de vouloir déplacer \"%(icon)s%(title)s\" vers la section "
"Partagé ? Il sera partagé avec tous les membres répertoriés."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" to the Workspace? It "
"will be shared with all internal users."
msgstr ""
"Êtes-vous sûr de vouloir déplacer \"%(icon)s%(title)s\" vers l'espace de "
"travail ? Il sera partagé avec tous les utilisateurs internes."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Are you sure you want to move \"%(icon)s%(title)s\" under "
"\"%(parentIcon)s%(parentTitle)s\"? It will be shared with the same persons."
msgstr ""
"Êtes-vous sûr de vouloir déplacer \"%(icon)s%(title)s\" sous "
"\"%(parentIcon)s%(parentTitle)s\" ? Il sera partagé avec les mêmes "
"personnes."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to remove your member? By leaving an article, you may "
"lose access to it."
msgstr ""
"Êtes-vous sûr de vouloir supprimer votre membre ? En quittant un article, "
"vous risquez de perdre l'accès à celui-ci."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Are you sure you want to remove your own \"Write\" access?"
msgstr "Êtes-vous sûr de vouloir supprimer votre propre accès \"Écriture\" ?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to restore access? This means this article will now "
"inherit any access set on its parent articles."
msgstr ""
"Êtes-vous sûr de vouloir restaurer l'accès ? Cela signifie que cet article "
"héritera désormais de tout accès défini sur ses articles parents."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to restrict access to this article? This means it will"
" no longer inherit access rights from its parents."
msgstr ""
"Êtes-vous sûr de vouloir restreindre l'accès à cet article ? Ceci signifie "
"que cet article n'héritera plus des droit d'accès de ses parents."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Are you sure you want to send this article to the trash?"
msgstr "Êtes-vous sûr de vouloir supprimer cet article ?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to set the internal permission to \"none\"? If you do,"
" you will no longer have access to the article."
msgstr ""
"Êtes-vous sûr de vouloir définir l'autorisation interne sur \"aucune\" ? Si "
"vous le faites, vous n'aurez plus accès à l'article."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid ""
"Are you sure you want to set your permission to \"none\"? If you do, you "
"will no longer have access to the article."
msgstr ""
"Êtes-vous sûr de vouloir définir votre autorisation sur \"Aucune\" ? Si vous"
" le faites, vous n'aurez plus accès à l'article."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__article_id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__article_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
msgid "Article"
msgstr "Article"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_member.py:0
msgid ""
"Article '%s' should always have a writer: inherit write permission, or have "
"a member with write access"
msgstr ""
"L'article '%s' doit toujours avoir un rédacteur : hériter de l'autorisation "
"d'écriture ou avoir un membre avec un accès en écriture"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_thread
msgid "Article Discussion Thread"
msgstr "Fil de discussion de l'article"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_properties_definition
msgid "Article Item Properties"
msgstr "Propriétés de l'élément d'article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_item_calendar
#: model:ir.actions.act_window,name:knowledge.knowledge_article_item_action
#: model:ir.actions.act_window,name:knowledge.knowledge_article_item_action_stages
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Article Items"
msgstr "Éléments d'article"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_member
msgid "Article Member"
msgstr "Membre de l'article"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_template_category
msgid "Article Template Category"
msgstr "Catégorie de modèle d'article"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_action
msgid "Article Templates"
msgstr "Modèles d'article"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_url
msgid "Article URL"
msgstr "URL de l'article"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_id
msgid "Article cover"
msgstr "Couverture d'article"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action_item_calendar
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action_stages
msgid ""
"Article items are articles that exist inside their parents but are not displayed in the menu.\n"
"                They can be used to handle lists (Buildings, Tasks, ...)."
msgstr ""
"Les éléments d'article sont des articles qui existent à l'intérieur de leurs parents, mais qui ne sont pas affichés dans le menu. \n"
"Ils peuvent être utilisés pour gérer des listes (bâtiments, taĉhes,...)."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid ""
"Article items are not showed in the left-side menu\n"
"but are shown in inserted kanban/list views"
msgstr ""
"Les éléments d'article ne sont pas affichés dans le menu de gauche,\n"
"mais sont affichés dans des vues kanban/liste intégrées."

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_article_item_parent
msgid "Article items must have a parent."
msgstr "Les éléments d'article doivent avoir un parent."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Article shared with you: %s"
msgstr "Article partagé avec vous : %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_form
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_form_show_resolved
#: model:ir.actions.server,name:knowledge.ir_actions_server_knowledge_home_page
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Articles"
msgstr "Articles"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Articles %s cannot be updated as this would create a recursive hierarchy."
msgstr ""
"Les articles %s ne peuvent pas être mis à jour car cela créerait une "
"hiérarchie récursive."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__article_ids
msgid "Articles using cover"
msgstr "Articles utilisant la couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "As a reader, you can't leave a Workspace article"
msgstr ""
"En tant que lecteur, vous ne pouvez pas modifier vos accès pour quitter un "
"article de l'espace de travail"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid ""
"As an administrator, you can always modify this article and its members."
msgstr ""
"En tant qu'administrateur, vous pouvez toujours modifier cet article et ses "
"membres."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Attach"
msgstr "Joindre"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_attachment_count
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_attachment_count
msgid "Attachment Count"
msgstr "Nombre de pièces jointes"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Available Models ✅"
msgstr "Modèles disponibles ✅"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "BOOK NOW"
msgstr "RESERVER MAINTENANT"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Background:"
msgstr "Contexte :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Backlog"
msgstr "Tâche en retard"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Base Color On"
msgstr "Baser la couleur sur"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Based on"
msgstr "Basé sur"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Be assertive but listen to what is being said"
msgstr "Soyez fermes, mais écoutez ce que l'on vous dit"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action
msgid "Be the first one to unleash the power of Knowledge!"
msgstr "Soyez le premier à libérer le pouvoir de Connaissances !"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__body
msgid "Body"
msgstr "Corps"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Booklets for a total price &gt; $1000"
msgstr "Brochures pour un prix total supérieur à $1000"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_brand_assets
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Brand Assets"
msgstr "Brand Assets"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Attraction de la marque <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Attraction de la marque <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Attraction de la marque <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Brand Attraction <span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Attraction de la marque <span class=\"o_stars o_five_stars\" "
"id=\"checkId-3\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Brand Name Rules"
msgstr "Règles relatives au nom de marque"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Browse Templates"
msgstr "Parcourir les modèles"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Bug Fixes 🔨"
msgstr "Corrections de bugs 🔨"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item Calendar"
msgstr "Créer un élément Calendrier"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item Kanban"
msgstr "Créer un élément Kanban"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Build an Item List"
msgstr "Créer un élément Liste"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Build fictional representation of your customers to better tailor your "
"advertising messages for them. "
msgstr ""
"Construisez une représentation fictive de vos clients pour mieux adapter vos"
" messages publicitaires."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "But Also..."
msgstr "Mais aussi..."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Buying Process"
msgstr "Processus d'achat"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Calendar of %s"
msgstr "Calendrier de %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Calendar of Article Items"
msgstr "Calendrier d'éléments d'article"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_can_write
msgid "Can Edit"
msgstr "Peut modifier"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_can_read
msgid "Can Read"
msgstr "Peut lire"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__write
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__write
msgid "Can edit"
msgstr "Peut modifier"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_visible_by_everyone
msgid "Can everyone see the Article?"
msgstr "Tout le monde peut voir l'article ?"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_member.py:0
msgid "Can not update the article or partner of a member."
msgstr "Impossible de mettre à jour l'article ou le partenaire d'un membre."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_favorite.py:0
msgid "Can not update the article or user of a favorite."
msgstr "Impossible de mettre à jour l'article ou l'utilisateur d'un favori."

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__read
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__read
msgid "Can read"
msgstr "Peut lire"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_access_parent_path
msgid "Can the user join?"
msgstr "L'utilisateur peut-il rejoindre ?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_visible
msgid "Can the user see the article?"
msgstr "L'utilisateur peut-il voir l'article ?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.xml:0
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Cancel"
msgstr "Annuler"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Cannot create an article under article %(parent_name)s which is a non-"
"private parent"
msgstr ""
"Impossible de créer un article sous l'article %(parent_name)s, qui est un "
"parent non privé"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Capitalize the word <strong>YourCompany</strong>, except if it's part of an "
"URL e.g. website/company"
msgstr ""
"Mettre une majuscule au nom <strong>VotreSociété</strong>, sauf s'il fait "
"partie d'une URL, par exemple website/company"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Car Policy"
msgstr "Politique automobile"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Categories"
msgstr "Catégories"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__sequence
msgid "Category Sequence"
msgstr "Séquence de la catégorie"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Causes"
msgstr "Causes"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_account_management
msgid ""
"Centralize account insights in one document for comprehensive monitoring and"
" follow-up."
msgstr ""
"Centralisez les informations sur les comptes dans un seul document pour un "
"contrôle et un suivi complets."

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_meeting_minutes
msgid ""
"Centralize team meetings in a single article, while making sure notes are "
"handled efficiently."
msgstr ""
"Centralisez les réunions d'équipe en un seul article, tout en vous assurant "
"que les minutes sont traitées efficacement."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Challenges &amp; Competitive Landscape<br>"
msgstr "Défis &amp; Paysage compétitif<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 1"
msgstr "Changement 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 2"
msgstr "Changement 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Change 3"
msgstr "Changement 3"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Change Permission"
msgstr "Changer l'autorisation"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Change cover"
msgstr "Changer la couverture"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__child_ids
msgid "Child Articles and Items"
msgstr "Articles et éléments enfants"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.xml:0
msgid "Choose a nice cover"
msgstr "Choisissez une jolie couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.js:0
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Choose an Article..."
msgstr "Choisissez un Article..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Click and hold to reposition"
msgstr "Cliquer et maintenir pour repositionner"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_clipboard_plugin/embedded_clipboard_plugin.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Clipboard"
msgstr "Presse-papiers"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "Close"
msgstr "Fermer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/comments_plugin/comments_plugin.js:0
msgid "Comment"
msgstr "Commenter"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Company Abbreviations"
msgstr "Abréviations commerciales"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Details"
msgstr "Détails de la société"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Location:"
msgstr "Lieu de la société :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Name:"
msgstr "Nom de la société :"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_company_newsletter
msgid "Company Newsletter"
msgstr "Newsletter de la société"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_company_organization
msgid "Company Organization"
msgstr "Organisation de la société"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Company Structure:"
msgstr "Structure de la société :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Compiled below are tips &amp; tricks collected among our veterans to help "
"newcomers get started. We hope it will help you sign deals."
msgstr ""
"Vous trouverez ci-dessous des conseils et des astuces recueillis par nos "
"vétérans pour aider les nouveaux à démarrer. Nous espérons qu'ils vous "
"aideront à conclure des ventes."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Complicated buying process"
msgstr "Processus d'achat compliqué"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_menu_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Confirmation"
msgstr "Confirmation"

#. module: knowledge
#: model:ir.model,name:knowledge.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Contact Us"
msgstr "Contactez-nous"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_contact_our_lawyer
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_contact_our_lawyer
msgid "Contact our Lawyer"
msgstr "Contacter notre avocat"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.js:0
msgid "Content copied to clipboard."
msgstr "Contenu copie dans le presse-papiers."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Contract Due Date:"
msgstr "Date d'échéance du contrat :"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Convert into Article"
msgstr "Convertir en article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Convert into Article Item"
msgstr "Convertir en élément d'article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
msgid "Copy"
msgstr "Copier"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Copy Link"
msgstr "Copier le lien"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/clipboard/embedded_clipboard.xml:0
msgid "Copy to Clipboard"
msgstr "Copier dans le presse-papiers"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Costing too much money"
msgstr "Coût trop élevé"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid ""
"Could not move \"%(icon)s%(title)s\" under "
"\"%(parentIcon)s%(parentTitle)s\", because you do not have write permission "
"on the latter."
msgstr ""
"Impossible de déplacer \"%(icon)s%(title)s\" sous "
"\"%(parentIcon)s%(parentTitle)s\", parce que vous n'avez pas l'autorisation "
"d'écrire sur ce dernier."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Could you please let me know on which number I could reach you so that we could get in touch?<br>\n"
"                        It should not take longer than 15 minutes."
msgstr ""
"Pourriez-vous m'indiquer le numéro de téléphone auquel je peux vous joindre ?<br>\n"
"Cela ne devrait pas prendre plus de 15 minutes."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__attachment_url
msgid "Cover URL"
msgstr "URL de couverture"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__attachment_id
msgid "Cover attachment"
msgstr "Pièce jointe de couverture"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_url
msgid "Cover url"
msgstr "URL de couverture"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__cover_image_position
msgid "Cover vertical offset"
msgstr "Décalage vertical de la couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "Create \""
msgstr "Créer ''"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_selection_dialog/article_selection_dialog.js:0
msgid "Create \"%s\""
msgstr "Créer \"%s\""

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Create a Copy"
msgstr "Créer une copie"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
msgid "Create a nested article"
msgstr "Créer un sous-article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Create a new article in workspace"
msgstr "Créer un nouvel article dans l'espace de travail"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Create a new private article"
msgstr "Créer un nouvel article privé"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_product_catalog
msgid ""
"Create a simple catalog to provide technical details about your products."
msgstr ""
"Créez un catalogue simple pour fournir des détails techniques à propos de "
"vos produits."

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action_item_calendar
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_item_action_stages
msgid "Create an Article Item"
msgstr "Créer un élément d'article"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_action
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Create an article"
msgstr "Créer un article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Created"
msgstr "Créé"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__create_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__create_uid
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Created by"
msgstr "Créé par"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__create_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__create_date
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Created on"
msgstr "Créé le"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Current Contract:"
msgstr "Contrat en cours :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Current Satisfaction:"
msgstr "Satisfaction actuelle :"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_personas
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Customer Personas"
msgstr "Personnages clients"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "DESIGN PROTOTYPE"
msgstr "PROTOTYPE DE CONCEPTION"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Date Properties"
msgstr "Propriétés de date"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "Date and Time Properties"
msgstr "Propriétés de date et d'heure"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "Dear"
msgstr "Cher(e)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 1"
msgstr "Décision 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 2"
msgstr "Décision 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 3"
msgstr "Décision 3"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Decision 4"
msgstr "Décision 4"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Default Access Rights"
msgstr "Droits d'accès par défaut"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Default Scale"
msgstr "Échelle par défaut"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__internal_permission
msgid ""
"Default permission for all internal users. (External users can still have "
"access to this article if they are added to its members)"
msgstr ""
"Autorisation par défaut pour tous les utilisateurs internes. (Les "
"utilisateurs externes peuvent toujours avoir accès à cet article s'ils sont "
"ajoutés à ses membres)"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Deleted articles are stored in Trash an extra <b>%(threshold)s</b> days\n"
"                 before being permanently removed for your database"
msgstr ""
"Les articles supprimés sont sauvegardés dans la corbeille pendant <b>%(threshold)s</b> jours supplémentaires\n"
"                 avant d'être définitivement supprimés de votre base de données."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__deletion_date
msgid "Deletion Date"
msgstr "Date de suppression"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_permission_on_desync
msgid "Desynchronized articles must have internal permission."
msgstr "Les articles désynchronisés doivent avoir une autorisation interne."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_desynchronized
msgid "Desyncronized with parents"
msgstr "Désynchronisé avec ses parents"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_0
msgid ""
"Did you know that access rights can be defined per user on any Knowledge "
"Article?"
msgstr ""
"Saviez-vous que les droits d'accès peuvent être définis par utilisateur sur "
"n'importe quel article de Connaissances ?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover_dialog.xml:0
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Discard"
msgstr "Ignorer"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__display_name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_brand_assets
msgid ""
"Distribute brand digital assets while ensuring compliance with company "
"policies and guidelines."
msgstr ""
"Distribuez la version numérique des brand assets  tout en veillant au "
"respect des politiques et lignes directrices de la société."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Diving in"
msgstr "Se lancer"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_marketing_campaign_brief
msgid ""
"Document your Marketing Campaigns to prioritize key objectives and outcomes."
msgstr ""
"Documentez vos campagnes de marketing pour prioriser les principaux "
"objectifs et résultats."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Don't forget to spread the word, we're <em>so</em> looking forward to "
"unveiling this new Odoo version! 🥳"
msgstr ""
"N'oubliez pas de faire passer le mot, nous avons <em>tellement</em> hâte de "
"dévoiler cette nouvelle version d'Odoo ! 🥳"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_done
msgid "Done"
msgstr "Terminé"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Drop here to delete this article"
msgstr "Glissez cet article ici pour le supprimer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/list_view.js:0
msgid "Duplicate"
msgstr "Dupliquer"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "EDIT"
msgstr "MODIFIER"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/embedded_view_actions_menu/embedded_view_actions_menu.xml:0
msgid "Edit"
msgstr "Modifier"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Edit Link"
msgstr "Modifier le lien"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Education:"
msgstr "Éducation :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Education: Bachelor's degree in Marketing"
msgstr "Éducation : Diplôme de bachelier en marketing"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Education: High school diploma"
msgstr "Éducation : Diplôme de l'enseignement secondaire"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Education: PhD."
msgstr "Éducation : Doctorat"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Education: Student"
msgstr "Éducation : Étudiant"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Egg'cellent run"
msgstr "Egg'cellent run"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Email:"
msgstr "E-mail :"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Embed a View"
msgstr "Intégrer une vue"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__icon
msgid "Emoji"
msgstr "Émoji"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "End Date Time"
msgstr "Date de fin"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_software_specification
msgid ""
"Ensure all stakeholders of a product change are aligned by clearly "
"communicating the requirements."
msgstr ""
"Assurez-vous que toutes les parties prenantes d'un changement de produit "
"sont alignées en communiquant clairement les exigences."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "Error"
msgstr "Erreur"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Estimated Revenues:"
msgstr "Chiffre d'affaires estimé :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Events 🌍"
msgstr "Événements 🌍"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Everyone"
msgstr "Tout le monde"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Expense Policy"
msgstr "Politique des notes de frais"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Export"
msgstr "Exporter"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Extra Comment"
msgstr "Commentaire supplémentaire"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Extra Technical Instructions:"
msgstr "Instructions techniques supplémentaires :"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Favorite"
msgstr "Favori"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_favorite
msgid "Favorite Article"
msgstr "Article favori"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__favorite_ids
msgid "Favorite Articles"
msgstr "Articles favoris"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_favorite_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_favorite_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_tree
msgid "Favorites"
msgstr "Favoris"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.portal_my_home_knowledge
msgid "Find all articles shared with you"
msgstr "Trouvez tous les articles partagés avec vous"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__fold
msgid "Folded in kanban view"
msgstr "Repliée en vue kanban"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_follower_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_follower_ids
msgid "Followers"
msgstr "Abonnés"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_partner_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_partner_ids
msgid "Followers (Partners)"
msgstr "Abonnés (Partenaires)"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icône Font Awesome par ex. fa-tasks"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"For all other categories, we simply require you to follow the rules listed "
"below."
msgstr ""
"Pour toutes les autres catégories, nous vous demandons simplement de "
"respecter les règles énumérées ci-dessous."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Full Width"
msgstr "Pleine largeur"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__full_width
msgid "Full width"
msgstr "Pleine largeur"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Gender(s):"
msgstr "Genre :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Gender(s): M"
msgstr "Genre : M"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Gender(s): W"
msgstr "Genre : F"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Generate an Article with AI"
msgstr "Générez un article avec l'IA"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Group By"
msgstr "Regrouper par"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Guest"
msgstr "Invité"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_hr_faq
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "HR FAQ"
msgstr "FAQ RH"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_access
msgid "Has Access"
msgstr "A accès"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_message
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__has_message
msgid "Has Message"
msgstr "A un message"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_has_write_access
msgid "Has Write Access"
msgstr "A l'accès écriture"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_item_children
msgid "Has article item children?"
msgstr "L'article a-t-il des éléments enfants ?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_article_children
msgid "Has normal article children?"
msgstr "L'article normal a-t-il des enfants ?"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__user_has_access_parent_path
msgid ""
"Has the user access to each parent from current article until its root?"
msgstr ""
"L'utilisateur a-t-il accès à chaque parent de l'article actuel jusqu'à sa "
"racine ?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__have_share_partners
msgid "Have Share Partners"
msgstr "Ont des partenaires de partage"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Hello there"
msgstr "Bonjour "

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid ""
"Hello there, I am a template 👋\n"
"            <br/>\n"
"            Use the buttons at the top-right of this box to re-use my content.\n"
"            <br/>\n"
"            No more time wasted! 🔥"
msgstr ""
"Bonjour, je suis un modèle 👋\n"
"            <br/>\n"
"            Utilisez les boutons en haut à droite de cette zone pour réutiliser mon contenu.\n"
"            <br/>\n"
"            Ne perdez plus de temps ! 🔥"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Here are logos that you can use at your own convenience.\n"
"                <br>\n"
"                They can also be shared with customers, journalists and resellers."
msgstr ""
"Voici des logos que vous pouvez utiliser à votre convenance.\n"
"                <br>\n"
"                Vous pouvez également les partager avec les clients, les journalistes et les revendeurs."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"Here is a short guide that will help you pick the right tiny house for you."
msgstr ""
"Voici un petit guide qui vous aidera à choisir la tiny house parfaite pour "
"vous."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Hey ProspectName,"
msgstr "Bonjour NomProspect,"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "Hidden"
msgstr "Masqué"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Highlight content and use the"
msgstr "Surlignez du contenu et utilisez le"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__html_field_history
msgid "History data"
msgstr "Données historiques"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__html_field_history_metadata
msgid "History metadata"
msgstr "Métadonnées historiques"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: model:ir.ui.menu,name:knowledge.knowledge_menu_home
msgid "Home"
msgstr "Page d'accueil"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Hours Display"
msgstr "Affichage des heures"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_cielo
msgid "House Model \"Cielo\""
msgstr "Modèle de maison \"Cielo\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_dolcezza
msgid "House Model \"Dolcezza\""
msgstr "Modèle de maison \"Dolcezza\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_incanto
msgid "House Model \"Incanto\""
msgstr "Modèle de maison \"Incanto\""

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog_house_serenita
msgid "House Model \"Serenità\""
msgstr "Modèle de maison \"Serenità\""

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_cielo
msgid "House Model - Cielo"
msgstr "Modèle de maison - Cielo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_dolcezza
msgid "House Model - Dolcezza"
msgstr "Modèle de maison - Dolcezza"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_incanto
msgid "House Model - Incanto"
msgstr "Modèle de maison - Incanto"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog_house_serenita
msgid "House Model - Serenità"
msgstr "Modèle de maison - Serenità"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "How do I know which model I can order or not?"
msgstr "Comment puis-je savoir quel modèle je peux commander ou non ?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "How they measure success:"
msgstr "Comment mesurent-ils le succès :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "How to find the perfect model for your needs 😍"
msgstr "Comment trouver le modèle parfait et adapté à vos besoins 😍"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"I was doing some research online and found your company.<br>\n"
"                        Considering we just launched ProductName, I was thinking you would be interested."
msgstr ""
"Je faisais des recherches en ligne et je suis tombé sur votre société.<br>\n"
"Comme nous venons de lancer NomProduit, j'ai pensé que vous seriez intéressé."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will not steal prospects from colleagues"
msgstr "Je ne volerai pas de prospects à mes collègues"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will not waste time and energy bad-mouthing competitors"
msgstr "Je ne perdrai pas mon temps et mon énergie à dénigrer mes concurrents"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "I will only sell a project if I am convinced it can be a success"
msgstr ""
"Je ne vendrai un projet que si je suis convaincu qu'il peut être couronné de"
" succès"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__id
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__id
msgid "ID"
msgstr "ID"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_exception_icon
msgid "Icon"
msgstr "Icône"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icône pour indiquer une activité d'exception."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Identify the pain points and offer clear solutions"
msgstr "Identifiez les points de douleur et proposez des solutions claires"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_needaction
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Si coché, de nouveaux messages demandent votre attention."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_sms_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_error
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si coché, certains messages ont une erreur de livraison."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"If none of those offers convinced you, just get in touch with our "
"team.<br>At MyCompany, your happiness is our utmost priority, and we'll go "
"the extra mile to make sure you find what you're looking for!"
msgstr ""
"Si aucune de ces offres ne vous a convaincu, il vous suffit de contacter "
"notre équipe.<br>Chez MyCompany, votre bonheur est notre priorité absolue et"
" nous ferons tout pour que vous trouviez ce que vous cherchez !"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__is_desynchronized
msgid ""
"If set, this article won't inherit access rules from its parents anymore."
msgstr ""
"Si défini, cet article n'héritera plus des règles d'accès de ses parents."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Impact"
msgstr "Impact"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Improvements 🔬"
msgstr "Améliorations 🔬"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Income:"
msgstr "Revenus :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Income: $109,160"
msgstr "Revenus : 109.160 $"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Income: $142,170"
msgstr "Revenus : 142.170 $"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Income: $293,650"
msgstr "Revenus : 293.650 $"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Income: $68,170"
msgstr "Revenus : 68.170 $"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Inconsistent customer experience"
msgstr "Expérience client incohérente"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
msgid "Index"
msgstr "Index"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Industrial ⚙-"
msgstr "Industriel ⚙-"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Industry:"
msgstr "Secteur :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Inform HR and your Team Leader."
msgstr "Informez les RH et votre chef d'équipe"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_release_note
msgid "Inform users about your latest software updates and improvements."
msgstr ""
"Informer les utilisateurs des dernières mises à jours et améliorations de "
"votre logiciel."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__inherited_permission
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__article_permission
msgid "Inherited Permission"
msgstr "Autorisation héritée"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__inherited_permission_parent_id
msgid "Inherited Permission Parent Article"
msgstr "Autorisation héritée de l'article parent"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "Insert"
msgstr "Insérer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Insert Link"
msgstr "Insérer un lien"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Insert a Calendar View"
msgstr "Insérer une vie calendrier"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Calendar view of article items"
msgstr "Insérer une vue calendrier des éléments d'article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Card view of article items"
msgstr "Insérer une vue Carte des éléments d'article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Insert a Kanban View"
msgstr "Insérer une vue Kanban"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a Kanban view of article items"
msgstr "Insérer une vue Kanban des éléments"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "Insert a List View"
msgstr "Insérer une vue liste"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Insert a List view of article items"
msgstr "Insérer une vue Liste des éléments"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Insert an Article shortcut"
msgstr "Insérer un raccourci pour un article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
msgid "Insert link in article"
msgstr "Insérer lien dans article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
msgid "Insert view in article"
msgstr "Insérer vue dans article"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Instead of setting up complicated processes, <strong>we prefer to let our "
"employees buy whatever they need</strong>. Just fill in an expense and we "
"will reimburse you."
msgstr ""
"Plutôt que de mettre en place des processus compliqués, <strong>nous "
"préférons laisser nos employés acheter ce dont ils ont besoin</strong>. Il "
"suffit de compléter une déclaration de note de frais et nous vous "
"rembourserons."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid "Interests:"
msgstr "Intérêts :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Interests: Cooking"
msgstr "Intérêts : Cuisiner"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Interests: Music"
msgstr "Intérêts : Musique"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Interests: Politics"
msgstr "Intérêts : Politique"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Interests: Science"
msgstr "Intérêts : Sciences"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__internal_permission
msgid "Internal Permission"
msgstr "Autorisation interne"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Invitation to access an article"
msgstr "Invitation à accéder à un article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_invite_view_form
msgid "Invite"
msgstr "Inviter"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Invite People"
msgstr "Inviter des gens"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_invite_action_from_article
msgid "Invite people"
msgstr "Inviter des gens"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__is_article_active
msgid "Is Article Active"
msgstr "Est un article actif"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_user_favorite
msgid "Is Favorited"
msgstr "Est favori"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_is_follower
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_is_follower
msgid "Is Follower"
msgstr "Est un abonné"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_article_item
msgid "Is Item?"
msgstr "Est un élément ?"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_template
msgid "Is Template"
msgstr "Est un modèle"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__has_item_parent
msgid "Is the parent an Item?"
msgstr "Le parent est-il un élément ?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Issue Summary"
msgstr "Récapitulatif du problème"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Issues they are trying to solve:"
msgstr "Problèmes qu'ils tentent de résoudre :"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__template_category_sequence
#: model:ir.model.fields,help:knowledge.field_knowledge_article_template_category__sequence
msgid "It determines the display order of the category"
msgstr "Cela détermine l'ordre d'affichage de la catégorie"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__template_sequence
msgid "It determines the display order of the template within its category"
msgstr "Cela détermine l'ordre d'affichage du modèle dans sa catégorie"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 1"
msgstr "Élément 1"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 2"
msgstr "Élément 2"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 3"
msgstr "Élément 3"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Item 4"
msgstr "Élément 4"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Calendar"
msgstr "Élément Calendrier"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Cards"
msgstr "Élément Cartes"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item Kanban"
msgstr "Elément Kanban"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_plugin/embedded_view_plugin.js:0
msgid "Item List"
msgstr "Élément Liste"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__stage_id
msgid "Item Stage"
msgstr "Étape élément"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_calendar_items
msgid "Items"
msgstr "Articles"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Job Position:"
msgstr "Fonction :"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Join"
msgstr "Rejoindre"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "Join a hidden article"
msgstr "Rejoindre un article masqué"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Julius"
msgstr "Julius"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Kanban of %s"
msgstr "Kanban de %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Kanban of Article Items"
msgstr "Kanban d'éléments d'article"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_shared_todos
msgid "Keep track of your company to-dos and share them with your colleagues."
msgstr ""
"Gardez une trace des to-dos de votre entreprise et partagez-les avec vos "
"collègues."

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Keep your colleagues informed about the company's latest developments and "
"activities through periodic updates."
msgstr ""
"Tenez vos collègues informés des derniers développements et activités de "
"l'entreprise grâce à des mises à jour périodiques."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/insert_embedded_view.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
#: model:ir.ui.menu,name:knowledge.knowledge_menu_root
#: model:ir.ui.menu,name:knowledge.knowledge_menu_technical
#: model_terms:ir.ui.view,arch_db:knowledge.portal_my_home_knowledge
msgid "Knowledge"
msgstr "Connaissances"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article
msgid "Knowledge Article"
msgstr "Article de Connaissances"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_cover
msgid "Knowledge Cover"
msgstr "Couverture Connaissances"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_invite
msgid "Knowledge Invite Wizard"
msgstr "Assistant d'invitation à Connaissances"

#. module: knowledge
#: model:ir.model,name:knowledge.model_knowledge_article_stage
msgid "Knowledge Stage"
msgstr "Connaissances Étape"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Last Edit Date"
msgstr "Date de la dernière modification"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Last Edited"
msgstr "Dernière modification"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__last_edition_uid
msgid "Last Edited by"
msgstr "Dernière modification par"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__last_edition_date
msgid "Last Edited on"
msgstr "Dernière modification le"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__write_uid
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_cover__write_date
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Leave"
msgstr "Quitter"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Leave Article"
msgstr "Quitter l'article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Leave Private Article"
msgstr "Quitter l'article privé"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Leaves &amp; Time Off"
msgstr "Absences & Congés"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Let your Team Leader know in advance."
msgstr "Notifiez votre chef d'équipe à l'avance."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Link"
msgstr "Lien"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/plugins/article_plugin/article_plugin.js:0
msgid "Link an Article"
msgstr "Lier un article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/embedded_view_link_plugin/embedded_view_link_plugin.js:0
msgid "Link copied to clipboard."
msgstr "Lien copié dans votre presse-papiers."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "List of %s"
msgstr "Liste de %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "List of Article Items"
msgstr "Liste d'éléments d'article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Load More Discussions"
msgstr "Charger plus de discussions"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "Load Template"
msgstr "Charger le modèle"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid "Load a Template"
msgstr "Charger un modèle"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Lock Content"
msgstr "Verrouiller le contenu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__is_locked
msgid "Locked"
msgstr "Verrouillé"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Logging changes from %(partner_name)s without write access on article "
"%(article_name)s due to hierarchy tree update"
msgstr ""
"Enregistrer les changements de %(partner_name)s sans accès d'écriture sur "
"l'article %(article_name)s en raison d'une mise à jour de la hiérarchie"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Logo"
msgstr "Logo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Logos should only be used in the colors provided"
msgstr "Les logos doivent uniquement être utilisés dans les couleurs fournies"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Lose Access"
msgstr "Perdre l'accès"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_personal_organizer
msgid ""
"Make every week a success by proactively organizing your priority and "
"optional tasks."
msgstr ""
"Faites de chaque semaine un succès en organisant de manière proactive vos "
"tâches prioritaires et optionnelles."

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_sprint_calendar
msgid "Manage your team schedule for the upcoming sprint."
msgstr "Gérez le planning de votre équipe pour le sprint à venir."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_thread.py:0
msgid "Mark Comment as Closed"
msgstr "Marquer le commentaire comme fermé"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_actions.js:0
msgid "Mark the discussion as resolved"
msgstr "Marquer la discussion comme résolue"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_marketing
msgid "Marketing"
msgstr "Marketing"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_campaign_brief
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "Marketing Campaign Brief"
msgstr "Brief de marketing"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar_meeting
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar_meeting
msgid "Meeting"
msgstr "Réunion"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_example_branding
msgid "Meeting Example"
msgstr "Exemple de réunion"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_minutes
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_meeting_minutes_template
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes
msgid "Meeting Minutes"
msgstr "Minutes de la réunion"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Meeting Minutes Template"
msgstr "Modèle de minutes de la réunion"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_form
msgid "Member"
msgstr "Membre"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model:ir.actions.act_window,name:knowledge.knowledge_article_member_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_member_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_tree
msgid "Members"
msgstr "Membres"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_member_ids
msgid "Members Information"
msgstr "Informations des membres"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__root_article_id
msgid "Menu Article"
msgstr "Article de menu"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__message
msgid "Message"
msgstr "Message"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_error
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_error
msgid "Message Delivery error"
msgstr "Erreur d'envoi du message"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_model_patch.js:0
msgid "Message Link Copied!"
msgstr "Lien du message copié !"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_model_patch.js:0
msgid "Message Link Copy Failed (Permission denied?)!"
msgstr "Échec de la copie du lien du message (Permission refusée ?)!"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_ids
msgid "Messages"
msgstr "Messages"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
msgid "Missing Calendar configuration."
msgstr "Configuration de calendrier manquante."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "More actions"
msgstr "Plus d'actions"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"More than 150 Odooers participated in this years' edition of the run of 5 or 11\n"
"                            kilometers.<br>Starting from the office, they enjoyed a great tour in the countryside before\n"
"                            coming back to Grand-Rosière, where they were welcomed with a drink and a burger."
msgstr ""
"Plus de 150 Odooers ont participé à cette édition de la course à pied de 5 ou 11\n"
"                            kilomètres.<br>Au départ du bureau, ils ont bénéficié d'un grand tour dans la campagne avant de\n"
"                            revenir à Grand-Rosière, où ils ont été accueillis avec une boisson et un burger."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
msgid "Move \"%s\" under:"
msgstr "Déplacer \"%s\" vers :"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move Article"
msgstr "Déplacer l'article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Move To"
msgstr "Déplacer vers"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move an Article"
msgstr "Déplacer un article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move cancelled"
msgstr "Déplacement annulé"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.xml:0
msgid "Move the untitled article under:"
msgstr "Déplacer l'article sans titre sous :"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Private"
msgstr "Déplacer vers privé"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Shared"
msgstr "Déplacer vers Partagé"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Move to Trash"
msgstr "Déplacer vers la corbeille"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Move to Workspace"
msgstr "Déplacer vers l'espace de travail"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Échéance de mon activité"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "My Favorites"
msgstr "Mes favoris"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "My Forecast will always be accurate and up-to-date"
msgstr "Mes provisions sont toujours exactes et à jour"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "My Items"
msgstr "Mes éléments"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form_item_quick_create
msgid "My New Item"
msgstr "Mon nouvel élément"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__name
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Name"
msgstr "Nom"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Natural Style ☘ -"
msgstr "Style naturel ☘ -"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Navigation Basics 🐣"
msgstr "Les bases de la navigation 🐣 "

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_new
msgid "New"
msgstr "Nouveau"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "New Features 🎉"
msgstr "Nouvelles fonctionnalités 🎉"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article_thread.py:0
msgid "New Mention in %s"
msgstr "Nouvelle mention dans %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "New property could not be created."
msgstr "Impossible de créer la nouvelle propriété."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Activité suivante de l'événement du calendrier"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Date limite de l'activité à venir"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_summary
msgid "Next Activity Summary"
msgstr "Résumé de l'activité suivante"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_type_id
msgid "Next Activity Type"
msgstr "Type d'activités à venir"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "Next Meeting: <font class=\"text-400\">@Date</font>"
msgstr "Prochaine réunion : <font class=\"text-400\">@Date</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Next Meeting: <u>6th May, @John's Office</u>"
msgstr "Prochaine réunion : <u>6 mai, @Bureau de John</u>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
msgid "No Article found."
msgstr "Aucun article trouvé."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "No Article found. Create \"%s\""
msgstr "Aucun article trouvé. Créez \"%s\""

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "No Article in Trash"
msgstr "Aucun article dans la corbeille"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_favorite_action
msgid "No Favorites yet!"
msgstr "Pas encore de favoris !"

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_member_action
msgid "No Members yet!"
msgstr "Pas encore de membres !"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__inherited_permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__internal_permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article_member__permission__none
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_invite__permission__none
msgid "No access"
msgstr "Pas d'accès"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "No article found."
msgstr "Aucun article trouvé."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/article_index/readonly_article_index.xml:0
msgid "No article to display"
msgstr "Aucun article à afficher"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "No article yet."
msgstr "Pas encore d'article."

#. module: knowledge
#: model_terms:ir.actions.act_window,help:knowledge.knowledge_article_stage_action
msgid "No stage yet!"
msgstr "Pas encore d'étape !"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "No template yet."
msgstr "Pas encore de modèle."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Nothing going on!"
msgstr "Il ne se passe rien !"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Nth <strong>Q</strong>uarter of the fiscal year.<br>\n"
"                            <em>E.g. Q4 starts on Oct. 1 and ends on Dec. 31.</em>"
msgstr ""
"Nième <strong>Q</strong>uarter (Trimestre) de l'exercice fiscal.<br>\n"
"                            <em>Par ex. Q4 commence le 1er octobre et se termine le 31 décembre.</em>"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_needaction_counter
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_needaction_counter
msgid "Number of Actions"
msgstr "Nombre d'actions"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_error_counter
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_error_counter
msgid "Number of errors"
msgstr "Nombre d'erreurs"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_needaction_counter
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Nombre de messages nécessitant une action"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__message_has_error_counter
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Nombre de messages avec des erreurs d'envoi"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Objectives with our Collaboration"
msgstr "Objectifs de notre collaboration"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Odoo Brand Assets"
msgstr "Odoo Brand Assets"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Odoo Experience 🎉"
msgstr "Odoo Experience 🎉"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Odoo: Manage your SME online"
msgstr "Odoo : Gérez votre PME en ligne"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: model:knowledge.article.stage,name:knowledge.knowledge_article_template_stage_ongoing
msgid "Ongoing"
msgstr "En cours"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to alter memberships."
msgstr ""
"Seuls les utilisateurs internes sont autorisés à modifier les affiliations."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to create workspace root articles."
msgstr ""
"Seuls les utilisateurs internes sont autorisés à créer des articles de "
"racine de l'espace de travail."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to modify this information."
msgstr ""
"Seuls les utilisateurs internes sont autorisés à modifier ces informations."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Only internal users are allowed to remove memberships."
msgstr ""
"Seuls les utilisateurs internes sont autorisés à supprimer les affiliations."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"Only internal users are allowed to restore the original article access "
"information."
msgstr ""
"Seuls les utilisateurs internes sont autorisés à restaurer les informations "
"d'accès de l'article d'origine."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Oops, there's nothing here. Try another search."
msgstr "Oups, il n'y a rien ici. Effectuez une autre recherche."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/embedded_view_actions_menu/embedded_view_actions_menu.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_views.xml:0
msgid "Open"
msgstr "Ouvert"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Open Discussions"
msgstr "Discussions ouvertes"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Open comments panel"
msgstr "Ouvrir le volet des commentaires"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Open history"
msgstr "Ouvrir l'historique"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Open the Trash"
msgstr "Ouvrir la corbeille"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "OpenERP becomes Odoo"
msgstr "OpenERP devient Odoo"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid ""
"Organize your database with custom fields\n"
"                        (Text, Selection, ...)."
msgstr ""
"Organisez votre base de données grâce à des champs personnalisés\n"
"(Texte, Sélection,...)."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Otherwise, feel free to handle others listed below:"
msgstr "Sinon, n'hésitez pas à gérer les tâches suivantes :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Our catalog can be found here and is updated every 2 years. If you do not "
"manage to find the specific model you are looking for,"
msgstr ""
"Notre catalogue est disponible ici et est mis à jour tous les 2 ans. Si vous"
" ne parvenez pas à trouver le modèle spécifique que vous recherchez,"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Outcome"
msgstr "Résultat"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__parent_id
msgid "Owner Article"
msgstr "Propriétaire de l'article"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "PLANET ODOO"
msgstr "PLANET ODOO"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Pain Points (<em>tick the relevant ones</em>)"
msgstr "Points de douleur (<em>cochez les points pertinents</em>)"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__parent_id
msgid "Parent Article"
msgstr "Article parent"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__parent_path
msgid "Parent Path"
msgstr "Chemin parent"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_form
msgid "Parent Template"
msgstr "Modèle parent"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__partner_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
msgid "Partner"
msgstr "Partenaire"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_pay_the_electricity_bill
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_pay_the_electricity_bill
msgid "Pay the Electricity Bill"
msgstr "Payer la facture d'électricité"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_member__permission
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__permission
msgid "Permission"
msgstr "Autorisation"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Persona 1"
msgstr "Personnage 1"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid "Persona 2"
msgstr "Personnage 2"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid "Persona 3"
msgstr "Personnage 3"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Persona 4"
msgstr "Personnage 4"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_personal_organizer
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Personal Organizer"
msgstr "Organisateur personnel"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Phone:"
msgstr "Téléphone :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Please pick the following tasks first:"
msgstr "Choisissez d'abord parmi les tâches suivantes :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Please refer to the chart below."
msgstr "Veuillez consulter le tableau suivant."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Please submit a request for assistance to the Marketing Team if you fall "
"into one of the following:"
msgstr ""
"Veuillez soumettre une demande d'assistance à l'équipe Marketing si vous "
"êtes dans l'une des situations suivantes :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Pluralize the trademark (e.g.<em>YourCompanies</em>)"
msgstr "Pluraliser la marque (par ex. <em>VosSociétés</em>)"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "Podcast updates 📻"
msgstr "Mise à jour des podcasts 📻"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Post-Mortem Analysis"
msgstr "Analyse post mortem"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_post_mortem
msgid "Post-mortem"
msgstr "Post mortem"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Potential Risks:"
msgstr "Risques potentiels :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Prepare your demos in advance and integrate the prospect's use case into it"
msgstr ""
"Préparez vos démos à l'avance et intégrez-y le cas d'utilisation de votre "
"prospect"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Preview"
msgstr "Aperçu"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"Prix <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Prix <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Prix <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i"
" class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Prix <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Price <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa "
"fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>​"
msgstr ""
"Prix <span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>​"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Primary"
msgstr "Primaire"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__private
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Private"
msgstr "Privée"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_product_management
msgid "Product Management"
msgstr "Gestion des produits"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_productivity
msgid "Productivity"
msgstr "Productivité"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__article_properties
msgid "Properties"
msgstr "Propriétés"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid ""
"Properties are fields that can only be added on articles that have a parent."
msgstr ""
"Les propriétés sont des champs qui ne peuvent être ajoutés que sur des "
"articles qui ont un parent."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Property Field"
msgstr "Champ de propriété"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Prospect Qualification"
msgstr "Qualification du prospect"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Prospection Templates"
msgstr "Modèles de prospection"

#. module: knowledge
#: model:knowledge.article,template_description:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Provide salespeople with tips, lexicon and templates to help them sell "
"faster."
msgstr ""
"Fournissez aux vendeurs des conseils, du lexique et des modèles pour aider à"
" vendre plus rapidement."

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar_public_holiday
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar_public_holiday
msgid "Public Holidays"
msgstr "Jours fériés"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Q1: <font class=\"text-600\">Would it be possible to...</font>"
msgstr "Q1 : <font class=\"text-600\">Serait-il possible de...</font>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Q2: <font class=\"text-600\">Would there be an issue if...</font>"
msgstr "Q2 : <font class=\"text-600\">Il y aurait-il un problème si...</font>"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "READ"
msgstr "LIRE"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__rating_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__rating_ids
msgid "Ratings"
msgstr "Évaluations"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/mail/message/message_actions.js:0
msgid "Re-open the discussion"
msgstr "Réouvrir la discussion"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Read"
msgstr "Lire"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_invite__partner_ids
msgid "Recipients"
msgstr "Destinataires"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_post_mortem
msgid "Recovery"
msgstr "Récupération"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Refresh"
msgstr "Actualiser"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_release_note
msgid "Release Notes"
msgstr "Notes de version"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "Release Notes 🎉"
msgstr "Notes de version 🎉"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Remove"
msgstr "Supprimer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Remove Cover"
msgstr "Supprimer la couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: code:addons/knowledge/static/src/mail/emoji_picker/emoji_picker_patch.xml:0
msgid "Remove Icon"
msgstr "Supprimer l'icône"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_popover.xml:0
msgid "Remove Link"
msgstr "Supprimer le lien"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Remove Member"
msgstr "Supprimer le membre"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Remove cover"
msgstr "Supprimer la couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.js:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
msgid "Remove from favorites"
msgstr "Supprimer des favoris"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "Rename"
msgstr "Renommer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Replace"
msgstr "Remplacer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Replace cover"
msgstr "Remplacer la couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Reposition"
msgstr "Repositionner"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Reposition cover"
msgstr "Repositionner la couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "Resolved Discussions"
msgstr "Discussions résolues"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__activity_user_id
msgid "Responsible User"
msgstr "Utilisateur responsable"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_trash
msgid "Restore"
msgstr "Restaurer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restore Access"
msgstr "Restaurer l'accès"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Restore from Trash"
msgstr "Restaurer depuis la corbeille"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restrict Access"
msgstr "Restreindre l'accès"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.js:0
msgid "Restrict own access"
msgstr "Restreindre votre propre accès"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_desync_on_root
msgid "Root articles cannot be desynchronized."
msgstr "Les articles racine ne peuvent pas être désynchronisés."

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_permission_on_root
msgid "Root articles must have internal permission."
msgstr "Les articles racine doivent avoir une autorisation interne."

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_template_category_on_root
msgid "Root templates must have a category."
msgstr "Les modèles racine doivent avoir une catégorie"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "SEO &amp; SEA projects (betting on keywords, ...)"
msgstr "Projets SEO &amp; SEA (miser sur des mots-clés, ...)"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__message_has_sms_error
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Erreur d'envoi SMS"

#. module: knowledge
#: model:knowledge.article.template.category,name:knowledge.knowledge_article_template_category_sales
msgid "Sales"
msgstr "Ventes"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Sales Details"
msgstr "Détails de la vente"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sales_playbook
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Sales Playbook"
msgstr "Guide des ventes"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Save"
msgstr "Enregistrer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Save Position"
msgstr "Enregistrer la position"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "Save position"
msgstr "Enregistrer la position"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_menu_article
msgid "Search"
msgstr "Rechercher"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
msgid "Search Favorites"
msgstr "Rechercher dans les favoris"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/xml/form_controller.xml:0
msgid "Search Knowledge Articles"
msgstr "Rechercher dans les articles de Connaissances"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_member_view_search
msgid "Search Members"
msgstr "Rechercher dans les membres"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_search
msgid "Search Stages"
msgstr "Rechercher des étapes"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "Search an Article..."
msgstr "Recherche un article ..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "Search an article..."
msgstr "Rechercher dans un article..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Search for an article..."
msgstr "Rechercher un article..."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "Search hidden Articles..."
msgstr "Rechercher des articles masqués..."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Search results"
msgstr "Résultats de recherche"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/core/embedded_view_link/embedded_view_link_style.js:0
msgid "Secondary"
msgstr "Secondaire"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__category
msgid "Section"
msgstr "Section"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "See a doctor and send us the sick note."
msgstr "Consultez un médecin et envoyez-nous le certificat médical."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/article_template_picker_dialog/article_template_picker_dialog.xml:0
msgid "Select a Template"
msgstr "Sélectionnez un modèle"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/external_embedded_view_favorite_menu/embedded_view_favorite_menu.js:0
msgid "Select an article"
msgstr "Sélectionner un article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Send"
msgstr "Envoyer"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/clipboard/macros_embedded_clipboard.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Send as Message"
msgstr "Envoyer en tant que message"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree
msgid "Send to Trash"
msgstr "Envoyer à la corbeille"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/views/item_calendar/item_calendar_view.js:0
msgid "Send to trash"
msgstr "Déplacer vers la corbeille"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__sequence
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__sequence
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_stage__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Share"
msgstr "Partager"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__shared
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Shared"
msgstr "Partagé"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos
msgid "Shared To-Do List"
msgstr "Liste to-do partagée"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Show All"
msgstr "Afficher tout"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Show Less"
msgstr "Afficher moins"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comment/comment.xml:0
msgid "Show More"
msgstr "Voir plus"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Show Properties"
msgstr "Afficher les propriétés"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Show Weekends?"
msgstr "Afficher les week-ends ?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/plugins/article_index_plugin/article_index_plugin.js:0
msgid "Show nested articles"
msgstr "Affiches les sous-articles"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Size:"
msgstr "Taille :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Social <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"
msgstr ""
"Social <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i class=\"fa "
"fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Statut social <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Statut social <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Social Status <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Statut social <span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid ""
"Social Status ​<span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Statut social ​<span class=\"o_stars o_five_stars\" id=\"checkId-4\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_software_specification
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Software Specification"
msgstr "Spécification du logiciel"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Some articles have been sent to Trash"
msgstr "Certains articles ont été envoyés dans la corbeille"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/embedded_view.xml:0
msgid "Something went wrong!"
msgstr "Une erreur est survenue !"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
msgid "Sonya"
msgstr "Sonya"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Source Feedback"
msgstr "Feedback de la source"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_3
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"Rapidité du service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Rapidité du service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_1
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_2
msgid ""
"Speed of Service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Rapidité du service <span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i"
" class=\"fa fa-star\"></i><i class=\"fa fa-star\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_personas
msgid ""
"Speed of Service ​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i></span>"
msgstr ""
"Rapidité du service ​<span class=\"o_stars o_five_stars\" "
"id=\"checkId-2\"><i class=\"fa fa-star-o\"></i><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_sprint_calendar
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sprint_calendar
msgid "Sprint Calendar"
msgstr "Calendrier des sprints."

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_form
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search_items
msgid "Stage"
msgstr "Étape"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_stage_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_stage_menu
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_tree
msgid "Stages"
msgstr "Phases"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article_stage__parent_id
msgid "Stages are shared among acommon parent and its children articles."
msgstr ""
"Les étapes sont partagés entre un article parent commun et ses articles "
"enfants."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_account_management
msgid "Stakeholders Analysis"
msgstr "Analyse des parties prenantes"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Start Date"
msgstr "Date de début"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "Start Date Time"
msgstr "Date de début"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Start typing"
msgstr "Commencez à écrire"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.xml:0
msgid ""
"Start typing to continue with an empty page or pick an option below to get "
"started."
msgstr ""
"Commencez à écrire pour continuer avec une page vide ou choisissez une "
"option ci-dessous pour commencer."

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_2
msgid ""
"Start working together on any Knowledge Article by sharing your article with"
" others."
msgstr ""
"Commencez à travailler ensemble sur un article de Connaissances en "
"partageant votre article."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Statut basé sur les activités\n"
"En retard : la date d'échéance est déjà dépassée\n"
"Aujourd'hui : la date d'activité est aujourd'hui\n"
"Planifiée : activités futures"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "Stop Date"
msgstr "Date d'arrêt"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Switch Mode"
msgstr "Basculer le mode"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"Talk to you soon,<br>\n"
"                        YourName"
msgstr ""
"À bientôt,<br>\n"
"                        VotreNom"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
msgid "Template"
msgstr "Modèle"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_body
msgid "Template Body"
msgstr "Corps du modèle"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_category_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_category_menu
msgid "Template Categories"
msgstr "Catégories de modèle"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_category_id
msgid "Template Category"
msgstr "Catégorie de modèle"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_category_sequence
msgid "Template Category Sequence"
msgstr "Séquence de catégorie de modèle"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_description
msgid "Template Description"
msgstr "Description du modèle"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_template_view_search
msgid "Template Items"
msgstr "Éléments de modèle"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_preview
msgid "Template Preview"
msgstr "Aperçu du modèle"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_sequence
msgid "Template Sequence"
msgstr "Séquence de modèle"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_template_stage_action
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_stage_menu
msgid "Template Stages"
msgstr "Étapes du modèle"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__template_name
msgid "Template Title"
msgstr "Titre du modèle"

#. module: knowledge
#: model:ir.ui.menu,name:knowledge.knowledge_article_template_menu
msgid "Templates"
msgstr "Modèles"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_template_name_required
msgid "Templates should have a name."
msgstr "Les modèles doivent avoir un nom."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "Test Environment"
msgstr "Environnement de test"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Thank you to everyone involved in the organization of this edition of the\n"
"                            <font class=\"text-o-color-2\">\n"
"                                <strong>Odoo Run</strong>\n"
"                            </font>!"
msgstr ""
"Merci à toutes les personnes impliquées dans l'organisation de cette édition de l'\n"
"                            <font class=\"text-o-color-2\">\n"
"                                <strong>Odoo Run</strong>\n"
"                            </font> !"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "The 5 Commandments"
msgstr "Les 5 commandements"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"The Accounting team handles all payments on Fridays afternoon.\n"
"                <br>\n"
"                If 2 weeks have passed and you are still waiting to be paid, get in touch with them."
msgstr ""
"L'équipe comptabilité traite tous les paiements le vendredi après-midi.\n"
"<br>\n"
"Si deux semaines se sont écoulées et que vous attendez toujours le paiement, contactez l'équipe."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "The Article you are trying to access has been deleted"
msgstr "L'article auquel vous essayez d'accéder a été supprimé"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "The Future Entrepreneurship Fair"
msgstr "Salon du futur de l'entrepreneuriat"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "The article '%s' needs at least one member with 'Write' access."
msgstr ""
"L'article '%s' a besoin d'au moins un membre avec un accès en 'écriture'."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/js/knowledge_controller.js:0
msgid ""
"The article you are trying to open has either been removed or is "
"inaccessible."
msgstr ""
"L'article que vous tentez d'ouvrir a été supprimé ou est inaccessible."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"The destination placement of %(article_name)s is ambiguous, you should "
"specify the category."
msgstr ""
"L'emplacement de destination de %(article_name)s est ambigu. Veuillez "
"préciser la catégorie."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "The operation could not be completed."
msgstr "L'opération n'a pas pu être terminée."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__article_anchor_text
msgid ""
"The original highlighted anchor text, giving initial context if that text is"
" modified or removed afterwards."
msgstr ""
"Le texte d'ancrage original mis en évidence, fournit un contexte initial si "
"le texte est modifié ou supprimé par la suite."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"The podcast launches its new technical talks, for all tech-savvy listeners out there! This\n"
"                            new episode of the series features Géry, the mastermind behind OWL, the world fastest JS\n"
"                            framework. 🚀"
msgstr ""
"Le podcast lance ses nouvelles conférences techniques, pour tous les auditeurs férus de technologie ! Ce\n"
"                            nouvel épisode de la série met en scène Géry, le cerveau derrière OWL, le framework JS\n"
"                            le plus rapide du monde. 🚀"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/macros/abstract_macro.js:0
msgid "The record that this macro is targeting could not be found."
msgstr "L'enregistrement visé par cette macro n'a pas pu être trouvé."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "The selected member does not exists or has been already deleted."
msgstr "Le membre sélectionné n'existe pas ou a déjà été supprimé."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__sequence
msgid ""
"The sequence is computed only among the articles that have the same parent."
msgstr ""
"La séquence est calculée uniquement parmi les articles qui ont le même "
"parent."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.js:0
msgid "The start date property is required."
msgstr "La propriété date de début est obligatoire."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__root_article_id
msgid ""
"The subject is the title of the highest parent in the article hierarchy."
msgstr ""
"Le sujet est le titre du parent le plus élevé dans la hiérarchie des "
"articles."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"The tickets to participate to Odoo Experience 23 are now available and they "
"are cheaper if you book them now!"
msgstr ""
"Les tickets pour participer à Odoo Experience 23 sont maintenant disponibles"
" et ils sont moins chers si vous les réservez maintenant !"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/embedded_view.xml:0
msgid "The view does not exist or you are not allowed to access to it."
msgstr "La vue n'existe pas ou vous n'êtes pas autorisé à y accéder."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "There are no Articles in your Workspace."
msgstr "Il n'y a aucun article dans votre espace de travail."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "Things to handle this week*"
msgstr "Choses à faire cette semaine*"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This Article is in Trash and will be deleted on the"
msgstr "Cet article est dans la corbeille et sera supprimé le"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This article is archived."
msgstr "Cet article est archivé."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "This article is locked"
msgstr "Cet article est vérrouillé"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "This article is only displayed to its members."
msgstr "Cet article n'est affiché qu'à ses membres."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"This year again, Odoo was present at the <em>Tech and Innovation festival for students</em> in\n"
"                            Antwerp, ready to promote our company!<br>Fabien was also there and gave an interview on the main\n"
"                            stage."
msgstr ""
"Cette année encore, Odoo était présent au <em>Technology & Innovation Festival pour étudiants</em> à\n"
"                            Anvers, prêt à promouvoir notre société !<br>Fabien était également présent et a donné une interview sur la scène\n"
"                            principale."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/properties_panel/properties_panel.xml:0
msgid ""
"Those fields will be available on all articles that share the same parent."
msgstr ""
"Ces champs doivent être disponibles sur tous les articles qui partagent le "
"même parent."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__is_resolved
msgid "Thread Closed"
msgstr "Fil fermé"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_product_catalog
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "Tiny House Catalog"
msgstr "Catalogue des tiny houses"

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_0
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_0
msgid "Tip: A Knowledge well kept"
msgstr "Conseil : Un savoir bien gardé"

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_2
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_2
msgid "Tip: Be on the same page"
msgstr "Conseil : Soyez sur la même longueur d'onde"

#. module: knowledge
#: model:digest.tip,name:knowledge.digest_tip_knowledge_1
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_1
msgid "Tip: Use Clipboards to easily inject repetitive content"
msgstr ""
"Conseil : Utilisez les presse-papiers pour insérer facilement du contenu "
"répétitif."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid "Tips to close more deals"
msgstr "Astuces pour conclure plus de ventes"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__name
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_template_category__name
msgid "Title"
msgstr "Titre"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "To be sure to stay updated, follow us on"
msgstr "Pour être sûr de rester informé, suivez-nous sur"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Toggle aside menu"
msgstr "Menu latéral"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Toggle chatter"
msgstr "Chatter"

#. module: knowledge
#: model:ir.actions.act_window,name:knowledge.knowledge_article_action_trashed
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_tree_items
msgid "Trash"
msgstr "Corbeille"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__to_delete
#: model:ir.ui.menu,name:knowledge.knowledge_article_menu_trashed
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Trashed"
msgstr "Dans la corbeille"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_check_trash
msgid "Trashed articles must be archived."
msgstr "Les articles mis à la corbeille doivent être archivés."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Trying to remove wrong member."
msgstr "Tentative de suppression d'un membre erroné."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/embedded_view_link/embedded_view_link_edit_dialog.xml:0
msgid "Type"
msgstr "Type"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type d'activité d'exception enregistrée."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "Unarchive"
msgstr "Désarchiver"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/topbar/topbar.xml:0
msgid "Unlock"
msgstr "Déverrouiller"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Unsupported search operation"
msgstr "Recherche non prise en charge"

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/hierarchy/hierarchy.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
#: code:addons/knowledge/static/src/editor/html_migrations/migration-1.0.js:0
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
#: code:addons/knowledge/static/src/xml/knowledge_command_palette.xml:0
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_hierarchy
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_kanban_items
msgid "Untitled"
msgstr "Sans titre"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/article_index/article_index.xml:0
msgid "Update"
msgstr "Mettre à jour"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Usage"
msgstr "Utilisation"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/clipboard/macros_embedded_clipboard.js:0
msgid "Use as %s"
msgstr "Utiliser comme %s"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/file/macros_file_mixin.xml:0
msgid "Use as Attachment"
msgstr "Utiliser comme pièce jointe"

#. module: knowledge
#: model_terms:digest.tip,tip_description:knowledge.digest_tip_knowledge_1
msgid "Use the /clipboard command on a Knowledge Article and get going."
msgstr ""
"Utilisez la commande /presse-papiers sur un article de Connaissances et "
"lancez-vous."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use the expression \"YourCompanions\" to refer to our community"
msgstr "Utiliser l'expression \"VosCompagnons\" pour désigner notre communauté"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use the logo instead of the word inside sentences"
msgstr "Utiliser le logo à la place du mot dans les phrases"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "Use this template whenever you have an announcement to make."
msgstr "Utilisez ce modèle lorsque vous avez une annonce à faire."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__category
msgid ""
"Used to categorize articles in UI, depending on their main permission "
"definitions."
msgstr ""
"Utilisé pour catégoriser les articles dans l'interface utilisateur, en "
"fonction de leurs principales définitions d'autorisation."

#. module: knowledge
#: model:ir.model,name:knowledge.model_res_users
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_favorite__user_id
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_favorite_view_search
msgid "User"
msgstr "Utilisateur"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_favorite_sequence
msgid "User Favorite Sequence"
msgstr "Séquence préférée de l'utilisateur"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "User Permission"
msgstr "Autorisation de l'utilisateur"

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_favorite_unique_article_user
msgid "User already has this article in favorites."
msgstr "L'utilisateur a déjà cet article dans ses favoris."

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__user_permission
msgid "User permission"
msgstr "Autorisation de l'utilisateur"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_release_note
msgid "VersionName - ReleaseDate"
msgstr "Nom de la version - Date de sortie"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "Visibility"
msgstr "Visibilité"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_customer_persona_4
msgid "Vittorio"
msgstr "Vittorio"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_water_the_plants
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_water_the_plants
msgid "Water the Plants"
msgstr "Donner de l'eau aux plantes"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid "We already cannot wait for the next one🏃"
msgstr "Vivement la prochaine édition🏃"

#. module: knowledge
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article__website_message_ids
#: model:ir.model.fields,field_description:knowledge.field_knowledge_article_thread__website_message_ids
msgid "Website Messages"
msgstr "Messages du site web"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__website_message_ids
#: model:ir.model.fields,help:knowledge.field_knowledge_article_thread__website_message_ids
msgid "Website communication history"
msgstr "Historique de communication du site web"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/res_users.py:0
msgid "Welcome %s"
msgstr "Bienvenue %s"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_company_newsletter
msgid ""
"Welcome to the last edition of the MyCompany Newsletter!<br>\n"
"                We are very excited to share with you the hottest updates and news! 🤩"
msgstr ""
"Bienvenue à la dernière édition de la newsletter de MyCompany !<br>\n"
"                Nous sommes très heureux de partager avec vous les mises à jour et les nouvelles les plus récentes ! 🤩"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "What do I have to do if I cannot work?"
msgstr "Que dois-je faire si je ne peux pas travailler ?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "What do you want to manage?"
msgstr "Que voulez-vous gérer ?"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.xml:0
msgid "What items do you want to manage?"
msgstr "Quels éléments souhaitez-vous gérer ?"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__is_locked
msgid ""
"When locked, users cannot write on the body or change the title, even if "
"they have write access on the article."
msgstr ""
"Lorsqu'il est verrouillé, les utilisateurs ne peuvent pas écrire sur le "
"corps du texte ni modifier le titre, même s'ils ont un accès en écriture sur"
" l'article."

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__to_delete
msgid ""
"When sent to trash, articles are flagged to be deleted\n"
"                days after last edit. knowledge_article_trash_limit_days config\n"
"                parameter can be used to modify the number of days. \n"
"                (default is 30)"
msgstr ""
"Lorsqu'ils sont envoyés à la corbeille, les articles sont marqués pour être supprimés\n"
" quelques jours après la dernière modification. Le paramètre de configuration\n"
" knowledge_article_trash_limit_days peut être utilisé pour modifier le nombre de jours.\n"
"(la valeur par défaut est 30)"

#. module: knowledge
#: model:ir.model.fields,help:knowledge.field_knowledge_article__full_width
msgid ""
"When set, the article body will take the full width available on the article"
" page. Otherwise, the body will have large horizontal margins."
msgstr ""
"Si défini, le corps de l'article prendra toute la largeur disponible sur la "
"page de l'article. Sinon, le corps aura de grandes marges horizontales."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"Whether your heart leans towards the rustic charm of a wooden hut or the "
"intricate beauty of Victorian steelworks, we have you covered. Desiring a "
"specific option? Rest assured, we are here to fulfill your wishes! Just get "
"in touch with our architects and we will make sure to provide any specific "
"choice you desire."
msgstr ""
"Que votre cœur penche pour le charme rustique d'une cabane en bois ou la "
"beauté intrinsèque des aciéries victoriennes, nous avons ce qu'il vous faut."
" Vous souhaitez une option spécifique ? Rassurez-vous, nous sommes là pour "
"répondre à vos souhaits ! Contactez simplement nos architectes et nous nous "
"assurerons de fournir tout choix spécifique que vous désirez."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which color should we pick for the logo?"
msgstr "Quelle couleur devrions-nous choisir pour le logo ?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which size should the rollups be?"
msgstr "Quelle doit être la taille des roll-ups ?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
msgid "Which text should we print on the billboards?"
msgstr "Quel texte devions-nous imprimer sur les panneaux publicitaires ?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"While tiny houses are inherently small, it's important to prioritize comfort"
" when sharing such limited space. To ensure everyone's happiness, we "
"recommend allowing at least 9m² per person. If you plan to live with "
"multiple roommates, our Serenità model is highly recommended, providing "
"ample space for each individual to enjoy<br>"
msgstr ""
"Bien que les tiny houses soient petites, il est important de privilégier de "
"confort lorsque l'on partage un espace aussi limité. Pour assurer le bonheur"
" de chacun, nous recommandons de prévoir au moins 9 m² par personne. Si vous"
" prévoyez de vivre avec plusieurs personnes, notre modèle Serenità est ce "
"qu'il vous faut, car il offre suffisamment d'espace pour que chacun puisse "
"en profiter<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid ""
"While tiny houses do offer a more cost-effective alternative to traditional "
"houses, at MyCompany, we prioritize durability, which comes with a price. "
"However, if you're on a tight budget, we have the perfect solution: our "
"Cielo model.<br>It provides everything you need while allowing you to "
"save.<br>"
msgstr ""
"Bien que les tiny houses offrent une alternative plus rentable aux maisons "
"traditionnelles, chez MyCompany, nous donnons la priorité à la durabilité, "
"ce qui a un prix. Toutefois, si votre budget est serré, nous avons la "
"solution parfaite : le modèle Cielo.<br>Il offre tout ce dont vous avez "
"besoin tout en vous permettant d'économiser.<br>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Who can drive my car?"
msgstr "Qui peut conduire ma voiture ?"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Who do I need to address if I need office supplies?"
msgstr "À qui dois-je m'adresser si j'ai besoin de fournitures de bureau ?"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "Who has access to what? 🕵️"
msgstr "Qui a accès à quoi ? 🕵️"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "Why has my expense not been reimbursed? It has been accepted."
msgstr ""
"Pourquoi ma note de frais n'a-t-elle pas été remboursée ? Elle a été "
"acceptée."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/move_article_dialog/move_article_dialog.js:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__category__workspace
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_search
msgid "Workspace"
msgstr "Espace de travail "

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Write"
msgstr "Écrire"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/wysiwyg_article_helper/wysiwyg_article_helper.js:0
msgid "Write an article about"
msgstr "Écrivez un article sur"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"Write it without any article(<em><s>the</s> YourCompany, <s>a</s> "
"YourCompany, ...</em>)"
msgstr ""
"L'écrire sans article (<em><s>la</s> VotreSociété, <s>une</s> VotreSociété, "
"...</em>)"

#. module: knowledge
#: model:knowledge.article,template_name:knowledge.knowledge_article_template_shared_todos_write_the_next_newsletter
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_shared_todos_write_the_next_newsletter
msgid "Write the next Newsletter"
msgstr "Écrire la nouvelle newsletter"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You"
msgstr "Vous "

#. module: knowledge
#: model:ir.model.constraint,message:knowledge.constraint_knowledge_article_member_unique_article_partner
msgid "You already added this partner on this article."
msgstr "Vous avez déjà ajouté ce partenaire sur cet article."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to create a new template."
msgstr "Vous n'êtes pas autorisé à créer un nouveau modèle."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/readonly_embedded_view.js:0
msgid "You are not allowed to delete a favorite filter in this article."
msgstr ""
"Vous n'êtes pas autorisé à supprimer un filtre favori dans cet article."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to delete a template."
msgstr "Vous n'êtes pas autorisé à supprimer un modèle."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to make '%(article_name)s' private."
msgstr "Vous n'êtes pas autorisé à rendre '%(article_name)s' privé."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You are not allowed to move '%(article_name)s' under '%(parent_name)s'."
msgstr ""
"Vous n'êtes pas autorisé à déplacer '%(article_name)s' sous "
"'%(parent_name)s'."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to move '%(article_name)s'."
msgstr "Vous n'êtes pas autorisé à déplacer '%(article_name)s'."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/editor/embedded_components/backend/view/readonly_embedded_view.js:0
msgid "You are not allowed to save a favorite filter in this article."
msgstr ""
"Vous n'êtes pas autorisé à enregistrer un filtre favori dans cet article."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to update a template."
msgstr "Vous n'êtes pas autorisé à mettre à jour un modèle."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You are not allowed to update the type of a article or a template."
msgstr ""
"Vous n'êtes pas autorisé à mettre à jour le type d'un article ou d'un "
"modèle."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You can't leave an article for which you are the last writer"
msgstr ""
"Vous ne pouvez pas quitter un article dont vous êtes le dernier auteur"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You can't move %(article_name)s under %(item_name)s, as %(item_name)s is an "
"Article Item. Convert %(item_name)s into an Article first."
msgstr ""
"Vous ne pouvez pas déplacer %(article_name)s sous %(item_name)s, car "
"%(item_name)s est un élément d'article. Convertissez %(item_name)s d'abord "
"en un article."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
msgid "You can't remove the last writer of the article"
msgstr "Vous ne pouvez pas supprimer le dernier auteur de l'article"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You cannot add or remove this article to your favorites"
msgstr "Vous ne pouvez pas ajouter ou supprimer cet article de vos favoris"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "You cannot change the internal permission of this article."
msgstr "Vous ne pouvez pas modifier l'autorisation interne de cet article."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/controllers/main.py:0
msgid "You cannot change the permission of this member."
msgstr "Vous ne pouvez pas modifier l'autorisation de ce membre."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_cover.py:0
msgid "You cannot create a new Knowledge Cover from here."
msgstr ""
"Vous ne pouvez pas créer une nouvelle couverture de connaissance à partir "
"d'ici."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You cannot create an article under articles on which you cannot write"
msgstr ""
"Vous ne pouvez pas créer d'article sous des articles sur lesquels vous ne "
"pouvez pas écrire"

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You cannot move an article under %(parent_name)s as you cannot write on it"
msgstr ""
"Vous ne pouvez pas déplacer un article sous %(parent_name)s car vous ne "
"pouvez pas écrire dessus"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/hierarchy/hierarchy.xml:0
msgid "You do not have access to this article"
msgstr "Vous n'avez pas accès à cet article"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar_section.xml:0
msgid "You do not have any private Article."
msgstr "Vous n'avez aucun article privé."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You have to be editor on %(article_name)s to add members."
msgstr ""
"Vous devez être éditeur sur %(article_name)s pour ajouter des membres."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to change its internal permission."
msgstr ""
"Vous devez être éditeur sur %(article_name)s pour modifier son autorisation "
"interne."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to modify members permissions."
msgstr ""
"Vous devez être éditeur sur %(article_name)s pour modifier les autorisations"
" des membres."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You have to be editor on %(article_name)s to remove or exclude member "
"%(member_name)s."
msgstr ""
"Vous devez être éditeur sur %(article_name)s pour supprimer ou exclure le "
"membre %(member_name)s."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You have to be editor on %(article_name)s to restore it."
msgstr "Vous devez être éditeur sur %(article_name)s pour le restaurer."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_sales_playbook
msgid ""
"You may have heard those a million times and yet you are not entirely sure of what it means.<br>\n"
"                Here is a quick recap for you to shine during the next meeting."
msgstr ""
"Vous avez peut-être déjà entendu ces abréviations un million de fois, mais vous n'êtes toujours pas tout à faire sûr de ce qu'elles signifient.<br>\n"
"Voici un bref récapitulatif qui vous permettra de briller lors de la prochaine réunion."

#. module: knowledge
#. odoo-javascript
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.js:0
msgid "You need at least 2 members for the Article to be shared."
msgstr ""
"Vous devez avoir au moins 2 membres pour que l'article puisse être partagé."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "You need to have access to this article in order to join its members."
msgstr "Vous devez avoir accès à cet article pour rejoindre ses membres."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid ""
"You need to have access to this article's root in order to join its members."
msgstr ""
"Vous devez avoir accès à la racine de cet article pour rejoindre ses "
"membres."

#. module: knowledge
#. odoo-python
#: code:addons/knowledge/models/knowledge_article.py:0
msgid "Your Access: %s"
msgstr "Votre accès : %s"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid ""
"Your car can be driven by <strong>you, your spouse and by any person living "
"under the same roof</strong> as long as they have a valid permit."
msgstr ""
"Votre voiture peut être conduite par <strong>vous, votre époux(se) et par "
"toute personne vivant sous votre toit</strong>, pour autant qu'elle soit en "
"possession d'un permis valable."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid "YourCompany is proud to announce that..."
msgstr "VotreSociété est fière d'annoncer que..."

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_brand_assets
msgid ""
"YourCompany is very proud of its brand image.\n"
"                <br>\n"
"                When representing the brand, we thus ask you to be very cautious in how you refer to the company."
msgstr ""
"VotreSociété est très fière de son image de marque.\n"
"                <br>\n"
"                Lorsque vous représentez la marque, nous vous demandons donc d'être très prudent dans la manière dont vous faites référence à l'entreprise."

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "all"
msgstr "tous"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "and the following child article(s) have"
msgstr "et les articles enfants suivants ont"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/webclient/commands/knowledge_providers.js:0
msgid "articles"
msgstr "articles"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "been sent to Trash.<br/><br/>"
msgstr "envoyés à la Corbeille.<br/><br/>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "button to add comments"
msgstr "bouton pour ajouter des commentaires"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_hr_faq
msgid "contact our FleetOfficer"
msgstr "contactez la personne chargée du parc automobile"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/knowledge_cover/knowledge_cover.xml:0
msgid "cover"
msgstr "couverture"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "e.g. Buildings"
msgstr "par ex. Bâtiments"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/item_calendar_props_dialog/item_calendar_props_dialog.xml:0
msgid "e.g. Meetings"
msgstr "par ex. Réunions"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_stage_view_form
msgid "e.g. Ongoing"
msgstr "par ex. En cours"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/prompt_embedded_view_name_dialog/prompt_embedded_view_name_dialog.js:0
msgid "e.g. Todos"
msgstr "par ex. À faire"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "has"
msgstr "a"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_trash_notification
msgid "have"
msgstr "ont"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "invited you to"
msgstr "vous a invité à"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "invited you to access an article.<br/>"
msgstr "vous a invité à accéder à un article.<br/>"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/permission_panel/permission_panel.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
#: code:addons/knowledge/static/src/components/sidebar/sidebar_row.xml:0
#: code:addons/knowledge/static/src/components/with_lazy_loading/with_lazy_loading.xml:0
msgid "loader"
msgstr "chargeur"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_mail_notification_layout
msgid "mentioned you in a comment:"
msgstr "vous a mentionné dans un commentaire :"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__none
msgid "none"
msgstr "aucune"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_user_onboarding
msgid "or"
msgstr "ou"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__read
msgid "read"
msgstr "lire"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "resolved"
msgstr "résolu"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/components/sidebar/sidebar.xml:0
msgid "search"
msgstr "Rechercher"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_mail_invite
msgid "the article"
msgstr "l'article"

#. module: knowledge
#: model_terms:ir.ui.view,arch_db:knowledge.knowledge_article_view_form
msgid "to unleash the power of Knowledge !"
msgstr "pour libérer le pouvoir de Connaissances !"

#. module: knowledge
#. odoo-javascript
#: code:addons/knowledge/static/src/comments/comments_panel/comments_panel.xml:0
msgid "unresolved"
msgstr "non résolu"

#. module: knowledge
#: model:ir.model.fields.selection,name:knowledge.selection__knowledge_article__user_permission__write
msgid "write"
msgstr "écrire"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-1\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-2\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"
msgstr ""
"​<span class=\"o_stars o_five_stars\" id=\"checkId-3\"><i class=\"fa fa-"
"star-o\"></i><i class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i><i "
"class=\"fa fa-star-o\"></i><i class=\"fa fa-star-o\"></i></span>"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "⌚ Elevator Pitch"
msgstr "⌚ Argumentaire"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "☝🏼 Please prepare the following before the meeting:"
msgstr "☝🏼 Veuillez préparer les documents suivants avant la réunion :"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "⚙ Technical Specifications"
msgstr "⚙ Spécifications techniques"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "❓ Open Questions"
msgstr "❓ Questions ouvertes"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "⭐ Release Notes"
msgstr "⭐ Notes de version"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "🎯 Target Audience"
msgstr "🎯 Public cible"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏘️ House Model - Incanto"
msgstr "🏘️ Modèle de maison - Incanto"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏠 House Model - Cielo"
msgstr "🏠 Modèle de maison - Cielo"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🏢 House Model - Serenità"
msgstr "🏢 Modèle de maison - Serenità"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "📊 KPIs"
msgstr "📊 KPIs"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "📝 Purpose"
msgstr "📝 Objectif"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "📣 Message"
msgstr "📣 Message"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🔍 Review Checklist"
msgstr "🔍 Check-list de révision"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🔗 Links"
msgstr "🔗 Liens"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_personal_organizer
msgid "🗓 WEEKLY AGENDA"
msgstr "🗓 AGENDA HEBDOMADAIRE"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "🗣 Meeting Agenda"
msgstr "🗣 Ordre du jour de la réunion"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_example_branding
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_meeting_minutes_template
msgid "🙌🏼 Decisions Taken"
msgstr "🙌🏼 Décisions prises"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_marketing_campaign_brief
msgid "🚀 Objective"
msgstr "🚀 Objectif"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_product_catalog
msgid "🛕 House Model - Dolcezza"
msgstr "🛕 Modèle de maison - Dolcezza"

#. module: knowledge
#: model_terms:knowledge.article,template_body:knowledge.knowledge_article_template_software_specification
msgid "🧠 Functional Specifications"
msgstr "🧠 Spécifications fonctionnelles"
