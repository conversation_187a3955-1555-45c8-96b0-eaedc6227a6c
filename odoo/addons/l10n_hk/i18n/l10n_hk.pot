# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_hk
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-07 09:39+0000\n"
"PO-Revision-Date: 2023-03-07 09:39+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_hk
#: model:ir.model,name:l10n_hk.model_res_partner_bank
msgid "Bank Accounts"
msgstr ""

#. module: l10n_hk
#: model:ir.model.fields,field_description:l10n_hk.field_account_setup_bank_manual_config__country_code
#: model:ir.model.fields,field_description:l10n_hk.field_res_partner_bank__country_code
msgid "Country Code"
msgstr ""

#. module: l10n_hk
#: model:ir.model.fields.selection,name:l10n_hk.selection__res_partner_bank__l10n_hk_fps_type__email
msgid "Email Address"
msgstr ""

#. module: l10n_hk
#: model:ir.model.fields.selection,name:l10n_hk.selection__res_partner_bank__l10n_hk_fps_type__id
msgid "FPS ID"
msgstr ""

#. module: l10n_hk
#: model:ir.model.fields,field_description:l10n_hk.field_account_setup_bank_manual_config__l10n_hk_fps_identifier
#: model:ir.model.fields,field_description:l10n_hk.field_res_partner_bank__l10n_hk_fps_identifier
msgid "FPS ID/Phone Number/Email Address"
msgstr ""

#. module: l10n_hk
#: model:ir.model.fields,field_description:l10n_hk.field_account_setup_bank_manual_config__l10n_hk_fps_type
#: model:ir.model.fields,field_description:l10n_hk.field_res_partner_bank__l10n_hk_fps_type
msgid "FPS Type"
msgstr ""

#. module: l10n_hk
#. odoo-python
#: code:addons/l10n_hk/models/res_bank.py:0
msgid "Invalid Email! Please enter a valid email address."
msgstr ""

#. module: l10n_hk
#. odoo-python
#: code:addons/l10n_hk/models/res_bank.py:0
msgid "Invalid FPS ID! Please enter a valid FPS ID with length 7 or 9."
msgstr ""

#. module: l10n_hk
#. odoo-python
#: code:addons/l10n_hk/models/res_bank.py:0
msgid ""
"Invalid Mobile! Please enter a valid mobile number with format "
"+852-********."
msgstr ""

#. module: l10n_hk
#: model:ir.model.fields.selection,name:l10n_hk.selection__res_partner_bank__l10n_hk_fps_type__phone
msgid "Phone Number"
msgstr ""

#. module: l10n_hk
#: model:ir.model.fields,help:l10n_hk.field_account_setup_bank_manual_config__country_code
#: model:ir.model.fields,help:l10n_hk.field_res_partner_bank__country_code
msgid ""
"The ISO country code in two chars. \n"
"You can use this field for quick search."
msgstr ""

#. module: l10n_hk
#. odoo-python
#: code:addons/l10n_hk/models/res_bank.py:0
msgid ""
"The account receiving the payment must have a FPS type and a FPS identifier "
"set."
msgstr ""

#. module: l10n_hk
#: model_terms:ir.ui.view,arch_db:l10n_hk.view_partner_bank_form_inherit_account
msgid "e.g. +852-********"
msgstr ""
