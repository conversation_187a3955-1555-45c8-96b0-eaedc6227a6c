# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_skills
# 
# Translators:
# Wil Odoo, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 08:39+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Wil Odoo, 2024\n"
"Language-Team: Russian (https://app.transifex.com/odoo/teams/41243/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_skill_type.py:0
msgid "%s (copy)"
msgstr "%s (копия)"

#. module: hr_skills
#: model:ir.actions.report,print_report_name:hr_skills.action_report_employee_cv
msgid "'CV - %s' % (object.name)"
msgstr "'CV - %s' % (object.name)"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "+1234567890"
msgstr "+1234567890"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "2022"
msgstr "2022"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "2023"
msgstr "2023"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_7
msgid "A 2D/3D map generator for incremental games."
msgstr "Генератор 2D/3D карт для инкрементных игр."

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
msgid "ADD"
msgstr "ДОБАВИТЬ"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__active
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__active
msgid "Active"
msgstr "Активный"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_adaptability
msgid "Adaptability"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_agile_scrum
msgid "Agile and Scrum methodologies"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_allenkeller
msgid "Allen-Keller"
msgstr "Аллен-Келлер"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_5
msgid ""
"Allows to encrypt/decrypt plain text or files. Available as a web app or as "
"an API."
msgstr ""
"Позволяет шифровать/дешифровать обычный текст или файлы. Доступно в виде "
"веб-приложения или API."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_goodman_inc
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_ngh_jackson_schwartz_and_aguirre
msgid "Analytical chemist"
msgstr "Химик-аналитик"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_analytics
msgid "Analytics"
msgstr "Аналитика"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_android
msgid "Android"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_arabic
msgid "Arabic"
msgstr "Арабский"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_green_ltd
msgid "Arboriculturist"
msgstr "Арборикультурист"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_wilkinson_plc
msgid "Architectural technologist"
msgstr "Архитектурный технолог"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
msgid "Archived"
msgstr "Архивировано"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_armidale_city_public_school
msgid "Armidale City Public School"
msgstr "Городская государственная школа Армидейла"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_arnoldcohen
msgid "Arnold-Cohen"
msgstr "Арнольд-Коэн"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_niv_arroyo_ltd
msgid "Arroyo Ltd"
msgstr "Арройо Лтд"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_avoca_primary_school
msgid "Avoca Primary School"
msgstr "Начальная школа Авока"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_bathurst_west_public_school
msgid "Bathurst West Public School"
msgstr "Государственная школа Батерст Вест"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_bengali
msgid "Bengali"
msgstr "Бенгальский"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_big_data_technologies
msgid "Big data technologies (Hadoop,Spark)"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_russellwebster
msgid "Biochemist, clinical"
msgstr "Биохимик, клинический"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_blue_mountains_grammar_school
msgid "Blue Mountains Grammar School"
msgstr "Гимназия Голубых гор"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_boyd_wilson_and_moore
msgid "Boyd, Wilson and Moore"
msgstr "Бойд, Уилсон и Мур"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_burns_lester_and_cuevas
msgid "Burns, Lester and Cuevas"
msgstr "Бернс, Лестер и Куэвас"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_3
msgid "Burtho Inc."
msgstr "Burtho Inc."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_c
msgid "C/C++"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cms
msgid "CMS"
msgstr ""

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/xml/resume_templates.xml:0
msgid "CREATE A NEW ENTRY"
msgstr "СОЗДАТЬ НОВУЮ ЗАПИСЬ"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_css
msgid "CSS"
msgstr ""

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__can_show_others
msgid "Can Show Others"
msgstr "Может показать другим"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__can_show_skills
msgid "Can Show Skills"
msgstr "Может демонстрировать навыки"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_garcia_and_sons
msgid "Careers information officer"
msgstr "Информационный сотрудник по вопросам карьеры"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_chavez_group
msgid "Chavez Group"
msgstr "Группа Чавеса"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_christian_outreach_college
msgid "Christian Outreach College"
msgstr "Христианский аутрич-колледж"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_lewisbailey
msgid "Civil Service fast streamer"
msgstr "Гражданская служба - быстрый поток"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_claremont_college
msgid "Claremont College"
msgstr "Клэрмонтский колледж"

#. module: hr_skills
#: model:ir.model.fields.selection,name:hr_skills.selection__hr_resume_line__display_type__classic
msgid "Classic"
msgstr "Классический"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cloud_computing
msgid "Cloud computing"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_cole_ltd
msgid "Cole Ltd"
msgstr "Коул Лтд"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__color
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__color
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__color
msgid "Color"
msgstr "Цвет"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Colors"
msgstr "Цвета"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_com
#: model:hr.skill,name:hr_skills.hr_skill_communication
msgid "Communication"
msgstr "Связь"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__company_id
msgid "Company"
msgstr "Компания"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fpi_hubbarddean
msgid "Conference centre manager"
msgstr "Менеджер конференц-центра"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_conflict_management
msgid "Conflict Management"
msgstr ""

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_contact
msgid "Contact Information"
msgstr "Контактная информация"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_finley_rowe_and_adams
msgid "Copywriter, advertising"
msgstr "Копирайтер, реклама"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_craigmore_south_junior_primary_school
msgid "Craigmore South Junior Primary School"
msgstr "Южная младшая начальная школа Крейгмор"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid "Create a new entry"
msgstr "Создайте новую запись"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Create new Skills"
msgstr "Создать новые навыки"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_uid
msgid "Created by"
msgstr "Создано"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__create_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__create_date
msgid "Created on"
msgstr "Создано"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_creativity
msgid "Creativity"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_critical_thinking
msgid "Critical Thinking"
msgstr ""

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid "Current"
msgstr "Текущий"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_davis_sanchez_and_miller
msgid "Customer service manager"
msgstr "Менеджер по работе с клиентами"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_cybersecurity
msgid "Cybersecurity"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_dandenong_north_primary_school
msgid "Dandenong North Primary School"
msgstr "Начальная школа Данденонг Норт"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_darlington_primary_school
msgid "Darlington Primary School"
msgstr "Дарлингтонская начальная школа"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_data_analysis
msgid "Data analysis/visualization"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_database_management
msgid "Database Management"
msgstr ""

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__date
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Date"
msgstr "Дата"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_end
msgid "Date End"
msgstr "Дата окончания"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__date_start
msgid "Date Start"
msgstr "Дата начала"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_davis_plc
#: model:hr.resume.line,name:hr_skills.employee_resume_han_davis_plc
msgid "Davis PLC"
msgstr "Davis PLC"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_davis_and_sons
msgid "Davis and Sons"
msgstr "Дэвис и сыновья"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_davis_sanchez_and_miller
msgid "Davis, Sanchez and Miller"
msgstr "Дэвис, Санчес и Миллер"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_decision_making
msgid "Decision-Making"
msgstr ""

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__default_level
msgid "Default Level"
msgstr "Уровень по умолчанию"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Demo Address"
msgstr "Демонстрационный адрес"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Demo Company Name"
msgstr "Название демо-компании"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__department_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__department_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Department"
msgstr "Отдел"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__description
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Description"
msgstr "Описание"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_white_inc
msgid "Designer, television/film set"
msgstr "Дизайнер, декорации для телевидения и кино"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_devops
msgid "DevOps"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_digital_ad
msgid "Digital advertising"
msgstr ""

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Discard"
msgstr "Отменить"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Display"
msgstr "Показать"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__display_name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__display_name
msgid "Display Name"
msgstr "Отображаемое имя"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__display_type
msgid "Display Type"
msgstr "Тип отображения"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_django
msgid "Django"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_douglas_thompson_and_conner
msgid "Douglas, Thompson and Conner"
msgstr "Дуглас, Томпсон и Коннер"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Duration"
msgstr "Продолжительность"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_education
msgid "Education"
msgstr "Образование"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_ellinbank_primary_school
msgid "Ellinbank Primary School"
msgstr "Начальная школа Эллинбэнк"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_elphinstone_primary_school
msgid "Elphinstone Primary School"
msgstr "Начальная школа Элфинстоун"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_email
msgid "Email Marketing"
msgstr "Рекламная рассылка"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__employee_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__employee_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__employee_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employee"
msgstr "Сотрудник"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__display_name
msgid "Employee Name"
msgstr "ФИО сотрудника"

#. module: hr_skills
#: model:ir.actions.report,name:hr_skills.action_report_employee_cv
#: model:ir.model,name:hr_skills.model_report_hr_skills_report_employee_cv
msgid "Employee Resume"
msgstr "Резюме сотрудника"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_employee_skill_report_action
msgid "Employee Skills"
msgstr "Навыки сотрудников"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill_report
msgid "Employee Skills Report"
msgstr "Отчет о квалификации сотрудников"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employees with Skills"
msgstr "Сотрудники с навыками"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
msgid "Employees without Skills"
msgstr "Сотрудники без навыков"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_5
msgid "Encryption/decryption"
msgstr "Шифрование/дешифрование"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_al_jones_ltd
msgid "Energy manager"
msgstr "Менеджер по энергетике"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_hughes_parker_and_barber
msgid "Engineer, drilling"
msgstr "Инженер, бурение"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_schultz_inc
msgid "Engineer, electrical"
msgstr "Инженер, электрик"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_johnson_shaw_and_carroll
msgid "Engineer, mining"
msgstr "Инженер, горнодобывающая промышленность"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_han_davis_plc
msgid "Engineer, production"
msgstr "Инженер, производство"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_english
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "English"
msgstr "Английский"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_6
msgid ""
"Enter your finance data and the app tries to forecast what will be your "
"future incomes/expenses. The application uses machine learning to train "
"itself."
msgstr ""
"Введите данные о своих финансах, и приложение попытается спрогнозировать, "
"какими будут ваши будущие доходы/расходы. Приложение использует машинное "
"обучение для самообучения."

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_evans_cooper_and_white
msgid "Evans, Cooper and White"
msgstr "Эванс, Купер и Уайт"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_experience
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Experience"
msgstr "Опыт"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_cole_ltd
msgid "Fast food restaurant manager"
msgstr "Менеджер ресторана быстрого питания"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_filipino
msgid "Filipino"
msgstr "Филиппинский"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_6
msgid "Finance forecaster"
msgstr "Финансовый прогнозист"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_finley_rowe_and_adams
msgid "Finley, Rowe and Adams"
msgstr "Финли, Роу и Адамс"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Fluent"
msgstr "Свободный"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_fox_and_sons
msgid "Fox and Sons"
msgstr "Фокс и сыновья"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_freeman_williams_and_berger
msgid "Freeman, Williams and Berger"
msgstr "Фримен, Уильямс и Бергер"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_french
msgid "French"
msgstr "Французский"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_galilee_catholic_school
msgid "Galilee Catholic School"
msgstr "Католическая школа Галилея"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_gallegos_little_and_walters
msgid "Gallegos, Little and Walters"
msgstr "Гальегос, Литтл и Уолтерс"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_garcia_and_sons
msgid "Garcia and Sons"
msgstr "Гарсия и сыновья"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_garcia_smith_and_king
msgid "Garcia, Smith and King"
msgstr "Гарсия, Смит и Кинг"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_hanson_roach_and_jordan
msgid "Geographical information systems officer"
msgstr "Сотрудник по географическим информационным системам"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_han_perezmorgan
msgid "Geoscientist"
msgstr "Геофизик"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_german
msgid "German"
msgstr "Немецкий"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_lur_ramirez_inc
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_hill_group
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_ngh_stanleymendez
msgid "Glass blower/designer"
msgstr "Стеклодув/дизайнер"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_go
msgid "Go"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_goodman_inc
msgid "Goodman Inc"
msgstr "Гудман Инк"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_green_ltd
msgid "Green Ltd"
msgstr "Грин Лтд"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_greeneorr
msgid "Greene-Orr"
msgstr "Грин-Орр"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Group By"
msgstr "Группировать по"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Group By..."
msgstr "Группировать По…"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_html
msgid "HTML"
msgstr "HTML"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_hadoop
msgid "Hadoop"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_chs_hanson_roach_and_jordan
msgid "Hanson, Roach and Jordan"
msgstr "Хансон, Роуч и Джордан"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_harrington_park_public_school
msgid "Harrington Park Public School"
msgstr "Государственная школа Харрингтон Парк"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__has_department_manager_access
msgid "Has Department Manager Access"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_davis_and_sons
msgid "Health physicist"
msgstr "Физик здоровья"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_hill_group
msgid "Hill Group"
msgstr "Хилл Групп"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_hindi
msgid "Hindi"
msgstr "Хинди"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_holy_family_primary_school
msgid "Holy Family Primary School"
msgstr "Начальная школа Святого Семейства"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_hne_nortonsilva
msgid "Horticulturist, commercial"
msgstr "Садовод, коммерческий"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_hubbarddean
msgid "Hubbard-Dean"
msgstr "Хаббард-Дин"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_hughes_parker_and_barber
msgid "Hughes, Parker and Barber"
msgstr "Хьюз, Паркер и Барбер"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_freeman_williams_and_berger
msgid "Human resources officer"
msgstr "Сотрудник отдела кадров"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__id
msgid "ID"
msgstr "ID"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_it
msgid "IT"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_governance_compliance
msgid "IT governance and compliance (GDPR,HIPAA,...)"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_infrastructure_architecture
msgid "IT infrastructure and architecture"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jth_simmonswilcox
msgid "IT sales professional"
msgstr "Специалист по продажам в сфере информационных технологий"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_service_management
msgid "IT service management (ITSM)"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_it_support
msgid "IT support"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jgo_martin_stanley_and_duncan
msgid "IT technical support officer"
msgstr "Сотрудник службы технической поддержки ИТ"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__default_level
msgid ""
"If checked, this level will be the default one selected when choosing this "
"skill."
msgstr ""
"Если флажок установлен, этот уровень будет выбран по умолчанию при выборе "
"данного навыка."

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "If skills are missing, they can be created by an HR officer."
msgstr "Если навыков не хватает, их может создать сотрудник отдела кадров."

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill_report__active
msgid ""
"If the active field is set to False, it will allow you to hide the resource "
"record without removing it."
msgstr ""
"Если значение активного поля \"ложно\", это позволит вам скрыть запись "
"ресурса, не удаляя ее."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_indonesian
msgid "Indonesian"
msgstr "Индонезийский"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_niv_arroyo_ltd
msgid "Insurance risk surveyor"
msgstr "Сюрвейер по страховым рискам"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_iot_embedded_systems
msgid "IoT and embedded systems"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_jackson_schwartz_and_aguirre
msgid "Jackson, Schwartz and Aguirre"
msgstr "Джексон, Шварц и Агирре"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_japanese
msgid "Japanese"
msgstr "Японский"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_java
msgid "Java"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_javanese
msgid "Javanese"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_js
msgid "Javascript"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_saundersadkins
msgid "Jewellery designer"
msgstr "Дизайнер ювелирных изделий"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_4
msgid ""
"Job position: Development team leader\n"
"- Supported technical operations with investigating and correcting varied production support issues (Java, Perl, Shell scripts, SQL).\n"
"- Led quality assurance planning for multiple concurrent projects relative to overall system architecture or trading system changes/new developments.\n"
"- Configured and released business critical alpha and risk models using MATLAB and SQL with inputs from Portfolio Managers."
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_3
msgid ""
"Job position: Product manager\n"
"- Coordinated and managed software deployment across five system environments from development to production.\n"
"- Developed stored procedures to assist Java level programming efforts.\n"
"- Developed multiple renewable energy plant architectures, both commercial installations and defense-related."
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_johnson_shaw_and_carroll
msgid "Johnson, Shaw and Carroll"
msgstr "Джонсон, Шоу и Кэрролл"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_al_jones_ltd
msgid "Jones Ltd"
msgstr "Джонс Лтд"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_niv_kialla_west_primary_school
msgid "Kialla West Primary School"
msgstr "Начальная школа Киалла Вест"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_king_island_district_high_school
msgid "King Island District High School"
msgstr "Окружная средняя школа Кинг-Айленда"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_korean
msgid "Korean"
msgstr "Корейский"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_kotlin
msgid "Kotlin"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_rivera_shaw_and_hughes
msgid "Landscape architect"
msgstr "Ландшафтный архитектор"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_lang
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Languages"
msgstr "Языки"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_uid
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_uid
msgid "Last Updated by"
msgstr "Последнее обновление"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__write_date
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__write_date
msgid "Last Updated on"
msgstr "Последнее обновление"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_lawson_public_school
msgid "Lawson Public School"
msgstr "Государственная школа Лоусона"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_leadership
msgid "Leadership"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_vad_gallegos_little_and_walters
msgid "Lecturer, higher education"
msgstr "Преподаватель, высшее образование"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_leinster_school
msgid "Leinster School"
msgstr "Школа Лейнстер"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__level_progress
msgid "Level Progress"
msgstr "Прогресс уровня"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_level_ids
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "Levels"
msgstr "Уровни"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_lewis_group
msgid "Lewis Group"
msgstr "Группа Льюиса"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_lewisbailey
msgid "Lewis-Bailey"
msgstr "Льюис-Бейли"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jog_allenkeller
msgid "Lexicographer"
msgstr "Лексикограф"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_lindenow_primary_school
msgid "Lindenow Primary School"
msgstr "Начальная школа Линденоу"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.hr_resume_line_type_menu
msgid "Line Types"
msgstr "Типы линий"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_loganmartin
msgid "Logan-Martin"
msgstr "Логан-Мартин"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_company
msgid "Logo"
msgstr "Логотип"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_lynchhodges
msgid "Lynch-Hodges"
msgstr "Линч-Ходжес"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_machine_learning
msgid "Machine Learning (AI)"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_admin_greeneorr
msgid "Magazine journalist"
msgstr "Журналист журнала"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_mandarin_chinese
msgid "Mandarin Chinese"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_mandurah_catholic_college
msgid "Mandurah Catholic College"
msgstr "Католический колледж Мандуры"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_7
msgid "Map Generator"
msgstr "Генератор карт"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_marathi
msgid "Marathi"
msgstr "Маратхи"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "Marc Demo"
msgstr "Марк Демо"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_marketing
msgid "Marketing"
msgstr "Маркетинг"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_martin_stanley_and_duncan
msgid "Martin, Stanley and Duncan"
msgstr "Мартин, Стэнли и Дункан"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_1
msgid ""
"Master in Electrical engineering\n"
"            Master thesis: Better grid management and control through machine learning"
msgstr ""
"Магистр в области электротехники\n"
"            Магистерская диссертация: Улучшение управления и контроля энергосистем с помощью машинного обучения"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_matlab
msgid "Matlab"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_mcneil_rodriguez_and_warren
msgid "Mcneil, Rodriguez and Warren"
msgstr "Макнейл, Родригес и Уоррен"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_al_garcia_smith_and_king
msgid "Medical illustrator"
msgstr "Медицинский иллюстратор"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_boyd_wilson_and_moore
msgid "Medical physicist"
msgstr "Медицинский физик"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fpi_chavez_group
msgid "Mental health nurse"
msgstr "Медсестра по охране психического здоровья"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jgo_fox_and_sons
msgid "Merchant navy officer"
msgstr "Офицер торгового флота"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_mobile_app_development
msgid "Mobile app development"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jog_douglas_thompson_and_conner
msgid "Music therapist"
msgstr "Музыкальный терапевт"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__name
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__name
msgid "Name"
msgstr "Имя"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_narellan_public_school
msgid "Narellan Public School"
msgstr "Государственная школа Нареллана"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_narrogin_primary_school
msgid "Narrogin Primary School"
msgstr "Наррогинская начальная школа"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_network_administration
msgid "Network administration"
msgstr ""

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.js:0
msgid "New Resume line"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_nosql
msgid "NoSQL"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_northern_bay_p12_college
msgid "Northern Bay P-12 College"
msgstr "Колледж Северной бухты P-12"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_nortonsilva
msgid "Norton-Silva"
msgstr "Нортон-Сильва"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_4
msgid "Odoo SA"
msgstr "Odoo SA"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_openness_to_criticism
msgid "Openness to criticism"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_organizational
msgid "Organizational"
msgstr ""

#. module: hr_skills
#. odoo-javascript
#. odoo-python
#: code:addons/hr_skills/report/hr_employee_cv_report.py:0
#: code:addons/hr_skills/static/src/views/skills_list_renderer.js:0
msgid "Other"
msgstr "Другое"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_others
msgid "Others"
msgstr "Другие"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_our_lady_star_of_the_sea_school
msgid "Our Lady Star of the Sea School"
msgstr "Пресвятая Дева Звезда Морской школы"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_php
msgid "PHP"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_park_lake_state_school
msgid "Park Lake State School"
msgstr "Государственная школа Парк-Лейк"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_parke_state_school
msgid "Parke State School"
msgstr "Школа штата Парке"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_parker_roberson_and_acosta
msgid "Parker, Roberson and Acosta"
msgstr "Паркер, Роберсон и Акоста"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_perezmorgan
msgid "Perez-Morgan"
msgstr "Перес-Морган"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_perl
msgid "Perl"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jve_arnoldcohen
msgid "Personnel officer"
msgstr "Сотрудник кадровой службы"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_persuasion
msgid "Persuasion"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_vad_loganmartin
msgid "Petroleum engineer"
msgstr "Инженер-нефтяник"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_phillips_jones_and_brown
msgid "Phillips, Jones and Brown"
msgstr "Филлипс, Джонс и Браун"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Pick a skill from the list"
msgstr "Выберите навык из списка"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_burns_lester_and_cuevas
msgid "Police officer"
msgstr "Офицер полиции"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jog_port_curtis_road_state_school
msgid "Port Curtis Road State School"
msgstr "Государственная школа Порт Кертис Роуд"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_portuguese
msgid "Portuguese"
msgstr ""

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Present"
msgstr "Сейчас"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__color_primary
msgid "Primary Color"
msgstr "Основной цвет"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Print"
msgstr "Печать"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/wizard/hr_employee_cv_wizard.py:0
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_cv_wizard
#: model:ir.actions.server,name:hr_skills.action_print_employees_cv
#: model:ir.model,name:hr_skills.model_hr_employee_cv_wizard
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_cv_wizard_view_form
msgid "Print Resume"
msgstr "Печать резюме"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_problem_solving
msgid "Problem-Solving"
msgstr ""

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_dev
msgid "Programming Languages"
msgstr ""

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__level_progress
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__level_progress
msgid "Progress"
msgstr "Прогресс"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Progress (%)"
msgstr "Прогресс (%)"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Progress bar"
msgstr "Прогресс-бар"

#. module: hr_skills
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_employee_skill_log__level_progress
#: model:ir.model.fields,help:hr_skills.field_hr_skill_level__level_progress
msgid "Progress from zero knowledge (0%) to fully mastered (100%)."
msgstr "Продвигайтесь от нулевых знаний (0%) до полностью освоенных (100%)."

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_skill_level_check_level_progress
msgid "Progress should be a number between 0 and 100."
msgstr "Прогресс должен быть числом от 0 до 100."

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_project_management
msgid "Project Management"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_robinson_crawford_and_norman
msgid "Psychiatric nurse"
msgstr "Психиатрическая медсестра"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_public
msgid "Public Employee"
msgstr "Государственный служащий"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_public
msgid "Public Speaking"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_stw_lynchhodges
msgid "Publishing rights manager"
msgstr "Менеджер по издательским правам"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_punjabi
msgid "Punjabi"
msgstr "Панджаби"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_python
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Python"
msgstr "Python"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_sql
msgid "RDMS"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_ramirez_inc
msgid "Ramirez Inc"
msgstr "Рамирес Инк"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_react
msgid "React"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_resourcefulness
msgid "Resourcefulness"
msgstr ""

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_resource_resource
msgid "Resources"
msgstr "Ресурсы"

#. module: hr_skills
#: model:ir.ui.menu,name:hr_skills.menu_human_resources_configuration_resume
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_form_inherit
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_public_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.res_users_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Resume"
msgstr "Резюме"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/controllers/main.py:0
msgid "Resume %s"
msgstr "Резюме %s"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_resume_type_action
msgid "Resume Line Types"
msgstr "Типы строк резюме"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line
msgid "Resume line of an employee"
msgstr "Строка резюме сотрудника"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__resume_line_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__resume_line_ids
msgid "Resume lines"
msgstr "Строки резюме"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/controllers/main.py:0
msgid "Resumes"
msgstr "Резюме"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_qdp_rivera_shaw_and_hughes
msgid "Rivera, Shaw and Hughes"
msgstr "Ривера, Шоу и Хьюз"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_robinson_crawford_and_norman
msgid "Robinson, Crawford and Norman"
msgstr "Робинсон, Кроуфорд и Норман"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_ruby
msgid "Ruby"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_russellwebster
msgid "Russell-Webster"
msgstr "Рассел-Уэбстер"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_russian
msgid "Russian"
msgstr "Русский"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_rust
msgid "Rust"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_2
msgid "Saint-Joseph School"
msgstr "Школа Сен-Жозеф"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_saundersadkins
msgid "Saunders-Adkins"
msgstr "Сондерс-Адкинс"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_scala
msgid "Scala"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_schultz_inc
msgid "Schultz Inc"
msgstr "Шульц Инк"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_line_admin_2
msgid "Science &amp; math"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_mit_parker_roberson_and_acosta
msgid "Science writer"
msgstr "Научный писатель"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
msgid "Search Logs"
msgstr "Найти"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Search Skill"
msgstr "Искать навык"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
msgid "Search Skill Type"
msgstr ""

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__color_secondary
msgid "Secondary Color"
msgstr "Вторичный цвет"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_chs_davis_plc
msgid "Secretary, company"
msgstr "Секретарь, компания"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/form_view_one2many/form_view_one2many.xml:0
msgid "Select & Close"
msgstr "Выберите и закройте"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/form_view_one2many/form_view_one2many.xml:0
msgid "Select & New"
msgstr "Выбор и новинки"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.js:0
msgid "Select Skills"
msgstr "Выберите навыки"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line_type__sequence
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__sequence
msgid "Sequence"
msgstr "Последовательность"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_seymour_p12_college
msgid "Seymour P-12 College"
msgstr "Колледж Сеймур P-12"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_side_projects
msgid "Side Projects"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_simmonswilcox
msgid "Simmons-Wilcox"
msgstr "Симмонс-Вилкокс"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Skill"
msgstr "Навык"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_skill_log_department
#: model:ir.actions.act_window,name:hr_skills.action_hr_employee_skill_log_employee
#: model:ir.actions.server,name:hr_skills.action_open_skills_log_department
msgid "Skill History Report"
msgstr "Отчет об истории навыков"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_level
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_level_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_level_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_level
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_form
msgid "Skill Level"
msgstr "Уровень квалификации"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_level_view_tree
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_tree
msgid "Skill Levels"
msgstr "Уровни сложности"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_skill_type
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_log__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_skill_report__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill__skill_type_id
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_level__skill_type_id
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_report_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_search
msgid "Skill Type"
msgstr "Тип навыков"

#. module: hr_skills
#: model:ir.actions.act_window,name:hr_skills.hr_skill_type_action
#: model:ir.ui.menu,name:hr_skills.hr_skill_type_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_search
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_type_view_tree
msgid "Skill Types"
msgstr "Типы навыков"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill
msgid "Skill level for an employee"
msgstr "Уровень квалификации сотрудника"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/components/avatar_card_resource/avatar_card_resource_popover.xml:0
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_cv_wizard__show_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_employee_public__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_hr_skill_type__skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_res_users__employee_skill_ids
#: model:ir.model.fields,field_description:hr_skills.field_resource_resource__employee_skill_ids
#: model:ir.ui.menu,name:hr_skills.hr_employee_skill_report_menu
#: model_terms:ir.ui.view,arch_db:hr_skills.employee_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_skill_view_form
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
msgid "Skills"
msgstr "Навыки"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_employee_skill_log
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_department_view_kanban
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_graph_department
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_graph_employee
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_log_view_tree
msgid "Skills History"
msgstr "История навыков"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.js:0
msgid "Skills Report"
msgstr "Отчет о навыках"

#. module: hr_skills
#: model:hr.resume.line.type,name:hr_skills.resume_type_social_media
msgid "Social Media"
msgstr "Социальные сети"

#. module: hr_skills
#: model:hr.skill.type,name:hr_skills.hr_skill_type_softskill
msgid "Soft Skills"
msgstr ""

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_main_panel
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "Software Developer"
msgstr "Разработчик"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_spanish
msgid "Spanish"
msgstr "Испанский"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_spark
msgid "Spark"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_lur_whitebell
msgid "Sports coach"
msgstr "Спортивный тренер"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_fme_lewis_group
msgid "Sports development officer"
msgstr "Сотрудник по развитию спорта"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_st_michaels_primary_school
msgid "St Michael's Primary School"
msgstr "Начальная школа Святого Михаила"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_hne_st_peters_parish_primary_school
msgid "St Peter's Parish Primary School"
msgstr "Начальная школа прихода Святого Петра"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_st_raphaels_primary_school
msgid "St Raphael's Primary School"
msgstr "Начальная школа Святого Рафаила"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_ngh_stanleymendez
msgid "Stanley-Mendez"
msgstr "Стэнли-Мендес"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_stress_management
msgid "Stress management"
msgstr ""

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jep_mcneil_rodriguez_and_warren
msgid "Sub"
msgstr "Под"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_mit_sutherland_dianella_primary_school
msgid "Sutherland Dianella Primary School"
msgstr "Начальная школа Сазерленд-Дианелла"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_swift
msgid "Swift"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_system_administration
msgid "System Administration (Linux, Windows)"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jve_talbot_primary_school
msgid "Talbot Primary School"
msgstr "Начальная школа Талбот"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_phillips_jones_and_brown
msgid "Teacher, special educational needs"
msgstr "Преподаватель, особые образовательные потребности"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_teamwork
msgid "Teamwork"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_telugu
msgid "Telugu"
msgstr "Телугу"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_employee_skill.py:0
msgid "The skill %(name)s and skill type %(type)s doesn't match"
msgstr "Навык %(name)s и тип навыка %(type)s не совпадают"

#. module: hr_skills
#. odoo-python
#: code:addons/hr_skills/models/hr_employee_skill.py:0
msgid "The skill level %(level)s is not valid for skill type: %(type)s"
msgstr "Уровень навыка %(level)s не действителен для типа навыка: %(type)s"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_resume_line_date_check
msgid "The start date must be anterior to the end date."
msgstr "Начальная дата должна быть меньше конечной."

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_qdp_evans_cooper_and_white
msgid "Therapist, speech and language"
msgstr "Терапевт, речь и язык"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/resume_one2many/resume_one2many.xml:0
msgid ""
"There are no resume lines on this employee.\n"
"                        Why not add a new one?"
msgstr ""

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "There are no skills defined in the library."
msgstr "В библиотеке нет определенных навыков."

#. module: hr_skills
#: model_terms:ir.actions.act_window,help:hr_skills.hr_employee_skill_report_action
msgid ""
"This report will give you an overview of the skills per Employee.\n"
"                Create them in configuration and add them on the Employee."
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_thomas_chirnside_primary_school
msgid "Thomas Chirnside Primary School"
msgstr "Начальная школа Томаса Чирнсайда"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_time_management
msgid "Time Management"
msgstr "Тайм менеджмент"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Timeline"
msgstr "Временная шкала"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "Title"
msgstr "Заголовок"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jgo_tottenham_central_school
msgid "Tottenham Central School"
msgstr "Центральная школа Тоттенхэма"

#. module: hr_skills
#: model_terms:hr.resume.line,description:hr_skills.employee_resume_jod_wilson_ltd
msgid "Trade union research officer"
msgstr "Сотрудник по исследованиям профсоюзов"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_trinity_college
msgid "Trinity College"
msgstr "Тринити-колледж"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_turkish
msgid "Turkish"
msgstr "Турецкий"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill__unique_skill
msgid "Two levels for the same skill is not allowed"
msgstr "Два уровня для одного навыка не допускаются"

#. module: hr_skills
#: model:ir.model.constraint,message:hr_skills.constraint_hr_employee_skill_log__unique_skill_log
msgid "Two levels for the same skill on the same day is not allowed"
msgstr ""
"Два уровня для одного и того же навыка в один и тот же день не допускаются"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_tyndale_christian_school
msgid "Tyndale Christian School"
msgstr "Христианская школа Тиндейл"

#. module: hr_skills
#: model:ir.model.fields,field_description:hr_skills.field_hr_resume_line__line_type_id
msgid "Type"
msgstr "Тип"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_hr_resume_line_type
msgid "Type of a resume line"
msgstr "Тип строки резюме"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_typescript
msgid "TypeScript"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jod_umbakumba_school
msgid "Umbakumba School"
msgstr "Школа Умбакумба"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_line_admin_1
msgid "Université Libre de Bruxelles - Polytechnique"
msgstr "Свободный университет Брюсселя - Политехнический институт"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_urdu
msgid "Urdu"
msgstr "Урду"

#. module: hr_skills
#: model:ir.model,name:hr_skills.model_res_users
msgid "User"
msgstr "Пользователь"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_virtualization_containerization
msgid "Virtualization and Containerization"
msgstr ""

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_web_development
msgid "Web Development"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_admin_white_inc
msgid "White Inc"
msgstr "Уайт Инк"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_lur_whitebell
msgid "White-Bell"
msgstr "Белый колокольчик"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_stw_whitsunday_anglican_school
msgid "Whitsunday Anglican School"
msgstr "Англиканская школа Уитсанди"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "Why not try adding some ?"
msgstr "Почему бы не попробовать добавить немного?"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jth_wilkinson_plc
msgid "Wilkinson PLC"
msgstr "Уилкинсон ПЛС"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_han_william_light_r12_school
msgid "William Light R-12 School"
msgstr "Школа Уильяма Лайта R-12"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jod_wilson_ltd
msgid "Wilson Ltd"
msgstr "Уилсон Лтд"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fme_wodonga_primary_school
msgid "Wodonga Primary School"
msgstr "Начальная школа Уодонга"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_jep_woodend_primary_school
msgid "Woodend Primary School"
msgstr "Начальная школа Вуденд"

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_fpi_woodridge_state_school
msgid "Woodridge State School"
msgstr "Государственная школа Вудриджа"

#. module: hr_skills
#: model:hr.skill,name:hr_skills.hr_skill_wu_chinese
msgid "Wu Chinese"
msgstr ""

#. module: hr_skills
#: model:hr.resume.line,name:hr_skills.employee_resume_vad_wycheproof_p12_college
msgid "Wycheproof P-12 College"
msgstr "Колледж Wycheproof P-12"

#. module: hr_skills
#. odoo-javascript
#: code:addons/hr_skills/static/src/fields/skills_one2many/skills_one2many.xml:0
msgid "You can add skills from our library to the employee profile."
msgstr "Вы можете добавить навыки из нашей библиотеки в профиль сотрудника."

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "<EMAIL>"
msgstr "<EMAIL>"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.hr_employee_skill_type_view_form
msgid "e.g. Languages"
msgstr "например, языки"

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.resume_line_view_form
msgid "e.g. Odoo Inc."
msgstr "например, Odoo Inc."

#. module: hr_skills
#: model_terms:ir.ui.view,arch_db:hr_skills.report_employee_cv_sidepanel
msgid "www.demo.com"
msgstr "www.demo.com"
