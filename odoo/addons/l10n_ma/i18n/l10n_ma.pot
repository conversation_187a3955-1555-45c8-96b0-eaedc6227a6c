# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_ma
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.3alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-03-31 12:42+0000\n"
"PO-Revision-Date: 2023-03-31 12:42+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_d_1
msgid "1 - NON-FIXED ASSETS PURCHASES"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_10
msgid ""
"10 - Amount of the revenues realized, including non-taxable operations "
"(excl. VAT)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_100
msgid "100 - Banking and credit transactions and exchange commissions"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_101
msgid "101 - Transactions involving shares and units shares"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_102
msgid "102 - Other"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_103
msgid "103 - Operations of sale and delivery of works and objects of art"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_104
msgid "104 - Other"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_105
msgid ""
"105 - Operations of lawyers, interpreters, notaries, adouls, bailiffs and "
"veterinarians"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_106
msgid ""
"106 - Water delivered to the public distribution networks and sanitation "
"services"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_107
msgid "107 - Rental of water and electricity meters"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_108
msgid "108 - Petroleum gas and other gaseous hydrocarbons"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_109
msgid "109 - Petroleum or shale oils, crude or refined"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_110
msgid ""
"110 - Pharmaceutical products, raw materials and products used in their "
"composition as well as non-returnable packaging of these products and "
"materials"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_111
msgid "111 - School supplies, products and materials in their composition"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_112
msgid "112 - Ticket sales operations for museums, cinema and theater"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_113
msgid "113 - Refined or agglomerated sugar"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_114
msgid "114 - Canned sardines"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_115
msgid "115 - Milk powder"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_116
msgid "116 - Household soap"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_117
msgid ""
"117 - Economy car and products and materials used in its in its manufacture"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_118
msgid "118 - Other"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_119
msgid "119 - Other"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_120
msgid ""
"120 - Reversal of VAT in different ways (cessation, regularization, ...)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_121
msgid ""
"121 - Withholding tax on the amount of commissions allocated by insurance "
"companies to their brokers"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_122
msgid "122 - Withholding tax on interest paid by credit institutions"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_123
msgid "123 - Withholding tax on proceeds from securitization transactions"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_124
msgid ""
"124 - Withholding tax on transactions carried out by non-residents for the "
"benefit of Moroccan customers excluded from the scope of application of VAT"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_129
msgid "129 - Operations with non-resident taxpayers"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_130
msgid "130 - Total VAT Payable"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_140
msgid "140 - Services (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_141
msgid "141 - Transportation (14%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_142
msgid "142 - Banking operation (10%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_143
msgid ""
"143 - Hotels for travelers, and all real estate for tourism purposes (10%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_144
msgid ""
"144 - Operations performed by lawyers, interpreters, notaries, adouls, "
"bailiffs and veterinarians (10%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_145
msgid "145 - Import purchases (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_146
msgid "146 - Domestic purchases (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_147
msgid "147 - Import purchases (14%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_148
msgid "148 - Domestic purchases (14%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_149
msgid "149 - Import purchases (10%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_150
msgid "150 - Domestic purchases (10%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_151
msgid "151 - Import purchases (7%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_152
msgid "152 - Domestic purchases (7%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_153
msgid "153 - Other services (10%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_155
msgid "155 - Contract work (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_156
msgid "156 - Subcontracting (real estate work) (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_158
msgid "158 - Undisclosed VAT (Article 125 ter of the CGI)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_160
msgid "160 - Other fixed assets (10%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_162
msgid "162 - Import purchases (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_163
msgid "163 - Domestic purchases (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_164
msgid "164 - Self-supply other than constructions (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_165
msgid "165 - Facilities and installations (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_166
msgid "166 - Constructions (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_167
msgid "167 - Self-supply of constructions (20%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_168
msgid "168 - Other fixed assets (14%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_169
msgid "169 - Other fixed assets (7%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_170
msgid "170 - Credit from previous period"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_180
msgid "180 - Additional deduction of the pro-rata regularization"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_181
msgid "181 - Cumulative tax credit fraction"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_182
msgid "182 - Total deductions (lines 140 to 169)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_185
msgid ""
"185 - Amount of cancelled credit, remaining chargeable, not reimbursed for "
"investment property"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_186
msgid ""
"186 - Amount claimed as reimbursement for investment property (Article 103 "
"bis of the CGI)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_187
msgid "187 - Amount of reimbursement authorized (Article 103 of the CGI)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_190
msgid "190 - Total deductible VAT ((182 to 185) - (186 and 187))"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_d_2
msgid "2 - FIXED ASSETS"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_20
msgid "20 - Operations outside the scope of VAT"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_200
msgid "200 - Payable VAT (130 - 190)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_201
msgid "201 - Credit (190 - 130)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_202
msgid ""
"202 - 15% reduction of the credit for the period ((182 + 180) - 130) x 15%"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_203
msgid "203 - Credit to be carried forward (201 - 202)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_204
msgid ""
"204 - Credit with payment including VAT due under articles 115, 116 and 117 "
"of the CGI"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_30
msgid ""
"30 - Exempt operations without right of deduction (Article 91 of the CGI)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_40
msgid "40 - Exempt operations with right of deduction (Article 92 of the CGI)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_50
msgid ""
"50 - Operations carried out under suspension of VAT (Article 94 of the CGI)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_55
msgid ""
"55 - Taxable operations made by mobile payment (Article 247 ter of the CGI)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_56
msgid "56 - Exempt, out-of-scope or suspended operations by mobile payment"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_60
msgid ""
"60 - Taxable revenues to be allocated (difference line 10 - (20 + 30 + 40 + "
"50)) (excl. VAT)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_76
msgid "76 - Photovoltaic panels"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_77
msgid "77 - Solar water heaters"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_78
msgid ""
"78 - Feed for livestock and farmyard animals as well as oil cakes used in "
"their manufacture, excluding other simple feed"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_79
msgid "79 - Equipment for exclusive agricultural use"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_80
msgid "80 - Production and distribution operations"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_81
msgid "81 - Services"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_82
msgid ""
"82 - Liberal professions referred to in article 89-I-12°-(b) of the CGI"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_83
msgid "83 - Leasing operations"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_84
msgid ""
"84 - Butter, excluding home-made butter referred to in Article 91 (I-A. 2°)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_85
msgid ""
"85 - Wood in logs, debarked or simply squared, cork in its natural state, "
"firewood in bundles or sawn to short lengths and charcoal"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_86
msgid "86 - Fishing gear and nets for professional marine fishermen"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_87
msgid "87 - Operations of real estate works companies"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_88
msgid ""
"88 - Passenger and freight transport operations, excluding rail transport"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_89
msgid ""
"89 - Accommodation and catering operations as well as services provided by "
"cafés"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_90
msgid "90 - Electrical energy"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_91
msgid ""
"91 - Services rendered by any soliciting agent or insurance broker in "
"respect of contracts brought by him to an insurance company"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_92
msgid "92 - Rental operations of buildings used as hotels"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_93
msgid "93 - Sales and delivery of used movable property"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_94
msgid "94 - Self-supply of constructions"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_95
msgid ""
"95 - Margin regime referred to in articles 125 bis and 125 quater of the CGI"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_96
msgid "96 - Cooking salt (rock or sea salt)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_97
msgid "97 - Milled rice and pasta"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_98
msgid "98 - Food fluid oils excluding palm oil"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_line_99
msgid "99 - Securities transactions"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_a
msgid "A - Breakdown of total revenues"
msgstr ""

#. module: l10n_ma
#: model:ir.model,name:l10n_ma.model_account_chart_template
msgid "Account Chart Template"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_b
msgid "B - Breakdown of taxable revenues"
msgstr ""

#. module: l10n_ma
#: model:account.report.column,name:l10n_ma.tax_report_column_base
msgid "Base"
msgstr ""

#. module: l10n_ma
#: model:account.report.column,name:l10n_ma.tax_report_column_base_tax
msgid "Base Tax"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_c
msgid ""
"C - Operations carried out with non-resident taxpayers (Article 115 of the "
"CGI)"
msgstr ""

#. module: l10n_ma
#: model:ir.model.fields,field_description:l10n_ma.field_base_document_layout__company_details
msgid "Company Details"
msgstr ""

#. module: l10n_ma
#: model:ir.model,name:l10n_ma.model_base_document_layout
msgid "Company Document Layout"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_d
msgid "D - Breakdown of deductions"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_ded
msgid "Deduction adjustments"
msgstr ""

#. module: l10n_ma
#: model:ir.model.fields,help:l10n_ma.field_base_document_layout__company_details
msgid "Header text displayed at the top of all reports."
msgstr ""

#. module: l10n_ma
#: model_terms:ir.ui.view,arch_db:l10n_ma.view_company_form
#: model_terms:ir.ui.view,arch_db:l10n_ma.view_partner_property_form
msgid "ICE"
msgstr ""

#. module: l10n_ma
#: model_terms:ir.ui.view,arch_db:l10n_ma.report_invoice_document
msgid "ICE:"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_d_1_2
msgid "Other non-fixed assets purchases"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.l10n_ma_vat_d_prorata
msgid "Percentage of prorated deductions (ADC110)"
msgstr ""

#. module: l10n_ma
#: model:account.report.column,name:l10n_ma.tax_report_column_pro
msgid "Prorata (%)"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_b_10
msgid "Reduced rate of 10% with right to deduct"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_b_14
msgid "Reduced rate of 14%"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_b_7
msgid "Reduced rate of 7% with right to deduct"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_d_1_1
msgid "Services"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_b_20
msgid "Standard rate of 20% with right to deduct"
msgstr ""

#. module: l10n_ma
#: model:account.report.column,name:l10n_ma.tax_report_column_tax
msgid "Tax Due/Ded."
msgstr ""

#. module: l10n_ma
#: model:account.report,name:l10n_ma.tax_report_vat
msgid "VAT Report"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_results
msgid "VAT Results"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_b_14_d
msgid "With right to deduct"
msgstr ""

#. module: l10n_ma
#: model:account.report.line,name:l10n_ma.tax_report_part_b_14_nd
msgid "Without right to deduct"
msgstr ""

#. module: l10n_ma
#. odoo-python
#: code:addons/l10n_ma/models/res_partner.py:0
msgid "ICE number should have exactly 15 digits."
msgstr ""

#. module: l10n_ma
#. odoo-python
#: code:addons/l10n_ma/models/res_partner.py:0
msgid "ICE"
msgstr ""
