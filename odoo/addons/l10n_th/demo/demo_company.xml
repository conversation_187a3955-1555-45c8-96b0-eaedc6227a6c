<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="base.partner_demo_company_th" model="res.partner" forcecreate="1">
        <field name="name">TH Company</field>
        <field name="vat"/>
        <field name="street">A5</field>
        <field name="city">บ้านสันทราย</field>
        <field name="country_id" ref="base.th"/>

        <field name="zip">50220</field>
        <field name="phone">+66 81 234 5678</field>
        <field name="email"><EMAIL></field>
        <field name="website">www.thexample.com</field>
        <field name="is_company" eval="True"/>
    </record>

    <record id="base.demo_company_th" model="res.company" forcecreate="1">
        <field name="name">TH Company</field>
        <field name="partner_id" ref="base.partner_demo_company_th"/>
    </record>

    <function model="res.company" name="_onchange_country_id">
        <value eval="[ref('base.demo_company_th')]"/>
    </function>

    <function model="res.users" name="write">
        <value eval="[ref('base.user_root'), ref('base.user_admin'), ref('base.user_demo')]"/>
        <value eval="{'company_ids': [(4, ref('base.demo_company_th'))]}"/>
    </function>

    <function model="account.chart.template" name="try_loading">
        <value eval="[]"/>
        <value>th</value>
        <value model="res.company" eval="obj().env.ref('base.demo_company_th')"/>
        <value name="install_demo" eval="True"/>
    </function>
</odoo>
