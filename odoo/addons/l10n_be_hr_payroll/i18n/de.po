# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_hr_payroll
# 
# Translators:
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-01-22 08:43+0000\n"
"PO-Revision-Date: 2025-02-07 17:07+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Days"
msgstr "# Tage"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Hours"
msgstr "# Stunden"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "# Hours:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_children
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "# Insured Children < 19 y/o"
msgstr "# Versicherte Kinder ≤ 19 J"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_adults
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "# Insured Children >= 19 y/o"
msgstr "# Versicherte Kinder ≥ 19 J"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__months_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__months_count
msgid "# Months"
msgstr "# Monate"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_dependent_children_attachment
msgid "# dependent children for salary attachement"
msgstr "# Kinder zu Lasten für Gehaltspfändung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_disabled_juniors_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_disabled_juniors_dependent
msgid "# disabled people (<65)"
msgstr "# Personen mit Behinderung (<65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_disabled_senior_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_disabled_senior_dependent
msgid "# disabled seniors (>=65)"
msgstr "# Senioren mit Behinderung (≥65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_juniors_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_juniors_dependent
msgid "# people (<65)"
msgstr "# Personen (<65)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_senior_dependent
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_senior_dependent
msgid "# seniors (>=65)"
msgstr "# Senioren (≥65)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_line_view_form
msgid "#Months"
msgstr "#Monate"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "%(date_from)s to %(date_to)s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "%(employee)s - Part Time %(calendar)s"
msgstr "%(employee)s - Teilzeit %(calendar)s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_leave.py:0
msgid ""
"%(employee)s is in %(holiday_status)s. Fill in the appropriate eDRS here: "
"%(link)s"
msgstr ""
"%(employee)s befindet sich in %(holiday_status)s. Füllen Sie hier das "
"entsprechende eDRS aus: %(link)s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
msgid "%(employee_name)s-individual-account-%(year)s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
msgid "%(months)s months"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "%(reference)s %(quarter)s quarter %(year)s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
msgid "%(year)s-%(employee)s-281_10"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "%(year)s-%(employee)s-281_45"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
msgid "%(years)s years and %(months)s months"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
msgid "%s days"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_holidays_n
msgid "'Holiday Attest (Year N) - %s' % (object.employee_id.name)"
msgstr "'Urlaubsbescheinigung (Jahr N) - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_holidays_n1
msgid "'Holiday Attest (Year N-1) - %s' % (object.employee_id.name)"
msgstr "'Urlaubsbescheinigung (Jahr N-1) - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_bonus_month
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_light_payslip_be
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_payslip_be
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr "'Gehaltsabrechnung - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_termination_fees
msgid "'Termination - %s' % (object.employee_id.name)"
msgstr "'Beendigung - %s' % (object.employee_id.name)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "(Hours / Week)"
msgstr "(Stunden/Woche)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,print_report_name:l10n_be_hr_payroll.action_report_individual_account
msgid "(object._get_report_base_filename())"
msgstr "(object._get_report_base_filename())"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Amount"
msgstr "* Betrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* Compensation granted to insurance inspectors in reimbursement of costs"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Individual agreement available"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* Options granted by a foreign company not having an establishment in "
"Belgium"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* Social Security package"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* amount"
msgstr "* Anzahl"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* code"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* nature"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* number of days out of border area"
msgstr "* Anzahl der Tage außerhalb des Grenzgebiets"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "* percentage(s)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* specific remuneration paid in 2024 for specific services performed in "
"2023, 2022, 2021 and/or 2020"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"* total amount of all remuneration paid under a student employment contract"
msgstr ""
"* Gesamtbetrag aller im Rahmen eines Arbeitsvertrags für Studenten gezahlten"
" Vergütungen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_light_payslip
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "*10€ + 1*"
msgstr "*10€ + 1*"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "- Previous occupation for Double Holiday Pay Recovery in"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "- Simple Holiday Pay from previous employer to recover in"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
msgid ""
"- Without Income: The spouse of the income recipient has no professional income.\n"
"\n"
"- High income: The spouse of the recipient of the income has professional income, other than pensions, annuities or similar income, which exceeds %(low_income_threshold)s€ net per month.\n"
"\n"
"- Low Income: The spouse of the recipient of the income has professional income, other than pensions, annuities or similar income, which does not exceed %(low_income_threshold)s€ net per month.\n"
"\n"
"- Low Pensions: The spouse of the beneficiary of the income has professional income which consists exclusively of pensions, annuities or similar income and which does not exceed %(other_income_threshold)s€ net per month.\n"
"\n"
"- High Pensions: The spouse of the beneficiary of the income has professional income which consists exclusively of pensions, annuities or similar income and which exceeds %(other_income_threshold)s€ net per month."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- in the construction sector with registration system"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- lump sum reimbursements based on serious standards"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- lump sum reimbursements in the absence of serious standards"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- mobility allowance"
msgstr "- Mobilitätszuschuss"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "- reimbursements based on supporting documents"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
".\n"
"                    <span>Your future holidays won't be paid. So you are advised to keep this amount\n"
"                    until you take these days off.</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "0,87% of gross reference remuneration"
msgstr "0,87% der Brutto-Referenzvergütung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "0987"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off
msgid "1 day is 7 hours and 36 minutes"
msgstr "1 Arbeitstag besteht aus 7 Stunden und 36 Minuten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off_to_allocate
msgid ""
"1 day is the number of the amount of hours per day in the working schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"1) Amount which does not exceed the thresholds set in art. 269, § 1, 4°, CIR"
" 92:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1) Remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_company_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_company_id
msgid "10-digit code given by ONSS"
msgstr "10-stelliger Code von LSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "100000"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1001"
msgstr "1001"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1002"
msgstr "1002"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1003"
msgstr "1003"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1011"
msgstr "1011"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1012"
msgstr "1012"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1013"
msgstr "1013"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "102: Staff Costs:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "103: Benefits Above Salary:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "105"
msgstr "105"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "110"
msgstr "110"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "111"
msgstr "111"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "112"
msgstr "112"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "113"
msgstr "113"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__12mo
msgid "12 months +"
msgstr "12 Monate +"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "120"
msgstr "120"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1200"
msgstr "1200"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1201"
msgstr "1201"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1202"
msgstr "1202"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1203"
msgstr "1203"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "121"
msgstr "121"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1210"
msgstr "1210"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1211"
msgstr "1211"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1212"
msgstr "1212"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "1213"
msgstr "1213"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "130"
msgstr "130"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "132"
msgstr "132"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "133"
msgstr "133"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "134"
msgstr "134"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_thirteen_month
msgid "13th Month Slip"
msgstr "13. Monat"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "15"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "1500.00"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "1999"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__1
msgid "1st"
msgstr "1."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° - which count for the limit up to 180 hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Awarded in 2024"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Compensation, not mentioned in 2°, 3°, 4°"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "1° Ordinary remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"2) Amount that exceeds the thresholds set in art. 269, § 1, 4°, CIR 92:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) Overtime hours worked and paid between 01.07.2023 and 31.12.2023"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) Overtime hours worked and paid in 2024"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2) Overtime hours worked in 2022 and paid in 2024"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "2.00"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "20"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "20-03-2000"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "20.00"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "200.000"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "2022"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "2023"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "205"
msgstr "205"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "21-04-2001"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "210"
msgstr "210"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "211"
msgstr "211"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "212"
msgstr "212"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "213"
msgstr "213"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "233"
msgstr "233"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "2334"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "234"
msgstr "234"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "238"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "240"
msgstr "240"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "242"
msgstr "242"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "243"
msgstr "243"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "247"
msgstr "247"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "250"
msgstr "250"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "251"
msgstr "251"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "252"
msgstr "252"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "254"
msgstr "254"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "262"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "263"
msgstr "263"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "267"
msgstr "267"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "271"
msgstr "271"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "273"
msgstr "273"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
msgid "273S"
msgstr "273S"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_ip_273S
msgid "273S PDF"
msgstr "273S PDF"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_273S_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_273s
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_273S
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "273S Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_tree
msgid "273S Sheets"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "274"
msgstr "274"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_274_10
msgid "274.10 PDF"
msgstr "274.10 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__sheet_274_10
msgid "274.10 Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_274_XX_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_274_xx
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_274_XX
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_tree
msgid "274.XX Sheets"
msgstr "274.XX Blätter"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_274_xx_line
msgid "274.XX Sheets Line"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "275"
msgstr "275"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "276"
msgstr "276"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "277"
msgstr "277"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "278"
msgstr "278"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "279"
msgstr "279"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "280"
msgstr "280"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_action_view_tree
msgid "281.10 Forms"
msgstr "281.10 Formulare"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_281_10
msgid "281.10 PDF"
msgstr "281.10 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_l10n_be_281_10
msgid "281.10 Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "281.10 Sheet: Income"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_action_view_tree
msgid "281.45 Forms"
msgstr "281.45 Formulare"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_employee_281_45
msgid "281.45 PDF"
msgstr "281.45 PDF"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_l10n_be_281_45
msgid "281.45 Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "281.45 Sheet - Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "283"
msgstr "283"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "284"
msgstr "284"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "285"
msgstr "285"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "286"
msgstr "286"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "287"
msgstr "287"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "290"
msgstr "290"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "2b. Name and address of income debtor:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__2
msgid "2nd"
msgstr "2."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"2° - provided from 01.06.2024 and which are taken into consideration for the"
" limit up to 280 hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Actions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Arrears"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "2° Awarded before 2024"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__dmfa_employer_class
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__dmfa_employer_class
msgid "3-digit code given by ONSS"
msgstr "3-stelliger Code des LSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "305"
msgstr "305"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "308"
msgstr "308"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "310"
msgstr "310"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "311"
msgstr "311"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "312"
msgstr "312"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "313"
msgstr "313"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "317"
msgstr "317"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "32"
msgstr "32"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "32.00"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "33"
msgstr "33"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "335"
msgstr "335"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "336"
msgstr "336"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "337"
msgstr "337"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "338"
msgstr "338"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "34"
msgstr "34"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "340"
msgstr "340"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "341"
msgstr "341"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "342"
msgstr "342"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "343"
msgstr "343"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "3456"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "34565"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "34893"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "360"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "368"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "369"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "378"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "379"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "381"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "382"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "386"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "387"
msgstr "387"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "391"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "395"
msgstr "395"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "396"
msgstr "396"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "397"
msgstr "397"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "398"
msgstr "398"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "3b. Income recipient"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__3
msgid "3rd"
msgstr "3"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "3° Bonuses, premiums and stock options"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "3° which are taken into account for the limit up to 360 hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__quarter__4
msgid "4th"
msgstr "4. Platz"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "4° Benefits in kind"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "5 Hours/Week"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "5.00"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__55yo
msgid "55+ years old"
msgstr "Über 55 J"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "567877"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "57.75 %"
msgstr "57.75 %"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5801"
msgstr "5801"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5802"
msgstr "5802"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5803"
msgstr "5803"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58031"
msgstr "58031"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58032"
msgstr "58032"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58033"
msgstr "58033"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5811"
msgstr "5811"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5812"
msgstr "5812"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5813"
msgstr "5813"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58131"
msgstr "58131"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58132"
msgstr "58132"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "58133"
msgstr "58133"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5821"
msgstr "5821"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5822"
msgstr "5822"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5823"
msgstr "5823"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5831"
msgstr "5831"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5832"
msgstr "5832"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5833"
msgstr "5833"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5841"
msgstr "5841"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5842"
msgstr "5842"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5843"
msgstr "5843"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5851"
msgstr "5851"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5852"
msgstr "5852"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "5853"
msgstr "5853"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "6,8% of gross reference remuneration"
msgstr "6,8% der Brutto-Referenzvergütung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "6,8% of gross reference remuneration - additional time off amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "6. Amount of retained withholding tax:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "6000.00"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "66.81 %"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "6870"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "7,67% of gross reference remuneration"
msgstr "7,67% der Brutto-Referenzvergütung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"7,67% of gross reference remuneration * (time off not taken) / (right to "
"time off)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "789"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "8.00"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "865"
msgstr "865"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "870"
msgstr "870"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_registration_number
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_registration_number
msgid "9-digit code given by ONSS"
msgstr "9-stelliger Code von LSS"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "9876"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report_single_declaration
msgid "9998"
msgstr "9998"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_contract__l10n_be_impulsion_plan__25yo
msgid "< 25 years old"
msgstr "< 25 J"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Contributions Summary:</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Deductions Summary:</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Remunerations Summary:</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Services Summary:</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "<b>Student Contributions Summary:</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<span class=\"fw-bold\">2a. Company number: </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"<span class=\"fw-bold\">3a. Nature of beneficiary: </span>\n"
"                                    <span>Natural Person</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<span class=\"fw-bold\">3c. NISS: </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\" invisible=\"wage_type == 'hourly'\">%</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\"> / month</span>"
msgstr "<span class=\"ms-3\"> / Monat</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ month</span>"
msgstr "<span class=\"ms-3\">/Monat"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ worked day</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span class=\"ms-3\">/ year</span>"
msgstr "<span class=\"ms-3\">/Jahr</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"o_form_label\" groups=\"hr.group_hr_user\" invisible=\"not "
"transport_mode_private_car\">Distance home-work</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid ""
"<span class=\"o_form_label\" invisible=\"not "
"transport_mode_private_car\">Reimboursed amount</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "<span class=\"o_stat_text\">Eligible Employees</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "<span class=\"oe_inline\">From</span>"
msgstr "<span class=\"oe_inline\">Von</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "<span class=\"oe_inline\">To</span>"
msgstr "<span class=\"oe_inline\">Bis</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"<span class=\"text-start fw-bold\">4. Gross amount of income referred to in "
"art. 17, § 1, 3°, CIR 92, with regard to copyright and related "
"rights:</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"<span class=\"text-start fw-bold\">5. Gross amount of revenue from copyright"
" and related rights referred to in art. 17, § 1, 5°, CIR 92:</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">10. Taxable at the rate of"
" 33%:</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">11. Remuneration obtained "
"by athletes within the framework of their sporting activity:</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">12. Remuneration obtained "
"by sports competition referees for their refereeing services, or by "
"trainers, coaches and accompanying persons for their activity for the "
"benefit of sportsmen:</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span class=\"text-start text-uppercase fw-bold\">13. PC Privé</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">14. Allowances and "
"benefits for travel from home to work:</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span class=\"text-start text-uppercase fw-bold\">15. Impulse Fund</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">16. Deductions for "
"supplementary pensions</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">17. Remuneration for "
"overtime in the hospitality industry which qualifies for the "
"exemption</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">18. Overtime which gives "
"the right to extra pay</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">19. Remuneration which is "
"taken into account for the exemption for voluntary overtime</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">20. Purchasing power "
"premium which is taken into account for the exemption</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">21. Remuneration for a "
"flexi-job carried out by a non-pensioner which is taken into account for the"
" exemption</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">22. Allowances for "
"volunteer firefighters, volunteer ambulance drivers and volunteer civil "
"protection officers who are eligible for exemption</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">23. Withholding "
"Taxes</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">24. Special contributions "
"for social security</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">25. Public sector staff "
"without an employment contract</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">26. Employment "
"bonus</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">27. Miscellaneous "
"information</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">28. Remuneration and other"
" benefits received from a related foreign company</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">29. Foreign seasonal "
"worker in agriculture and horticulture subject to professional withholding "
"tax who provided a residence certificate</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">30. Special tax regimes "
"for inpatriate taxpayers and inpatriate researchers</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">31. Remuneration paid or "
"awarded for services carried out within the framework of association "
"activities after exceeding an hourly limit</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">6. REMUNERATION (other "
"than referred to under 10, 11a and 12a)</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">7. Miscellaneous taxable "
"income</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">8. Bad weather "
"stamps</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">9. Non-recurring benefits "
"linked to results</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Nature des revenus</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte Professionnel "
"Retenu</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Précompte professionnel "
"dû</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables "
"répondant aux conditions d’application de la dispense</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"<span class=\"text-start text-uppercase fw-bold\">Revenus imposables</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid ""
"<span invisible=\"notice_duration_month_before_2014 == 0\"> months and "
"</span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span> %</span>"
msgstr "<span> %</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span> included:</span>"
msgstr "<span>included:</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "<span> weeks</span>"
msgstr "<span>Wochen</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span> years old</span>"
msgstr "<span> Jahre alt</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "<span>%</span>"
msgstr "<span>%</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "<span>/ month</span>"
msgstr "<span>/Monat</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>/Holiday year </span>"
msgstr "<span>/Urlaubsjahr</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<span>Attendance (hours)</span>"
msgstr "<span>Anwesenheit (Stunden)</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>Holiday exercise </span>"
msgstr "<span>Ferienübung</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<span>Individual Account Report for year </span>"
msgstr "<span>Einzelkontobericht für das Jahr</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span>No</span>"
msgstr "<span>Nein</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>Remuneration for the period from </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>Social contributions on the various vacation pay have already been "
"paid.</span>"
msgstr ""
"<span>Die Sozialbeiträge für die verschiedenen Urlaubsgelder wurden bereits "
"gezahlt.</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid ""
"<span>The amount covered by this certificate pre-emptively compensates the vacation days you will take with\n"
"                        your next employer in </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>The amount covered by this certificate pre-emptively compensates the vacation days you will take with your next\n"
"                    employer in </span>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<span>Yes</span>"
msgstr "<span>Ja</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<span>You must return this certificate to your next employer, or failing that, to your allowance payment agency.\n"
"                    Social security contributions on holiday pay have already been retained.</span>"
msgstr ""
"<span>Sie müssen diese Bescheinigung an Ihren nächsten Arbeitgeber oder andernfalls an die für die Auszahlung der Vergütung zuständige Stelle zurückgeben. \n"
"Die Sozialversicherungsbeiträge für das Urlaubsgeld sind bereits einbehalten worden.</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "<span>days</span>"
msgstr "<span>Tage</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<span>included:</span>"
msgstr "<span>enthalten:</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<span>to </span>"
msgstr "<span>zu</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "<span>€ / month</span>"
msgstr "<span>€ / Monat</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "<span>€ / year</span>"
msgstr "<span>€ / Jahr</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "<span>€</span>"
msgstr "<span>€</span>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "<strong class=\"me-2\">Eco-Vouchers:</strong>"
msgstr "<strong class=\"me-2\">Betrag Ökoschecks</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid "<strong class=\"me-2\">Person in Charge:</strong>"
msgstr "<strong class=\"me-2\"> VerantwortlichePerson</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid ""
"<strong class=\"o_group_col_12\" invisible=\"not leave_type_id or found_leave_allocation\" style=\"color:#ff6600;\">\n"
"                        No time off allocation has been found for this time off type, no changes will occur to time off for this employee.\n"
"                    </strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong> Departure Date: </strong>"
msgstr "<strong> Austrittsdatum: </strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>1. Nr.</strong>"
msgstr "<strong>1. Nr.</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "<strong>1. Nᵒ:</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>2. Date of entry: </strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>3. Income debtor:</strong> <br/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>4. Beneficiary:</strong><br/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>4. Sender:</strong> <br/>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>5. National number:</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Address</strong>"
msgstr "<strong>Adresse</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Amount</strong>"
msgstr "<strong>Betrag</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Au:</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Authorized signature</strong>"
msgstr "<strong>Autorisierte Signatur</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Bank Account</strong>"
msgstr "<strong>Bankkonto</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "<strong>Code</strong>"
msgstr "<strong>Code</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Company Information</strong>"
msgstr "<strong>Unternehmensinformationen</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Designation</strong>"
msgstr "<strong>Stellenbezeichnung</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Du:</strong>"
msgstr "<strong>Du:</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Email</strong>"
msgstr "<strong>Email</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Employee</strong>"
msgstr "<strong>Mitarbeiter</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Identification No</strong>"
msgstr "<strong>Identifikationsnummer</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Job Position</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Montant</strong>"
msgstr "<strong></strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Name</strong>"
msgstr "<strong>Name</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Nr.</strong>"
msgstr "<strong>Nr.</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<strong>Pay holiday double complementary</strong>"
msgstr "<strong>Komplementäres doppeltes Urlaubsgeld</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
msgid "<strong>Pay holiday double</strong>"
msgstr "<strong>Doppeltes Urlaubsgeld</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid ""
"<strong>Pay holiday double</strong> only if the majority of vacation days\n"
"                        have not yet been taken"
msgstr ""
"<strong>Bezahlen Sie den doppelten Urlaub</strong> nur, wenn die Mehrheit der Urlaubstage\n"
"noch nicht genommen wurde"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Pay simple</strong>"
msgstr "<strong>Bezahlen Sie einfach</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Quantity</strong>"
msgstr "<strong>Menge</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Rate</strong>"
msgstr "<strong>Satz</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>Reference</strong>"
msgstr "<strong>Referenz</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Registration Number</strong>"
msgstr "<strong>Registrierungsnummer</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Salary Computation</strong>"
msgstr "<strong>Gehaltsberechnung</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "<strong>Société:</strong> <br/>"
msgstr "<strong>Société:</strong> <br/>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Start notice period</strong>"
msgstr "<strong>Start-Kündigungsfrist</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "<strong>TOTAL</strong>"
msgstr "<strong>GESAMT</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "<strong>Total</strong>"
msgstr "<strong>Gesamt</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Worked Days</strong>"
msgstr "<strong>Gearbeitete Tage</strong>"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "<strong>Worked Time</strong>"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_l10n_be_dmfa_location_unit__unique
msgid ""
"A DMFA location cannot be set more than once for the same company and "
"partner."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__has_laptop
msgid "A benefit in kind is paid when the employee uses its laptop at home."
msgstr ""
"Eine Sachleistung wird gezahlt, wenn der Mitarbeiter seinen Laptop zu Hause "
"benutzt."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "A. Total: (6a + 6b + 6c + 6d, 1° + 6d + 6e, 2° + 6e)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "APR"
msgstr "APR"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_advantage_any_kind
msgid "ATN"
msgstr "Atn"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_inverse_atn_warrant
msgid "ATN Warrant"
msgstr "ATN-Garantie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "AUG"
msgstr "AUG"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__absence_work_entry_type_id
msgid "Absence Work Entry Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Accident Insurance"
msgstr "Krankenhausversicherung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__accident_insurance_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__accident_insurance_name
msgid "Accident Insurance Name"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__accident_insurance_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__accident_insurance_number
msgid "Accident Insurance Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Accident insurance organization"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_ONSS
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_onss_employer
msgid "Accounting: ONSS (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_basic
msgid "Accounting: ONSS Basic (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_cpae
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_cpae
msgid "Accounting: ONSS CPAE (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_ffe
msgid "Accounting: ONSS FFE (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_special_ffe
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_special_ffe
msgid "Accounting: ONSS Special FFE (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_temporary_unemployment
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_temporary_unemployment
msgid "Accounting: ONSS Temporary Unemployment (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_onss_employer_wage_restreint
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_thirteen_month_onss_employer_wage_restreint
msgid "Accounting: ONSS Wage Restreint (Employer)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_owed_remuneration
msgid "Accounting: Owed Remuneration"
msgstr "Buchhaltung: ausstehende Vergütung"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_remuneration
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_remuneration
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_remuneration
msgid "Accounting: Remuneration"
msgstr "Buchhaltung: Vergütung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__actual_notice_duration
msgid "Actual Notice Duration"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Actual number of hours worked full time"
msgstr "Tatsächliche Zahl der geleisteten Arbeitsstunden in Vollzeit"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Actual number of hours worked part-time"
msgstr "Tatsächliche Zahl der geleisteten Arbeitsstunden in Teilzeit"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__wage_with_holidays
msgid ""
"Adapted salary, according to the sacrifices defined on the contract "
"(Example: Extra-legal time off, a percentage of the salary invested in a "
"group insurance, etc...)"
msgstr ""
"Angepasstes Gehalt, je nach den im Vertrag festgelegten Leistungen "
"(Beispiel: Zusätzliche Urlaubstage, ein Prozentsatz des Gehalts, der in eine"
" Gruppenversicherung investiert wird, usw...)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__2
msgid "Add"
msgstr "Hinzufügen"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_additional_gross
msgid "Additional Gross"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Additional Information"
msgstr "Weitere Informationen"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_additional_paid
msgid "Additional Time (Paid)"
msgstr "Zusätzliche Zeit (bezahlt)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_additional_leave
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_additional_leave
msgid "Additional Time Off"
msgstr "Zusätzliche Freizeit"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Additional amount to deduct"
msgstr "Zusätzlich abzuziehender Betrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Additional time off (european, ...)"
msgstr "Zusätzliche freie Zeit (europäisch, ...)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Address"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Administration générale de la Fiscalité"
msgstr "Abwesenheitsquote"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Adresse e-mail :"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_after_contract_public_holiday
msgid "After Contract Public Holidays"
msgstr "Gesetzliche Feiertage nach Vertragsabschluss"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__employee_age
msgid "Age of Employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__aggregation_level
msgid "Aggregation Level"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Aggregation Level:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__alloc_employee_ids
msgid "Alloc Employee"
msgstr "Alloc-Mitarbeiter"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__alloc_paid_leave_id
msgid "Alloc Paid Leave"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Allocation Time Off"
msgstr "Zuweisungszeit aus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_allocation_n_ids
msgid "Allocations N"
msgstr "Zulagen N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_onss_restructuring
msgid "Allow ONSS Reduction for Restructuring"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Ambulatory Insurance"
msgstr "Ambulante Versicherung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insurance_notes
msgid "Ambulatory Insurance: Additional Info"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_children
msgid "Ambulatory: # Insured Children < 19 y/o"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_adults
msgid "Ambulatory: # Insured Children >= 19 y/o"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_amount_per_adult
msgid "Ambulatory: Amount per Adult"
msgstr "Ambulant: Betrag pro Erwachsener"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_amount_per_child
msgid "Ambulatory: Amount per Child"
msgstr "Ambulant: Betrag pro Kind"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insurance_amount
msgid "Ambulatory: Insurance Amount"
msgstr "Ambulant: Versicherungsbetrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_spouse
msgid "Ambulatory: Insured Spouse"
msgstr "Ambulant: Versicherter Ehepartner"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_line_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Amount"
msgstr "Menge"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Amount of the employer's intervention"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n1
msgid ""
"Amount of the holiday pay paid by the previous employer already recovered."
msgstr ""
"Betrag des vom früheren Arbeitgeber gezahlten Urlaubsgeldes, der bereits "
"zurückgefordert wurde."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n1
msgid "Amount of the holiday pay paid by the previous employer to recover."
msgstr ""
"Betrag des vom früheren Arbeitgeber gezahlten Urlaubsgeldes, der "
"zurückzufordern ist."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__hospital_insurance_amount_per_adult
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Amount per Adult"
msgstr "Betrag pro Erwachsener"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__hospital_insurance_amount_per_child
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Amount per Child"
msgstr "Betrag pro Kind"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__meal_voucher_amount
msgid ""
"Amount the employee receives in the form of meal vouchers per worked day."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Amount to recover"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Amounts to Recover"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_annual_salary_revalued
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination
msgid "Annual salary revalued"
msgstr "Jahresgehalt neu bewertet"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_annual_variable_salary
msgid "Annual variable salary"
msgstr "Jährliches variables Gehalt"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Another reason"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Anticipated Holiday Pay Retenue"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__4
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__4
msgid "April"
msgstr "April"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_asignment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_asignment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_asignment_salary
msgid "Assignment of Salary"
msgstr "Lohnabtretung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "At the end of the exercise"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_attachment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_attachment_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_attachment_salary
msgid "Attachment of Salary"
msgstr "Anbringung des Gehalts"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__8
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__8
msgid "August"
msgstr "August"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of full-time workers"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of part-time workers"
msgstr "Durchschnittliche Anzahl von Teilzeitbeschäftigten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Average number of total workers or FTEs"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n
msgid "Average remuneration by month current year"
msgstr "Durchschnittliche Vergütung nach Monat des laufenden Jahres"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n1
msgid "Average remuneration by month previous year"
msgstr "Durchschnittliche Vergütung nach Monat Vorjahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_average_remunaration_n1
msgid "Average remuneration for the 12 months preceding unpaid leave"
msgstr "Durchschnittliche Vergütung für die 12 Monate vor unbezahltem Urlaub"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Bachelors"
msgstr "Bachelor"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_basic
msgid "Basic Complementary Double Holiday"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_basic_pay_simple
msgid "Basic Pay Simple"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_basic
msgid "Basic Salary"
msgstr "Grundgehalt"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_basic
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double_basic
msgid "Basic double"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.departure.reason,name:l10n_be_hr_payroll.departure_freelance
msgid "Became Freelance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Belgian Localization"
msgstr "Belgische Lokalisierung"

#. module: l10n_be_hr_payroll
#: model:ir.actions.server,name:l10n_be_hr_payroll.ir_cron_schedule_change_allocation_ir_actions_server
msgid "Belgian Payroll: Update time off allocations on schedule change"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_configuration
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_reporting_l10n_be
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Belgium"
msgstr "Belgien"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_social_balance_sheet
msgid "Belgium: Social Balance Sheet"
msgstr "Belgien: Sozialbilanz"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_social_security_certificate
msgid "Belgium: Social Security Certificate"
msgstr "Belgien: Sozialversicherungsausweis"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Beneficiary holdings"
msgstr "Begünstigte Betriebe"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__benefit_name
msgid "Benefit Name"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_company_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_company_car_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_company_car_annual
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_company_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_company_car_2
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__car_atn
msgid "Benefit in Kind (Company Car)"
msgstr "Sachleistung (Firmenwagen)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_internet
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_internet_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_internet
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_internet_2
msgid "Benefit in Kind (Internet)"
msgstr "Sachleistung (Internet)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_laptop
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_laptop_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_laptop
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_laptop_2
msgid "Benefit in Kind (Laptop)"
msgstr "Sachleistung (Laptop)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_mobile
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_mobile_2
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_mobile
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_atn_mobile_2
msgid "Benefit in Kind (Phone Subscription)"
msgstr "Sachleistung (Telefonabonnement)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_atn_deduction
msgid "Benefit in Kind Deductions (All)"
msgstr "Abzüge für Sachleistungen (Alle)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefit in kind deduction"
msgstr "Sachleistungsabzug"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Benefits"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Benefits In Kind (Company Car)"
msgstr "Sachleistung (Firmenwagen)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Benefits In Kind Deduction"
msgstr "Sachleistungsabzug"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Benefits In Kind Submitted To ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Benefits In Kind Without ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind (Company Car)"
msgstr "Sachleistungen  (Firmenwagen)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Benefits in kind and bonuses"
msgstr "Sachleistungen und Boni"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind submitted to ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Benefits in kind without ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_bicycle
msgid "Bicycle to work"
msgstr "Fahrrad zur Arbeit"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Boulevard de Waterloo"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_breast_feeding
msgid "Breastfeeding Break"
msgstr "Stillpause"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Business Closure Fund Cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Business closure fund cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "By"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "By Contract Type"
msgstr "Nach Vertragsart"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__department
msgid "By Department"
msgstr "Nach Abteilung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__employee
msgid "By Employee"
msgstr "Nach Mitarbeiter"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "By Gender"
msgstr "Nach Geschlecht"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By contract type"
msgstr "Nach Vertragsart"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By gender"
msgstr "Nach Geschlecht"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By professional category"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "By reason for termination of contract"
msgstr "Nach Grund für die Beendigung des Vertrags"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "By reason for termination of the contract"
msgstr "Nach Grund für die Beendigung des Vertrags"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cdd
msgid "CDD"
msgstr "CDD"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cdi
msgid "CDI"
msgstr "CDI"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_cip
msgid "CIP"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_job_view_form
msgid "CP200 Category"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_december_slip_wizard
msgid "CP200: December Slip Computation"
msgstr "CP200: Dezember Lohnzettelberechnung"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_line
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_line_wizard
msgid "CP200: Double Pay Recovery Line Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_double_pay_recovery_wizard
msgid "CP200: Double Pay Recovery Wizard"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid ""
"CSV format: you may edit it directly with your favorite spreadsheet "
"software, the rightmost column (value) contains the commission value over 3 "
"months. When you're done, reimport the file to generate the commission "
"payslips with the accurate commissions."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Cadre I. - Calcul du Précompte Mobilier (Pr.M) à payer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Cadre II. - Bénéficiaire(s) des revenus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Calculation Basis"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__3
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Cancel"
msgstr "Abbrechen"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_canteen
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_canteen_cost
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_l10n_be_canteen_cost
msgid "Canteen Cost"
msgstr "Canteen Cost"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Canteen Costs"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__capped_amount_34
msgid "Capped Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__car_atn
msgid "Car BIK"
msgstr "Auto Sachleistung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__a
msgid "Category A"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_job__l10n_be_scale_category
msgid ""
"Category A - Executive functions:\n"
"Included in this class are functions characterized by performing a limited number of simple and repetitive tasks. For example: the worker exclusively responsible for typing.\n"
"\n"
"Category B - Support functions.\n"
"Included in this class are functions characterized by making a contribution to the achievement of a larger mission. For example: the administrative employee or the receptionist.\n"
"\n"
"Category C - Management functions.\n"
"Included in this class are functions characterized by carrying out a complete set of tasks which, together, constitute one and the same mission. For example: the personnel administration employee or the PC technician.\n"
"\n"
"Category D - Advisory functions.\n"
"Included in this class are functions characterized by monitoring and developing the same professional process within the framework of a specific objective. For example: the programmer, accountant or consultant"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__b
msgid "Category B"
msgstr "Kategorie B"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__c
msgid "Category C"
msgstr "Kategorie C"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_job__l10n_be_scale_category__d
msgid "Category D"
msgstr "Kategorie D"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_certificate_certificate
msgid "Certificate"
msgstr "Zertifikat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__certificate
msgid "Certificate Level"
msgstr "Zertifikatsebene"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Certificate of Holidays"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_certificate_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_certificate_id
msgid "Certificate to allow access to batch declarations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_wizard_action
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_employee_lang_wizard
msgid "Change Employee Language"
msgstr ""
"Mitarbeitersprache ändern\n"
" "

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_employee_lang_wizard_line
msgid "Change Employee Language Line"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_hr_payroll_schedule_change_wizard
msgid "Change contract working schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Checkout"
msgstr "Kassiervorgang"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_child_alw
msgid "Child Allowance Belgium"
msgstr "Kindergeld Belgien"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_child_support
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_child_support
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_child_support
msgid "Child Support"
msgstr "Kindergeld"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Circ. administrative 19.03.1982 Ci. RH. 241/315.785"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__t
msgid "Circuit Test (T)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Close"
msgstr "Schließen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__code
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Code"
msgstr "Code"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_fixed_commission
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__commission_on_target
msgid "Commission"
msgstr "Provision"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__commission_amount
msgid "Commission Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_commission_on_target
msgid "Commission on Target"
msgstr "Kommission zu Ziel"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.salary_rule_category_commissions
msgid "Commissions"
msgstr "Provisionen"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.salary_rule_category_commissions_adjustment
msgid "Commissions Adjustment"
msgstr "Provisionsanpassung"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_company
msgid "Companies"
msgstr "Unternehmen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__company_id
msgid "Company"
msgstr "Unternehmen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__company_car_total_depreciated_cost
msgid "Company Car Total Depreciated Cost"
msgstr "Firmenauto: ingesamt abgeschriebene Kosten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Company Information"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_company_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_company_number
msgid "Company Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Company number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_already_paid
msgid "Complementary Double Holidays (Already Paid)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary_december
msgid "Complementary Double Holidays (Lost due to working time reduction)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_view_form_inherit_double_pay
msgid "Compute December Holiday Pay"
msgstr "Berechnung des Urlaubsgeldes für Dezember"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_view_form_inherit_double_pay
msgid "Compute Double Pay Recovery"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurationseinstellungen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure Default Values for Belgian Benefits"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure ONSS codes"
msgstr "LSS-Codes konfigurieren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Configure your company accident insurance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
msgid "Confirm"
msgstr "Bestätigen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_children
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_children
msgid "Considered number of dependent children"
msgstr "Berücksichtigte Anzahl unterhaltsberechtigter Kinder"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_juniors
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_juniors
msgid "Considered number of dependent juniors"
msgstr "Berücksichtigte Anzahl der abhängigen Junioren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__dependent_seniors
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__dependent_seniors
msgid "Considered number of dependent seniors"
msgstr "Berücksichtigte Anzahl der unterhaltsberechtigten Senioren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__contract_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__contract_id
msgid "Contract"
msgstr "Vertrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__contract_next_year_id
msgid "Contract Active Next Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Contract Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract_employee_report
msgid "Contract and Employee Analysis Report"
msgstr "Vertrags- und Mitarbeiteranalysebericht"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Contract for the execution of a clearly defined work"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract_history
msgid "Contract history"
msgstr "Vertragshistorie"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Contribution Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Contribution:"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Contributions paid and payments to collective funds"
msgstr "Gezahlte Beiträge und Zahlungen an kollektive Fonds"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_corona
msgid "Corona Unemployment"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
msgid ""
"Count of dependent people/children or disabled dependent people/children "
"must be positive."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
msgid ""
"Count of disabled dependent people/children must be less or equal to the "
"number of dependent people/children."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Create 274.XX Sheets"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_281_10_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid "Create 281.10 Form"
msgstr "Erstellen des Formulars 281.10"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_281_45_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Create 281.45 Form"
msgstr "Erstellen von 281.45 Formular"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Create XML"
msgstr "Xml erstellen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Create new contract and adapt time off allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__create_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__create_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__create_date
msgid "Created on"
msgstr "Erstellt auf"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_credit_time
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_credit_time
msgid "Credit Time"
msgstr "Kreditzeit"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "Credit time contract"
msgstr "Kreditzeitvertrag"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__currency_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__currency_id
msgid "Currency"
msgstr "Währung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Currency:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__months_count_description
msgid "Current Occupation Duration (Description)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__months_count
msgid "Current Occupation Duration (Months)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__current_resource_calendar_id
msgid "Current Resource Calendar"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__resource_calendar_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_resource_calendar_id
msgid "Current Working Schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "Current contract is finished before the end of the new contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Current work regime"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Cycle Allowance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_cycle_transportation
msgid "Cycle Transportation (Days Count)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "DATE D’ATTRIBUTION OU DE MISE EN PAIEMENT DES REVENUS :"
msgstr "DATE D’ATTRIBUTION OU DE MISE EN PAIEMENT DES REVENUS :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "DEC"
msgstr "Dec"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "DECLARATION AU PRECOMPTE MOBILIER (Pr.M)"
msgstr "DECLARATION AU PRECOMPTE MOBILIER (Pr.M)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.hr_payslip_report_action_dmfa
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_payroll_dmfa
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "DMFA"
msgstr "Dmfa"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__dmfa_employer_class
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__dmfa_employer_class
msgid "DMFA Employer Class"
msgstr "DMFA Arbeitgeberklasse"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_tree
msgid "DMFA Reports"
msgstr "DMFA-Berichte"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__dmfa_code
msgid "DMFA code"
msgstr "DMFA-Code"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_dmfa
msgid "DMFA xml report"
msgstr "DMFA-XML-Bericht"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_dmfa_location_unit
msgid "DMFA: Work Locations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Data for:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_search
msgid "Date"
msgstr "Datum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__date_end
msgid "Date End"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__date_start
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__date_from
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__date_from
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__date_from
msgid "Date From"
msgstr "Datum von"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__date_start
msgid "Date Start"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__date_end
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__date_to
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__date_to
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__date_to
msgid "Date To"
msgstr "Datum bis"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Date de réception de la déclaration :"
msgstr "Date de réception de la déclaration :"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Date of birth"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__day
msgid "Day"
msgstr "Tag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__private_car_missing_days
msgid "Days Not Granting Private Car Reimbursement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__representation_fees_missing_days
msgid "Days Not Granting Representation Fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Days Per Week:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n
msgid "Days Unpaid time off current year"
msgstr "Tage Unbezahlte Abwesenheit im laufenden Jahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n1
msgid "Days Unpaid time off previous year"
msgstr "Tage Unbezahlte Abwesenheit im Vorjahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__12
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__12
msgid "December"
msgstr "Dezember"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "December Pay"
msgstr "Lohn Dezember"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "December Slip"
msgstr "Gehaltszettel Dezember"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_respect
msgid ""
"Decides whether the employee will still work during his notice period or "
"not."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__s
msgid "Declaration Test (S)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__declaration_type
msgid "Declaration Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__line_ids
msgid "Declarations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_32
msgid "Deducted Amount 32"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_33
msgid "Deducted Amount 33"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount_34
msgid "Deducted Amount 34"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_deduction
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_deduction
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_deduction
msgid "Deduction"
msgstr "Abzüge"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__presence_work_entry_type_id
msgid "Default Work Entry Type"
msgstr "Standard-Arbeitseintragstyp"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__company_calendar
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__company_calendar
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__company_calendar
msgid "Default Working Hours"
msgstr "Standardarbeitszeiten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Demo Name"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__department_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__department_id
msgid "Department"
msgstr "Abteilung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_date
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Departure Date"
msgstr "Austrittsdatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_description
msgid "Departure Description"
msgstr "Austritt Beschreibung"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_departure_reason
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__leaving_type_id
msgid "Departure Reason"
msgstr "Grund für den Austritt"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Departure date"
msgstr "Austrittsdatum"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.departure_holiday_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_holiday_attests
msgid "Departure: Holiday Attests"
msgstr "Austritt: Urlaubsbescheinigung"

#. module: l10n_be_hr_payroll
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_hr_payroll_notice
msgid "Departure: Notice period"
msgstr "Austritt: Kündigungsfrist"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.departure_notice_wizard_action
msgid "Departure: Notice period and payslip"
msgstr "Austritt: Kündigungsfrist und Lohnabrechnung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Departures"
msgstr "Austritte"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__name
msgid "Description"
msgstr "Beschreibung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled
msgid "Disabled"
msgstr "Deaktiviert"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_children_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_children_bool
msgid "Disabled Children"
msgstr "Kinder mit Behinderung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_spouse_bool
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_spouse_bool
msgid "Disabled Spouse"
msgstr "Behinderter Ehepartner"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Discard"
msgstr "Verwerfen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Dismissal"
msgstr "Kündigung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Dismissal Date"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_job__display_l10n_be_scale
msgid "Display L10N Be Scale"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__display_name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_dmfa
msgid "DmfA"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "DmfA Declaration ("
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_go_filename
msgid "Dmfa Go Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_pdf_filename
msgid "Dmfa Pdf Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_signature_filename
msgid "Dmfa Signature Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_xml_filename
msgid "Dmfa Xml Filename"
msgstr "Dmfa Xml-Dateiname"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Doctors / Civil Engineers"
msgstr "Ärzte / Bauingenieure"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid ""
"Domicile, siège social ou siège du principal établissement administratif "
"(adresse complète) :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state_xlsx__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state_xlsx__done
msgid "Done"
msgstr "Erledigt"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_double_december_category
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_december_pay
msgid "Double December Pay"
msgstr "Doppeltes Gehalt Dezember"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_basic
msgid "Double December Pay Basic"
msgstr "Doppeltes Dezembergehalt Basic"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_gross
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_double_december_category_gross
msgid "Double December Pay Gross"
msgstr "Doppeltes Dezembergehalt Brutto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_net
msgid "Double December Pay Net"
msgstr "Doppeltes Dezembergehalt Netto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_pp
msgid "Double December Pay Withholding Tax"
msgstr "Doppeltes Dezembergehalt Quellensteuer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Double Holiday"
msgstr "Doppelter Urlaub"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_double_already_paid
msgid "Double Holiday (Already Paid)"
msgstr "Doppelter Urlaub (bereits gezahlt)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Double Holiday Gross"
msgstr "Doppelter Urlaub Brutto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_dp
msgid "Double Holiday Pay"
msgstr "Doppeltes Urlaubsgeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_double_pay_december
msgid "Double Holiday Pay (Lost due to working time reduction)"
msgstr "Doppeltes Urlaubsgeld (Verlust durch Arbeitszeitverkürzung)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Double Holiday Pay Global Amount:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Double Holiday Pay Global Contributions:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_holiday_n
msgid "Double Holiday Pay N"
msgstr "Doppeltes Urlaubsgeld N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__double_holiday_n1
msgid "Double Holiday Pay N-1"
msgstr "Doppeltes Urlaubsgeld N-1"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_recovery
msgid "Double Holiday Pay Recovery"
msgstr "Doppeltes Urlaubsgeld Rückzahlung"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_double_holiday
msgid "Double Holidays Slip"
msgstr "Double Holidays Slip"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_december_slip_wizard_action
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_action
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Double Pay Recovery Computation"
msgstr "Berechnung Rückforderung doppelter Löhne"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__double_pay_to_recover
msgid "Double Pay To Recover"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "Download the 273S PDF file:"
msgstr "273S PDF-Datei herunterladen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the 274.XX PDF file:"
msgstr "274.XX PDF-Datei herunterladen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
msgid "Download the 281.10 XML file:"
msgstr "XML-Datei 281.10 herunterladen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
msgid "Download the 281.45 XML file:"
msgstr "281.45 XML-Datei herunterladen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Download the Social Balance Sheet PDF file:"
msgstr "PDF-Datei der Sozialbilanz herunterladen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Download the Social Balance Sheet XLSX file:"
msgstr "XLSX-Datei der Sozialbilanz herunterladen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Download the Social Security Certificate PDF file:"
msgstr "PDF-Datei der Sozialversicherungsbescheinigung herunterladen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Download the Social Security Certificate XLSX file:"
msgstr "XLSX-Datei der Sozialversicherungsbescheinigung herunterladen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the XLSX details file:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Download the XML Export file:"
msgstr "XML-Exportdatei herunterladen:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_balance_sheet__state_xlsx__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state__draft
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__state_xlsx__draft
msgid "Draft"
msgstr "Entwurf"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "During the exercise"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Early Holiday Pay"
msgstr "Vorzeitiges Urlaubsgeld"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Early holiday pay"
msgstr "Vorzeitiges Urlaubsgeld"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_eco_checks
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__eco_checks
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_eco_checks
msgid "Eco Vouchers"
msgstr "Ökoschecks"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_eco_vouchers_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid "Eco-Vouchers"
msgstr "Ökoschecks"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_eco_vouchers_line_wizard
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_eco_vouchers_wizard
msgid "Eco-Vouchers Wizard"
msgstr "Ökoscheck-Assistent"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_light_payslip
msgid "Eco-Vouchers:"
msgstr "Ökoschecks"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_economic_unemployment
msgid "Economic Unemployment"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
msgid "Ecovouchers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_training_time_off
msgid "Educational Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__effective_date
msgid "Effective Date"
msgstr "Effektives Datum"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Eghezee"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_employee
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__employee_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__employee_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "Employee"
msgstr "Mitarbeiter"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Mitarbeitervertrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Employee Departure"
msgstr "Mitarbeiter-Abreise"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Employee Departure - Holiday Attests"
msgstr "Mitarbeiterabreise - Urlaubs-Attest"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Employee Information"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Employee Name"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_reimbursement
msgid "Employee Reimbursement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__without
msgid "Employee doesn't work during his notice period"
msgstr "Mitarbeiter geht vor Kündigungsfrist"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_sick_more_than_30_days
msgid "Employee on Mutual Health (> 30 days Illness)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__with
msgid "Employee works during his notice period"
msgstr "Mitarbeiter geht nach Kündigungsfrist"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__notice_respect__partial
msgid "Employee works partially during his notice period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__wage
msgid "Employee's monthly gross wage for the new contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__resource_calendar_id
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_resource_calendar_id
msgid ""
"Employee's working schedule.\n"
"        When left empty, the employee is considered to have a fully flexible schedule, allowing them to work without any time limit, anytime of the week.\n"
"        "
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employee:"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__employee_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Employees"
msgstr "Personal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employees Information"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_invalid_gender
msgid "Employees With Invalid Configured Gender"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_invalid_lang
msgid "Employees With Invalid Configured Language"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_invalid_niss
msgid "Employees With Invalid NISS Numbers"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Employees:"
msgstr "Personal:"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Employer Class:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Employer Information"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Employer contribution to the Fund"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Employer contribution to the fund"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Employer details"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__res_company__l10n_be_ffe_employer_type__commercial
msgid "Employers with industrial or commercial purposes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__res_company__l10n_be_ffe_employer_type__non_commercial
msgid "Employers without industrial or commercial purposes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_employment_bonus_employees
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_employment_bonus
msgid "Employment Bonus"
msgstr "Beschäftigungsbonus"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_employment_bonus_A
msgid "Employment Bonus (Volet A)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_employment_bonus_volet_A
msgid "Employment Bonus (Volet A: 33.14%)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_employment_bonus_B
msgid "Employment Bonus (Volet B)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_employment_bonus_volet_B
msgid "Employment Bonus (Volet B: 52.54%)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
msgid ""
"Employment bonus: Volet A (%(bonus_volet_A)s €) + Volet B (%(bonus_volet_B)s"
" €) not equal to total (%(total_employment_bonus)s) for payslip "
"(id=%(payslip_id)s) of employee %(employee_name)s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_end
msgid "End Date"
msgstr "Enddatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__end_notice_period
msgid "End Notice Period"
msgstr "Ende der Kündigungsfrist"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__date_end
msgid "End Period"
msgstr "Endperiode"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_end
msgid "End date of the new contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__end_notice_period
msgid "End notice period"
msgstr "Ende der Kündigungsfrist"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "End-of-year bonus, 13th month or other similar amount"
msgstr "Jahresendbonus, 13. Monat oder ein ähnlicher Betrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Entries"
msgstr "Buchungen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Entry date"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__error_message
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__error_message
msgid "Error Message"
msgstr "Fehlermeldung"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
msgid "Error while importing file"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Established on"
msgstr "Gegründet am"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Established on:"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_european_leaves_deduction
msgid "European Leaves Deduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_european
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_european
msgid "European Time Off"
msgstr "Europäische Abwesenheit"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Example Day"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Example Hour"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__deducted_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Exempted Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_expatriate
msgid "Expatriate Allowance"
msgstr "Expatriate-Zulage"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__representation_fees
msgid "Expense Fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Export"
msgstr "Export"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Export Complete"
msgstr "Export abgeschlossen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
msgid "Export PDF file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Export XLS"
msgstr "XLS exportieren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Export XLSX details"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Export XML file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__export
msgid "Export the employees file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Export to PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Export to XLSX"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_extra_legal
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_extra_legal
msgid "Extra Legal Time Off"
msgstr "Zusätzliche gesetzliche Abwesenheit"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "FEB"
msgstr "FEB"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report
msgid "FINPROF"
msgstr "FINPROF"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__2
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__2
msgid "February"
msgstr "Februar"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Female"
msgstr "Weiblich"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_ffe_employer_type
msgid "Ffe Employer Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.10: Précompte professionnel versé."
msgstr "Fiche 274.10: Précompte professionnel versé."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid ""
"Fiche 274.32: Dispense de précompte professionnel pour doctorants/ingénieurs"
" civils."
msgstr ""
"Fiche 274.32: Dispense de précompte professionnel pour doctorants/ingénieurs"
" civils."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.33: Dispense de précompte professionnel pour master."
msgstr "Fiche 274.33: Dispense de précompte professionnel pour master."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_274_10
msgid "Fiche 274.34: Dispense de précompte professionnel pour bacheliers."
msgstr "Fiche 274.34: Dispense de précompte professionnel pour bacheliers."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "Field Year does not seem to be a year. It must be an integer."
msgstr "Das Feldjahr scheint kein Jahr zu sein. Es muss eine ganze Zahl sein."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__file_type
msgid "File Type"
msgstr "Dateiformat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_year
msgid "First Contract Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_year_n
msgid "First Contract Year N"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_year_n_plus_1
msgid "First Contract Year N Plus 1"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_year_n1
msgid "First Contract Year N1"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__first_contract_in_company
msgid "First contract in company"
msgstr "Erster Vertrag im Unternehmen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__first_contract
msgid "First contract start date."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__start_notice_period
msgid ""
"First monday from the departure date (or the following open day if it is a "
"public holiday)."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Fiscal Voluntarism"
msgstr "Fiskalvoluntarismus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__fiscal_voluntary_rate
msgid "Fiscal Voluntary Rate"
msgstr "Freiwilliger Fiskalsatz"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Fixed and variable remuneration"
msgstr "Feste und variable Vergütung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Fixed term contract (CDD)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Fixed-term contract (CDD)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_flemish_training_time_off
msgid "Flemish Educational Time Off"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "For The Period"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Foreign Expenses"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Formal continuous trainings at the employer's expense"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__found_leave_allocation
msgid "Found Leave Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Frais déductibles"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Frais déduits"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Frequency (Month)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "From"
msgstr "Von"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__fuel_card
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_employee_report__fuel_card
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_fuel_card
msgid "Fuel Card"
msgstr "Tankkarte"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Full Time"
msgstr "Vollzeit"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_employee_report__fte
msgid "Full Time Equivalent (Today)"
msgstr "Vollzeitäquivalent (heute)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_wage
msgid "Full Time Equivalent Wage"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_time_off_allocation
msgid "Full Time Off Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__full_resource_calendar_id
msgid "Full Working Schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Function held"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Fund or company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Gender"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Generate PDF report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid "Generate Payslips"
msgstr "Lohnabrechnungen erzeugen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
msgid "Generate Time Off Allocations"
msgstr "Generieren von Zeit-Off-Zuweisungen"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.action_hr_payroll_generate_warrant_payslips
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_generate_warrant_payslips
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payslip_run_view_tree
msgid "Generate Warrant Payslips"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_generate_warrant_payslips_line
msgid "Generate Warrant Payslips Lines"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Generate XML report"
msgstr "XML-Bericht generieren"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Lohnabrechnungen für alle ausgewählten Mitarbeiter generieren"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_273S_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Generation Complete"
msgstr "Generation Complete"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_273s_274_273s
msgid "Get 273S report as PDF."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_273s_274_274_10
msgid "Get 274.10 report as PDF."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_dmfa_pdf_report
msgid "Get DmfA declaration as PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_social_balance
msgid "Get Social Balance Sheet as PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_report_l10n_be_hr_payroll_report_social_security_certificate
msgid "Get Social Security Certificate as PDF"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Gift In Kind"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Gifts in kind"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Global Information"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_go
msgid "Go file"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Grants and other financial benefits received (to be deducted)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_salary
msgid "Gross"
msgstr "Brutto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__net_n
msgid "Gross Annual Remuneration Current Year"
msgstr "Bruttojahresvergütung im laufenden Jahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__net_n1
msgid "Gross Annual Remuneration Previous Year"
msgstr "Bruttojahresvergütung Vorjahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__salary_december_2013
msgid "Gross Annual Salary as of December 31, 2013"
msgstr "Bruttojahresgehalt zum 31. Dezember 2013"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_salary
msgid "Gross Double December Pay Salary"
msgstr "Bruttodoppelbezüge Gehalt Dezember"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_gross_salary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_gross_salary
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__gross_salary
msgid "Gross Salary"
msgstr "Bruttogehalt"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Gross Salary Before ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_basic_12_92
msgid "Gross Yearly Salary"
msgstr "Bruttomonatsgehalt"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__salary_december_2013__inferior
msgid "Gross annual salary < 32.254 €"
msgstr "Bruttojahresgehalt < 32.254 €"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payslip_employee_depature_notice__salary_december_2013__superior
msgid "Gross annual salary > 32.254 €"
msgstr "Bruttojahresgehalt > 32.254 €"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Gross cost directly linked to training"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Gross reference remuneration"
msgstr "Bruttoreferenzierungsvergütung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Gross salary before ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Gross wage"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_group_insurance
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_group_insurance
msgid "Group Insurance (Employer)"
msgstr "Gruppenversicherung (Arbeitgeber)"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_group_insurance_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_group_insurance_wizard_view_form
msgid "Group Insurance Export"
msgstr "Gruppenversicherung Export"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Group Insurance Global Contributions:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_rate
msgid "Group Insurance Sacrifice Rate"
msgstr "Gruppenversicherung Verzichtssatz"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_group_insurance_wizard
msgid "Group Insurance Wizard"
msgstr "Gruppenversicherungs-Assistent"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_group_insurance_line_wizard
msgid "Group Insurance Wizard Line"
msgstr "Gruppenversicherung Assistentenzeile"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_individual_account
msgid "HR Individual Account Report By Employee"
msgstr "HR-Einzelkontobericht nach Mitarbeiter"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_10
msgid "HR Payroll 281.10 Wizard"
msgstr "HR-Lohnabrechnung 281.10 Assistent"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_281_45
msgid "HR Payroll 281.45 Wizard"
msgstr "HR-Lohnabrechnung 281.45 Assistent"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_work_entry
msgid "HR Work Entry"
msgstr "HR-Arbeitseintrag"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_work_entry_type
msgid "HR Work Entry Type"
msgstr "HR-Arbeitspostentyp"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_has_ambulatory_insurance
msgid "Has Ambulatory Insurance"
msgstr "Hat Ambulante Versicherung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_hospital_insurance
msgid "Has Hospital Insurance"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__has_valid_schedule_change_contract
msgid "Has Valid Schedule Change Contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_hiring_bonus
msgid "Hiring Bonus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Holiday Attest"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n_holidays
msgid "Holiday Pay (N Year)"
msgstr "Urlaubsgeld (Jahr n)"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_departure_n1_holidays
msgid "Holiday Pay (N-1 Year)"
msgstr "Urlaubsgeld (Jahr n-1)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday Pay Provision"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_holiday_pay_recovery_n
msgid "Holiday Pay Recovery (Year N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_holiday_pay_recovery_n1
msgid "Holiday Pay Recovery (Year N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Holiday Pay Supplement"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday Pay Supplement Retenue"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__amount
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__amount
msgid "Holiday pay amount on the holiday attest from the previous employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Holiday pay details:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Holiday pay supplement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__distance_home_work
msgid "Home-Work Distance"
msgstr "Home-Work-Distanz"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__km_home_work
msgid "Home-Work Distance in Km"
msgstr "Home-Work-Distanz in Km"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__distance_home_work_unit
msgid "Home-Work Distance unit"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_residence
msgid "Home/Residence Allowance"
msgstr "Wohn-/Aufenthaltsbeihilfe"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Hospital Insurance"
msgstr "Krankenhausversicherung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__hospital_insurance_amount_adult
msgid "Hospital Insurance Amount per Adult"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__hospital_insurance_amount_child
msgid "Hospital Insurance Amount per Child"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_hospital_insurance_notes
msgid "Hospital Insurance: Additional Info"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_work_entry_daily_benefit_report__id
msgid "ID"
msgstr "Id"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "IDENTIFICATION DU REDEVABLE"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip_wage_rate
msgid "IP percentage"
msgstr "IP-Prozentsatz"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_ip_part
msgid "IP. Part."
msgstr "Ip. Teil."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Identification Of The Company And Infos"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Identification du bénéficiaire"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Identification of the company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__previous_contract_creation
msgid ""
"If checked, the wizard will create another contract after the new working "
"schedule contract, with current working schedule"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_dependent_people
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_dependent_people
msgid "If other people are dependent on the employee"
msgstr "Wenn andere Personen vom Mitarbeiter abhängig sind"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled
msgid "If the employee is declared disabled by law"
msgstr "Wird der Mitarbeiter gesetzlich für behindert erklärt"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "Import Complete"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__import_file
msgid "Import File"
msgstr "Datei importieren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_payroll_generate_warrant_payslips__state__import
msgid "Import the employee file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_impulsion_plan
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Impulsion Plan"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__first_contract
msgid "In the Company Since"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__occupation_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__occupation_rate
msgid "Included between 0 and 100%"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_individual_account
msgid "Individual Account"
msgstr "Individuelles Konto"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_individual_account_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_individual_account
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "Individual Accounts"
msgstr "Individuelle Konten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_individual_account.py:0
msgid "Individual Accounts - Year %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Informal continuous trainings at the employer's expense"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Information on training for workers during the exercise"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__initial_time_off_allocation
msgid "Initial Time Off Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Initial trainings at the employer's expense"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insurance_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Insurance Amount"
msgstr "Versicherungsbetrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_adults_total
msgid "Insured Relative Adults Total"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__insured_relative_spouse
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Insured Spouse"
msgstr "Versicherter Ehepartner"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Insurer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip_part
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip
msgid "Intellectual Property"
msgstr "Geistiges Eigentum"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ip_deduction
msgid "Intellectual Property Income Deduction"
msgstr "Einkommensabzug für geistiges Eigentum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_internet
msgid "Internet"
msgstr "Internet"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__internet
msgid "Internet Subscription"
msgstr "Internet-Abo"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__invalid
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__invalid
msgid "Invalid"
msgstr "Ungültig"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_273S.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"Invalid NISS number for those employees:\n"
" %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__ip_value
msgid "Ip Value"
msgstr "Ip-Wert"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_is_below_scale
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__l10n_be_is_below_scale
msgid "Is below CP200 salary scale"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__is_test
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__is_test
msgid "Is it a test?"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JAN"
msgstr "JAN"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JUL"
msgstr "JUL"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "JUN"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__1
msgid "January"
msgstr "Januar"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_job
msgid "Job Position"
msgstr "Beruf"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Joint Commission:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Joint committee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Joint committee for employees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__7
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__7
msgid "July"
msgstr "Juli"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__6
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__6
msgid "June"
msgstr "Juni"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__leave_right
msgid "Keep Time Off Right"
msgstr "Abwesenheit richtig halten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_ambulatory_insured_adults_total
msgid "L10N Be Ambulatory Insured Adults Total"
msgstr "L10N Ambulant Versicherte Erwachsene Insgesamt"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_run__l10n_be_display_eco_voucher_button
msgid "L10N Be Display Eco Voucher Button"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_ffe_employer_type
msgid "L10N Be Ffe Employer Type"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_amount
msgid "L10N Be Group Insurance Amount"
msgstr "L10N Be Gruppenversicherungsbetrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_cost
msgid "L10N Be Group Insurance Cost"
msgstr "L10N Be Gruppenversicherungskosten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_has_eco_vouchers
msgid "L10N Be Has Eco Vouchers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__l10n_be_is_below_scale_warning
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract_history__l10n_be_is_below_scale_warning
msgid "L10N Be Is Below Scale Warning"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_is_december
msgid "L10N Be Is December"
msgstr "L10N Be Ist Dezember"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_is_double_pay
msgid "L10N Be Is Double Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_max_seizable_amount
msgid "L10N Be Max Seizable Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__l10n_be_max_seizable_warning
msgid "L10N Be Max Seizable Warning"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_job__l10n_be_scale_category
msgid "L10N Be Scale Category"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_leave__l10n_be_sickness_can_relapse
msgid "L10N Be Sickness Can Relapse"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__lang
msgid "Language"
msgstr "Sprache"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__has_laptop
msgid "Laptop"
msgstr "Laptop"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__write_uid
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__write_date
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid ""
"Le précompte mobilier est supporté par le débiteur des revenus à la décharge"
" du bénéficiaire :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__leave_allocation_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__leave_allocation_id
msgid "Leave Allocation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Legal nature of the contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Limburg"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__line_ids
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_wizard__line_ids
msgid "Line"
msgstr "Zeile"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__line_ids
msgid "Lines"
msgstr "Linien"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__lines_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__lines_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__lines_count
msgid "Lines Count"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_long_sick
msgid "Long Term Sick"
msgstr "Langfristig krank"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "MAR"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "MAY"
msgstr "Mai"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Male"
msgstr "Männlich"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_alloc_paid_leave
msgid "Manage the Allocation of Paid Time Off"
msgstr "Verwalten der Zuweisung bezahlter Abwesenheiten"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_alloc_employee
msgid "Manage the Allocation of Paid Time Off Employee"
msgstr "Verwalten der Zuweisung von bezahlten Abwesenheiten"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employee_depature_notice
msgid "Manage the Employee Departure - Notice Duration"
msgstr "Verwalten der Mitarbeiterabreise - Kündigungsdauer"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_employee_depature_holiday_attests
msgid "Manage the Employee Departure Holiday Attests"
msgstr "Verwalten der Mitarbeiter Abreise Urlaub Attests"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Management staff"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Marc Demo"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__3
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__3
msgid "March"
msgstr "März"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Marital Status"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__certificate__civil_engineer
msgid "Master: Civil Engineering"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Masters"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_maternity
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_maternity
msgid "Maternity Time Off"
msgstr "Mutterschaftsurlaub"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_leave_allocation__max_leaves_allocated
msgid "Max Leaves Allocated"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__maximum_days
msgid "Maximum Days"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__5
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__5
msgid "May"
msgstr "Mai"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_ch_year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__meal_voucher
msgid "Meal Voucher"
msgstr "Mahlzeitscheck"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Meal Voucher (Employee Part)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Meal Voucher (Employer Part)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_meal_voucher_employer
msgid "Meal Voucher (Employer)"
msgstr "Essensscheck (Arbeitgeber)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_average_monthly_amount
msgid "Meal Voucher Average Monthly Amount"
msgstr "Essensschecks Durschnittlicher Monatsbetrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_meal_voucher_count
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip__meal_voucher_count
msgid "Meal Voucher Count"
msgstr "Anzahl Essensschecks"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_paid_monthly_by_employer
msgid "Meal Voucher Paid Monthly By Employer"
msgstr "Essensscheck monatlich vom Arbeitgeber bezahlt"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_paid_by_employer
msgid "Meal Voucher Paid by Employer"
msgstr "Vom Arbeitgeber bezahlter Essensscheck"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_action
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__meal_voucher_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_meal_voucher_amount
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_payroll_meal_voucher
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_view_pivot
msgid "Meal Vouchers"
msgstr "Essensschecks"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Meal Vouchers (Employee Part)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Meal Vouchers (Employer Part)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Mean Working Hours:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_medical_assistance
msgid "Medical Assistance"
msgstr "Medizinische Hilfe"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_ir_ui_menu
msgid "Menu"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_div_net
msgid "Misc. Net"
msgstr "Misc. Netz"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_div_impos
msgid "Misc. Taxable"
msgstr "Sonstiges Steuerpflichtig"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_mobile
msgid "Mobile"
msgstr "Mobil"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__mobile
msgid "Mobile Subscription"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Mobility Bonus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__1
msgid "Modification"
msgstr "Änderung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant brut des revenus"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant du Pr.M"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant imposable"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Montant à payer :"
msgstr "Betrag zu zahlen:"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__month
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__month
msgid "Month"
msgstr "Monat"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_ambulatory_insurance
msgid "Monthly ambulatory insurance (employer's share)"
msgstr "Monatliche ambulante Versicherung (Arbeitgeberanteil)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__fuel_card
msgid "Monthly amount the employee receives on his fuel card."
msgstr "Monatlicher Betrag, den der Mitarbeiter auf seiner Tankkarte erhält."

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_benefit_in_kind
msgid "Monthly benefit in kind"
msgstr "Monatliche Sachleistungen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__commission_on_target
msgid ""
"Monthly gross amount that the employee receives if the target is reached."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_group_insurance
msgid "Monthly group insurance (employer's share)"
msgstr "Monatliche Gruppenversicherung (Arbeitgeberanteil)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_hospital_insurance
msgid "Monthly hospital insurance (employer's share)"
msgstr "Monatliche Krankenhausversicherung (Arbeitgeberanteil)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__representation_fees
msgid ""
"Monthly net amount the employee receives to cover his representation fees."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report
msgid "Multiple_PRP_Declaration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.departure.reason,name:l10n_be_hr_payroll.departure_mutual_agreement
msgid "Mutual Agreement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "N Year"
msgstr "N Jahr"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid "N-1 Year"
msgstr "N-1 Jahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__normal
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__normal
msgid "N/A"
msgstr "N/A"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__niss
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__niss
msgid "NISS"
msgstr "NISS"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__niss
msgid "NISS Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "NISS:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "NOV"
msgstr "NOV"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__name
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__name
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Name"
msgstr "Namen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Name of the joint committee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "National Register Identification Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_negative_net
msgid "Negative Net"
msgstr "Negatives Netto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_net_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_net_termination
msgid "Net"
msgstr "Netto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_warrant_deduction
msgid "Net Deductions from Wages"
msgstr "Nettoabzüge von Löhnen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_net
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Net Salary"
msgstr "Nettogehalt"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Net Salary Paid By Third Party"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Net cost to the business"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_impulsion_12mo
msgid "Net part payable by the Onem (12+ months)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_impulsion_25yo
msgid "Net part payable by the Onem (< 25 years old)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Net salary paid by third party"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_schedule_change_allocation__new_resource_calendar_id
msgid "New Resource Calendar"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__wage
msgid "New Wage"
msgstr "Neuer Lohn"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__resource_calendar_id
msgid "New Working Schedule"
msgstr "Neuer Arbeitsplan"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_schedule_change_allocation.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "New working schedule on %(contract_name)s.<br/>New total: %(days)s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "No"
msgstr "Nein"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "No Certificate definer on the Payroll Configuration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__no_onss
msgid "No ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid ""
"No ONSS registration number nor company ID was found for company %s. Please "
"provide at least one."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__no_withholding_taxes
msgid "No Withholding Taxes"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
msgid "No first contract date found for employee %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/certificate.py:0
msgid ""
"No private key linked to the certificate, it is required to sign documents."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "No start/end notice period defined for %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Nom, prénoms, dénomination sociale ou officielle :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_non_respect_motivation
msgid "Non respect motivation (= 2 weeks)"
msgstr "Respektlosigkeit Motivation (= 2 Wochen)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Non-university higher education"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "None"
msgstr "Keine"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
msgid ""
"Note: The double holiday pay should only be computed if the reduction in "
"working time took place between 01/01 and 30/06 compared to year N-1."
msgstr ""
"Hinweis: Das doppelte Urlaubsgeld ist nur dann zu berechnen, wenn die "
"Verringerung der Arbeitszeit zwischen dem 01.01. und dem 30.06. im Vergleich"
" zum Jahr N-1 stattgefunden hat."

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_notice
msgid "Notice (Unprovided)"
msgstr "Hinweis (nicht bereitgestellt)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_notice_duration
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Notice Duration"
msgstr "Kündigungsdauer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_duration_month_before_2014
msgid "Notice Duration in month"
msgstr "Kündigungsfrist im Monat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_duration_week_after_2014
msgid "Notice Duration in weeks"
msgstr "Kündigungsdauer in Wochen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Notice Period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_fees
msgid "Notice duration"
msgstr "Kündigungsdauer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
msgid ""
"Notice period not set for %s. Please, set the departure notice period first."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_salary
msgid "Notice salary"
msgstr "Kündigungsgehalt"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__11
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__11
msgid "November"
msgstr "November"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of Affected Employees"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Number of Workers"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of completed training hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Number of days"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n
msgid "Number of days of unpaid time off taken during current year"
msgstr "Anzahl der im laufenden Jahr genommenen unbezahlten Urlaubstage"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__unpaid_time_off_n1
msgid "Number of days of unpaid time off taken during previous year"
msgstr "Anzahl der im Vorjahr genommenen unbezahlten Freizeiten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n1
msgid "Number of days on which you should recover the holiday pay."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n
msgid "Number of days to recover (N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_number_of_days_n1
msgid "Number of days to recover (N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Number of dependent children declared as disabled"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__disabled_children_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__disabled_children_number
msgid "Number of disabled children"
msgstr "Anzahl behinderter Kinder"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Number of hours"
msgstr "Anzahl der Stunden"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Number of joint committees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_juniors_dependent
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_juniors_dependent
msgid ""
"Number of juniors dependent on the employee, including the disabled ones"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Number of overtime hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__other_senior_dependent
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__other_senior_dependent
msgid ""
"Number of seniors dependent on the employee, including the disabled ones"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Number of tax dependents"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Number of workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Numbers of joint committees: 20000"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N° d’entreprise ou, à défaut, n° national :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N° téléphone :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "N°273S - 2020"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "OCT"
msgstr "Okt"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "ONNS Employer"
msgstr "LSS Arbeitgeber"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "ONSS"
msgstr "LSS"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_special_contribution_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_special_contribution_termination
msgid "ONSS (Double Holiday)"
msgstr "LSS (Urlaubsgeld)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_employer
msgid "ONSS (Employer)"
msgstr "LSS (Arbeitgeber)"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "ONSS (Office National de sécurité sociale)"
msgstr "LSS (Landesamt für soziale Sicherheit)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_onss_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_onss_termination
msgid "ONSS (Simple Holiday)"
msgstr "LSS (kein Urlaubsgeld)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_special_contribution_onss_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_special_contribution_onss_total
msgid "ONSS (TOTAL)"
msgstr "LSS (GESAMT)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_total
msgid "ONSS (Total)"
msgstr "LSS (Gesamt)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_certificate_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_certificate_id
msgid "ONSS Certificate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_company_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_company_id
msgid "ONSS Company ID"
msgstr "LSS-Unternehmens-ID"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "ONSS Company ID:"
msgstr "LSS-Unternehmens-ID:"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "ONSS Cotisation Termination Fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation on termination fees"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "ONSS Cotisation: Charges Redistribution"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Cotisation: Charges redistribution"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_onss_employer_detail
msgid "ONSS Detail (Employer)"
msgstr "LSS-Details (Arbeitgeber)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Double Holiday"
msgstr "LSS: Urlaubsgeld"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "ONSS Employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_expeditor_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_expeditor_number
msgid "ONSS Expeditor Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_company__onss_expeditor_number
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_config_settings__onss_expeditor_number
msgid ""
"ONSS Expeditor Number provided when registering service on the technical "
"user"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "ONSS Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Number:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "ONSS Reduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__onss_registration_number
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__onss_registration_number
msgid "ONSS Registration Number"
msgstr "LSS-Registrierungsnummer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "ONSS Registration Number:"
msgstr "LSS-Registrierungsnummer:"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_restructuring
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_onss_restructuring
msgid "ONSS Restructuring Reduction"
msgstr "LSS: Umstrukturierung der Ermäßigung"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Student"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS Thirteen Month"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "ONSS termination fees cotisation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__line_ids
msgid "Occupation Lines"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__occupation_rate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__occupation_rate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_line_view_form
msgid "Occupation Rate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Occupations #"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__10
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__10
msgid "October"
msgstr "Oktober"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Odoo"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Offical Company Information"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__oldest_contract_id
msgid "Oldest Contract"
msgstr "Ältester Vertrag"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid ""
"Only employees with a Bachelor/Master/Doctor/Civil Engineer degree can "
"benefit from the withholding taxes exemption."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_l10n_be_dmfa__unique
msgid ""
"Only one DMFA per year and per quarter is allowed. Another one already "
"exists."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
#: code:addons/l10n_be_hr_payroll/report/hr_contract_history.py:0
msgid "Operation not supported"
msgstr "Vorgang nicht unterstützt"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_treatment__0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_treatment__0
msgid "Original"
msgstr "Original"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_sending__0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_sending__0
msgid "Original send"
msgstr "Original-Senden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__other_dependent_people
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__other_dependent_people
msgid "Other Dependent People"
msgstr "Andere abhängige Personen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Other Exempted Amount From ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Other employer contributions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Other exempted amount from ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_other_annual
msgid "Other monthly/yearly"
msgstr "Sonstige monatlich/jährlich"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Others"
msgstr "Andere"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_outplacement
msgid "Outplacement (if more than 30 weeks notice duration)"
msgstr "Outplacement (bei mehr als 30 Wochen Kündigungsfrist)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Overseas Social Security"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Overtime worked"
msgstr "Überstunden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__pdf_error
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__pdf_error
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__pdf_error
msgid "PDF Error Message"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__pdf_file
msgid "PDF File"
msgstr "PDF-Datei"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__pdf_filename
msgid "PDF Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_pdf
msgid "PDF file"
msgstr "PDF Dokument"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_pfi
msgid "PFI"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.allocating_paid_time_off_wizard_action
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_hr_allocating_paid_time_off_view
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_allocating_paid_time_off_view_form
msgid "Paid Time Off Allocation"
msgstr "Bezahlte Abwesenheitszuweisung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off
msgid "Paid Time Off For The Period"
msgstr "Bezahlter Urlaub für den Zeitraum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_employee__paid_time_off_to_allocate
msgid "Paid Time Off To Allocate"
msgstr "Zuzuweisender bezahlter Urlaub"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_parental_time_off
msgid "Parental Time Off"
msgstr "Elternzeit"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__part_time
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_search
msgid "Part Time"
msgstr "Teilzeit"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid "Part Time of %(employee)s must be stated at %(link)s."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Part Time:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Part-Time"
msgstr "Teilzeit"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_partial_incapacity
msgid "Partial Incapacity (due to illness)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_paternity_legal
msgid "Paternity Time Off (Legal)"
msgstr "Vaterschaftsfreizeit (Legal)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_paternity_company
msgid "Paternity Time Off (Paid by Company)"
msgstr "Vaterschaftsfreizeit (bezahlt durch Unternehmen)"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip
msgid "Pay Slip"
msgstr "Pay Slip"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double
msgid "Pay double"
msgstr "Zahlen Doppelt"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_double_complementary
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_double_complementary
msgid "Pay double complementary"
msgstr "Doppelt ertragspflichtig"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_pay_variable_salary
msgid "Pay on variable salary (15.34 of the annual amount)"
msgstr "Zahlung auf variables Gehalt (15,34 des Jahresbetrags)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_pay_simple
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_pay_simple
msgid "Pay simple"
msgstr "Bezahlen Sie einfach"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Payment Structured Communication:"
msgstr "Strukturierte Zahlungsmitteilung:"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payroll_report
msgid "Payroll Analysis Report"
msgstr "Bericht zur Gehaltsabrechnung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__payslip_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__payslip_id
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip"
msgstr "Payslip"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_run
msgid "Payslip Batches"
msgstr "Lohnzettel Stapel"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_bonus_month
msgid "Payslip Double Holidays / 13th Month"
msgstr "Abrechnung Urlaubsgeld / 13. Monat"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_payslip_be
msgid "Payslip Regular Pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_light_payslip_be
msgid "Payslip Regular Pay (Light)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_payslip_worked_days
msgid "Payslip Worked Days"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip current year"
msgstr "Payslip aktuelles Jahr"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Payslip previous year"
msgstr "Payslip Vorjahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__payslip_n_ids
msgid "Payslips N"
msgstr "Payslips N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__payslip_n1_ids
msgid "Payslips N-1"
msgstr "Payslips N-1"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payslip_filter
msgid "Payslips with Eco-Vouchers"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Pension"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__period
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Period"
msgstr "Periode"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Period -"
msgstr "Periode -"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Period - Year"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Period: From"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Permanent contract (CDI)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid ""
"Please configure a gender (either male or female) for the following "
"employee: %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid ""
"Please configure a gender (either male or female) for the following employees:\n"
"\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
msgid ""
"Please configure the 'Company Number' and the 'Revenue Code' on the Payroll "
"Settings."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid ""
"Please find attached some data that will be useful to you to establish the "
"Social Report for the accounting year noted below. The Social Report for the"
" previous year may be useful for you to complete information concerning the "
"previous accounting year."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid ""
"Please note that the declaration by batch is rather complex and requires "
"some technical knowledge (using some concepts like ssh keys, SFTP servers, "
"and electronical signatures). You may want to take a look at the"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid ""
"Please provide an employer class for company %s. The employer class is given"
" by the ONSS and should be encoded in the Payroll setting."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid "Populate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__previous_contract_creation
msgid "Post Change Contract Creation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Posted Employee"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_32
msgid "Pp Amount 32"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_33
msgid "Pp Amount 33"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount_34
msgid "Pp Amount 34"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Pr.M versé"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"Premium from the Impulse Fund for general medicine obtained by a general "
"practitioner approved to settle in a \"priority\" area"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__double_pay_line_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
msgid "Previous Occupations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__double_pay_line_n_ids
msgid "Previous Occupations (N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__double_pay_line_n1_ids
msgid "Previous Occupations (N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Primary education"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Private Car"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__private_car_reimbursed_amount
msgid "Private Car Reimbursed Amount"
msgstr "Private Sacar erstattet Betrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__private_car
msgid "Private Car Reimbursement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_private_car
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_private_car
msgid "Private car"
msgstr "Privatauto"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_professional_tax_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_professional_tax_termination
msgid "Professional Tax"
msgstr "Berufssteuer"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Professional qualification"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_bank_holiday
msgid "Public Holiday"
msgstr "Gesetzlicher Feiertag"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_phc
msgid "Public Holiday Compensation"
msgstr "Ausgleich Gesetzlicher Feiertage"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Public Transport"
msgstr "Öffentliche Verkehrsmittel"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__public_transport_reimbursed_amount
msgid "Public Transport Reimbursed amount"
msgstr "Öffentlicher Verkehr Erstatteter Betrag"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Public Transportation"
msgstr "Öffentliche Verkehrsmittel"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_public_transport
msgid "Public Transportation (Tram - Bus - Metro)"
msgstr "Öffentliche Verkehrsmittel (Tram - Bus - U-Bahn)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__public_transport_employee_amount
msgid "Public transport paid by the employee (Monthly)"
msgstr "Vom Arbeitnehmer bezahlte öffentliche Verkehrsmittel (monatlich)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter
msgid "Quarter"
msgstr "Quartal"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 1"
msgstr "Quartal 1"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 2"
msgstr "Viertel 2"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 3"
msgstr "Quartal 3"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Quarter 4"
msgstr "Viertel 4"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter_end
msgid "Quarter End"
msgstr "Quartalsende"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Quarter End:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__quarter_start
msgid "Quarter Start"
msgstr "Quartalsstart"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Quarter Start:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "REVENUS MOBILIERS des DROITS D'AUTEUR et DROITS VOISINS"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Re-Generate PDF report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "Re-Generate XML report"
msgstr "XML-Bericht neu generieren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__file_type__r
msgid "Real File (R)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__departure_reason_code
msgid "Reason Code"
msgstr "Code für Grund"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
msgid "Recovered Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n
msgid "Recovered Simple Holiday Pay (N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_recovered_n1
msgid "Recovered Simple Holiday Pay (N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_recovery_additional
msgid "Recovery Additional Time"
msgstr "Wiederherstellung Zusätzliche Zeit"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_recovery
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_recovery
msgid "Recovery Bank Holiday"
msgstr "Erholungsbank Holiday"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__reference
msgid "Reference"
msgstr "Verweis"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Reference Mean Working Hours"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__reference_period
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Reference Period"
msgstr "Bezugszeitraum"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
msgid "Reference Working Time"
msgstr "Referenz Arbeitszeit"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_wizard__reference_year
msgid "Reference Year"
msgstr "Bezugsjahr"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_expense_refund
msgid "Refund Expenses"
msgstr "Erstattungskosten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Reimbursed Expenses"
msgstr "Erstattete Auslagen"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Reimbursed Expenses (Code 330)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Reimbursed Expenses (Representation Fees)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_reimbursement
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_reimbursement
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_reimbursement
msgid "Reimbursement"
msgstr "Erstattung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__remuneration_n1
msgid "Remuneration N-1"
msgstr "Vergütung N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__fictitious_remuneration_n
msgid "Remuneration fictitious current year"
msgstr "Vergütung fiktives laufendes Jahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__fictitious_remuneration_n1
msgid "Remuneration fictitious previous year"
msgstr "Vergütung fiktiv im Vorjahr"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid ""
"Remuneration of statutory holidays occurring within 30 days of the end date "
"of the contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Remuneration payment frequency"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Reorganization Measures:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_replacement
msgid "Replacement"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Replacement contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_representation_fees
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_work_entry_type__representation_fees
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__default_representation_fees
msgid "Representation Fees"
msgstr "Repräsentationsgebühren"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_representation_fees_volatile
msgid "Representation Fees (Without Serious Standards)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_rep_fees_regul
msgid "Representation Fees Regularization"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__requires_new_contract
msgid "Requires New Contract"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Resignation Date"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__notice_respect
msgid "Respect of the notice period"
msgstr "Einhaltung der Kündigungsfrist"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_ch_worker
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_ch_worker
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_ch_worker
msgid "Retain on Meal Voucher"
msgstr "Abzug Mahlzeitschecks"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Retenue de versement: précompte professionnel (salaires)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Retirement Date"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__l10n_be_revenue_code
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_config_settings__l10n_be_revenue_code
msgid "Revenue Code"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Right to time off"
msgstr "Urlaubsanspruch"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Rue du Paradis"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_specific_CP
msgid "Rules specific to Auxiliary Joint Committee"
msgstr "Spezifische Vorschriften für den Gemischten Hilfsausschuß"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Réduction CPDI"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Réservé à l'administration"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "SBS %(month)s %(year)s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "SDE"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "SEP"
msgstr "SEP"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "SOCIAL BALANCE SHEET -  COMPLETE SCHEME"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "SOCIAL SECURITY CERTIFICATE"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_xml_report
msgid "SU"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Salaries paid in relation to previous years"
msgstr "Gezahlte Gehälter im Vergleich zu den Vorjahren"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_advance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Advance"
msgstr "Gehaltsvorschuss"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Assignment"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Salary Attachment"
msgstr "Gehaltspfändungen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Salary Attachments"
msgstr "Gehaltspfändungen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__structure_type_id
msgid "Salary Structure Type"
msgstr "Art der Gehaltsstruktur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__salary_visibility
msgid "Salary as of December 2013"
msgstr "Gehalt ab Dezember 2013"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_work_entry_daily_benefit_report_search
msgid "Search Meal Voucher Report"
msgstr "Bericht Suche-Gutschein"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Secondary education"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__type_sending__1
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__type_sending__1
msgid "Send grouped corrections"
msgstr "Gruppierte Korrekturen senden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__type_sending
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__type_sending
msgid "Sending Type"
msgstr "Sendender Typ"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__seniority_description
msgid "Seniority"
msgstr "Dienstalter"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_scale_seniority
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__l10n_be_scale_seniority
msgid "Seniority at Hiring"
msgstr "Dienstalter bei Einstellung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__month__9
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__month__9
msgid "September"
msgstr "September"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Service Public Fédéral FINANCES"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__sheet_id
msgid "Sheet"
msgstr "Tabelle"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__sheet_274_10_filename
msgid "Sheet 274 10 Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__ip_wage_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__l10n_be_group_insurance_rate
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__fiscal_voluntary_rate
msgid "Should be between 0 and 100 %"
msgstr "Sollte zwischen 0 und 100 % liegen"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_part_sick
msgid "Sick Time Off (Without Guaranteed Salary)"
msgstr "Krankheitsbedingte Fehlzeiten (ohne Gehaltsgarantie)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_work_entry.py:0
msgid "Sick time off to report to DRS for %s."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_leave__l10n_be_sickness_relapse
msgid "Sickness Relapse"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_signature
msgid "Signature file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_simple_december
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.l10n_be_simple_december_category
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_december_pay
msgid "Simple December Pay"
msgstr "Einfaches Gehalt Dezember"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_simple_pay_december
msgid "Simple Holiday Pay (Lost due to working time reduction)"
msgstr "Einfaches Urlaubsgeld (Verlust durch Arbeitszeitverkürzung)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_simple_holiday_pay_variable_salary
msgid "Simple Holiday Pay - Variable Salary"
msgstr "Einfaches Urlaubsgeld - variables Gehalt"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_holiday_n
msgid "Simple Holiday Pay N"
msgstr "Einfaches Urlaubsgeld N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_december_slip_wizard__simple_holiday_n1
msgid "Simple Holiday Pay N-1"
msgstr "Einfaches Urlaubsgeld N-1"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n
msgid "Simple Holiday Pay to Recover (N)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__l10n_be_holiday_pay_to_recover_n1
msgid "Simple Holiday Pay to Recover (N-1)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard__slip_ids
msgid "Slip"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_small_unemployment
msgid "Small Unemployment"
msgstr "Sonderurlaub"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_small_unemployment
msgid "Small Unemployment (Brief Holiday)"
msgstr "Sonderurlaub (kurz)"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_filename
msgid "Social Balance Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_filename_xlsx
msgid "Social Balance Filename Xlsx"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_social_balance_sheet_action
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_social_balance
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_sheet
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_social_balance_sheet
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_balance_sheet_view_form
msgid "Social Balance Sheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__social_balance_xlsx
msgid "Social Balance Sheet Spreadsheet"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_social_security_certificate_action
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_social_security_certificate
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_sheet
#: model:ir.ui.menu,name:l10n_be_hr_payroll.menu_l10n_be_social_security_certificate
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_social_security_certificate_view_form
msgid "Social Security Certificate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_xlsx
msgid "Social Security Certificate Spreadsheet"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_filename
msgid "Social Security Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__social_security_filename_xlsx
msgid "Social Security Filename Xlsx"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_onss_rule
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_double_december_onss
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_onss_rule
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_onss_rule
msgid "Social contribution"
msgstr "Sozialbeitrag"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Social security organization"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "SocialBalance-%(date_from)s-%(date_to)s.pdf"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "SocialBalance-%(date_from)s-%(date_to)s.xlsx"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_solicitation_time_off
msgid "Solicitation Time Off"
msgstr "Solicitation Time Off"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.hr_salary_rule_student_regular_pay_solidarity_cotisation
msgid "Solidarity Cotisation - Student Job"
msgstr "Solidaritäts-Cotisation - Studentenjob"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Solidarity Cotisation: Company Cars"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
msgid ""
"Some employee don't have any contract.:\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"Some employee has no contract:\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Special Social Cotisation"
msgstr "Sondersozialbeitrag"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_m_onss_total
msgid "Special Social Cotisation (Total)"
msgstr "Sondersozialbeitrag (Gesamt)"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_spec_soc_contribution
msgid "Special social contribution"
msgstr "Sondersozialbeitrag"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_mis_ex_onss
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_mis_ex_onss
msgid "Special social cotisation"
msgstr "Sondersozialbeitrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__spouse_fiscal_status_explanation
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__spouse_fiscal_status_explanation
msgid "Spouse Fiscal Status Explanation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Staff movements during the exercise"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
msgid "Standard 38 hours/week"
msgstr "Standard 38 Stunden/Woche"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_start
msgid "Start Date"
msgstr "Startdatum"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_notice__start_notice_period
msgid "Start Notice Period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__date_start
msgid "Start Period"
msgstr "Startperiode"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "Start date must be earlier than end date."
msgstr "Das Startdatum muss vor dem Enddatum liegen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid "Start date must be later than the current contract's start date."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__date_start
msgid "Start date of the new contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__start_notice_period
msgid "Start notice period"
msgstr "Startbenachrichtigungszeitraum"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "Start notice period is defined after end notice period for %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__state
msgid "State"
msgstr "Staat"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_balance_sheet__state_xlsx
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_social_security_certificate__state_xlsx
msgid "State Xlsx"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid "State the Dimona at %(link)s to declare the arrival of %(employee)s."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Status of employed persons"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_stock_option
msgid "Stock Option"
msgstr "Aktienoption"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_strike
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_strike
msgid "Strike"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Structural Reductions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Structural reductions"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__struct_id
msgid "Structure"
msgstr "Struktur"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__structure_type_id
msgid "Structure Type"
msgstr "Strukturtyp"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Student"
msgstr "Student"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_student_regular_pay
msgid "Student: Regular Pay"
msgstr "Student: Regelmäßige Bezahlung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Students"
msgstr "Studenten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Sub-Total Gross"
msgstr "Brutto insgesamt"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Sub-total Gross"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "System 5:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "TOTAL"
msgstr "GESAMT"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Taking into account for remuneration:"
msgstr "Berücksichtigung der Vergütung:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Taux du Pr.M"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_employee__spouse_fiscal_status
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_users__spouse_fiscal_status
msgid "Tax status for spouse"
msgstr "Steuerstatus für Ehegatte"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Taxable Adaptation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx_line__taxable_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "Taxable Amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_32
msgid "Taxable Amount 32"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_33
msgid "Taxable Amount 33"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__taxable_amount_34
msgid "Taxable Amount 34"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Taxable Amounts (325)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_gross
msgid "Taxable Salary"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_taxable_termination
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_taxable_termination
msgid "Taxable Termination Amount"
msgstr "Steuerpflichtige Kündigungsbetrag"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays
msgid "Terminaison Holidays"
msgstr "Urlaubsanspruch bei Kündigung"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_additional_leave
msgid "Terminaison Holidays Additional Leave"
msgstr "Anspruch auf zusätzlichen Urlaub bei Kündigung"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_double
msgid "Terminaison Holidays Double Pay"
msgstr "Anspruch auf Urlaubsgeld bei Kündigung"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_double_basic
msgid "Terminaison Holidays Double Pay Basic"
msgstr "Anspruch auf Urlaubsgeldbasis bei Kündigung "

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_termination_holidays_simple
msgid "Terminaison Holidays Simple Pay"
msgstr "Anspruch auf Gehalt bei Kündigung"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_fees
msgid "Termination"
msgstr "Beendigung"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_employee_termination_fees
msgid "Termination Fees"
msgstr "Kündigungsgebühren"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_holidays_n
msgid "Termination Holidays Current Year"
msgstr "Kündigungsurlaub im laufenden Jahr"

#. module: l10n_be_hr_payroll
#: model:ir.actions.report,name:l10n_be_hr_payroll.action_report_termination_holidays_n1
msgid "Termination Holidays Previous Year"
msgstr "Kündigung Urlaub Vorjahr"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Termination fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__dmfa_code
msgid "The DMFA Code will identify the work entry in DMFA report."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_employee_check_percentage_fiscal_voluntary_rate
msgid "The Fiscal Voluntary rate on wage should be between 0 and 100."
msgstr ""
"Der freiwillige Steuersatz für Löhne sollte zwischen 0 und 100 liegen."

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_ip_rate
msgid "The IP rate on wage should be between 0 and 100."
msgstr "Der IP-Satz für DenLohn sollte zwischen 0 und 100 liegen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "The VAT or the ZIP number is not specified on your company"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"The belgian postcode length shouldn't exceed 4 characters and should contain"
" only numbers for employee %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"The company is not correctly configured on your employees. Please be sure "
"that the following pieces of information are set: street, zip, city, phone "
"and vat"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/res_company.py:0
msgid ""
"The company number should contain digits only, starts with a '0' or a '1' "
"and be 10 characters long."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "The company phone number shouldn't exceed 12 characters"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__time_off_allocation
msgid ""
"The computed amount is the sum of the new right to time off and the number "
"of time off already taken by the employee. Example: Moving from a full time "
"to a 4/5 part time with 6 days already taken will result into an amount of "
"80%% of 14 days + 6 days (rounded down) = 17 days."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid ""
"The contract %(contract_name)s for %(employee)s is not of one the following "
"types: CDI, CDD. Replacement, For a clearly defined work"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid ""
"The contract %(contract_name)s for %(employee)s is not of one the following "
"types: CP200 Employees or Student"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "The employee %s doesn't have a specified certificate"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "The employee first name shouldn't exceed 30 characters for employee %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
msgid "The employee is occupied from the %(date_from)s to the %(date_to)s."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
msgid ""
"The employee is occupied from the %(date_from)s to the %(date_to)s. There is"
" nothing to recover as the employee is there for more than 12 months"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_employee.py:0
msgid ""
"The employee start notice period should be set before the end notice period"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__internet
msgid "The employee's internet subcription will be paid up to this amount."
msgstr ""
"Die Internet-Subcription des Mitarbeiters wird bis zu diesem Betrag bezahlt."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__mobile
msgid "The employee's mobile subscription will be paid up to this amount."
msgstr ""
"Das Mobile-Abonnement des Mitarbeiters wird bis zu diesem Betrag bezahlt."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid "The file has been downloaded."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_10_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_281_45_form_wizard
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_individual_account_view_form
msgid ""
"The files won't be posted in the employee portal if you don't have the "
"Documents app."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid ""
"The following employees are linked to work addresses without any ONSS identification code:\n"
" %s"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid ""
"The following employees don't have a valid private address (with a street, a zip, a city and a country):\n"
"%s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_employee_lang_view_form
msgid ""
"The following employees have an invalid language for the selected salary structure.\n"
"                        <br/>\n"
"                        Please assign them a language below before continuing."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid ""
"The following work entry types do not have any DMFA code set:\n"
" %s"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.constraint,message:l10n_be_hr_payroll.constraint_hr_contract_check_percentage_group_insurance_rate
msgid ""
"The group insurance salary sacrifice rate on wage should be between 0 and "
"100."
msgstr ""
"Der Gehaltsverzichtssatz der Gruppenversicherung auf den Lohn sollte "
"zwischen 0 und 100 liegen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
msgid "The payslips should be from the same company."
msgstr "Die Gehaltsabrechnungen sollten von demselben Unternehmen stammen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
msgid ""
"The payslips should cover the same period:\n"
"%s"
msgstr ""
"Die Gehaltsabrechnungen sollten sich auf denselben Zeitraum beziehen:\n"
"%s"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "The phone number is not specified on your company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid ""
"The printed pdf contains all the data to encode into the 'DmfA declaration "
"web interface' that you can find"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
msgid ""
"The reference period is from the <b>1st of June %(previous_year)s</b> to the"
" <b>31st of May %(reference_year)s</b>"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
msgid ""
"The seized amount (%(seized_amount)s€) is above the belgian ceilings. Given "
"a global net salary of %(net_amount)s€ for the pay period and "
"%(dependent_children)s dependent children, the maximum seizable amount is "
"equal to %(max_seizable_amount)s€"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid "The time Percentage in R&D should be between 0-100"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid ""
"The wage is under the minimum scale of %(amount)s€ for a seniority of "
"%(years)s years."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__absence_work_entry_type_id
msgid ""
"The work entry type used when generating work entries to fit full time "
"working schedule."
msgstr ""
"Die Arbeitseintragsart, die bei der Erstellung von Arbeitseinträgen für "
"Vollzeitarbeitszeitpläne verwendet wird."

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Theoretical Notice Duration"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_dmfa.py:0
msgid "There is no defined expeditor number for the company."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
msgid "There is no payslip to generate for those employees"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
msgid "There is no valid payslip to declare."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Thirteen Month"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/certificate.py:0
msgid "This certificate is not valid, its validity has expired."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_payslip.py:0
msgid "This document is a translation. This is not a legal document."
msgstr "Dieses Dokument ist eine Übersetzung. Dies ist kein Rechtsdokument."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
msgid "This employee doesn't have a first contract date"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/hr_contract.py:0
msgid "This feature can only be used on a single contract."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_eco_vouchers_wizard_view_form
msgid ""
"This will add the eco-vouchers amounts on the open payslips in this batch "
"associated with the respective employees, and create new payslips where they"
" don't exist. Are you sure you want to proceed?"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_wizard__threshold
msgid "Threshold"
msgstr "Schwelle"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_leave
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Time Off"
msgstr "Abwesenheit"

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_hr_leave_allocation
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__time_off_allocation
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Time Off Allocation"
msgstr "Zuweisung Abwesenheit"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_n_ids
msgid "Time Off N"
msgstr "Abwesenheit N"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__holiday_status_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__leave_type_id
msgid "Time Off Type"
msgstr "Abwesenheitsart"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__rd_percentage
msgid "Time Percentage in R&D"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_allocated
msgid "Time off allocated during current year"
msgstr "Im laufenden Jahr zugeteilte Abwesenheit"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Time off already taken"
msgstr "Bereits genommene Abwesenheit"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payslip_employee_depature_holiday_attests__time_off_taken
msgid "Time off taken during current year"
msgstr "Im laufenden Jahr genommene Abwesenheit"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "To"
msgstr "An"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_alloc_employee_view_tree
msgid "To Allocate"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__l10n_be_dependent_children_attachment
msgid ""
"To benefit from this increase in the elusive or non-transferable quotas, the worker whose remuneration is subject to seizure or transfer, must declare it using a form, the model of which has been published in the Belgian Official Gazette. of 30 November 2006.\n"
"\n"
"He must attach to this form the documents establishing the reality of the charge invoked.\n"
"\n"
"Source: Opinion on the indexation of the amounts set in Article 1, paragraph 4, of the Royal Decree of 27 December 2004 implementing Articles 1409, § 1, paragraph 4, and 1409, § 1 bis, paragraph 4 , of the Judicial Code relating to the limitation of seizure when there are dependent children, MB, December 13, 2019."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "To the attention of the next employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "To the attention of the worker"
msgstr "Zur Aufmerksamkeit des Arbeiters"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "Toggle Explanation"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_total_n
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_total_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total"
msgstr "Gesamt"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Total (10.a + 10.b)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Total (18.a.1)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total (FTE)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Basic before ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Employer Cost"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Full Time (Code 1021)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Total Full Time (code 1021)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Total Full Time + Part Time (code 1023)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Full Time + Part-Time (Code 1023)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_gross_with_ip
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_gross_with_ip
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Gross"
msgstr "Brutto insgesamt"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Total Gross Before ONSS"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Total Net"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Total Net (Advance included)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
msgid "Total Part Time (code 1022)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total Part-Time (Code 1022)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Total Year"
msgstr "Gesamtjahr"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Total actual number of hours worked or FTE"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total allocated days"
msgstr "Gesamtzahl der zugewiesenen Tage"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total basic wage"
msgstr "Gesamtgrundlohn"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_total_cost
msgid "Total cost employer"
msgstr "Gesamtkosten Arbeitgeber"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total gross wage"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total net wage"
msgstr "Nettolohn insgesamt"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Total of all Contributions:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Total time off days"
msgstr "Gesamte Freizeit tagelang"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "Total: (14a + 14b + 14c + 14d + 14e)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "Totaux :"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__train_transport_reimbursed_amount
msgid "Train Transport Reimbursed amount"
msgstr "Zugverkehr Erstatteter Betrag"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__train_transport_employee_amount
msgid "Train transport paid by the employee (Monthly)"
msgstr "Vom Arbeitnehmer bezahlte Zugbeförderung (monatlich)"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_training
msgid "Training"
msgstr "Ausbildung"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_training
msgid "Training Time Off"
msgstr "Trainingszeit aus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Transportation"
msgstr "Transport"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__type_treatment
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__type_treatment
msgid "Treatment Type"
msgstr "Behandlungstyp"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Unemployment with company supplement"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "University education"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_unjustified_reason
msgid "Unjustified Reason"
msgstr "Ungerechtfertigte Begründung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Unpaid Time Off"
msgstr "Unbezahlter Urlaub"

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_type_unpredictable
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_unpredictable
msgid "Unpredictable Reason"
msgstr "Unvorhersehbarer Grund"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_termination_unreasonable_dismissal
msgid "Unreasonable dismissal"
msgstr "Unzumutbare Entlassung"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_10.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_281_45.py:0
msgid "Unsupported country code %s. Please contact an administrator."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_schedule_change_allocation
msgid "Update allocation on schedule change"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__has_bicycle
msgid "Use a bicycle as a transport mode to go to work"
msgstr "Verwenden Sie ein Fahrrad als Transportmittel, um zur Arbeit zu gehen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__employee_ids
msgid "Use this to limit the employees to compute"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_res_users
msgid "User"
msgstr "Benutzer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_public
msgid "Uses another public transportation"
msgstr "Nutzung eines anderen öffentlichen Verkehrs"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_car
msgid "Uses company car"
msgstr "Verwendet Firmenwagen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_private_car
msgid "Uses private car"
msgstr "Verwendet Privatauto"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__transport_mode_train
msgid "Uses train transportation"
msgstr "Verwendet Zugtransport"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "VAT Number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "VAT Number:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__xml_validation_state__done
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__validation_state__done
msgid "Valid"
msgstr "Gültig"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_work_entry_type_view_form
msgid "Valid For Advantages"
msgstr "Gültig für Vorteile"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_december_slip_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_double_pay_recovery_wizard_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Validate"
msgstr "Überprüfen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Validate & Compute holiday attests"
msgstr "Validate & Compute Urlaubsbetestat"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_notice
msgid "Validate & Compute termination fees"
msgstr "Validate & Compute Kündigungsgebühren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__validation_state
msgid "Validation State"
msgstr "Validierungsstatus"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid "Various bonuses"
msgstr "Verschiedene Boni"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__declaration_type__batch
msgid "Via Batch"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_dmfa__declaration_type__web
msgid "Via Web"
msgstr "Via Web"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__current_wage
msgid "Wage"
msgstr "Vergütung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__wage_with_holidays
msgid "Wage With Sacrifices"
msgstr "Gehalt mit Verzichten"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_273s__state__waiting
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_274_xx__state__waiting
msgid "Waiting"
msgstr "Warteliste"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__warrants_cost
msgid "Warrant monthly cost for the employer"
msgstr "Garantieren Sie monatliche Kosten für den Arbeitgeber"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__warrant_value_employee
msgid "Warrant monthly value for the employee"
msgstr "Warrant monatlicher Wert für den Mitarbeiter"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.structure,payslip_name:l10n_be_hr_payroll.hr_payroll_structure_cp200_structure_warrant
msgid "Warrants"
msgstr "Optionsscheine"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.view_hr_payroll_employee_departure_holiday_attests
msgid ""
"We draw your attention to the fact that this information is based on the data in Odoo and / or that you\n"
"                            have introduced in Odoo and that it is important that they be accompanied by a verification on your part\n"
"                            according to the particularities related to contract of the worker or your company which Odoo would not\n"
"                            know."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_payroll_generate_warrant_payslips_view_form
msgid ""
"When you're done with the commission edition, click on the 'Generate "
"Payslip' button to generate a batch of payslips using the commissions you've"
" provided."
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract_history__has_valid_schedule_change_contract
msgid ""
"Whether or not the employee has a contract candidate for a working schedule "
"change"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_social_security_certificate__aggregation_level__company
msgid "Whole Company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__high_pension
msgid "With High Pensions"
msgstr "Mit hohen Pensionen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__high_income
msgid "With High income"
msgstr "Mit hohem Einkommen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__low_income
msgid "With Low Income"
msgstr "Mit niedrigem Einkommen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__low_pension
msgid "With Low Pensions"
msgstr "Mit niedrigen Renten"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Withdrawal not retained"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_atn_warrant_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_taxes
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_withholding_taxes
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_p_p
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_pfi_salary_withholding_taxes
msgid "Withholding Tax"
msgstr "Quellensteuer"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__payment_reference
msgid "Withholding Tax Payment Reference"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_pp
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__pp_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes"
msgstr "Quellensteuern"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_double_holiday_pay_pp_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n1_rules_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_n_rules_withholding_taxes_total
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_thirteen_month_pp_total
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_salary_rule_category_withholding_taxes_total
msgid "Withholding Taxes (Total)"
msgstr "Quellensteuern (Gesamt)"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Capping (Bachelors)"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "Withholding Taxes Deduction"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_report__l10n_be_withholding_taxes_exemption
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_view_form
msgid "Withholding Taxes Exemption"
msgstr "Befreiung von der Quellensteuer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Exemption (Scientific Research) - Bachelors"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid ""
"Withholding Taxes Exemption (Scientific Research) - Doctors / Civil "
"Engineers"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Withholding Taxes Exemption (Scientific Research) - Masters"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_salary_withholding_reduction
msgid "Withholding reduction"
msgstr "Einbehaltung"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__hr_employee__spouse_fiscal_status__without_income
msgid "Without Income"
msgstr "Ohne Einkommen"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_payroll_generate_warrant_payslips_line__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_eco_vouchers_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_group_insurance_line_wizard__wizard_id
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_employee_lang_wizard_line__wizard_id
msgid "Wizard"
msgstr "Assistent"

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_work_accident
msgid "Work Accident"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.leave.type,name:l10n_be_hr_payroll.holiday_status_work_accident
msgid "Work Accident Time Off"
msgstr "Arbeitsunfälle - Freistellung"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Work Address:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_work_entry_daily_benefit_report
msgid "Work Entry Related Benefit Report"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model,name:l10n_be_hr_payroll.model_l10n_be_dmfa_location_unit
msgid "Work Place defined by ONSS"
msgstr "Von LSS definierte Arbeitsstelle"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Work Place:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__work_time_rate
msgid "Work Time Rate"
msgstr "Arbeitszeitrate"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Work accident policy number"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.l10n_be_hr_payroll_action_work_address_codes
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_res_company__dmfa_location_unit_ids
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_location_unit_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_config_settings_view_form
msgid "Work address DMFA codes"
msgstr "Arbeitsadresse DMFA-Codes"

#. module: l10n_be_hr_payroll
#: model:hr.payroll.dashboard.warning,name:l10n_be_hr_payroll.hr_payroll_dashboard_warning_invalid_work_address
msgid "Work addresses without ONSS identification code"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__meal_voucher
msgid "Work entries counts for meal vouchers"
msgstr "Arbeitsbeiträge zählen für Essensgutscheine"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__private_car
msgid "Work entries counts for private car reimbursement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__representation_fees
msgid "Work entries counts for representation fees"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_work_entry_type__leave_right
msgid "Work entries counts for time off right for next year."
msgstr "Arbeitseinträge zählen für das nächste Jahr für die Abwesenheit."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__presence_work_entry_type_id
msgid "Work entry type for regular attendances."
msgstr "Arbeitserfassungsart für regelmäßige Anwesenheiten."

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_l10n_be_hr_payroll_schedule_change_wizard__work_time_rate
msgid ""
"Work time rate versus full time working schedule, should be between 0 and "
"100 %."
msgstr ""
"Die Arbeitszeitquote im Vergleich zur Vollzeitarbeitszeit sollte zwischen 0 "
"und 100 % liegen."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Worker"
msgstr "Mitarbeiter"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.dmfa_pdf_report
msgid "Worker Code:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule.category,name:l10n_be_hr_payroll.hr_payroll_head_onss
msgid "Worker Social Contribution"
msgstr "Sozialbeitrag der Arbeitnehmer"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_security_certificate
msgid "Workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid ""
"Workers for whom the company has submitted a DIMONA declaration or who are "
"registered in the general staff register"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa_location_unit__partner_id
msgid "Working Address"
msgstr "Arbeitsadresse"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_termination_holidays_n1
msgid "Working Schedule"
msgstr "Arbeitszeiten"

#. module: l10n_be_hr_payroll
#: model:ir.actions.act_window,name:l10n_be_hr_payroll.schedule_change_wizard_action
#: model:ir.actions.server,name:l10n_be_hr_payroll.action_working_schedule_change_request
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_contract_history_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_hr_payroll_schedule_change_wizard_view_form
msgid "Working Schedule Change"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_hr_payroll_schedule_change_wizard.py:0
msgid ""
"Working schedule would stay unchanged by this action. Please select another "
"working schedule."
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "Workplace"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xls_file
msgid "XLS file"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_file
msgid "XML File"
msgstr "XML-Datei"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_filename
msgid "XML Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_file
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__dmfa_xml
msgid "XML file"
msgstr "XML-Datei"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xls_filename
msgid "Xls Filename"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_filename
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_filename
msgid "Xml Filename"
msgstr "Xml-Dateiname"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__xml_validation_state
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__xml_validation_state
msgid "Xml Validation State"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_273s__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_274_xx__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_10__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_281_45__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_dmfa__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_double_pay_recovery_line_wizard__year
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_l10n_be_individual_account__year
msgid "Year"
msgstr "Jahr"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_payroll_alloc_paid_leave__year
msgid "Year of the period to consider"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_year_end_bonus
msgid "Year-end bonus"
msgstr "Jahresendbonus"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__yearly_commission
msgid "Yearly Commission"
msgstr "Jährliche Kommission"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,field_description:l10n_be_hr_payroll.field_hr_contract__yearly_commission_cost
msgid "Yearly Commission Cost"
msgstr "Jährliche Kosten der Kommission"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_contract__eco_checks
msgid "Yearly amount the employee receives in the form of eco vouchers."
msgstr ""
"Jährlicher Betrag, den der Mitarbeiter in Form von Öko-Gutscheinen erhält."

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
msgid ""
"You don't have the right to do this. Please contact your administrator!"
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/models/l10n_be_273S.py:0
#: code:addons/l10n_be_hr_payroll/models/l10n_be_274_XX.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_allocating_paid_time_off.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_holiday_attest.py:0
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_employee_departure_notice.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_december_slip_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_double_pay_recovery_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_eco_vouchers_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_group_insurance_wizard.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""
"Sie müssen bei einem belgischen Unternehmen angemeldet sein, um diese "
"Funktion nutzen zu können"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/hr_payroll_generate_warrant_payslips.py:0
msgid "You should upload a file to import first."
msgstr ""

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_balance_sheet.py:0
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_social_balance
msgid "Youth Hiring Plan"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.work.entry.type,name:l10n_be_hr_payroll.work_entry_type_youth_time_off
msgid "Youth Time Off"
msgstr "Youth Time Off"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Advantages"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"a) Amount of income paid or allocated, broken down according to the "
"applicable withholding tax rate:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid "a) Amount of income paid or attributed:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Code 250"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Early vacation pay (other than referred to under 11b and 12b):"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Employer-specific expenses"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "a) Forfaitaires"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "a) Nom et prénom, ou dénomination"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Normal contributions and premiums"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Occasional worker in the Horeca sector"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Provided in 2024 as part of the relaunch:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Public transport"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Remuneration:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Repetitive expenses"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) Total number of overtime hours actually worked"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) based on income received from employer"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) from employers who do not use the cash register system"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "a) which is taken into account for the tax credit of 33.14%"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "and at the"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"b) Amount recognized as an expense in 2024 if this amount does not "
"correspond to the amount indicated in section 4.a:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_45
msgid ""
"b) Amount recognized as an expense in 2024 if this amount does not "
"correspond to the total of the amounts indicated in sections 5.a.1) and "
"5.a.2):"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Arrears"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Arrears (other than referred to in 9b, 11c and 12c):"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"b) Basis for the calculation of the additional salary relating to overtime "
"giving right to a reduction of:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Benefits in kind:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Contributions and bonuses for individual continuation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Early vacation pay"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Organized public transport"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Other codes"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Other expenses resulting directly from employment in Belgium"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Retired worker in the care sector"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "b) Rue et numéro/boîte"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "b) Réels"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) Tips"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"b) Voluntary overtime worked between 01.07.2023 and 31.12.2023 as part of "
"the relaunch:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) based on income received from a related foreign company"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) from employers who use the cash register system"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "b) which is taken into account for the tax credit of 52.54%"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Arrears"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Cancellation indemnities (other than referred to under 11d and 12d) and "
"redeployment indemnities:"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "c) Code pays, code postal et commun"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"c) Contributions and premiums for free supplementary pensions for salaried "
"workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Frontier workers"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Gross amount of remuneration"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Loyalty stamps"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Other means of transport"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "c) Voluntary overtime worked in 2022 as part of the recovery"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Cancellation indemnities"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"d) Income exempt from a flexi-job employment contract exercised by a "
"pensioner"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_273S
msgid "d) Numéro d'identification fiscale (facultatif)"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Remuneration for the month of December (Public authority) (2):"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Stock options"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "d) Travel by bicycle or speed pedelec"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_day
msgid "days"
msgstr "Tage"

#. module: l10n_be_hr_payroll
#. odoo-python
#: code:addons/l10n_be_hr_payroll/wizard/l10n_be_social_security_certificate.py:0
msgid "departments:"
msgstr "Abteilungen:"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"e) Advantage resulting from the provision of a cycle or a speed-pedelec"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "e) Beneficiary premium"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid ""
"e) Benefit of any kind resulting from temporary unemployment benefits "
"directly reimbursed to ONEM"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "f) Mobility budget: total amount"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
msgid "false"
msgstr "falsch"

#. module: l10n_be_hr_payroll
#: model:hr.contract.type,name:l10n_be_hr_payroll.l10n_be_contract_type_clearly_defined_work
msgid "for clearly defined work"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "for more informations"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.273S_xml_report
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.finprof_xml_report_single_declaration
msgid "fr"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "g) First employment agreement: compensatory supplement"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__state__generate
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__state__generate
msgid "generate"
msgstr "Generieren"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_10__state__get
#: model:ir.model.fields.selection,name:l10n_be_hr_payroll.selection__l10n_be_281_45__state__get
msgid "get"
msgstr "Erhalten"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_281_10
msgid "h) Student job"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "here"
msgstr "Hier"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_individual_account
msgid "hours/week"
msgstr ""

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled_children_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled_children_bool
msgid "if recipient children is/are declared disabled by law"
msgstr "Wenn Empfängerkinder gesetzlich für behindert erklärt werden/werden"

#. module: l10n_be_hr_payroll
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_hr_employee__disabled_spouse_bool
#: model:ir.model.fields,help:l10n_be_hr_payroll.field_res_users__disabled_spouse_bool
msgid "if recipient spouse is declared disabled by law"
msgstr "wenn empfängerehepartnerrechtlich für behindert erklärt wird"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.hr_employee_form__l10n_be_view_for
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "if spouse has professionnel income or not"
msgstr "wenn der Ehegatte ein Berufseinkommen hat oder nicht"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_month
msgid "months"
msgstr "Monate"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.res_users_view_form
msgid "number of dependent children declared as disabled"
msgstr "Anzahl der als behindert erklärten unterhaltsberechtigten Kinder"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "process overview"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_dmfa_view_form
msgid "the official documentation"
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.l10n_be_274_XX_view_form
msgid "to"
msgstr "An"

#. module: l10n_be_hr_payroll
#: model:hr.salary.rule,name:l10n_be_hr_payroll.cp200_employees_termination_fees_notice_duration_week
msgid "weeks"
msgstr "Wochen"

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_light_payslip
msgid ""
"€<span class=\"me-2\"/>\n"
"                (="
msgstr ""

#. module: l10n_be_hr_payroll
#: model_terms:ir.ui.view,arch_db:l10n_be_hr_payroll.report_belgium_payslip
msgid ""
"€<span class=\"me-2\"/>\n"
"            (="
msgstr ""
