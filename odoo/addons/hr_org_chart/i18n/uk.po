# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_org_chart
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON> <alina.lisnen<PERSON>@erp.co.ua>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:55+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>, 2024\n"
"Language-Team: Ukrainian (https://app.transifex.com/odoo/teams/41243/uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != 11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % 100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || (n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
msgid "<span class=\"o_stat_text\">Org Chart</span>"
msgstr "<span class=\"o_stat_text\">Оргструктура</span>"

#. module: hr_org_chart
#: model_terms:ir.actions.act_window,help:hr_org_chart.action_hr_employee_org_chart
msgid "Add a new employee"
msgstr "Додати нового співробітника"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_base
msgid "Basic Employee"
msgstr "Звичайний користувач"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__department_color
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__department_color
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__department_color
msgid "Department Color"
msgstr "Колір відділу"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_count
msgid "Direct Subordinates Count"
msgstr "Враховуються прямі підлеглі"

#. module: hr_org_chart
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,help:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Direct and indirect subordinates"
msgstr "Прямі та непрямі підлеглі"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "Direct subordinates"
msgstr "Призначені підлеглі"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee
msgid "Employee"
msgstr "Співробітник"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_department_hierarchy_view
msgid "Employees"
msgstr "Співробітники"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "In order to get an organigram, set a manager and save the record."
msgstr "Щоб отримати організацію, встановіть менеджера та збережіть запис."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_controller.xml:0
msgid ""
"In the Organigram you will have a clear overview of the hierarchy of "
"employees."
msgstr ""

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_base__child_all_count
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__child_all_count
msgid "Indirect Subordinates Count"
msgstr "Пірахунок непрямих підлеглих"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "Indirect subordinates"
msgstr "Непризначені підлеглі"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__is_subordinate
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__is_subordinate
msgid "Is Subordinate"
msgstr "Підлеглий"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "More managers"
msgstr "Більше менеджерів"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_controller.xml:0
msgid "No Data"
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "No hierarchy position."
msgstr "Немає ієрархічної позиції."

#. module: hr_org_chart
#. odoo-python
#: code:addons/hr_org_chart/models/hr_org_chart_mixin.py:0
msgid "Operation not supported"
msgstr "Операція не підтримується"

#. module: hr_org_chart
#: model:ir.actions.act_window,name:hr_org_chart.action_hr_employee_org_chart
#: model:ir.ui.menu,name:hr_org_chart.menu_hr_employee_org_chart
msgid "Org Chart"
msgstr "Оргструктура"

#. module: hr_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_public_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.hr_employee_view_form_inherit_org_chart
#: model_terms:ir.ui.view,arch_db:hr_org_chart.res_users_view_form
msgid "Organization Chart"
msgstr "Організаційна схема"

#. module: hr_org_chart
#: model:ir.model,name:hr_org_chart.model_hr_employee_public
msgid "Public Employee"
msgstr "Зовнішній користувач"

#. module: hr_org_chart
#: model_terms:ir.actions.act_window,help:hr_org_chart.action_hr_employee_org_chart
msgid ""
"Quickly find all the information you need for your employees such as contact"
" data, job position, availability, etc."
msgstr ""

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "Redirect"
msgstr "Перенаправлення"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "See All"
msgstr "Переглянути все"

#. module: hr_org_chart
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee__subordinate_ids
#: model:ir.model.fields,field_description:hr_org_chart.field_hr_employee_public__subordinate_ids
msgid "Subordinates"
msgstr "Підпорядкування"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hooks.js:0
msgid "Team"
msgstr "Команда"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "This employee has no manager or subordinate."
msgstr "Цей співробітник не має керівника або підлеглого."

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/fields/hr_org_chart.xml:0
msgid "Total"
msgstr "Разом"

#. module: hr_org_chart
#. odoo-javascript
#: code:addons/hr_org_chart/static/src/views/hr_employee_hierarchy/hr_employee_hierarchy_card.xml:0
msgid "people"
msgstr "Працівники"
