<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.l10n_fr_pos_cert</field>
        <field name="model">res.config.settings</field>
        <field name="inherit_id" ref="point_of_sale.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <form position="inside">
                <field name="country_code" invisible="1"/>
            </form>
            <xpath expr="//field[@name='point_of_sale_use_ticket_qr_code']/.." position="attributes">
                <attribute name="invisible">country_code in ['FR', 'MF', 'MQ', 'NC', 'PF', 'RE', 'GF', 'GP', 'TF']</attribute>
            </xpath>
        </field>
    </record>
</odoo>
