# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_plm
# 
# Translators:
# <PERSON><PERSON>, 2024
# <PERSON>, 2024
# c<PERSON><PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON><PERSON>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# Rune Restad, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: Rune Restad, 2025\n"
"Language-Team: Norwegian <PERSON>l (https://app.transifex.com/odoo/teams/41243/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__document_count
msgid "# Attachments"
msgstr "# vedlegg"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__eco_count
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__eco_count
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__eco_count
msgid "# ECOs"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "%(approval_name)s %(approver_name)s %(approval_status)s this ECO"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"Domain alias\"/>&amp;nbsp;"
msgstr ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"Domain alias\"/>&amp;nbsp;"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your ECO, that will be applied to the product later\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_plm_production_form_view
msgid "<span class=\"o_stat_text\">ECO(S)</span>"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "<span class=\"o_stat_text\">Revision</span>"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python-dictionary som brukes til å sette standardverdier på nye poster for "
"dette aliaset."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#, python-format
msgid "A user cannot be assigned more than once to the same stage"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_needaction
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_needaction
msgid "Action Needed"
msgstr "Handling påkrevet"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__active
msgid "Active"
msgstr "Aktiv"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_ids
msgid "Activities"
msgstr "Aktiviteter"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Dekorering for Aktivitetsunntak"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_state
msgid "Activity State"
msgstr "Aktivitetsstatus"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikon type Aktivitet"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__add
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__add
msgid "Add"
msgstr "Legg til"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Add a description..."
msgstr "Legg til beskrivelse..."

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_tag_action
msgid "Add a new tag"
msgstr "Opprett en ny etikett"

#. module: mrp_plm
#: model:res.groups,name:mrp_plm.group_plm_manager
msgid "Administrator"
msgstr "Administrator"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_id
msgid "Alias"
msgstr "Alias"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_contact
msgid "Alias Contact Security"
msgstr "Alias kontaktsikkerhet"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_domain_id
msgid "Alias Domain"
msgstr "Aliasdomene"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_domain
msgid "Alias Domain Name"
msgstr "Alias domenenavn"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_full_name
msgid "Alias Email"
msgstr "Alias epost"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_name
msgid "Alias Name"
msgstr "Aliasnavn"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_status
msgid "Alias Status"
msgstr "Alias status"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Alias-status vurderes på den siste meldingen mottatt."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_model_id
msgid "Aliased Model"
msgstr "Modell med alias"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "All Validations"
msgstr "Alle valideringer"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_change_kanban_state
msgid "Allow Change Kanban State"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_change_stage
msgid "Allow Change Stage"
msgstr "Tillat endring av stadium"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__allow_apply_change
msgid "Allow to apply changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__allow_apply_change
msgid "Allow to apply changes from this stage."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_tree
msgid "Apply Changes"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Apply Rebase"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__type
msgid "Apply on"
msgstr "Bruk på"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__approval_date
msgid "Approval Date"
msgstr "Godkjenningsdato"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__approval_roles
msgid "Approval Roles"
msgstr "Godkjenningsroller"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__template_stage_id
msgid "Approval Stage"
msgstr "Godkjenningsstadium"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__approval_type
msgid "Approval Type"
msgstr "Godkjenningstype"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__approval_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__approval_template_ids
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Approvals"
msgstr "Godkjenninger"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__approval_ids
msgid "Approvals by stage"
msgstr "Godkjenninger etter stadium"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Approve"
msgstr "Godkjenn"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__done
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__approved
msgid "Approved"
msgstr "Godkjent"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__user_id
msgid "Approved by"
msgstr "Godkjent av"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__optional
msgid "Approves, but the approval is optional"
msgstr "Godkjenner, men godkjenning er valgfritt"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Archived"
msgstr "Arkivert"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__effectivity__asap
msgid "As soon as possible"
msgstr "Så snart som mulig"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__effectivity__date
msgid "At Date"
msgstr "Ikke forfalt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_attachment_count
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_attachment_count
msgid "Attachment Count"
msgstr "Antall vedlegg"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__document_ids
msgid "Attachments"
msgstr "Vedlegg"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__awaiting_my_validation
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Awaiting My Validation"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Awaiting Validation"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Stykkliste oversiktsrapport"

#. module: mrp_plm
#: model:mrp.eco.type,name:mrp_plm.ecotype_bom_update
msgid "BOM Updates"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_mode_batch
msgid "Batch count Change"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom
msgid "Bill of Material"
msgstr "Stykkliste"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Material linje for stykkliste"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.actions.act_window,name:mrp_plm.mrp_bom_action_kanban
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_id
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_boms
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Bill of Materials"
msgstr "Stykklister"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_bom_action_kanban
msgid ""
"Bills of materials allow you to define the list of required components\n"
"              used to make a finished product; through a manufacturing\n"
"              order or a pack of products."
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__blocked
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_validated
msgid "Blocked"
msgstr "Blokkert"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__is_blocking
msgid "Blocking Stage"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__byproduct_id
msgid "BoM By-Product"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__bom_line_id
msgid "BoM Line"
msgstr "Stykkliste linje"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_rebase_ids
msgid "BoM Rebase"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__new_bom_revision
msgid "BoM Revision"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid "BoM Suggestions from %(mo_name)s"
msgstr ""

#. module: mrp_plm
#. odoo-javascript
#: code:addons/mrp_plm/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_plm.report_mrp_bom_inherit_mrp_plm
msgid "BoM Version"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "BoM:"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "By-Product Changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom_byproduct
msgid "Byproduct"
msgstr "Biprodukt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_can_approve
msgid "Can Approve"
msgstr "Kan godkjenne"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_can_reject
msgid "Can Reject"
msgstr "Kan avvise"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid ""
"Cannot create an ECO if the Manufacturing Order doesn't use a Bill of "
"Materials"
msgstr ""

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_changes
msgid "Changes"
msgstr "Endringer"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made in previous eco"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on old bill of materials"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on the new revision bill of materials"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on the operation."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__color
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__color
msgid "Color"
msgstr "Farge"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__color
msgid "Color Index"
msgstr "Fargeindeks"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__comment
msgid "Commented"
msgstr "Kommentert"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__comment
msgid "Comments only"
msgstr "Bare kommentarer"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__company_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__company_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Company"
msgstr "Firma"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Component Changes"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Component Rebase"
msgstr ""

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_configuration
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "Configuration"
msgstr "Konfigurasjon"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__conflict
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__conflict
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Conflict"
msgstr "Konflikt"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Conflict Resolved"
msgstr "Konflikt løst"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__operation_change
msgid "Consumed in Operation"
msgstr "Forbrukt i operasjon"

#. module: mrp_plm
#: model:ir.actions.server,name:mrp_plm.action_production_order_create_eco
msgid "Create ECO"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_stage_action
msgid "Create a new ECO stage"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_type_action_dashboard
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_type_action_form
msgid "Create a new ECO type"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__create_uid
msgid "Created by"
msgstr "Opprettet av"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__create_date
msgid "Created on"
msgstr "Opprettet den"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Tilpasset returmelding"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__effectivity
msgid "Date on which the changes should be applied. For reference only."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_defaults
msgid "Default Values"
msgstr "Standardverdier"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Define the approval roles on the ECO stages."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Delete"
msgstr "Slett"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__description
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Description"
msgstr "Beskrivelse"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__description
msgid "Description and tooltips of the stage states."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Description of the change and its reason."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__bom_change_ids
msgid "Difference between old BoM and new BoM revision"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__routing_change_ids
msgid "Difference between old operation and new operation revision"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__display_name
msgid "Display Name"
msgstr "Visningsnavn"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__displayed_image_id
msgid "Displayed Image"
msgstr "Vist bilde"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Documents"
msgstr "Dokumenter"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__done
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Done"
msgstr "Fullført"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__eco_id
msgid "ECO"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_graph
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_pivot
msgid "ECO Analysis"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_approval
#: model:mail.activity.type,name:mrp_plm.mail_activity_eco_approval
msgid "ECO Approval"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_approval_template
msgid "ECO Approval Template"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids
msgid "ECO BoM Changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids_on_byproduct
msgid "ECO BoM Changes - By-Product"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids_on_line
msgid "ECO BoM Changes - Component"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_bom_change
msgid "ECO BoM changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__eco_rebase_id
msgid "ECO Rebase"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__routing_change_ids
msgid "ECO Routing Changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_stage
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__eco_stage_id
msgid "ECO Stage"
msgstr ""

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_stage_action
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_stages
msgid "ECO Stages"
msgstr ""

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_tag_action
#: model:ir.model,name:mrp_plm.model_mrp_eco_tag
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_tag
msgid "ECO Tags"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_type
msgid "ECO Type"
msgstr ""

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_type_action_form
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_types
msgid "ECO Types"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_stage_action
msgid "ECO stages give the different stages for the Engineering Change Orders"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__eco_ids
msgid "ECO to be applied"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_bom_view_form_inherit_plm
msgid "ECO(s)"
msgstr ""

#. module: mrp_plm
#. odoo-javascript
#: code:addons/mrp_plm/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp_plm/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp_plm/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_ecos
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__eco_ids
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__eco_ids
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__eco_ids
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_report
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_calendar
#: model_terms:ir.ui.view,arch_db:mrp_plm.product_product_view_form_inherit_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.product_template_view_form_inherit_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.report_mrp_bom_inherit_mrp_plm
msgid "ECOs"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Eco"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Eco BoM"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__eco_count
msgid "Eco Count"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_routing_change
msgid "Eco Routing changes"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "Edit Task"
msgstr "Rediger oppgave"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__effectivity
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,name:mrp_plm.ecostage_effective
msgid "Effective"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__effectivity_date
msgid "Effective Date"
msgstr "Effektiv dato"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_email
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "Email Alias"
msgstr "E-postalias"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__email_cc
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Email cc"
msgstr "Email cc"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "Epost domene f.eks 'example.com' i '<EMAIL>'"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__eco_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__eco_id
msgid "Engineering Change"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco
msgid "Engineering Change Order (ECO)"
msgstr ""

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_approval
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_approval_my
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_late
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_main
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_product_tmpl
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_report
msgid "Engineering Change Orders"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "Engineering Changes"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Extra Info"
msgstr "Tilleggsinformasjon"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Filters"
msgstr "Filtre"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__final_stage
msgid "Final Stage"
msgstr "Siste stadium"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__folded
msgid "Folded in kanban view"
msgstr "Minimert i kanban-visning"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_follower_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_partner_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome-ikon, for eksempel fa-tasks"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__effectivity_date
msgid "For reference only."
msgstr "Bare for referanse."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Future Activities"
msgstr "Fremtidige aktiviteter"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_done
msgid "Green Kanban Label"
msgstr "Grønt merke i Kanban"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Grått merke i Kanban"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Group by..."
msgstr "Grupper etter..."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__has_message
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__has_message
msgid "Has Message"
msgstr "Har melding"

#. module: mrp_plm
#: model:ir.module.category,description:mrp_plm.module_category_manufacturing_product_lifecycle_management_(plm)
msgid "Helps you manage your product's lifecycles."
msgstr "Hjelper deg med administrasjon av produktenes livssyklus."

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__priority__1
msgid "High"
msgstr "Høy"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__id
msgid "ID"
msgstr "ID"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for å indikere aktivitetsunntak."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_needaction
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis haket av, vil nye meldinger kreve din oppmerksomhet."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_sms_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis haket av, har enkelte meldinger leveringsfeil."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"engineering change order without removing it."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__will_update_version
msgid ""
"If unchecked, the version of the product/BoM will remain unchanged once the "
"ECO is applied"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__image_128
msgid "Image 128"
msgstr "Bilde 128"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__normal
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_validated
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,name:mrp_plm.ecostage_progress
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "In Progress"
msgstr "Pågår"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_approved
msgid "Is Approved"
msgstr "Er godkjent"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_closed
msgid "Is Closed"
msgstr "Er avsluttet"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_is_follower
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_rejected
msgid "Is Rejected"
msgstr "Er avvist"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__mandatory
msgid "Is required to approve"
msgstr "Er påkrevd for å godkjenne"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__kanban_state
msgid "Kanban State"
msgstr "Kanban-status"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__kanban_state_label
msgid "Kanban State Label"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_done
msgid "Kanban Valid Explanation"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__write_uid
msgid "Last Updated by"
msgstr "Sist oppdatert av"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__write_date
msgid "Last Updated on"
msgstr "Sist oppdatert"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Late Activities"
msgstr "Forsinkede aktiviteter"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__latest_bom_id
msgid "Latest Bom"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Inngående deteksjon basert på lokal del"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval_my
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_late
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_main
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_product_tmpl
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_report
msgid ""
"Manage your products and bills of materials changes with the ECO's.\n"
"              Gather the related documentation and receive the necessary approvals\n"
"              before applying your changes."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_cycle_manual
msgid "Manual Duration Change"
msgstr "Manuell endring i varighet"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_production
msgid "Manufacturing Order"
msgstr "Produksjonsordre"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__production_id
msgid "Manufacturing Orders"
msgstr "Produksjonsordre"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_master_data
msgid "Master Data"
msgstr "Hoveddata"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_error
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_error
msgid "Message Delivery error"
msgstr "Melding ved leveringsfeil"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_ids
msgid "Messages"
msgstr "Meldinger"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_mode
msgid "Mode Change"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_tag_view_search
msgid "Mrp Eco Tags"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "MIn aktivitets tidsfrist"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "My Change Orders"
msgstr "Endringsordrene mine"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "My Validations"
msgstr "Valideringene mine"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__name
msgid "Name"
msgstr "Navn"

#. module: mrp_plm
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,name:mrp_plm.ecostage_new
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "New"
msgstr "Ny"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__new_bom_id
msgid "New Bill of Materials"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid "New BoM from %(mo_name)s"
msgstr "Ny stykkliste fra %(mo_name)s"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__current_bom_id
msgid "New Bom"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_operation_id
msgid "New Consumed in Operation"
msgstr ""

#. module: mrp_plm
#: model:mrp.eco.type,name:mrp_plm.ecotype0
msgid "New Product Introduction"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_uom_id
msgid "New Product UoM"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_product_qty
msgid "New revision quantity"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Neste kalender aktivitet"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Frist for neste aktivitet"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_summary
msgid "Next Activity Summary"
msgstr "Oppsummering av neste aktivitet"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_type_id
msgid "Next Activity Type"
msgstr "Neste aktivitetstype"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_bom_action_kanban
msgid "No bill of materials found. Let's create one!"
msgstr "Ingen stykkliste er funnet. La oss lage en!"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval_my
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_late
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_main
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_product_tmpl
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_report
msgid "No engineering change order found"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__priority__0
msgid "Normal"
msgstr "Normal"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__none
msgid "Not Yet"
msgstr "Ikke enda"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__note
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Note"
msgstr "Notat"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_needaction_counter
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_needaction_counter
msgid "Number of Actions"
msgstr "Antall handlinger"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_error_counter
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_error_counter
msgid "Number of errors"
msgstr "Antall feil"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_needaction_counter
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Antall beskjeder som trenger oppfølging"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_error_counter
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antall meldinger med leveringsfeil"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__final_stage
msgid "Once the changes are applied, the ECOs will be moved to this stage."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Open Component"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Open Operation"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__operation_name
msgid "Operation"
msgstr "Operasjon"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Operation Changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__operation_id
msgid "Operation Id"
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Operation not supported"
msgstr "Operasjon ikke støttet"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_attachment_id
msgid "Origin Attachment"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_res_model
msgid "Origin Model"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_res_name
msgid "Origin Name"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_blocked
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection, when the ECO is in that stage."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_done
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection, when the ECO is in that stage."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_normal
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection, when the ECO is in that stage."
msgstr ""

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_dashboard
msgid "Overview"
msgstr "Oversikt"

#. module: mrp_plm
#: model:ir.module.category,name:mrp_plm.module_category_manufacturing_product_lifecycle_management_(plm)
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_root
msgid "PLM"
msgstr ""

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_type_action_dashboard
msgid "PLM Overview"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_parent_model_id
msgid "Parent Model"
msgstr "Overordnede modell"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Hovedposterings tråd ID"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__previous_bom_id
msgid "Previous BoM"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_operation_id
msgid "Previous Consumed in Operation"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__previous_change_ids
msgid "Previous ECO Changes"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Previous Eco Component Changes"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_uom_id
msgid "Previous Product UoM"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_product_qty
msgid "Previous revision quantity"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__priority
msgid "Priority"
msgstr "Prioritet"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Produced in Operation"
msgstr "Produsert i operasjon"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_template
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__product_tmpl_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__product_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Product"
msgstr "Produkt"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_document
msgid "Product Document"
msgstr "Produkt dokument"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Product Only"
msgstr ""

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_product
msgid "Product Variant"
msgstr "Produktvariant"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Production"
msgstr "Produksjon"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__active
msgid "Production Ready"
msgstr ""

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_products
msgid "Products"
msgstr "Produkter"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__upd_product_qty
msgid "Quantity"
msgstr "Antall"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__rating_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__rating_ids
msgid "Ratings"
msgstr "Vurderinger"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_validated
msgid "Ready"
msgstr "Klar"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__rebase_id
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__rebase
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Rebase"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Rebase new revision of BoM with previous eco bom and old bom changes."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Post-id på Tråd"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Rødt merke i Kanban"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__name
msgid "Reference"
msgstr "Referanse"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Reject"
msgstr "Avvis"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__rejected
msgid "Rejected"
msgstr "vvist"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__displayed_image_attachment_id
msgid "Related attachment"
msgstr "Tilknyttet vedlegg"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__remove
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__remove
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Remove"
msgstr "Fjern"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_reporting
msgid "Reporting"
msgstr "Rapportering"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__required_user_ids
msgid "Requested Users"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_id
msgid "Responsible"
msgstr "Ansvarlig"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruker"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__name
msgid "Role"
msgstr "Rolle"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Routing"
msgstr "Ruting"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_sms_error
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS Leveringsfeil"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_search
msgid "Search"
msgstr "Søk"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__sequence
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__sequence
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "Set Cover Image"
msgstr "Sett forsidebilde"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_apply_change
msgid "Show Apply Change"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Show all records which has next action date is before today"
msgstr "Vis alle poster som har neste handlingsdato før dagen i dag"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__stage_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__stage_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Stage"
msgstr "Stadium"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Beskrivelse og hjelpetekster for stadium"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__stage_ids
msgid "Stages"
msgstr "Stadier"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Start Revision"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "State"
msgstr "Status"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__state
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__status
msgid "Status"
msgstr "Status"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basert på aktiviteter\n"
"Utgått: Fristen er allerede passert\n"
"I dag: Aktiviteten skal gjøres i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Successfully Rebased!"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid ""
"System will automatically resolved the conflict(s) and apply changes. Do you"
" agree?"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__name
msgid "Tag Name"
msgstr "Etikettnavn"

#. module: mrp_plm
#: model:ir.model.constraint,message:mrp_plm.constraint_mrp_eco_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Tag finnes fra før"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__tag_ids
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_tag_view_tree
msgid "Tags"
msgstr "Etiketter"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__user_can_approve
msgid "Technical field to check if approval by current user is required"
msgstr ""
"Teknisk felt for å sjekke om godkjenning fra nåværende bruker er påkrevd"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__user_can_reject
msgid "Technical field to check if reject by current user is possible"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__approval_template_id
msgid "Template"
msgstr "Mal"

#. module: mrp_plm
#: model:res.groups,comment:mrp_plm.group_plm_manager
msgid "The PLM manager manages products lifecycle management"
msgstr ""

#. module: mrp_plm
#: model:res.groups,comment:mrp_plm.group_plm_user
msgid "The PLM user uses products lifecycle management"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_product_product__version
#: model:ir.model.fields,help:mrp_plm.field_product_template__version
msgid "The current version of the product."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Navnet på e-postaliaset, f.eks. 'jobber' hvis du vil fange e-poster for "
"<<EMAIL>>"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr ""

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_validation
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "To Apply"
msgstr "Til bruk"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "To Do"
msgstr "Å gjøre"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Today Activities"
msgstr "Dagens aktiviteter"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__type_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__change_type
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__change_type
msgid "Type"
msgstr "Type"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type unntaks-aktivitet på posten."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__type_ids
msgid "Types"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Undo"
msgstr "Angre"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__uom_change
msgid "Unit of Measure"
msgstr "Enhet"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__update
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__update
msgid "Update"
msgstr "Oppdater"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__will_update_version
msgid "Update Version"
msgstr ""

#. module: mrp_plm
#: model:res.groups,name:mrp_plm.group_plm_user
msgid "User"
msgstr "Bruker"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__user_ids
msgid "Users"
msgstr "Brukere"

#. module: mrp_plm
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,name:mrp_plm.ecostage_validated
msgid "Validated"
msgstr "Validert"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Variant"
msgstr "Variant"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__version
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__version
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__version
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_bom_view_form_inherit_plm
msgid "Version"
msgstr "Versjon"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_approvals
msgid "Waiting Approvals"
msgstr "Godkjenninger som venter"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_approvals_my
msgid "Waiting my Approvals"
msgstr "Venter på mine godkjenninger"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__website_message_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__website_message_ids
msgid "Website Messages"
msgstr "Meldinger fra nettsted"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__website_message_ids
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__website_message_ids
msgid "Website communication history"
msgstr " Kommunikasjonshistorikk for nettsted"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__workcenter_id
msgid "Work Center"
msgstr "Arbeidssenter"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Arbeidssenter bruk"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_workcenters
msgid "Work Centers"
msgstr "Arbeidssentre"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels."
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "You cannot change the stage, as approvals are required in the process."
msgstr ""

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "You cannot change the stage, as approvals are still required."
msgstr "Du kan ikke endre stadiet, godkjenninger er fortsatt påkrevde."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "alias"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "e.g. Awesome Product 2.0"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "e.g. Engineering Department"
msgstr "for eksempel Utviklingsavdeling"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "e.g. mycompany.com"
msgstr ""

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "text-danger"
msgstr ""
