# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mrp_plm
# 
# Translators:
# Wil Odoo, 2024
# <PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:25+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2025\n"
"Language-Team: German (https://app.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__document_count
msgid "# Attachments"
msgstr "# Anhänge"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__eco_count
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__eco_count
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__eco_count
msgid "# ECOs"
msgstr "# Änderungsaufträge"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "%(approval_name)s %(approver_name)s %(approval_status)s this ECO"
msgstr ""
"%(approval_name)s %(approver_name)s %(approval_status)s dieser "
"Änderungsauftrag"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"Domain alias\"/>&amp;nbsp;"
msgstr ""
"<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Domain alias\" "
"title=\"Domain alias\"/>&amp;nbsp;"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Upload files to your ECO, that will be applied to the product later\n"
"                    </p><p>\n"
"                        Use this feature to store any files, like drawings or specifications.\n"
"                    </p>"
msgstr ""
"<p class=\"o_view_nocontent_smiling_face\">\n"
"                        Laden Sie Dateien in Ihren Änderungsauftrag hoch, um sie später auf das Produkt anzuwenden\n"
"                    </p><p>\n"
"                        Verwenden Sie diese Funktion, um beliebige Dateien, wie Zeichnungen oder Spezifikationen, zu speichern.\n"
"                    </p>"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_plm_production_form_view
msgid "<span class=\"o_stat_text\">ECO(S)</span>"
msgstr "<span class=\"o_stat_text\">Änderungsauftrag/-aufträge</span>"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "<span class=\"o_stat_text\">Revision</span>"
msgstr "<span class=\"o_stat_text\">Revision</span>"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Ein Python-Dictionary, das Standardwerte zur Verfügung stellt, wenn neue "
"Datensätze für diesen Alias angelegt werden."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#, python-format
msgid "A user cannot be assigned more than once to the same stage"
msgstr ""
"Ein Benutzer kann nicht mehr als einmal derselben Phase zugewiesen werden."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_needaction
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_needaction
msgid "Action Needed"
msgstr "Aktion notwendig"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__active
msgid "Active"
msgstr "Aktiv"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_ids
msgid "Activities"
msgstr "Aktivitäten"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitätsausnahme-Dekoration"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_state
msgid "Activity State"
msgstr "Status der Aktivität"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_type_icon
msgid "Activity Type Icon"
msgstr "Symbol des Aktivitätstyps"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__add
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__add
msgid "Add"
msgstr "Hinzufügen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Add a description..."
msgstr "Beschreibung hinzufügen …"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_tag_action
msgid "Add a new tag"
msgstr "Ein neues Stichwort hinzufügen"

#. module: mrp_plm
#: model:res.groups,name:mrp_plm.group_plm_manager
msgid "Administrator"
msgstr "Administrator"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_id
msgid "Alias"
msgstr "Alias"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_contact
msgid "Alias Contact Security"
msgstr "Aliaskontakt-Sicherheit"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_domain_id
msgid "Alias Domain"
msgstr "Alias-Domain"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_domain
msgid "Alias Domain Name"
msgstr "Alias-Domainname"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_full_name
msgid "Alias Email"
msgstr "Alias-E-Mail"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_name
msgid "Alias Name"
msgstr "Alias-Name"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_status
msgid "Alias Status"
msgstr "Alias-Status"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_status
msgid "Alias status assessed on the last message received."
msgstr ""
"Alias-Status, der bei der letzten empfangenen Nachricht festgestellt wurde."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_model_id
msgid "Aliased Model"
msgstr "Alias-Modell"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "All Validations"
msgstr "Alle Validierungen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_change_kanban_state
msgid "Allow Change Kanban State"
msgstr "Änderung des Kanban-Status zulassen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_change_stage
msgid "Allow Change Stage"
msgstr "Phasewechsel zulassen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__allow_apply_change
msgid "Allow to apply changes"
msgstr "Anwendung der Änderungen zulassen"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__allow_apply_change
msgid "Allow to apply changes from this stage."
msgstr "Anwenden der Änderungen ab dieser Phase zulassen."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_tree
msgid "Apply Changes"
msgstr "Änderungen anwenden"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Apply Rebase"
msgstr "Umbasierung anwenden"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__type
msgid "Apply on"
msgstr "Anwenden auf"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__approval_date
msgid "Approval Date"
msgstr "Genehmigungsdatum"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__approval_roles
msgid "Approval Roles"
msgstr "Genehmigungsrollen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__template_stage_id
msgid "Approval Stage"
msgstr "Genehmigungsphase"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__approval_type
msgid "Approval Type"
msgstr "Genehmigungstyp"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__approval_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__approval_template_ids
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Approvals"
msgstr "Genehmigungen"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__approval_ids
msgid "Approvals by stage"
msgstr "Genehmigungen nach Phase"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Approve"
msgstr "Genehmigen"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__done
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__approved
msgid "Approved"
msgstr "Genehmigt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__user_id
msgid "Approved by"
msgstr "Genehmigt von"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__optional
msgid "Approves, but the approval is optional"
msgstr "Genehmigt, aber die Genehmigung ist optional"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Archived"
msgstr "Archiviert"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__effectivity__asap
msgid "As soon as possible"
msgstr "Sobald wie möglich"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__effectivity__date
msgid "At Date"
msgstr "Zum Datum"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_attachment_count
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_attachment_count
msgid "Attachment Count"
msgstr "Anzahl Anhänge"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__document_ids
msgid "Attachments"
msgstr "Dateianhänge"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__awaiting_my_validation
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Awaiting My Validation"
msgstr "Validierung ausstehend"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Awaiting Validation"
msgstr "Validierung ausstehend"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_report_mrp_report_bom_structure
msgid "BOM Overview Report"
msgstr "Stücklistenübersichtsbericht"

#. module: mrp_plm
#: model:mrp.eco.type,name:mrp_plm.ecotype_bom_update
msgid "BOM Updates"
msgstr "Stücklistenaktualisierungen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_mode_batch
msgid "Batch count Change"
msgstr "Änderung an Sammelzählung"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom
msgid "Bill of Material"
msgstr "Stückliste"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom_line
msgid "Bill of Material Line"
msgstr "Stücklistenposition"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.actions.act_window,name:mrp_plm.mrp_bom_action_kanban
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_id
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_boms
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Bill of Materials"
msgstr "Stückliste"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_bom_action_kanban
msgid ""
"Bills of materials allow you to define the list of required components\n"
"              used to make a finished product; through a manufacturing\n"
"              order or a pack of products."
msgstr ""
"Mit Hilfe von Stücklisten können Sie die Liste der erforderlichen Komponenten definieren,\n"
"die zur Herstellung eines Endprodukts benötigt werden; durch einen\n"
"Fertigungsauftrag oder ein Produktpaket."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__blocked
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_blocked:mrp_plm.ecostage_validated
msgid "Blocked"
msgstr "Blockiert"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__is_blocking
msgid "Blocking Stage"
msgstr "Blockierende Phase"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__byproduct_id
msgid "BoM By-Product"
msgstr "Nebenprodukt der Stückliste"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__bom_line_id
msgid "BoM Line"
msgstr "Stücklistenposition"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_rebase_ids
msgid "BoM Rebase"
msgstr "Stücklistenumbasierung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__new_bom_revision
msgid "BoM Revision"
msgstr "Stücklistenrevision"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid "BoM Suggestions from %(mo_name)s"
msgstr "Stücklistenvorschläge von %(mo_name)s"

#. module: mrp_plm
#. odoo-javascript
#: code:addons/mrp_plm/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model_terms:ir.ui.view,arch_db:mrp_plm.report_mrp_bom_inherit_mrp_plm
msgid "BoM Version"
msgstr "Stücklistenversion"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "BoM:"
msgstr "Stückliste:"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "By-Product Changes"
msgstr "Änderungen an Nebenprodukt"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_bom_byproduct
msgid "Byproduct"
msgstr "Nebenprodukt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_can_approve
msgid "Can Approve"
msgstr "Kann genehmigen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_can_reject
msgid "Can Reject"
msgstr "Kann ablehnen"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid ""
"Cannot create an ECO if the Manufacturing Order doesn't use a Bill of "
"Materials"
msgstr ""
"Sie können keinen Änderungsauftrag erstellen, wenn der Fertigungsauftrag "
"keine Stückliste verwendet"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_changes
msgid "Changes"
msgstr "Änderungen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made in previous eco"
msgstr "Änderungen beim vorherigen ECO"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on old bill of materials"
msgstr "Änderungen bei der alten Stückliste"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on the new revision bill of materials"
msgstr "Änderungen bei der neuen Revisionsstückliste"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Changes made on the operation."
msgstr "Änderungen, die an dem Vorgang vorgenommen wurden."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__color
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__color
msgid "Color"
msgstr "Farbe"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__color
msgid "Color Index"
msgstr "Farbkennzeichnung"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__comment
msgid "Commented"
msgstr "Kommentiert"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__comment
msgid "Comments only"
msgstr "Nur Kommentare"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__company_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__company_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Company"
msgstr "Unternehmen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Component Changes"
msgstr "Änderungen an Komponente"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Component Rebase"
msgstr "Rebasierung der Komponente"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_configuration
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "Configuration"
msgstr "Konfiguration"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__conflict
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__conflict
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Conflict"
msgstr "Konflikt"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Conflict Resolved"
msgstr "Konflikt gelöst"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__operation_change
msgid "Consumed in Operation"
msgstr "Verbraucht bei Vorgang"

#. module: mrp_plm
#: model:ir.actions.server,name:mrp_plm.action_production_order_create_eco
msgid "Create ECO"
msgstr "Änderungsauftrag erstellen"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_stage_action
msgid "Create a new ECO stage"
msgstr "Eine neue Änderungsauftragsphase erstellen"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_type_action_dashboard
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_type_action_form
msgid "Create a new ECO type"
msgstr "Eine neue Änderungsauftragsart erstellen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__create_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__create_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Benutzerdefinierte unzustellbare Nachricht"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__effectivity
msgid "Date on which the changes should be applied. For reference only."
msgstr ""
"Datum, an dem die Änderungen angewandt werden sollen. Nur als Referenz "
"gedacht."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_defaults
msgid "Default Values"
msgstr "Standardwerte"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Define the approval roles on the ECO stages."
msgstr ""
"Definieren Sie die Genehmigungsrollen für die Phasen des Änderungsauftrags."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Delete"
msgstr "Löschen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__description
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Description"
msgstr "Beschreibung"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__description
msgid "Description and tooltips of the stage states."
msgstr "Beschreibung und Tooltipps der Phasenstatus."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Description of the change and its reason."
msgstr "Beschreibung der Änderung und ihrer Begründung."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__bom_change_ids
msgid "Difference between old BoM and new BoM revision"
msgstr "Unterschied der alten Stückliste und der neuen Stücklistenrevision"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__routing_change_ids
msgid "Difference between old operation and new operation revision"
msgstr "Unterschiede dem alten Vorgang und der neuen Vorgangsrevision"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__display_name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__displayed_image_id
msgid "Displayed Image"
msgstr "Angezeigtes Bild"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Documents"
msgstr "Dokumente"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__done
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Done"
msgstr "Erledigt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__eco_id
msgid "ECO"
msgstr "Änderungsauftrag"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_graph
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_pivot
msgid "ECO Analysis"
msgstr "Analyse der Änderungsaufträge"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_approval
#: model:mail.activity.type,name:mrp_plm.mail_activity_eco_approval
msgid "ECO Approval"
msgstr "Genehmigung für Änderungsauftrag"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_approval_template
msgid "ECO Approval Template"
msgstr "Genehmigungsvorlage für Änderungsauftrag"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids
msgid "ECO BoM Changes"
msgstr "Änderungsauftrag Stücklistenänderungen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids_on_byproduct
msgid "ECO BoM Changes - By-Product"
msgstr "Stücklistenänderung des Änderungsauftrags - Nebenprodukt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__bom_change_ids_on_line
msgid "ECO BoM Changes - Component"
msgstr "Stücklistenänderung des Änderungsauftrags - Komponente"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_bom_change
msgid "ECO BoM changes"
msgstr "Änderungsauftrag Stücklistenänderungen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__eco_rebase_id
msgid "ECO Rebase"
msgstr "Änderungsauftragsumbasierung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__routing_change_ids
msgid "ECO Routing Changes"
msgstr "Änderungsauftrag Arbeitsplanänderungen"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_stage
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__eco_stage_id
msgid "ECO Stage"
msgstr "Änderungsauftragsphase"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_stage_action
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_stages
msgid "ECO Stages"
msgstr "Änderungsauftragsphasen"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_tag_action
#: model:ir.model,name:mrp_plm.model_mrp_eco_tag
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_tag
msgid "ECO Tags"
msgstr "Stichwörter für Änderungsaufträge"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_type
msgid "ECO Type"
msgstr "Änderungsauftragsart"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_type_action_form
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_types
msgid "ECO Types"
msgstr "Änderungsauftragsarten"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_stage_action
msgid "ECO stages give the different stages for the Engineering Change Orders"
msgstr ""
"Änderungsauftragsphasen stellen die verschiedenen Phasen für die technischen"
" Änderungsaufträge"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__eco_ids
msgid "ECO to be applied"
msgstr "Anzuwendender Änderungsauftrag"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_bom_view_form_inherit_plm
msgid "ECO(s)"
msgstr "Änderungsauftrag/-aufträge"

#. module: mrp_plm
#. odoo-javascript
#: code:addons/mrp_plm/static/src/components/bom_overview_display_filter/mrp_bom_overview_display_filter.js:0
#: code:addons/mrp_plm/static/src/components/bom_overview_line/mrp_bom_overview_line.js:0
#: code:addons/mrp_plm/static/src/components/bom_overview_table/mrp_bom_overview_table.xml:0
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_ecos
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__eco_ids
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__eco_ids
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__eco_ids
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_eco_report
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_calendar
#: model_terms:ir.ui.view,arch_db:mrp_plm.product_product_view_form_inherit_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.product_template_view_form_inherit_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.report_mrp_bom_inherit_mrp_plm
msgid "ECOs"
msgstr "Änderungsaufträge"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Eco"
msgstr "Änderungsauftrag"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Eco BoM"
msgstr "Stückliste von Änderungsaufträgen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__eco_count
msgid "Eco Count"
msgstr "Anzahl Änderungsaufträge"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco_routing_change
msgid "Eco Routing changes"
msgstr "Änderungsauftrag Arbeitsplanänderungen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "Edit Task"
msgstr "Aufgabe bearbeiten"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__effectivity
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,name:mrp_plm.ecostage_effective
msgid "Effective"
msgstr "In Kraft"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__effectivity_date
msgid "Effective Date"
msgstr "Datum der Inkraftsetzung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_email
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "Email Alias"
msgstr "E-Mail-Alias"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__email_cc
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Email cc"
msgstr "E-Mail-CC"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "E-Mail-Domain z. B. 'beispiel.com' in '<EMAIL>'"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__eco_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__eco_id
msgid "Engineering Change"
msgstr "Technische Änderung"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_eco
msgid "Engineering Change Order (ECO)"
msgstr "Änderungsauftrag (ECO)"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_approval
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_approval_my
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_late
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_main
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_product_tmpl
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_action_report
msgid "Engineering Change Orders"
msgstr "Technische Änderungsaufträge"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "Engineering Changes"
msgstr "Technische Änderungen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Extra Info"
msgstr "Weitere Informationen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Filters"
msgstr "Filter"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__final_stage
msgid "Final Stage"
msgstr "Endphase"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__folded
msgid "Folded in kanban view"
msgstr "In Kanban-Ansicht eingeklappt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_follower_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_follower_ids
msgid "Followers"
msgstr "Follower"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_partner_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_partner_ids
msgid "Followers (Partners)"
msgstr "Follower (Partner)"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "FontAwesome-Icon, z. B. fa-tasks"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__effectivity_date
msgid "For reference only."
msgstr "Nur als Referenz gedacht."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Future Activities"
msgstr "Anstehende Aktivitäten"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_done
msgid "Green Kanban Label"
msgstr "Grüne Kanban-Kennzeichnung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Graue Kanban-Kennzeichnung"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Group by..."
msgstr "Gruppieren nach ..."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__has_message
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__has_message
msgid "Has Message"
msgstr "Hat eine Nachricht"

#. module: mrp_plm
#: model:ir.module.category,description:mrp_plm.module_category_manufacturing_product_lifecycle_management_(plm)
msgid "Helps you manage your product's lifecycles."
msgstr "Hilft Ihnen, den Lebenszyklus Ihres Produktes zu verwalten."

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__priority__1
msgid "High"
msgstr "Hoch"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__id
msgid "ID"
msgstr "ID"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID des übergeordneten Alias-Datensatz (Beispiel: Projekt welches den Alias "
"für die Erstellung von Aufgaben beinhaltet)."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_exception_icon
msgid "Icon"
msgstr "Icon"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icon, um eine Ausnahmeaktivität anzuzeigen."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_needaction
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Falls markiert, erfordern neue Nachrichten Ihre Aufmerksamkeit."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_sms_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_error
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""
"Falls markiert, weisen einige Nachrichten einen Zustellungsfehler auf."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Wenn diese Option gesetzt ist, wird dieser Inhalt automatisch anstelle der "
"Standardnachricht an nichtautorisierte Benutzer gesendet."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__active
msgid ""
"If the active field is set to False, it will allow you to hide the "
"engineering change order without removing it."
msgstr ""
"Wenn das aktive Feld auf Falsch gesetzt ist, können Sie den technischen "
"Änderungsauftrag ausblenden, ohne ihn zu entfernen."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__will_update_version
msgid ""
"If unchecked, the version of the product/BoM will remain unchanged once the "
"ECO is applied"
msgstr ""
"Wenn nicht angekreuzt, bleibt die Version des Produkts oder der Stückliste "
"unverändert, sobald der Änderungsauftrag angewendet wird"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__image_128
msgid "Image 128"
msgstr "Bild 128"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__kanban_state__normal
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_normal:mrp_plm.ecostage_validated
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,name:mrp_plm.ecostage_progress
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "In Progress"
msgstr "In Bearbeitung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_approved
msgid "Is Approved"
msgstr "Wurde genehmigt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_closed
msgid "Is Closed"
msgstr "Ist Geschlossen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_is_follower
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_is_follower
msgid "Is Follower"
msgstr "Ist Follower"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__is_rejected
msgid "Is Rejected"
msgstr "Wurde abgelehnt"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval_template__approval_type__mandatory
msgid "Is required to approve"
msgstr "Muss genehmigt werden"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_blocked
msgid "Kanban Blocked Explanation"
msgstr "Erläuterung zu blockiertem Kanban "

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_normal
msgid "Kanban Ongoing Explanation"
msgstr "Erläuterung zu Kanban in Bearbeitung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__kanban_state
msgid "Kanban State"
msgstr "Kanban-Status"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__kanban_state_label
msgid "Kanban State Label"
msgstr "Kanban-Statuskennzeichnung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__legend_done
msgid "Kanban Valid Explanation"
msgstr "Erläuterung zu gültigem Kanban "

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__write_uid
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert von"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__write_date
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Late Activities"
msgstr "Verspätete Aktivitäten"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_production__latest_bom_id
msgid "Latest Bom"
msgstr "Letzte Stückliste"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "Erkennung des Eingangs eines lokalen Elements"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval_my
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_late
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_main
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_product_tmpl
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_report
msgid ""
"Manage your products and bills of materials changes with the ECO's.\n"
"              Gather the related documentation and receive the necessary approvals\n"
"              before applying your changes."
msgstr ""
"Verwalten Sie die Änderungen Ihrer Produkte und Stücklisten mithilfe von Änderungsaufträgen.\n"
"              Sammeln Sie die entsprechende Dokumentation und erhalten Sie die erforderlichen Genehmigungen,\n"
"              bevor Sie Ihre Änderungen vornehmen."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_cycle_manual
msgid "Manual Duration Change"
msgstr "Änderung der manuellen Dauer"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_production
msgid "Manufacturing Order"
msgstr "Fertigungsauftrag"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__production_id
msgid "Manufacturing Orders"
msgstr "Fertigungsaufträge"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_master_data
msgid "Master Data"
msgstr "Stammdaten"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_error
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_error
msgid "Message Delivery error"
msgstr "Nachricht mit Zustellungsfehler"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_ids
msgid "Messages"
msgstr "Nachrichten"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__upd_time_mode
msgid "Mode Change"
msgstr "Modus-Änderung"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_tag_view_search
msgid "Mrp Eco Tags"
msgstr "Stichwörter für MRP-Änderungsauftrag"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Frist für meine Aktivitäten"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "My Change Orders"
msgstr "Meine Änderungsaufträge"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "My Validations"
msgstr "Meine Validierungen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__name
msgid "Name"
msgstr "Name"

#. module: mrp_plm
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,name:mrp_plm.ecostage_new
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "New"
msgstr "Neu"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__new_bom_id
msgid "New Bill of Materials"
msgstr "Neue Stückliste"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_production.py:0
msgid "New BoM from %(mo_name)s"
msgstr "Neue Stückliste von %(mo_name)s"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__current_bom_id
msgid "New Bom"
msgstr "Neue Stückliste"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_operation_id
msgid "New Consumed in Operation"
msgstr "Neu im Vorgang verbraucht"

#. module: mrp_plm
#: model:mrp.eco.type,name:mrp_plm.ecotype0
msgid "New Product Introduction"
msgstr "Produkt-Neueinführung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_uom_id
msgid "New Product UoM"
msgstr "Neue ME des Produkts"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__new_product_qty
msgid "New revision quantity"
msgstr "Neue Revisionsmenge"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Nächstes Aktivitätskalenderereignis"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Nächste Aktivitätsfrist"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_summary
msgid "Next Activity Summary"
msgstr "Zusammenfassung der nächsten Aktivität"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_type_id
msgid "Next Activity Type"
msgstr "Nächster Aktivitätstyp"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_bom_action_kanban
msgid "No bill of materials found. Let's create one!"
msgstr "Es wurde keine Stückliste gefunden. Erstellen Sie eine!"

#. module: mrp_plm
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_approval_my
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_late
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_main
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_product_tmpl
#: model_terms:ir.actions.act_window,help:mrp_plm.mrp_eco_action_report
msgid "No engineering change order found"
msgstr "Kein Änderungsauftrag gefunden"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__priority__0
msgid "Normal"
msgstr "Normal"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__none
msgid "Not Yet"
msgstr "Noch nicht"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__note
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Note"
msgstr "Notiz"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_needaction_counter
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_needaction_counter
msgid "Number of Actions"
msgstr "Anzahl der Aktionen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_error_counter
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_error_counter
msgid "Number of errors"
msgstr "Anzahl der Fehler"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_needaction_counter
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Anzahl der Nachrichten, die eine Aktion erfordern"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__message_has_error_counter
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Anzahl der Nachrichten mit Zustellungsfehler."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__final_stage
msgid "Once the changes are applied, the ECOs will be moved to this stage."
msgstr ""
"Sobald die Änderungen übernommen wurden, werden die Änderungsaufträge in "
"diese Phase verschoben."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Open Component"
msgstr "Komponente öffnen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Open Operation"
msgstr "Vorgang öffnen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__operation_name
msgid "Operation"
msgstr "Vorgang"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Operation Changes"
msgstr "Vorgangsänderungen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__operation_id
msgid "Operation Id"
msgstr "Vorgangs-ID"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Operation not supported"
msgstr "Vorgang nicht unterstützt"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Optionale ID eines Threads (Datensatz), dem alle eingehenden Nachrichten "
"zugeordnet werden, auch wenn auf sie nicht geantwortet wird. Wenn gesetzt, "
"verhindert dies die Anlage neuer Datensätze vollständig."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_attachment_id
msgid "Origin Attachment"
msgstr "Ursprünglicher Anhang"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_res_model
msgid "Origin Model"
msgstr "Ursprüngliches Modell"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_product_document__origin_res_name
msgid "Origin Name"
msgstr "Ursprünglicher Name"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_blocked
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_blocked
msgid ""
"Override the default value displayed for the blocked state for kanban "
"selection, when the ECO is in that stage."
msgstr ""
"Überschreiben Sie den Standardwert, der für den blockierten Status bei der "
"Kanban-Auswahl angezeigt wird, wenn sich der Änderungsauftrag in dieser "
"Phase befindet."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_done
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_done
msgid ""
"Override the default value displayed for the done state for kanban "
"selection, when the ECO is in that stage."
msgstr ""
"Überschreiben Sie den Standardwert, der für den Erledigt-Status der Kanban-"
"Auswahl angezeigt wird, wenn sich der Änderungsauftrag in dieser Phase "
"befindet."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__legend_normal
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_stage__legend_normal
msgid ""
"Override the default value displayed for the normal state for kanban "
"selection, when the ECO is in that stage."
msgstr ""
"Überschreiben Sie den Standardwert, der für den normalen Status der Kanban-"
"Auswahl angezeigt wird, wenn sich der Änderungsauftrag in dieser Phase "
"befindet."

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_dashboard
msgid "Overview"
msgstr "Übersicht"

#. module: mrp_plm
#: model:ir.module.category,name:mrp_plm.module_category_manufacturing_product_lifecycle_management_(plm)
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_root
msgid "PLM"
msgstr "PLM"

#. module: mrp_plm
#: model:ir.actions.act_window,name:mrp_plm.mrp_eco_type_action_dashboard
msgid "PLM Overview"
msgstr "PLM-Übersicht"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_parent_model_id
msgid "Parent Model"
msgstr "Übergeordnetes Modell"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Thread-ID des übergeordneten Datensatzes"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Das übergeordnete Modell des Alias. Dieses Modell, welches die Alias-"
"Referenz enthält, ist nicht zwangsläufig das Modell, das von alias_model_id "
"(Beispiel: project (parent_model) und task (model)) vorgegeben wird"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Richtlinie zum Hinterlassen einer Mitteilung im Dokument über das E-Mail-Gateway.\n"
"- Jeder: jeder kann eine Nachricht hinterlassen\n"
"- Partner: nur bestätigte Partner\n"
"- Follower: nur Follower des entsprechenden Dokuments\n"
" oder Mitglieder der verfolgten Kanäle\n"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__previous_bom_id
msgid "Previous BoM"
msgstr "Vorherige Stückliste"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_operation_id
msgid "Previous Consumed in Operation"
msgstr "Vorherig im Vorgang verbraucht"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__previous_change_ids
msgid "Previous ECO Changes"
msgstr "Vorherige Änderungen des Änderungsauftrags"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Previous Eco Component Changes"
msgstr "Änderungen an vorherigen Komponente des Änderungsauftrags"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_uom_id
msgid "Previous Product UoM"
msgstr "Vorherige ME des Produkts"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__old_product_qty
msgid "Previous revision quantity"
msgstr "Vorherige Revisionsmenge"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__priority
msgid "Priority"
msgstr "Priorität"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Produced in Operation"
msgstr "Produziert in Vorgang"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_template
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__product_tmpl_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__product_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Product"
msgstr "Produkt"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_document
msgid "Product Document"
msgstr "Produktdokument"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Product Only"
msgstr "Nur Produkt"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_product_product
msgid "Product Variant"
msgstr "Produktvariante"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Production"
msgstr "Produktion"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__active
msgid "Production Ready"
msgstr "Produktionsbereit"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_products
msgid "Products"
msgstr "Produkte"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__upd_product_qty
msgid "Quantity"
msgstr "Menge"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__rating_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__rating_ids
msgid "Ratings"
msgstr "Bewertungen"

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_effective
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_new
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_progress
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_effective
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_new
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_progress
#: model:mrp.eco.stage,legend_done:mrp_plm.ecostage_validated
msgid "Ready"
msgstr "Bereit"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__rebase_id
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__rebase
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Rebase"
msgstr "Umbasieren"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Rebase new revision of BoM with previous eco bom and old bom changes."
msgstr ""
"Basieren Sie die neue Stücklistenrevision mit der vorherigen Stückliste des "
"Änderungsauftrags und den alten Stücklistenänderungen um."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Thread-ID des Datensatzes"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Rote Kanban-Kennzeichnung"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__name
msgid "Reference"
msgstr "Referenz"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Reject"
msgstr "Ablehnen"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_approval__status__rejected
msgid "Rejected"
msgstr "Abgelehnt"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__displayed_image_attachment_id
msgid "Related attachment"
msgstr "Zugehöriger Anhang"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__remove
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__remove
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Remove"
msgstr "Entfernen"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_reporting
msgid "Reporting"
msgstr "Berichtswesen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__required_user_ids
msgid "Requested Users"
msgstr "Angeforderter Benutzer"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__user_id
msgid "Responsible"
msgstr "Verantwortlich"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__activity_user_id
msgid "Responsible User"
msgstr "Verantwortlicher Benutzer"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__name
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__name
msgid "Role"
msgstr "Rolle"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Routing"
msgstr "Arbeitsplan"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__message_has_sms_error
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS-Zustellungsfehler"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_search
msgid "Search"
msgstr "Suchen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__sequence
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__sequence
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__sequence
msgid "Sequence"
msgstr "Sequenz"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "Set Cover Image"
msgstr "Titelbild festlegen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__allow_apply_change
msgid "Show Apply Change"
msgstr "Anwendung der Änderungen anzeigen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Show all records which has next action date is before today"
msgstr "Alle Datensätze mit vor heute geplanten Aktionen anzeigen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__stage_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__stage_id
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Stage"
msgstr "Phase"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Stage Description and Tooltips"
msgstr "Phasenbeschreibung und Tooltipps"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__stage_ids
msgid "Stages"
msgstr "Phasen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "Start Revision"
msgstr "Revision starten"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "State"
msgstr "Status"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__state
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__status
msgid "Status"
msgstr "Status"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status basierend auf Aktivitäten\n"
"Überfällig: Fälligkeitsdatum bereits überschritten\n"
"Heute: Aktivitätsdatum ist heute\n"
"Geplant: anstehende Aktivitäten."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "Successfully Rebased!"
msgstr "Erfolgreich umbasiert!"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid ""
"System will automatically resolved the conflict(s) and apply changes. Do you"
" agree?"
msgstr ""
"Das System löst den/die Konflikt(e) automatisch und übernimmt die "
"Änderungen. Stimmen Sie zu?"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_tag__name
msgid "Tag Name"
msgstr "Stichwortbezeichnung"

#. module: mrp_plm
#: model:ir.model.constraint,message:mrp_plm.constraint_mrp_eco_tag_name_uniq
msgid "Tag name already exists!"
msgstr "Stichwortbezeichnung existiert bereits!"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__tag_ids
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_tag_view_tree
msgid "Tags"
msgstr "Stichwörter"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Task in progress. Click to block or set as done."
msgstr ""
"Aufgabe in Bearbeitung. Klicken Sie, um zu blockieren oder als erledigt "
"festzulegen."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "Task is blocked. Click to unblock or set as done."
msgstr ""
"Aufgabe ist blockiert. Klicken Sie hier, um die Blockierung aufzuheben oder "
"die Einstellungen als erledigt festzulegen."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__user_can_approve
msgid "Technical field to check if approval by current user is required"
msgstr ""
"Technisches Feld, um zu überprüfen, ob die Genehmigung des aktuellen "
"Benutzers erforderlich ist"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__user_can_reject
msgid "Technical field to check if reject by current user is possible"
msgstr ""
"Technisches Feld, um zu überprüfen, ob die Ablehnung des aktuellen Benutzers"
" möglich ist"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval__approval_template_id
msgid "Template"
msgstr "Vorlage"

#. module: mrp_plm
#: model:res.groups,comment:mrp_plm.group_plm_manager
msgid "The PLM manager manages products lifecycle management"
msgstr "Der PLM-Manager überwacht das Produktlebenszyklusmanagement"

#. module: mrp_plm
#: model:res.groups,comment:mrp_plm.group_plm_user
msgid "The PLM user uses products lifecycle management"
msgstr "Der PLM-Benutzer verwendet das Produktlebenszyklusmanagement"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_product_product__version
#: model:ir.model.fields,help:mrp_plm.field_product_template__version
msgid "The current version of the product."
msgstr "Die aktuelle Version des Produkts."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Das Modell (Odoo-Dokumentart), auf das sich der Alias bezieht. Alle "
"eingehenden E-Mails ohne Bezug zu einer bereits vorhandenen E-Mail führen "
"üblicherweise zur Erstellung eines neuen Datensatz dieses Modells (z. B. "
"Projektaufgabe)."

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Die Bezeichnung des E-Mail-Alias, z. B. „Jobs“, falls Sie E-Mails für "
"<<EMAIL>> erhalten möchten"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "This step is done. Click to block or set in progress."
msgstr ""
"Dieser Schritt ist abgeschlossen. Klicken Sie hier, um zu blockieren oder in"
" Bearbeitung zu setzen."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_validation
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_dashboard_view_kanban
msgid "To Apply"
msgstr "Anzuwenden"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco__state__confirmed
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "To Do"
msgstr "To-do"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_search
msgid "Today Activities"
msgstr "Heutige Aktivitäten"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__type_id
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__change_type
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__change_type
msgid "Type"
msgstr "Art"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ der Ausnahmeaktivität im Datensatz."

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_stage__type_ids
msgid "Types"
msgstr "Typen"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Undo"
msgstr "Rückgängig"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_bom_change__uom_change
msgid "Unit of Measure"
msgstr "Maßeinheit"

#. module: mrp_plm
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_bom_change__change_type__update
#: model:ir.model.fields.selection,name:mrp_plm.selection__mrp_eco_routing_change__change_type__update
msgid "Update"
msgstr "Aktualisieren"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__will_update_version
msgid "Update Version"
msgstr "Version aktualisieren"

#. module: mrp_plm
#: model:res.groups,name:mrp_plm.group_plm_user
msgid "User"
msgstr "Benutzer"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_approval_template__user_ids
msgid "Users"
msgstr "Benutzer"

#. module: mrp_plm
#: model:mrp.eco.stage,name:mrp_plm.ecostage_bom_update_validated
#: model:mrp.eco.stage,name:mrp_plm.ecostage_validated
msgid "Validated"
msgstr "Validiert"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.view_document_file_kanban_mrp_plm
msgid "Variant"
msgstr "Variante"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_bom__version
#: model:ir.model.fields,field_description:mrp_plm.field_product_product__version
#: model:ir.model.fields,field_description:mrp_plm.field_product_template__version
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_bom_view_form_inherit_plm
msgid "Version"
msgstr "Version"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_approvals
msgid "Waiting Approvals"
msgstr "Ausstehende Genehmigungen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__nb_approvals_my
msgid "Waiting my Approvals"
msgstr "Meine ausstehenden Genehmigungen"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco__website_message_ids
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_type__website_message_ids
msgid "Website Messages"
msgstr "Website-Nachrichten"

#. module: mrp_plm
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco__website_message_ids
#: model:ir.model.fields,help:mrp_plm.field_mrp_eco_type__website_message_ids
msgid "Website communication history"
msgstr "Website-Kommunikationsverlauf"

#. module: mrp_plm
#: model:ir.model.fields,field_description:mrp_plm.field_mrp_eco_routing_change__workcenter_id
msgid "Work Center"
msgstr "Arbeitsplatz"

#. module: mrp_plm
#: model:ir.model,name:mrp_plm.model_mrp_routing_workcenter
msgid "Work Center Usage"
msgstr "Arbeitsplatznutzung"

#. module: mrp_plm
#: model:ir.ui.menu,name:mrp_plm.menu_mrp_plm_workcenters
msgid "Work Centers"
msgstr "Arbeitsplätze"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid ""
"You can also add a description to help your coworkers understand the meaning"
" and purpose of the stage."
msgstr ""
"Sie können auch eine Beschreibung eingeben, damit Ihre Kollegen den "
"Hintergrund und Zweck dieser Phase verstehen."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid ""
"You can define here labels that will be displayed for the state instead\n"
"                            of the default labels."
msgstr ""
"Sie können hier Beschriftungen für Phasen definieren, die anstelle der Standardbeschriftungen \n"
"angezeigt werden."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "You cannot change the stage, as approvals are required in the process."
msgstr ""
"Sie können die Phase nicht ändern, da im Prozess Genehmigungen benötigt "
"werden."

#. module: mrp_plm
#. odoo-python
#: code:addons/mrp_plm/models/mrp_eco.py:0
msgid "You cannot change the stage, as approvals are still required."
msgstr ""
"Sie können die Phase nicht ändern, da noch einige Genehmigungen benötigt "
"werden."

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "alias"
msgstr "Alias"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_view_form
msgid "e.g. Awesome Product 2.0"
msgstr "z. B. Großartiges Produkt 2.0"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_stage_view_form
msgid "e.g. Engineering Department"
msgstr "z. B. Technikabteilung"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_type_view_form
msgid "e.g. mycompany.com"
msgstr "z. B. mycompany.com"

#. module: mrp_plm
#: model_terms:ir.ui.view,arch_db:mrp_plm.mrp_eco_kanban
msgid "text-danger"
msgstr "text-danger"
