# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* l10n_be_reports
#
# Translators:
# <PERSON> <<EMAIL>>, 2023
# <PERSON>, 2023
# <PERSON><PERSON><PERSON>, 2023
# <PERSON>, 2023
# <PERSON>, 2023
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.5alpha1+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-08 09:20+0000\n"
"PO-Revision-Date: 2024-08-08 09:20+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.1\n"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_plcf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_plcf
msgid "(14) - Profit (Loss) to Be Carried Forward (+)/(-)"
msgstr "(14) - Over te dragen winst (verlies) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba_plpafa
msgid ""
"(9905) - Profit (Loss) of the Period Available for Appropriation (+)/(-)"
msgstr "(9905) - Te bestemmen winst (verlies) van het boekjaar (+)/(-)"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "(including amounts that are related to other taxable periods):"
msgstr ""
"is uitbetaald (met inbegrip van de sommen die betrekking hebben op andere "
"belastbare tijdperken):"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
",\n"
"                                                enter the amount actually "
"paid in"
msgstr "is uitbetaald, vermeld dan hier het bedrag dat werkelijk in"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "0.00"
msgstr "0.00"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"0.00\n"
"                                                <br/><br/>"
msgstr ""
"0.00\n"
"                                                <br/><br/>"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "1. NN ou NE:"
msgstr "1. NN of ON:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "1. N°"
msgstr "1. N°"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_aff
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_aff
msgid "10 - Association or Foundation Funds"
msgstr "10 - Fondsen van de vereniging of stichting "

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c
msgid "10 - Capital"
msgstr "10 - Kapitaal"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c
msgid "10/11 - Contributions"
msgstr "10/11 - Inbreng"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e
msgid "10/15 - Equity"
msgstr "10/15 - Eigen vermogen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_tot
msgid "10/49 - TOTAL LIABILITIES"
msgstr "10/49 - TOTAAL VAN DE PASSIVA"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c_ic
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c_ic
msgid "100 - Issued Capital"
msgstr "100 - Geplaatst kapitaal"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_c_uc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_c_uc
msgid "101 - Uncalled Capital"
msgstr "101 - Niet-opgevraagd kapitaal"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc
msgid "11 - Beyond Capital"
msgstr "11 - Buiten kapitaal"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c_a
msgid "110 - Available"
msgstr "110 - Beschikbaar"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc_spa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc_spa
msgid "1100/10 - Share Premium Account"
msgstr "1100/10 - Uitgiftepremies"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_c_bc_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_c_bc_o
msgid "1109/19 - Other"
msgstr "1109/19 - Andere"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_c_na
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_c_na
msgid "111 - Not Available"
msgstr "111 - Onbeschikbaar"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_rs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_rs
msgid "12 - Revaluation Surpluses"
msgstr "12 - Herwaarderingsmeerwaarden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_for
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_for
msgid "13 - Funds and Other Reserves"
msgstr "13 - Bestemde fondsen en andere reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r
msgid "13 - Reserves"
msgstr "13 - Reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_lr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_lr
msgid "130 - Legal Reserve"
msgstr "130 - Wettelijke reserve"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno
msgid "130/1 - Reserves Not Available"
msgstr "130/1 - Onbeschikbare reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_rnos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_rnos
msgid "1311 - Reserves Not Available Statutorily"
msgstr "1311 - Statutair onbeschikbare reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_pos
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_pos
msgid "1312 - Purchase of Own Shares"
msgstr "1312 - Inkoop eigen aandelen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_fs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_fs
msgid "1313 - Financial Support"
msgstr "1313 - Financiële steunverlening"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_rno_o
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_rno_o
msgid "1319 - Other"
msgstr "1319 - Overige"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_ur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_ur
msgid "132 - Untaxed Reserves"
msgstr "132 - Belastingvrije reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_r_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_r_ar
msgid "133 - Available Reserves"
msgstr "133 - Beschikbare reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl
msgid "14 - Accumulated Profits (Losses) (+)/(-)"
msgstr "14 - Overgedragen winst (verlies) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba_plppbf
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba_plppbf
msgid "14P - Profit (Loss) of the Preceding Period Brought Forward (+)/(-)"
msgstr "14P - Overgedragen winst (verlies) van het vorige boekjaar (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_cs
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_cs
msgid "15 - Capital Subsidies"
msgstr "15 - Kapitaalsubsidies"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt
msgid "16 - Provisions and Deferred Taxes"
msgstr "16 - Voorzieningen en uitgestelde belastingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_pso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_pso
msgid "160 - Pensions and Similar Obligations"
msgstr "160 - Pensioenen en soortgelijke verplichtingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc
msgid "160/5 - Provisions for Liabilities and Charges"
msgstr "160/5 - Voorzieningen voor risico's en kosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_t
msgid "161 - Taxes"
msgstr "161 - Belastingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_mrm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_mrm
msgid "162 - Major Repairs and Maintenance"
msgstr "162 - Grote herstellings- en onderhoudswerken"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_eo
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_eo
msgid "163 - Environmental Obligations"
msgstr "163 - Milieuverplichtingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_plc_olc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_plc_olc
msgid "164/5 - Other Liabilities and Charges"
msgstr "164/5 - Overige risico's en kosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_plc_pslrgrr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_plc_pslrgrr
msgid ""
"167 - Provisions for Subsidies and Legacies to Reimburse and Gifts With a "
"Recovery Right"
msgstr ""
"167 - Voorzieningen voor terug te betalen subsidies en legaten en voor "
"schenkingen met terugnemingsrecht"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_pdt_dt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_pdt_dt
msgid "168 - Deferred Taxes"
msgstr "168 - Uitgestelde belastingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy
msgid "17 - Amounts Payable After More Than One Year"
msgstr "17 - Schulden op meer dan één jaar"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap
msgid "17/49 - Amounts Payable"
msgstr "17/49 - Schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_sl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_sl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_sl
msgid "170 - Subordinated Loans"
msgstr "170 - Achtergestelde leningen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd
msgid "170/4 - Financial Debts"
msgstr "170/4 - Financiële schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ud
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ud
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ud
msgid "171 - Unsubordinated Debentures"
msgstr "171 - Niet-achtergestelde obligatieleningen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_loso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_loso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_loso
msgid "172 - Leasing and Other Similar Obligations"
msgstr "172 - Leasingschulden en soortgelijke schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd_ciloso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd_ciloso
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd_ciloso
msgid "172/3 - Credit Institutions, Leasing and Other Similar Obligations"
msgstr "172/3 - Kredietinstellingen, leasingschulden en soortgelijke schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ci
msgid "173 - Credit Institutions"
msgstr "173 - Kredietinstellingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_fd_ol
msgid "174 - Other Loans"
msgstr "174 - Overige leningen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_fd_ol
msgid "174/0 - Other Loans"
msgstr "174/0 - Overige leningen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td
msgid "175 - Trade Debts"
msgstr "175 - Handelsschulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td_s
msgid "1750 - Suppliers"
msgstr "1750 - Leveranciers"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_td_bep
msgid "1751 - Bills of Exchange Payable"
msgstr "1751 - Te betalen wissels"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_apcp
msgid "176 - Advance Payments on Contracts in Progress"
msgstr "176 - Vooruitbetalingen op bestellingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apamtoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apamtoy_oap
msgid "178/9 - Other Amounts Payable"
msgstr "178/9 - Overige schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_asdna
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_asdna
msgid "19 - Advance to Shareholders on the Distribution of Net Assets"
msgstr "19 - Voorschot aan de vennoten op de verdeling van het netto-actief"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "2. Name (or denomination) and address of the debtor of the income:"
msgstr "2. Naam (of benaming) en adres van de schuldeiser van de inkomsten:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "2. Phone number:"
msgstr "2. Telefoonnummer:"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fe
msgid "20 - Formation Expenses"
msgstr "20 - Oprichtingskosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_tot
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_tot
msgid "20/58 - TOTAL ASSETS"
msgstr "20/58 - TOTAAL VAN DE ACTIVA"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ifa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ifa
msgid "21 - Intangible Fixed Assets"
msgstr "21 - Immateriële vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa
msgid "21/28 - Fixed Assets"
msgstr "21/28 - Vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_lb
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_lb
msgid "22 - Land and Buildings"
msgstr "22 - Terreinen en gebouwen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa
msgid "22/27 - Tangible Fixed Assets"
msgstr "22/27 - Materiële vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_pme
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_pme
msgid "23 - Plant, Machinery and Equipment"
msgstr "23 - Installaties, machines en uitrusting"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_fv
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_fv
msgid "24 - Furniture and Vehicles"
msgstr "24 - Meubilair en rollend materieel"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_losr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_losr
msgid "25 - Leasing and Other Similar Rights"
msgstr "25 - Leasing en soortgelijke rechten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_otfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_otfa
msgid "26 - Other Tangible Fixed Assets"
msgstr "26 - Overige materiële vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_tfa_aucap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_tfa_aucap
msgid "27 - Assets Under Construction and Advance Payments"
msgstr "27 - Activa in aanbouw en vooruitbetalingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa
msgid "28 - Financial Fixed Assets"
msgstr "28 - Financiële vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac_pi
msgid "280 - Participating Interests"
msgstr "280 - Deelnemingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac
msgid "280/1 - Affiliated Companies"
msgstr "280/1 - Verbonden ondernemingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_ac_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_ac_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_ac_ar
msgid "281 - Amounts Receivable"
msgstr "281 - Vorderingen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.10"
msgstr "281.10"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.11"
msgstr "281.11"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.12"
msgstr "281.12"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.13"
msgstr "281.13"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.14"
msgstr "281.14"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.15"
msgstr "281.15"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.16"
msgstr "281.16"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.17"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.18"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.20"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "281.30"
msgstr ""

#. module: l10n_be_reports
#: model:res.partner.category,name:l10n_be_reports.res_partner_tag_281_50
msgid "281.50"
msgstr "281.50"

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_atn
msgid "281.50 - ATN"
msgstr "281.50 - VAA"

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_commissions
msgid "281.50 - Commissions"
msgstr "281.50 - Commissies"

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_exposed_expenses
msgid "281.50 - Exposed Expenses"
msgstr "281.50 - Uitgegeven kosten"

#. module: l10n_be_reports
#: model:account.account.tag,name:l10n_be_reports.account_tag_281_50_fees
msgid "281.50 - Fees"
msgstr "281.50 - Kosten"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_281_50_view_form
msgid "281.50 Form"
msgstr "Fiche 281.50"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "281.50 Forms"
msgstr "Fiches 281.50"

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_partner_281_50_pdf
msgid "281.50 PDF"
msgstr "281.50 PDF"

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_partner_281_50_xml
msgid "281.50 XML"
msgstr "281.50 XML"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__forms_281_50
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__forms_281_50
msgid "281.50 forms"
msgstr "Fiches 281.50"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi_pi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi_pi
msgid "282 - Participating Interests"
msgstr "282 - Deelnemingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi
msgid "282/3 - Other Companies Linked by Participating Interests"
msgstr "282/3 - Ondernemingen waarmee een deelnemingsverhouding bestaat"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_oclpi_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_oclpi_ar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_oclpi_ar
msgid "283 - Amounts Receivable"
msgstr "283 - Vorderingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa_s
msgid "284 - Shares"
msgstr "284 - Aandelen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa
msgid "284/8 - Other Financial Fixed Assets"
msgstr "284/8 - Andere financiële vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_fa_ffa_offa_arcg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_fa_ffa_offa_arcg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_fa_ffa_offa_arcg
msgid "285/8 - Amounts Receivable and Cash Guarantees"
msgstr "285/8 - Vorderingen en borgtochten in contanten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy
msgid "29 - Amounts Receivable After More Than One Year"
msgstr "29 - Vorderingen op meer dan één jaar"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca
msgid "29/58 - Current Assets"
msgstr "29/58 - Vlottende activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy_td
msgid "290 - Trade Debtors"
msgstr "290 - Handelsvorderingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_aramtoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_aramtoy_oar
msgid "291 - Other Amounts Receivable"
msgstr "291 - Overige vorderingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp
msgid "3 - Stocks and Contracts in Progress"
msgstr "3 - Voorraden en bestellingen in uitvoering"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "3. Identity of the debtor of the income"
msgstr "2. Identiteit van de schuldenaar van de inkomsten"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "3. Nature"
msgstr "3. Aard"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_rmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_rmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_rmc
msgid "30/31 - Raw Materials and Consumables"
msgstr "30/31 - Grond- en hulpstoffen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s
msgid "30/36 - Stocks"
msgstr "30/36 - Voorraden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_wip
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_wip
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_wip
msgid "32 - Work in Progress"
msgstr "32 - Goederen in bewerking"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_tree
msgid "325 Form"
msgstr "Opgave 325"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_325_wizard
msgid "325 Form Wizard"
msgstr "Opgave 325 Wizard"

#. module: l10n_be_reports
#: model:ir.actions.report,name:l10n_be_reports.action_report_325_form_pdf
msgid "325 PDF"
msgstr "325 PDF"

#. module: l10n_be_reports
#: model:ir.actions.act_window,name:l10n_be_reports.action_open_325_tree_view
msgid "325 forms"
msgstr "Opgaven 325"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_fg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_fg
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_fg
msgid "33 - Finished Goods"
msgstr "33 - Gereed product"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_gpr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_gpr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_gpr
msgid "34 - Goods Purchased for Resale"
msgstr "34 - Handelsgoederen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_ipis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_ipis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_ipis
msgid "35 - Immovable Property Intended for Sale"
msgstr "35 - Onroerende goederen bestemd voor verkoop"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_s_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_s_ap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_s_ap
msgid "36 - Advance Payments"
msgstr "36 - Vooruitbetalingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_scp_cp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_scp_cp
msgid "37 - Contracts in Progress"
msgstr "37 - Bestellingen in uitvoering"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "4. Comments:"
msgstr "4. Opmerkingen:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"4. Domicile (natural persons), registered office or main administrative\n"
"                                                office (companies and other "
"institutions)"
msgstr ""
"4. Woonplaats (natuurlijke personen), maatschappelijke zetel of voornaamste "
"bestuursinrichting (vennootschappen en andere instellingen):"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy_td
msgid "40 - Trade Debtors"
msgstr "40 - Handelsvorderingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy
msgid "40/41 - Amounts Receivable Within One Year"
msgstr "40/41 - Vorderingen op ten hoogste één jaar"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_arwoy_oar
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_arwoy_oar
msgid "41 - Other Amounts Receivable"
msgstr "41 - Overige vorderingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_cpapdwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_cpapdwoy
msgid ""
"42 - Current Portion of Amounts Payable After More Than One Year Falling Due "
"Within One Year"
msgstr "42 - Schulden op meer dan één jaar die binnen het jaar vervallen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy
msgid "42/48 - Amounts Payable Within One Year"
msgstr "42/48 - Schulden op ten hoogste één jaar"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd
msgid "43 - Financial Debts"
msgstr "43 - Financiële schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd_ci
msgid "430/8 - Credit Institutions"
msgstr "430/8 - Kredietinstellingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_fd_ol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_fd_ol
msgid "439 - Other Loans"
msgstr "439 - Overige leningen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td
msgid "44 - Trade Debts"
msgstr "44 - Handelsschulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td_s
msgid "440/4 - Suppliers"
msgstr "440/4 - Leveranciers"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_td_bep
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_td_bep
msgid "441 - Bills of Exchange Payable"
msgstr "441 - Te betalen wissels"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss
msgid "45 - Taxes, Remuneration and Social Security"
msgstr ""
"45 - Schulden met betrekking tot belastingen, bezoldigingen en sociale lasten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss_t
msgid "450/3 - Taxes"
msgstr "450/3 - Belastingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_trss_rss
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_trss_rss
msgid "454/9 - Remuneration and Social Security"
msgstr "454/9 - Bezoldigingen en sociale lasten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_apcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_apcp
msgid "46 - Advance Payments on Contracts in Progress"
msgstr "46 - Vooruitbetalingen op bestellingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_apwoy_oap
msgid "47/48 - Other Amounts Payable"
msgstr "47/48 - Overige schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_apwoy_oap
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_apwoy_oap
msgid "48 - Other Amounts Payable"
msgstr "48 - Overige schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_adi
msgid "490/1 - Accruals and Deferred Income"
msgstr "490/1 - Overlopende rekeningen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_adi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_adi
msgid "492/3 - Accruals and Deferred Income"
msgstr "492/3 - Overlopende rekeningen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_ap_sa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_ap_sa
msgid "499 - Suspense Accounts"
msgstr "499 - Wachtrekeningen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"5. Administrative or operating headquarters (companies and other\n"
"                                                institutions)\n"
"                                                or operating headquarters "
"(natural persons who have more than one such\n"
"                                                headquarters) when it is a "
"statement specific to that administrative or\n"
"                                                operating headquarters:"
msgstr ""
"5. Administratieve of exploitatiezetel (vennootschappen en andere "
"instellingen) of exploitatiezetel (natuurlijke personen die over "
"verscheidene zulke zetels beschikken), wanneer het een opgave betreft van "
"die administratieve of exploitatiezetel:"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci_os
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci_os
msgid "50 - Own Shares"
msgstr "50 - Eigen aandelen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci
msgid "50/53 - Current Investments"
msgstr "50/53 - Geldbeleggingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_ci_oi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_ci_oi
msgid "51/53 - Other Investments"
msgstr "51/53 - Overige beleggingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a_ca_cbh
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a_ca_cbh
msgid "54/58 - Cash at Bank and in Hand"
msgstr "54/58 - Liquide middelen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc
msgid "60 - Goods for Resale, Raw Materials and Consumables"
msgstr "60 - Handelsgoederen, grond- en hulpstoffen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_grrmcsog
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_grrmcsog
msgid ""
"60/61 - Goods for Resale, Raw Materials, Consumables, Services and Other "
"Goods"
msgstr ""
"60/61 - Handelsgoederen, grond- en hulpstoffen, diensten en diverse goederen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc
msgid "60/66A - Operating Charges"
msgstr "60/66A - Bedrijfskosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc_p
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc_p
msgid "600/8 - Purchases"
msgstr "600/8 - Aankopen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_grrmc_s
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_grrmc_s
msgid "609 - Stocks: Decrease (Increase) (+)/(-)"
msgstr "609 - Voorraad: afname (toename) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_sog
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_sog
msgid "61 - Services and Other Goods"
msgstr "61 - Diensten en diverse goederen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_rssp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_rssp
msgid "62 - Remuneration, Social Security and Pensions (+)/(-)"
msgstr "62 - Bezoldigingen, sociale lasten en pensioenen (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_aoawdfefa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_aoawdfeitfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_aoawdfefa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_aoawdfeitfa
msgid ""
"630 - Amortisations of and Other Amounts Written Down on Formation Expenses, "
"Intangible and Tangible Fixed Assets"
msgstr ""
"630 - Afschrijvingen en waardeverminderingen op oprichtingskosten, op "
"immateriële en materiële vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_awdscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_awdscptd
msgid ""
"631/4 - Amounts Written Down on Stocks, Contracts in Progress and Trade "
"Debtors: Additions (Write-Backs) (+)/(-)"
msgstr ""
"631/4 - Waardeverminderingen op voorraden, op bestellingen in uitvoering en "
"op handelsvorderingen: toevoegingen (terugnemingen) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_plca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_plc
msgid ""
"635/8 - Provisions for Liabilities and Charges: Appropriations (Uses and "
"Write-Backs) (+)/(-)"
msgstr ""
"635/8 - Voorzieningen voor risico's en kosten: toevoegingen (bestedingen en "
"terugnemingen) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_plca
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_plc
msgid ""
"635/9 - Provisions for Liabilities and Charges: Appropriations (Uses and "
"Write-Backs) (+)/(-)"
msgstr ""
"635/9 - Voorzieningen voor risico's en kosten: toevoegingen (bestedingen en "
"terugnemingen) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_ooc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_ooc
msgid "640/8 - Other Operating Charges"
msgstr "640/8 - Andere bedrijfskosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_ocraurc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_ocrarc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_ocraurc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_ocrarc
msgid ""
"649 - Operating Charges Reported as Assets Under Restructuring Costs (-)"
msgstr "649 - Als herstructureringskosten geactiveerde bedrijfskosten (-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc_rfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc
msgid "65 - Recurring Financial Charges"
msgstr "65 - Recurrente financiële kosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc
msgid "65/66B - Financial Charges"
msgstr "65/66B - Financiële kosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_dc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_dc
msgid "650 - Debt Charges"
msgstr "650 - Kosten van schulden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_awdcaoscptd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_awdcaoscptd
msgid ""
"651 - Amounts Written Down on Current Assets Other Than Stocks, Contracts in "
"Progress and Trade Debtors: Additions (Write-Backs) (+)/(-)"
msgstr ""
"651 - Waardeverminderingen op vlottende activa andere dan voorraden, "
"bestellingen in uitvoering en handelsvorderingen: toevoegingen "
"(terugnemingen) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_rfc_ofc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_rfc_ofc
msgid "652/9 - Other Financial Charges"
msgstr "652/9 - Andere financiële kosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_nroc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oc_nroc
msgid "66A - Non-Recurring Operating Charges"
msgstr "66A - Niet-recurrente bedrijfskosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fc_nrfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fc_nrfc
msgid "66B - Non-Recurring Financial Charges"
msgstr "66B - Niet-recurrente financiële kosten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_itor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_itor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr
msgid "67/77 - Income Taxes on the Result (+)/(-)"
msgstr "67/77 - Belastingen op het resultaat (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr_t
msgid "670/3 - Taxes"
msgstr "670/3 - Belastingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_ttdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_ttdt
msgid "680 - Transfer to Deferred Taxes"
msgstr "680 - Overboeking naar de uitgestelde belastingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_ttur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_ttur
msgid "689 - Transfer to Untaxed Reserves"
msgstr "689 - Overboeking naar de belastingvrije reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tc
msgid "691 - To Contributions"
msgstr "691 - aan de inbreng"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_tafor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_tafor
msgid "691 - Transfers to Allocated Funds and Other Reserves"
msgstr "691 - Toevoeging aan de bestemde fondsen en andere reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a
msgid "691/2 - Appropriations to Equity"
msgstr "691/2 - Toevoeging aan het eigen vermogen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tlr
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tlr
msgid "6920 - To Legal Reserve"
msgstr "6920 - aan de wettelijke reserve"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ate_tor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_a_tor
msgid "6921 - To Other Reserves"
msgstr "6921 - aan de overige reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_cfc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_cfc
msgid "694 - Compensation for Contributions"
msgstr "694 - Vergoeding van de inbreng"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd
msgid "694/7 - Profit to Be Distributed"
msgstr "694/7 - Uit te keren winst"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_dom
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_dom
msgid "695 - Directors or Managers"
msgstr "695 - Bestuurders of zaakvoerders"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_e
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_e
msgid "696 - Employees"
msgstr "696 - Werknemers"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_ptbd_ob
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_ptbd_ob
msgid "697 - Other Beneficiaries"
msgstr "697 - Andere rechthebbenden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_t
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_t
msgid "70 - Turnover"
msgstr "70 - Omzet"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi
msgid "70/76A - Operating Income"
msgstr "70/76A - Bedrijfsopbrengsten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_sfgwcp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_sfgwcp
msgid ""
"71 - Stocks of Finished Goods and Work and Contracts in Progress: Increase "
"(Decrease) (+)/(-)"
msgstr ""
"71 - Voorraad goederen in bewerking en gereed product en bestellingen in "
"uitvoering: toename (afname) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_pfa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_pfa
msgid "72 - Produced Fixed Assets"
msgstr "72 - Geproduceerde vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_mfgls
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_mfgls
msgid "73 - Membership Fees, Gifts, Legacies and Subsidies"
msgstr "73 - Lidgeld, schenkingen, legaten en subsidies"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_ooi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_ooi
msgid "74 - Other Operating Income"
msgstr "74 - Andere bedrijfsopbrengsten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_rfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi
msgid "75 - Recurring Financial Income"
msgstr "75 - Recurrente financiële opbrengsten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi
msgid "75/76B - Financial Income"
msgstr "75/76B - Financiële opbrengsten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_iffa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_iffa
msgid "750 - Income From Financial Fixed Assets"
msgstr "750 - Opbrengsten uit financiële vaste activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_ica
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_ica
msgid "751 - Income From Current Assets"
msgstr "751 - Opbrengsten uit vlottende activa"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_rfi_ofi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_rfi_ofi
msgid "752/9 - Other Financial Income"
msgstr "752/9 - Andere financiële opbrengsten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_rfi_cis
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_rfi_cis
msgid "753 - Of Which: Capital and Interest Subsidies"
msgstr "753 - Waarvan: kapitaal- en interestsubsidies"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_oi_nroi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_oi_nroi
msgid "76A - Non-Recurring Operating Income"
msgstr "76A - Niet-recurrente bedrijfsopbrengsten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm_nroi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm_nroi
msgid "76A - Of Which: Non-Recurring Operating Income"
msgstr "76A - Waarvan: niet-recurrente bedrijfsopbrengsten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_fi_nrfi
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_comp_fi_nrfi
msgid "76B - Non-Recurring Financial Income"
msgstr "76B - Niet-recurrente financiële opbrengsten"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_itr_aitwbtp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_itr_aitwbtp
msgid "77 - Adjustment of Income Taxes and Write-Back of Tax Provisions"
msgstr ""
"77 - Regularisering van belastingen en terugneming van voorzieningen voor "
"belastingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_tfdt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_tfdt
msgid "780 - Transfer From Deferred Taxes"
msgstr "780 - Onttrekking aan de uitgestelde belastingen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_tfur
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_tfur
msgid "789 - Transfer From Untaxed Reserves"
msgstr "789 - Onttrekking aan de belastingvrije reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe_tc
msgid "791 - From Contributions"
msgstr "791 - aan de inbreng"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_tfefafor
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_tfefafor
msgid "791 - Transfers From Equity: Funds, Allocated Funds and Other Reserves"
msgstr ""
"791 - Onttrekking aan het eigen vermogen: fondsen, bestemde fondsen en "
"andere reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_tfe
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe
msgid "791/2 - Transfers From Equity"
msgstr "791/2 - Onttrekking aan het eigen vermogen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_tfe_fr
msgid "792 - From Reserves"
msgstr "792 - aan de reserves"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_scirol
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_scirol
msgid "794 - Shareholders' Contribution in Respect of Losses"
msgstr "794 - Tussenkomst van de vennoten in het verlies"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc_gm
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc_gm
msgid "9900 - Gross Margin (+)/(-)"
msgstr "9900 - Brutomarge (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_opl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_opl
msgid "9901 - Operating Profit (Loss) (+)/(-)"
msgstr "9901 - Bedrijfswinst (Bedrijfsverlies) (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plpbt
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plpbt
msgid "9903 - Profit (Loss) for the Period Before Taxes (+)/(-)"
msgstr "9903 - Winst (Verlies) van het boekjaar vóór belasting (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plp
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plp
msgid "9904 - Profit (Loss) of the Period (+)/(-)"
msgstr "9904 - Winst (Verlies) van het boekjaar (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl_plpa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_plpafa
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl_plpa
msgid "9905 - Profit (Loss) of the Period Available for Appropriation (+)/(-)"
msgstr "9905 - Te bestemmen winst (verlies) van het boekjaar (+)/(-)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a_pltba
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a_pltba
msgid "9906 - Profit (Loss) to Be Appropriated (+)/(-)"
msgstr "9906 - Te bestemmen winst (verlies) (+)/(-)"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "AMOUNT TOTAL"
msgstr "TOTAAL BEDRAG"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_a
msgid "APPROPRIATION ACCOUNT"
msgstr "RESULTAATVERWERKING"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_a
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_a
msgid "ASSETS"
msgstr "ACTIVA"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__atn
msgid "ATN"
msgstr "VAA"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_chart_template
msgid "Account Chart Template"
msgstr "Rekeningschema"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_needaction
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_needaction
msgid "Action Needed"
msgstr "Actie gevraagd"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__2
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__2
msgid "Add"
msgstr "Toevoegen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_address
msgid "Address"
msgstr "Adres"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_address
msgid "Address of the partner when the form was created"
msgstr "Adres van de partner toen de fiche werd aangemaakt"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_amount
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Amount"
msgstr "Bedrag"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_general_ledger.py:0
msgid "Annual Accounts"
msgstr "Jaarrekening"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__ask_payment
msgid "Ask Payment"
msgstr "Betaling vragen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__ask_restitution
msgid "Ask Restitution"
msgstr "Teruggave vragen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Ask payment:"
msgstr "Betaling vragen:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Ask restitution:"
msgstr "Teruggave vragen:"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_attachment_count
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_attachment_count
msgid "Attachment Count"
msgstr "Aantal bijlagen"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "Audit"
msgstr "Audit"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_bce_number
msgid "BCE number"
msgstr "Ondernemingsnummer"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_asso_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_asso_f_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_acap_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_acon_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_fcap_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_bs_comp_fcon_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_asso_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_asso_f_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_comp_a_column
#: model:account.report.column,name:l10n_be_reports.account_financial_report_pl_comp_f_column
msgid "Balance"
msgstr "Saldo"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_asso_a
msgid "Balance Sheet (Abbr Assoc)"
msgstr "Balans (VKT/MIC-vzw)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_acap
msgid "Balance Sheet (Abbr Cap)"
msgstr "Balans (VKT/MIC-kap)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_acon
msgid "Balance Sheet (Abbr Con)"
msgstr "Balans (VKT/MIC-inb)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_asso_f
msgid "Balance Sheet (Full Assoc)"
msgstr "Balans (VOL-vzw)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_fcap
msgid "Balance Sheet (Full Cap)"
msgstr "Balans (VOL-kap)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_bs_comp_fcon
msgid "Balance Sheet (Full Con)"
msgstr "Balans (VOL-inb)"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_ec_sales_report_handler
msgid "Belgian EC Sales Report Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_reports_periodic_vat_xml_export
msgid "Belgian Periodic VAT Report Export Wizard"
msgstr "Wizard Belgische periodieke btw-rapporten exporteren"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_tax_report_handler
msgid "Belgian Tax Report Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "CERTIFIED AS ACCURATE"
msgstr "ECHT VERKLAARD"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__calling_export_wizard_id
msgid "Calling Export Wizard"
msgstr "Wizard exporteren opvragen"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__3
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__3
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Cancel"
msgstr "Annuleren"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Carry over from the previous divider"
msgstr "Overgebracht van het vorige tussenblad"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Choose option(s) before exporting XML:"
msgstr "Maak je keuze voor het exporteren van de XML:"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__citizen_identification
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__citizen_identification
msgid "Citizen Identification"
msgstr "Nationaal registratienummer"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_citizen_identification
msgid "Citizen identification number"
msgstr "Nationaal nummer"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_city
msgid "City"
msgstr "Plaats"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_city
msgid "City of the partner when the form was created"
msgstr "Gemeente van de partner toen de fiche werd aangemaakt"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__client_nihil
msgid "Client Nihil"
msgstr "Klant Nihil"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.pdf_export_filters
msgid "Client nihil:"
msgstr "Klant nihil:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Close"
msgstr "Sluiten"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__comment
msgid "Comment"
msgstr "Opmerkingen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Comments"
msgstr "Opmerkingen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__commissions
msgid "Commissions"
msgstr "Commissies"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_res_company
msgid "Companies"
msgstr "Bedrijven"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__company_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__company_id
msgid "Company"
msgstr "Bedrijf"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Company name or exact name (companies and other institutions)"
msgstr ""
"Handelsnaam of juiste benaming (vennootschappen en andere instellingen)"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Company: %s"
msgstr "Bedrijf: %s"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__control_value
msgid "Control Value"
msgstr "Controlewaarde"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__country_id
msgid "Country"
msgstr "Land"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_country
msgid "Country Code"
msgstr "Landcode"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__country_id
msgid "Country of the partner when the form was created"
msgstr "Land van de partner toen de fiche werd aangemaakt"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Create 325 Form"
msgstr "Opgave 325 maken"

#. module: l10n_be_reports
#: model:ir.actions.act_window,name:l10n_be_reports.action_open_create_325_form
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_create_325_form
msgid "Create 325 form"
msgstr "Opgave 325 maken"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__create_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__create_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__currency_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__currency_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__currency_id
msgid "Currency"
msgstr "Valuta"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "DESIGNATION OF THE FORMS"
msgstr "BENAMING VAN DE FICHES"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_id
msgid "Debtor"
msgstr "Debiteur"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_address
msgid "Debtor Address"
msgstr "Debiteuradres"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_bce_number
msgid "Debtor BCE Number"
msgstr "Ondernemingsnummer van de schuldenaar"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_citizen_identification
msgid "Debtor Citizen Identification"
msgstr "Nationaal nummer van de schuldenaar"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_city
msgid "Debtor City"
msgstr "Debiteur Stad"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_country_id
msgid "Debtor Country"
msgstr "Debiteur Land"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_name
msgid "Debtor Name"
msgstr "Naam van de schuldenaar"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_phone_number
msgid "Debtor Phone Number"
msgstr "Telefoonnummer van de schuldenaar"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_zip
msgid "Debtor ZIP"
msgstr "Postcode van de schuldenaar"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__company_id
msgid "Debtor company"
msgstr "Bedrijf van de schuldenaar"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__company_id
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__company_id
msgid "Debtor for which the form is created"
msgstr "Schuldenaar waarvoor de opgave is gemaakt"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__debtor_is_natural_person
msgid "Debtor is Natural Person"
msgstr "Schuldenaar is een natuurlijk persoon"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__display_name
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Divider N°"
msgstr "Tussenblad nr."

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
msgid "Do not forget to submit the"
msgstr "Vergeet niet om de"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_281_50_form.py:0
msgid "Download 281.50 Form PDF"
msgstr "Fiche 281.50 PDF downloaden"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_281_50_view_form
msgid "Download pdf"
msgstr "PDF downloaden"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__state__draft
msgid "Draft"
msgstr "Concept"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "Duplicate VAT number"
msgstr "Duplicaat BTW-nummer"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__1
msgid "Dutch"
msgstr "Nederlands"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.belgian_ec_sales_report
msgid "EC Sales List"
msgstr "Btw-opgave van de intracommunautaire handelingen"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
msgid "EC Sales List Audit"
msgstr "Btw-opgave van de intracommunautaire handelingen"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
msgid "EC Sales list report"
msgstr "Btw-opgave van de intracommunautaire handelingen"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/sales_report/warnings.xml:0
msgid ""
"EC Sales taxes report total does not match Tax Report lines 44 + 46L + 46T - "
"48s44 - 48s46L - 48s46T."
msgstr ""
"Het totaal van de intracommunautaire listing komt niet overeen met lijnen 44 "
"+ 46L + 46T - 48s44 - 48s46L - 48s46T op de btw-aangifte."

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el
msgid "EQUITY AND LIABILITIES"
msgstr "PASSIVA"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid ""
"Either there isn't any account nor partner with a 281.50 tag or there isn't "
"any amount to report for this period."
msgstr ""
"Er is geen rekening of partner met een 281.50-tag of er is geen bedrag te "
"rapporteren voor deze periode."

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Enterprise N°:"
msgstr "Ondernemingsnummer:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Export Options"
msgstr "Exporteer opties"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_account_financial_report_export
msgid "Export XML"
msgstr "Exporteer in XML"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_reports_export_wizard_format
msgid "Export format for accounting's reports"
msgstr "Exporteer formaat voor boekhoudkundige rapportages"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_reports_export_wizard
msgid "Export wizard for accounting's reports"
msgstr "Exporteer wizard voor boekhoudkundige rapportages"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__exposed_expenses
msgid "Exposed expenses"
msgstr "Uitgegeven kosten"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "FINANCES"
msgstr "FINANCIËN"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "FORM N° 281.50 (commissions, brokerage, etc) - YEAR"
msgstr "FORMULIER N° 281.50 (commissies, makelaarslonen, enz) - JAAR"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "FORMS 325.50"
msgstr "OPGAVEN 325.50"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Federal Public Service"
msgstr "Federale Overheidsdienst"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__fees
msgid "Fees"
msgstr "Vergoedingen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_follower_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_follower_ids
msgid "Followers"
msgstr "Volgers"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_partner_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_partner_ids
msgid "Followers (Partners)"
msgstr "Volgers (relaties)"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Form 281.50 N°"
msgstr "Nummer van het fiche Nr. 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__form_325_id
msgid "Form 325"
msgstr "Opgave 325"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_partner__form_file
#: model:ir.model.fields,field_description:l10n_be_reports.field_res_users__form_file
msgid "Form File"
msgstr "Formulierbestand"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_ids
msgid "Forms 281.50"
msgstr "Fiches 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_count
msgid "Forms 281.50 count"
msgstr "Aantal fiches 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__form_281_50_total_amount
msgid "Forms 281.50 total"
msgstr "Totaal fiches 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__2
msgid "French"
msgstr "Frans"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "GENERAL TAX ADMINISTRATION"
msgstr "ALGEMENE ADMINISTRATIE VAN DE FISCALITEIT"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_account_general_ledger_report_handler
msgid "General Ledger Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 281.50 forms PDF"
msgstr "PDF voor fiches 281.50 genereren"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 281.50 forms XML"
msgstr "XML voor fiches 281.50 genereren"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "Generate 325 form"
msgstr "Opgave 325 genereren"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Generate 325 form PDF"
msgstr "PDF voor opgave 325 genereren"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__state__generated
msgid "Generated"
msgstr "Gegenereerd"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sender_lang_code__3
msgid "German"
msgstr "Duits"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_281_50_pdf
msgid "Get 281.50 report as PDF."
msgstr "Haal formulier 281.50 op als PDF."

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_281_50_xml
msgid "Get 281.50 report as XML."
msgstr "Haal formulier 281.50 op als XML."

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_report_l10n_be_reports_report_325_pdf
msgid "Get 325 Report as PDF"
msgstr "Opgave 325 downloaden als PDF"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__has_message
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__has_message
msgid "Has Message"
msgstr "Heeft een bericht"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__id
msgid "ID"
msgstr "ID"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__official_id
msgid "Identification number"
msgstr "Identificatienummer"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_needaction
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Indien aangevinkt, vragen nieuwe berichten je aandacht."

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_sms_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_error
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "indien aangevinkt hebben sommige leveringen een fout."

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__income_debtor_bce_number
msgid "Income debtor BCE number"
msgstr "Ondernemingsnummer van de schuldenaar van de inkomsten"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__is_test
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__is_test
msgid "Indicates if the 325 is a test"
msgstr "Geeft aan of de 325 een test is"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid "Internal reference to the following 281.50 tags are missing:\n"
msgstr "Interne referentie naar de volgende 281.50 tags ontbreekt:\n"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_is_follower
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_is_follower
msgid "Is Follower"
msgstr "Is een volger"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_is_natural_person
msgid "Is the partner a natural person? (as opposed to a moral person)"
msgstr ""
"Is de partner een natuurlijk persoon? (in tegenstelling tot een "
"rechtspersoon)"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_job_position
msgid "Job position"
msgstr "Beroep"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__write_uid
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__write_date
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Lastname, Firstname (natural persons)"
msgstr "Naam, voornamen (natuurlijke personen)"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"Lastname, Firstname (or denomination) and address of the recipient of the "
"income:"
msgstr ""
"Achternaam, Voornaam (of benaming) en adres van de ontvanger van de "
"inkomsten:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid ""
"Lastname, Firstname (or denomination) of beneficiaries\n"
"                                    <br/>\n"
"                                    Street, n°, eventually box\n"
"                                    <br/>\n"
"                                    Zip code and city"
msgstr ""
"Naam en voornaam (of benaming) van de verkrijgers\n"
"                                    <br/>\n"
"                                    Straat, nummer en eventueel bus\n"
"                                    <br/>\n"
"                                    Postcode en gemeente"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__forms_281_50
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__forms_281_50
msgid "List of 281.50 forms for this partner"
msgstr "Lijst van fiches 281.50 voor deze partner"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_error
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_error
msgid "Message Delivery error"
msgstr "Fout bij het afleveren van bericht"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_ids
msgid "Messages"
msgstr "Berichten"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid "Missing partner data"
msgstr "Ontbrekende partnergegevens"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__1
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__1
msgid "Modification"
msgstr "Wijziging"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "NIL"
msgstr "NIHIL"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "NN or NE:"
msgstr "NN of ON:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "NUMBER OF FORMS"
msgstr "AANTAL OPGEMAAKTE FICHES"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_name
msgid "Name of the partner when the form was created"
msgstr "Naam van de partner toen deze fiche werd aangemaakt"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_is_natural_person
msgid "Natural person"
msgstr "Natuurlijk persoon"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No VAT number associated with your company."
msgstr "Geen btw-nummer geassocieerd met je bedrijf."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "No email address associated with company %s."
msgstr "Geen e-mailadres geassocieerd met het bedrijf %s."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No email address associated with the company."
msgstr "Geen emailadres geassocieerd met het bedrijf."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "No phone associated with company %s."
msgstr "Geen telefoonnummer geassocieerd met het bedrijf %s."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "No phone associated with the company."
msgstr "Geen telefoonnummer geassocieerd met het bedrijf."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
msgid "No vat number defined for %s."
msgstr "Geen btw-nummer gedefinieerd voor %s."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Not allowed negative amounts"
msgstr "Negatieve bedragen zijn niet toegestaan"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_needaction_counter
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_needaction_counter
msgid "Number of Actions"
msgstr "Aantal acties"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_error_counter
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_error_counter
msgid "Number of errors"
msgstr "Aantal fouten"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_needaction_counter
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Aantal berichten die actie vereisen"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__message_has_error_counter
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Aantal berichten met leveringsfout"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 1"
msgstr "nr. 1"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 2"
msgstr "nr. 2"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "N° 3"
msgstr "nr. 3"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
#, python-format
msgid ""
"Only users with the access group '%s' can unset the 281.50 category on "
"partners."
msgstr ""
"Alleen gebruikers met de toegangsgroep '%s' kunnen de categorie 281.50 "
"op partners uitschakelen"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_view_form
msgid "Open"
msgstr "Open"

#. module: l10n_be_reports
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_open_325_tree_view
msgid "Open 325 forms"
msgstr "Opgaven 325 openen"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid "Open list"
msgstr "Lijst openen"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl_oioc
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl_oioc
msgid "Operating Income and Operating Charges"
msgstr "Bedrijfsopbrengsten en bedrijfskosten"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_reports_periodic_vat_xml_export__calling_export_wizard_id
msgid ""
"Optional field containing the report export wizard calling this wizard, if "
"there is one."
msgstr ""
"Optioneel veld met de rapport export wizard die deze wizard aanroept, indien "
"er een is."

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__treatment_type__0
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__treatment_type__0
msgid "Original"
msgstr "Origineel"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sending_type__0
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__sending_type__0
msgid "Original send"
msgstr "Origineel verzenden"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_oay
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_oay
msgid "Other Appropriations of the Year"
msgstr "Andere resultaatverwerkingen van het boekjaar"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "PAYROLL DEDUCTIONS"
msgstr "BEDRIJFSVOORHEFFING"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_a_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_asso_f_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_a_pl
#: model:account.report.line,name:l10n_be_reports.account_financial_report_pl_comp_f_pl
msgid "PROFIT AND LOSS ACCOUNT"
msgstr "RESULTATENREKENING"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__paid_amount
msgid "Paid amount"
msgstr "Betaald bedrag"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_id
msgid "Partner"
msgstr "Partner"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_name
msgid "Partner Name"
msgstr "Relatienaam"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.l10n_be_partner_vat_listing
#: model:account.report.line,name:l10n_be_reports.l10n_be_partner_vat_listing_line
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_l10n_be_partner_vat_listing
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_partner_vat_listing
#: model:ir.ui.menu,name:l10n_be_reports.menu_action_account_report_l10n_be_partner_vat_listing
msgid "Partner VAT Listing"
msgstr "Jaarlijkse lijst van de btw-belastingplichtige afnemers"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_partner_vat_handler
msgid "Partner VAT Listing Custom Handler"
msgstr ""

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_id
msgid "Partner for which this 281.50 form has been created"
msgstr "Partner voor wie deze fiche 281.50 werd opgemaakt"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.view_partner_281_50_required_fields
msgid "Partner with missing information"
msgstr "Partner met ontbrekende informatie"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
msgid "Partners sharing the same VAT number"
msgstr "Partners die hetzelfde BTW-nummer delen"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_account_reports_export_wizard__l10n_be_reports_periodic_vat_wizard_id
msgid "Periodic VAT Export Wizard"
msgstr "Wizard periodieke btw-rapporten exporteren"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Postal code and city"
msgstr "Postnr. en gemeente"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid "Profession:"
msgstr "Beroep:"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_ply
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_ply
msgid "Profit (Loss) of the Year"
msgstr "Winst (Verlies) van het boekjaar"

#. module: l10n_be_reports
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_asso_a
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_asso_f
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_comp_a
#: model:ir.actions.client,name:l10n_be_reports.action_account_report_be_pl_comp_f
msgid "Profit and Loss"
msgstr "Winst en Verlies"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_asso_a
msgid "Profit and Loss (Abbr Assoc)"
msgstr "Resultatenrekening (VKT/MIC-vzw)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_comp_a
msgid "Profit and Loss (Abbr)"
msgstr "Resultatenrekening (VKT/MIC)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_asso_f
msgid "Profit and Loss (Full Assoc)"
msgstr "Resultatenrekening (VOL-vzw)"

#. module: l10n_be_reports
#: model:account.report,name:l10n_be_reports.account_financial_report_pl_comp_f
msgid "Profit and Loss (Full)"
msgstr "Resultatenrekening (VOL)"

#. module: l10n_be_reports
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_a_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_asso_f_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acap_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_acon_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcap_el_e_apl_plpy
#: model:account.report.line,name:l10n_be_reports.account_financial_report_bs_comp_fcon_el_e_apl_plpy
msgid "Profits (Losses) from Previous Years"
msgstr "Winst (Verlies) van vorige boekjaren"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid ""
"RECEPTION DATE\n"
"                                                        <br/>\n"
"                                                        (For administration "
"use only)"
msgstr ""
"DATUM VAN ONTVANGST\n"
"                                                        <br/>\n"
"                                                        (vak bestemd voor de "
"administratie)"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__reference_year
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__reference_year
msgid "Reference Year"
msgstr "Referentiejaar"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__reference_year
msgid "Reference year"
msgstr "Referentiejaar"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_281_50
msgid "Represents a 281.50 form"
msgstr "Stelt een fiche 281.50 voor"

#. module: l10n_be_reports
#: model:ir.model,name:l10n_be_reports.model_l10n_be_form_325
msgid "Represents a 325 form"
msgstr "Stelt een opgave 325 voor"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__message_has_sms_error
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Sms-afleveringsfout"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "SUMMARY STATEMENT YEAR"
msgstr "SAMENVATTENDE OPGAVE JAAR"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "SUMMARY TABLE OF RECORDS AND PAYROLL DEDUCTIONS (1)"
msgstr "SAMENVATTENDE TABEL VAN DE FICHES EN DE BEDRIJFSVOORHEFFING (1)"

#. module: l10n_be_reports
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325__sending_type__1
#: model:ir.model.fields.selection,name:l10n_be_reports.selection__l10n_be_form_325_wizard__sending_type__1
msgid "Send grouped corrections"
msgstr "Gegroepeerde correcties verzenden"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_id
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__sender_id
msgid "Sender"
msgstr "Afzender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_address
msgid "Sender Address"
msgstr "Adres van de verzender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_bce_number
msgid "Sender BCE Number"
msgstr "Ondernemingsnummer van de verzender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_city
msgid "Sender City"
msgstr "Gemeente van de verzender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_lang_code
msgid "Sender Language Code"
msgstr "Taalcode van de verzender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_name
msgid "Sender Name"
msgstr "Naam van de verzender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_phone_number
msgid "Sender Phone Number"
msgstr "Telefoonnummer van de verzender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sender_zip
msgid "Sender ZIP"
msgstr "Telefoonnummer van de verzender"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__sending_type
msgid "Sending Type"
msgstr "Type verzenden"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__sending_type
msgid "Sending type"
msgstr "Verzendtype"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Some fields required for the export are missing. Please specify them."
msgstr ""
"Sommige velden die nodig zijn voor de export ontbreken. Gelieve ze in te "
"vullen."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/res_partner.py:0
msgid ""
"Some partners are not correctly configured. Please be sure that the "
"following pieces of information are set: street, zip code, country%s and vat "
"or citizen identification."
msgstr ""
"Sommige partners zijn niet correct geconfigureerd. Zorg ervoor dat de "
"volgende informatie is ingesteld: straat, postcode, land%s en btw-nummer of "
"nationaal nummer."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "Specify"
msgstr "Preciseren"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__state
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__state
msgid "State"
msgstr "Staat"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "Street and n°"
msgstr "Straat en nr."

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "TOTAL AMOUNT"
msgstr "TOTAAL BEDRAG"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "TOTALS"
msgstr "TOTALEN"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_general_ledger.py:0
msgid "TXT"
msgstr ""

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_tax
msgid "Tax Code"
msgstr "Code"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__form_file
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__form_file
msgid "Technical field to store all forms file."
msgstr "Technisch veld om alle formulieren in op te slaan."

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__is_test
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__is_test
msgid "Test Form"
msgstr "Testformulier"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__sender_id
msgid "The company responsible for sending the form."
msgstr "Het bedrijf verantwoordelijk voor het verzenden van het formulier."

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/tax_report/warnings.xml:0
#, fuzzy, python-format
#| msgid "Some controls failed"
msgid "The following controls failed:"
msgstr "Sommige controles zijn mislukt"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "The reference year must be a number."
msgstr "Het referentiejaar moet een getal zijn."

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_res_partner__citizen_identification
#: model:ir.model.fields,help:l10n_be_reports.field_res_users__citizen_identification
msgid ""
"This code corresponds to the personal identification number for the tax "
"authorities."
msgstr ""
"Dit nummer komt overeen met het persoonlijke identificatienummer voor de "
"belastingautoriteiten."

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__sending_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__sending_type
msgid ""
"This field allows to make an original sending(correspond to first send) or a "
"grouped corrections(if you have made some mistakes before)."
msgstr ""
"Met dit veld kunt u een originele verzending maken (komt overeen met de "
"eerste verzending) of een gegroepeerde correctie (als u eerder fouten hebt "
"gemaakt)."

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__treatment_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__treatment_type
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325_wizard__treatment_type
msgid "This field represents the nature of the form."
msgstr "Dit veld geeft de aard van het formulier aan."

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "To be completed only if this divider is not the first or the only one"
msgstr "Alleen invullen als dit tussenblad niet het eerste of het enige is"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Total amount or to be transferred to the next divider:"
msgstr "ALGEMEEN TOTAAL OF OVER TE BRENGEN NAAR HET VOLGENDE TUSSENBLAD:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_50_divider_pdf
msgid "Total mentionned in box 3 e of the form 281.50"
msgstr "Totaal vermeld in vak 3, e van het fiche Nr. 281.50"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__total_remuneration
msgid "Total remuneration"
msgstr "Totaal"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__treatment_type
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__treatment_type
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325_wizard__treatment_type
msgid "Treatment Type"
msgstr "Type behandeling"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_turnover
msgid "Turnover"
msgstr "Omzet"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__user_id
msgid "User"
msgstr "Gebruiker"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_vat_amount
msgid "VAT Amount"
msgstr "Btw-bedrag"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "VAT Listing Audit"
msgstr "Audit van de jaarlijkse lijst van de btw-belastingplichtige afnemers"

#. module: l10n_be_reports
#: model:account.report.column,name:l10n_be_reports.account_financial_report_ec_sales_vat
#: model:account.report.column,name:l10n_be_reports.l10n_be_partner_vat_listing_vat_number
msgid "VAT Number"
msgstr "Btw-nummer"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "View Partner"
msgstr "Bekijk relatie"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__website_message_ids
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_325__website_message_ids
msgid "Website Messages"
msgstr "Websiteberichten"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__website_message_ids
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_325__website_message_ids
msgid "Website communication history"
msgstr "Website communicatiegeschiedenis"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
#: code:addons/l10n_be_reports/models/account_sales_report.py:0
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
msgid "XML"
msgstr "XML"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "XML Export Options"
msgstr "XML exportopties"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid "You already generated 281.50 forms for this 325 form."
msgstr "Je hebt al fiches 281.50 gegenereerd voor deze opgave 325."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_281_50_form.py:0
#: code:addons/l10n_be_reports/models/account_325_form.py:0
msgid "You can't delete a 281.50 for which its form 325 xml has been generated"
msgstr ""
"Je kan geen fiche 281.50 verwijderen waarvoor de XML van zijn opgave 325 "
"gegenereerd is"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "You can't use a reference year in the future or for the current year."
msgstr ""
"Je kan geen referentiejaar in de toekomst of voor het huidige jaar gebruiken."

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/wizard/l10n_be_325_form_wizard.py:0
msgid "You must be logged in a Belgian company to use this feature"
msgstr ""
"Je moet ingelogd zijn bij een Belgische bedrijf om deze functie te gebruiken"

#. module: l10n_be_reports
#: model:ir.model.fields,field_description:l10n_be_reports.field_l10n_be_form_281_50__partner_zip
msgid "Zip"
msgstr "Postcode"

#. module: l10n_be_reports
#: model:ir.model.fields,help:l10n_be_reports.field_l10n_be_form_281_50__partner_zip
msgid "Zip of the partner when the form was created"
msgstr "Postcode van de partner toen de fiche werd opgemaakt"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200 "
"if [44] > 99.999"
msgstr ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200 "
"als [44] > 99.999"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
#, python-format
msgid ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200 "
"if [88] > 99.999"
msgstr ""
"[44] < ([00] + [01] + [02] + [03] + [45] + [46] + [47] + [48] + [49]) * 200 "
"als [88] > 99.999"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[55] > 0 if [86] > 0 or [88] > 0"
msgstr "[55] > 0 als [86] > 0 of [88] > 0"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[56] + [57] > 0 if [87] > 0"
msgstr "[56] + [57] > 0 als [87] > 0"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/account_report.py:0
msgid "[88] < ([81] + [82] + [83] + [84]) * 100 if [88] > 99.999"
msgstr "[88] < ([81] + [82] + [83] + [84]) * 100 als [88] > 99.999"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"a) Commissions, brokerage, commercial discounts, etc:\n"
"                                                <br/>\n"
"                                                b) Fees or vacations:\n"
"                                                <br/>\n"
"                                                c) Benefits in kind "
"(nature : ................)\n"
"                                                <br/>\n"
"                                                d) Expenses incurred on "
"behalf of the beneficiary:\n"
"                                                <br/>\n"
"                                                e) Total (see also in "
"sections f and g below):"
msgstr ""
"a) Commissies, makelaarslonen, handelsrestorno's, enz.:\n"
"                                                <br/>\n"
"                                                b) Erelonen of "
"vacatiegelden:\n"
"                                                <br/>\n"
"                                                c) Voordelen van alle aard "
"(aard: ................)\n"
"                                                <br/>\n"
"                                                d) Kosten gemaakt voor "
"rekening van de verkrijger:\n"
"                                                <br/>\n"
"                                                e) Totaal (zie ook rubrieken "
"f en g hierna):"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.res_partner_view_form_inherit
msgid "e.g. 123455 555 6"
msgstr "bijv. 123455 555 6"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.form_325_wizard_view_form
msgid "e.g. 2018"
msgstr "bijv. 2018"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_325_pdf
msgid "established by :"
msgstr "opgemaakt door:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"f) Enter here, if applicable, the amount included in item e\n"
"                                            above relates to compensation "
"paid to:\n"
"                                            <br/>\n"
"                                            - athletes for their sports "
"performances:\n"
"                                            <br/>\n"
"                                            - trainers, coaches and "
"accompaniers for their activity for the\n"
"                                            benefit of the athletes:"
msgstr ""
"f) Als het in rubriek e ingevulde bedrag vergoedingen bevat voor sportieve "
"activiteiten van sportbeoefenaars of vergoedingen voor activiteiten van "
"opleiders, trainers of begeleiders ten behoeve van sportbeoefenaars, vermeld "
"dan hier het daarin begrepen bedrag dat is uitgekeerd aan:\n"
"<br/>\n"
"- sportbeoefenaars voor hun sportieve activiteiten:\n"
"<br/>\n"
"- opleiders, trainers en begeleiders voor hun activiteiten ten behoeve van "
"sportbeoefenaars:"

#. module: l10n_be_reports
#: model_terms:ir.ui.view,arch_db:l10n_be_reports.report_281_50_pdf
msgid ""
"g) If the amount indicated in item e above does not coincide with\n"
"                                                the amount actually paid in"
msgstr ""
"g) Als het in rubriek e ingevulde bedrag niet overeenstemt met het bedrag "
"dat werkelijk in"

#. module: l10n_be_reports
#. odoo-python
#: code:addons/l10n_be_reports/models/partner_vat_listing.py:0
#, python-format
msgid "Missing partners"
msgstr "Ontbrekende partners"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid ""
"customers with a turnover of more than 250€ or one or more credit notes in "
"the selected period who are not included in the report. Click"
msgstr ""
"klanten met een omzet van meer dan € 250 of één of meer creditnota's in "
"de geselecteerde periode die niet in het rapport zijn opgenomen. Klik"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "You have"
msgstr "Je hebt"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "here"
msgstr "hier"

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
#, python-format
msgid "to see the list."
msgstr "om de lijst te bekijken."

#. module: l10n_be_reports
#. odoo-javascript
#: code:addons/l10n_be_reports/static/src/components/partner_vat_listing/warnings.xml:0
msgid "will be grouped in the XML export."
msgstr ""
