# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* partner_commission
# 
# Translators:
# Wil O<PERSON>, 2024
# <PERSON><PERSON>, 2024
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-25 09:27+0000\n"
"PO-Revision-Date: 2024-09-25 09:44+0000\n"
"Last-Translator: <PERSON><PERSON>, 2024\n"
"Language-Team: French (https://app.transifex.com/odoo/teams/41243/fr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: fr\n"
"Plural-Forms: nplurals=3; plural=(n == 0 || n == 1) ? 0 : n != 0 && n % 1000000 == 0 ? 1 : 2;\n"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid ""
"\n"
"%(product)s: from %(start_date)s to %(end_date)s"
msgstr ""
"\n"
"%(product)s : du %(start_date)s au %(end_date)s"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid " (%d month(s))"
msgstr " (%d mois)"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__active
msgid "Active"
msgstr "Actif"

#. module: partner_commission
#: model:res.groups,name:partner_commission.group_commission_manager
msgid "All Documents"
msgstr "Tous les documents"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.commission_plan_search_view
msgid "Archived"
msgstr "Archivé"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Automatic PO frequency"
msgstr "Fréquence automatique des bons de commande"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__is_capped
msgid "Capped"
msgstr "Plafonné"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__purchase_order__purchase_type__commission
#: model:product.template,name:partner_commission.product_commission_product_template
msgid "Commission"
msgstr "Commission"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_company__commission_automatic_po_frequency
#: model:ir.model.fields,field_description:partner_commission.field_res_config_settings__commission_automatic_po_frequency
msgid "Commission Automatic Po Frequency"
msgstr "Commission Fréquence automatique des bons de commande"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__plan_id
#: model:ir.model.fields,field_description:partner_commission.field_res_partner__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_res_users__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order_log_report__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_report__commission_plan_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_subscription_report__commission_plan_id
msgid "Commission Plan"
msgstr "Plan de comission"

#. module: partner_commission
#: model:ir.actions.act_window,name:partner_commission.action_commission_plans
#: model:ir.ui.menu,name:partner_commission.menu_commission_plans
msgid "Commission Plans"
msgstr "Plans de comission"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "Commission on %(invoice)s, %(partner)s, %(amount)s"
msgstr "Commission sur %(invoice)s, %(partner)s, %(amount)s"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_commission_plan
msgid "Commission plan"
msgstr "Plan de commission"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "Commission refunded. Invoice: %(link)s. Amount: %(amount)s."
msgstr "Commission remboursée. Facture : %(link)s. Montant : %(amount)s."

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_commission_rule
msgid "Commission rules management."
msgstr "Gestion des règles des commissions."

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_company
msgid "Companies"
msgstr "Sociétés"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__company_id
msgid "Company"
msgstr "Société"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de configuration"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_partner
msgid "Contact"
msgstr "Contact"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__create_uid
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__create_date
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__create_date
msgid "Created on"
msgstr "Créé le"

#. module: partner_commission
#: model:ir.actions.act_window,name:partner_commission.action_view_customer_invoices
msgid "Customer Invoices"
msgstr "Factures clients"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_partner_grade__default_commission_plan_id
msgid "Default Commission Plan"
msgstr "Plan de commission par défaut"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__display_name
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__display_name
msgid "Display Name"
msgstr "Nom d'affichage"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission_plan_frozen
msgid "Freeze Plan"
msgstr "Geler le plan"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Frequency at which purchase orders will be automatically confirmed"
msgstr "Fréquence à laquelle les commandes seront automatiquement confirmées"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__id
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__id
msgid "ID"
msgstr "ID"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__product_id
msgid ""
"If set, the rule does not apply to the whole category but only on the given product.\n"
"The product must belong to the selected category.\n"
"Use several rules if you need to match multiple products within a category."
msgstr ""
"Si défini, la règle ne s'applique pas à la catégorie entière mais seulement au produit donné.\n"
"Le produit doit appartenir à la catégorie sélectionnée.\n"
"Utilisez plusieurs règles si vous avez besoin d'associer plusieurs produits au sein d'une catégorie."

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_purchase_order__invoice_commission_count
msgid "Invoices that have generated commissions included in this order"
msgstr "Factures ayant généré des commissions incluses dans cette commande"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_account_move
msgid "Journal Entry"
msgstr "Pièce comptable"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_account_move_line
msgid "Journal Item"
msgstr "Écriture comptable"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__write_uid
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__write_uid
msgid "Last Updated by"
msgstr "Mis à jour par"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__write_date
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__write_date
msgid "Last Updated on"
msgstr "Mis à jour le"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_crm_lead
msgid "Lead/Opportunity"
msgstr "Piste/opportunité"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__manually
msgid "Manually"
msgstr "Manuellement"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__max_commission
msgid "Max Commission"
msgstr "Commission maximale"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__max_commission
msgid "Maximum amount, specified in the currency of the pricelist, if given."
msgstr ""
"Montant maximum, précisé dans la devise de la liste de prix, si elle est "
"définie."

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Minimum PO amount total"
msgstr "Montant minimum total du bon de commande"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_res_company__commission_po_minimum
#: model:ir.model.fields,field_description:partner_commission.field_res_config_settings__commission_po_minimum
msgid "Minimum Total Amount for PO commission"
msgstr "Montant total minimum pour la commission sur bons de commande"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__monthly
msgid "Monthly"
msgstr "Mensuel"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__name
msgid "Name"
msgstr "Nom"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid "New commission. Invoice: %(link)s. Amount: %(amount)s."
msgstr "Nouvelle commission. Facture : %(link)s. Montant : %(amount)s."

#. module: partner_commission
#: model:res.groups,name:partner_commission.group_commission_user
msgid "Own Documents Only"
msgstr "Documents personnels seulement"

#. module: partner_commission
#: model:ir.actions.server,name:partner_commission.cron_confirm_purchase_orders_ir_actions_server
msgid "Partner Commission: confirm purchase orders"
msgstr "Commission partenaire : confirmer les bons de commande"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_res_partner_grade
msgid "Partner Grade"
msgstr "Niveau partenaire"

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid "Partners Commissions"
msgstr "Commissions des partenaires "

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__pricelist_id
msgid "Pricelist"
msgstr "Liste de prix"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__purchase_order__purchase_type__procurement
msgid "Procurement"
msgstr "Approvisionnement"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__product_id
msgid "Product"
msgstr "Produit"

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/commission_plan.py:0
msgid "Product %(product)s does not belong to category %(category)s"
msgstr "Le produit %(product)s n'appartient pas à la caégorie %(category)s"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__category_id
msgid "Product Category"
msgstr "Catégorie de produits"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__product_id
msgid "Purchase Default Product"
msgstr "Acheter le produit par défaut"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_purchase_order
msgid "Purchase Order"
msgstr "Bon de commande fournisseur"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_purchase_order__purchase_type
msgid "Purchase Type"
msgstr "Type d'achat"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__quarterly
msgid "Quarterly"
msgstr "Trimestriel"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__rate
msgid "Rate"
msgstr "Taux"

#. module: partner_commission
#: model:ir.model.constraint,message:partner_commission.constraint_commission_rule_check_rate
msgid "Rate should be between 0 and 100."
msgstr "Le taux doit être entre 0 et 100"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_account_bank_statement_line__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_account_move__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_order_log_report__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_report__referrer_id
#: model:ir.model.fields,field_description:partner_commission.field_sale_subscription_report__referrer_id
#: model_terms:ir.ui.view,arch_db:partner_commission.account_move_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_log_search
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_subsciption_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_order_view_search_inherit_partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_report_search
#: model_terms:ir.ui.view,arch_db:partner_commission.sale_subscription_report_search
msgid "Referrer"
msgstr "Référent"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_sale_order__commission
msgid "Referrer Commission"
msgstr "Commission du référent"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_account_bank_statement_line__commission_po_line_id
#: model:ir.model.fields,field_description:partner_commission.field_account_move__commission_po_line_id
msgid "Referrer Purchase Order line"
msgstr "Ligne de bon de commande du référent"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_plan__commission_rule_ids
#: model_terms:ir.ui.view,arch_db:partner_commission.commission_plan_form_view
msgid "Rules"
msgstr "Règles"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__template_id
msgid "Sale Order Template"
msgstr "Modèle de commande client"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_report
msgid "Sales Analysis Report"
msgstr "Rapport d'analyse des ventes"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_order_log_report
msgid "Sales Log Analysis Report"
msgstr "Rapport d'analyse du journal des ventes"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_order
msgid "Sales Order"
msgstr "Commande client"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_commission_rule__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: partner_commission
#: model:ir.model.fields,field_description:partner_commission.field_purchase_order__invoice_commission_count
#: model_terms:ir.ui.view,arch_db:partner_commission.purchase_order_form_inherit_partner_commission
msgid "Source Invoices"
msgstr "Origine des factures"

#. module: partner_commission
#: model:ir.model,name:partner_commission.model_sale_subscription_report
msgid "Subscription Analysis"
msgstr "Analyse des abonnements"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_sale_order__commission_plan_id
msgid "Takes precedence over the Referrer's commission plan."
msgstr "A priorité sur le plan de commission du référent."

#. module: partner_commission
#. odoo-python
#: code:addons/partner_commission/models/account_move.py:0
msgid ""
"The commission partner order %s must be checked manually (especially refund "
"lines which can be duplicated)."
msgstr ""
"La commande de commission partenaire %s doit être vérifiée manuellement "
"(surtout les lignes de remboursement qui peuvent être dupliquées)."

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_res_partner_grade__default_commission_plan_id
msgid ""
"The default commission plan used for this grade. Can be overwritten on the "
"partner form."
msgstr ""
"Le plan de commission par défaut utilisé pour ce niveau. Peut être écrasé "
"sur le formulaire du partenaire."

#. module: partner_commission
#: model_terms:ir.ui.view,arch_db:partner_commission.res_config_settings_view_form
msgid ""
"The required minimum amount total needed to automatically confirm purchase "
"orders"
msgstr ""
"Le montant total minimum requis nécessaire pour confirmer automatiquement "
"les bons de commande"

#. module: partner_commission
#: model:ir.model.fields.selection,name:partner_commission.selection__res_company__commission_automatic_po_frequency__weekly
msgid "Weekly"
msgstr "Hebdomadaire"

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_commission_rule__is_capped
msgid "Whether the commission is capped."
msgstr "Si la commission est plafonnée."

#. module: partner_commission
#: model:ir.model.fields,help:partner_commission.field_sale_order__commission_plan_frozen
msgid ""
"Whether the commission plan is frozen. When checked, the commission plan "
"won't automatically be updated according to the partner level."
msgstr ""
"Si le plan de commission est gelé. Si la case est cochée, le plan de "
"commission ne sera pas automatiquement mis à jour en fonction du niveau du "
"partenaire."
